from rank_csv import RankInstanceCsvResolver
from rank_xls import RankXlsResolver
from topn import TrankInstanceCsvResolver
from topnext import TopNextInstanceCsvResolver, TopNextTemplate
from utils import CSVMerger, CSVAppender


class TopNMerger(RankInstanceCsvResolver):
    csv_header = "榜单ID,赛季类型,分区"
    output_names = {"add": "topn.merge.csv", "del": "del.txt", "mod": "conflict.txt"}

    def __init__(self, name: str, id_to_rule: dict, id_to_shard: dict):
        output_names = TopNMerger.output_names.copy()
        output_names['add'] = name + '.csv'

        super().__init__(id_to_rule=id_to_rule, id_to_shard=id_to_shard, output_names=output_names,
                         output_dir=TrankInstanceCsvResolver.default_output_dir(), csv_header=TopNMerger.csv_header)

    def csv_one_line(self, zone: int, rank: int, name: str, enable_shard: bool, enable_lite: bool) -> str:
        if enable_shard:
            zone = self.id_to_shard.get(rank, self.default_shard) * 10000 + zone

        season = "奇数"
        if int(str(rank)[0]) % 2 == 0:
            season = "偶数"

        return f"{rank},{season},{zone}"

    def csv_one_line_make_key(self, line: str) -> str:
        return line.split(',')[0]

    def compare_one_line(self, old_csv_str: str, new_csv_str: str) -> bool:
        return False


class TopNextMerger(RankInstanceCsvResolver):
    csv_header = "榜单ID,赛季类型,榜单类型,分区"
    output_names = {"del": "del.txt", "mod": "conflict.txt"}

    def __init__(self, name: str, id_to_rule: dict, id_to_shard: dict, rank_rules: dict):
        self.template = TopNextTemplate(rank_rules)
        skip_top_next_rules = self.template.skip_rules
        id_to_rule = {rank_id: rule for rank_id, rule in id_to_rule.items() if rule not in skip_top_next_rules}

        output_names = TopNextMerger.output_names.copy()
        output_names['add'] = name + '.csv'

        super().__init__(id_to_rule=id_to_rule, id_to_shard=id_to_shard, output_names=output_names,
                         output_dir=TopNextInstanceCsvResolver.default_output_dir(),
                         csv_header=TopNextMerger.csv_header)

    def csv_one_line(self, zone: int, rank: int, name: str, enable_shard: bool, enable_lite: bool) -> str:
        rank_type = self.template.get(name, 'template_id')

        if enable_lite:
            rank_type = 100 + rank_type

        season = "奇数"
        if int(str(rank)[0]) % 2 == 0:
            season = "偶数"

        return f"{rank},{season},{rank_type},{zone}"

    def csv_one_line_make_key(self, line: str) -> str:
        return line.split(',')[0]

    def compare_one_line(self, old_csv_str: str, new_csv_str: str) -> bool:
        return False


class Dumper:
    def __init__(self):
        resolver = RankXlsResolver().resolve()
        self.rules, self.shards, self.rank_rules = resolver.get('id_to_rule'), resolver.get('id_to_shard'), resolver.get('rank_rule')
        print('xlsx loaded')

    def dump_release_to_beta(self, name: str, release_zone: int, beta_zone: int):
        rules, shards, rank_rules = self.rules, self.shards, self.rank_rules

        print('xls loaded')

        topn_release = TopNMerger('release_' + name, rules, shards)
        topn_release.dump([release_zone], [release_zone], [])

        topn_beta = TopNMerger('beta_' + name, rules, shards)
        topn_beta.dump([beta_zone], [], [beta_zone])

        topn_merger = CSVMerger(lambda x: x[0])
        files = [TrankInstanceCsvResolver.default_output_dir() + '/' + f for f in
                 [f'release_{name}.csv', f'beta_{name}.csv']]
        topn_merger.merge_first(files[0])
        topn_merger.merge(files[1], lambda x: [x[-1]], '(BETA)')
        topn_merger.dump(TrankInstanceCsvResolver.default_output_dir() + f'/after_{name}.csv')

        topnext_release = TopNextMerger('release_' + name, rules, shards, rank_rules)
        topnext_release.dump([release_zone], [release_zone], [])

        topnext_beta = TopNextMerger('beta_' + name, rules, shards, rank_rules)
        topnext_beta.dump([beta_zone], [], [beta_zone])

        topnext_merger = CSVMerger(lambda x: x[0])
        files = [TopNextInstanceCsvResolver.default_output_dir() + '/' + f for f in
                 [f'release_{name}.csv', f'beta_{name}.csv']]
        topnext_merger.merge_first(files[0])
        topnext_merger.merge(files[1], lambda x: [x[-2], x[-1]], '(BETA)')
        topnext_merger.dump(TopNextInstanceCsvResolver.default_output_dir() + f'/after_{name}.csv')

    def dump_release_to_release(self, name: str, src_zone: int, dest_zone: int, offset: int):
        rules, shards, rank_rules = self.rules, self.shards, self.rank_rules

        print('xls loaded')

        topn_release = TopNMerger('release_from_' + name, rules, shards)
        topn_release.dump([src_zone], [src_zone], [])

        topn_beta = TopNMerger('release_to_' + name, rules, shards)
        topn_beta.dump([dest_zone], [dest_zone], [])

        topn_merger = CSVMerger(lambda x: x[0])
        files = [TrankInstanceCsvResolver.default_output_dir() + '/' + f for f in
                 [f'release_from_{name}.csv', f'release_to_{name}.csv']]
        topn_merger.merge_first(files[0])
        topn_merger.merge(files[1], lambda x: [x[-1]], '(DEST)')
        topn_merger.dump(TrankInstanceCsvResolver.default_output_dir() + f'/move_{name}.csv')

        topnext_release = TopNextMerger('release_from_' + name, rules, shards, rank_rules)
        topnext_release.dump([src_zone], [src_zone], [])

        topnext_beta = TopNextMerger('release_to_' + name, rules, shards, rank_rules)
        topnext_beta.dump([dest_zone], [dest_zone], [])

        topnext_merger = CSVMerger(lambda x: x[0])
        files = [TopNextInstanceCsvResolver.default_output_dir() + '/' + f for f in
                 [f'release_from_{name}.csv', f'release_to_{name}.csv']]
        topnext_merger.merge_first(files[0])
        topnext_merger.merge(files[1], lambda x: [x[-2], x[-1]], '(DEST)')
        topnext_merger.dump(TopNextInstanceCsvResolver.default_output_dir() + f'/move_{name}.csv')

    def move(self, from_wx, to_wx, from_qq, to_qq):
        self.dump_release_to_release('wx', from_wx, to_wx, 0)
        self.dump_release_to_release('qq', from_qq, to_qq, 10000000)

        appender = CSVAppender()
        appender.append_first(TrankInstanceCsvResolver.default_output_dir() + f'/move_wx.csv')
        appender.append(TrankInstanceCsvResolver.default_output_dir() + f'/move_qq.csv')
        appender.dump(TrankInstanceCsvResolver.default_output_dir() + f'/move.csv')

        appender = CSVAppender()
        appender.append_first(TopNextInstanceCsvResolver.default_output_dir() + f'/move_wx.csv')
        appender.append(TopNextInstanceCsvResolver.default_output_dir() + f'/move_qq.csv')
        appender.dump(TopNextInstanceCsvResolver.default_output_dir() + f'/move.csv')


def merge_topn(zone_wx, zone_qq, zone_merged, release: bool = True, release_merged: bool = True):
    resolver = RankXlsResolver().resolve()
    rules, shards = resolver.get('id_to_rule'), resolver.get('id_to_shard')

    topn_qq = TopNMerger('qq', rules, shards)
    topn_qq.dump([zone_qq], [zone_qq] if release else [], [])

    topn_wx = TopNMerger('wx', rules, shards)
    topn_wx.dump([zone_wx], [zone_wx] if release else [], [])

    topn_merged = TopNMerger('merged', rules, shards)
    topn_merged.dump([zone_merged], [zone_merged] if release_merged else [], [])

    topn_merger = CSVMerger(lambda x: x[0])
    files = [TrankInstanceCsvResolver.default_output_dir() + '/' + f for f in ['wx.csv', 'qq.csv', 'merged.csv']]
    topn_merger.merge_first(files[0])
    topn_merger.merge(files[1], lambda x: [x[-1]], 'QQ')
    topn_merger.merge(files[2], lambda x: [x[-1]], 'MERGED')
    topn_merger.dump(TrankInstanceCsvResolver.default_output_dir() + '/after.csv')


def merge_topnext(zone_wx, zone_qq, zone_merged, release: bool = True, release_merged: bool = True):
    resolver = RankXlsResolver().resolve()
    rules, shards = resolver.get('id_to_rule'), resolver.get('id_to_shard')

    topnext_qq = TopNMerger('qq', rules, shards)
    topnext_qq.dump([zone_qq], [], [zone_qq] if release else [])

    topnext_wx = TopNMerger('wx', rules, shards)
    topnext_wx.dump([zone_wx], [], [zone_wx] if release else [])

    topnext_merged = TopNMerger('merged', rules, shards)
    topnext_merged.dump([zone_merged], [], [zone_merged] if release_merged else [])

    topnext_merger = CSVMerger(lambda x: x[0])
    files = [TopNextInstanceCsvResolver.default_output_dir() + '/' + f for f in ['wx.csv', 'qq.csv', 'merged.csv']]
    topnext_merger.merge_first(files[0])
    topnext_merger.merge(files[1], lambda x: [x[-2], x[-1]], 'QQ')
    topnext_merger.merge(files[2], lambda x: [x[-2], x[-1]], 'MERGED')
    topnext_merger.dump(TopNextInstanceCsvResolver.default_output_dir() + '/after.csv')


if __name__ == '__main__':
    Dumper().move(1002, 5, 2001, 5)

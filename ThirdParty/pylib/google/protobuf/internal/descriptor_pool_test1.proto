// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto2";

package google.protobuf.python.internal;


message DescriptorPoolTest1 {
  extensions 1000 to max;

  enum NestedEnum {
    ALPHA = 1;
    BETA = 2;
  }

  optional NestedEnum nested_enum = 1 [default = BETA];

  message NestedMessage {
    enum NestedEnum {
      EPSILON = 5;
      ZETA = 6;
    }
    optional NestedEnum nested_enum = 1 [default = ZETA];
    optional string nested_field = 2 [default = "beta"];
    optional DeepNestedMessage deep_nested_message = 3;

    message DeepNestedMessage {
      enum NestedEnum {
        ETA = 7;
        THETA = 8;
      }
      optional NestedEnum nested_enum = 1 [default = ETA];
      optional string nested_field = 2 [default = "theta"];
    }
  }

  optional NestedMessage nested_message = 2;
}

message DescriptorPoolTest2 {
  enum NestedEnum {
    GAMMA = 3;
    DELTA = 4;
  }

  optional NestedEnum nested_enum = 1 [default = GAMMA];

  message NestedMessage {
    enum NestedEnum {
      IOTA = 9;
      KAPPA = 10;
    }
    optional NestedEnum nested_enum = 1 [default = IOTA];
    optional string nested_field = 2 [default = "delta"];
    optional DeepNestedMessage deep_nested_message = 3;

    message DeepNestedMessage {
      enum NestedEnum {
        LAMBDA = 11;
        MU = 12;
      }
      optional NestedEnum nested_enum = 1 [default = MU];
      optional string nested_field = 2 [default = "lambda"];
    }
  }

  optional NestedMessage nested_message = 2;
}

<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="toc.hide.show">
<refmeta>
<refentrytitle>toc.hide.show</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">boolean</refmiscinfo>
</refmeta>
<refnamediv>
<refname>toc.hide.show</refname>
<refpurpose>Enable hide/show button for ToC frame</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="toc.hide.show.frag">
<xsl:param name="toc.hide.show" select="0"/>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>If non-zero, JavaScript (and an additional icon, see
<link linkend="hidetoc.image">hidetoc.image</link> and
<link linkend="hidetoc.image">showtoc.image</link>) is added to each slide
to allow the ToC panel to be <quote>toggled</quote> on each panel.</para>

<note><para>There is a bug in Mozilla 1.0 (at least as of CR3) that causes
the browser to reload the titlepage when this feature is used.</para></note>

</refsection>
</refentry>

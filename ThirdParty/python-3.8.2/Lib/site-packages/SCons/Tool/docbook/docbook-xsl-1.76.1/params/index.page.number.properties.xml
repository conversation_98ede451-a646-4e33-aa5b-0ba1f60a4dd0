<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="index.page.number.properties">
<refmeta>
<refentrytitle>index.page.number.properties</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">attribute set</refmiscinfo>
</refmeta>
<refnamediv>
<refname>index.page.number.properties</refname>
<refpurpose>Properties associated with index page numbers</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="index.page.number.properties.frag">
<xsl:attribute-set name="index.page.number.properties">
</xsl:attribute-set>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>Properties associated with page numbers in indexes. 
Changing color to indicate the page number is a link is
one possibility.
</para>

</refsection>
</refentry>

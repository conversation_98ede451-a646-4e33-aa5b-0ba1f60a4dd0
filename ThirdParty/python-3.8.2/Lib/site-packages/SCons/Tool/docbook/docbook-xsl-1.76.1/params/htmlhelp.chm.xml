<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="htmlhelp.chm">
<refmeta>
<refentrytitle>htmlhelp.chm</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">string</refmiscinfo>
</refmeta>
<refnamediv>
<refname>htmlhelp.chm</refname>
<refpurpose>Filename of output HTML Help file.</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="htmlhelp.chm.frag">
<xsl:param name="htmlhelp.chm">htmlhelp.chm</xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>Set the  name of resulting CHM file</para>

</refsection>
</refentry>

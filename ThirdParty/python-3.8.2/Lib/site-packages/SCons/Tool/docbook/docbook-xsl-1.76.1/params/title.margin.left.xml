<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="title.margin.left">
<refmeta>
<refentrytitle>title.margin.left</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">length</refmiscinfo>
</refmeta>
<refnamediv>
<refname>title.margin.left</refname>
<refpurpose>Adjust the left margin for titles</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="title.margin.left.frag">
<xsl:param name="title.margin.left">
  <xsl:choose>
    <xsl:when test="$fop.extensions != 0">-4pc</xsl:when>
    <xsl:when test="$passivetex.extensions != 0">0pt</xsl:when>
    <xsl:otherwise>0pt</xsl:otherwise>
  </xsl:choose>
</xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>This parameter provides
the means of adjusting the left margin for titles
when the XSL-FO processor being used is
an old version of FOP (0.25 and earlier).
It is only useful when the <parameter>fop.extensions</parameter>
is nonzero.</para>

<para>The left margin of the body region
is calculated to include this space,
and titles are outdented to the left outside
the body region by this amount,
effectively leaving titles at the intended left margin
and the body text indented.
Currently this method is only used for old FOP because
it cannot properly use the <parameter>body.start.indent</parameter>
parameter.
</para>
<para>
The default value when the <parameter>fop.extensions</parameter>
parameter is nonzero is -4pc, which means the
body text is indented 4 picas relative to
the titles.
The default value when the <parameter>fop.extensions</parameter>
parameter equals zero is 0pt, and
the body indent should instead be specified
using the <parameter>body.start.indent</parameter>
parameter.
</para>
<para>
If you set the value to zero, be sure to still include
a unit indicator such as <literal>0pt</literal>, or
the FO processor will report errors.
</para>

</refsection>
</refentry>

The SciPy repository and source distributions bundle a number of libraries that
are compatibly licensed.  We list these here.

Name: Numpydoc
Files: doc/sphinxext/numpydoc/*
License: 2-clause BSD
  For details, see doc/sphinxext/LICENSE.txt

Name: scipy-sphinx-theme
Files: doc/scipy-sphinx-theme/*
License: 3-clause BSD, PSF and Apache 2.0
  For details, see doc/sphinxext/LICENSE.txt

Name: Decorator
Files: scipy/_lib/decorator.py
License: 2-clause BSD
  For details, see the header inside scipy/_lib/decorator.py

Name: ID
Files: scipy/linalg/src/id_dist/*
License: 3-clause BSD
  For details, see scipy/linalg/src/id_dist/doc/doc.tex

Name: L-BFGS-B
Files: scipy/optimize/lbfgsb/*
License: BSD license
  For details, see scipy/optimize/lbfgsb/README

Name: LAPJVsp
Files: scipy/sparse/csgraph/_matching.pyx
License: 3-clause BSD
Copyright 1987-, A. Vol<PERSON>/Amsterdam School of Economics,
                 University of Amsterdam

  Distributed under 3-clause BSD license with permission from
  University of Amsterdam.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

  1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

  2. Redistributions in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.

  3. Neither the name of the copyright holder nor the names of its contributors
     may be used to endorse or promote products derived from this software
    without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.


Name: SuperLU
Files: scipy/sparse/linalg/dsolve/SuperLU/*
License: 3-clause BSD
  For details, see scipy/sparse/linalg/dsolve/SuperLU/License.txt

Name: ARPACK
Files: scipy/sparse/linalg/eigen/arpack/ARPACK/*
License: 3-clause BSD
  For details, see scipy/sparse/linalg/eigen/arpack/ARPACK/COPYING

Name: Qhull
Files: scipy/spatial/qhull/*
License: Qhull license (BSD-like)
  For details, see scipy/spatial/qhull/COPYING.txt

Name: Cephes
Files: scipy/special/cephes/*
License: 3-clause BSD
  Distributed under 3-clause BSD license with permission from the author,
  see https://lists.debian.org/debian-legal/2004/12/msg00295.html

  Cephes Math Library Release 2.8:  June, 2000
  Copyright 1984, 1995, 2000 by Stephen L. Moshier

  This software is derived from the Cephes Math Library and is
  incorporated herein by permission of the author.

  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
      * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
      * Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
      * Neither the name of the <organization> nor the
        names of its contributors may be used to endorse or promote products
        derived from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Name: Faddeeva
Files: scipy/special/Faddeeva.*
License: MIT
  Copyright (c) 2012 Massachusetts Institute of Technology

  Permission is hereby granted, free of charge, to any person obtaining
  a copy of this software and associated documentation files (the
  "Software"), to deal in the Software without restriction, including
  without limitation the rights to use, copy, modify, merge, publish,
  distribute, sublicense, and/or sell copies of the Software, and to
  permit persons to whom the Software is furnished to do so, subject to
  the following conditions:

  The above copyright notice and this permission notice shall be
  included in all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
  LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
  OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
  WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Name: qd
Files: scipy/special/cephes/dd_*.[ch]
License: modified BSD license ("BSD-LBNL-License.doc")
  This work was supported by the Director, Office of Science, Division
  of Mathematical, Information, and Computational Sciences of the
  U.S. Department of Energy under contract numbers DE-AC03-76SF00098 and
  DE-AC02-05CH11231.

  Copyright (c) 2003-2009, The Regents of the University of California,
  through Lawrence Berkeley National Laboratory (subject to receipt of
  any required approvals from U.S. Dept. of Energy) All rights reserved.

  1. Redistribution and use in source and binary forms, with or
  without modification, are permitted provided that the following
  conditions are met:

  (1) Redistributions of source code must retain the copyright
  notice, this list of conditions and the following disclaimer.

  (2) Redistributions in binary form must reproduce the copyright
  notice, this list of conditions and the following disclaimer in
  the documentation and/or other materials provided with the
  distribution.

  (3) Neither the name of the University of California, Lawrence
  Berkeley National Laboratory, U.S. Dept. of Energy nor the names
  of its contributors may be used to endorse or promote products
  derived from this software without specific prior written
  permission.

  2. THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

  3. You are under no obligation whatsoever to provide any bug fixes,
  patches, or upgrades to the features, functionality or performance of
  the source code ("Enhancements") to anyone; however, if you choose to
  make your Enhancements available either publicly, or directly to
  Lawrence Berkeley National Laboratory, without imposing a separate
  written license agreement for such Enhancements, then you hereby grant
  the following license: a non-exclusive, royalty-free perpetual license
  to install, use, modify, prepare derivative works, incorporate into
  other computer software, distribute, and sublicense such enhancements
  or derivative works thereof, in binary and source code form.

Name: pypocketfft
Files: scipy/fft/_pocketfft/[pocketfft.h, pypocketfft.cxx]
License: 3-Clause BSD
  For details, see scipy/fft/_pocketfft/LICENSE.md

Name: uarray
Files: scipy/_lib/uarray/*
License: 3-Clause BSD
  For details, see scipy/_lib/uarray/LICENSE

Name: ampgo
Files: benchmarks/benchmarks/go_benchmark_functions/*.py
License: MIT
  Functions for testing global optimizers, forked from the AMPGO project,
  https://code.google.com/archive/p/ampgo

Name: pybind11
Files: no source files are included, however pybind11 binary artifacts are
  included with every binary build of SciPy.
License:
  Copyright (c) 2016 Wenzel Jakob <<EMAIL>>, All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

  1. Redistributions of source code must retain the above copyright notice, this
     list of conditions and the following disclaimer.

  2. Redistributions in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

  3. Neither the name of the copyright holder nor the names of its contributors
     may be used to endorse or promote products derived from this software
     without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Name: HiGHS
Files: scipy/optimize/_highs/*
License: MIT
  For details, see scipy/optimize/_highs/LICENCE

Name: Boost
Files: scipy/_lib/boost/*
License: Boost Software License - Version 1.0
  For details, see scipy/_lib/boost/LICENSE_1_0.txt

Name: Biasedurn
Files: scipy/stats/biasedurn/*
License 3-Clause BSD
  For details, see scipy/stats/biasedurn/license.txt

Name: UNU.RAN
Files: scipy/_lib/unuran/*
License 3-Clause BSD
  For details, see scipy/_lib/unuran/license.txt

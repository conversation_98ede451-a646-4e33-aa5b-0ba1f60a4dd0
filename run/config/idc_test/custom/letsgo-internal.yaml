outip: test.ymzx.qq.com

# gamesvr对外ip和端口
gamesvr_out:
  ip: test.ymzx.qq.com

tcaplus:
  appid: 335
  secret: 0211EA0908151A36
  host: tcp://set18.tcapdir.idc.tcaplus.db:9999

tbuspp:
  gameid: 593815947
  passwd: f3361aa39618f68248377aa1a93294b8

# gcloud设置  G6 dsa/dsc配置
gcloud:
  game_id: 593815947
  game_key: f3361aa39618f68248377aa1a93294b8
  log_level: DEBUG

chat_config:
  public_chat_switch: true # 公共聊天模块

module_config:
  club_switch: true # 公会模块

ds_need_private: 1
enable_ds_seed: "false"
ds_crossrouter: 0
cross_route_type: 0
is_need_recovery: true
ds_report_full_game_sessions_second: 30

raffle:
  wechat_bot_key: "783f6188-eca2-4005-9b20-8444b6979200"

#new version compatible switch
new_version_comp_open: "true"
ignor_version_comp_excel_check: "false"  # 绕过兼容表检查, new_version_comp_open 为true时生效

#tabtest
report_tid: "iegab_sdk_report_ymzx"

# 平台白名单配置
user_bind_package:
  open: true

# 需要自定义配置在这里覆盖common.yaml的
helm:
  version: 33.2.33
  common:
    hookrun:
      enabled: true
      count: 5
      interval: 30s
  game-svr:
    dump_bench: true
    replicaCount: 10
  room-svr:
    replicaCount: 10
  chat-svr:
    replicaCount: 6
  dsc-svr-1:
    g6EnvRegion: shanghai
  dsa-svr-1:
    g6EnvRegion: shanghai
  dsc-svr-2:
    g6EnvRegion: nanjing
  dsa-svr-2:
    g6EnvRegion: nanjing
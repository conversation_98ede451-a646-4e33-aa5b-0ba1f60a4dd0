#外部访问的ip或域名
outip: **********

# gamesvr对外ip和端口
gamesvr_out:
  ip: **********
  port: 8101

tcaplus:
  host: tcp://set3.tcapdir.tcaplusdev.oa.com:9999

env:
  deploy_mode: starttoken
  deploy_for: test

#ds config
ds_health_check_second: 120
ds_debug_time: 120

dev_env_process_list:
  - { process: tbusppmetrics, belong: world, instance_no: 1 }
  - { process: tbusppbaseagent, belong: world, instance_no: 1 }
  - { process: tconndcluster, belong: world, instance_no: 1 }
  - { process: dirsvr, belong: world, instance_no: 1 }
  - { process: gamesvr, belong: world, instance_no: 1 }
  - { process: proxysvr, belong: world, instance_no: 1 }
  - { process: matchsvr, belong: world, instance_no: 1 }
  - { process: roomsvr, belong: world, instance_no: 1 }
  - { process: dsc, belong: world, instance_no: 1, g6_env_region: dev }
  - { process: dsa, belong: world, instance_no: 1, g6_env_region: dev }
  - { process: battlesvr, belong: world, instance_no: 1 }
  - { process: lobbysvr, belong: world, instance_no: 1 }
  - { process: ugcsvr, belong: world, instance_no: 1 }
  - { process: ugcplatsvr, belong: world, instance_no: 1 }
  - { process: aigcsvr, belong: world, instance_no: 1 }
  - { process: clubsvr, belong: world, instance_no: 1 }
  - { process: dscallocsvr, belong: world, instance_no: 1 }
  - { process: seqsvr, belong: world, instance_no: 1 }
  #- { process: matchallocsvr, belong: world, instance_no: 1 }
  #- { process: routesvr, belong: world, instance_no: 1 }
  #- { process: xiaowosvr, belong: world, instance_no: 1 }
  - { process: chatsvr, belong: world, instance_no: 1 }

java:
  heap_size:
    clubsvr: 512m
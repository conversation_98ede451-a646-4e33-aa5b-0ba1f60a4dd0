syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";

message ReportEntryConf
{
    option (resKey) = "id";
    optional int32 id = 1;
    optional int32 reportScene = 2;
    repeated int32 reportContentList = 3;
    optional string reportName = 4;
    optional bool isCanMultipleChoice = 5;
}

message table_ReportEntryConf
{
    repeated ReportEntryConf rows = 1;
}

message ReportContentConf
{
    option (resKey) = "id";
    optional int32 id = 1;
    optional string reportContent = 2;
    optional string functionName = 3;
    optional int32 reportReason = 4;
    optional int32 reportCategory = 5;
    optional int32 reportEntrance = 6;
    optional int32 needPic = 7;
    repeated int32 supportGameTypeList = 8;
}

message table_ReportContentConf
{
    repeated ReportContentConf rows = 1;
}


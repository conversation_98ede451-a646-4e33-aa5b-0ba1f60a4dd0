com.tencent.wea.xlsRes.starp.table_BackpackItem
excel/xls/D_道具表_时装.xlsx sheet:背饰
rows {
  id: 620106
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "蜜罐儿"
  desc: "送给你新鲜的春天味道！"
  icon: "CDN:Icon_Wing_056_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620104
    fashionValue: 25
    belongToGroup: 620105
    belongToGroup: 620106
  }
  resourceConf {
    model: "SM_Wing_056"
    material: "MI_Wing_056_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71004
  scaleTimes: 160
  shareTexts: "随身携带的甜蜜味道"
  shareTexts: "香香甜甜，我的最爱"
  shareTexts: "将芬芳洒满人间"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620107
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "胜利队头盔"
  desc: "元梦之星 & 奥特曼联动饰品，为你保驾护航"
  icon: "CDN:Icon_Wing_076"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_076"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "元梦之星 & 奥特曼联动饰品，为你保驾护航"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60059
  suitName: "胜利队头盔"
  suitIcon: "CDN:Icon_Wing_076"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620108
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "年年有余"
  desc: "年年有余，岁岁平安"
  icon: "CDN:Icon_Wing_059"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_059"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "如鱼得水，奔赴星河"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60060
  suitName: "年年有余"
  suitIcon: "CDN:Icon_Wing_059"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620109
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "糖葫芦"
  desc: "酸里面裹着甜"
  icon: "CDN:Icon_Wing_071"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_071"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "一起实现糖葫芦自由"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60061
  suitName: "糖葫芦"
  suitIcon: "CDN:Icon_Wing_071"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620110
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "中国结"
  desc: "心似双丝网，中有千千结"
  icon: "CDN:Icon_Wing_072"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_072"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "千千结千千意，愿美好与你环环相扣"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60062
  suitName: "中国结"
  suitIcon: "CDN:Icon_Wing_072"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620111
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "莫生气"
  desc: "别人生气我不气"
  icon: "CDN:Icon_Wing_073"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_073"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "我心态超棒的！"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60063
  suitName: "莫生气"
  suitIcon: "CDN:Icon_Wing_073"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620112
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "如意金箍棒"
  desc: "吃俺老孙一棒！"
  icon: "CDN:Icon_Wing_088"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_088"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "东海龙宫镇海之物"
  beginTime {
    seconds: 1704988800
  }
  suitId: 60064
  suitName: "如意金箍棒"
  suitIcon: "CDN:Icon_Wing_088"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620113
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "红包包"
  desc: "恭喜发财，红包拿来"
  icon: "CDN:Icon_Wing_083"
  getWay: "限时礼包"
  jumpId: 1064
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_083"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "听说给我发红包的都发财了"
  minVer: "1.3.26.61"
  beginTime {
    seconds: 1730736000
  }
  suitId: 60065
  suitName: "红包包"
  suitIcon: "CDN:Icon_Wing_083"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620114
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "发财扇"
  desc: "心无旁骛，直取财富"
  icon: "CDN:Icon_Wing_084"
  getWay: "山海通行证"
  jumpId: 9
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_084"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "扇来好运风"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1706198400
  }
  suitId: 60066
  suitName: "发财扇"
  suitIcon: "CDN:Icon_Wing_084"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620115
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "显眼包"
  desc: "背上进入二维世界"
  icon: "CDN:Icon_Wing_077"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_077"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "这个显眼包我当定了"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60067
  suitName: "显眼包"
  suitIcon: "CDN:Icon_Wing_077"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620116
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "龙崽福袋"
  desc: "龙年的好运和福气全部送给你~"
  icon: "CDN:Icon_Wing_069"
  getWay: "携友同行"
  jumpId: 132
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_069"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "好运一条龙，幸福一条龙"
  shareTexts: "家有一龙，如有一宝"
  shareTexts: "开年好运请接好！"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1737043200
  }
  suitId: 60068
  suitName: "龙崽福袋"
  suitIcon: "CDN:Icon_Wing_069"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620117
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "龙崽福袋"
  desc: "龙年的好运和福气全部送给你~"
  icon: "CDN:Icon_Wing_069_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620116
    fashionValue: 25
    belongToGroup: 620117
    belongToGroup: 620118
  }
  resourceConf {
    model: "SM_Wing_069"
    material: "MI_Wing_069_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71014
  scaleTimes: 160
  shareTexts: "好运一条龙，幸福一条龙"
  shareTexts: "家有一龙，如有一宝"
  shareTexts: "开年好运请接好！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620118
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "龙崽福袋"
  desc: "龙年的好运和福气全部送给你~"
  icon: "CDN:Icon_Wing_069_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620116
    fashionValue: 25
    belongToGroup: 620117
    belongToGroup: 620118
  }
  resourceConf {
    model: "SM_Wing_069"
    material: "MI_Wing_069_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71015
  scaleTimes: 160
  shareTexts: "好运一条龙，幸福一条龙"
  shareTexts: "家有一龙，如有一宝"
  shareTexts: "开年好运请接好！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620119
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "醒狮背包"
  desc: "身披锦绣，舞动乾坤"
  icon: "CDN:Icon_Wing_080"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_080"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "祝你新的一天，福气满满，幸运多多！"
  shareTexts: "醒狮的吉祥与幸运，分享给你！"
  shareTexts: "一秒变国潮达人！"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60069
  suitName: "醒狮背包"
  suitIcon: "CDN:Icon_Wing_080"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620120
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "醒狮背包"
  desc: "身披锦绣，舞动乾坤"
  icon: "CDN:Icon_Wing_080_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620119
    fashionValue: 25
    belongToGroup: 620120
    belongToGroup: 620121
  }
  resourceConf {
    model: "SM_Wing_080"
    material: "MI_Wing_080_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71017
  scaleTimes: 160
  shareTexts: "祝你新的一天，福气满满，幸运多多！"
  shareTexts: "醒狮的吉祥与幸运，分享给你！"
  shareTexts: "一秒变国潮达人！"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620121
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "醒狮背包"
  desc: "身披锦绣，舞动乾坤"
  icon: "CDN:Icon_Wing_080_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620119
    fashionValue: 25
    belongToGroup: 620120
    belongToGroup: 620121
  }
  resourceConf {
    model: "SM_Wing_080"
    material: "MI_Wing_080_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71018
  scaleTimes: 160
  shareTexts: "祝你新的一天，福气满满，幸运多多！"
  shareTexts: "醒狮的吉祥与幸运，分享给你！"
  shareTexts: "一秒变国潮达人！"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620122
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福运爆竹"
  desc: "爆竹声中一岁除，春风送暖入屠苏"
  icon: "CDN:Icon_Wing_081"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_081"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "烟火向星辰，所愿皆成真"
  shareTexts: "噼里啪啦放鞭炮啦！"
  shareTexts: "把对你的祝福都放在烟花里了"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60070
  suitName: "福运爆竹"
  suitIcon: "CDN:Icon_Wing_081"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620123
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福运爆竹"
  desc: "爆竹声中一岁除，春风送暖入屠苏"
  icon: "CDN:Icon_Wing_081_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620122
    fashionValue: 25
    belongToGroup: 620123
    belongToGroup: 620124
  }
  resourceConf {
    model: "SM_Wing_081"
    material: "MI_Wing_081_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71020
  scaleTimes: 160
  shareTexts: "烟火向星辰，所愿皆成真"
  shareTexts: "噼里啪啦放鞭炮啦！"
  shareTexts: "把对你的祝福都放在烟花里了"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620124
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福运爆竹"
  desc: "爆竹声中一岁除，春风送暖入屠苏"
  icon: "CDN:Icon_Wing_081_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620122
    fashionValue: 25
    belongToGroup: 620123
    belongToGroup: 620124
  }
  resourceConf {
    model: "SM_Wing_081"
    material: "MI_Wing_081_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71021
  scaleTimes: 160
  shareTexts: "烟火向星辰，所愿皆成真"
  shareTexts: "噼里啪啦放鞭炮啦！"
  shareTexts: "把对你的祝福都放在烟花里了"
  shareOffset: -10
  shareOffset: 35
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620125
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "企鹅背包"
  desc: "暖绒绒的企鹅宝贝，温暖你的心"
  icon: "CDN:Icon_Wing_065"
  getWay: "一元购背包"
  jumpId: 82
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_065"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "快把知识都装我包包里！"
  shareTexts: "快来看看我的企鹅宝贝！"
  shareTexts: "背起书包上学去咯~"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1712160000
  }
  suitId: 60071
  suitName: "企鹅背包"
  suitIcon: "CDN:Icon_Wing_065"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620126
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "企鹅背包"
  desc: "暖绒绒的企鹅宝贝，温暖你的心"
  icon: "CDN:Icon_Wing_065_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620125
    fashionValue: 25
    belongToGroup: 620126
    belongToGroup: 620127
  }
  resourceConf {
    model: "SM_Wing_065"
    material: "MI_Wing_065_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71023
  scaleTimes: 160
  shareTexts: "快把知识都装我包包里！"
  shareTexts: "快来看看我的企鹅宝贝！"
  shareTexts: "背起书包上学去咯~"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620127
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "企鹅背包"
  desc: "暖绒绒的企鹅宝贝，温暖你的心"
  icon: "CDN:Icon_Wing_065_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620125
    fashionValue: 25
    belongToGroup: 620126
    belongToGroup: 620127
  }
  resourceConf {
    model: "SM_Wing_065"
    material: "MI_Wing_065_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71024
  scaleTimes: 160
  shareTexts: "快把知识都装我包包里！"
  shareTexts: "快来看看我的企鹅宝贝！"
  shareTexts: "背起书包上学去咯~"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620128
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "一枝春"
  desc: "江南无所有，聊赠一枝春"
  icon: "CDN:Icon_Wing_066"
  getWay: "山海通行证"
  jumpId: 9
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_066"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "遥知不是雪，为有暗香来"
  shareTexts: "宝剑锋从磨砺出，梅花香自苦寒来"
  shareTexts: "想跟你一起去看，整个春天"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1706198400
  }
  suitId: 60072
  suitName: "一枝春"
  suitIcon: "CDN:Icon_Wing_066"
  shareOffset: -5
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620129
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "一枝春"
  desc: "江南无所有，聊赠一枝春"
  icon: "CDN:Icon_Wing_066_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620128
    fashionValue: 25
    belongToGroup: 620129
    belongToGroup: 620130
  }
  resourceConf {
    model: "SM_Wing_066"
    material: "MI_Wing_066_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71026
  scaleTimes: 160
  shareTexts: "遥知不是雪，为有暗香来"
  shareTexts: "宝剑锋从磨砺出，梅花香自苦寒来"
  shareTexts: "想跟你一起去看，整个春天"
  shareOffset: -5
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620130
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "一枝春"
  desc: "江南无所有，聊赠一枝春"
  icon: "CDN:Icon_Wing_066_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620128
    fashionValue: 25
    belongToGroup: 620129
    belongToGroup: 620130
  }
  resourceConf {
    model: "SM_Wing_066"
    material: "MI_Wing_066_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71027
  scaleTimes: 160
  shareTexts: "遥知不是雪，为有暗香来"
  shareTexts: "宝剑锋从磨砺出，梅花香自苦寒来"
  shareTexts: "想跟你一起去看，整个春天"
  shareOffset: -5
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620131
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "漂泊之钥"
  desc: "转动发条，开始流浪"
  icon: "CDN:Icon_Wing_075"
  getWay: "星光剧场"
  jumpId: 707
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_075"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "开启一趟说走就走的旅行"
  shareTexts: "一旦打入我的内心，你会发现另一个我"
  shareTexts: "这是打开我心扉的发条"
  minVer: "1.2.80.1"
  beginTime {
    seconds: 1711728000
  }
  suitId: 60073
  suitName: "漂泊之钥"
  suitIcon: "CDN:Icon_Wing_075"
  shareOffset: -8
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620132
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "漂泊之钥"
  desc: "转动发条，开始流浪"
  icon: "CDN:Icon_Wing_075_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620131
    fashionValue: 25
    belongToGroup: 620132
    belongToGroup: 620133
  }
  resourceConf {
    model: "SM_Wing_075"
    material: "MI_Wing_075_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71029
  scaleTimes: 160
  shareTexts: "开启一趟说走就走的旅行"
  shareTexts: "一旦打入我的内心，你会发现另一个我"
  shareTexts: "这是打开我心扉的发条"
  shareOffset: -8
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620133
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "漂泊之钥"
  desc: "转动发条，开始流浪"
  icon: "CDN:Icon_Wing_075_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620131
    fashionValue: 25
    belongToGroup: 620132
    belongToGroup: 620133
  }
  resourceConf {
    model: "SM_Wing_075"
    material: "MI_Wing_075_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71030
  scaleTimes: 160
  shareTexts: "开启一趟说走就走的旅行"
  shareTexts: "一旦打入我的内心，你会发现另一个我"
  shareTexts: "这是打开我心扉的发条"
  shareOffset: -8
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620134
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "绮梦灯"
  desc: "明灯自黑夜中来，映照前行的道路"
  icon: "CDN:Icon_Wing_062"
  getWay: "绮梦灯"
  jumpId: 181
  useType: IUTO_None
  outlookConf {
    fashionValue: 300
  }
  resourceConf {
    model: "SK_Wing_062"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001"
  }
  scaleTimes: 120
  shareTexts: "此情此景，谁与共赏？"
  shareTexts: "一起去灯会玩耍吧"
  shareTexts: "期待与你共同掌灯的每一天"
  minVer: "1.2.80.1"
  beginTime {
    seconds: 1707494400
  }
  suitId: 60074
  suitName: "绮梦灯"
  suitIcon: "CDN:Icon_Wing_062"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620135
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "绮梦灯"
  desc: "明灯自黑夜中来，映照前行的道路"
  icon: "CDN:Icon_Wing_062_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620134
    fashionValue: 35
    belongToGroup: 620135
    belongToGroup: 620136
  }
  resourceConf {
    model: "SK_Wing_062"
    material: "MI_Wing_062_1_HP01;MI_Wing_062_2_HP01"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001_HP01"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71032
  scaleTimes: 120
  shareTexts: "此情此景，谁与共赏？"
  shareTexts: "一起去灯会玩耍吧"
  shareTexts: "期待与你共同掌灯的每一天"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620136
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "绮梦灯"
  desc: "明灯自黑夜中来，映照前行的道路"
  icon: "CDN:Icon_Wing_062_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620134
    fashionValue: 35
    belongToGroup: 620135
    belongToGroup: 620136
  }
  resourceConf {
    model: "SK_Wing_062"
    material: "MI_Wing_062_1_HP02;MI_Wing_062_2_HP02"
    modelType: 2
    idleAnim: "AS_Wing_062_Idle_001_HP02"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71033
  scaleTimes: 120
  shareTexts: "此情此景，谁与共赏？"
  shareTexts: "一起去灯会玩耍吧"
  shareTexts: "期待与你共同掌灯的每一天"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620137
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "烈之翼"
  desc: "翱翔于九天之上，俯瞰尘埃"
  icon: "CDN:Icon_Wing_074"
  getWay: "赛季祈愿"
  jumpId: 175
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_074"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "每一次飞翔都引领着风的方向"
  shareTexts: "不仅是风的轻吟，更有雷霆的豪迈"
  shareTexts: "划破天际束缚，扶摇而上"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1706198400
  }
  suitId: 60075
  suitName: "烈之翼"
  suitIcon: "CDN:Icon_Wing_074"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620138
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "烈之翼"
  desc: "翱翔于九天之上，俯瞰尘埃"
  icon: "CDN:Icon_Wing_074_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620137
    fashionValue: 25
    belongToGroup: 620138
    belongToGroup: 620139
  }
  resourceConf {
    model: "SM_Wing_074"
    material: "MI_Wing_074_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71035
  scaleTimes: 160
  shareTexts: "每一次飞翔都引领着风的方向"
  shareTexts: "不仅是风的轻吟，更有雷霆的豪迈"
  shareTexts: "划破天际束缚，扶摇而上"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620139
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "烈之翼"
  desc: "翱翔于九天之上，俯瞰尘埃"
  icon: "CDN:Icon_Wing_074_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620137
    fashionValue: 25
    belongToGroup: 620138
    belongToGroup: 620139
  }
  resourceConf {
    model: "SM_Wing_074"
    material: "MI_Wing_074_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71036
  scaleTimes: 160
  shareTexts: "每一次飞翔都引领着风的方向"
  shareTexts: "不仅是风的轻吟，更有雷霆的豪迈"
  shareTexts: "划破天际束缚，扶摇而上"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620140
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福袋"
  desc: "吉祥好运，招财纳福"
  icon: "CDN:Icon_Wing_085"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_085"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "惊喜开出来！"
  shareTexts: "满满的福气，分一点给你也可以哦~"
  shareTexts: "福多不嫌少！"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60076
  suitName: "福袋"
  suitIcon: "CDN:Icon_Wing_085"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620141
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "鲤跃祥云"
  desc: "鲤跃祥云，招财进宝"
  icon: "CDN:Icon_Wing_064"
  getWay: "充值送好礼"
  jumpId: 171
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_064"
    modelType: 1
  }
  scaleTimes: 120
  shareTexts: "运气交给锦鲤，你只管努力就行"
  shareTexts: "接到这条锦鲤的人新年会走大运~"
  shareTexts: "大展宏图的时候到了！"
  minVer: "1.2.80.1"
  beginTime {
    seconds: 1707408000
  }
  suitId: 60077
  suitName: "鲤跃祥云"
  suitIcon: "CDN:Icon_Wing_064"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620142
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "鲤跃祥云"
  desc: "鲤跃祥云，招财进宝"
  icon: "CDN:Icon_Wing_064_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620141
    fashionValue: 25
    belongToGroup: 620142
    belongToGroup: 620143
  }
  resourceConf {
    model: "SM_Wing_064"
    material: "MI_Wing_064_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71038
  scaleTimes: 120
  shareTexts: "运气交给锦鲤，你只管努力就行"
  shareTexts: "接到这条锦鲤的人新年会走大运~"
  shareTexts: "大展宏图的时候到了！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620143
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "鲤跃祥云"
  desc: "鲤跃祥云，招财进宝"
  icon: "CDN:Icon_Wing_064_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620141
    fashionValue: 25
    belongToGroup: 620142
    belongToGroup: 620143
  }
  resourceConf {
    model: "SM_Wing_064"
    material: "MI_Wing_064_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71039
  scaleTimes: 120
  shareTexts: "运气交给锦鲤，你只管努力就行"
  shareTexts: "接到这条锦鲤的人新年会走大运~"
  shareTexts: "大展宏图的时候到了！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620144
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "丘比特之爱"
  desc: "爱心发射！"
  icon: "CDN:Icon_Wing_070"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_070"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "救救我救救我，我被爱情击中了！ "
  shareTexts: "嘘，别透露了爱情的通关秘语"
  shareTexts: "啊，我被击中了！"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60078
  suitName: "丘比特之爱"
  suitIcon: "CDN:Icon_Wing_070"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620145
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "丘比特之爱"
  desc: "爱心发射！"
  icon: "CDN:Icon_Wing_070_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620144
    fashionValue: 25
    belongToGroup: 620145
    belongToGroup: 620146
  }
  resourceConf {
    model: "SM_Wing_070"
    material: "MI_Wing_070_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71041
  scaleTimes: 160
  shareTexts: "救救我救救我，我被爱情击中了！ "
  shareTexts: "嘘，别透露了爱情的通关秘语"
  shareTexts: "啊，我被击中了！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620146
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "丘比特之爱"
  desc: "爱心发射！"
  icon: "CDN:Icon_Wing_070_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620144
    fashionValue: 25
    belongToGroup: 620145
    belongToGroup: 620146
  }
  resourceConf {
    model: "SM_Wing_070"
    material: "MI_Wing_070_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71042
  scaleTimes: 160
  shareTexts: "救救我救救我，我被爱情击中了！ "
  shareTexts: "嘘，别透露了爱情的通关秘语"
  shareTexts: "啊，我被击中了！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620147
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "奶龙蛋"
  desc: "你这龙还怪可爱的嘞！"
  icon: "CDN:Icon_Wing_082"
  getWay: "奶龙返场"
  jumpId: 1047
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_082"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "奇妙的礼物，适合有好奇心的你"
  shareTexts: "等待奇迹发生"
  shareTexts: "好想去探索未知的一切！"
  minVer: "1.3.7.97"
  beginTime {
    seconds: 1720454400
  }
  suitId: 60079
  suitName: "奶龙蛋"
  suitIcon: "CDN:Icon_Wing_082"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620148
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "九环锡杖"
  desc: "此锡杖上有九环，持在手中，不遭毒害"
  icon: "CDN:Icon_Wing_089"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_089"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "持我锡杖，不遭毒害"
  beginTime {
    seconds: 1704988800
  }
  suitId: 60080
  suitName: "九环锡杖"
  suitIcon: "CDN:Icon_Wing_089"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620149
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "沧海贝环"
  desc: "海潮流转不休，如梦如幻"
  icon: "CDN:Icon_Wing_060"
  getWay: "赛季祈愿"
  jumpId: 175
  useType: IUTO_None
  outlookConf {
    fashionValue: 300
  }
  resourceConf {
    model: "SK_Wing_060"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001"
  }
  scaleTimes: 110
  shareTexts: "听，海浪翻涌的声音"
  shareTexts: "想与你一起在海边漫步"
  shareTexts: "海潮涌动，犹如万马奔腾"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1706198400
  }
  suitId: 60081
  suitName: "沧海贝环"
  suitIcon: "CDN:Icon_Wing_060"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620150
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "沧海贝环"
  desc: "海潮流转不休，如梦如幻"
  icon: "CDN:Icon_Wing_060_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620149
    fashionValue: 35
    belongToGroup: 620150
    belongToGroup: 620151
  }
  resourceConf {
    model: "SK_Wing_060"
    material: "MI_Wing_060_1_HP01;MI_Wing_060_2_HP01"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001_HP01"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71045
  scaleTimes: 110
  shareTexts: "听，海浪翻涌的声音"
  shareTexts: "想与你一起在海边漫步"
  shareTexts: "海潮涌动，犹如万马奔腾"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620151
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "沧海贝环"
  desc: "海潮流转不休，如梦如幻"
  icon: "CDN:Icon_Wing_060_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620149
    fashionValue: 35
    belongToGroup: 620150
    belongToGroup: 620151
  }
  resourceConf {
    model: "SK_Wing_060"
    material: "MI_Wing_060_1_HP02;MI_Wing_060_2_HP02"
    physics: "SK_Wing_060_Physics"
    modelType: 2
    idleAnim: "AS_Wing_060_Idle_001_HP02"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71046
  scaleTimes: 110
  shareTexts: "听，海浪翻涌的声音"
  shareTexts: "想与你一起在海边漫步"
  shareTexts: "海潮涌动，犹如万马奔腾"
  shareOffset: -8
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620152
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 3
    itemNum: 20
  }
  quality: 1
  name: "冰晶花梦"
  desc: "春天是蝴蝶在冬天做的一场美梦"
  icon: "CDN:Icon_Wing_063"
  getWay: "星之恋空"
  jumpId: 628
  useType: IUTO_None
  outlookConf {
    fashionValue: 300
  }
  resourceConf {
    model: "SK_Wing_063"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001"
  }
  scaleTimes: 120
  shareTexts: "冬天来了，春天还会远么？"
  shareTexts: "比梦境还要甜美的相遇"
  shareTexts: "白雪映照羽翼，璀璨之光在冰晶上起舞"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1731600000
  }
  suitId: 60082
  suitName: "冰晶花梦"
  suitIcon: "CDN:Icon_Wing_063"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620153
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "冰晶花梦"
  desc: "春天是蝴蝶在冬天做的一场美梦"
  icon: "CDN:Icon_Wing_063_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620152
    fashionValue: 35
    belongToGroup: 620153
    belongToGroup: 620154
  }
  resourceConf {
    model: "SK_Wing_063"
    material: "MI_Wing_063_HP01"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001_HP01"
    materialSlot: "Wing"
  }
  commodityId: 71048
  scaleTimes: 120
  shareTexts: "冬天来了，春天还会远么？"
  shareTexts: "比梦境还要甜美的相遇"
  shareTexts: "白雪映照羽翼，璀璨之光在冰晶上起舞"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620154
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "冰晶花梦"
  desc: "春天是蝴蝶在冬天做的一场美梦"
  icon: "CDN:Icon_Wing_063_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620152
    fashionValue: 35
    belongToGroup: 620153
    belongToGroup: 620154
  }
  resourceConf {
    model: "SK_Wing_063"
    material: "MI_Wing_063_HP02"
    modelType: 2
    idleAnim: "AS_Wing_063_Idle_001_HP02"
    materialSlot: "Wing"
  }
  commodityId: 71049
  scaleTimes: 120
  shareTexts: "冬天来了，春天还会远么？"
  shareTexts: "比梦境还要甜美的相遇"
  shareTexts: "白雪映照羽翼，璀璨之光在冰晶上起舞"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 10
}
rows {
  id: 620155
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "玫瑰之翼"
  desc: "为玫瑰插上翅膀，带着浪漫飞向你"
  icon: "CDN:Icon_Wing_067"
  getWay: "冰雪玫瑰"
  jumpId: 183
  useType: IUTO_None
  outlookConf {
    fashionValue: 300
  }
  resourceConf {
    model: "SK_Wing_067"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001"
  }
  scaleTimes: 120
  shareTexts: "每一次展翅，都在诉说不朽的浪漫故事"
  shareTexts: "带着梦幻的色彩，翱翔在幻想天空"
  shareTexts: "这份爱，亘古不变"
  minVer: "1.2.80.1"
  beginTime {
    seconds: 1708704000
  }
  suitId: 60083
  suitName: "玫瑰之翼"
  suitIcon: "CDN:Icon_Wing_067"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620156
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "玫瑰之翼"
  desc: "为玫瑰插上翅膀，带着浪漫飞向你"
  icon: "CDN:Icon_Wing_067_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620155
    fashionValue: 35
    belongToGroup: 620156
    belongToGroup: 620157
  }
  resourceConf {
    model: "SK_Wing_067"
    material: "MI_Wing_067_HP01_1;MI_Wing_067_HP01_2"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001_HP01"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71051
  scaleTimes: 120
  shareTexts: "每一次展翅，都在诉说不朽的浪漫故事"
  shareTexts: "带着梦幻的色彩，翱翔在幻想天空"
  shareTexts: "这份爱，亘古不变"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620157
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "玫瑰之翼"
  desc: "为玫瑰插上翅膀，带着浪漫飞向你"
  icon: "CDN:Icon_Wing_067_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620155
    fashionValue: 35
    belongToGroup: 620156
    belongToGroup: 620157
  }
  resourceConf {
    model: "SK_Wing_067"
    material: "MI_Wing_067_HP02_1;MI_Wing_067_HP02_2"
    modelType: 2
    idleAnim: "AS_Wing_067_Idle_001_HP02"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71052
  scaleTimes: 120
  shareTexts: "每一次展翅，都在诉说不朽的浪漫故事"
  shareTexts: "带着梦幻的色彩，翱翔在幻想天空"
  shareTexts: "这份爱，亘古不变"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620158
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星宝花束"
  desc: "把可爱一次性打包送你"
  icon: "CDN:Icon_Wing_068"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_068"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "星宝的花语，是元气可爱噢"
  shareTexts: "送礼首选"
  shareTexts: "谁能拒绝星宝花束呢？"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60084
  suitName: "星宝花束"
  suitIcon: "CDN:Icon_Wing_068"
  shareOffset: -15
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620159
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星宝花束"
  desc: "把可爱一次性打包送你"
  icon: "CDN:Icon_Wing_068_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620158
    fashionValue: 25
    belongToGroup: 620159
    belongToGroup: 620160
  }
  resourceConf {
    model: "SM_Wing_068"
    material: "MI_Wing_068_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71054
  scaleTimes: 160
  shareTexts: "星宝的花语，是元气可爱噢"
  shareTexts: "送礼首选"
  shareTexts: "谁能拒绝星宝花束呢？"
  shareOffset: -15
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620160
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星宝花束"
  desc: "把可爱一次性打包送你"
  icon: "CDN:Icon_Wing_068_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620158
    fashionValue: 25
    belongToGroup: 620159
    belongToGroup: 620160
  }
  resourceConf {
    model: "SM_Wing_068"
    material: "MI_Wing_068_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71055
  scaleTimes: 160
  shareTexts: "星宝的花语，是元气可爱噢"
  shareTexts: "送礼首选"
  shareTexts: "谁能拒绝星宝花束呢？"
  shareOffset: -15
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620161
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福袋"
  desc: "吉祥好运，招财纳福"
  icon: "CDN:Icon_Wing_085_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620140
    fashionValue: 25
    belongToGroup: 620161
    belongToGroup: 620162
  }
  resourceConf {
    model: "SM_Wing_085"
    material: "MI_Wing_085_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71057
  scaleTimes: 160
  shareTexts: "惊喜开出来！"
  shareTexts: "满满的福气，分一点给你也可以哦~"
  shareTexts: "福多不嫌少！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620162
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "福袋"
  desc: "吉祥好运，招财纳福"
  icon: "CDN:Icon_Wing_085_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620140
    fashionValue: 25
    belongToGroup: 620161
    belongToGroup: 620162
  }
  resourceConf {
    model: "SM_Wing_085"
    material: "MI_Wing_085_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71058
  scaleTimes: 160
  shareTexts: "惊喜开出来！"
  shareTexts: "满满的福气，分一点给你也可以哦~"
  shareTexts: "福多不嫌少！"
  shareOffset: -10
  shareOffset: 15
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620164
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "蜜桃喵喵糖"
  desc: "沉甸甸的幸福，无比甜蜜"
  icon: "CDN:Icon_Wing_098"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_098"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "日常生活需要甜味来调剂"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1676390400
  }
  suitId: 60085
  suitName: "蜜桃喵喵糖"
  suitIcon: "CDN:Icon_Wing_098"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620165
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "青柠喵喵糖"
  desc: "酸甜甜的回忆，青柠点缀"
  icon: "CDN:Icon_Wing_141"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_141"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "美好回忆需要酸甜来铭记"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1676390400
  }
  suitId: 60086
  suitName: "青柠喵喵糖"
  suitIcon: "CDN:Icon_Wing_141"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620166
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "香芒喵喵糖"
  desc: "金灿灿的幸运，尽在眼前"
  icon: "CDN:Icon_Wing_142"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_142"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "幸福未来需要甜蜜来添彩"
  minVer: "1.2.67.1"
  beginTime {
    seconds: 1676390400
  }
  suitId: 60087
  suitName: "香芒喵喵糖"
  suitIcon: "CDN:Icon_Wing_142"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620167
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "瑞幸采茶篓"
  desc: "采摘一篓新鲜好茶，为你打造醇香新口味"
  icon: "CDN:Icon_Wing_100"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_100"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60088
  suitName: "瑞幸采茶篓"
  suitIcon: "CDN:Icon_Wing_100"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620168
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "节奏遥控器"
  desc: "生活的节奏，掌握在你的遥控器中"
  icon: "CDN:Icon_Wing_097"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_097"
    modelType: 1
  }
  scaleTimes: 120
  shareTexts: "每一次按键，都是一次方向选择"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60089
  suitName: "节奏遥控器"
  suitIcon: "CDN:Icon_Wing_097"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620169
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "魔方格格"
  desc: "随时随地开启你的益智游戏！"
  icon: "CDN:Icon_Wing_112"
  getWay: "春水溯游"
  jumpId: 186
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_112"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "每一面都是一个等待探索的世界！"
  minVer: "1.2.100.1"
  beginTime {
    seconds: 1714665600
  }
  suitId: 60090
  suitName: "魔方格格"
  suitIcon: "CDN:Icon_Wing_112"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620170
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "魔方格格"
  desc: "随时随地开启你的益智游戏！"
  icon: "CDN:Icon_Wing_112_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620169
    fashionValue: 25
    belongToGroup: 620170
    belongToGroup: 620171
  }
  resourceConf {
    model: "SM_Wing_112"
    material: "MI_Wing_112_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71102
  scaleTimes: 160
  shareTexts: "每一面都是一个等待探索的世界！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620171
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "魔方格格"
  desc: "随时随地开启你的益智游戏！"
  icon: "CDN:Icon_Wing_112_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620169
    fashionValue: 25
    belongToGroup: 620170
    belongToGroup: 620171
  }
  resourceConf {
    model: "SM_Wing_112"
    material: "MI_Wing_112_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71103
  scaleTimes: 160
  shareTexts: "每一面都是一个等待探索的世界！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620172
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "涂山璟 识神小狐"
  desc: "吱吱吱，我看到许多小秘密"
  icon: "CDN:Icon_Wing_091"
  getWay: "长相思"
  jumpId: 1054
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_091"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "我看到了，我全都看到了"
  minVer: "1.3.12.47"
  beginTime {
    seconds: 1721923200
  }
  suitId: 60091
  suitName: "涂山璟 识神小狐"
  suitIcon: "CDN:Icon_Wing_091"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620173
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "玱玹 若木花簪"
  desc: "若木花送所爱，若水族一生只爱一个人"
  icon: "CDN:Icon_Wing_092"
  getWay: "长相思"
  jumpId: 1054
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_092"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "若水族听你调遣，护你一生周全"
  minVer: "1.3.12.47"
  beginTime {
    seconds: 1721923200
  }
  suitId: 60092
  suitName: "玱玹 若木花簪"
  suitIcon: "CDN:Icon_Wing_092"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620174
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "丰隆 火球"
  desc: "想看烟花吗？"
  icon: "CDN:Icon_Wing_093"
  getWay: "长相思"
  jumpId: 1054
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_093"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "看到我火热的爱意了吗？"
  minVer: "1.3.12.47"
  beginTime {
    seconds: 1721923200
  }
  suitId: 60093
  suitName: "丰隆 火球"
  suitIcon: "CDN:Icon_Wing_093"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620175
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "小夭 团扇"
  desc: "今日有钱今日花，明日事来明日愁"
  icon: "CDN:Icon_Wing_094"
  getWay: "长相思"
  jumpId: 1054
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_094"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "这世上谁也不能陪谁一辈子"
  minVer: "1.3.12.47"
  beginTime {
    seconds: 1721923200
  }
  suitId: 60094
  suitName: "小夭 团扇"
  suitIcon: "CDN:Icon_Wing_094"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620176
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星光之矢"
  desc: "箭矢划开天际，为你洒落一整片天的星光"
  icon: "CDN:Icon_Wing_096"
  getWay: "霜天冰雨"
  jumpId: 185
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_096"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "准备开始暗黑森林的大冒险吧"
  beginTime {
    seconds: 1712160000
  }
  suitId: 60095
  suitName: "星光之矢"
  suitIcon: "CDN:Icon_Wing_096"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620177
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星光之矢"
  desc: "箭矢划开天际，为你洒落一整片天的星光"
  icon: "CDN:Icon_Wing_096_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620176
    fashionValue: 25
    belongToGroup: 620177
    belongToGroup: 620178
  }
  resourceConf {
    model: "SM_Wing_096"
    material: "MI_Wing_096_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71110
  scaleTimes: 160
  shareTexts: "准备开始暗黑森林的大冒险吧"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620178
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "星光之矢"
  desc: "箭矢划开天际，为你洒落一整片天的星光"
  icon: "CDN:Icon_Wing_096_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620176
    fashionValue: 25
    belongToGroup: 620177
    belongToGroup: 620178
  }
  resourceConf {
    model: "SM_Wing_096"
    material: "MI_Wing_096_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71111
  scaleTimes: 160
  shareTexts: "准备开始暗黑森林的大冒险吧"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620179
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "超级战术包"
  desc: "机密背包，切勿私拆！"
  icon: "CDN:Icon_Wing_095"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_095"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "快快加入战斗！"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60096
  suitName: "超级战术包"
  suitIcon: "CDN:Icon_Wing_095"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620180
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "超级战术包"
  desc: "机密背包，切勿私拆！"
  icon: "CDN:Icon_Wing_095_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620179
    fashionValue: 25
    belongToGroup: 620180
    belongToGroup: 620181
  }
  resourceConf {
    model: "SM_Wing_095"
    material: "MI_Wing_095_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71113
  scaleTimes: 160
  shareTexts: "快快加入战斗！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620181
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "超级战术包"
  desc: "机密背包，切勿私拆！"
  icon: "CDN:Icon_Wing_095_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620179
    fashionValue: 25
    belongToGroup: 620180
    belongToGroup: 620181
  }
  resourceConf {
    model: "SM_Wing_095"
    material: "MI_Wing_095_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71114
  scaleTimes: 160
  shareTexts: "快快加入战斗！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620182
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "降妖宝杖"
  desc: "取经路上功能最多的武器"
  icon: "CDN:Icon_Wing_103"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_103"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60097
  suitName: "降妖宝杖"
  suitIcon: "CDN:Icon_Wing_103"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620183
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "时光漂流瓶"
  desc: "我们的时光被珍藏于此，贴身保管"
  icon: "CDN:Icon_Wing_101"
  getWay: "赛季祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 300
  }
  resourceConf {
    model: "SK_Wing_101"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001"
  }
  scaleTimes: 120
  shareTexts: "你我的回忆，是不是也在这里呢？"
  beginTime {
    seconds: 1710432000
  }
  suitId: 60098
  suitName: "时光漂流瓶"
  suitIcon: "CDN:Icon_Wing_101"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620184
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "时光漂流瓶"
  desc: "我们的时光被珍藏于此，贴身保管"
  icon: "CDN:Icon_Wing_101_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620183
    fashionValue: 35
    belongToGroup: 620184
    belongToGroup: 620185
  }
  resourceConf {
    model: "SK_Wing_101"
    material: "MI_Wing_101_1_HP01;MI_Wing_101_2_HP01"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001_HP01"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71117
  scaleTimes: 120
  shareTexts: "你我的回忆，是不是也在这里呢？"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620185
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 320
  }
  quality: 1
  name: "时光漂流瓶"
  desc: "我们的时光被珍藏于此，贴身保管"
  icon: "CDN:Icon_Wing_101_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620183
    fashionValue: 35
    belongToGroup: 620184
    belongToGroup: 620185
  }
  resourceConf {
    model: "SK_Wing_101"
    material: "MI_Wing_101_1_HP02;MI_Wing_101_2_HP02"
    modelType: 2
    idleAnim: "AS_Wing_101_Idle_001_HP02"
    materialSlot: "Wing;Wing_Translucent"
  }
  commodityId: 71118
  scaleTimes: 120
  shareTexts: "你我的回忆，是不是也在这里呢？"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620186
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "天慧法杖"
  desc: "功夫的传承，在此定义"
  icon: "CDN:Icon_Wing_102"
  getWay: "功夫熊猫返场"
  jumpId: 1048
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_102"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 120
  shareTexts: "阴与阳，终将融为一体"
  minVer: "1.3.7.97"
  beginTime {
    seconds: 1720454400
  }
  suitId: 60099
  suitName: "天慧法杖"
  suitIcon: "CDN:Icon_Wing_102"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620187
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "功夫竹笛"
  desc: "静下心来，聆听天地的声音"
  icon: "CDN:Icon_Wing_109"
  getWay: "功夫熊猫返场"
  jumpId: 1048
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_109"
    modelType: 1
    IconLabelId: 102
  }
  scaleTimes: 160
  shareTexts: "听师傅为你吹奏一曲"
  minVer: "1.3.7.97"
  beginTime {
    seconds: 1720454400
  }
  suitId: 60100
  suitName: "功夫竹笛"
  suitIcon: "CDN:Icon_Wing_109"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620188
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "时光手提箱"
  desc: "收拾好过去，才可以面对美好的将来"
  icon: "CDN:Icon_Wing_086"
  getWay: "赛季祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_086"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "幸福的旅程已经妥当，美妙的故事还在路上！"
  beginTime {
    seconds: 1710432000
  }
  suitId: 60101
  suitName: "时光手提箱"
  suitIcon: "CDN:Icon_Wing_086"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620189
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "时光手提箱"
  desc: "收拾好过去，才可以面对美好的将来"
  icon: "CDN:Icon_Wing_086_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620188
    fashionValue: 25
    belongToGroup: 620189
    belongToGroup: 620190
  }
  resourceConf {
    model: "SM_Wing_086"
    material: "MI_Wing_086_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71122
  scaleTimes: 160
  shareTexts: "幸福的旅程已经妥当，美妙的故事还在路上！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620190
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "时光手提箱"
  desc: "收拾好过去，才可以面对美好的将来"
  icon: "CDN:Icon_Wing_086_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620188
    fashionValue: 25
    belongToGroup: 620189
    belongToGroup: 620190
  }
  resourceConf {
    model: "SM_Wing_086"
    material: "MI_Wing_086_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71123
  scaleTimes: 160
  shareTexts: "幸福的旅程已经妥当，美妙的故事还在路上！"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620191
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "九齿钉耙"
  desc: "八戒闯天下离不开的好伙伴"
  icon: "CDN:Icon_Wing_099"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_099"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60102
  suitName: "九齿钉耙"
  suitIcon: "CDN:Icon_Wing_099"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620192
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "唤星之杖"
  desc: "星之轨迹，听我号令！"
  icon: "CDN:Icon_Wing_087"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_087"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60103
  suitName: "唤星之杖"
  suitIcon: "CDN:Icon_Wing_087"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620193
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "热辣小熊"
  desc: "爱就是和你在一起吃好多好多汉堡！"
  icon: "CDN:Icon_Wing_113"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_113"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "与其原地吃饱，不如去看看世界美好！"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60104
  suitName: "热辣小熊"
  suitIcon: "CDN:Icon_Wing_113"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620194
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "热辣小熊"
  desc: "爱就是和你在一起吃好多好多汉堡！"
  icon: "CDN:Icon_Wing_113_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620193
    fashionValue: 25
    belongToGroup: 620194
    belongToGroup: 620195
  }
  resourceConf {
    model: "SM_Wing_113"
    material: "MI_Wing_113_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71127
  scaleTimes: 160
  shareTexts: "与其原地吃饱，不如去看看世界美好！"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620195
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "热辣小熊"
  desc: "爱就是和你在一起吃好多好多汉堡！"
  icon: "CDN:Icon_Wing_113_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620193
    fashionValue: 25
    belongToGroup: 620194
    belongToGroup: 620195
  }
  resourceConf {
    model: "SM_Wing_113"
    material: "MI_Wing_113_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71128
  scaleTimes: 160
  shareTexts: "与其原地吃饱，不如去看看世界美好！"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: -10
}
rows {
  id: 620196
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "千千星鹤"
  desc: "数到一千的时候，会有好事发生"
  icon: "Icon_Wing_115"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_115"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60105
  suitName: "千千星鹤"
  suitIcon: "Icon_Wing_115"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620197
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "鸢尾花"
  desc: "让梦和生命，拥有自由的香气"
  icon: "CDN:Icon_Wing_122"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_122"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60106
  suitName: "鸢尾花"
  suitIcon: "CDN:Icon_Wing_122"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620198
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "四季风车"
  desc: "为你向风祈福，带来四季的平安和幸福"
  icon: "CDN:Icon_Wing_129"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_129"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 1711641600
  }
  suitId: 60107
  suitName: "四季风车"
  suitIcon: "CDN:Icon_Wing_129"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620199
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "金巧风车"
  desc: "风吹风车转，轮转快乐来"
  icon: "CDN:Icon_Wing_145"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_145"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 1711641600
  }
  suitId: 60108
  suitName: "金巧风车"
  suitIcon: "CDN:Icon_Wing_145"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620200
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "折纸风车"
  desc: "风中带来幸福的气息"
  icon: "CDN:Icon_Wing_146"
  getWay: "印章祈愿"
  jumpId: 189
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_146"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 1711641600
  }
  suitId: 60109
  suitName: "折纸风车"
  suitIcon: "CDN:Icon_Wing_146"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620201
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "仙木法剑"
  desc: "风生水起，运势亨通！"
  icon: "CDN:Icon_Wing_144"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_144"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60110
  suitName: "仙木法剑"
  suitIcon: "CDN:Icon_Wing_144"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620202
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "煦风使者"
  desc: "梦盛开的地方，一定是在路上"
  icon: "CDN:Icon_Wing_104"
  getWay: "时光通行证"
  jumpId: 9
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_104"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "我把风带来了，一起出发吧！"
  beginTime {
    seconds: 1710432000
  }
  suitId: 60111
  suitName: "煦风使者"
  suitIcon: "CDN:Icon_Wing_104"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620203
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "煦风使者"
  desc: "梦盛开的地方，一定是在路上"
  icon: "CDN:Icon_Wing_104_01"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620202
    fashionValue: 25
    belongToGroup: 620203
    belongToGroup: 620204
  }
  resourceConf {
    model: "SM_Wing_104"
    material: "MI_Wing_104_HP01"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71136
  scaleTimes: 160
  shareTexts: "我把风带来了，一起出发吧！"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620204
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "煦风使者"
  desc: "梦盛开的地方，一定是在路上"
  icon: "CDN:Icon_Wing_104_02"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    belongTo: 620202
    fashionValue: 25
    belongToGroup: 620203
    belongToGroup: 620204
  }
  resourceConf {
    model: "SM_Wing_104"
    material: "MI_Wing_104_HP02"
    modelType: 1
    materialSlot: "Wing"
  }
  commodityId: 71137
  scaleTimes: 160
  shareTexts: "我把风带来了，一起出发吧！"
  shareOffset: -10
  shareOffset: 20
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620205
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 50
  }
  quality: 3
  name: "神秘扫帚"
  desc: "即使不能飞上天空，也可以用来打扫当下"
  icon: "CDN:Icon_Wing_131"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 60
  }
  resourceConf {
    model: "SM_Wing_131"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "测试测试"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60112
  suitName: "神秘扫帚"
  suitIcon: "CDN:Icon_Wing_131"
  shareOffset: 0
  shareOffset: 0
  previewShareOffset: 0
  previewShareOffset: 0
}
rows {
  id: 620206
  effect: true
  type: ItemType_BackOrnament
  maxNum: 1
  exceedReplaceItem {
    itemId: 6
    itemNum: 100
  }
  quality: 2
  name: "嘟嘟奶瓶"
  desc: "做个可爱的星宝，给生活加点奶香"
  icon: "CDN:Icon_Wing_111"
  getWay: "商城"
  jumpId: 15
  useType: IUTO_None
  outlookConf {
    fashionValue: 125
  }
  resourceConf {
    model: "SM_Wing_111"
    modelType: 1
  }
  scaleTimes: 160
  shareTexts: "真正的宝贝和年龄无关！"
  beginTime {
    seconds: 4074768000
  }
  suitId: 60113
  suitName: "嘟嘟奶瓶"
  suitIcon: "CDN:Icon_Wing_111"
  shareOffset: -10
  shareOffset: 30
  previewShareOffset: 0
  previewShareOffset: 0
}

com.tencent.wea.xlsRes.starp.table_FPSWeaponPropertyConf
excel/xls/W_SP武器配置.xlsx sheet:枪械配置
rows {
  blueprintName: "Weapon"
  propertyName: "DamageDistanceMap"
  propertyValue: "5000.0:0.95;7000.0:0.9"
  propertyType: "Property_Map"
  value_KeyType: "Property_Number"
  value_ValueType: "Property_Number"
}
rows {
  blueprintName: "Weapon"
  propertyName: "Damage"
  propertyValue: "10"
  propertyType: "Property_Number"
}
rows {
  blueprintName: "Weapon"
  propertyName: "MaxAmmoCount"
  propertyValue: "30"
  propertyType: "Property_Number"
}
rows {
  blueprintName: "Weapon"
  propertyName: "bAlwaysCheckDamage"
  propertyValue: "false"
  propertyType: "Property_Bool"
}
rows {
  blueprintName: "Weapon"
  propertyName: "AttackColliderCenter"
  propertyValue: "0,0,0"
  propertyType: "Property_Vector"
}
rows {
  blueprintName: "Weapon"
  propertyName: "CurrentBulletDecalRot"
  propertyValue: "0,0,90"
  propertyType: "Property_Rotate"
}
rows {
  blueprintName: "Weapon"
  propertyName: "ResourceID"
  propertyValue: "M4"
  propertyType: "Property_String"
}
rows {
  blueprintName: "Weapon"
  propertyName: "HitSoundBank"
  propertyValue: "Melee_Hit_WoodenStick"
  propertyType: "Property_String"
}

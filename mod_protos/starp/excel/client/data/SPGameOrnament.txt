com.tencent.wea.xlsRes.starp.table_SPGameOrnament
excel/xls/Z_SP装备_饰品表.xlsx sheet:Ornament
rows {
  itemID: 140014011
  type: ESPOT_Ring
  rarity: 2
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40110
  mainEffectLib: 40111
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014011
  equipPriority: 5
}
rows {
  itemID: 140014012
  type: ESPOT_Necklace
  rarity: 2
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40120
  mainEffectLib: 40121
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014011
  equipPriority: 5
}
rows {
  itemID: 140014013
  type: ESPOT_Amulet
  rarity: 2
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40130
  mainEffectLib: 40131
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014011
  equipPriority: 5
}
rows {
  itemID: 140014014
  type: ESPOT_Pendant
  rarity: 2
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40140
  mainEffectLib: 40141
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomEffectLib: 40102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014011
  equipPriority: 5
}
rows {
  itemID: 140014021
  type: ESPOT_Ring
  rarity: 3
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40210
  mainEffectLib: 40211
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014021
  equipPriority: 10
}
rows {
  itemID: 140014022
  type: ESPOT_Necklace
  rarity: 3
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40220
  mainEffectLib: 40221
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014021
  equipPriority: 10
}
rows {
  itemID: 140014023
  type: ESPOT_Amulet
  rarity: 3
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40230
  mainEffectLib: 40231
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014021
  equipPriority: 10
}
rows {
  itemID: 140014024
  type: ESPOT_Pendant
  rarity: 3
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40240
  mainEffectLib: 40241
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomEffectLib: 40202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014021
  equipPriority: 10
}
rows {
  itemID: 140014031
  type: ESPOT_Ring
  rarity: 4
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40310
  mainEffectLib: 40311
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014031
  equipPriority: 15
}
rows {
  itemID: 140014032
  type: ESPOT_Necklace
  rarity: 4
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40320
  mainEffectLib: 40321
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014031
  equipPriority: 15
}
rows {
  itemID: 140014033
  type: ESPOT_Amulet
  rarity: 4
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40330
  mainEffectLib: 40331
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014031
  equipPriority: 15
}
rows {
  itemID: 140014034
  type: ESPOT_Pendant
  rarity: 4
  star: 4
  roleLevel: 35
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 40340
  mainEffectLib: 40341
  mainRepeated: 0
  suitID: 1001
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomEffectLib: 40302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140014031
  equipPriority: 15
}
rows {
  itemID: 140025021
  type: ESPOT_Ring
  rarity: 3
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50210
  mainEffectLib: 50211
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025021
  equipPriority: 20
}
rows {
  itemID: 140025022
  type: ESPOT_Necklace
  rarity: 3
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50220
  mainEffectLib: 50221
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025021
  equipPriority: 20
}
rows {
  itemID: 140025023
  type: ESPOT_Amulet
  rarity: 3
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50230
  mainEffectLib: 50231
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025021
  equipPriority: 20
}
rows {
  itemID: 140025024
  type: ESPOT_Pendant
  rarity: 3
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50240
  mainEffectLib: 50241
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomEffectLib: 50202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025021
  equipPriority: 20
}
rows {
  itemID: 140025031
  type: ESPOT_Ring
  rarity: 4
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50310
  mainEffectLib: 50311
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025031
  equipPriority: 25
}
rows {
  itemID: 140025032
  type: ESPOT_Necklace
  rarity: 4
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50320
  mainEffectLib: 50321
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025031
  equipPriority: 25
}
rows {
  itemID: 140025033
  type: ESPOT_Amulet
  rarity: 4
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50330
  mainEffectLib: 50331
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025031
  equipPriority: 25
}
rows {
  itemID: 140025034
  type: ESPOT_Pendant
  rarity: 4
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50340
  mainEffectLib: 50341
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomEffectLib: 50302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025031
  equipPriority: 25
}
rows {
  itemID: 140025041
  type: ESPOT_Ring
  rarity: 5
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50410
  mainEffectLib: 50411
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025041
  equipPriority: 30
}
rows {
  itemID: 140025042
  type: ESPOT_Necklace
  rarity: 5
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50420
  mainEffectLib: 50421
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025041
  equipPriority: 30
}
rows {
  itemID: 140025043
  type: ESPOT_Amulet
  rarity: 5
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50430
  mainEffectLib: 50431
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025041
  equipPriority: 30
}
rows {
  itemID: 140025044
  type: ESPOT_Pendant
  rarity: 5
  star: 5
  roleLevel: 45
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 50440
  mainEffectLib: 50441
  mainRepeated: 0
  suitID: 1002
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomEffectLib: 50402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140025041
  equipPriority: 30
}
rows {
  itemID: 140033011
  type: ESPOT_Ring
  rarity: 2
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30110
  mainEffectLib: 30111
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033011
  equipPriority: 35
}
rows {
  itemID: 140033012
  type: ESPOT_Necklace
  rarity: 2
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30120
  mainEffectLib: 30121
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033011
  equipPriority: 35
}
rows {
  itemID: 140033013
  type: ESPOT_Amulet
  rarity: 2
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30130
  mainEffectLib: 30131
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033011
  equipPriority: 35
}
rows {
  itemID: 140033014
  type: ESPOT_Pendant
  rarity: 2
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30140
  mainEffectLib: 30141
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomEffectLib: 30102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033011
  equipPriority: 35
}
rows {
  itemID: 140033021
  type: ESPOT_Ring
  rarity: 3
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30210
  mainEffectLib: 30211
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033021
  equipPriority: 40
}
rows {
  itemID: 140033022
  type: ESPOT_Necklace
  rarity: 3
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30220
  mainEffectLib: 30221
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033021
  equipPriority: 40
}
rows {
  itemID: 140033023
  type: ESPOT_Amulet
  rarity: 3
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30230
  mainEffectLib: 30231
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033021
  equipPriority: 40
}
rows {
  itemID: 140033024
  type: ESPOT_Pendant
  rarity: 3
  star: 3
  roleLevel: 25
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 30240
  mainEffectLib: 30241
  mainRepeated: 0
  suitID: 1003
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomEffectLib: 30202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140033021
  equipPriority: 40
}
rows {
  itemID: 140046021
  type: ESPOT_Ring
  rarity: 3
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60210
  mainEffectLib: 60211
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046021
  equipPriority: 45
}
rows {
  itemID: 140046022
  type: ESPOT_Necklace
  rarity: 3
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60220
  mainEffectLib: 60221
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046021
  equipPriority: 45
}
rows {
  itemID: 140046023
  type: ESPOT_Amulet
  rarity: 3
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60230
  mainEffectLib: 60231
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046021
  equipPriority: 45
}
rows {
  itemID: 140046024
  type: ESPOT_Pendant
  rarity: 3
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60240
  mainEffectLib: 60241
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 1000
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomEffectLib: 60202
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046021
  equipPriority: 45
}
rows {
  itemID: 140046031
  type: ESPOT_Ring
  rarity: 4
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60310
  mainEffectLib: 60311
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046031
  equipPriority: 50
}
rows {
  itemID: 140046032
  type: ESPOT_Necklace
  rarity: 4
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60320
  mainEffectLib: 60321
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046031
  equipPriority: 50
}
rows {
  itemID: 140046033
  type: ESPOT_Amulet
  rarity: 4
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60330
  mainEffectLib: 60331
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046031
  equipPriority: 50
}
rows {
  itemID: 140046034
  type: ESPOT_Pendant
  rarity: 4
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60340
  mainEffectLib: 60341
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 1000
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomEffectLib: 60302
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046031
  equipPriority: 50
}
rows {
  itemID: 140046041
  type: ESPOT_Ring
  rarity: 5
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60410
  mainEffectLib: 60411
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046041
  equipPriority: 55
}
rows {
  itemID: 140046042
  type: ESPOT_Necklace
  rarity: 5
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60420
  mainEffectLib: 60421
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046041
  equipPriority: 55
}
rows {
  itemID: 140046043
  type: ESPOT_Amulet
  rarity: 5
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60430
  mainEffectLib: 60431
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046041
  equipPriority: 55
}
rows {
  itemID: 140046044
  type: ESPOT_Pendant
  rarity: 5
  star: 6
  roleLevel: 55
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 60440
  mainEffectLib: 60441
  mainRepeated: 0
  suitID: 1004
  randomEffectNum {
    num: 1
    weight: 0
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 1000
  }
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomEffectLib: 60402
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140046041
  equipPriority: 55
}
rows {
  itemID: 140051001
  type: ESPOT_Ring
  rarity: 1
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10010
  mainEffectLib: 10011
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051001
  equipPriority: 60
}
rows {
  itemID: 140051002
  type: ESPOT_Necklace
  rarity: 1
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10020
  mainEffectLib: 10021
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051001
  equipPriority: 60
}
rows {
  itemID: 140051003
  type: ESPOT_Amulet
  rarity: 1
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10030
  mainEffectLib: 10031
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051001
  equipPriority: 60
}
rows {
  itemID: 140051004
  type: ESPOT_Pendant
  rarity: 1
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10040
  mainEffectLib: 10041
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomEffectLib: 10002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051001
  equipPriority: 60
}
rows {
  itemID: 140051011
  type: ESPOT_Ring
  rarity: 2
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10110
  mainEffectLib: 10111
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051011
  equipPriority: 65
}
rows {
  itemID: 140051012
  type: ESPOT_Necklace
  rarity: 2
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10120
  mainEffectLib: 10121
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051011
  equipPriority: 65
}
rows {
  itemID: 140051013
  type: ESPOT_Amulet
  rarity: 2
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10130
  mainEffectLib: 10131
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051011
  equipPriority: 65
}
rows {
  itemID: 140051014
  type: ESPOT_Pendant
  rarity: 2
  star: 1
  roleLevel: 5
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 10140
  mainEffectLib: 10141
  mainRepeated: 0
  suitID: 1006
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomEffectLib: 10102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140051011
  equipPriority: 65
}
rows {
  itemID: 140062001
  type: ESPOT_Ring
  rarity: 1
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20010
  mainEffectLib: 20011
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062001
  equipPriority: 70
}
rows {
  itemID: 140062002
  type: ESPOT_Necklace
  rarity: 1
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20020
  mainEffectLib: 20021
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062001
  equipPriority: 70
}
rows {
  itemID: 140062003
  type: ESPOT_Amulet
  rarity: 1
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20030
  mainEffectLib: 20031
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062001
  equipPriority: 70
}
rows {
  itemID: 140062004
  type: ESPOT_Pendant
  rarity: 1
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20040
  mainEffectLib: 20041
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomEffectLib: 20002
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062001
  equipPriority: 70
}
rows {
  itemID: 140062011
  type: ESPOT_Ring
  rarity: 2
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20110
  mainEffectLib: 20111
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062011
  equipPriority: 75
}
rows {
  itemID: 140062012
  type: ESPOT_Necklace
  rarity: 2
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20120
  mainEffectLib: 20121
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062011
  equipPriority: 75
}
rows {
  itemID: 140062013
  type: ESPOT_Amulet
  rarity: 2
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20130
  mainEffectLib: 20131
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062011
  equipPriority: 75
}
rows {
  itemID: 140062014
  type: ESPOT_Pendant
  rarity: 2
  star: 2
  roleLevel: 15
  mainEffectNum {
    num: 2
    weight: 1000
  }
  mainEffectLib: 20140
  mainEffectLib: 20141
  mainRepeated: 0
  suitID: 1007
  randomEffectNum {
    num: 1
    weight: 1000
  }
  randomEffectNum {
    num: 2
    weight: 0
  }
  randomEffectNum {
    num: 3
    weight: 0
  }
  randomEffectNum {
    num: 4
    weight: 0
  }
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomEffectLib: 20102
  randomRepeated: 0
  decomposeId: 1
  suitGroupId: 140062011
  equipPriority: 75
}

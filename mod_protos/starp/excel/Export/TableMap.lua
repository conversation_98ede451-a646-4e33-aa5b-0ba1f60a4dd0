-- This file is automatically generated

local TableMap = {
	FPSWeaponStarPTable = {
		FPSWeaponClientConfSubTables = {
			"SPWeaponClientConfData",
		},
		FPSWeaponDissolveEffectConfSubTables = {
			"SPWeaponDissolveEffectConfData",
		},
		WeaponEnergySubTables = {
			"SPWeaponEnergy",
		},
		FPSWeaponPropertyConfSubTables = {
			"SPWeaponPropertyConfData",
		},
		FPSWeaponRenovationConfSubTables = {
			"SPWeaponRenovationConfData",
		},
		FPSWeaponUnlockInfoSubTables = {
			"SPWeaponUnlockInfo",
		},
	},
	GeneralStarPTable = {
		GamePlayUpdateConfSubTables = {
			"GamePlayUpdateConf_StarP",
		},
		VersionCompBattleConfSubTables = {
			"VersionCompBattleConfData_starp",
		},
		VersionCompFeatureConfSubTables = {
			"VersionCompFeatureConf_StarP",
		},
	},
	LetsGoDeviceCommandInfoStarpTable = {
		DeviceProfileCommandDataSubTables = {
			"PWDeviceFPSCommandAndroid",
			"PWDeviceFPSCommandIOS",
			"PWDeviceFPSCommandWindows",
			"PWDeviceProfileCommandAndroid",
			"PWDeviceProfileCommandIOS",
			"PWDeviceProfileCommandWindows",
			"PWDeviceScreenPercentageCommandAndroid",
			"PWDeviceScreenPercentageCommandIOS",
			"PWDeviceScreenPercentageCommandWindows",
		},
	},
	LetsGoDeviceProfileInfoStarpTable = {
		DeviceProfileInfoDataSubTables = {
			"PWDeviceFPSInfoAndroid",
			"PWDeviceFPSInfoIOS",
			"PWDeviceFPSInfoWindows",
			"PWDeviceProfileInfoAndroid",
			"PWDeviceProfileInfoIOS",
			"PWDeviceProfileInfoWindows",
			"PWDeviceScreenPercentageInfoAndroid",
			"PWDeviceScreenPercentageInfoIOS",
			"PWDeviceScreenPercentageInfoWindows",
		},
	},
	LevelInfoStarpTable = {
		LevelInfoDataSubTables = {
			"LevelInfoData_SPGame",
		},
	},
	MatchTable = {
		MatchDimensionDataSubTables = {
			"MatchDimensionData_SP",
		},
		MatchRoomInfoDataSubTables = {
			"MatchRoomInfoData_SP",
		},
		MatchRuleDataSubTables = {
			"MatchRuleData_SP",
		},
		MatchRuleRangeDataSubTables = {
			"MatchRuleRangeData_SPGame",
		},
		MatchTypeDataSubTables = {
			"MatchTypeData_SPGame",
		},
	},
	MatchStarpTable = {
		MatchRuleDataSubTables = {
			"MatchRuleData_starp",
		},
		MatchRoomInfoDataSubTables = {
			"MatchRoomInfoData_starp",
		},
		MatchDynamicTeamRobotsDataSubTables = {
			"MatchDynamicTeamRobotsData_starp",
		},
		MatchDynamicMaxTimeoutDataSubTables = {
			"MatchDynamicMaxTimeoutData_starp",
		},
		MatchSimulatorDynamicMaxTimeoutDataSubTables = {
			"MatchSimulatorDynamicMaxTimeoutData_starp",
		},
		MatchSpecialRoundRobotSideDiffVirtualRoomInfoDataSubTables = {
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_starp",
		},
	},
	ProtectedScoreStarpTable = {
		ProtectedScoreAdditionalDataSubTables = {
			"ProtectedScoreAdditionalData_starp_pvp",
		},
		ProtectedScoreDataSubTables = {
			"ProtectedScoreData_starp_pvp",
		},
		ProtectedScoreWinDataSubTables = {
			"ProtectedScoreWinData_starp_pvp",
		},
		ProtectedScoreWinStreakDataSubTables = {
			"ProtectedScoreWinStreakData_starp_pvp",
		},
		SPProtectedScoreLevelDimensionDataSubTables = {
			"SPProtectedScoreLevelDimensionData_starp_pvp",
		},
	},
	QualifyingStarpTable = {
		DegreeTypeDataSubTables = {
			"QDTDegreeTypeData_starp_pvp",
		},
		EspecialIntegralDataSubTables = {
			"QDTEspecialIntegralData_starp_pvp",
		},
		QualifyingInheritDataSubTables = {
			"QDTQualifyingInheritData_starp_pvp",
		},
		SeasonCfgDataSubTables = {
			"QDTSeasonCfgData_starp_pvp",
		},
		QualifyingLevelDimensionConditionDataSubTables = {
			"QualifyingLevelDimensionConditionData_starp_pvp",
		},
		QualifyingLevelDimensionScoreDataSubTables = {
			"QualifyingLevelDimensionScoreData_starp_pvp",
		},
		RankKingDegreeDataSubTables = {
			"RankKingDegreeData_starp_pvp",
		},
		SPQualifyingIntegralDataSubTables = {
			"SPQualifyingIntegralData_starp_pvp",
		},
	},
	ReportStarpTable = {
		ReportContentConfSubTables = {
			"ReportContentDataStarp",
		},
		ReportEntryConfSubTables = {
			"ReportEntryConfDataStarp",
		},
	},
	SPAbilityActionTable = {
		SPAbilityActionDataSubTables = {
			"SPAbilityActionData",
		},
	},
	SPAbilityActionParamStarpTable = {
		SPAbilityActionParamSubTables = {
			"SPAbilityActionParam",
		},
	},
	SPAbilityConditionTable = {
		SPAbilityConditionDataSubTables = {
			"SPAbilityConditionData",
		},
	},
	SPAbilityConsumeTable = {
		SPAbilityConsumeDataSubTables = {
			"SPAbilityConsumeData",
		},
	},
	SPAbilityDataTable = {
		SPAbilitySubTables = {
			"SPAbilityData",
			"SPAbilityDataPVP",
		},
		SPPVPAbilityExtendSubTables = {
			"SPPVPAbilityExtend",
		},
		SPSkillPositionSubTables = {
			"SPSkillPositionData",
		},
	},
	SPAbilityEventTable = {
		SPAbilityEventDataSubTables = {
			"SPAbilityEventData",
		},
	},
	SPAbilitySubPackageTable = {
		SPSubPackageDataSubTables = {
			"SPSubPackageData",
		},
	},
	SPAchievementTable = {
		SPAchievementConfSubTables = {
			"SPAchievementConf",
		},
		SPAdventureLevelSubTables = {
			"SPAdventureLevel",
		},
		SPAdventureShopSubTables = {
			"SPAdventureShop",
		},
		SPAdventureShopSubPageSubTables = {
			"SPAdventureShopSubPage",
		},
		SPRewardPointsSubTables = {
			"SPRewardPoints",
		},
		SPTrophyInfoSubTables = {
			"SPTrophyInfo",
		},
	},
	SPAdventureGroupTable = {
		SPAdventureGroupSpeedUpSubTables = {
			"SPAdventureGroup",
		},
	},
	SPAffixDataTable = {
		SPAffixDataSubTables = {
			"SPAffixData",
		},
	},
	SPAirWallTable = {
		SPAirWallSubTables = {
			"SPAirWall",
		},
	},
	SPAttrBalanceTable = {
		SPBalanceDatabaseSubTables = {
			"SPBalanceDatabase",
		},
		SPBalanceIndexDataSubTables = {
			"SPBalanceIndexData",
		},
		SPPassiveBuffSwitchesSubTables = {
			"SPPassiveBuffSwitches",
		},
	},
	SPAvatarConfigTable = {
		SPAvatarPartInGameConfigSubTables = {
			"SPAvatarPartInGameConfig",
		},
		SPAvatarPartPreviewConfigSubTables = {
			"SPAvatarPartPreviewConfig",
		},
		SPAvatarSuitInGameConfigSubTables = {
			"SPAvatarSuitInGameConfig",
		},
		SPAvatarSuitPreviewConfigSubTables = {
			"SPAvatarSuitPreviewConfig",
		},
	},
	SPBaseAttributeTable = {
		SPMonsterBaseAttributeSubTables = {
			"SPMonsterBaseAttribute",
		},
		SPPetBaseAttributeSubTables = {
			"SPPetBaseAttribute",
		},
		SPPlayerBaseAttributeSubTables = {
			"SPPlayerBaseAttribute",
		},
	},
	SPBattleTalentTable = {
		SPBattleTalentSubTables = {
			"SPBattleTalent",
		},
		SPBattleTalentElementEffectSubTables = {
			"SPBattleTalentElementEffect",
		},
		SPBattleTalentElementWeaponSubTables = {
			"SPBattleTalentElementWeapon",
		},
		SPBattleTalentEnchantSubTables = {
			"SPBattleTalentEnchant",
		},
		SPBattleTalentGroupSubTables = {
			"SPBattleTalentGroup",
		},
		SPBattleTalentPositionSubTables = {
			"SPBattleTalentPosition",
		},
		SPBattleTalentWeaponInlaySubTables = {
			"SPBattleTalentWeaponInlay",
		},
	},
	SPBlessTable = {
		SPBlessSubTables = {
			"SPBless",
		},
	},
	SPBreedTable = {
		SPBreedAbilityInheritSubTables = {
			"SPBreedAbilityInherit",
		},
		SPBreedAbilityMutationSubTables = {
			"SPBreedAbilityMutation",
		},
		SPBreedAbilityNumSubTables = {
			"SPBreedAbilityNum",
		},
		SPBreedBuildItemConfigSubTables = {
			"SPBreedBuildItemConfig",
		},
		SPBreedCombinationSubTables = {
			"SPBreedCombination",
		},
	},
	SPBroadcastStarpTable = {
		SPBroadcastDataSubTables = {
			"SPBroadcastData",
		},
	},
	SPBroadcastTemplateStarpTable = {
		SPBroadcastTemplateDataSubTables = {
			"SPBroadcastTemplateData",
		},
	},
	SPBuffConversionTable = {
		SPBuffConversionDataSubTables = {
			"SPBuffConversionData",
		},
	},
	SPBuffDataTable = {
		SPBuffDataSubTables = {
			"SPBuffData",
			"SPBuffDataPVP",
		},
		SPBuffData_ABTestSubTables = {
			"SPBuffDataPVP_ABTest",
			"SPBuffData_ABTest",
		},
		SPPVPBuffExtendSubTables = {
			"SPPVPBuffExtend",
		},
		SPPVPBuffFilterSubTables = {
			"SPPVPBuffFilter",
		},
	},
	SPBuffFlowExtraInfoTable = {
		SPBuffFlowExtraInfoSubTables = {
			"SPBuffFlowExtraInfo",
		},
	},
	SPBuildConfigTable = {
		SPBuildConfigDataSubTables = {
			"SPBuildConfigData",
		},
		SPBuildTerminalCondSubTables = {
			"SPBuildTerminalCond",
		},
		SPBuildTerminalLevelSubTables = {
			"SPBuildTerminalLevel",
		},
		SPTerminalWorkStatusSubTables = {
			"SPTerminalWorkStatus",
		},
	},
	SPBuildFunctionTable = {
		SPBuildBaseViewSubTables = {
			"SPBuildBaseView",
		},
		SPBuildBattleSubTables = {
			"SPBuildBattle",
		},
		SPBuildBattleBulletSubTables = {
			"SPBuildBattleBullet",
		},
		SPBuildElecGeneratorSubTables = {
			"SPBuildElecGenerator",
		},
		SPBuildFarmSubTables = {
			"SPBuildFarm",
		},
		SPBuildFarmCropSubTables = {
			"SPBuildFarmCrop",
		},
		SPBuildFarmCropStateSubTables = {
			"SPBuildFarmCropState",
		},
		SPBuildFarmVegetealSubTables = {
			"SPBuildFarmVegeteal",
		},
		SPBuildFormulaSubTables = {
			"SPBuildFormula",
		},
		SPBuildGuildCheckSubTables = {
			"SPBuildGuildCheck",
		},
		SPBuildImpIncubatorSubTables = {
			"SPBuildImpIncubator",
		},
		SPBuildLightSubTables = {
			"SPBuildLight",
		},
		SPBuildMakeSubTables = {
			"SPBuildMake",
		},
		SPBuildMakeEquipSubTables = {
			"SPBuildMakeEquip",
		},
		SPBuildMakeFoodSubTables = {
			"SPBuildMakeFood",
		},
		SPBuildMakeFoodProcessSubTables = {
			"SPBuildMakeFoodProcess",
		},
		SPBuildPastureSubTables = {
			"SPBuildPasture",
		},
		SPBuildPastureDropSubTables = {
			"SPBuildPastureDrop",
		},
		SPBuildPetRelaxSubTables = {
			"SPBuildPetRelax",
		},
		SPBuildPetSleepSubTables = {
			"SPBuildPetSleep",
		},
		SPBuildPlanterSubTables = {
			"SPBuildPlanter",
		},
		SPBuildPlayerSleepSubTables = {
			"SPBuildPlayerSleep",
		},
		SPBuildProductSubTables = {
			"SPBuildProduct",
		},
		SPBuildStorageSubTables = {
			"SPBuildStorage",
		},
		SPBuildTechSubTables = {
			"SPBuildTech",
		},
		SPBuildTempControlSubTables = {
			"SPBuildTempControl",
		},
		SPBuildTempControlEffectSubTables = {
			"SPBuildTempControlEffect",
		},
	},
	SPBuildTypeConfigTable = {
		SPBuildTypeConfigDataSubTables = {
			"BuildTypeConfigData",
		},
	},
	SPBusinessFuncSwitchTable = {
		SPBusinessFuncSwitchSubTables = {
			"ResSPBusinessFuncSwitch",
		},
	},
	SPCaptureDataTable = {
		CaptureMonsterLevelFactorSubTables = {
			"CaptureMonsterLevelFactor",
		},
		ResCaptureGuaranteedSubTables = {
			"ResCaptureGuaranteed",
		},
		ResCaptureLevelSuppressSubTables = {
			"ResCaptureLevelSuppress",
		},
		ResCaptureProbabilityGrowthSubTables = {
			"ResCaptureProbabilityGrowth",
		},
		ResCaptureProbabilityScalingSubTables = {
			"ResCaptureProbabilityScaling",
		},
		ResCharacterCapturePowerSubTables = {
			"ResCharacterCapturePower",
		},
		ResNewCaptureLevelSuppressSubTables = {
			"ResNewCaptureLevelSuppress",
		},
		ResSPCaptureBallConfigSubTables = {
			"ResSPCaptureBallConfig",
		},
		SPCaptureFailReasonSubTables = {
			"SPCaptureFailReason",
		},
	},
	SPCardTable = {
		SPCardSubTables = {
			"SPCard",
		},
	},
	SPCareerIdolStoryInfoDataTable = {
		SPCareerIdolStoryInfoDataSubTables = {
			"SPCareerIdolStoryInfoData",
		},
	},
	SPCharacterWorkTable = {
		SPCharacterWorkSubTables = {
			"SPCharacterWork",
		},
	},
	SPChatTable = {
		SPAssistQuickTextConfDataSubTables = {
			"SPAssistQuickTextConfData",
		},
		SPQuickTextConfDataSubTables = {
			"SPQuickTextConfDataLobby",
		},
	},
	SPChoppableConfigDataStarpTable = {
		SPChoppableConfigDataSubTables = {
			"SPChoppableConfigData",
		},
	},
	SPClimbSpeedStarpTable = {
		SPClimbSpeedSubTables = {
			"SPClimbSpeed",
		},
	},
	SPClimbTowerConfigStarpTable = {
		SPClimbTowerAchievementRewardDataSubTables = {
			"SPClimbTowerAchievementRewardData",
		},
		SPClimbTowerAwardPondDataSubTables = {
			"SPClimbTowerAwardPondData",
		},
		SPClimbTowerChapterDataSubTables = {
			"SPClimbTowerChapterData",
		},
		SPClimbTowerGoodModifierDataSubTables = {
			"SPClimbTowerGoodModifierData",
		},
		SPClimbTowerLinearLevelDataSubTables = {
			"SPClimbTowerLinearLevelData",
		},
		SPClimbTowerModifiersPondDataSubTables = {
			"SPClimbTowerModifiersPondData",
		},
		SPClimbTowerPeriodLevelDataSubTables = {
			"SPClimbTowerPeriodLevelData",
		},
		SPClimbTowerRewardBonusDataSubTables = {
			"SPClimbTowerRewardBonusData",
		},
	},
	SPClimbTowerEntranceTable = {
		SPClimbTowerEntranceDataSubTables = {
			"SPClimbTowerEntranceData",
		},
	},
	SPCombatAttributeStarpTable = {
		SPCombatAttributeSubTables = {
			"SPCombatAttribute",
		},
		SPSecondaryAttributeConvertSubTables = {
			"SPSecondaryAttributeConvert",
		},
	},
	SPCombatEffectCalcTable = {
		SPCombatEffectCalcSubTables = {
			"SPCombatEffectCalcConfig",
		},
		SPCombatEffectUploadConfigSubTables = {
			"SPCombatEffectUploadConfig",
		},
	},
	SPConditionCombinationTable = {
		SPConditionCombinationConfSubTables = {
			"SPConditionCombinationConf",
		},
	},
	SPConfigUpdateLevelTable = {
		SPConfigUpdateLevelDataSubTables = {
			"SPConfigUpdateLevelData",
		},
	},
	SPConstantDataTable = {
		SPConstantDataSubTables = {
			"ResSPConstantData",
		},
		SPSvrConstantDataSubTables = {
			"SPSvrConstantData",
		},
	},
	SPCreateTeamConfTable = {
		SPCreateTeamConfSubTables = {
			"SPCreateTeamConf",
		},
	},
	SPDamageConfigTable = {
		SPDamageAttenuationConfigSubTables = {
			"SPDamageAttenuationConfig",
		},
		SPDamageConfigSubTables = {
			"SPDamageConfig",
		},
		SPDamageConfig_ABTestSubTables = {
			"SPDamageConfig_ABTest",
		},
		SPDamageTypeConfigSubTables = {
			"SPDamageTypeConfig",
		},
	},
	SPDamageDistanceAttenuationTable = {
		SPDamageDistanceAttenuationSubTables = {
			"SPDamageDistanceAttenuation",
		},
	},
	SPDamageLevelSuppressTable = {
		ResDamageLevelSuppressSubTables = {
			"ResDamageLevelSuppress",
		},
	},
	SPDeviceCommandInfoTable = {
		SPDeviceCorrectCommandDataSubTables = {
			"SPDeviceCorrectCommandAndroid",
			"SPDeviceCorrectCommandIOS",
			"SPDeviceCorrectCommandWindows",
		},
	},
	SPDressUpItemTable = {
		SPDressUpItemSubTables = {
			"SPDressUpItem",
		},
	},
	SPDropBagTable = {
		SPDropBagSubTables = {
			"SPDropBag",
		},
	},
	SPDungeonDataStarpTable = {
		SPDungeonDataSubTables = {
			"SPDungeonData",
		},
	},
	SPDungeonDeathTimePenaltyDataTable = {
		SPDungeonDeathTimePenaltyDataSubTables = {
			"SPDungeonDeathTimePenaltyData",
		},
	},
	SPDungeonLinkPointTable = {
		SPDungeonLinkPointSubTables = {
			"SPDungeonLinkPoint",
		},
	},
	SPDungeonStepTable = {
		SPDungeonStepDataSubTables = {
			"SPDungeonStepData",
		},
	},
	SPElementDamageEffectTable = {
		ResElementDamageEffectPVESubTables = {
			"ResElementDamageEffectPVE",
		},
		ResElementDamageEffectPVPSubTables = {
			"ResElementDamageEffectPVP",
		},
		ResElementDamageRemindSubTables = {
			"ResElementDamageRemind",
		},
		ResElementTenacityEffectPVESubTables = {
			"ResElementTenacityEffectPVE",
		},
		ResElementTenacityEffectPVPSubTables = {
			"ResElementTenacityEffectPVP",
		},
	},
	SPEncounterDataTable = {
		SPEncounterDataSubTables = {
			"SPEncounterData",
		},
		SPOWMissionDataSubTables = {
			"SPOWMissionData",
		},
	},
	SPEquipmentSetConfigTable = {
		SPEquipmentAttributeConfigSubTables = {
			"SPEquipmentAttributeConfig",
		},
		SPEquipmentFenceSubTables = {
			"SPEquipmentFence",
		},
		SPEquipmentFormulaIdConfigSubTables = {
			"SPEquipmentFormulaIdConfig",
		},
		SPEquipmentItemConfigSubTables = {
			"SPEquipmentItemConfig",
		},
		SPEquipmentPartSubTables = {
			"SPEquipmentPart",
		},
		SPEquipmentSetConfigSubTables = {
			"SPEquipmentSetConfig",
		},
		SPEquipmentSetEffectConfigSubTables = {
			"SPEquipmentSetEffectConfig",
		},
		SPEquipmentSlotConfigSubTables = {
			"SPEquipmentSlotConfig",
		},
		SPEquipmentSortConfigSubTables = {
			"SPEquipmentSortConfig",
		},
		SPEquipmentTermLibConfigSubTables = {
			"SPEquipmentTermLibConfig",
		},
		SPEquipmentTermRandomModeConfigSubTables = {
			"SPEquipmentTermRandomModeConfig",
		},
		SPEquipmentTermSubLibConfigSubTables = {
			"SPEquipmentTermSubLibConfig",
		},
	},
	SPEquipmentSetGradeDefineTable = {
		SPEquipmentSetGradeDefineSubTables = {
			"SPEquipmentSetGradeDefine",
		},
	},
	SPEquipmentSetUnlockDefineTable = {
		SPEquipmentSetUnlockDefineSubTables = {
			"SPEquipmentSetUnlockDefine",
		},
	},
	SPExperienceLimitTable = {
		SPExperienceLimitConfigSubTables = {
			"SPExperienceLimitConfig",
		},
	},
	SPExperienceLimitDefineTable = {
		SPExperienceLimitDefineSubTables = {
			"SPExperienceLimitDefine",
		},
	},
	SPExperienceWhiteListTable = {
		SPExperienceWhiteListConfigSubTables = {
			"SPExperienceWhiteListConfig",
		},
	},
	SPFactionRelationTable = {
		SPFactionRelationSubTables = {
			"SPFactionRelation",
		},
	},
	SPFocusActorTypeConfigDataTable = {
		SPFocusActorTypeConfigDataSubTables = {
			"SPFocusActorTypeConfigData",
		},
	},
	SPFriendIntimacyTable = {
		SPFriendIntimacyActionTypeSubTables = {
			"SPFriendIntimacyActionType",
		},
		SPFriendIntimacyDeductionSubTables = {
			"SPFriendIntimacyDeduction",
		},
		SPFriendIntimacyLevelSubTables = {
			"SPFriendIntimacyLevel",
		},
	},
	SPFriendSkillDataTable = {
		SPFriendSkillDataSubTables = {
			"SPFriendSkillData",
		},
	},
	SPFriendSkillEnergyTable = {
		SPFriendSkillEnergySubTables = {
			"SPFriendSkillEnergy",
		},
		SPFriendSkillEnergyCostSubTables = {
			"SPFriendSkillEnergyCost",
		},
	},
	SPFriendSkillMappingTable = {
		SPFriendSkillMappingSubTables = {
			"SPFriendSkillMapping",
		},
	},
	SPFunctionControlTable = {
		SPFunctionControlSubTables = {
			"SPFunctionControl",
		},
	},
	SPFunctionLockTable = {
		SPFunctionLockSubTables = {
			"SPFunctionLock",
		},
		SPProtocolBlockListSubTables = {
			"SPProtocolBlockList",
		},
	},
	SPGPOBuildingAttributeTable = {
		SPGPOBuildingAttributeSubTables = {
			"SPGPOBuildingAttribute",
		},
	},
	SPGPOCageTable = {
		SPGPOCageSubTables = {
			"SPGPOCage",
		},
	},
	SPGPOInteractionTargetTable = {
		SPGPOInteractionTargetSubTables = {
			"SPGPOInteractionTarget",
		},
	},
	SPGPOItemsTable = {
		SPGPOItemsSubTables = {
			"SPGPOItems",
		},
	},
	SPGPONPCTypeObjectsTable = {
		SPGPONPCTypeObjectsSubTables = {
			"SPGPONPCTypeObjects",
		},
	},
	SPGPOTreasureChestTable = {
		SPGPOTreasureChestSubTables = {
			"SPGPOTreasureChest",
		},
	},
	SPGameArmorTable = {
		SPGameArmorSubTables = {
			"SPGameArmor",
		},
		SPGameArmorAttributeSubTables = {
			"SPGameArmorAttribute",
		},
		SPGameArmorSetEffectSubTables = {
			"SPGameArmorSetEffect",
		},
		SPGameArmorSortSubTables = {
			"SPGameArmorSort",
		},
	},
	SPGameGachaTable = {
		SPGameGachaBuffSubTables = {
			"SPGameGachaBuff",
		},
		SPGameGachaItemSubTables = {
			"SPGameGachaItem",
		},
		SPGameGachaOnetimeRewardSubTables = {
			"SPGameGachaOnetimeReward",
		},
		SPGameGachaRewardSubTables = {
			"SPGameGachaReward",
		},
		SPGameGachaSortSubTables = {
			"SPGameGachaSort",
		},
	},
	SPGameGraphicalGuideTable = {
		SPGameGraphicalGuideSubTables = {
			"SPGameGraphicalGuide",
		},
	},
	SPGameItemTable = {
		SPGameBackpackSubTables = {
			"SPGameBackpack",
		},
		SPGameClothingItemSubTables = {
			"SPGameClothingItem",
		},
		SPGameDropBagTreasureSubTables = {
			"SPGameDropBagTreasure",
		},
		SPGameFoodSubTables = {
			"SPGameFood",
		},
		SPGameGlobalSubTables = {
			"SPGameGlobal",
		},
		SPGameImportantItemSubTables = {
			"SPGameImportantItem",
		},
		SPGameItemSummarySubTables = {
			"SPGameItemSummary",
		},
		SPGameItemTypeSubTables = {
			"SPGameItemType",
		},
		SPGameMedicineSubTables = {
			"SPGameMedicine",
		},
		SPGamePetSubTables = {
			"SPGamePet",
		},
		SPGameSkillFruitSubTables = {
			"SPGameSkillFruit",
		},
		SPGameWeaponSubTables = {
			"SPGameWeapon",
		},
		SPGameXingShouBallSubTables = {
			"SPGameXingShouBall",
		},
		SPGameXingShouItemSubTables = {
			"SPGameXingShouItem",
		},
	},
	SPGameMapTable = {
		SPGameMapIconSubTables = {
			"SPGameMapIcon",
		},
		SPGameMapIconTypeSummarySubTables = {
			"SPGameMapIconTypeSummary",
		},
		SPGameStaticIconSubTables = {
			"SPGameStaticIcon",
		},
		SPGameStaticVisibleSubTables = {
			"SPGameStaticVisible",
		},
	},
	SPGameNavigationTable = {
		SPGameNavigationSubTables = {
			"SPGameNavigation",
		},
	},
	SPGameOrnamentStarpTable = {
		SPGameBackpackFilterUILayoutSubTables = {
			"SPGameBackpackFilterUILayout",
		},
		SPGameEquipFenceSubTables = {
			"SPGameEquipFence",
		},
		SPGameEquipSlotSubTables = {
			"SPGameEquipSlot",
		},
		SPGameOrnamentSubTables = {
			"SPGameOrnament",
		},
		SPGameOrnamentAttributeSubTables = {
			"SPGameOrnamentAttribute",
		},
		SPGameOrnamentDecomposeSubTables = {
			"SPGameOrnamentDecompose",
		},
		SPGameOrnamentEffectSubTables = {
			"SPGameOrnamentEffect",
		},
		SPGameOrnamentPoolSubTables = {
			"SPGameOrnamentPool",
		},
		SPGameOrnamentPoolDetailSubTables = {
			"SPGameOrnamentPoolDetail",
		},
		SPGameOrnamentSetSubTables = {
			"SPGameOrnamentSet",
		},
		SPGameOrnamentSetEffectSubTables = {
			"SPGameOrnamentSetEffect",
		},
		SPGameOrnamentSortSubTables = {
			"SPGameOrnamentSort",
		},
		SPGameOrnamentTypeSubTables = {
			"SPGameOrnamentType",
		},
	},
	SPGiftPackageTable = {
		SPGiftPackageDataSubTables = {
			"SPGiftPackageConf",
		},
	},
	SPGlidingDataTable = {
		SPGlidingDataSubTables = {
			"SPGlidingData",
		},
	},
	SPGodStatueStoryInfoDataTable = {
		SPGodStatueStoryInfoDataSubTables = {
			"SPGodStatueStoryInfoData",
		},
	},
	SPGroupTable = {
		SPGroupActivityOutputSubTables = {
			"SPGroupActivityOutput",
		},
		SPGroupAttrSubTables = {
			"SPGroupAttr",
		},
		SPGroupGachaConfigSubTables = {
			"SPGroupGachaConfig",
		},
		SPGroupIconSubTables = {
			"SPGroupIcon",
		},
		SPGroupLevelSubTables = {
			"SPGroupLevel",
		},
	},
	SPGrowthPathConfigTable = {
		SPGrowthPathChapterSubTables = {
			"SPGrowthPathChapterConf",
		},
		SPGrowthPathChildMissionSubTables = {
			"SPGrowthPathChildMissionConf",
		},
		SPGrowthPathGeneralSubTables = {
			"SPGrowthPathGeneralConf",
		},
		SPGrowthPathMissionSubTables = {
			"SPGrowthPathMissionConf",
		},
	},
	SPGuideConfigTable = {
		SPGameGuideDebugInfoSubTables = {
			"SPGameGuideDebugInfo",
		},
		SPGameHelperSubTables = {
			"SPGameHelper",
		},
		SPGuideBubbleConfigSubTables = {
			"SPGuideBubbleConfig",
		},
		SPGuideConfigSubTables = {
			"SPGuideConfig",
			"SPGuideConfigPVP",
		},
		SPGuideDecisionConfigSubTables = {
			"SPGuideDecisionConfig",
		},
		SPGuideWindowConfigSubTables = {
			"SPGuideWindowConfig",
		},
		SPItemGetPopupConfigSubTables = {
			"SPItemGetPopupConfig",
		},
	},
	SPGuildConfigTable = {
		SPGuildHeadIconConfigSubTables = {
			"SPGuildHeadIconConfig",
		},
		SPGuildIconSubTables = {
			"SPGuildIcon",
		},
		SPGuildJoinCdConfigSubTables = {
			"SPGuildJoinCdConfig",
		},
		SPGuildMapIconConfigSubTables = {
			"SPGuildMapIconConfig",
		},
		SPGuildMaterialConfigSubTables = {
			"SPGuildMaterialConfig",
		},
		SPGuildNewsConfigSubTables = {
			"SPGuildNewsConfig",
		},
		SPGuildParamConfigSubTables = {
			"SPGuildParamConfig",
		},
		SPGuildSystemMessageConfigSubTables = {
			"SPGuildSystemMessageConfig",
		},
		SPGuildTitleConfigSubTables = {
			"SPGuildTitleConfig",
		},
	},
	SPHallGuideConfStarpTable = {
		SPHallGuideConfSubTables = {
			"SPHallGuideConf",
		},
	},
	SPHitConfigTable = {
		SPHitConfigSubTables = {
			"SPHitConfig",
		},
	},
	SPInteract3DUITable = {
		ResSPInteract3DUISubTables = {
			"SPInteract3DUI",
		},
		ResSPInteractProgressUISubTables = {
			"SPInteractProgressUI",
		},
	},
	SPInteractUITable = {
		ResSPBInteractUISubTables = {
			"SPBInteractUI",
		},
	},
	SPInvasionLevelConfigTable = {
		SPInvasionLevelConfigDataSubTables = {
			"SPInvasionLevelConfigData",
		},
	},
	SPItemAcquisitionPathTable = {
		SPItemAcquisitionPathConfSubTables = {
			"SPItemAcquisitionPathConf",
		},
	},
	SPItemModifySyncSvrTable = {
		SPItemModifySyncSvrConfSubTables = {
			"SPItemModifySyncSvrConf",
		},
	},
	SPLearningDiagramLimitDataTable = {
		SPLearningDiagramLimitDataSubTables = {
			"SPLearningDiagramLimitData",
		},
	},
	SPLevelTable = {
		SPLevelSubTables = {
			"SPLevel",
		},
	},
	SPLevelSequenceConfigTable = {
		SPLevelSequenceConfigDataSubTables = {
			"SPLevelSequenceConfigData",
		},
	},
	SPLevelUpAddTechPointTable = {
		SPLevelUpAddTechPointSubTables = {
			"SPLevelUpAddTechPoint",
		},
	},
	SPLocationTraceTable = {
		SPLocationTraceSubTables = {
			"SPLocationTrace",
		},
	},
	SPLogSettingConfigTable = {
		SPCLogSettingConfSubTables = {
			"SPCLogSettingConf",
		},
		SPLogSettingConfSubTables = {
			"SPLogSettingConf",
		},
	},
	SPLotteryTable = {
		SPLotteryPoolSubTables = {
			"SPLotteryPool",
		},
		SPLotteryRewardSubTables = {
			"SPLotteryReward",
		},
	},
	SPMailConfigTable = {
		SPMailConfigSubTables = {
			"SPMailConfig",
		},
		SPMailRevertConfigSubTables = {
			"SPMailRevertConfig",
		},
		SPTimedMailConfigSubTables = {
			"SPTimedMailConfig",
		},
	},
	SPMailTabConfigTable = {
		SPMailTabConfigSubTables = {
			"SPMailTabConfig",
		},
	},
	SPMainProcessTlogConfigTable = {
		SPMainProcessTlogGroupConfigSubTables = {
			"SPMainProcessTlog",
		},
	},
	SPMainProcessTlogDefineTable = {
		SPMainProcessTlogDefineSubTables = {
			"SPMainProcessTlogDefine",
		},
	},
	SPMainUITipsTable = {
		SPMainUISequentialGroupSubTables = {
			"SPMainUISequentialGroup",
		},
		SPMainUITipsSubTables = {
			"SPMainUITips",
		},
	},
	SPMapTable = {
		SPMapSubTables = {
			"SPMap",
		},
	},
	SPMapQualityTable = {
		SPImpactFoliageSettingSubTables = {
			"SPImpactFoliageSetting",
		},
		SPMapQualitySubTables = {
			"SPMapQuality",
		},
	},
	SPModeTable = {
		StarPModeConfSubTables = {
			"StarPModeConf",
		},
	},
	SPMonsterDynamicAttributeTable = {
		SPMonsterDynamicAttributeSubTables = {
			"SPMonsterDynamicAttribute",
		},
		SPMonsterDynamicAttributeFactorSubTables = {
			"SPMonsterDynamicAttributeFactor",
		},
	},
	SPMonsterPoolDataTable = {
		SPMonsterPoolDataSubTables = {
			"SPMonsterPoolData",
		},
	},
	SPMonsterRangePositionTable = {
		SPItemMonsterRangePositionConfSubTables = {
			"SPItemMonsterRangePositionConf",
		},
	},
	SPMonsterSafeLevelTable = {
		SPPointSafeLevelSubTables = {
			"SPPointSafeLevel",
		},
		SPRegionSafeLevelSubTables = {
			"SPRegionSafeLevel",
		},
	},
	SPMonsterSkinConfigTable = {
		SPMonsterSkinTagConfigSubTables = {
			"ResSPMonsterSkinTagConfig",
		},
		SPMonsterSkinConfigSubTables = {
			"SPMonsterSkinConfig",
		},
		SPMonsterSkinSocConfigSubTables = {
			"SPMonsterSkinSocConfig",
		},
	},
	SPMonsterVoiceTable = {
		SPMonsterVoiceSubTables = {
			"SPMonsterVoice",
		},
	},
	SPMonsterWoundTable = {
		SPMonsterWoundSubTables = {
			"SPMonsterWound",
		},
	},
	SPMonthCardPrivilegeConfTable = {
		SPMonthCardPrivilegeConfSubTables = {
			"SPMonthCardPrivilegeConf",
		},
	},
	SPNPCConfigDataTable = {
		SPNPCConfigDataSubTables = {
			"SPNPCConfigData",
		},
	},
	SPOWScenarioTriggerConfigStarpTable = {
		SPOWScenarioTriggerConfigDataSubTables = {
			"SPOWScenarioTriggerConfigData",
		},
	},
	SPOceanPolygonConfigDataTable = {
		SPOceanPolygonConfigDataSubTables = {
			"SPOceanPolygonConfigData",
		},
	},
	SPOfflineSOCStarpTable = {
		SPOfflineSOCSubTables = {
			"SPOfflineSOC",
		},
		SPOfflineSOCPetStatusSubTables = {
			"SPOfflineSOCPetStatus",
		},
		SPOfflineSOCSatietySubTables = {
			"SPOfflineSOCSatiety",
		},
	},
	SPPCKeyConfigDataTable = {
		SPPCKeyActionNameDataSubTables = {
			"SPPCKeyActionNameData",
		},
		SPPCKeyGroupDataSubTables = {
			"SPPCKeyGroupData",
		},
		SPPCKeyIconDataSubTables = {
			"SPPCKeyIconData",
		},
	},
	SPPVPAutoChessTable = {
		SPPVPAutoChessMapSubTables = {
			"SPPVPAutoChessMap",
		},
	},
	SPPVPGlobalTable = {
		SPPVPAITemplatePetDataSubTables = {
			"SPPVPAITemplatePetDataCfg",
		},
		SPPVPAirSupplyGetConditionSubTables = {
			"SPPVPAirSupplyGetCondition",
		},
		SPPVPGlobalSubTables = {
			"SPPVPGlobal",
			"SPPVPGlobal1",
		},
		SPPVPGlobal_ABTestSubTables = {
			"SPPVPGlobal_ABTest",
			"SPPVPGlobal_ABTest1",
		},
		SPPVPRobotDataSubTables = {
			"SPPVPRobotData",
		},
	},
	SPPVPGuideTable = {
		SPPVPGuideSubTables = {
			"SPPVPGuide",
		},
		SPPVPGuideGlobalSubTables = {
			"SPPVPGuideGlobal",
		},
		SPPVPGuideMonsterSubTables = {
			"SPPVPGuideMonster",
		},
		SPPVPInitGuideSubTables = {
			"SPPVPInitGuide",
		},
	},
	SPPVPOfficialLineupTable = {
		SPPvpOfficialLineupSubTables = {
			"ResSPPvpOfficialLineup",
		},
	},
	SPPVPPetTable = {
		SPPVPAirSupplySubTables = {
			"SPPVPAirSupply",
		},
		SPPVPAirSupplyPoolSubTables = {
			"SPPVPAirSupplyPool",
		},
		SPPVPAirSupplyRuleSubTables = {
			"SPPVPAirSupplyRule",
		},
		SPPVPPetSubTables = {
			"SPPVPPet",
		},
		SPPVPPetBodyScaleSubTables = {
			"SPPVPPetBodyScale",
		},
		SPPVPPetJobSubTables = {
			"SPPVPPetJob",
		},
		SPPVPPetPresetsSubTables = {
			"SPPVPPetPresets",
		},
		SPPVPPetTagSubTables = {
			"SPPVPPetTag",
		},
		SPPVPPetTypeSubTables = {
			"SPPVPPetType",
		},
		SPPVPPsychicPowerSubTables = {
			"SPPVPPsychicPower",
		},
		SPPVPSkillGroupSubTables = {
			"SPPVPSkillGroup",
		},
	},
	SPPVPPetLevelAttributeTable = {
		SPPVPPetLevelAttributeSubTables = {
			"SPPVPPetLevelAttribute",
		},
		SPPVPPetLevelAttribute_ABTestSubTables = {
			"SPPVPPetLevelAttribute_ABTest",
		},
		SPPVPPetMonsterLevelAttributeSubTables = {
			"SPPVPPetMonsterLevelAttribute",
		},
		SPPVPPetMonsterLevelAttribute_ABTestSubTables = {
			"SPPVPPetMonsterLevelAttribute_ABTest",
		},
		SPPVPPetStarLevelAttributeSubTables = {
			"SPPVPPetStarLevelAttribute",
		},
	},
	SPPVPTalentTable = {
		SPPVPTalentSubTables = {
			"SPPVPTalent",
		},
		SPPVPTalentSlotSubTables = {
			"SPPVPTalentSlot",
		},
		SPPVPTalentTreeSubTables = {
			"SPPVPTalentTree",
		},
	},
	SPPanelTable = {
		SPPanelSubTables = {
			"SPPanel",
		},
	},
	SPPassiveAbilityDataTable = {
		SPPassiveAbilitySubTables = {
			"SPPassiveAbilityData",
		},
	},
	SPPassiveSkillsConfigTable = {
		SPPassiveSkillsConfSubTables = {
			"SPPassiveSkillsConf",
		},
	},
	SPPerformanceSettingsTable = {
		SPMapPerfSettingSubTables = {
			"SPMapPerfSettingData",
		},
		SPPerfSettingTypeSubTables = {
			"SPPerfSettingTypeData",
		},
	},
	SPPetTable = {
		SPAfterLeavingCombatResetAttributeListSubTables = {
			"SPAfterLeavingCombatResetAttributeList",
		},
		SPCaptureExpSubTables = {
			"SPCaptureExpCfg",
		},
		SPCaptureExpGroupSubTables = {
			"SPCaptureExpGroup",
		},
		SPFlyPetWorkSlotSubTables = {
			"SPFlyPetWorkSlot",
		},
		SPMoveAreaConfigSubTables = {
			"SPMoveAreaConfig",
		},
		SPPetSubTables = {
			"SPPet",
		},
		SPPetAISubTables = {
			"SPPetAI",
		},
		SPPetActiveSkillPoolSubTables = {
			"SPPetActiveSkillPool",
		},
		SPPetAffinitySubTables = {
			"SPPetAffinity",
		},
		SPPetAffinityTemplateSubTables = {
			"SPPetAffinityTemplate",
		},
		SPPetBTSubTables = {
			"SPPetBT",
		},
		SPPetBeLazyLimitDataSubTables = {
			"SPPetBeLazyLimitData",
		},
		SPPetBodyTypeSubTables = {
			"SPPetBodyType",
		},
		SPPetCommonBehaviorConifgSubTables = {
			"SPPetCommonBehaviorConifg",
		},
		SPPetElementDetailSubTables = {
			"SPPetElementDetail",
		},
		SPPetExpListSubTables = {
			"SPPetExpList",
		},
		SPPetFormSubTables = {
			"SPPetForm",
		},
		SPPetHungerSubTables = {
			"SPPetHunger",
		},
		SPPetInteractiveBehaviorSubTables = {
			"SPPetInteractiveBehavior",
		},
		SPPetPassiveSkillDetailSubTables = {
			"SPPetPassiveSkillDetail",
		},
		SPPetPassiveSkillMutexSubTables = {
			"SPPetPassiveSkillMutex",
		},
		SPPetPassiveSkillPoolSubTables = {
			"SPPetPassiveSkillPool",
		},
		SPPetPotentialAttrSubTables = {
			"SPPetPotentialAttr",
		},
		SPPetPotentialPoolSubTables = {
			"SPPetPotentialPool",
		},
		SPPetRarityEffectSubTables = {
			"SPPetRarityEffect",
		},
		SPPetRarityPoolSubTables = {
			"SPPetRarityPool",
		},
		SPPetSOCBehaviorSubTables = {
			"SPPetSOCBehavior",
		},
		SPPetSOCConifgSubTables = {
			"SPPetSOCConifg",
		},
		SPPetStatusEffectSubTables = {
			"SPPetStatusEffect",
		},
		SPPetStatusEffectConditionSubTables = {
			"SPPetStatusEffectCondition",
		},
		SPPetTenacitySubTables = {
			"SPPetTenacity",
		},
		SPPetTypeSubTables = {
			"SPPetType",
		},
		SPPetWorkSkillDetailSubTables = {
			"SPPetWorkSkillDetail",
		},
		SPPetWorldJobSubTables = {
			"SPPetWorldJob",
		},
		SPPetWorldJobTypeSubTables = {
			"SPPetWorldJobType",
		},
		SPSkillGroupSubTables = {
			"SPSkillGroup",
		},
		SPTransportDataSubTables = {
			"SPTransportData",
		},
	},
	SPPetAffinityConfigTable = {
		SPPetAffinityConfigSubTables = {
			"SPPetAffinityConfig",
		},
	},
	SPPetAmuletTable = {
		SPPetAmuletSubTables = {
			"SPPetAmulet",
		},
		SPPetAmuletUpgradeSubTables = {
			"SPPetAmuletUpgrade",
		},
	},
	SPPetClientNetConfigTable = {
		SPPetClientNetConfigSubTables = {
			"SPPetClientNetConfig",
		},
	},
	SPPetIllustratedAwardTable = {
		SPPetIllustratedAwardSubTables = {
			"SPPetIllustratedAward",
		},
	},
	SPPetInteractionConfigTable = {
		SPPetInteractionConfigSubTables = {
			"SPPetInteractionConfig",
		},
	},
	SPPetLevelAttributeStarpTable = {
		SPPetLevelAttributeSubTables = {
			"SPPetLevelAttribute",
		},
		SPPetLevelAttribute_ABTestSubTables = {
			"SPPetLevelAttribute_ABTest",
		},
		SPPetMonsterLevelAttributeSubTables = {
			"SPPetMonsterLevelAttribute",
		},
		SPPetMonsterLevelAttribute_ABTestSubTables = {
			"SPPetMonsterLevelAttribute_ABTest",
		},
		SPPetStarLevelAttributeSubTables = {
			"SPPetStarLevelAttribute",
		},
	},
	SPPetPrefebTable = {
		SPPetPrefebSubTables = {
			"SPPetPrefeb",
		},
	},
	SPPetRideTable = {
		SPPetRideBasicInfoSubTables = {
			"SPPetRideBasicInfo",
		},
		SPPetRideMoveSubTables = {
			"SPPetRideMove",
		},
		SPPetRidePoseSubTables = {
			"SPPetRidePose",
		},
		SPPetRidePoseTMSubTables = {
			"SPPetRidePoseTM",
		},
		SPPetRideShapeSubTables = {
			"SPPetRideShape",
		},
	},
	SPPetRotationControlConfigTable = {
		SPPetRotationControlConfigSubTables = {
			"SPPetRotationControlConfig",
		},
	},
	SPPetTakeCreditDataTable = {
		SPPetTakeCreditDataSubTables = {
			"SPPetTakeCreditData",
		},
	},
	SPPetTypeAssetTable = {
		SPPetTypeAssetSubTables = {
			"SPPetTypeAsset",
		},
	},
	SPPhysicsMatAttrDataTable = {
		SPPhysicsMatAttrDataSubTables = {
			"SPPhysicsMatAttrData",
		},
	},
	SPPreloadTable = {
		SPPreloadPathSubTables = {
			"SPPreloadPath",
		},
		SPSeedDSPreloadPathSubTables = {
			"SPSeedDSPreloadPath",
		},
	},
	SPProfessionConfigStarpTable = {
		SPProfessionSubTables = {
			"SPProfessionConf_common",
		},
		SPProfessionGenreSubTables = {
			"SPProfessionGenreConf",
		},
	},
	SPPuzzleMatrixConfigTable = {
		SPPuzzleMatrixConfigSubTables = {
			"SPPuzzleMatrixConfig",
		},
	},
	SPPvpTable = {
		SPPvpBasicConfSubTables = {
			"ResSPPvpBasicConf",
		},
	},
	SPQualifyingTable = {
		SPSeasonQualifyingMailSubTables = {
			"SPSeasonQualifyingMailData_starp_pvp",
		},
		SPShocksQualifyingRewardDataSubTables = {
			"SPShocksQualifyingRewardData_starp_pvp",
		},
	},
	SPResourceAICreatorTable = {
		SPResourceAICreatorSubTables = {
			"SPResourceAICreator",
		},
	},
	SPResourceAirwallStarpTable = {
		SPResourceAirwallSubTables = {
			"SPResourceAirwall",
		},
	},
	SPResourceBalanceRuleTable = {
		SPResourceBalanceRuleSubTables = {
			"ResSPResourceBalanceRule",
		},
	},
	SPResourceControlTable = {
		SPResourceControlConfSubTables = {
			"SPResourceControl",
		},
	},
	SPResourceGPOTable = {
		SPResourceGPOSubTables = {
			"SPResourceGPO",
		},
	},
	SPResourcePatrolStarpTable = {
		SPResourcePatrolSubTables = {
			"SPResourcePatrol",
		},
	},
	SPResourceSTPTable = {
		SPResourceSTPSubTables = {
			"SPResourceSTP",
		},
	},
	SPResourceVirtualPointTable = {
		SPResourceVirtualPointSubTables = {
			"SPResourceVirtualPoint",
		},
	},
	SPResourceZoneTable = {
		SPResourceZoneSubTables = {
			"SPResourceZone",
		},
	},
	SPRiverPolygonConfigDataTable = {
		SPRiverPolygonConfigDataSubTables = {
			"SPRiverPolygonConfigData",
		},
	},
	SPRoleLevelBaseAttributeTable = {
		SPRoleLevelBaseAttributeSubTables = {
			"SPRoleLevelBaseAttribute",
		},
	},
	SPSOCAIBuildTaskConfigTable = {
		SPSOCAIBuildTaskSubTables = {
			"SPSOCAIBuildTask",
		},
		SPSOCLODConfigSubTables = {
			"SPSOCLODConfig",
		},
	},
	SPSettingConfigTable = {
		SPSettingConfSubTables = {
			"SPSettingConfig",
		},
	},
	SPSettingPCConfigTable = {
		SPSettingPCConfSubTables = {
			"SPSettingPCConfig",
		},
	},
	SPShareTable = {
		SPShareConfigSubTables = {
			"SPShareConfig",
		},
	},
	SPShipFunctionControlTable = {
		SPShipFunctionControlSubTables = {
			"SPShipFunctionControl",
		},
	},
	SPShopConfigTable = {
		SPShopConfigSubTables = {
			"SPShopConfig",
		},
	},
	SPShopDetailsConfigTable = {
		SPShopDetailsConfigSubTables = {
			"SPShopDetailsConfig",
		},
	},
	SPSimpleActionTable = {
		SPSimpleCondActionPairSubTables = {
			"SPSimpleCondActionPair",
		},
	},
	SPSkillComboDataTable = {
		SPSkillComboDataSubTables = {
			"SPSkillComboData",
		},
	},
	SPSkinVoiceBankMapConfigDataTable = {
		SPSkinVoiceBankMapConfigDataSubTables = {
			"SPSkinVoiceBankMapConfigData",
		},
	},
	SPSnapConfigTable = {
		SPSnapConfigDataSubTables = {
			"SPSnapConfigData",
		},
	},
	SPSocInteractionRatioTable = {
		SPSocInteractionRatioConfSubTables = {
			"SPSocInteractionRatio",
		},
	},
	SPSoundVolumeConfigDataTable = {
		SPSoundVolumeConfigDataSubTables = {
			"SPSoundVolumeConfigData",
		},
	},
	SPStaminaStarpTable = {
		SPStaminaCostSubTables = {
			"SPStaminaCost",
		},
		SPStaminaSumSubTables = {
			"SPStaminaSum",
		},
	},
	SPStarEggConfigDataTable = {
		SPStarEggConfigDataSubTables = {
			"SPStarEggConfigData",
		},
		SPStarEggFirstIncubateDataSubTables = {
			"SPStarEggFirstIncubateData",
		},
		SPStarEggFlashDataSubTables = {
			"SPStarEggFlashData",
		},
		SPStarEggGroupDataSubTables = {
			"SPStarEggGroupData",
		},
		SPStarEggRandomPoolDataSubTables = {
			"SPStarEggRandomPoolData",
		},
		SPStarEggRandomWeightDataSubTables = {
			"SPStarEggRandomWeightData",
		},
		SPStarEggTemperatureDataSubTables = {
			"SPStarEggTemperatureData",
		},
	},
	SPStatusStarpTable = {
		SPPetStatusSubTables = {
			"SPPetStatus",
		},
		SPPlayerStatusSubTables = {
			"SPPlayerStatus",
		},
	},
	SPStorageBoxItemConfTable = {
		SPStorageBoxItemConfSubTables = {
			"SPStorageBoxItemConf",
		},
	},
	SPStorageBoxItemPushConvertTable = {
		SPStorageBoxItemPushConvertSubTables = {
			"SPStorageBoxItemPushConvert",
		},
	},
	SPStoryGroupConfigDataTable = {
		SPStoryGroupConfigDataSubTables = {
			"SPStoryGroupConfigData",
		},
	},
	SPStoryLineConfigTable = {
		SPStoryLineCondConfigSubTables = {
			"SPStoryLineCondConfig",
		},
		SPStoryLineConfigSubTables = {
			"SPStoryLineConfig",
		},
		SPStoryLineFixedCameraConfigSubTables = {
			"SPStoryLineFixedCameraConfig",
		},
		SPStoryLineSwitcherConfigSubTables = {
			"SPStoryLineSwitcherConfig",
		},
	},
	SPStrangerChatTable = {
		SPSayHiTextConfDataSubTables = {
			"SPSayHiTextConfData",
		},
	},
	SPStrangerChatEntranceTable = {
		SPSayHiEntranceConfDataSubTables = {
			"SPSayHiEntranceConfData",
		},
	},
	SPStrangerChatPopTable = {
		SPSayHiPopConfDataSubTables = {
			"SPSayHiPopConfData",
		},
	},
	SPStrongerTable = {
		SPStrongerSceneSubTables = {
			"SPStrongerScene",
		},
		SPStrongerWaySubTables = {
			"SPStrongerWay",
		},
	},
	SPSummonConfigStarpTable = {
		SPSummonConfigSubTables = {
			"SPSummonConfig",
		},
	},
	SPTagTable = {
		SPStateToTagSubTables = {
			"SPStateToTag",
		},
		SPTagNameSubTables = {
			"SPTagName",
		},
		SPTagToStateSubTables = {
			"SPTagToState",
		},
		SPTagToTagSubTables = {
			"SPTagToTag",
		},
	},
	SPTalentStarpTable = {
		SPTalentInfoSubTables = {
			"SPTalentInfo",
		},
		SPTalentTreeSubTables = {
			"SPTalentTree",
		},
		SPTalentAbilityInfoSubTables = {
			"SPalentAbilityInfo",
		},
	},
	SPTargetIndicatorTable = {
		SPTargetIndicatorSubTables = {
			"SPTargetIndicator",
		},
	},
	SPTaskTable = {
		SPTaskConfSubTables = {
			"SPTaskConf_common",
		},
	},
	SPTaskActiveRewardTable = {
		ResSPTaskActiveRewardSubTables = {
			"ResSPTaskActiveRewardConf",
		},
	},
	SPTaskConditionSkipTable = {
		ResSPTaskConditionSkipSubTables = {
			"ResSPTaskConditionSkipConf",
		},
	},
	SPTaskGroupTable = {
		ResSPTaskGroupSubTables = {
			"ResSPTaskGroupConf",
		},
	},
	SPTaskGroupTypeTable = {
		ResSPTaskGroupTypeSubTables = {
			"ResSPTaskGroupTypeConf",
		},
	},
	SPTechGroupConfigTable = {
		SPTechGroupConfigDataSubTables = {
			"SPTechGroupConfigData",
		},
	},
	SPTechPointConfigTable = {
		SPTechPointConfigDataSubTables = {
			"SPTechPointConfigData",
		},
	},
	SPTechTreeConfigTable = {
		SPTechTreeConfigDataSubTables = {
			"SPTechTreeConfigData",
		},
	},
	SPTimeRefreshRuleTable = {
		ResSPTimeRefreshRuleSubTables = {
			"ResSPTimeRefreshRule",
		},
	},
	SPTlogCheckerStarpTable = {
		SPTlogCheckerSubTables = {
			"ResSPTlogChecker",
		},
	},
	SPTradingPostStarpTable = {
		SPTradingPostLevelDataSubTables = {
			"SPTradingPostLevelData",
		},
		SPTradingPostOrderDataSubTables = {
			"SPTradingPostOrderData",
		},
		SPTradingPostOrderRefreshCDDataSubTables = {
			"SPTradingPostOrderRefreshCDData",
		},
		SPTradingPostOrderRefreshDataSubTables = {
			"SPTradingPostOrderRefreshData",
		},
		SPTradingPostTagDataSubTables = {
			"SPTradingPostTagData",
		},
	},
	SPTreasureChestConfigDataTable = {
		SPTreasureChestConfigDataSubTables = {
			"SPTreasureChestConfigData",
		},
		SPTreasureChestRandomPoolDataSubTables = {
			"SPTreasureChestRandomPoolData",
		},
		SPTreasureChestRandomWeightDataSubTables = {
			"SPTreasureChestRandomWeightData",
		},
	},
	SPTreeStaticMeshScaleDataTable = {
		TreeStaticMeshScaleDataSubTables = {
			"SPTreeStaticMeshScaleData",
		},
	},
	SPTriggerTlogConfigTable = {
		SPTriggerTlogConfigSubTables = {
			"SPTriggerTlogConfig",
		},
	},
	SPTutorialConfigTable = {
		SPQuickSearchConfigSubTables = {
			"SPQuickSearchConfig",
		},
		SPTutorialCategoryConfigSubTables = {
			"SPTutorialCategoryConfig",
		},
		SPTutorialConfigSubTables = {
			"SPTutorialConfig",
		},
		SPTutorialDetailConfigSubTables = {
			"SPTutorialDetailConfig",
		},
	},
	SPUITipTable = {
		SPCaptureTipSubTables = {
			"SPCaptureTip",
		},
		SPCommonUITipSubTables = {
			"SPCommonUITip",
		},
		SPMainUITipSubTables = {
			"SPMainUITip",
		},
	},
	SPValueChangeTable = {
		SPElectricLevelSubTables = {
			"SPElectricLevel",
		},
	},
	SPVideoConfigTable = {
		SPVideoConfigSubTables = {
			"SPVideoConfig",
		},
	},
	SPWeaponTable = {
		SPWeaponRenovationConfSubTables = {
			"SPWeaponRenovationConfDataNew",
		},
		SPWeaponUnlockInfoNewSubTables = {
			"SPWeaponUnlockInfoNew",
		},
	},
	SPWorldCareerIdolConfigDataTable = {
		SPWorldCareerIdolConfigDataSubTables = {
			"SPWorldCareerIdolConfigData",
		},
	},
	SPWorldCareerIdolPointDataTable = {
		SPWorldCareerIdolPointDataSubTables = {
			"SPWorldCareerIdolPointData",
		},
	},
	SPWorldChoppablePointDataTable = {
		SPWorldChoppablePointDataSubTables = {
			"SPWorldChoppablePointData",
			"SPWorldChoppablePointData_PCG",
			"SPWorldChoppablePointData_Tree",
		},
	},
	SPWorldChoppableTreeSMDataTable = {
		SPWorldChoppableTreeSMDataSubTables = {
			"SPWorldChoppableTreeSMData",
		},
	},
	SPWorldFireBeadPointDataTable = {
		SPWorldFireBeadPointDataSubTables = {
			"SPWorldFireBeadPointData",
		},
	},
	SPWorldGodStatuePointDataTable = {
		SPWorldGodStatuePointDataSubTables = {
			"SPWorldGodStatuePointData",
		},
	},
	SPWorldIllegalBuildRegionPointDataTable = {
		SPWorldIllegalBuildRegionPointDataSubTables = {
			"SPWorldIllegalBuildRegionPointData",
		},
	},
	SPWorldLandTemplePointDataStarpTable = {
		SPWorldLandTemplePointDataSubTables = {
			"SPWorldLandTemplePointData",
		},
	},
	SPWorldLeafEffectDataTable = {
		SPWorldLeafEffectPointDataSubTables = {
			"SPWorldLeafEffectPointData",
		},
	},
	SPWorldLevelTable = {
		SPWorldLevelSubTables = {
			"SPWorldLevel",
		},
	},
	SPWorldMazeConfigStarpTable = {
		SPWorldMazeConfigDataSubTables = {
			"SPWorldMazeConfigData",
		},
	},
	SPWorldMazeEntranceStarpTable = {
		SPWorldMazeEntranceDataSubTables = {
			"SPWorldMazeEntranceData",
		},
	},
	SPWorldMonsterLevelRangeDataTable = {
		SPWorldMonsterLevelRangeDataSubTables = {
			"SPWorldMonsterLevelRangeData",
		},
	},
	SPWorldMonsterPointDataTable = {
		SPWorldMonsterPointDataSubTables = {
			"SPWorldMonsterPointData",
		},
	},
	SPWorldMonsterTimeTypeDataStarpTable = {
		SPWorldMonsterTimeTypeDataSubTables = {
			"SPWorldMonsterTimeTypeData",
		},
	},
	SPWorldNPCPointDataTable = {
		SPWorldNPCPointDataSubTables = {
			"SPWorldNPCPointData",
		},
	},
	SPWorldOceanPolygonPointDataTable = {
		SPWorldOceanPolygonPointDataSubTables = {
			"SPWorldOceanPolygonPointData",
		},
	},
	SPWorldPOIRegionInjectDataTable = {
		SPWorldPOIRegionInjectDataSubTables = {
			"SPWorldPOIRegionInjectData",
		},
	},
	SPWorldPOIRegionPointDataTable = {
		SPWorldPOIRegionPointDataSubTables = {
			"SPWorldPOIRegionPointData",
		},
	},
	SPWorldPVPEntranceStarpTable = {
		SPWorldPVPEntranceDataSubTables = {
			"SPWorldPVPEntranceData",
		},
	},
	SPWorldPickablesDataTable = {
		SPWorldPickablesPointDataSubTables = {
			"SPWorldPickablesPointData",
			"SPWorldPickablesPointData_PCG",
		},
		SPWorldPickablesPoolDataSubTables = {
			"SPWorldPickablesPoolData",
		},
	},
	SPWorldPropsPointDataTable = {
		SPWorldPropsPointDataSubTables = {
			"SPWorldPropsPointData",
			"SPWorldPropsPointData_PCG",
		},
	},
	SPWorldPropsPoolDataTable = {
		SPWorldPropsPoolDataSubTables = {
			"SPWorldPropsPoolData",
		},
	},
	SPWorldRebornPointDataTable = {
		SPWorldRebornPointDataSubTables = {
			"SPWorldRebornPointData",
		},
	},
	SPWorldRegionDataTable = {
		SPWorldRegionDataSubTables = {
			"SPWorldRegionData",
		},
	},
	SPWorldRegionDefineTable = {
		SPWorldRegionDefineSubTables = {
			"SPWorldRegionDefine",
		},
	},
	SPWorldRiverPolygonPointDataTable = {
		SPWorldRiverPolygonPointDataSubTables = {
			"SPWorldRiverPolygonPointData",
		},
	},
	SPWorldSeaAudioDataTable = {
		SPWorldSeaAudioDataSubTables = {
			"SPWorldSeaAudioData",
		},
	},
	SPWorldSkillFruitTreeDataTable = {
		SPWorldSkillFruitConfigDataSubTables = {
			"SPWorldSkillFruitConfigData",
		},
		SPWorldSkillFruitTreePointDataSubTables = {
			"SPWorldSkillFruitTreePointData",
		},
	},
	SPWorldSoundVolumePointDataTable = {
		SPWorldSoundVolumePointDataSubTables = {
			"SPWorldSoundVolumePointData",
		},
	},
	SPWorldStarDiaryPointDataTable = {
		SPWorldStarDiaryPointDataSubTables = {
			"SPWorldStarDiaryPointData",
		},
	},
	SPWorldStarEggPointDataTable = {
		SPWorldStarEggPointDataSubTables = {
			"SPWorldStarEggPointData",
			"SPWorldStarEggPointData_PCG",
		},
	},
	SPWorldStreamablePOIConfigTable = {
		SPWorldStreamablePOIConfigSubTables = {
			"SPWorldStreamablePOIConfig",
		},
	},
	SPWorldStreamablePOIPreloadAssetBundleConfigTable = {
		SPWorldStreamablePOIPreloadAssetBundleConfigSubTables = {
			"SPWorldStreamablePOIPreloadAssetBundleConfig",
		},
	},
	SPWorldStreamablePOIPreloadAssetPathConfigTable = {
		SPWorldStreamablePOIPreloadAssetPathConfigSubTables = {
			"SPWorldStreamablePOIPreloadAssetPathConfig",
		},
	},
	SPWorldStreamablePOIPreloadDataTable = {
		SPWorldStreamablePOIPreloadDataSubTables = {
			"SPWorldStreamablePOIPreloadData",
		},
	},
	SPWorldTeamPVEConfigStarpTable = {
		SPWorldTeamPVEConfigDataSubTables = {
			"SPWorldTeamPVEConfigData",
		},
	},
	SPWorldTeamPVEEntranceStarpTable = {
		SPWorldTeamPVEEntranceDataSubTables = {
			"SPWorldTeamPVEEntranceData",
		},
	},
	SPWorldTeleportPointDataStarpTable = {
		SPWorldTeleportPointDataSubTables = {
			"SPWorldTeleportPointData",
		},
	},
	SPWorldTemperatureDefineTable = {
		SPWorldTemperatureDefineSubTables = {
			"SPWorldTemperatureDefine",
		},
	},
	SPWorldTowerConfigStarpTable = {
		SPWorldTowerConfigDataSubTables = {
			"SPWorldTowerConfigData",
		},
	},
	SPWorldTowerEntranceStarpTable = {
		SPWorldTowerEntranceDataSubTables = {
			"SPWorldTowerEntranceData",
		},
	},
	SPWorldTreasureChestPointDataTable = {
		SPWorldTreasureChestPointDataSubTables = {
			"SPWorldTreasureChestPointData",
			"SPWorldTreasureChestPointData_PCG",
		},
	},
	SPWorldUnderlandConfigStarpTable = {
		SPWorldUnderlandConfigDataSubTables = {
			"SPWorldUnderlandConfigData",
		},
	},
	SPWorldUnderlandEntranceStarpTable = {
		SPWorldUnderlandEntranceDataSubTables = {
			"SPWorldUnderlandEntranceData",
		},
	},
	SPWorldViewPointDataTable = {
		SPWorldViewPointDataSubTables = {
			"SPWorldViewPointData",
		},
	},
	SPWorldViewPointDescTable = {
		SPWorldViewPointDescSubTables = {
			"SPWorldViewPointDesc",
		},
	},
	SPWorldZoneTemperatureTable = {
		SPWorldZoneTemperatureSubTables = {
			"SPWorldZoneTemperature",
		},
	},
	SnsShareTable = {
		ShareGameChatTypeConfigSubTables = {
			"ShareGameChatTypeConfig_StarP",
		},
		SnsShareConfigSubTables = {
			"SnsShareConfigData_StarP",
		},
	},
	StarPCommonDbWhiteListConfTable = {
		StarPCommonDbWhiteListConfSubTables = {
			"StarPCommonDbWhiteListConf",
		},
	},
	StarPEnumConfTable = {
		StarPEnumConfSubTables = {
			"StarPEnumConf",
		},
	},
	StarPLoadingPicConfTable = {
		StarPLoadingPicConfSubTables = {
			"StarPLoadingPicConf",
		},
	},
	StarPPicConfTable = {
		StarPPicConfSubTables = {
			"StarPPicConf",
		},
	},
	StarPRoomConfTable = {
		StarPRoomConfSubTables = {
			"StarPRoomConf",
		},
	},
	StarPRoomNumConfTable = {
		StarPRoomNumConfSubTables = {
			"StarPRoomNumConf",
		},
	},
	StarPSysConfStarpTable = {
		StarPSysConfSubTables = {
			"StarPSysConf",
		},
	},
	StarPTradeTable = {
		StarPTradeConstantDataSubTables = {
			"StarPTradeConstantData",
		},
		StarPTradeExprieMailDataSubTables = {
			"StarPTradeExprieMailData",
		},
		StarPTradeGoodsTypeDataSubTables = {
			"StarPTradeGoodsTypeData",
		},
		StarPTradeShelfDataSubTables = {
			"StarPTradeShelfData",
		},
		StarPTradeTabDataSubTables = {
			"StarPTradeTabData",
		},
		StarPTradeThingTypeDataSubTables = {
			"StarPTradeThingTypeData",
		},
	},
	TextTable = {
		ClientServerTextConfDataSubTables = {
			"ServerTextConfData_starp",
		},
	},
	TextLuaStarpTable = {
		TextEntryDataSubTables = {
			"TextEntryData_StarP",
		},
	},
	TextStarpTable = {
		ClientServerTextConfDataSubTables = {
			"ServerTextConfData_mainStarp",
		},
	},
}

return TableMap

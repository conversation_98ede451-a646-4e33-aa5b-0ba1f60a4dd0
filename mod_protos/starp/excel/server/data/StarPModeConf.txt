com.tencent.wea.xlsRes.starp.table_StarPModeConf
excel/xls/F_SP副本模式.xlsx sheet:模式列表
rows {
  id: 922
  name: "遗迹"
  type: E_MODE_TYPE_QUICKDUNGEON
  functionId: 1
  reviveType: E_MODE_Revive_LimitCount
  revivePara: 3
  revivePara: 2000
  revivePara: 300
  revivePointType: E_MODE_RevivePoint_ActiveFirst
  petreviveType: E_MODE_Pet_Revive_OW
  monsterDropType: SPMMDT_Drop
  lightMapPath: "/Game/Feature/StarP/Levels/Dungeons_Runtime/Starp_A01_Dungeon_Light"
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 0
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 0
  forbidPet: 0
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 923
  name: "栖地"
  type: E_MODE_TYPE_REGION
  functionId: 2
  reviveType: E_MODE_Revive_LimitCount
  revivePara: 3
  revivePara: 2000
  revivePara: 300
  revivePointType: E_MODE_RevivePoint_ActiveFirst
  petreviveType: E_MODE_Pet_Revive_OW
  monsterDropType: SPMMDT_Drop
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 0
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 0
  forbidPet: 0
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 924
  name: "封印之塔"
  type: E_MODE_TYPE_TOWER
  functionId: 3
  reviveType: E_MODE_Revive_LimitCount
  revivePara: 3
  revivePara: 2000
  revivePara: 300
  revivePointType: E_MODE_RevivePoint_ActiveFirst
  petreviveType: E_MODE_Pet_Revive_OW
  monsterDropType: SPMMDT_Drop
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 0
  forbidPet: 0
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 925
  name: "爬塔挑战"
  type: E_MODE_TYPE_TOWERPLUS
  functionId: 4
  reviveType: E_MODE_Revive_UseCountdown
  revivePara: 1
  revivePointType: E_MODE_RevivePoint_Invalid
  petreviveType: E_MODE_Pet_Revive_Invalid
  monsterDropType: SPMMDT_Drop
  forbidFly: 1
  pausePlayMessageGroup: SPUIGroupType_LetTop_Center
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 1
  forbidPet: 1
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 926
  name: "溯灵秘境"
  type: E_MODE_TYPE_DUNGEON
  functionId: 5
  reviveType: E_MODE_Revive_LimitCount
  revivePara: 3
  revivePara: 2000
  revivePara: 300
  revivePointType: E_MODE_RevivePoint_ActiveFirst
  petreviveType: E_MODE_Pet_Revive_Invalid
  monsterDropType: SSPMMDT_No_Drop
  lightMapPath: "/Game/Feature/StarP/Levels/Dungeons_Runtime/Starp_A01_Dungeon_Light"
  forbidFly: 1
  pausePlayMessageGroup: SPUIGroupType_LetTop_Center
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 1
  forbidPet: 1
  forbidFitness: 0
  dailyReduceFitness: 0
}
rows {
  id: 927
  name: "单人PVP副本"
  type: E_MODE_TYPE_PVP_QUICK
  functionId: 6
  reviveType: E_MODE_Revive_Invalid
  revivePointType: E_MODE_RevivePoint_Invalid
  petreviveType: E_MODE_Pet_Revive_Invalid
  monsterDropType: SSPMMDT_No_Drop
  lightMapPath: "/Game/Feature/StarP/Levels/Dungeons_Runtime/Starp_A01_Dungeon_Light"
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 1
  forbidPet: 1
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 928
  name: "单人PVP副本"
  type: E_MODE_TYPE_PVP_RANK
  functionId: 7
  reviveType: E_MODE_Revive_Invalid
  revivePointType: E_MODE_RevivePoint_Invalid
  petreviveType: E_MODE_Pet_Revive_Invalid
  monsterDropType: SSPMMDT_No_Drop
  lightMapPath: "/Game/Feature/StarP/Levels/Dungeons_Runtime/Starp_A01_Dungeon_Light"
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 1
  forbidPet: 1
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 929
  name: "单人PVP副本"
  type: E_MODE_TYPE_PVP_FRIEND
  functionId: 8
  reviveType: E_MODE_Revive_Invalid
  revivePointType: E_MODE_RevivePoint_Invalid
  petreviveType: E_MODE_Pet_Revive_Invalid
  monsterDropType: SSPMMDT_No_Drop
  lightMapPath: "/Game/Feature/StarP/Levels/Dungeons_Runtime/Starp_A01_Dungeon_Light"
  forbidFly: 1
  forbidPetInteract: 1
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 1
  forbidPet: 1
  forbidFitness: 1
  dailyReduceFitness: 0
}
rows {
  id: 930
  name: "宗门"
  type: E_MODE_TYPE_GROUP
  functionId: 9
  reviveType: E_MODE_Revive_None
  revivePointType: E_MODE_RevivePoint_None
  petreviveType: E_MODE_Pet_Revive_None
  monsterDropType: SPMMDT_None
  forbidFly: 1
  forbidPetInteract: 0
  finishedInvincible: 1
  forbidRecall: 1
  forbidBuild: 1
  forbidBag: 0
  forbidPet: 1
  forbidFitness: 1
  dailyReduceFitness: 0
}

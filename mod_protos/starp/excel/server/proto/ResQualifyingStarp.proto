syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes.starp;
import "ResKeywords.proto";
import "google/protobuf/timestamp.proto";


message DegreeStar{
  optional int32 star = 1;   // 升级需要的积分
  optional int32 integralLeft = 2;   // 积分左区间
  optional int32 integralRight = 3;  // 积分右区间
  optional int32 nextIntegral = 4;   // 升级需要的积分
  optional string starDesInfo = 5; // 星级描述
  optional int32 integralProtect = 6;// 是否积分保护 1 保护
  optional int32 degreeProtect = 7;  // 是否段位保护 1 保护
  optional int32 qualifyingWeight = 8;  // 段位权重
  optional int32 additionalIntegral = 9;   // 加星卡加星的分数
}

message DegreeCfg {
  optional int32 degreeID = 1;
  optional string degreeDesInfo = 2; // 段位描述
  optional string degreeIcon = 3;
  repeated DegreeStar degreeStar = 4;
  optional string degreeIconBP = 5;
  optional string degreeIconBG = 6;
  optional string QIDBG = 7;
  optional int32 robotGroupID = 8; // 机器人组ID 【SPPVP使用】
  optional int32 warmNfc = 9; // 温暖局nfc 【SPPVP使用】
  optional int32 warmMatchTimeout = 10; // 匹配超时温暖局 【SPPVP使用】
}


message DegreeType {
  optional int32 QID = 1; // 段位ID
  optional string desDegreeType = 2; // 段位描述
  optional string degreeIcon = 3;
  optional string degreeIconBP = 4;
  repeated DegreeCfg degreeCfgList = 5; // 段位配置
  optional bool isKingDegree = 6; // 是否王者段位
}

message DegreeTypeCfg {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated DegreeType degreeType = 2; // 段位描述
}

message table_DegreeTypeData {
  repeated DegreeTypeCfg rows = 1;
}


message QualifyingScore {
  optional int32 qualifyingIntegralRangeLeft = 1; // 排位分范围
  optional int32 qualifyingIntegralRangeRight = 2; // 排位分范围
  repeated int32 eliminateRoundsScore = 3; // 淘汰轮次积分
  optional int32 championScore = 4; // 夺冠积分
  optional int32 drawScore = 5; // 平局积分
}

message QualifyingIntegral{
  option (resKey) = "id";
  optional int32 id = 1;
  repeated SeasonQualifyIntergral seasonQualifyConfig = 2;
}

message SeasonQualifyIntergral {
  optional int32 matchType = 1;
  repeated QualifyingScore matchQualifyScore = 2;
}

message table_QualifyingIntegralData {
  repeated QualifyingIntegral rows = 1;
}

message table_QualifyingScoreData{
  repeated QualifyingScoreData rows = 1;
}

message QualifyingScoreData{
  option (resKey) = "id";
  optional int32 id = 1;
  repeated QualifyScoreKeyConfig QualifyingScoreConfig = 2;
}

message QualifyScoreKeyConfig{
  optional string scoreType = 1;  //服务器下发的分数类型
  optional string scoreText = 2;      //该类型对应的文本
}

message EspecialIntegral{
  optional int32 id = 1;
  optional int32 score = 3;     // 触发后的变动分数
  optional QualifyingEspecialIntegralType type = 4;   // 处理类型
  optional int32 finishNum = 5;   // 完成次数
}

message SeasonEspecialIntegral {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated EspecialIntegral especialIntegral = 2;
  optional string name= 3; 
}

message table_EspecialIntegralData {
  repeated SeasonEspecialIntegral rows = 1;
}

message SeasonCfg{
  option (resKey) = "id";
  optional int32 id = 1;// 赛季id
  optional string desInfo = 3;      // 描述
  optional google.protobuf.Timestamp startTime = 4;    // 赛季开始时间
  optional google.protobuf.Timestamp endTime = 5;      // 赛季结束时间
  optional int32 qualifyingID = 8;  // 指定段位信息ID
  optional int32 battleTimes = 13; // 排位次数，解锁奖励
  optional string lowVer = 16; //最低版本号
  optional int32 qualifyType = 17;// 排位类型 QualifyType枚举
  optional bool protectedScoreSwitch = 18; // 保分开关
  optional int32 mailConfId = 19;// 赛季重置邮件配置
  optional int32 matchTypeId = 20;// 玩法id
  optional int32 seasonId = 21;// 对应经典赛季id
  optional int32 qualifyIconStyle = 22;// 排位赛自定义图标显示样式
  optional int32 taskGroupId = 23;//赛季奖励任务组
  optional bool filterSeasonSettlementNtf = 24; // 过滤赛季结算协议 @noCli
  optional bool deprecated = 25; // 是否废弃 @noCli
  optional int32 playModeSeasonId = 26; // 关联的玩法赛季
  optional string seasonIcon = 27; // 印记图标
  optional string seasonTitle = 28; // 赛季标题
}

message table_SeasonCfgData {
  repeated SeasonCfg rows = 1;
}


message QualifyingReward{
  option (resKey) = "id";
  optional int32 id = 1;                   // 结算奖励ID
  repeated DegreeRewardCfg rewardList = 3; // 排位
}

message RewardCfg{
  optional int32 ID = 1;  // 奖励的道具ID
  optional int32 Num = 2; // 奖励的个数
  optional int32 RewardType = 3;
}

message DegreeRewardCfg{
  optional int32 qualifying = 1;
  optional int32 degreeID = 2;
  optional int32 star = 3;
  optional RewardCfg reward = 5; // 单人积分

}

message table_QualifyingRewardData {
  repeated QualifyingReward rows = 1;
}

message ShocksQualifyingReward{
  option (resKey) = "id";
  optional int32 id = 1;                        // 冲击段位奖励ID,配置在赛季中
}

message ShocksRewardCfg{
  optional int32 needQualifying = 1;                   // 冲击段位奖励ID
  repeated ShocksDegreeRewardCfg degreeIDList = 2;     // 小段位列表
}

message ShocksDegreeRewardCfg{
  optional int32 needDegreeID = 1;           // 冲击小段位奖励ID
  optional RewardCfg reward = 2;             // 奖励列表
}


message table_ShocksQualifyingRewardData {
  repeated ShocksQualifyingReward rows = 1;
}

message QualifyingInherit {// @noCli
  optional int32 qualifying = 1;  //对应段位
  optional int32 degreeID = 2;    // 对应小段位
  repeated int32 inherits = 3;     // 继承的积分 数组跨段位使用
}

message QualifyingInheritInfo {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  repeated QualifyingInherit qualifyingInherit = 2;
}

message table_QualifyingInheritData {// @noCli
  repeated QualifyingInheritInfo rows = 1;
}

message AILevelCfg{
  optional int32 probability = 1;
}

message QualifyingAICfg{
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 scoreLeft = 2;  //对应段位
  optional int32 scoreRight = 3;    // 对应小段位
}

message table_QualifyingAICfgData {
  repeated QualifyingAICfg rows = 1;
}




message QualifyingRankScore {// @noCli
  repeated int32 rankRange = 1;
  optional int32 score = 2;
}

message QualifyingLevelScoreInfo {// @noCli
  optional int32 qualifyingIntegralRangeLeft = 1;
  optional int32 qualifyingIntegralRangeRight = 2;
  optional int32 aliveScore = 3;
  optional int32 killScore = 4;
  repeated QualifyingRankScore rankScore = 5;
  repeated QualifyingRankScore teamRankScore = 6;
}


message QualifyingLevelScore {// @noCli
  optional com.tencent.wea.xlsRes.LevelType levelType = 1;
  repeated QualifyingLevelScoreInfo scoreInfo = 2;
}

message MatchQualifyingLevelScore {// @noCli
  optional int32 matchTypeId = 1;
  repeated QualifyingLevelScore qualifyingLevelScore = 2;
}

message SeasonMatchQualifyingLevelScore {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  repeated MatchQualifyingLevelScore matchQualifyingLevelScore = 2;
}


message table_QualifyingLevelScoreData {// @noCli
  repeated SeasonMatchQualifyingLevelScore rows = 1;
}

message RankKingDegreeInfo {
  optional int32 degreeID = 1;
  repeated int32 rankRange = 3;
}

message QualifyRankKingDegreeInfo {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated RankKingDegreeInfo rankKingDegreeInfo = 2;

}

message table_RankKingDegreeData {
  repeated QualifyRankKingDegreeInfo rows = 1;
}

message MatchDegreeTypeGroupInfo {
  optional int32 groupId = 1;
  optional int32 qualifyType = 2; // enum QualifyType
}

message MatchDegreeTypeGroupConfig {
  option (resKey) = "id";
  optional int32 id = 1; // 玩法ID
  repeated MatchDegreeTypeGroupInfo matchDegreeTypeGroup = 2; // 分组信息
  optional bool qualifyingSettlementAtFinal = 3; // 最终结算计算排位分
}

message table_MatchDegreeTypeGroupData {
  repeated MatchDegreeTypeGroupConfig rows = 1;
}


message MatchTypeIdJumpInfo {
  optional google.protobuf.Timestamp QualifyMatchJumpOpenTime = 1; // 玩法详情排位版面跳转显示开启时间(不配置常驻，配置则根据时间显示)
  optional google.protobuf.Timestamp QualifyMatchJumpCloseTime = 2; // 玩法详情排位版面跳转显示结束时间(不配置常驻，配置则根据时间显示)
  optional int32 QualifyMatchJumpId = 3; // 玩法详情排位版面跳转ID
  optional string QualifyMatchJumpDesc = 4; // 玩法详情排位版面跳转文本
  optional int32 RedDotType = 5;  //红点类型
  optional int32 RewardItemId = 6; //奖励物品Id
  optional int32 PlayModeSeasonId = 7; // 关联玩法赛季时间
  optional int32 RedDotParam = 8; //红点参数
}

message MatchTypeIdJumpConfig {
  option (resKey) = "MatchTypeId";
  optional int32 MatchTypeId = 1; // 玩法ID
  repeated MatchTypeIdJumpInfo JumpInfoConfig = 2; // 分组信息
}

message table_MatchTypeIdJumpConfig {
  repeated MatchTypeIdJumpConfig rows = 1;
}

message DimensionConditionInfo {
  optional int32 dimensionId = 1;
  repeated int32 dimensions = 2;
  optional MatchRelationOperType operType = 3;
  optional int32 score = 4;
  optional string text = 5; //文本Key
}

message QualifyDimensionInfo {
  optional int32 qualifyingIntegralRangeLeft = 1;
  optional int32 qualifyingIntegralRangeRight = 2;
  repeated DimensionConditionInfo dimensionConditions = 3;
}

message MatchTypeQualifyDimensionInfo {
  optional int32 matchTypeId = 1;
  repeated QualifyDimensionInfo qualifyDimensionInfo = 2;
  optional string name= 3; 
}

message SeasonMatchTypeQualifyDemensionInfo {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated MatchTypeQualifyDimensionInfo matchTypeQualifyDimensionInfo = 2;
}

message table_QualifyingLevelDimensionScoreData {
  repeated SeasonMatchTypeQualifyDemensionInfo rows = 1;
}


message QualifyingLevelDimensionConditionConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional BattleEventType battleEventType = 2;
  optional int32 rangeLeft = 3;
  optional int32 rangeRight = 4;
  optional string displayText = 5;
}

message table_QualifyingLevelDimensionConditionData {
  repeated QualifyingLevelDimensionConditionConfig rows = 1;
}




message QualifyingRewardCfg {
  optional int32 qualifying = 1;   // 大段位
  optional int32 degreeId = 2;     // 小段位
  repeated RewardCfg reward = 3;   // 赛季重置奖励
  optional int32 rewardId = 4; // 奖励ID
}

message SeasonQualifyingMail {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 mailId = 2;
  repeated QualifyingRewardCfg rewardCfg = 3;
  optional bool seasonSettlement = 4; // 是都赛季结算
  optional int32 settlementMailId = 5;
}

message table_SeasonQualifyingMail {
  repeated SeasonQualifyingMail rows = 1;
}


enum QualifyingIntegralSettlementType {// @noCli
  QIST_Default = 0;
  QIST_ELO = 1;
}

message QualifyingIntegralRangeSettlement {// @noCli
  optional int32 qualifyingIntegralRangeLeft = 1;
  optional int32 qualifyingIntegralRangeRight = 2;
  optional QualifyingIntegralSettlementType settlementType = 3;
}

message SettlementTypeConfig {// @noCli
  optional int32 matchTypeId = 1;
  repeated QualifyingIntegralRangeSettlement qualifyingIntegralRangeSettlement = 2;
}

message QualifyingIntegralSettlementConfig {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  repeated SettlementTypeConfig settlementTypeConfig = 2;
}

message table_QualifyingIntegralSettlementData {// @noCli
  repeated QualifyingIntegralSettlementConfig rows = 1;
}

message SeasonQualifyingContinueWinConfig {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  repeated MatchTypeQualifyingContinueWinConfig seasonConfig = 2;
}

message MatchTypeQualifyingContinueWinConfig {// @noCli
  optional int32 matchTypeId = 1;
  repeated QualifyingContinueWinConfig matchTypeConfig = 2;
}

message QualifyingContinueWinConfig {// @noCli
  optional int32 continueWinLeft = 1;
  optional int32 continueWinRight = 2;
  optional int32 addQualifyingIntegral = 3;
}

message table_QualifyingContinueWinData {// @noCli
  repeated SeasonQualifyingContinueWinConfig rows = 1;
}

// MOBA战力表现分上限
message PerfScoreLimit {
  optional int32 degreeID = 1;        // 小段位ID
  optional int32 limit = 2;           // 表现分上限
}

message QualifyingPerfScore {
  optional int32 QID = 1;                         // 段位ID
  optional int32 defaultLimit = 2;                // 段位默认表现分上限
  repeated PerfScoreLimit perfScoreLimitList = 3; // 小段位表现分上限配置
}

message QualifyingPerfScoreConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated QualifyingPerfScore qualifyingPerfScore = 2; // 大段位表现分上限配置
}

message table_QualifyingPerfScore{
  repeated QualifyingPerfScoreConf rows = 1;
}

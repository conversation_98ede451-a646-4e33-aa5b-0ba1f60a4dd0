syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.starp.protocol;

import "attr_base.proto";

message proto_StarPAddItem {
    option (wea_attr_cls) = "com.tencent.wea.starp.attr.StarPAddItem";
    option (wea_attr_key) = "ItemId";
    // 道具id
    optional int32 ItemId = 1;
    // 数量
    optional int32 ItemCount = 2;
    // 道具自定义信息(Bytes:wea.attr@StarPItemUserDataUnion)
    optional string UserData = 3;
    // 绑定的玩家id
    optional int64 BindPlayerUid = 4;
}
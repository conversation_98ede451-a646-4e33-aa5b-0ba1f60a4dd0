# -*- coding:utf-8 -*-
from log import logger
import click

from tools import RefreshTools, ConfigPatch


@click.command()
@click.option('--rainbow_env', help="请输入刷表服务器", required=True)
@click.option('--username', help='请输入执行人', required=True)
@click.option('--intranet', help='是否内网刷表', required=False, default="1")
@click.option('--rainbow_appid', help='七彩石应用id', required=True, default="")
@click.option('--rainbow_userid', help='七彩石用户id', required=True, default="")
@click.option('--rainbow_secret_key', help='七彩石密钥', required=True, default="")
@click.option('--ds_version', help='ds版本号', required=True, default="")
@click.option('--pack_version', help='副玩法版本号', required=True, default="")
@click.option('--conf_version', help='配置版本号', required=True, default="")
@click.option('--main_version', help='主版本号', required=True, default="")
@click.option('--feature_only', help='只更新副玩法', required=True, default=False)
def release_to_server(rainbow_env, username, intranet, rainbow_appid, rainbow_userid, rainbow_secret_key, ds_version, pack_version, conf_version, main_version, feature_only):
    main_tools = RefreshTools(rainbow_env, username, intranet, ds_version, pack_version, conf_version, main_version)
    # 进行配置打包
    try:
        if not main_tools.pack_config(globals()):
            logger.info("[ERROR] FAILED TO PACK CONFIG!")
            return
    except Exception as e:
        logger.error(str(e))
        exit(-1)

    # 刷表
    # if not main_tools.start_refresh_rainbow(rainbow_appid, rainbow_userid, rainbow_secret_key, feature_only):
    #     logger.info("[ERROR] FAILED TO REFRESH CONFIG!")
    #     exit(1)
    if not main_tools.refresh_ds_cdn():
        logger.info("[ERROR] FAILED TO REFRESH DS CDN!")
        exit(-2)

    logger.info("[INFO ]REFRESH SUCCESS!")


if __name__ == '__main__':
    release_to_server()

syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_RoguelikeMarkInBoardInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.RoguelikeMarkInBoardInfo";
    option (wea_attr_key) = "markId";
    // 印记板中的印记ID
    optional int32 markId = 1;
    // 印记在印记板中的位置(横坐标)
    optional int32 rowIndex = 2;
    // 印记在印记板中的位置(纵坐标)
    optional int32 columnIndex = 3;
    // 印记在印记板中的旋转方式
    optional int32 rotateType = 4;
}
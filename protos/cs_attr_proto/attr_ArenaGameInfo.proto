syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ArenaCardPackDrawHistory.proto";
import "attr_ArenaCardRegulationRecords.proto";
import "attr_ArenaHeroInfo.proto";
import "attr_ArenaHeroStarGeneralInfo.proto";
import "attr_ArenaMoodSetting.proto";
import "attr_ArenaPeakInfo.proto";
import "attr_ArenaPlayerProfile.proto";
import "attr_ArenaSevenDaysLoginActivity.proto";
import "attr_ArenaSprayPaintSetting.proto";
import "attr_ArenaStat.proto";
import "attr_DailyVictoryRecord.proto";

message proto_ArenaGameInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.ArenaGameInfo";
    // 玩家抽取到的卡牌
    repeated int32 cards = 1;
    repeated int32 cards_deleted = 2001;
    optional bool cards_is_cleared = 4001;
    // 玩家英雄信息
    repeated proto_ArenaHeroInfo heroMap = 2;
    repeated int32 heroMap_deleted = 2002;
    optional bool heroMap_is_cleared = 4002;
    // 已获得的Arena道具
    repeated int32 items = 3;
    optional bool items_is_cleared = 2003;
    // 当前选择的头像框
    optional int32 selectedFrame = 4;
    // 当前选择的播报款式
    optional int32 selectedVoiceStyle = 5;
    // 卡包抽卡历史
    optional proto_ArenaCardPackDrawHistory drawHistory = 6;
    optional bool drawHistory_deleted = 2006;
    // 玩家限免英雄信息
    repeated proto_ArenaHeroInfo limitedTimeFreeHeroMap = 7;
    repeated int32 limitedTimeFreeHeroMap_deleted = 2007;
    optional bool limitedTimeFreeHeroMap_is_cleared = 4007;
    // 玩家openId
    optional string openId = 8;
    // 玩家统计数据
    optional proto_ArenaStat stats = 9;
    // 已获得皮肤(皮肤id)
    repeated int32 skins = 10;
    repeated int32 skins_deleted = 2010;
    optional bool skins_is_cleared = 4010;
    // 个人信息展示
    optional proto_ArenaPlayerProfile profile = 11;
    // 峡谷战力值
    optional int32 riftPower = 12;
    // 5v5热力值活动 id
    optional int32 heatPowerActivityId = 13;
    // 5v5热力值
    optional int32 heatPower = 14;
    // 槽位设置的心情
    repeated proto_ArenaMoodSetting moodSettings = 15;
    repeated int32 moodSettings_deleted = 2015;
    optional bool moodSettings_is_cleared = 4015;
    // 5v5热力值排名
    optional int32 heatPowerRank = 16;
    // 峡谷七日登录活动
    optional proto_ArenaSevenDaysLoginActivity sevenDaysLoginActivity = 17;
    // 槽位设置的喷漆道具
    repeated proto_ArenaSprayPaintSetting sprayPaintSetting = 18;
    repeated int32 sprayPaintSetting_deleted = 2018;
    optional bool sprayPaintSetting_is_cleared = 4018;
    // 是否自动设置喷漆道具
    optional bool isAutoSetSprayPaint = 19;
    // 巅峰赛信息
    optional proto_ArenaPeakInfo peekInfo = 20;
    // 英雄峡谷星总进度奖励
    optional proto_ArenaHeroStarGeneralInfo heroStarGeneralInfo = 21;
    // 是否推送了玩家随机事件弹框 0: 未满足条件 1：满足条件需要弹框  2：已推送
    optional int32 pushRandomEventState = 22;
    // 已获得专武(专武id)
    repeated int32 equips = 23;
    repeated int32 equips_deleted = 2023;
    optional bool equips_is_cleared = 4023;
    // 64位客户端版本号, 用以取代以前的32位版本号
    optional int64 clientVersion64 = 24;
    // 峡谷英雄帖完成状态（1为完成，设置完成后不可逆）
    optional int32 heroRoadComplete = 25;
    // 记录最近N天moba对战的胜利次数
    repeated proto_DailyVictoryRecord dailyVictoryRecord = 26;
    repeated int64 dailyVictoryRecord_deleted = 2026;
    optional bool dailyVictoryRecord_is_cleared = 4026;
    // 峡谷主目标系统关闭（0开启，1关闭）
    optional int32 heroStarDisable = 27;
    // 是否发送5v5新手体验卡 0：未发送  1：已发送
    optional int32 sendHokExperienceCard = 28;
    // 卡牌随机策略记录
    repeated proto_ArenaCardRegulationRecords cardRegulationRecordsMap = 29;
    repeated int32 cardRegulationRecordsMap_deleted = 2029;
    optional bool cardRegulationRecordsMap_is_cleared = 4029;
}
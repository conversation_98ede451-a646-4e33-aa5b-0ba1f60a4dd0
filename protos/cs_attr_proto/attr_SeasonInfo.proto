syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_SeasonInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.SeasonInfo";
    // 玩家存储的赛季Id(不一定是服务器当前的赛季)
    optional int32 seasonId = 1;
    // 当前赛季获得的所有时装和交互类道具的itemId
    repeated int32 dresses = 2;
    repeated int32 dresses_deleted = 2002;
    optional bool dresses_is_cleared = 4002;
}
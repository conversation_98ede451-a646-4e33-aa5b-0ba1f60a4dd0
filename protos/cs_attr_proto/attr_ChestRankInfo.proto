syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChestActorRankValue.proto";

message proto_ChestRankInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChestRankInfo";
    // 最后更新时间
    optional int64 lastUpdateTime = 1;
    repeated proto_ChestActorRankValue chestRankValues = 2;
    repeated int32 chestRankValues_deleted = 2002;
    optional bool chestRankValues_is_cleared = 4002;
    // 排行榜结算时间
    optional int64 rankRewardTime = 3;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_StarPPetKindDressUpInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.StarPPetKindDressUpInfo";
    option (wea_attr_key) = "kindId";
    // id
    optional int32 kindId = 1;
    // 当前装扮
    optional int32 currentDressUp = 2;
    // 同步元梦状态
    optional int32 syncStatus = 3;
    // 玩法隔离扩展字段
    optional bytes extIsoData = 13;
}
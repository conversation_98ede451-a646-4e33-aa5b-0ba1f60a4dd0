syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_StarPWishStatueData {
    option (wea_attr_cls) = "com.tencent.wea.attr.StarPWishStatueData";
    option (wea_attr_key) = "uid";
    // 神像数据对应的玩家UID
    optional int64 uid = 1;
    // 神像当前神力值
    optional int32 godPower = 2;
    // 今日累计获得神力值
    optional int32 todayGetPower = 3;
    // 神像道具加成buff
    optional int32 extraBuff = 4;
    // 神像运势
    optional int32 luckBuff = 5;
    // 前来祈福的访客UID列表,会限制长度,采用LRU淘汰
    repeated int64 visitUids = 6;
    optional bool visitUids_is_cleared = 2006;
    // 神力值的上次刷新时间
    optional int64 lastRefreshTime = 7;
    // 玩法隔离扩展字段
    optional bytes extIsoData = 17;
}
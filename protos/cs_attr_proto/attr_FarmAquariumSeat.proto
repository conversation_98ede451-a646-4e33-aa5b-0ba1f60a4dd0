syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_FarmAquariumSeat {
    option (wea_attr_cls) = "com.tencent.wea.attr.FarmAquariumSeat";
    option (wea_attr_key) = "idx";
    optional int32 idx = 1;
    optional int32 scale = 2;
    // 物品id
    optional int32 itemId = 3;
    // 操作订单id
    optional string billNo = 4;
    // 收益开始计算的时间
    optional int64 benefitStartTime = 5;
    // 客户端轨迹id
    optional int32 trackId = 6;
    // 品质
    optional int32 quality = 7;
    // 当前品质使用了多少 秒
    optional int64 qualityUsedSec = 8;
}
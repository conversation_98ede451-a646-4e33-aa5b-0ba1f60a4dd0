syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_KvLL.proto";
import "attr_StarPGeoPoint.proto";

message proto_StarPPublicInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.StarPPublicInfo";
    // 房间名称
    optional string roomName = 1;
    // 房间描述
    optional string desc = 2;
    // 房间owner
    optional int64 uid = 3;
    // 房间加入类型
    optional int32 joinType = 4;
    // 房间是否发布
    optional int32 open = 5;
    // 更新时间
    optional int64 updateTime = 6;
    // 图?
    optional string image = 7;
    // 房间成员
    repeated int64 member = 8;
    optional bool member_is_cleared = 2008;
    // 房间标签
    repeated int32 tags = 9;
    optional bool tags_is_cleared = 2009;
    // 展示的宠物以及等级
    repeated proto_KvLL pets = 10;
    repeated int64 pets_deleted = 2010;
    optional bool pets_is_cleared = 4010;
    // 啾灵世界id
    optional int64 starPWorldId = 11;
    // 是否隐藏(GM)
    optional int32 hidden = 12;
    // 用户定位信息
    optional proto_StarPGeoPoint geoLocation = 13;
    // 创建时间秒
    optional int64 createTime = 14;
    // 房间默认图片id
    optional int32 avatar = 15;
    // 用户拍照上传的房间图片url，如果此字段不为空表示使用自定义图片，为空使用默认avatar
    optional string avatarUrl = 16;
    // 用户是否开启定位
    optional bool isOpenLocation = 17;
    // 用户定位信息
    optional proto_StarPGeoPoint location = 18;
    // 世界等级
    optional int32 worldLevel = 19;
    // 官方房间
    optional bool isOfficial = 20;
    // 官方房间类型
    optional int32 officialType = 21;
    // 官方房间hashkey
    optional int64 officialKey = 22;
    // 灰度房间
    optional bool isGrey = 23;
    // 满员率
    optional int32 memberFullRate = 24;
    // 房间owner昵称
    optional string uidNickName = 25;
    // 当前使用的是什么群,ClubMSDKGroupType
    optional int32 msdkGroupType = 26;
    // QQ群ID
    optional string groupIdQQ = 27;
    // 微信群ID
    optional string groupIdWX = 28;
    // 备用
    optional string backup = 100;
    // 玩法隔离扩展字段
    optional bytes extIsoData = 110;
}
syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_StarPCommonBasePve.proto";
import "attr_StarPTrader.proto";

message proto_StarPDsWorldCommonDBUserDataUnion {
    option (wea_attr_cls) = "com.tencent.wea.attr.StarPDsWorldCommonDBUserDataUnion";    
oneof StarPDsWorldCommonDBUserDataUnion{
    // 世界商人
    proto_StarPTrader starPTrader = 1;
    // 地下城、密域、高塔副本的公共数据
    proto_StarPCommonBasePve starPCommonBasePve = 2;
    // 玩法隔离扩展字段
    bytes extIsoData = 12;
    }
    optional bool isFieldSwitched = 2000;
}

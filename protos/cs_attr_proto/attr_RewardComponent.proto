syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "ResKeywords.proto";
import "attr_RewardCostItem.proto";

message proto_RewardComponent {
    option (wea_attr_cls) = "com.tencent.wea.attr.RewardComponent";
    option (wea_attr_key) = "rewardId";
    // 奖励id
    optional int32 rewardId = 1;
    // 领奖状态
    optional com.tencent.wea.xlsRes.RewardStatus status = 2;
    // 是否自动领奖
    optional bool auto = 3;
    // 需要扣除的道具
    repeated proto_RewardCostItem costItem = 4;
    repeated int32 costItem_deleted = 2004;
    optional bool costItem_is_cleared = 4004;
}
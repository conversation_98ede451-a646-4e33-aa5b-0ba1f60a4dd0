syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "ResKeywords.proto";

message proto_LuckyFriendTaskAttr {
    option (wea_attr_cls) = "com.tencent.wea.attr.LuckyFriendTaskAttr";
    // 任务ID
    optional int32 taskId = 1;
    // 任务状态
    optional com.tencent.wea.xlsRes.LuckyFriendTaskState state = 2;
    // 状态结束时间
    optional int64 stateEndTimeMs = 3;
    // 向哪个好友发送了邀请
    optional int64 sendApplyFriendUid = 4;
    // 匹配的好友UID
    optional int64 matchFriendUid = 5;
    // 任务唯一ID
    optional int64 taskUniqueId = 6;
}
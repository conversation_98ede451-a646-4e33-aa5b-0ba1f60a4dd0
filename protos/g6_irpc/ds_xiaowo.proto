syntax = "proto3";
package ds_xiaowo;
import "irpc_field_option.proto";
option java_package = "com.tencent.wea.g6.irpc.proto.ds_xiaowo";

import "g6_common.proto";

// 小窝交互
message XiaoWoInteractRequest {
  int64 xiaowoId = 1 [(field_ds_session_id) = true];
  int64 itemUid = 2;
  int32 interactState = 3;
  string interactDetail = 4;
}

message XiaoWoInteractReply {
  int32 result = 1;
}

message XiaoWoLockRequest {
  int64 xiaowoId = 1 [(field_ds_session_id) = true];
  int64 itemUid = 2;
  int32 seatIndex = 3;
  int64 characterID = 4;
}

message <PERSON><PERSON><PERSON><PERSON>ockReply {
  int32 result = 1;
}

message XiaowoSetItemDetailRequest {
  int64 xiaowoId = 1 [(field_ds_session_id) = true];
  int64 itemUid = 2;
  string detail = 3;
}

message XiaowoSetItemDetailReply {
  int32 result = 1;
}


service XiaowoServer {
  rpc XiaoWoInteract (XiaoWoInteractRequest) returns (XiaoWoInteractReply) {}
  rpc <PERSON>WoLock (XiaoWoLockRequest) returns (XiaoWoLockReply) {}
  rpc XiaoWoSetDetail (XiaowoSetItemDetailRequest) returns (XiaowoSetItemDetailReply) {}
}


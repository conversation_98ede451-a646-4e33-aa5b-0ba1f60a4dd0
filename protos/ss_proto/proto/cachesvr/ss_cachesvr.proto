syntax = "proto2";

option cc_generic_services = false;

package com.tencent.wea.protocol;

import "common.proto";
import "ss_head.proto";
import "ss_common.proto";

//废弃的协议
message RpcGetCacheFromMasterReq { //缓存uuid对应的数据在一致性hash到的CacheSvr上
  optional int32 cacheType = 1; //枚举CacheType
  optional int64 playUuid = 2; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 uuid = 3 [(field_hash_key) = true]; //数据会缓存在uuid对应的CacheSvr。
  optional int32 slaveSvrId = 4; //从节点svrId
}
message RpcGetCacheFromMasterRes {
  optional int32 result = 1; //0表示成功，非0表示失败
  optional CacheInfoInMaster cacheInfo = 2; //缓存信息
}


message RpcCacheHeartbeatToMasterReq {
  optional int32 slaveSvrId = 1; //uuid被缓存的slaveSvrId
  optional int32 cacheType = 2; //枚举CacheType或uuid
  optional int64 uuid = 3 [(field_hash_key) = true];
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int32 cacheMasterSvrId = 5; //slave上标记的masterSvrId
}
message RpcCacheHeartbeatToMasterRes {
  optional int32 result = 1;
}


message RpcBatchGetCacheDataReq { //批量获取缓存数据
  optional int32 cacheType = 1; //枚举CacheType
  optional int64 playUuid = 2; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 hashUuid = 3 [(field_hash_key) = true]; //数据会缓存在hashUuid对应的CacheSvr。
  repeated int64 batchGetUuids = 4; //需要获取的uuid数据列表
  repeated string batchGetKeys = 5; //需要获取的字段列表
}

message RpcBatchGetCacheDataRes {
  optional int32 result = 1;
  repeated BatchGetCacheData cacheDatas = 2;
  map<int64, BatchGetCacheData> datas = 3;
}

message RpcSlaveCacheLoadFromMasterReq { //从master节点请求加载数据作为slave数据
  optional int32 cacheType = 1; //枚举CacheType
  optional int64 playUuid = 2; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 uuid = 3 [(field_hash_key) = true]; //数据会缓存在uuid对应的CacheSvr。
  optional int32 slaveSvrId = 4; //从节点svrId
}
message RpcSlaveCacheLoadFromMasterRes {
  optional int32 result = 1; //0表示成功，非0表示失败
  optional CacheInfoInMaster cacheInfo = 2; //缓存信息
}

message RpcSlaveCacheHeartbeatToMasterReq {  //废弃
  optional int32 slaveSvrId = 1; //uuid被缓存的slaveSvrId
  optional int32 cacheType = 2; //枚举CacheType或uuid
  optional int64 uuid = 3 [(field_hash_key) = true];
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int32 cacheMasterSvrId = 5; //slave上标记的masterSvrId
}
message RpcSlaveCacheHeartbeatToMasterRes {
  optional int32 result = 1;
  optional int32 dbVersion = 2; //当前db版本,暂时没用
  optional int32 syncVersion = 3;
}

message RpcSlaveHeartbeatToMasterReq {
  option (rpc_one_way) = true;
  optional int32 slaveSvrId = 1; //uuid被缓存的slaveSvrId
  optional int32 cacheType = 2; //枚举CacheType或uuid
  optional int64 uuid = 3 [(field_hash_key) = true];
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int32 cacheMasterSvrId = 5; //slave上标记的masterSvrId
}
message RpcSlaveHeartbeatToMasterRes {
}

message RpcMasterHeartbeatToSlaveReq {
  option (rpc_one_way) = true;
  optional int32 slaveSvrId = 1 [(field_dest_serv) = true]; //uuid被缓存的slaveSvrId
  optional int32 cacheType = 2; //枚举CacheType或uuid
  optional int64 uuid = 3 ;    //uuid
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int32 result = 5; //心跳结果
  optional int32 dbVersion = 6; //当前db版本,暂时没用
  optional int32 syncVersion = 7; //数据同步版本
  optional int32 masterSvrId = 8; //masterSvrId
}
message RpcMasterHeartbeatToSlaveRes {

}
message RpcSlaveCacheRemoveNtfToMasterReq { //通知Master移除slave信息
  optional int32 slaveSvrId = 1; //uuid被缓存的slaveSvrId
  optional int32 cacheType = 2; //枚举CacheType或uuid
  optional int64 uuid = 3 [(field_hash_key) = true];
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
}

message RpcSlaveCacheRemoveNtfToMasterRes { 
  optional int32 result = 1;
}


message RpcModifyCacheReq { //修改Master缓存数据
  optional int32 cacheType = 1; //枚举CacheType
  optional int64 playUuid = 2; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 uuid = 3 [(field_hash_key) = true];
  map<string, string> modifyInfos = 4; //修改的key，value值
  optional int32 dbVersion = 5; //当前db版本,暂时没用
}
message RpcModifyCacheRes {
  optional int32 result = 1;
}

message RpcMasterNtfModifyToSlaveReq { //缓存同步给Slave节点
  optional int32 slaveSvrId = 1 [(field_dest_serv) = true]; //uuid缓存所在的slaveSvrId
  optional int32 masterSvrId = 2; //master的SvrId
  optional int32 cacheType = 3; //枚举CacheType或uuid
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 uuid = 5;
  map<string, string> modifyInfos = 6; //修改的key，value值
  optional int32 dbVersion = 7; //db版本，暂时没用
  optional int32 syncVersion = 8; //同步版本，用来校验同步是否正常
}
message RpcMasterNtfModifyToSlaveRes { //如果失败就清理下slave信息
  optional int32 result = 1;
}

message RpcMasterNtfRemoveToSlaveReq { //master通知slave移除缓存数据
  optional int32 slaveSvrId = 1 [(field_dest_serv) = true]; //uuid缓存所在的slaveSvrId
  optional int32 masterSvrId = 2; //master的SvrId
  optional int32 cacheType = 3; //枚举CacheType或uuid
  optional int64 playUuid = 4; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
  optional int64 uuid = 5;
}
message RpcMasterNtfRemoveToSlaveRes { //如果失败就清理下slave信息
  optional int32 result = 1;
}


message RpcKickCacheReq {
    optional int32 masterSvrId = 1 [(field_dest_serv) = true];
    optional int32 cacheType = 2; //枚举CacheType或uuid
    optional int64 playUuid = 3; //一般传0，需要按玩法区分的情况下，设置这个值。一般在ugc玩法，这里传ugcId。也可以用在其他需要两个key才能确定缓存的地方
    optional int64 uuid = 4;
}
message RpcKickCacheRes {
    optional int32 result = 1;
}
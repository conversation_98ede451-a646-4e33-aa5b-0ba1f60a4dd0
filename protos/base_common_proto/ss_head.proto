syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "base_common.proto";
import "google/protobuf/descriptor.proto";

extend google.protobuf.FileOptions {
  optional int32 rpc_service_timeout_seconds = 300001;
  optional bool svr_rpc_split_service_interface = 300002;
}

extend google.protobuf.MessageOptions {
  optional int32 define_dest_zone = 100001;
  optional MetaDataType define_meta_type = 100002;
  optional LocalServiceType target_local_service = 100003;
  optional int32 rpc_call_timeout_seconds = 100004;
  optional bool rpc_one_way = 100005;
  optional bool region_msg = 100006;
  optional string friends_broadcast_call = 100007;  // 给在线好友广播调用的PlayerRef函数名
  optional string rpc_proto_version = 100008; // 单独控制rpc协议版本
  optional bool rpc_no_mailbox = 100009; // rpc不排队
}

extend google.protobuf.FieldOptions {
  optional bool field_dest_zone = 200001; //分区服
  optional bool field_dest_serv = 200002; //指定busid
  optional bool field_hash_key = 200003; //全局服
  optional bool field_meta_type = 200004;
  optional bool field_meta_uuid = 200005;
  optional bool field_meta_roomid = 200006;
  optional bool field_meta_matchzone = 200007;
  optional bool field_server_type = 200008;
  optional bool field_route_key = 200009;
  optional bool field_region_key = 200010;
  optional bool field_matchalloc_data = 200011;
  optional bool field_lobbyalloc_data = 200012;
  optional bool field_dest_serv_hash_key = 200013; // 指定busid情况下根据key来分配线程（必须配合field_dest_serv才能使用）
  optional bool field_dyn_oneway = 200014; // 动态指定是否oneWay
}

enum RpcCallMode {
  RCM_UNKNOWN = 0;
  RCM_RAW = 1;
  RCM_PB_MESSAGE = 2;
}

enum RpcResponseFlag {
  RRF_DEFAULT = 0; // 默认
  RRF_NULL = 1; // 返回null
  RRF_EXCEPTION = 2; // throw exception
  RRF_UNKNOWN_SERVICE = 3; // 找不到服务
  RRF_RELAY_FAILED = 4; // 转发失败
  RRF_NK_RUNTIME_EXCEPTION = 5; // 逻辑错误
  RRF_RELAY_REDIRECT_FAILED = 6; // 元数据转发失败
  RRF_GRPC_REDIRECT_FAILED = 7; // GRPC转发失败

}

enum RpcRelayMode {
  RRM_SpecDst = 0;        // 指定目的地址
  RRM_AnyNode = 1;        // 哈希取余
  RRM_KeyHash = 2;        // 一致性哈希
  RRM_MetaData = 3;       // 元数据转发
  RRM_MatchData = 4;      // 匹配模式
  RRM_StateRouteData = 5; // 状态转发模式
  RRM_Region = 6; // 大区转发模式
  RRM_LobbyData = 7;      // 大厅模式
  RRM_SpecDstKeyHash = 8; // 指定目的地址且根据key分配处理线程
}

enum LobbyModeRpcStage {
  LMRS_Src2Alloc = 0;
  LMRS_Alloc2Lobby = 1;
  LMRS_Lobby2Alloc = 2;
  LMRS_Alloc2Src = 3;
}

enum MatchModeRpcStage {
  MMRS_Room2MatchAlloc = 0;    // roomsvr->matchallocsvr
  MMRS_MatchAlloc2Match = 1;   // matchallocsvr->matchsvr
  MMRS_Match2MatchAlloc = 2;   // matchsvr->matchallocsvr
  MMRS_MatchAlloc2Room = 3;    // matchallocsvr->roomsvr
}

message MatchAllocData {
  optional bool matchAllocSwitch = 1; //是否使用新版分配开关
  optional int32 modeID = 2;  //模式id
  optional int32 stage = 3;   //rpc阶段  // 协议改造 枚举值切换至整数值 详情参考MatchModeRpcStage
  optional int32 matchAllocSvrID = 4;   //matchallocsvrid
  optional int32 roomSvrID = 5;  //roomsvrid
  optional string matchZone = 6; // match服列表, 新版分配开关没打开的时候使用
  optional int32 matchSvrID = 7;   //matchsvrid
  optional PBMatchLoadInfo loadInfo = 8;
}

message RpcRelayData {
  message KeyHash {
    optional int64 key = 1;
  }
  message MetaData {
    optional MetaDataType type = 1;
    optional int64 uuid = 2;
  }
  message MatchData {
    optional MatchAllocData matchAllocData = 1;
  }
  message LobbyData {
    optional int32 srcSvrId = 1; // game服id
    optional int32 lobbyAllocSvrId = 2; // 大厅分配服id
    optional int32 lobbySvrId = 3; // 大厅服id
    optional int32 playerNum = 5; // 人数
    optional int32 stage = 6; // 阶段  // 协议改造 枚举值切换至整数值 详情参考LobbyModeRpcStage
    optional string lobbyGroupId = 7; // 大厅组id
  }
  message StateRouteData {
    optional int32 server_type = 1;
    optional int64 route_key = 2;  //
  }

  message RegionData {
    optional int32 server_type = 1;
    optional int64 route_key = 2;  //
  }
  optional int32 relayMode = 1;// 协议改造 枚举值切换至整数值 详情参考RpcRelayMode
  oneof UnionData {
    KeyHash keyHash = 2;
    MetaData metaData = 3;
    MatchData matchData = 4;
    StateRouteData stateRouteData = 5;
    RegionData regionData = 6;
    LobbyData lobbyData = 7;
  }

  optional fixed32 relayCount = 100;
}

message RpcRouting {
  optional ServerType srcType = 1 [deprecated = true];
  optional int32 srcZone = 2;
  optional int32 srcServer = 3;
  optional ServerType dstType = 4 [deprecated = true];
  optional int32 dstZone = 5;
  optional int32 dstServer = 6;
  optional RpcRelayData relayData = 7;
  optional string dstServiceName = 8;
  optional string dstInstance = 9;
  optional string srcServiceName = 10;
  optional string srcInstance = 11;
  optional RpcRelayData regionRelayData = 12;
  optional bool dynOneWayValue = 13;
  optional int32 srcTypeInt = 14;       // srcType int 参考ServerType
  optional int32 dstTypeInt = 15;       // dstType int 参考ServerType
}

/*
协议:

length: 2 bytes (total length, not including 4 bytes length)
length: 2 bytes (header length)
RpcHeader
body:  具体消息内容
*/

message RpcHeader {
  optional int32 seqId = 1;
  optional int32 errorCode = 2; //错误码
  optional bytes errorMsg = 3; //错误消息
  optional int64 clientTs = 4;
  optional int64 serverTs = 5;
  optional string className = 6;
  optional string methodName = 7;
  optional int32 pbMessageType = 9; // callMode为2时有用
  optional int64 sendTs = 10; // 发送时间
  optional RpcRouting routing = 11; // proxy转发信息
  optional int64 asyncId = 12; // 异步id
  optional int64 traceId = 13; // for tracing
  optional int32 responseFlag = 15; // 响应标记 // 协议改造 枚举值切换至整数值 详情参考RpcResponseFlag
  optional int32 hop = 19; // hop limit
  optional string pluginName = 20;
  optional int32 pluginWorker = 21;
  optional bytes openTrace = 22;
  optional LocalServiceType targetLocalService = 23;
  map<string, string> openTraceTextMap = 24;
  optional SerializeCallChain playerCallChain = 25;
  repeated string lockedKeys = 26; // 事务锁定的key
  optional string lockedCaller = 27; // 事务锁定当前key的caller
  optional bool isRegion = 28; // 是否是大区
  optional int32 reqType = 29; // 请求类型 1 request 2 response
  optional bool noMailbox = 30; // 不排队
  optional int64 svrVersion = 31; //svr版本号，用来检测服务间的高低版本访问
}

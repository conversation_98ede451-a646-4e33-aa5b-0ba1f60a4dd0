<?xml version="1.0" encoding="GBK" standalone="yes" ?>
<metalib tagsetversion="1" name="tcaplus_tb" version="430">
    <struct name="DbPlatBuffer" version="1">
        <entry name="Length" type="int" desc="" />
        <entry name="Buffer" type="char" count="256000" refer="Length" desc="max len supported by tcaplus" />
    </struct>

    <struct name="DbPlatBufferLarge" version="1">
        <entry name="Length" type="int" desc="" />
        <entry name="Buffer" type="char" count="1000000" refer="Length" desc="max len supported by tcaplus" />
    </struct>

    <struct name="Server" version="1" primarykey="serverId,worldId" splittablekey="worldId" customattr2="TableType=GENERIC">
        <entry name="serverId" type="int32" version="1" />
        <entry name="worldId" type="int32" version="1" />
        <entry name="isRunning" type="int32" version="1" />
        <entry name="runCount" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <index name="index1" column="worldId" />
    </struct>

    <struct name="Metadata" version="1" primarykey="worldId,type,uuid" splittablekey="uuid" customattr2="TableType=GENERIC">
        <entry name="worldId" type="int32" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="serverId" type="int32" version="1" />
        <entry name="lockExpire" type="int64" version="1" />
        <entry name="runCount" type="int64" version="1" />
    </struct>

    <struct name="DirIpBlackList" version="1" primarykey="ip" splittablekey="ip" customattr2="TableType=GENERIC">
        <entry name="ip" type="string" size="1024" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="duration" type="int32" version="1" />
        <entry name="reason" type="string" size="1024" version="1" />
        <index name="index1" column="ip" />
    </struct>

    <struct name="OpenIdToUid" version="1" primarykey="Openid,PlatId" splittablekey="Openid" customattr2="TableType=GENERIC">
        <entry name="Openid" type="string" size="1024" version="1" />
        <entry name="PlatId" type="int32" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="Zoneid" type="int32" version="1" />
        <entry name="CreateTime" type="int64" version="1" />
        <entry name="Deleted" type="int32" version="1" />
        <entry name="LoginTime" type="int64" version="1" />
        <entry name="DeletedTime" type="int64" version="1" />
        <entry name="isRegisterFini" type="int32" version="1" />
        <entry name="accountType" type="int32" version="185" />
        <entry name="CreatorId" type="int64" version="190" />
        <entry name="TransferStatus" type="int32" version="410" />
        <entry name="TransferInterval" type="int64" version="410" />
        <index name="index1" column="Openid" />
    </struct>

    <struct name="OpenIdToCreatorId" version="1" primarykey="Openid,PlatId,worldId" splittablekey="Openid" customattr2="TableType=GENERIC">
        <entry name="Openid" type="string" size="1024" version="1" />
        <entry name="PlatId" type="int32" version="1" />
        <entry name="worldId" type="int32" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="CreatorId" type="int64" version="1" />
        <index name="index1" column="Openid" />
        <index name="index2" column="Openid,PlatId" />
    </struct>

    <struct name="PlayerPublic" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="PublicProfile" type="DbPlatBuffer" version="1" />
        <entry name="PublicSummary" type="DbPlatBuffer" version="1" />
        <entry name="idipTaskInfo" type="DbPlatBuffer" version="86" />
        <entry name="PublicEquipments" type="DbPlatBuffer" version="92" />
        <entry name="PublicGameData" type="DbPlatBuffer" version="92" />
        <entry name="PublicGameSettings" type="DbPlatBuffer" version="92" />
        <entry name="PublicHistoryData" type="DbPlatBuffer" version="92" />
        <entry name="PublicLiveStatus" type="DbPlatBuffer" version="92" />
        <entry name="PublicSceneData" type="DbPlatBuffer" version="92" />
        <entry name="PublicBasicInfo" type="DbPlatBuffer" version="156" />
        <entry name="MatchStaticsInfo" type="DbPlatBuffer" version="165" />
        <entry name="PartyInfo" type="DbPlatBuffer" version="217" />
        <entry name="XiaowoPublicInfo" type="DbPlatBuffer" version="217" />
        <entry name="FittingSlots" type="DbPlatBuffer" version="319" />
        <entry name="FarmPublicInfo" type="DbPlatBuffer" version="326" />
        <entry name="UgcGrowUpInfo" type="DbPlatBuffer" version="333" />
        <entry name="albumInfo" type="DbPlatBuffer" version="336" />
        <entry name="recentActivity" type="DbPlatBuffer" version="355" />
        <entry name="HousePublicInfo" type="DbPlatBuffer" version="372" />
        <entry name="mallWishList" type="DbPlatBuffer" version="393" />
        <entry name="CocPlayerPublicInfo" type="DbPlatBuffer" version="394" />
        <entry name="ChaseGameData" type="DbPlatBuffer" version="402" />
        <entry name="CookPublicInfo" type="DbPlatBuffer" version="414" />
        <entry name="VillagePublicInfo" type="DbPlatBuffer" version="415" />
        <entry name="StarPlayerInfo" type="DbPlatBuffer" version="421" />
        <entry name="StarPPublicUserInfo" type="DbPlatBuffer" version="421" />
        <entry name="StarPMiscUserInfo" type="DbPlatBuffer" version="421" />
        <entry name="StarPPublicUserInfo2" type="DbPlatBuffer" version="421" />
        <entry name="ModPublicInfo" type="DbPlatBuffer" version="421" />
        <entry name="ChestCollectionPublicInfo" type="DbPlatBuffer" version="430" />
    </struct>

    <struct name="OnlineMonitor" version="1" primarykey="timekey,gameappid,gsid" splittablekey="timekey" customattr2="TableType=GENERIC">
        <entry name="timekey" type="int64" version="1" />
        <entry name="gameappid" type="string" size="255" version="1" />
        <entry name="gsid" type="string" size="255" version="1" />
        <entry name="zoneareaid" type="int32" version="1" />
        <entry name="onlinecntios" type="int64" version="1" />
        <entry name="onlinecntandroid" type="int64" version="1" />
        <index name="index1" column="timekey,gameappid,gsid" />
    </struct>

    <struct name="KVStoreTable" version="1" primarykey="WorldZoneKey,key" splittablekey="WorldZoneKey" customattr2="TableType=GENERIC">
        <entry name="WorldZoneKey" type="int64" version="1" />
        <entry name="key" type="string" size="1024" version="1" />
        <entry name="value" type="DbPlatBuffer" version="1" />
        <index name="index1" column="WorldZoneKey" />
    </struct>

    <struct name="GuidKey" version="1" primarykey="Key,Type" splittablekey="Type" customattr2="TableType=GENERIC">
        <entry name="Key" type="int32" version="1" />
        <entry name="Type" type="int32" version="1" />
        <entry name="FreeId" type="int64" version="1" default="0" />
    </struct>

    <struct name="PlayerOnlineTable" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="Openid" type="string" size="1024" version="1" />
        <entry name="LastLoginTime" type="int64" version="1" />
        <entry name="LoginLockTime" type="int64" version="1" />
        <entry name="svrId" type="int32" version="1" />
        <entry name="LastKeepAliveTime" type="int64" version="94" />
        <entry name="LastSvrId" type="int32" version="157" />
        <entry name="playerState" type="int32" version="235" />
        <entry name="LastSvrVersion" type="int64" version="260" />
    </struct>

    <struct name="PlayerInteractionTable" version="1" primarykey="Id,Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Id" type="int64" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="Src" type="int64" version="1" />
        <entry name="body" type="DbPlatBuffer" version="1" />
        <entry name="RecvTime" type="int64" version="1" />
        <index name="index1" column="Uid" />
    </struct>

    <struct name="PlayerNickNameTable" version="1" primarykey="Name" splittablekey="Name" customattr2="TableType=GENERIC">
        <entry name="Name" type="string" size="1024" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="UpdateTime" type="int64" version="1" />
        <index name="index1" column="Name" />
    </struct>

    <struct name="RelationTable" version="1" primarykey="Uid1,Uid2,Type" splittablekey="Uid1" customattr2="TableType=GENERIC">
        <entry name="Uid1" type="int64" version="1" />
        <entry name="Uid2" type="int64" version="1" />
        <entry name="Type" type="int32" version="1" />
        <entry name="addTime" type="int64" version="1" />
        <entry name="intimacy" type="int64" version="1" />
        <entry name="updateTime" type="string" size="1024" version="180" />
        <entry name="platId" type="int64" version="180" />
        <entry name="openId" type="string" size="1024" version="180" />
        <entry name="friendOpenId" type="string" size="1024" version="180" />
        <entry name="friendPlatId" type="int32" version="180" />
        <entry name="Reason" type="int32" version="182" />
        <entry name="SubReason" type="int32" version="182" />
        <entry name="intimateId" type="int32" version="188" />
        <entry name="friendRemarkName" type="string" size="256" version="291" />
        <entry name="togetherBattleCount" type="int32" version="317" />
        <entry name="recentInteractTs" type="int64" version="317" />
        <entry name="followMallWishList" type="int32" version="393" />
        <entry name="mallWishListReadTs" type="int64" version="393" />
        <index name="index1" column="Uid1,Type" />
        <index name="index2" column="Uid1,Uid2" />
        <index name="index3" column="Uid1" />
    </struct>

    <struct name="PlatFriendTable" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="list" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="PlayerMail" version="1" primarykey="Uid,Id" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Id" type="uint64" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="Status" type="uint32" version="1" />
        <entry name="SenderId" type="int64" version="1" />
        <entry name="SenderName" type="string" size="1024" version="143" />
        <entry name="SendTime" type="uint64" version="1" />
        <entry name="Title" type="string" size="1024" version="1" />
        <entry name="Attachments" type="DbPlatBuffer" version="1" />
        <entry name="Content" type="DbPlatBuffer" version="1" />
        <entry name="Url" type="DbPlatBuffer" version="144" />
        <entry name="ExpireTime" type="uint64" version="1" />
        <entry name="SourceInfo" type="DbPlatBuffer" version="1" />
        <entry name="ExtraData" type="DbPlatBuffer" version="320" />
        <index name="index1" column="Uid" />
    </struct>

    <struct name="CommonNumericAttr" version="1" primarykey="Uuid,AttrType,AttrKey,TimeKey" splittablekey="Uuid" customattr2="TableType=GENERIC">
        <entry name="Uuid" type="int64" version="1" />
        <entry name="AttrType" type="int64" version="1" />
        <entry name="AttrKey" type="int64" version="1" />
        <entry name="TimeKey" type="int64" version="1" />
        <entry name="AttrValue" type="int64" version="1" />
        <index name="index_uid_query" column="Uuid" />
        <index name="index_type_query" column="Uuid,AttrType,TimeKey" />
        <index name="index_flash_query" column="Uuid,AttrType,AttrKey" />
    </struct>

    <struct name="ChatGroup" version="1" primarykey="Id,SubId" customattr2="TableType=GENERIC">
        <entry name="Type" type="int32" version="1" />
        <entry name="Id" type="int64" version="1" />
        <entry name="SubId" type="int64" version="1" />
        <entry name="Name" type="string" size="1024" version="1" />
        <entry name="CreateTime" type="uint64" version="1" />
        <entry name="CreateUid" type="int64" version="1" />
        <entry name="UserList" type="DbPlatBuffer" version="1" />
        <entry name="FirstMsgSeqId" type="uint64" version="1" />
        <entry name="LastMsgSeqId" type="uint64" version="1" />
    </struct>

    <struct name="ChatSession" version="1" primarykey="Uid,GroupId,GroupSubId" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="GroupId" type="int64" version="1" />
        <entry name="GroupSubId" type="int64" version="1" />
        <entry name="GroupType" type="int32" version="1" />
        <entry name="LastReadMsgSeqId" type="uint64" version="1" />
        <entry name="isShield" type="int32" version="1" />
        <entry name="isSticky" type="int32" version="1" />
        <index name="index1" column="Uid" />
    </struct>

    <struct name="ChatMsg" version="1" primarykey="GroupId,GroupSubId,SeqId" splittablekey="GroupId,GroupSubId" customattr2="TableType=GENERIC">
        <entry name="GroupType" type="int32" version="1" />
        <entry name="GroupId" type="int64" version="1" />
        <entry name="GroupSubId" type="int64" version="1" />
        <entry name="SeqId" type="uint64" version="1" />
        <entry name="SenderUid" type="int64" version="1" />
        <entry name="SendTime" type="uint64" version="1" />
        <entry name="Content" type="DbPlatBuffer" version="1" />
        <entry name="SenderSnapshot" type="DbPlatBuffer" version="62" />
        <entry name="SafetyCheckPassFlag" type="int32" version="71" />
        <index name="index1" column="GroupId,GroupSubId" />
    </struct>

    <struct name="ChatMsgAidInfo" version="1" primarykey="GroupType,GroupId,GroupSubId,SeqId" splittablekey="GroupId" customattr2="TableType=GENERIC">
        <entry name="GroupType" type="int32" version="1" />
        <entry name="GroupId" type="int64" version="1" />
        <entry name="GroupSubId" type="int64" version="1" />
        <entry name="SeqId" type="uint64" version="1" />
        <entry name="aidInfo" type="DbPlatBuffer" version="1" />
        <index name="index1" column="GroupType,GroupId,GroupSubId" />
    </struct>

    <struct name="BattleRoomInfo" version="1" primarykey="battleId" splittablekey="battleId" customattr2="TableType=GENERIC">
        <entry name="battleId" type="int64" version="1" />
        <entry name="memList" type="DbPlatBuffer" version="1" />
        <entry name="battleStatus" type="int32" version="1" />
        <entry name="extraInfo" type="DbPlatBuffer" version="1" />
        <entry name="groupKey" type="DbPlatBuffer" version="15" />
        <entry name="matchRule" type="DbPlatBuffer" version="48" />
        <entry name="resultInfo" type="DbPlatBuffer" version="49" />
        <entry name="frameExtraInfo" type="DbPlatBuffer" version="49" />
        <entry name="createtime" type="int64" version="49" />
        <entry name="endtime" type="int64" version="49" />
        <entry name="curStateStartTime" type="int64" version="50" />
        <entry name="isWarmRound" type="int32" version="193" />
        <entry name="levelRoundInfo" type="DbPlatBuffer" version="204" />
        <entry name="lastHeartbeatTime" type="int64" version="204" />
        <entry name="warmRoundTypeInt" type="int32" version="223" />
        <entry name="migrateCount" type="int32" version="245" />
        <entry name="matchedTimeMs" type="int64" version="285" />
        <entry name="metaAiGetMatchData" type="DbPlatBuffer" version="285" />
        <entry name="observerList" type="DbPlatBuffer" version="298" />
        <entry name="roomMidJoinShowInfo" type="DbPlatBuffer" version="365" />
        <entry name="ugcInfo" type="DbPlatBuffer" version="399" />
        <entry name="midJoinStatus" type="DbPlatBuffer" version="416" />
        <entry name="memberLeaveList" type="DbPlatBuffer" version="423" />
    </struct>

    <struct name="DSPlayerCustomData" version="1" primarykey="battleId,playerUid,customId" splittablekey="battleId" customattr2="TableType=GENERIC">
        <entry name="battleId" type="int64" version="1" />
        <entry name="playerUid" type="int64" version="1" />
        <entry name="customId" type="int64" version="1" />
        <entry name="customData" type="string" size="1024" version="1" />
        <index name="index1" column="battleId" />
    </struct>

    <struct name="BattleSceneInfo" version="1" primarykey="battleId" splittablekey="battleId" customattr2="TableType=GENERIC">
        <entry name="battleId" type="int64" version="1" />
        <entry name="sceneList" type="DbPlatBuffer" version="1" />
        <entry name="memList" type="DbPlatBuffer" version="1" />
        <entry name="briefInfo" type="DbPlatBuffer" version="1" />
        <entry name="battleSceneStatus" type="DbPlatBuffer" version="422" />
        <entry name="memberLeaveList" type="DbPlatBuffer" version="423" />
    </struct>

    <struct name="MatchRoomInfo" version="1" primarykey="roomId" splittablekey="roomId" customattr2="TableType=GENERIC">
        <entry name="roomId" type="int64" version="1" />
        <entry name="leaderId" type="int64" version="1" />
        <entry name="memList" type="DbPlatBuffer" version="1" />
        <entry name="roomStatus" type="int32" version="1" />
        <entry name="matchRule" type="DbPlatBuffer" version="1" />
        <entry name="teamType" type="int32" version="1" />
        <entry name="chatGroupKey" type="DbPlatBuffer" version="15" />
        <entry name="battleId" type="int64" version="47" />
        <entry name="keyValueInfoMap" type="DbPlatBuffer" version="47" />
        <entry name="beInvitedMap" type="DbPlatBuffer" version="47" />
        <entry name="want2JoinPlayerInfoMap" type="DbPlatBuffer" version="47" />
        <entry name="curStateStartTime" type="int64" version="47" />
        <entry name="activeTime" type="int64" version="47" />
        <entry name="matchSvrID" type="int32" version="60" />
        <entry name="kickPlayerList" type="DbPlatBuffer" version="78" />
        <entry name="roomType" type="int32" version="154" />
        <entry name="state" type="DbPlatBuffer" version="154" />
        <entry name="displayInfo" type="DbPlatBuffer" version="154" />
        <entry name="maxMemberLimit" type="int32" version="154" />
        <entry name="ugcInfo" type="DbPlatBuffer" version="154" />
        <entry name="compElimRoomInfo" type="DbPlatBuffer" version="235" />
        <entry name="roomVersion" type="int32" version="251" />
        <entry name="roomCreateOpts" type="DbPlatBuffer" version="273" />
        <entry name="LastSvrId" type="int32" version="274" />
        <entry name="battleCount" type="int32" version="290" />
        <entry name="maxObserverLimit" type="int32" version="297" />
        <entry name="observerList" type="DbPlatBuffer" version="299" />
        <entry name="teamBattleBroadcastInfo" type="DbPlatBuffer" version="328" />
        <entry name="midJoinStatus" type="DbPlatBuffer" version="416" />
    </struct>

    <struct name="CommonQueueTable" version="1" primarykey="Uid,Type,Id" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="Type" type="int32" version="1" />
        <entry name="Id" type="int64" version="1" />
        <entry name="RealId" type="int64" version="1" />
        <entry name="Data" type="DbPlatBuffer" version="1" />
        <index name="index1" column="Uid,Type" />
    </struct>

    <struct name="GlobalMgrDistLock" version="1" primarykey="lockKey,lockType" splittablekey="lockKey" customattr2="TableType=GENERIC">
        <entry name="lockType" type="int32" version="1" />
        <entry name="ownerID" type="int64" version="1" />
        <entry name="expireTime" type="int64" version="1" />
        <entry name="lockKey" type="int64" version="1" />
    </struct>

    <struct name="CacheLockDB" version="1" primarykey="lockType, lockKey, lockKey2" splittablekey="lockKey" customattr2="TableType=GENERIC">
        <entry name="lockType" type="int32" version="1" />
        <entry name="ownerID" type="int64" version="1" />
        <entry name="expireTime" type="int64" version="1" />
        <entry name="lockKey" type="int64" version="1" />
        <entry name="lockKey2" type="int64" version="1" />
    </struct>

    <struct name="BattleHistory" version="1" primarykey="type,uuid" splittablekey="uuid" customattr2="TableType=LIST;ListNum=100">
        <entry name="type" type="int32" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="commonBattleResult" type="DbPlatBuffer" version="185" />
        <entry name="battleDetailData" type="DbPlatBuffer" version="185" />
        <entry name="battleHistoryExtraInfo" type="DbPlatBuffer" version="229" />
        <entry name="battleMemberRecord" type="DbPlatBuffer" version="281" />
    </struct>

    <struct name="BattleHistorySettlementData" version="1" primarykey="type,uuid" splittablekey="uuid" customattr2="TableType=LIST;ListNum=110">
        <entry name="type" type="int32" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="battleSettlementData" type="DbPlatBuffer" version="359" />
    </struct>

    <struct name="GlobalBattleRecord" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="record" type="DbPlatBuffer" version="16" />
    </struct>

    <struct name="Player" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="Platid" type="int32" version="1" />
        <entry name="Openid" type="string" size="1024" version="1" />
        <entry name="Level" type="int32" version="1" />
        <entry name="UserAttr" type="DbPlatBuffer" version="1" />
        <entry name="ChangedUserAttr" type="DbPlatBuffer" version="1" />
        <entry name="ChangeTimes" type="int32" version="1" />
        <entry name="LoginCheckTime" type="int64" version="1" />
        <entry name="LastLoginSvrVersion" type="int64" version="260" />
    </struct>

    <struct name="PlayerActivity" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="ActivityData" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="ServerInfo" version="1" primarykey="namespace,serverType,name" splittablekey="namespace" customattr2="TableType=GENERIC">
        <entry name="name" type="string" size="1024" version="1" />
        <entry name="namespace" type="string" size="1024" version="1" />
        <entry name="serverType" type="string" size="1024" version="1" />
        <entry name="status" type="int32" version="1" />
        <entry name="registerTime" type="int64" version="1" />
        <entry name="lastHeartbeatTime" type="int64" version="1" />
        <entry name="loadConfigVersion" type="string" size="1024" version="1" />
        <entry name="loadConfigTime" type="int64" version="1" />
        <entry name="loadResourceVersion" type="string" size="1024" version="1" />
        <entry name="loadResourceTime" type="int64" version="1" />
        <entry name="configVersion" type="string" size="1024" version="1" />
        <entry name="resourceVersion" type="string" size="1024" version="1" />
        <entry name="totalOnline" type="int32" version="1" />
        <entry name="maxOnline" type="int32" version="1" />
        <entry name="weight" type="int32" version="1" />
        <entry name="port" type="int32" version="1" />
        <index name="index_namespace" column="namespace" />
        <index name="index_server_type" column="namespace,serverType" />
    </struct>

    <struct name="RoutingTable" version="1" primarykey="type,uuid" splittablekey="uuid" customattr2="TableType=GENERIC">
        <entry name="type" type="int32" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="delIndex" type="int64" version="1" />
        <entry name="svrId" type="int32" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="modifyTime" type="int64" version="1" />
    </struct>

    <struct name="SrvInteraction" version="1" primarykey="BusId,Id" splittablekey="BusId" customattr2="TableType=GENERIC">
        <entry name="BusId" type="int32" version="1" />
        <entry name="Id" type="int64" version="1" />
        <entry name="Params" type="DbPlatBuffer" version="1" />
        <entry name="CreateTime" type="int64" version="46" />
        <index name="index_query" column="BusId" />
    </struct>

    <struct name="IdipBanInfo" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="CreateTime" type="int64" version="1" />
        <entry name="banInfo" type="DbPlatBuffer" version="1" />
        <index name="index1" column="Uid" />
    </struct>

    <struct name="StatefullSvrPayload" version="1" primarykey="type,svrId" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="svrId" type="int32" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="payload" type="int32" version="1" />
        <entry name="lastReportTime" type="int64" version="1" />
        <index name="index_type" column="type" />
    </struct>

    <struct name="ServerLoadInfo" version="1" primarykey="type,svrId,subType" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="type" type="int32" version="1" />
        <entry name="svrId" type="int32" version="1" />
        <entry name="subType" type="int32" version="1" />
        <entry name="load" type="int32" version="1" />
        <entry name="lastUpdateTime" type="int64" version="1" />
        <index name="index1" column="type" />
        <index name="index2" column="type,svrId" />
    </struct>

    <struct name="SvrLoadInfo" version="1" primarykey="type,svrId,subType" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="type" type="int32" version="1" />
        <entry name="svrId" type="int32" version="1" />
        <entry name="subType" type="string" size="1024" version="1" />
        <entry name="load" type="int32" version="1" />
        <entry name="lastUpdateTime" type="int64" version="1" />
        <index name="index1" column="type" />
        <index name="index2" column="type,svrId" />
    </struct>

    <struct name="NotRepeatedName" version="1" primarykey="createName,moduleType" splittablekey="createName" customattr2="TableType=GENERIC">
        <entry name="createName" type="string" size="1024" version="1" />
        <entry name="moduleType" type="int32" version="1" />
        <entry name="extraInfo" type="string" size="1024" version="1" />
    </struct>

    <struct name="LobbyInfo" version="1" primarykey="lobbyId" splittablekey="lobbyId" customattr2="TableType=GENERIC">
        <entry name="lobbyId" type="int64" version="1" />
        <entry name="mapId" type="int32" version="1" />
        <entry name="chatGroupKey" type="DbPlatBuffer" version="1" />
        <entry name="dsaInstanceID" type="int64" version="1" />
        <entry name="dsAddr" type="string" size="1024" version="132" />
        <entry name="extraInfo" type="DbPlatBuffer" version="171" />
        <entry name="mapDynamicShowList" type="DbPlatBuffer" version="227" />
        <entry name="placedObjectInfo" type="DbPlatBuffer" version="269" />
        <entry name="mapInstanceId" type="int32" version="389" />
        <entry name="ugcId" type="int64" version="390" />
        <entry name="creatorUid" type="int64" version="390" />
        <entry name="creatorExitMsTime" type="int64" version="390" />
    </struct>

    <struct name="UgcBrief" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="212" />
        <entry name="ugcId" type="int64" version="212" />
        <entry name="name" type="string" size="1024" version="212" />
        <entry name="templateId" type="int32" version="212" />
        <entry name="saveType" type="int32" version="212" />
        <entry name="saveCount" type="int32" version="212" />
        <entry name="isDelete" type="int32" version="212" />
        <entry name="createTime" type="int64" version="212" />
        <entry name="expireTime" type="int64" version="212" />
        <entry name="mdList" type="DbPlatBuffer" version="212" />
        <entry name="oldUgcId" type="int64" version="212" />
        <entry name="desc" type="string" size="1024" version="212" />
        <entry name="tags" type="string" size="1024" version="212" />
        <entry name="reportStatus" type="int32" version="212" />
        <entry name="rejectTime" type="int64" version="212" />
        <entry name="editorSec" type="int32" version="212" />
        <entry name="mapType" type="int32" version="212" />
        <entry name="isCollect" type="int32" version="212" />
        <entry name="collectTime" type="int64" version="212" />
        <entry name="extraInfo" type="DbPlatBuffer" version="212" />
        <entry name="bucket" type="string" size="1024" version="212" />
        <entry name="ugcGroupIdList" type="DbPlatBuffer" version="212" />
        <entry name="ugcVersion" type="string" size="1024" version="212" />
        <entry name="clientVersion" type="string" size="1024" version="212" />
        <entry name="saveInfo" type="DbPlatBuffer" version="212" />
        <entry name="metaList" type="DbPlatBuffer" version="212" />
        <entry name="difficulty" type="string" size="1024" version="212" />
        <entry name="commMap" type="DbPlatBuffer" version="212" />
        <entry name="mapList" type="DbPlatBuffer" version="212" />
        <entry name="groupList" type="DbPlatBuffer" version="212" />
        <entry name="homeList" type="DbPlatBuffer" version="212" />
        <entry name="coCreateList" type="DbPlatBuffer" version="212" />
        <entry name="layers" type="DbPlatBuffer" version="230" />
        <entry name="editors" type="DbPlatBuffer" version="230" />
        <entry name="updateTime" type="int64" version="230" />
        <entry name="publishInfo" type="DbPlatBuffer" version="236" />
        <entry name="secFlags" type="int64" version="242" />
        <entry name="mgrInfo" type="DbPlatBuffer" version="253" />
        <entry name="ugcResType" type="int32" version="261" />
        <entry name="resCategory" type="int32" version="261" />
        <entry name="resSubCategory" type="int32" version="261" />
        <entry name="resLabels" type="string" size="1024" version="261" />
        <entry name="disableMultiTest" type="int32" version="286" />
        <entry name="publishTime" type="int64" version="301" />
        <entry name="resIdList" type="DbPlatBuffer" version="304" />
        <entry name="publishGoodsStatus" type="int32" version="309" />
        <entry name="buyGoodsStatus" type="int32" version="309" />
        <entry name="isResPubCosPath" type="int32" version="321" />
        <entry name="cover" type="DbPlatBuffer" version="321" />
        <entry name="modelType" type="int32" version="331" />
        <entry name="banResIdList" type="DbPlatBuffer" version="355" />
        <entry name="curLayerId" type="int32" version="358" />
        <entry name="hasPublishGoodsRecord" type="int32" version="363" />
        <entry name="ugcAchievement" type="DbPlatBuffer" version="379" />
        <entry name="achievementVerId" type="int32" version="379" />
        <entry name="evaluationStatus" type="int32" version="394" />
        <entry name="evaluationStatusReport" type="int32" version="396" />
        <entry name="extraConfigIndexMap" type="DbPlatBuffer" version="397" />
        <entry name="extraConfigIndexVerId" type="int32" version="397" />
        <entry name="mapLoading" type="DbPlatBuffer" version="401" />
        <entry name="lobbyCover" type="DbPlatBuffer" version="401" />
        <entry name="videoInfo" type="DbPlatBuffer" version="406" />
        <entry name="isAdvert" type="int32" version="424" />
        <entry name="advertStatus" type="int32" version="424" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="BillTable" version="1" primarykey="Uid,Type,BillNo,Seq" splittablekey="Uid,Type" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="Type" type="int32" version="1" />
        <entry name="BillNo" type="string" size="1024" version="1" />
        <entry name="Seq" type="int64" version="1" />
        <entry name="Status" type="int32" version="1" />
        <entry name="CreateTime" type="int64" version="1" />
        <entry name="BillInfo" type="DbPlatBuffer" version="1" />
        <index name="index1" column="Uid,Type,BillNo" />
        <index name="index2" column="Uid,Type,Seq" />
        <index name="index3" column="Uid,Type" />
    </struct>

    <struct name="BillSeq" version="1" primarykey="Uid,Type" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="Type" type="int32" version="1" />
        <entry name="Seq" type="int64" version="1" />
    </struct>

    <struct name="ActivityInviteActive" version="1" primarykey="activityId,inviteesUid" splittablekey="inviteesUid" customattr2="TableType=GENERIC">
        <entry name="activityId" type="int32" version="1" />
        <entry name="inviteesUid" type="int64" version="1" />
        <entry name="inviterUid" type="int64" version="1" />
    </struct>

    <struct name="ActivitySquadTable" version="1" primarykey="squadId" splittablekey="squadId" customattr2="TableType=GENERIC">
        <entry name="squadId" type="int64" version="1" />
        <entry name="activityNo" type="int32" version="1" />
        <entry name="memberList" type="DbPlatBuffer" version="1" />
        <entry name="dailyTaskInfo" type="DbPlatBuffer" version="1" />
        <entry name="achievementTaskInfo" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="MultiPlayerSquadTable" version="1" primarykey="squadId" splittablekey="squadId" customattr2="TableType=GENERIC">
        <entry name="squadId" type="int64" version="1" />
        <entry name="activityId" type="int32" version="1" />
        <entry name="activityNo" type="int32" version="1" />
        <entry name="memberUidList" type="DbPlatBuffer" version="1" />
        <entry name="lastUpdateTimestampMs" type="int64" version="1" />
        <entry name="recordArray" type="DbPlatBuffer" version="219" />
    </struct>

    <struct name="UgcPublishGoodsInfo" version="1" primarykey="ugcId" splittablekey="ugcId" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="ugcGoodsList" type="DbPlatBuffer" version="303" />
    </struct>

    <struct name="UgcPublish" version="1" primarykey="ugcId" splittablekey="ugcId" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="name" type="string" size="1024" version="1" />
        <entry name="desc" type="string" size="1024" version="1" />
        <entry name="type" type="DbPlatBuffer" version="1" />
        <entry name="isDelete" type="int32" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="mdList" type="DbPlatBuffer" version="1" />
        <entry name="bucket" type="string" size="1024" version="1" />
        <entry name="oldUgcId" type="int64" version="1" />
        <entry name="templateId" type="int32" version="1" />
        <entry name="editorName" type="string" size="1024" version="1" />
        <entry name="tags" type="string" size="1024" version="1" />
        <entry name="reviewStatus" type="int32" version="1" />
        <entry name="editorAvatar" type="string" size="1024" version="1" />
        <entry name="ugcExp" type="int64" version="1" />
        <entry name="likeCount" type="int32" version="1" />
        <entry name="collectCount" type="int32" version="1" />
        <entry name="mapHistorySeasonExp" type="DbPlatBuffer" version="1" />
        <entry name="mapExpUpdateTime" type="uint64" version="1" />
        <entry name="seasonId" type="int32" version="1" />
        <entry name="playCount" type="int32" version="1" />
        <entry name="pointsNumber" type="int32" version="1" />
        <entry name="ugcInstanceType" type="int32" version="1" />
        <entry name="ugcGroupIdList" type="DbPlatBuffer" version="1" />
        <entry name="editorSec" type="int32" version="1" />
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="extraInfo" type="DbPlatBuffer" version="1" />
        <entry name="isOfficial" type="int32" version="1" />
        <entry name="wxRecord" type="DbPlatBuffer" version="1" />
        <entry name="qqRecord" type="DbPlatBuffer" version="1" />
        <entry name="wxPlayRecord" type="DbPlatBuffer" version="1" />
        <entry name="qqPlayRecord" type="DbPlatBuffer" version="1" />
        <entry name="totalUgcExp" type="int64" version="1" />
        <entry name="platId" type="int32" version="1" />
        <entry name="useCount" type="int32" version="190" />
        <entry name="ugcVersion" type="string" size="1024" version="200" />
        <entry name="clientVersion" type="string" size="1024" version="200" />
        <entry name="metaList" type="DbPlatBuffer" version="202" />
        <entry name="difficulty" type="string" size="1024" version="203" />
        <entry name="commMap" type="DbPlatBuffer" version="205" />
        <entry name="shareCount" type="int32" version="208" />
        <entry name="mgrInfo" type="DbPlatBuffer" version="218" />
        <entry name="descInfo" type="DbPlatBuffer" version="226" />
        <entry name="takeoffReason" type="string" size="1024" version="226" />
        <entry name="layers" type="DbPlatBuffer" version="226" />
        <entry name="reviewReason" type="string" size="1024" version="226" />
        <entry name="editors" type="DbPlatBuffer" version="226" />
        <entry name="updateTime" type="int64" version="226" />
        <entry name="playSec" type="int64" version="228" />
        <entry name="occupancyValue" type="int32" version="231" />
        <entry name="propertyScore" type="int32" version="233" />
        <entry name="actorCount" type="int32" version="233" />
        <entry name="useCountPrePublish" type="int32" version="234" />
        <entry name="ugcResType" type="int32" version="261" />
        <entry name="resCategory" type="int32" version="261" />
        <entry name="resSubCategory" type="int32" version="261" />
        <entry name="resLabels" type="string" size="1024" version="261" />
        <entry name="resBagRefCount" type="int32" version="268" />
        <entry name="passTotalSec" type="int64" version="277" />
        <entry name="passTotalCount" type="int32" version="277" />
        <entry name="publishTime" type="int64" version="300" />
        <entry name="publishGoodsStatus" type="int32" version="302" />
        <entry name="bugGoodsStatus" type="int32" version="302" />
        <entry name="resIdList" type="DbPlatBuffer" version="304" />
        <entry name="buyGoodsStatus" type="int32" version="312" />
        <entry name="danMuCnt" type="int32" version="321" />
        <entry name="labelScore" type="DbPlatBuffer" version="321" />
        <entry name="isResPrivate" type="int32" version="321" />
        <entry name="isResPubCosPath" type="int32" version="321" />
        <entry name="isResPrivateDelete" type="int32" version="321" />
        <entry name="cover" type="DbPlatBuffer" version="321" />
        <entry name="rankList" type="DbPlatBuffer" version="326" />
        <entry name="blackPlayUv" type="int64" version="340" />
        <entry name="blackLikeCount" type="int64" version="340" />
        <entry name="blackPlaySec" type="int64" version="340" />
        <entry name="banResIdList" type="DbPlatBuffer" version="355" />
        <entry name="applyTakeOff" type="int32" version="360" />
        <entry name="applyTakeOffReason" type="string" size="1024" version="361" />
        <entry name="hasPublishGoodsRecord" type="int32" version="362" />
        <entry name="ugcAppCommonInfo" type="DbPlatBuffer" version="368" />
        <entry name="dataStoreAccessedPermissionInfo" type="DbPlatBuffer" version="377" />
        <entry name="ugcAchievement" type="DbPlatBuffer" version="379" />
        <entry name="versionPassRecordData" type="DbPlatBuffer" version="391" />
        <entry name="evaluationStatus" type="int32" version="394" />
        <entry name="extraConfigIndexMap" type="DbPlatBuffer" version="397" />
        <entry name="versionPassTotalSec" type="int64" version="398" />
        <entry name="versionPassTotalCount" type="int32" version="398" />
        <entry name="mapLoading" type="DbPlatBuffer" version="401" />
        <entry name="lobbyCover" type="DbPlatBuffer" version="401" />
        <entry name="publishParam" type="DbPlatBuffer" version="407" />
        <entry name="videoInfo" type="DbPlatBuffer" version="410" />
        <entry name="isAdvert" type="int32" version="424" />
        <entry name="advertStatus" type="int32" version="424" />
        <entry name="isPartyGame" type="int32" version="427" />
    </struct>

    <struct name="MessageSlip" version="1" primarykey="Id" splittablekey="Id" customattr2="TableType=GENERIC">
        <entry name="Id" type="uint64" version="1" />
        <entry name="Uid" type="int64" version="1" />
        <entry name="Status" type="uint32" version="1" />
        <entry name="slipDigest" type="string" size="1024" version="1" />
        <entry name="slipContent" type="string" size="1024" version="1" />
        <entry name="createdTime" type="uint64" version="1" />
        <entry name="favourCount" type="int64" version="1" />
        <entry name="commentCount" type="int64" version="1" />
    </struct>

    <struct name="MessageComment" version="1" primarykey="slipId" splittablekey="slipId" customattr2="TableType=LIST;ListNum=1000">
        <entry name="slipId" type="int64" version="1" />
        <entry name="Id" type="uint64" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="commentContent" type="string" size="1024" version="1" />
        <entry name="createdTime" type="uint64" version="1" />
    </struct>

    <struct name="MessageFavour" version="1" primarykey="slipId" splittablekey="slipId" customattr2="TableType=LIST;ListNum=1000">
        <entry name="slipId" type="int64" version="1" />
        <entry name="uuid" type="int64" version="1" />
        <entry name="createdTime" type="uint64" version="1" />
    </struct>

    <struct name="UgcPlayerInfo" version="1" primarykey="creatorId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcTasks" type="DbPlatBuffer" version="1" />
        <entry name="ugcExp" type="int64" version="1" />
        <entry name="ugcLv" type="int32" version="1" />
        <entry name="ugcSeasonExp" type="int64" version="1" />
        <entry name="playerHistorySeasonExp" type="DbPlatBuffer" version="1" />
        <entry name="seasonId" type="int32" version="1" />
        <entry name="fansCount" type="int32" version="1" />
        <entry name="subCount" type="int32" version="1" />
        <entry name="beLikeCount" type="int32" version="1" />
        <entry name="playCount" type="int32" version="1" />
        <entry name="publishCount" type="int32" version="1" />
        <entry name="passCount" type="int32" version="1" />
        <entry name="nickname" type="string" size="1024" version="1" />
        <entry name="gender" type="int32" version="1" />
        <entry name="profile" type="string" size="1024" version="1" />
        <entry name="shortUid" type="uint64" version="1" />
        <entry name="mapCollect" type="DbPlatBuffer" version="1" />
        <entry name="groupCollect" type="DbPlatBuffer" version="1" />
        <entry name="fans" type="DbPlatBuffer" version="1" />
        <entry name="subs" type="DbPlatBuffer" version="1" />
        <entry name="ugcLvInfo" type="DbPlatBuffer" version="1" />
        <entry name="ugcExpFlag" type="int32" version="1" />
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="platId" type="int32" version="1" />
        <entry name="dressIds" type="DbPlatBuffer" version="1" />
        <entry name="loginType" type="int32" version="1" />
        <entry name="rankGeoInfo" type="DbPlatBuffer" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="dressItemInfos" type="DbPlatBuffer" version="199" />
        <entry name="gameSettings" type="DbPlatBuffer" version="199" />
        <entry name="qualifiedMapCount" type="int32" version="202" />
        <entry name="commonInfo" type="DbPlatBuffer" version="205" />
        <entry name="peakUgcExp" type="int32" version="209" />
        <entry name="accountInfo" type="DbPlatBuffer" version="212" />
        <entry name="ugcExpUpdateMs" type="int64" version="265" />
        <entry name="trustworthy" type="int32" version="300" />
        <entry name="bitFlag" type="int64" version="315" />
        <entry name="accountType" type="int32" version="334" />
        <entry name="accountSource" type="int32" version="335" />
        <entry name="appOnline" type="int32" version="392" />
        <entry name="createCount" type="int32" version="403" />
        <entry name="homePageInfo" type="DbPlatBuffer" version="406" />
        <entry name="pictureLimit" type="int32" version="426" />
    </struct>

    <struct name="UgcPlayerManagementInfo" version="1" primarykey="creatorId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="source" type="int32" version="1" />
        <entry name="maintenanceState" type="string" size="1024" version="1" />
        <entry name="certificationState" type="int32" version="1" />
        <entry name="creatorType" type="int32" version="1" />
        <entry name="blackList" type="int32" version="1" />
        <entry name="fansCount" type="int32" version="1" />
        <entry name="subCount" type="int32" version="1" />
    </struct>

    <struct name="PlayerUgcGiveLike" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="status" type="int32" version="1" />
        <entry name="mapType" type="int32" version="1" />
        <entry name="ugcResType" type="int32" version="263" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="PlayerUgcCollect" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="status" type="int32" version="1" />
        <entry name="mapType" type="int32" version="1" />
        <entry name="ugcResType" type="int32" version="263" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="PlayerUgcSub" version="1" primarykey="creatorId,subId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="subId" type="int64" version="1" />
        <entry name="isTop" type="int32" version="1" />
        <entry name="topTime" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="PlayerUgcFans" version="1" primarykey="creatorId,fanId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="fanId" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="PlayerUgcFansList" version="1" primarykey="creatorId" splittablekey="creatorId" customattr2="TableType=LIST;ListNum=1000">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="fanId" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
    </struct>

    <struct name="UgcCoCreateBrief" version="1" primarykey="ugcId" splittablekey="ugcId" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="member" type="DbPlatBuffer" version="216" />
        <entry name="brief" type="DbPlatBuffer" version="216" />
        <entry name="name" type="string" size="1024" version="230" />
        <entry name="templateId" type="int32" version="230" />
        <entry name="saveType" type="int32" version="230" />
        <entry name="saveCount" type="int32" version="230" />
        <entry name="createTime" type="int64" version="230" />
        <entry name="expireTime" type="int64" version="230" />
        <entry name="mdList" type="DbPlatBuffer" version="230" />
        <entry name="oldUgcId" type="int64" version="230" />
        <entry name="desc" type="string" size="1024" version="230" />
        <entry name="tags" type="string" size="1024" version="230" />
        <entry name="reportStatus" type="int32" version="230" />
        <entry name="rejectTime" type="int64" version="230" />
        <entry name="editorSec" type="int32" version="230" />
        <entry name="mapType" type="int32" version="230" />
        <entry name="isCollect" type="int32" version="230" />
        <entry name="collectTime" type="int64" version="230" />
        <entry name="extraInfo" type="DbPlatBuffer" version="230" />
        <entry name="bucket" type="string" size="1024" version="230" />
        <entry name="ugcGroupIdList" type="DbPlatBuffer" version="230" />
        <entry name="ugcVersion" type="string" size="1024" version="230" />
        <entry name="clientVersion" type="string" size="1024" version="230" />
        <entry name="saveInfo" type="DbPlatBuffer" version="230" />
        <entry name="metaList" type="DbPlatBuffer" version="230" />
        <entry name="difficulty" type="string" size="1024" version="230" />
        <entry name="commMap" type="DbPlatBuffer" version="230" />
        <entry name="layers" type="DbPlatBuffer" version="230" />
        <entry name="updateTime" type="int64" version="230" />
        <entry name="publishInfo" type="DbPlatBuffer" version="236" />
        <entry name="secFlags" type="int64" version="242" />
        <entry name="disableMultiTest" type="int32" version="287" />
        <entry name="resIdList" type="DbPlatBuffer" version="304" />
        <entry name="publishTime" type="int64" version="307" />
        <entry name="publishGoodsStatus" type="int32" version="309" />
        <entry name="buyGoodsStatus" type="int32" version="309" />
        <entry name="cover" type="DbPlatBuffer" version="321" />
        <entry name="modelType" type="int32" version="332" />
        <entry name="curLayerId" type="int32" version="358" />
        <entry name="hasPublishGoodsRecord" type="int32" version="363" />
        <entry name="ugcAchievement" type="DbPlatBuffer" version="379" />
        <entry name="achievementVerId" type="int32" version="379" />
        <entry name="evaluationStatus" type="int32" version="394" />
        <entry name="evaluationStatusReport" type="int32" version="396" />
        <entry name="extraConfigIndexMap" type="DbPlatBuffer" version="397" />
        <entry name="extraConfigIndexVerId" type="int32" version="397" />
        <entry name="mapLoading" type="DbPlatBuffer" version="401" />
        <entry name="lobbyCover" type="DbPlatBuffer" version="401" />
        <entry name="mgrInfo" type="DbPlatBuffer" version="402" />
        <entry name="videoInfo" type="DbPlatBuffer" version="406" />
        <entry name="codingData" type="DbPlatBuffer" version="421" />
        <entry name="isAdvert" type="int32" version="424" />
        <entry name="advertStatus" type="int32" version="424" />
        <entry name="interfaceData" type="DbPlatBuffer" version="425" />
        <index name="index_type" column="ugcId" />
    </struct>

    <struct name="MapPlayedList" version="1" primarykey="creatorId" splittablekey="creatorId" customattr2="TableType=LIST;ListNum=2000">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="mapId" type="int64" version="1" />
    </struct>

    <struct name="UgcDailyStage" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="stepInfos" type="DbPlatBuffer" version="1" />
        <entry name="resetCount" type="int32" version="1" />
        <entry name="lastDayResetTime" type="int64" version="1" />
        <entry name="roundId" type="int64" version="1" />
    </struct>

    <struct name="SeqRuleTable" version="1" primarykey="seqType" splittablekey="seqType" customattr2="TableType=GENERIC">
        <entry name="seqType" type="int32" version="1" />
        <entry name="minSeq" type="int64" version="1" />
        <entry name="maxSeq" type="int64" version="1" />
        <entry name="increaseSeq" type="int64" version="1" />
        <entry name="seqScale" type="int64" version="1" />
        <entry name="seqOffset" type="int64" version="1" />
        <entry name="curSeq" type="int64" version="1" />
        <entry name="encryptType" type="int32" version="1" />
        <entry name="encryptKey" type="string" size="1024" version="1" />
        <entry name="expireTime" type="int64" version="1" />
        <entry name="encryptParams" type="DbPlatBuffer" version="179" />
        <index name="index_type" column="seqType" />
    </struct>

    <struct name="SeqSegmentTable" version="1" primarykey="seqType,startSeqId" splittablekey="seqType" customattr2="TableType=GENERIC">
        <entry name="seqType" type="int32" version="1" />
        <entry name="startSeqId" type="int64" version="1" />
        <entry name="endSeqId" type="int64" version="1" />
        <entry name="useFlags" type="DbPlatBuffer" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="useUpTime" type="int64" version="1" />
        <entry name="lockCheckTime" type="int64" version="1" />
        <index name="index_type" column="seqType,startSeqId" />
    </struct>

    <struct name="SeqTable" version="1" primarykey="seqType,seqId" splittablekey="seqId" customattr2="TableType=GENERIC">
        <entry name="seqType" type="int32" version="1" />
        <entry name="seqId" type="int64" version="1" />
        <entry name="desc" type="string" size="1024" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="reqSvr" type="string" size="1024" version="1" />
        <entry name="allocSvr" type="string" size="1024" version="1" />
        <entry name="useTime" type="int64" version="1" />
        <entry name="lastHeartbeatTime" type="int64" version="1" />
        <entry name="freeTime" type="int64" version="1" />
        <entry name="cacheTime" type="int64" version="1" />
        <entry name="encryptVersion" type="int32" version="183" />
        <index name="index_type" column="seqType,seqId" />
    </struct>

    <struct name="PlayerUgcPlay" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="isPass" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="sec" type="int32" version="1" />
        <entry name="avatar" type="string" size="1024" version="1" />
        <entry name="nickName" type="string" size="1024" version="1" />
        <entry name="playTime" type="int64" version="357" />
        <entry name="lastEndBattleTime" type="int64" version="378" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="GlobalInfoTable" version="1" primarykey="typeId" splittablekey="typeId" customattr2="TableType=GENERIC">
        <entry name="typeId" type="uint32" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="globalInfo" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="typeId" />
    </struct>

    <struct name="AccountCancelTaskTable" version="1" primarykey="openId,platId" splittablekey="openId" customattr2="TableType=GENERIC">
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="platId" type="int32" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="state" type="int32" version="1" />
        <entry name="cleanStage" type="int32" version="1" />
        <entry name="cancelTime" type="int64" version="1" />
        <entry name="cleanCompleteTime" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <index name="index1" column="openId,platId" />
        <index name="index2" column="openId" />
    </struct>

    <struct name="MultiLanguageConfTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="int64" version="1" />
        <entry name="multiLanguageConfList" type="DbPlatBuffer" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <index name="index_type" column="id" />
    </struct>

    <struct name="XiaoWo" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="XiaowoAttr" type="DbPlatBuffer" version="192" />
        <entry name="ChangedXiaowoAttr" type="DbPlatBuffer" version="192" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="SnsInvitationTable" version="1" primarykey="invitationId" splittablekey="invitationId" customattr2="TableType=GENERIC">
        <entry name="invitationId" type="int64" version="1" />
        <entry name="cfgId" type="int32" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="inviterId" type="int64" version="1" />
        <entry name="inviteeNum" type="int32" version="1" />
        <entry name="lastInviteeTs" type="int64" version="1" />
        <index name="index_type" column="invitationId" />
    </struct>

    <struct name="GlobalMailInfoTable" version="1" primarykey="type,worldId,id" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="type" type="uint32" version="1" />
        <entry name="worldId" type="uint32" version="1" />
        <entry name="id" type="uint64" version="1" />
        <entry name="currentTime" type="uint64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <index name="index_type1" column="type" />
        <index name="index_type2" column="type,worldId" />
        <index name="index_type3" column="type,worldId,id" />
    </struct>

    <struct name="InviteRegisterTable" version="1" primarykey="openId" splittablekey="openId" customattr2="TableType=GENERIC">
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="inviterUid" type="int64" version="1" />
        <entry name="inviterOpenId" type="string" size="1024" version="1" />
        <entry name="createTimeMs" type="int64" version="1" />
        <entry name="activityType" type="int32" version="241" />
        <entry name="activityId" type="int32" version="241" />
        <index name="index_type" column="openId" />
    </struct>

    <struct name="UGCTranslationDataInGameTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="string" size="1024" version="1" />
        <entry name="ugcId" type="int64" version="212" />
        <entry name="dbTranslationDataList" type="DbPlatBuffer" version="1" />
        <entry name="dataState" type="int32" version="1" />
        <index name="index_type" column="id" />
    </struct>

    <struct name="UGCTranslationDataOutGameTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="string" size="1024" version="1" />
        <entry name="ugcId" type="int64" version="212" />
        <entry name="dbTranslationDataList" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="id" />
    </struct>

    <struct name="BattleInfoDSCLoadDataTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="int32" version="1" />
        <entry name="battleDSCLoadData" type="DbPlatBuffer" version="1" />
        <entry name="battleWarmNormalInfo" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="id" />
    </struct>

    <struct name="DeleteBriefRecord" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="instance" type="int32" version="1" />
        <entry name="bucket" type="string" size="1024" version="1" />
        <entry name="mdList" type="DbPlatBuffer" version="1" />
        <entry name="commMap" type="DbPlatBuffer" version="1" />
        <entry name="layers" type="DbPlatBuffer" version="1" />
        <entry name="extraParams" type="string" size="256000" version="1" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="IdipSendMailFlowTable" version="1" primarykey="serialNo" splittablekey="serialNo" customattr2="TableType=GENERIC">
        <entry name="serialNo" type="string" size="1024" version="1" />
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="platId" type="int32" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="currentTime" type="uint64" version="1" />
        <entry name="source" type="uint32" version="291" />
        <index name="index_type1" column="serialNo" />
    </struct>

    <struct name="SampleRoomAllocatorTable" version="1" primarykey="type,confId,groupId" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="type" type="int32" version="1" />
        <entry name="confId" type="int64" version="1" />
        <entry name="groupId" type="int64" version="1" />
        <entry name="sampleRoomAllocatorData" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="type" />
    </struct>

    <struct name="XiaoWoLayoutTable" version="1" primarykey="layoutId" splittablekey="layoutId" customattr2="TableType=GENERIC">
        <entry name="layoutId" type="int64" version="1" />
        <entry name="layoutInfo" type="DbPlatBuffer" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="saveTime" type="uint64" version="1" />
        <index name="index_type" column="layoutId" />
    </struct>

    <struct name="XiaoWoLayoutPublishRecordTable" version="1" primarykey="recordId,uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="recordId" type="int64" version="1" />
        <entry name="recordInfo" type="DbPlatBuffer" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="saveTime" type="uint64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="PlayerActivityInteractionTable" version="1" primarykey="openId,platId,actId,seq" splittablekey="openId,platId" customattr2="TableType=GENERIC">
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="platId" type="int32" version="1" />
        <entry name="actId" type="int32" version="1" />
        <entry name="seq" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="src" type="int64" version="1" />
        <index name="index_act" column="openId,platId,actId" />
        <index name="index_user" column="openId,platId" />
    </struct>

    <struct name="ActivityInteractionTable" version="1" primarykey="uid,actId,seq" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="actId" type="int32" version="1" />
        <entry name="seq" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="src" type="int64" version="1" />
        <index name="index_act" column="uid,actId" />
        <index name="index_user" column="uid" />
    </struct>

    <struct name="WolfKillTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="reputationScore" type="int32" version="1" />
        <entry name="scoreRecord" type="DbPlatBuffer" version="1" />
        <entry name="singleReportRecord" type="DbPlatBuffer" version="306" />
        <entry name="reportRecord" type="DbPlatBuffer" version="306" />
        <entry name="roadToMaster" type="DbPlatBuffer" version="351" />
        <entry name="roleInfo" type="DbPlatBuffer" version="363" />
        <entry name="feedbackCount" type="int32" version="367" />
        <entry name="seasonRecvCount" type="int32" version="369" />
        <entry name="passiveReport" type="DbPlatBuffer" version="376" />
        <entry name="actionScore" type="int32" version="380" />
        <entry name="actionScoreInit" type="int32" version="381" />
        <entry name="repeatWin" type="int32" version="384" />
        <entry name="campRepeatWin" type="DbPlatBuffer" version="384" />
        <entry name="wolfNewPlayer" type="int32" version="385" />
        <entry name="wolfKillComeBack" type="DbPlatBuffer" version="386" />
        <entry name="latestTs" type="int64" version="387" />
        <entry name="wolfKillViolation" type="DbPlatBuffer" version="389" />
        <entry name="identityItemRecord" type="DbPlatBuffer" version="411" />
        <entry name="shieldVocation" type="DbPlatBuffer" version="411" />
        <entry name="monthCard" type="DbPlatBuffer" version="420" />
        <entry name="warmBattle" type="DbPlatBuffer" version="428" />
        <entry name="latestMatchTs" type="int64" version="429" />
    </struct>

    <struct name="ClubInfoTable" version="1" primarykey="clubId" splittablekey="clubId" customattr2="TableType=GENERIC">
        <entry name="clubId" type="int64" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="instanceId" type="int64" version="1" />
        <entry name="clubName" type="string" size="1024" version="250" />
        <entry name="clubIcon" type="int32" version="250" />
        <entry name="status" type="int32" version="250" />
        <entry name="msdkId" type="string" size="1024" version="250" />
        <entry name="title" type="string" size="1024" version="250" />
        <entry name="heat" type="int32" version="250" />
        <entry name="ownerUid" type="int64" version="250" />
        <entry name="memberCount" type="int32" version="250" />
        <entry name="boyCount" type="int32" version="250" />
        <entry name="girlCount" type="int32" version="250" />
        <entry name="brief" type="string" size="1024" version="252" />
        <entry name="basicInfo" type="DbPlatBuffer" version="1" />
        <entry name="memberInfo" type="DbPlatBuffer" version="1" />
        <entry name="extraInfo" type="DbPlatBuffer" version="1" />
        <entry name="applicantInfo" type="DbPlatBuffer" version="254" />
        <entry name="msdkAreaId" type="string" size="1024" version="255" />
        <entry name="msdkZoneId" type="string" size="1024" version="255" />
        <entry name="msdkGroupName" type="string" size="1024" version="255" />
        <entry name="creatorOpenId" type="string" size="1024" version="255" />
        <entry name="ownerOpenId" type="string" size="1024" version="255" />
        <entry name="instanceLease" type="int64" version="257" />
        <entry name="labels" type="string" size="1024" version="305" />
        <entry name="shortId" type="int64" version="331" />
        <entry name="challengeInfo" type="DbPlatBufferLarge" version="357" />
        <entry name="todayActivePlayerInfo" type="DbPlatBuffer" version="373" />
        <entry name="activeScoreInfo" type="DbPlatBuffer" version="404" />
        <entry name="autoJoinNum" type="int32" version="405" />
        <index name="index_type" column="clubId" />
    </struct>

    <struct name="ClubLogTable" version="1" primarykey="logId,clubId,logDay" splittablekey="clubId" customattr2="TableType=GENERIC">
        <entry name="logId" type="int64" version="1" />
        <entry name="clubId" type="int64" version="1" />
        <entry name="logDay" type="int32" version="1" />
        <entry name="logTime" type="int64" version="1" />
        <entry name="logType" type="int32" version="1" />
        <entry name="logClass" type="int32" version="1" />
        <entry name="logItem" type="DbPlatBuffer" version="1" />
        <index name="index_club" column="clubId" />
        <index name="index_club_day" column="clubId,logDay" />
        <index name="index_club_day_id" column="clubId,logDay,logId" />
    </struct>

    <struct name="UserDsDBTable" version="1" primarykey="uid,matchType,slotIdx" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="matchType" type="int32" version="1" />
        <entry name="DsUserDBInfo" type="DbPlatBuffer" version="1" />
        <entry name="slotIdx" type="int32" version="257" />
        <entry name="updateVersion" type="int64" version="291" />
        <entry name="battleId" type="int64" version="291" />
        <entry name="createTime" type="int64" version="291" />
        <index name="index1" column="uid,matchType,slotIdx" />
    </struct>

    <struct name="MarqueeNoticeTable" version="1" primarykey="type,noticeId" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="noticeId" type="uint32" version="1" />
        <entry name="areaId" type="uint32" version="1" />
        <entry name="platId" type="uint32" version="1" />
        <entry name="partition" type="uint32" version="1" />
        <entry name="imageBackground" type="string" size="1024" version="1" />
        <entry name="repeatedCnt" type="uint32" version="1" />
        <entry name="timeInterval" type="uint32" version="1" />
        <entry name="content" type="string" size="1024" version="1" />
        <entry name="beginTime" type="uint64" version="1" />
        <entry name="endTime" type="uint64" version="1" />
        <entry name="type" type="uint32" version="259" />
        <index name="index_type1" column="type" />
        <index name="index_type2" column="type,noticeId" />
    </struct>

    <struct name="MarqueeNoticeInfoTable" version="1" primarykey="type,noticeId" splittablekey="type" customattr2="TableType=GENERIC">
        <entry name="type" type="uint32" version="1" />
        <entry name="noticeId" type="uint64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="267" />
        <index name="index_type1" column="type" />
        <index name="index_type2" column="type,noticeId" />
    </struct>

    <struct name="UgcResBrief" version="1" primarykey="creatorId,resType,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="resType" type="int32" version="1" />
        <entry name="saveType" type="int32" version="1" />
        <entry name="name" type="string" size="1024" version="1" />
        <entry name="desc" type="string" size="1024" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="mdList" type="DbPlatBuffer" version="1" />
        <entry name="oldUgcId" type="int64" version="1" />
        <entry name="reportStatus" type="int32" version="1" />
        <entry name="isCollect" type="int32" version="1" />
        <entry name="collectTime" type="int64" version="1" />
        <entry name="extraInfo" type="DbPlatBuffer" version="1" />
        <entry name="bucket" type="string" size="1024" version="1" />
        <entry name="ugcVersion" type="string" size="1024" version="1" />
        <entry name="clientVersion" type="string" size="1024" version="1" />
        <entry name="resCategory" type="int32" version="1" />
        <entry name="resSubCategory" type="int32" version="1" />
        <entry name="resLabels" type="string" size="1024" version="1" />
        <entry name="instanceType" type="int32" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="publishInfo" type="DbPlatBuffer" version="1" />
        <entry name="isResPubCosPath" type="int32" version="321" />
        <entry name="realGroupResType" type="int32" version="324" />
        <index name="index_type" column="creatorId" />
        <index name="index_type2" column="creatorId,resType" />
    </struct>

    <struct name="UgcResBag" version="1" primarykey="creatorId,resType" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="resType" type="int32" version="1" />
        <entry name="resource" type="DbPlatBuffer" version="1" />
        <entry name="lastSyncTime" type="int64" version="1" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="RedPacketTable" version="1" primarykey="packetUuid" splittablekey="packetUuid" customattr2="TableType=GENERIC">
        <entry name="packetUuid" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="placeType" type="int32" version="284" />
        <entry name="placeId" type="int64" version="284" />
        <entry name="position" type="DbPlatBuffer" version="284" />
        <index name="index_type" column="packetUuid" />
    </struct>

    <struct name="RedEnvelopeRainOppTable" version="1" primarykey="playerUid" splittablekey="playerUid" customattr2="TableType=GENERIC">
        <entry name="playerUid" type="int64" version="1" />
        <entry name="dayZeroSec" type="int64" version="1" />
        <entry name="oppCnt" type="int32" version="1" />
        <index name="index_type" column="playerUid" />
    </struct>

    <struct name="MsgQueueChannel" version="1" primarykey="topic" splittablekey="topic" customattr2="TableType=LIST;ListNum=1023">
        <entry name="topic" type="string" size="1024" version="1" />
        <entry name="content" type="DbPlatBuffer" version="1" />
        <entry name="src" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
    </struct>

    <struct name="MsgQueueSubscriber" version="1" primarykey="topic,id" splittablekey="topic" customattr2="TableType=GENERIC">
        <entry name="topic" type="string" size="1024" version="1" />
        <entry name="id" type="int64" version="1" />
        <entry name="channelCursor" type="int32" version="1" />
        <entry name="channelVersion" type="int32" version="1" />
    </struct>

    <struct name="AIGCHistoryTable" version="1" primarykey="uid,type" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="record" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="PlayerToClubTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="joinedList" type="DbPlatBuffer" version="1" />
        <entry name="appliedList" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="GeneralOutputControl" version="1" primarykey="infoType,uid1,uid2" splittablekey="infoType" customattr2="TableType=GENERIC">
        <entry name="infoType" type="int32" version="1" />
        <entry name="uid1" type="int64" version="1" />
        <entry name="uid2" type="int64" version="1" />
        <entry name="info" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="infoType" />
        <index name="index_uid1" column="infoType,uid1" />
    </struct>

    <struct name="GeneralOutputControlTable" version="1" primarykey="infoType,uid1,uid2" splittablekey="uid1" customattr2="TableType=GENERIC">
        <entry name="infoType" type="int32" version="1" />
        <entry name="uid1" type="int64" version="1" />
        <entry name="uid2" type="int64" version="1" />
        <entry name="info" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uid1" />
        <index name="index2" column="infoType,uid1" />
    </struct>

    <struct name="UltramanSquadTable" version="1" primarykey="teamId,type" splittablekey="teamId" customattr2="TableType=GENERIC">
        <entry name="teamId" type="int32" version="1" />
        <entry name="leaderUid" type="int64" version="1" />
        <entry name="memberList" type="DbPlatBuffer" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="teamScore" type="int32" version="1" />
        <entry name="kvArray" type="DbPlatBuffer" version="1" />
        <entry name="type" type="int32" version="1" />
        <index name="index_type" column="teamId" />
    </struct>

    <struct name="UgcCollectionTable" version="1" primarykey="creatorId,collectionId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="collectionId" type="string" size="1024" version="1" />
        <entry name="face" type="DbPlatBuffer" version="1" />
        <entry name="info" type="DbPlatBuffer" version="1" />
        <entry name="maps" type="DbPlatBuffer" version="1" />
        <entry name="adminData" type="DbPlatBuffer" version="1" />
        <entry name="status" type="int64" version="1" />
        <entry name="playTimesUv" type="int64" version="1" />
        <entry name="playTimesPv" type="int64" version="1" />
        <entry name="collectTimes" type="int64" version="1" />
        <index name="index_creator" column="creatorId" />
    </struct>

    <struct name="PlayerGameSettingTable" version="1" primarykey="uid,idx" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="idx" type="int32" version="1" />
        <entry name="image" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uid,idx" />
        <index name="index2" column="uid" />
    </struct>

    <struct name="UgcDataStore" version="1" primarykey="ugcId,uid,saveId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="saveId" type="int32" version="1" />
        <entry name="saveName" type="string" size="1024" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="paidItem" type="DbPlatBuffer" version="1" />
        <entry name="recentBillNo" type="DbPlatBuffer" version="1" />
        <entry name="bag" type="DbPlatBufferLarge" version="1" />
        <entry name="task" type="DbPlatBufferLarge" version="1" />
        <entry name="attr" type="DbPlatBufferLarge" version="1" />
        <entry name="customKv1" type="DbPlatBufferLarge" version="1" />
        <entry name="customKv2" type="DbPlatBufferLarge" version="1" />
        <entry name="customKv3" type="DbPlatBufferLarge" version="1" />
        <entry name="shop" type="DbPlatBufferLarge" version="356" />
        <entry name="dataVersion" type="int64" version="359" />
        <entry name="baseInfo" type="DbPlatBuffer" version="366" />
        <index name="index1" column="uid" />
        <index name="index2" column="uid,ugcId" />
    </struct>

    <struct name="UgcPlayerPublic" version="1" primarykey="ugcId,uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="attrsData" type="DbPlatBuffer" version="1" />
        <entry name="arrayAttrsData" type="DbPlatBuffer" version="1" />
        <entry name="dsData" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uid" />
    </struct>

    <struct name="UgcResAssociation" version="1" primarykey="resId,ugcId" splittablekey="resId" customattr2="TableType=GENERIC">
        <entry name="resId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="status" type="int32" version="1" />
        <entry name="isPrivate" type="int32" version="1" />
        <index name="index" column="resId" />
    </struct>

    <struct name="DanMuTable" version="1" primarykey="danMuId,blockId,recordId,danMuType" splittablekey="danMuId" customattr2="TableType=GENERIC">
        <entry name="danMuId" type="int64" version="1" />
        <entry name="blockId" type="int64" version="1" />
        <entry name="recordId" type="int64" version="1" />
        <entry name="danMuType" type="int32" version="1" />
        <entry name="danMuSort" type="DbPlatBuffer" version="1" />
        <entry name="danMuDBAttrInfo" type="DbPlatBuffer" version="1" />
        <entry name="timestamp" type="int64" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="like" type="int32" version="1" />
        <index name="index1" column="danMuId,blockId,danMuType" />
        <index name="index2" column="danMuId,recordId,danMuType" />
        <index name="index3" column="danMuId,danMuType" />
    </struct>

    <struct name="DanMuOptTable" version="1" primarykey="danMuId,blockId,recordId,danMuType,optType,fromUid" splittablekey="danMuId" customattr2="TableType=GENERIC">
        <entry name="danMuId" type="int64" version="1" />
        <entry name="blockId" type="int64" version="1" />
        <entry name="recordId" type="int64" version="1" />
        <entry name="danMuType" type="int32" version="1" />
        <entry name="optType" type="int32" version="1" />
        <entry name="fromUid" type="int64" version="1" />
        <entry name="optValue" type="int32" version="1" />
        <index name="index1" column="danMuId,blockId,danMuType" />
        <index name="index2" column="danMuId,blockId,recordId,danMuType" />
        <index name="index3" column="danMuId,blockId,recordId,danMuType,optType" />
    </struct>

    <struct name="ActivityCommonSquadTable" version="1" primarykey="squadId" splittablekey="squadId" customattr2="TableType=GENERIC">
        <entry name="squadId" type="int64" version="1" />
        <entry name="activityId" type="int32" version="1" />
        <entry name="leaderUid" type="int64" version="1" />
        <entry name="memberList" type="DbPlatBuffer" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="kickUidList" type="DbPlatBuffer" version="375" />
        <entry name="createTimeMs" type="int64" version="393" />
        <index name="index_type" column="squadId" />
    </struct>

    <struct name="ActivityPlayerTeamTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="int64" version="1" />
        <entry name="activityId" type="int32" version="1" />
        <entry name="activityEndTime" type="int64" version="1" />
        <entry name="createUid" type="int64" version="1" />
        <entry name="createTimeMs" type="int64" version="1" />
        <entry name="leaderUid" type="int64" version="1" />
        <entry name="memberList" type="DbPlatBuffer" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="canRemove" type="int32" version="1" />
        <index name="index_type" column="id" />
    </struct>

    <struct name="PlayerMapLabelScore" version="1" primarykey="creatorId,ugcId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="ugcId" type="int64" version="1" />
        <entry name="info" type="DbPlatBuffer" version="1" />
        <index name="index1" column="creatorId" />
    </struct>

    <struct name="UniqueCheckTable" version="1" primarykey="uid,type,uniqueId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="uniqueId" type="string" size="1024" version="1" />
        <entry name="value" type="int64" version="1" />
        <entry name="extraData" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid,type" />
    </struct>

    <struct name="PlayerUgcBuyPartnerTable" version="1" primarykey="creatorId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="resOfferId" type="string" size="1024" version="1" />
        <entry name="resOfferName" type="string" size="1024" version="1" />
        <entry name="resOfferLogo" type="string" size="1024" version="1" />
        <entry name="sandboxTdeaSecretId" type="string" size="1024" version="1" />
        <entry name="sandboxTdeaSecretKey" type="string" size="1024" version="1" />
        <entry name="tdeaSecretId" type="string" size="1024" version="1" />
        <entry name="tdeaSecretKey" type="string" size="1024" version="1" />
        <entry name="sandboxSecretKey" type="string" size="1024" version="1" />
        <entry name="secretKey" type="string" size="1024" version="1" />
        <entry name="zoneAndSvr" type="DbPlatBuffer" version="1" />
        <index name="index1" column="creatorId" />
    </struct>

    <struct name="Farm" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="FarmAttr" type="DbPlatBuffer" version="1" />
        <entry name="ChangedFarmAttr" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="PlayerReputationScoreTable" version="1" primarykey="uid,scoreId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="scoreId" type="int32" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="reputationScore" type="int32" version="1" />
        <entry name="scoreInfo" type="DbPlatBuffer" version="1" />
        <index name="index_type1" column="uid,scoreId" />
        <index name="index_type2" column="uid" />
    </struct>

    <struct name="ClubColdRankTable" version="1" primarykey="clubId,type" splittablekey="clubId" customattr2="TableType=GENERIC">
        <entry name="clubId" type="int64" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="coldRankInfo" type="DbPlatBuffer" version="1" />
        <entry name="lastRankInfo" type="DbPlatBuffer" version="1" />
        <index name="index_type1" column="clubId" />
        <index name="index_type2" column="clubId,type" />
    </struct>

    <struct name="AppUserInfoTable" version="1" primarykey="infoKey,typeId" splittablekey="infoKey" customattr2="TableType=GENERIC">
        <entry name="infoKey" type="string" size="1024" version="1" />
        <entry name="typeId" type="uint32" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="appUserInfo" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="infoKey,typeId" />
    </struct>

    <struct name="MallBuyTimesTable" version="1" primarykey="uid,commodityId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="commodityId" type="int32" version="1" />
        <entry name="commodityBuyRecord" type="DbPlatBuffer" version="1" />
        <index name="index_type1" column="uid" />
        <index name="index_type2" column="uid,commodityId" />
    </struct>

    <struct name="MatchBattlePlayerRecord" version="1" primarykey="playId" splittablekey="playId" customattr2="TableType=GENERIC">
        <entry name="playId" type="int64" version="1" />
        <entry name="playTime" type="int64" version="1" />
        <entry name="detail" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="PlayerSnsTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="snsAttr" type="DbPlatBuffer" version="1" />
        <entry name="instanceId" type="int32" version="1" />
        <entry name="instanceLease" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="AppreciateMapTable" version="1" primarykey="creatorId,mapId,timeIdx" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="mapId" type="int64" version="1" />
        <entry name="timeIdx" type="int64" version="1" />
        <entry name="info" type="DbPlatBuffer" version="1" />
        <entry name="record" type="DbPlatBuffer" version="1" />
        <index name="index_time" column="creatorId,timeIdx" />
        <index name="index_creator" column="creatorId" />
        <index name="index_map" column="creatorId,mapId" />
    </struct>

    <struct name="TrainingCampTable" version="1" primarykey="uid,activityId,activityTime" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="activityId" type="int32" version="1" />
        <entry name="activityTime" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="assisted" type="DbPlatBuffer" version="1" />
        <entry name="beAssisted" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uid,activityId,activityTime" />
    </struct>

    <struct name="TrainingActivityTable" version="1" primarykey="uid,activityId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="activityId" type="int32" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <entry name="assisted" type="DbPlatBuffer" version="1" />
        <entry name="beAssisted" type="DbPlatBuffer" version="1" />
        <entry name="assistDetail" type="DbPlatBuffer" version="1" />
        <entry name="beAssistDetail" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uid,activityId" />
    </struct>

    <struct name="FriendHistoryInteractDataTable" version="1" primarykey="Uid1,Uid2" splittablekey="Uid1" customattr2="TableType=GENERIC">
        <entry name="Uid1" type="int64" version="1" />
        <entry name="Uid2" type="int64" version="1" />
        <entry name="UpdateTime" type="int64" version="1" />
        <entry name="TeamBattleCnt" type="int32" version="1" />
        <entry name="ChatCnt" type="int32" version="1" />
        <entry name="SendGoldCoinCnt" type="int32" version="1" />
        <entry name="VisitFarmCnt" type="int32" version="1" />
        <entry name="VisitXiaoWoCnt" type="int32" version="1" />
        <index name="index1" column="Uid1" />
        <index name="index2" column="Uid1,Uid2" />
    </struct>

    <struct name="ArenaTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="Arena" type="DbPlatBuffer" version="1" />
        <entry name="instanceId" type="int32" version="1" />
        <entry name="instanceLease" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="ArenaHeroStatisticsTable" version="1" primarykey="group,heroId,qualifyId,degreeId" splittablekey="heroId" customattr2="TableType=GENERIC">
        <entry name="group" type="int32" version="1" />
        <entry name="heroId" type="int32" version="1" />
        <entry name="qualifyId" type="int32" version="1" />
        <entry name="degreeId" type="int32" version="1" />
        <entry name="updateTimeMs" type="int64" version="1" />
        <entry name="topPercentileList" type="string" size="1024" version="1" />
        <index name="index_group_hero_qualify_degree" column="group,heroId,qualifyId,degreeId" />
    </struct>

    <struct name="ArenaHeroBattleStatisticsTable" version="1" primarykey="heroId,group,qualifyId,degreeId" splittablekey="heroId" customattr2="TableType=GENERIC">
        <entry name="heroId" type="int32" version="1" />
        <entry name="group" type="int32" version="1" />
        <entry name="qualifyId" type="int32" version="1" />
        <entry name="degreeId" type="int32" version="1" />
        <entry name="winRate" type="int32" version="1" />
        <entry name="appearanceRate" type="int32" version="1" />
        <entry name="banRate" type="int32" version="1" />
        <entry name="bluePopularCard" type="string" size="1024" version="1" />
        <entry name="purplePopularCard" type="string" size="1024" version="1" />
        <entry name="orangePopularCard" type="string" size="1024" version="1" />
        <index name="index_hero_group_qualify_degree" column="heroId,group,qualifyId,degreeId" />
    </struct>

    <struct name="UgcMultiVersionTable" version="1" primarykey="ugcId,mulVersion" splittablekey="ugcId" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="mulVersion" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="status" type="int32" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="saveType" type="int32" version="1" />
        <entry name="mdList" type="DbPlatBuffer" version="1" />
        <entry name="coverList" type="DbPlatBuffer" version="1" />
        <entry name="layerList" type="DbPlatBuffer" version="1" />
        <entry name="resIdList" type="DbPlatBuffer" version="1" />
        <entry name="ugcGoodsList" type="DbPlatBuffer" version="1" />
        <entry name="ugcInstanceType" type="int32" version="1" />
        <entry name="desc" type="string" size="1024" version="1" />
        <entry name="tags" type="string" size="1024" version="1" />
        <entry name="descInfo" type="DbPlatBuffer" version="1" />
        <entry name="pointsNumber" type="int32" version="1" />
        <entry name="clientVersion" type="string" size="1024" version="1" />
        <entry name="buyGoodsStatus" type="int32" version="1" />
        <entry name="templateId" type="int32" version="1" />
        <entry name="editorSec" type="int32" version="1" />
        <entry name="extraInfo" type="DbPlatBuffer" version="1" />
        <entry name="ugcGroupIdList" type="DbPlatBuffer" version="1" />
        <entry name="rankList" type="DbPlatBuffer" version="1" />
        <entry name="occupancyValue" type="int32" version="1" />
        <entry name="propertyScore" type="int32" version="1" />
        <entry name="actorCount" type="int32" version="1" />
        <entry name="ugcAchievement" type="DbPlatBuffer" version="379" />
        <entry name="wxRecord" type="DbPlatBuffer" version="382" />
        <entry name="qqRecord" type="DbPlatBuffer" version="382" />
        <entry name="versionPassRecordData" type="DbPlatBuffer" version="391" />
        <entry name="evaluationStatus" type="int32" version="394" />
        <entry name="extraConfigIndexMap" type="DbPlatBuffer" version="397" />
        <entry name="publishEditors" type="DbPlatBuffer" version="400" />
        <entry name="mapLoading" type="DbPlatBuffer" version="401" />
        <entry name="lobbyCover" type="DbPlatBuffer" version="401" />
        <entry name="publishParam" type="DbPlatBuffer" version="412" />
        <entry name="isAdvert" type="int32" version="424" />
        <entry name="advertStatus" type="int32" version="424" />
        <index name="index_type" column="ugcId" />
    </struct>

    <struct name="House" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="HouseAttr" type="DbPlatBuffer" version="1" />
        <entry name="ChangedHouseAttr" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="Village" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="VillageAttr" type="DbPlatBufferLarge" version="1" />
        <entry name="ChangedVillageAttr" type="DbPlatBufferLarge" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="HouseLayoutTable" version="1" primarykey="layoutId" splittablekey="layoutId" customattr2="TableType=GENERIC">
        <entry name="layoutId" type="int64" version="1" />
        <entry name="layoutInfo" type="DbPlatBuffer" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="saveTime" type="uint64" version="1" />
        <index name="index_type" column="layoutId" />
    </struct>

    <struct name="UgcBlobDataTable" version="1" primarykey="ugcId,dataType" splittablekey="ugcId,dataType" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="dataType" type="int32" version="1" />
        <entry name="blobData" type="string" size="1024" version="1" />
        <index name="index_type" column="ugcId,dataType" />
    </struct>

    <struct name="PlayerExtraInfoTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="albumInfo" type="DbPlatBuffer" version="1" />
        <entry name="albumLike" type="DbPlatBuffer" version="383" />
        <entry name="albumExtInfo" type="DbPlatBuffer" version="401" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="UgcAchievementTable" version="1" primarykey="ugcId,achId,verId" splittablekey="ugcId" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="achId" type="int32" version="1" />
        <entry name="verId" type="int32" version="1" />
        <entry name="conf" type="DbPlatBuffer" version="1" />
        <index name="index1" column="ugcId,achId" />
        <index name="index2" column="ugcId,verId" />
    </struct>

    <struct name="TradingCardHistoryTable" version="1" primarykey="uid,collectionId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="collectionId" type="int32" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="TradingCardTradeTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="TradingCardInteractionTable" version="1" primarykey="id,destUid" splittablekey="destUid" customattr2="TableType=GENERIC">
        <entry name="id" type="int64" version="1" />
        <entry name="destUid" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
        <index name="index1" column="destUid" />
    </struct>

    <struct name="TradingCardTradeRecordTable" version="1" primarykey="uid,collectionId,tradeType" splittablekey="uid" customattr2="TableType=LIST;ListNum=100">
        <entry name="uid" type="int64" version="1" />
        <entry name="collectionId" type="int32" version="1" />
        <entry name="tradeType" type="int32" version="1" />
        <entry name="tradeId" type="int64" version="1" />
        <entry name="storeInfo" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="UgcShowDataStoreTable" version="1" primarykey="ugcId,uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="ugcId" type="int64" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="createTime" type="int64" version="1" />
        <entry name="achWrapper" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="PlayerEstimationTable" version="1" primarykey="uid,levelId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="levelId" type="int32" version="1" />
        <entry name="EstimationOptions" type="int32" version="1" />
        <entry name="OptionsTextArray" type="string" size="1024" version="1" />
        <entry name="SelfEstimationText" type="string" size="1024" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="CocPlayer" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="cocUserAttr" type="DbPlatBuffer" version="1" />
        <entry name="changedCocUserAttr" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="UgcMapExtraConfigTable" version="1" primarykey="uniqueId,cfgType,verId,cfgId" splittablekey="uniqueId" customattr2="TableType=GENERIC">
        <entry name="uniqueId" type="int64" version="1" />
        <entry name="cfgType" type="int32" version="1" />
        <entry name="cfgId" type="int32" version="1" />
        <entry name="verId" type="int32" version="1" />
        <entry name="wrapper" type="DbPlatBuffer" version="1" />
        <index name="index1" column="uniqueId,cfgType" />
        <index name="index2" column="uniqueId,verId" />
    </struct>

    <struct name="SeasonReviewTable" version="1" primarykey="uid,seasonId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="seasonId" type="int32" version="1" />
        <entry name="basicData" type="DbPlatBuffer" version="1" />
        <entry name="eventData" type="DbPlatBuffer" version="1" />
        <entry name="updateTimeMs" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="SeasonReviewTLogTable" version="1" primarykey="uid,seasonId" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="seasonId" type="int32" version="1" />
        <entry name="manorVisitMostFriendUid" type="int64" version="1" />
        <entry name="socialGiftSendCnt" type="int64" version="1" />
        <entry name="socialTeamMostFriendUid" type="int64" version="1" />
        <entry name="farmLevel" type="int64" version="1" />
        <entry name="farmMaxBalance" type="int64" version="1" />
        <entry name="farmVisitCnt" type="int64" version="1" />
        <entry name="farmMostHarvestCropId" type="int64" version="1" />
        <entry name="farmCoinReduce" type="int64" version="1" />
        <entry name="farmCoinAdd" type="int64" version="1" />
        <entry name="farmCommonHarvestCnt" type="int64" version="1" />
        <entry name="farmSmallHarvestCnt" type="int64" version="1" />
        <entry name="farmGreatHarvestCnt" type="int64" version="1" />
        <entry name="farmStealCnt" type="int64" version="1" />
        <entry name="farmActDays" type="int64" version="1" />
        <entry name="ugcCreatorLevelTitle" type="int64" version="1" />
        <entry name="ugcGoldTrophyCnt" type="int64" version="1" />
        <entry name="ugcSilverTrophyCnt" type="int64" version="1" />
        <entry name="ugcCopperTrophyCnt" type="int64" version="1" />
        <entry name="ugcAddSubscribeCnt" type="int64" version="1" />
        <entry name="ugcAddFansCnt" type="int64" version="1" />
        <entry name="ugcAddLikeCnt" type="int64" version="1" />
        <entry name="ugcParadeScore" type="int64" version="1" />
        <entry name="ugcEnjoyMapCnt" type="int64" version="1" />
        <entry name="ugcReleaseMapCnt" type="int64" version="1" />
        <entry name="ugcPassMapCnt" type="int64" version="1" />
        <entry name="ugcMostPlayerReleaseMapId" type="int64" version="1" />
        <entry name="ugcMostPlayerEnjoyMapId" type="int64" version="1" />
        <entry name="ugcMostEnjoyMapId" type="int64" version="1" />
        <entry name="updateTimeMs" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="ShareGiftPlayerTable" version="1" primarykey="id" splittablekey="id" customattr2="TableType=GENERIC">
        <entry name="id" type="int64" version="1" />
        <entry name="data" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="ShareGiftPlayerHistoryTable" version="1" primarykey="uid,type,historyType,shareGiftUid,createMs" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="type" type="int32" version="1" />
        <entry name="historyType" type="int32" version="1" />
        <entry name="shareGiftUid" type="int64" version="1" />
        <entry name="createMs" type="int64" version="1" />
        <entry name="showExpireTimeMs" type="int64" version="1" />
        <entry name="dbExpireTimeMs" type="int64" version="1" />
        <index name="index1" column="uid,type,historyType" />
    </struct>

    <struct name="DiscoverClickedFoundTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="flag" type="int32" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="OrnamentCustomDataTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="ornamentCustomData" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="AiNpcTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="aiNpcAttr" type="DbPlatBuffer" version="1" />
        <entry name="instanceId" type="int32" version="1" />
        <entry name="instanceLease" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="AiNpcChatHistoryTable" version="1" primarykey="uid,sessionId" splittablekey="uid" customattr2="TableType=LIST;ListNum=500">
        <entry name="uid" type="int64" version="1" />
        <entry name="sessionId" type="int64" version="1" />
        <entry name="chatInfo" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="AccountTransferTaskTable" version="1" primarykey="openId" splittablekey="openId" customattr2="TableType=GENERIC">
        <entry name="openId" type="string" size="1024" version="1" />
        <entry name="uid" type="int64" version="1" />
        <entry name="state" type="int32" version="1" />
        <entry name="transferAccountInfoArray" type="DbPlatBuffer" version="1" />
        <entry name="transferStage" type="int32" version="1" />
        <entry name="transferTime" type="int64" version="1" />
        <entry name="transferStartTime" type="int64" version="1" />
        <entry name="transferCompleteTime" type="int64" version="1" />
        <entry name="svrId" type="int32" version="1" />
        <index name="index1" column="openId" />
    </struct>

    <struct name="AccountTransferBackUpTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="backUpTime" type="int64" version="1" />
        <entry name="openIdToUid" type="DbPlatBuffer" version="1" />
        <entry name="openIdToCreatorId" type="DbPlatBuffer" version="1" />
        <entry name="playerActivityInteractionData" type="DbPlatBuffer" version="1" />
        <entry name="player" type="DbPlatBuffer" version="418" />
        <entry name="playerPublic" type="DbPlatBuffer" version="418" />
        <entry name="idipBanInfo" type="DbPlatBuffer" version="418" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="ChaseIdentityStatisticsTable" version="1" primarykey="identityId,qualifyId,degreeId" splittablekey="identityId" customattr2="TableType=GENERIC">
        <entry name="identityId" type="int32" version="1" />
        <entry name="qualifyId" type="int32" version="1" />
        <entry name="degreeId" type="int32" version="1" />
        <entry name="updateTimeMs" type="int64" version="1" />
        <entry name="topPercentileList" type="string" size="1024" version="1" />
        <index name="index_identityId_qualify_degree" column="identityId,qualifyId,degreeId" />
    </struct>

    <struct name="UgcCreatorBadgeTable" version="1" primarykey="creatorId,badgeId" splittablekey="creatorId" customattr2="TableType=GENERIC">
        <entry name="creatorId" type="int64" version="1" />
        <entry name="badgeId" type="int32" version="1" />
        <entry name="addTime" type="int64" version="1" />
        <index name="index_type" column="creatorId" />
    </struct>

    <struct name="AnalyzeDataTable" version="1" primarykey="dataKey,module" splittablekey="dataKey" customattr2="TableType=GENERIC">
        <entry name="dataKey" type="string" size="1024" version="1" />
        <entry name="module" type="string" size="1024" version="1" />
        <entry name="dataJson" type="string" size="128000" version="1" />
        <entry name="updateTime" type="int64" version="1" />
        <entry name="protoData" type="DbPlatBuffer" version="413" />
        <index name="index_type" column="dataKey" />
    </struct>

    <struct name="FarmDailyAwardsBuyCountTable" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="itemId" type="int64" version="1" />
        <entry name="nums" type="int32" version="1" />
        <entry name="farmDailyAwardsBuyCount" type="int32" version="1" />
        <entry name="updateTimeMs" type="int64" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="Cook" version="1" primarykey="uid" splittablekey="uid" customattr2="TableType=GENERIC">
        <entry name="uid" type="int64" version="1" />
        <entry name="CookAttr" type="DbPlatBuffer" version="1" />
        <entry name="ChangedCookAttr" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="uid" />
    </struct>

    <struct name="FBXInfoTable" version="1" primarykey="fbxId" splittablekey="fbxId" customattr2="TableType=GENERIC">
        <entry name="fbxId" type="int64" version="1" />
        <entry name="md5" type="string" size="1024" version="1" />
        <entry name="creatorId" type="int64" version="1" />
        <entry name="param" type="string" size="1024" version="1" />
        <entry name="bucket" type="string" size="1024" version="1" />
        <entry name="infos" type="DbPlatBuffer" version="1" />
        <index name="index_type" column="fbxId" />
    </struct>

    <struct name="ChangePlatMidasDiff" version="1" primarykey="Openid,PlatId" splittablekey="Openid" customattr2="TableType=GENERIC">
        <entry name="Openid" type="string" size="1024" version="1" />
        <entry name="PlatId" type="int32" version="1" />
        <entry name="diffInfo" type="DbPlatBuffer" version="1" />
        <entry name="money" type="DbPlatBuffer" version="419" />
        <index name="index1" column="Openid" />
        <index name="index2" column="Openid,PlatId" />
    </struct>

    <struct name="ChaseIdentityBattlePerformance" version="1" primarykey="Uid" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="battlePerformance" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="ModuleIdentityBattlePerformance" version="1" primarykey="Uid,ModuleType" splittablekey="Uid" customattr2="TableType=GENERIC">
        <entry name="Uid" type="int64" version="1" />
        <entry name="ModuleType" type="int32" version="1" />
        <entry name="chestBattlePerformance" type="DbPlatBuffer" version="1" />
    </struct>

    <struct name="OnlineEarningOpRecordTable" version="1" primarykey="uid,opType,pageId" splittablekey="uid" customattr2="TableType=LIST;ListNum=200">
        <entry name="uid" type="int64" version="1" />
        <entry name="opType" type="int32" version="1" />
        <entry name="pageId" type="int64" version="1" />
        <entry name="opInfo" type="DbPlatBuffer" version="1" />
        <entry name="uuid" type="int64" version="429" />
    </struct>

</metalib>
-- This file is automatically generated

local TableMap = {
	UGCAIMagicGraphTable = {
		UGCAIMagicGraphDataSubTables = {
			"UGCAIMagicGraphConf",
		},
	},
	UGCAIModuleTable = {
		UGCAITypeSectionDataSubTables = {
			"UGCAITypeSectionConf",
		},
	},
	UGCEditorTable = {
		UGCCharacteristicsInfoSubTables = {
			"UGCCharacteristicsInfo",
		},
		UGCEditorBagTabSubTables = {
			"UGCEditorPrefabTab",
		},
		UGCEditorSignalInfoSubTables = {
			"UGCEditorSignalInfo",
		},
		UGCUIEditorBagInfo_BtnSubTables = {
			"UGCUIEditorBagInfo_Btn",
		},
		UGCUIEditorBagInfo_FunctionsSubTables = {
			"UGCUIEditorBagInfo_Functions",
		},
		UGCUIEditorBagInfo_IMGSubTables = {
			"UGCUIEditorBagInfo_IMG",
		},
		UGCUIEditorBagInfo_TEMSubTables = {
			"UGCUIEditorBagInfo_TEM",
		},
		UGCUIEditorBagInfo_TXTSubTables = {
			"UGCUIEditorBagInfo_TXT",
		},
		UGCUIEditorBagInfo_UITEMSubTables = {
			"UGCUIEditorBagInfo_UITEM",
		},
	},
	UGCOfiicialAnimTable = {
		UGCOfiicialAnimSubTables = {
			"ResUGCOfiicialAnim",
		},
	},
	UGCWeaponSkinIPTable = {
		UGCWeaponSkinConfSubTables = {
			"UGCWeaponSkinConf",
		},
	},
	UgcImageDisplayTable = {
		UgcImageDisplayDataSubTables = {
			"UgcImageDisplayData",
		},
	},
	UgcMapTable = {
		UgcMapAwardTagConfSubTables = {
			"UgcMapAwardTagConf",
		},
	},
	UgcMgrTable = {
		UgcAiAnicapWhiteListConfigSubTables = {
			"UgcAiAnicapWhiteListConfig",
		},
		UgcAiAnswerWhiteListConfigSubTables = {
			"UgcAiAnswerWhiteListConfig",
		},
		UgcAiImageWhiteListConfigSubTables = {
			"UgcAiImageWhiteListConfig",
		},
		UgcAiVoiceWhiteListConfigSubTables = {
			"UgcAiVoiceWhiteListConfig",
		},
		UgcBucketConfigSubTables = {
			"UgcBucketConfig",
		},
		UgcBuyGoodsCreatorListConfigSubTables = {
			"UgcBuyGoodsCreatorListConfig",
		},
		UgcCoCreateWhiteListConfigSubTables = {
			"UgcCoCreateWhiteListConfig",
		},
		UgcCreateChatGroupWhiteListConfigSubTables = {
			"UgcCreateChatGroupWhiteListConfig",
		},
		UgcCustomSkeletonAnimWhiteListConfigSubTables = {
			"UgcCustomSkeletonAnimWhiteListConfig",
		},
		UgcDialogueImageWhiteListConfigSubTables = {
			"UgcDialogueImageWhiteListConfig",
		},
		UgcRouteInsConfigSubTables = {
			"UgcRouteInsConfig",
		},
		UgcRoutePolarisInsConfigSubTables = {
			"UgcRoutePolarisInsConfig",
		},
		UgcSkillEditorWhiteListConfigSubTables = {
			"UgcSkillEditorWhiteListConfig",
		},
		UgcUploadConfigSubTables = {
			"UgcUploadConfig",
		},
		UgcVisualProgramWhiteListConfigSubTables = {
			"UgcVisualProgramWhiteListConfig",
		},
		UgcWhiteListConfigSubTables = {
			"UgcWhiteListConfig",
		},
	},
	UgcNewYearTable = {
		UgcNewYearCollectionConfSubTables = {
			"UgcNewYearCollectionConfData",
		},
		UgcNewYearConfSubTables = {
			"UgcNewYearConfData",
		},
	},
	XiaowoFarmingTable = {
		FarmingConfigSubTables = {
			"FarmingConfig",
		},
		FarmingHandBookConfigSubTables = {
			"FarmingHandBookConfig",
		},
		FarmingLevelConfigSubTables = {
			"FarmingLevelConfig",
		},
	},
	XiaowoPianoTable = {
		XiaowoGuzhengConfigSubTables = {
			"XiaowoGuzhengConfig",
		},
		XiaowoPianoConfigSubTables = {
			"XiaowoPianoConfig",
		},
		XiaowoSuoNaConfigSubTables = {
			"XiaowoSuoNaConfig",
		},
	},
}

return TableMap

--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_FarmingConfig"] = {
        TableKey = "cropType",
        SubTableNames = {
			"FarmingConfig",
        }
    },
    ["table_FarmingHandBookConfig"] = {
        TableKey = "cropType",
        SubTableNames = {
			"FarmingHandBookConfig",
        }
    },
    ["table_FarmingLevelConfig"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmingLevelConfig",
        }
    },
    ["table_UGCOfiicialAnim"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUGCOfiicialAnim",
        }
    },
    ["table_UGCAIMagicGraphData"] = {
        TableKey = "Id",
        SubTableNames = {
			"UGCAIMagicGraphConf",
        }
    },
    ["table_UGCAITypeSectionData"] = {
        TableKey = "SectionID",
        SubTableNames = {
			"UGCAITypeSectionConf",
        }
    },
    ["table_UGCCharacteristicsInfo"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCCharacteristicsInfo",
        }
    },
    ["table_UGCEditorBagTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorPrefabTab",
        }
    },
    ["table_UGCEditorSignalInfo"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCEditorSignalInfo",
        }
    },
    ["table_UGCUIEditorBagInfo_Btn"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_Btn",
        }
    },
    ["table_UGCUIEditorBagInfo_Functions"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_Functions",
        }
    },
    ["table_UGCUIEditorBagInfo_IMG"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_IMG",
        }
    },
    ["table_UGCUIEditorBagInfo_TEM"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_TEM",
        }
    },
    ["table_UGCUIEditorBagInfo_TXT"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_TXT",
        }
    },
    ["table_UGCUIEditorBagInfo_UITEM"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_UITEM",
        }
    },
    ["table_UGCWeaponSkinConf"] = {
        TableKey = "skinId",
        SubTableNames = {
			"UGCWeaponSkinConf",
        }
    },
    ["table_UgcAiAnicapWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcAiAnicapWhiteListConfig",
        }
    },
    ["table_UgcAiAnswerWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcAiAnswerWhiteListConfig",
        }
    },
    ["table_UgcAiImageWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcAiImageWhiteListConfig",
        }
    },
    ["table_UgcAiVoiceWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcAiVoiceWhiteListConfig",
        }
    },
    ["table_UgcBuyGoodsCreatorListConfig"] = {
        TableKey = "creatorId",
        SubTableNames = {
			"UgcBuyGoodsCreatorListConfig",
        }
    },
    ["table_UgcCoCreateWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCoCreateWhiteListConfig",
        }
    },
    ["table_UgcCreateChatGroupWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCreateChatGroupWhiteListConfig",
        }
    },
    ["table_UgcCustomSkeletonAnimWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCustomSkeletonAnimWhiteListConfig",
        }
    },
    ["table_UgcDialogueImageWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcDialogueImageWhiteListConfig",
        }
    },
    ["table_UgcImageDisplayData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcImageDisplayData",
        }
    },
    ["table_UgcMapAwardTagConf"] = {
        TableKey = "tagId",
        SubTableNames = {
			"UgcMapAwardTagConf",
        }
    },
    ["table_UgcNewYearCollectionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcNewYearCollectionConfData",
        }
    },
    ["table_UgcNewYearConf"] = {
        TableKey = "tabId",
        SubTableNames = {
			"UgcNewYearConfData",
        }
    },
    ["table_UgcSkillEditorWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcSkillEditorWhiteListConfig",
        }
    },
    ["table_UgcVisualProgramWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcVisualProgramWhiteListConfig",
        }
    },
    ["table_UgcWhiteListConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcWhiteListConfig",
        }
    },
    ["table_XiaowoGuzhengConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"XiaowoGuzhengConfig",
        }
    },
    ["table_XiaowoPianoConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"XiaowoPianoConfig",
        }
    },
    ["table_XiaowoSuoNaConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"XiaowoSuoNaConfig",
        }
    },
}

return TableHelperTables
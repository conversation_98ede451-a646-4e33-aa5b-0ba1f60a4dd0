--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_ChaseSituationScoreConfig_SingleBoss"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseSituationScoreConfig_SingleBoss",
        }
    },
    ["table_ChaseSituationScoreConfig_ThreeBoss"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseSituationScoreConfig_ThreeBoss",
        }
    },
    ["table_ChaseSituationScoreTimeConfig_SingleBoss"] = {
        TableKey = "time",
        SubTableNames = {
			"ChaseSituationScoreTimeConfig_SingleBoss",
        }
    },
    ["table_ChaseSituationScoreTimeConfig_ThreeBoss"] = {
        TableKey = "time",
        SubTableNames = {
			"ChaseSituationScoreTimeConfig_ThreeBoss",
        }
    },
    ["table_MatchDynamicTeamRobotsData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchDynamicTeamRobotsData_chase",
        }
    },
    ["table_MatchRuleRangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRuleRangeData_Chase",
        }
    },
    ["table_MatchWarmScoreData_Chase"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchWarmScoreData_Chase",
        }
    },
    ["table_ProtectedScoreAdditionalData"] = {
        TableKey = "id",
        SubTableNames = {
			"ProtectedScoreAdditionalData_chase_dawangbiezhuawo",
        }
    },
    ["table_QualifyingIntegralData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTQualifyingIntegralData_chase_dawangbiezhuawo",
        }
    },
    ["table_PlaceableActorConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SceneFlowData_chase",
        }
    },
    ["table_StepGuideData"] = {
        TableKey = "StepID",
        SubTableNames = {
			"StepGuideData_chase",
        }
    },
}

return TableHelperTables
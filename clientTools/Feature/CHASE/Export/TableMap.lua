-- This file is automatically generated

local TableMap = {
	ChaseSituationScoreTable = {
		ChaseSituationScoreConfig_SingleBossSubTables = {
			"ChaseSituationScoreConfig_SingleBoss",
		},
		ChaseSituationScoreConfig_ThreeBossSubTables = {
			"ChaseSituationScoreConfig_ThreeBoss",
		},
		ChaseSituationScoreTimeConfig_SingleBossSubTables = {
			"ChaseSituationScoreTimeConfig_SingleBoss",
		},
		ChaseSituationScoreTimeConfig_ThreeBossSubTables = {
			"ChaseSituationScoreTimeConfig_ThreeBoss",
		},
	},
	MatchTable = {
		MatchDynamicTeamRobotsDataSubTables = {
			"MatchDynamicTeamRobotsData_chase",
		},
		MatchRuleRangeDataSubTables = {
			"MatchRuleRangeData_Chase",
		},
	},
	MatchWarmScoreTable = {
		MatchWarmScoreData_ChaseSubTables = {
			"MatchWarmScoreData_Chase",
		},
	},
	PlayerGuideTable = {
		StepGuideDataSubTables = {
			"StepGuideData_chase",
		},
	},
	ProtectedScoreTable = {
		ProtectedScoreAdditionalDataSubTables = {
			"ProtectedScoreAdditionalData_chase_dawangbiezhuawo",
		},
	},
	QualifyingTable = {
		QualifyingIntegralDataSubTables = {
			"QDTQualifyingIntegralData_chase_dawangbiezhuawo",
		},
	},
	SceneFlowDataTable = {
		PlaceableActorConfigDataSubTables = {
			"SceneFlowData_chase",
		},
	},
}

return TableMap

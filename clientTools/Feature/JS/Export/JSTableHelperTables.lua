--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_JSFallBehindConf"] = {
        TableKey = "Key",
        SubTableNames = {
			"JSFallBehindConfForBike",
			"JSFallBehindConfForSkate",
        }
    },
    ["table_ResJSPropConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ResJSPropConf",
        }
    },
    ["table_ResJSPropMiscConf"] = {
        TableKey = "Key",
        SubTableNames = {
			"ResJSPropMiscConf",
        }
    },
    ["table_ResJSPropRankRangeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ResJSPropRankRangeConf",
        }
    },
    ["table_ResJSPropsGenWeightConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ResJSPropsGenWeightConf",
        }
    },
    ["table_PlaceableActorConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SceneFlowData_JS",
        }
    },
}

return TableHelperTables
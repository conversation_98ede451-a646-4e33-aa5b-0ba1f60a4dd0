-- This file is automatically generated

local TableMap = {
	OMDLevelInfoTable = {
		OMDGameTypeDataSubTables = {
			"OMDGameTypeData",
		},
		OMDLevelInfoDataSubTables = {
			"OMDLevelInfoData",
		},
	},
	OMDLevelMonstersTable = {
		OMDMonsterMessageConfSubTables = {
			"OMDLevelMonsterMessageConfData",
		},
		OMDLevelMonstersConfSubTables = {
			"OMDLevelMonstersConfData",
		},
	},
	OMDLevelRulesTable = {
		OMDLevelRuleDataSubTables = {
			"OMDLevelRuleData",
		},
	},
	OMDMiscTable = {
		OMDMiscConfDataSubTables = {
			"OMDMiscConfData",
		},
	},
	OMDMonsterTable = {
		OMDMonsterDataSubTables = {
			"OMDMonsterData",
		},
		OMDMonsterPoolDataSubTables = {
			"OMDMonsterPoolData",
		},
		OMDMonsterWaveDataSubTables = {
			"OMDMonsterWaveData",
		},
		OMDWeaponPoolDataSubTables = {
			"OMDWeaponPoolData",
		},
	},
	OMDOrcpediaTable = {
		OMDOrcpediaConfSubTables = {
			"OMDOrcpediaConf",
		},
	},
	OMDPropsTable = {
		OMDInitialPropsConfSubTables = {
			"OMDInitialPropsConfData",
		},
		OMDPropsConfSubTables = {
			"OMDPropsConfData",
		},
		OMDPropsLevelConfSubTables = {
			"OMDPropsLevelConfData",
		},
	},
	OMDRatingTable = {
		OMDRatingConfSubTables = {
			"OMDRatingConf",
		},
		OMDScoreToEvaluationConfSubTables = {
			"OMDScoreToEvaluationConf",
		},
	},
	OMDStoreTable = {
		OMDStoreConfSubTables = {
			"OMDStoreConf",
		},
	},
	OMDSupplyTable = {
		OMDSupplySubTables = {
			"OMDSupply",
		},
	},
	OMDWeaponTable = {
		OMDWeaponClientConfSubTables = {
			"OMDWeaponClientConfData",
		},
	},
	OMDWeaponAttrTable = {
		OMDWeaponAttrConfSubTables = {
			"OMDWeaponAttrConfData",
		},
	},
	OMDWeaponSkinTable = {
		OMDWeaponSkinConfSubTables = {
			"OMDWeaponSkinData",
		},
	},
	OMDWeaponSuitSkinTable = {
		OMDWeaponSuitSkinConfSubTables = {
			"OMDWeaponSuitSkinData",
		},
	},
	OMDWeaponUpgradeTable = {
		OMDWeaponUpgradeConfSubTables = {
			"OMDWeaponUpgradeConfData",
		},
	},
	PlayerGuideTable = {
		StepGuideDataSubTables = {
			"StepGuideData_omd",
		},
	},
}

return TableMap

--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_OMDGameTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDGameTypeData",
        }
    },
    ["table_OMDInitialPropsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDInitialPropsConfData",
        }
    },
    ["table_OMDLevelInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDLevelInfoData",
        }
    },
    ["table_OMDMonsterMessageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDLevelMonsterMessageConfData",
        }
    },
    ["table_OMDLevelMonstersConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDLevelMonstersConfData",
        }
    },
    ["table_OMDLevelRuleData"] = {
        TableKey = "ruleId",
        SubTableNames = {
			"OMDLevelRuleData",
        }
    },
    ["table_OMDMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDMiscConfData",
        }
    },
    ["table_OMDMonsterData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDMonsterData",
        }
    },
    ["table_OMDMonsterPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDMonsterPoolData",
        }
    },
    ["table_OMDMonsterWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDMonsterWaveData",
        }
    },
    ["table_OMDOrcpediaConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDOrcpediaConf",
        }
    },
    ["table_OMDPropsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDPropsConfData",
        }
    },
    ["table_OMDPropsLevelConf"] = {
        TableKey = "level,id",
        SubTableNames = {
			"OMDPropsLevelConfData",
        }
    },
    ["table_OMDRatingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDRatingConf",
        }
    },
    ["table_OMDScoreToEvaluationConf"] = {
        TableKey = "ScoreLeftLimit",
        SubTableNames = {
			"OMDScoreToEvaluationConf",
        }
    },
    ["table_OMDStoreConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDStoreConf",
        }
    },
    ["table_OMDSupply"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDSupply",
        }
    },
    ["table_OMDWeaponAttrConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponAttrConfData",
        }
    },
    ["table_OMDWeaponClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponClientConfData",
        }
    },
    ["table_OMDWeaponPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponPoolData",
        }
    },
    ["table_OMDWeaponSkinConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponSkinData",
        }
    },
    ["table_OMDWeaponSuitSkinConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponSuitSkinData",
        }
    },
    ["table_OMDWeaponUpgradeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"OMDWeaponUpgradeConfData",
        }
    },
    ["table_StepGuideData"] = {
        TableKey = "StepID",
        SubTableNames = {
			"StepGuideData_omd",
        }
    },
}

return TableHelperTables
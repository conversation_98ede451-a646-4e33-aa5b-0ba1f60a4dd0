-- This file is automatically generated

local TableMap = {
	NR3E8AIBoardTable = {
		NR3E8AIBoardDataSubTables = {
			"NR3E8AIBoardData",
		},
	},
	NR3E8ActivityEventTable = {
		NR3E8ActivityEventDataSubTables = {
			"NR3E8ActivityEventData",
		},
	},
	NR3E8BackpackItemTable = {
		NR3E8BackpackItemSubTables = {
			"NR3E8BackpackItemData_building",
			"NR3E8BackpackItemData_common",
			"NR3E8BackpackItemData_decorate_dice",
			"NR3E8BackpackItemData_decorate_shield",
			"NR3E8BackpackItemData_decorate_theme",
			"NR3E8BackpackItemData_package",
			"NR3E8BackpackItemData_skillCard",
			"NR3E8BackpackItemData_treasure",
		},
	},
	NR3E8BuffTable = {
		NR3E8BuffDataSubTables = {
			"NR3E8BuffData",
		},
		NR3E8BuffEffectDataSubTables = {
			"NR3E8BuffEffectData",
		},
	},
	NR3E8BuildingTable = {
		NR3E8BuildingDataSubTables = {
			"NR3E8BuildingData",
		},
	},
	NR3E8BuildingBuffTable = {
		NR3E8BuildingBuffDataSubTables = {
			"NR3E8BuildingBuffData",
		},
	},
	NR3E8BuildingCombineTable = {
		NR3E8BuildingCombineDataSubTables = {
			"NR3E8BuildingCombineData",
		},
	},
	NR3E8BuildingPlanTable = {
		NR3E8BuildingPlanDataSubTables = {
			"NR3E8BuildingPlanData",
		},
	},
	NR3E8BuildingPlanTypeTable = {
		NR3E8BuildingPlanTypeDataSubTables = {
			"NR3E8BuildingPlanTypeData",
		},
	},
	NR3E8BuildingUpgradeLvTable = {
		NR3E8BuildingUpgradeLvDataSubTables = {
			"NR3E8BuildingUpgradeLvData",
		},
	},
	NR3E8BuildingUpgradeStarTable = {
		NR3E8BuildingUpgradeStarDataSubTables = {
			"NR3E8BuildingUpgradeStarData",
		},
	},
	NR3E8ChoiceEventTable = {
		NR3E8ChoiceEventBuffDataSubTables = {
			"NR3E8ChoiceEventBuffData",
		},
		NR3E8ChoiceEventMoveDataSubTables = {
			"NR3E8ChoiceEventMoveData",
		},
	},
	NR3E8CityTable = {
		NR3E8CityDataSubTables = {
			"NR3E8CityData",
		},
	},
	NR3E8CitySubLevelTable = {
		NR3E8CitySubLevelDataSubTables = {
			"NR3E8CitySubLevelData",
		},
	},
	NR3E8DailyGiftTable = {
		NR3E8DailyGiftDataSubTables = {
			"NR3E8DailyGiftData",
		},
	},
	NR3E8DecorateTable = {
		NR3E8DecorateItemDataSubTables = {
			"NR3E8DecorateItemData_CityTheme",
			"NR3E8DecorateItemData_Dice",
			"NR3E8DecorateItemData_Shield",
		},
	},
	NR3E8DiceTable = {
		NR3E8DiceMaxDataSubTables = {
			"NR3E8DiceMaxData",
		},
		NR3E8DiceMultipleDataSubTables = {
			"NR3E8DiceMultipleData",
		},
		NR3E8DiceRandomDataSubTables = {
			"NR3E8DiceRandomData",
		},
		NR3E8DiceRecoverDataSubTables = {
			"NR3E8DiceRecoverData",
		},
		NR3E8DiceRewardMultipleDataSubTables = {
			"NR3E8DiceRewardMultipleData",
		},
	},
	NR3E8ForestTreasureGridTable = {
		NR3E8ForestTreasureGridDataSubTables = {
			"NR3E8ForestTreasureGridData",
		},
	},
	NR3E8ForestTreasureGridBitMapTable = {
		NR3E8ForestTreasureGridBitMapDataSubTables = {
			"NR3E8ForestTreasureGridBitMapData",
		},
	},
	NR3E8ForestTreasureItemInfoTable = {
		NR3E8ForestTreasureItemInfoDataSubTables = {
			"NR3E8ForestTreasureItemInfoData",
		},
	},
	NR3E8ForestTreasureSaveBitMapTable = {
		NR3E8ForestTreasureSaveBitMapDataSubTables = {
			"NR3E8ForestTreasureSaveBitMapData",
		},
	},
	NR3E8ForestTreasureTreasureBitMapTable = {
		NR3E8ForestTreasureTreasureBitMapDataSubTables = {
			"NR3E8ForestTreasureTreasureBitMapData",
		},
	},
	NR3E8FriendCardTable = {
		NR3E8FriendCardDataSubTables = {
			"NR3E8FriendCardData",
		},
	},
	NR3E8GameConfigTable = {
		NR3E8GameConfigDataSubTables = {
			"NR3E8GameConfig",
		},
	},
	NR3E8InternalJumpHandleTable = {
		NR3E8InternalJumpHandleDataSubTables = {
			"NR3E8InternalJumpHandleData",
		},
	},
	NR3E8MileStoneTable = {
		NR3E8MileStoneDataSubTables = {
			"NR3E8MileStoneData",
		},
	},
	NR3E8NewsEventTable = {
		NR3E8NewsEventDataSubTables = {
			"NR3E8NewsEventData",
		},
	},
	NR3E8ShieldTable = {
		NR3E8ShieldMaxDataSubTables = {
			"NR3E8ShieldMaxData",
		},
	},
	NR3E8SquareTable = {
		NR3E8SquareDataSubTables = {
			"NR3E8SquareData",
		},
	},
	NR3E8StealTable = {
		NR3E8StealDataSubTables = {
			"NR3E8StealData",
		},
	},
	NR3E8TaskTable = {
		NR3E8TaskDataSubTables = {
			"NR3E8TaskData",
		},
	},
	NR3E8VisitResourceTable = {
		NR3E8VisitResourceDataSubTables = {
			"NR3E8VisitResourceData",
			"NR3E8VisitResourceData_friend",
		},
	},
	NR3E8WeekActivityTable = {
		NR3E8WeekActivityDataSubTables = {
			"NR3E8WeekActivityData",
		},
	},
	TextLuaNR3E8Table = {
		TextEntryNR3E8DataSubTables = {
			"TextEntryNR3E8Data",
		},
	},
}

return TableMap

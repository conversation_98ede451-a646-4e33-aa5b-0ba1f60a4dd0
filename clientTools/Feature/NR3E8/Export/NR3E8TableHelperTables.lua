--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_NR3E8AIBoardData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8AIBoardData",
        }
    },
    ["table_NR3E8ActivityEventData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ActivityEventData",
        }
    },
    ["table_NR3E8BackpackItem"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BackpackItemData_building",
			"NR3E8BackpackItemData_common",
			"NR3E8BackpackItemData_decorate_dice",
			"NR3E8BackpackItemData_decorate_shield",
			"NR3E8BackpackItemData_decorate_theme",
			"NR3E8BackpackItemData_package",
			"NR3E8BackpackItemData_skillCard",
			"NR3E8BackpackItemData_treasure",
        }
    },
    ["table_NR3E8BuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuffData",
        }
    },
    ["table_NR3E8BuffEffectData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuffEffectData",
        }
    },
    ["table_NR3E8BuildingBuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingBuffData",
        }
    },
    ["table_NR3E8BuildingCombineData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingCombineData",
        }
    },
    ["table_NR3E8BuildingData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingData",
        }
    },
    ["table_NR3E8BuildingPlanData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingPlanData",
        }
    },
    ["table_NR3E8BuildingPlanTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingPlanTypeData",
        }
    },
    ["table_NR3E8BuildingUpgradeLvData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingUpgradeLvData",
        }
    },
    ["table_NR3E8BuildingUpgradeStarData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BuildingUpgradeStarData",
        }
    },
    ["table_NR3E8ChoiceEventBuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ChoiceEventBuffData",
        }
    },
    ["table_NR3E8ChoiceEventMoveData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ChoiceEventMoveData",
        }
    },
    ["table_NR3E8CityData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8CityData",
        }
    },
    ["table_NR3E8CitySubLevelData"] = {
        TableKey = "region",
        SubTableNames = {
			"NR3E8CitySubLevelData",
        }
    },
    ["table_NR3E8DailyGiftData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DailyGiftData",
        }
    },
    ["table_NR3E8DecorateItemData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DecorateItemData_CityTheme",
			"NR3E8DecorateItemData_Dice",
			"NR3E8DecorateItemData_Shield",
        }
    },
    ["table_NR3E8DiceMaxData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DiceMaxData",
        }
    },
    ["table_NR3E8DiceMultipleData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DiceMultipleData",
        }
    },
    ["table_NR3E8DiceRandomData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DiceRandomData",
        }
    },
    ["table_NR3E8DiceRecoverData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DiceRecoverData",
        }
    },
    ["table_NR3E8DiceRewardMultipleData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8DiceRewardMultipleData",
        }
    },
    ["table_NR3E8ForestTreasureGridBitMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ForestTreasureGridBitMapData",
        }
    },
    ["table_NR3E8ForestTreasureGridData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ForestTreasureGridData",
        }
    },
    ["table_NR3E8ForestTreasureItemInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ForestTreasureItemInfoData",
        }
    },
    ["table_NR3E8ForestTreasureSaveBitMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ForestTreasureSaveBitMapData",
        }
    },
    ["table_NR3E8ForestTreasureTreasureBitMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ForestTreasureTreasureBitMapData",
        }
    },
    ["table_NR3E8FriendCardData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8FriendCardData",
        }
    },
    ["table_NR3E8GameConfigData"] = {
        TableKey = "ConfigName",
        SubTableNames = {
			"NR3E8GameConfig",
        }
    },
    ["table_NR3E8InternalJumpHandleData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8InternalJumpHandleData",
        }
    },
    ["table_NR3E8MileStoneData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8MileStoneData",
        }
    },
    ["table_NR3E8NewsEventData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8NewsEventData",
        }
    },
    ["table_NR3E8ShieldMaxData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8ShieldMaxData",
        }
    },
    ["table_NR3E8SquareData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8SquareData",
        }
    },
    ["table_NR3E8StealData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8StealData",
        }
    },
    ["table_NR3E8TaskData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8TaskData",
        }
    },
    ["table_NR3E8VisitResourceData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8VisitResourceData",
			"NR3E8VisitResourceData_friend",
        }
    },
    ["table_NR3E8WeekActivityData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8WeekActivityData",
        }
    },
    ["table_TextEntryNR3E8Data"] = {
        TableKey = "id",
        SubTableNames = {
			"TextEntryNR3E8Data",
        }
    },
}

return TableHelperTables
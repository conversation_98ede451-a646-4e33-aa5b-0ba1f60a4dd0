-- This file is automatically generated

local PBPath = {
	Excel = {
		"GSReportTask_Chest.pb",
		"InLevelTaskInfo_Chest.pb",
		"NewGuideHelper_Chest.pb",
		"ResChestActivityBannerJumpStyle.pb",
		"ResChestAnimSkin.pb",
		"ResChestBackpackTag.pb",
		"ResChestBGMusicConfig.pb",
		"ResChestBossDecoration.pb",
		"ResChestBossSkinConfig.pb",
		"ResChestCameraSetting.pb",
		"ResChestCollection.pb",
		"ResChestComprehensiveScoreConfig.pb",
		"ResChestCustomConfig.pb",
		"ResChestExcelConfig.pb",
		"ResChestGameplayAbility.pb",
		"ResChestHealTeammates.pb",
		"ResChestHeroAbilityConfig.pb",
		"ResChestHitPrize.pb",
		"ResChestIDMastery.pb",
		"ResChestInLevelTarget.pb",
		"ResChestItem.pb",
		"ResChestMaxHP.pb",
		"ResChestMinimapPoint.pb",
		"ResChestMisc.pb",
		"ResChestNewbieAbTest.pb",
		"ResChestPersonalSettlement.pb",
		"ResChestPrizeRules.pb",
		"ResChestPropSkinConfig.pb",
		"ResChestScannableItemConfig.pb",
		"ResChestSituationScore.pb",
		"ResChestSkillPropConfig.pb",
		"ResChestSkinParticleConfig.pb",
		"ResChestSpeedUpToEnd.pb",
		"ResChestTagCountLimit.pb",
		"ResChestTalentConfig.pb",
		"ResMatchChestRule.pb",
	},
}

return PBPath
--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_DropConfig"] = {
        TableKey = "No",
        SubTableNames = {
			"DropConfig",
        }
    },
    ["table_MoneyTreeShakeDrop"] = {
        TableKey = "No",
        SubTableNames = {
			"MoneyTreeShakeDrop",
        }
    },
    ["table_MoneyTreeWaterDrop"] = {
        TableKey = "No",
        SubTableNames = {
			"MoneyTreeWaterDrop",
        }
    },
    ["table_PlantLevelConf"] = {
        TableKey = "Level",
        SubTableNames = {
			"PlantLevelConf",
        }
    },
    ["table_ShakeCostConf"] = {
        TableKey = "Times",
        SubTableNames = {
			"ShakeCostConf",
        }
    },
    ["table_XiaowoHotConf"] = {
        TableKey = "hotType",
        SubTableNames = {
			"XiaowoHotConf",
        }
    },
    ["table_XiaowoInitFurniture"] = {
        TableKey = "ID",
        SubTableNames = {
			"XiaowoInitFurniture",
        }
    },
}

return TableHelperTables
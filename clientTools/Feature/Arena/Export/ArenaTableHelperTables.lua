--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_ArenaABTestGroupConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaABTestGroupConf",
        }
    },
    ["table_ArenaAIConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaAIConfig",
        }
    },
    ["table_ArenaAIDynamicLevelChangeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaAIDynamicLevelChangeConfig",
        }
    },
    ["table_ArenaAttributeHOKOverride"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaAttributeHOKOverrideData",
        }
    },
    ["table_ArenaAvatarSkillConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaAvatarSkillConfData",
			"HOKAvatarSkillConfData",
        }
    },
    ["table_ArenaBuffAbnormalData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffAbnormalData",
        }
    },
    ["table_ArenaBuffAbnormalDisplayData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffAbnormalDisplayData",
        }
    },
    ["table_ArenaBuffAbnormalRelationDisplayData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffAbnormalRelationDisplayData",
        }
    },
    ["table_ArenaBuffAdvancedDisplayData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffAdvancedDisplayData",
        }
    },
    ["table_ArenaBuffData"] = {
        TableKey = "id,level,in_table_game_type",
        SubTableNames = {
			"ArenaBuffData",
			"ArenaBuffData_Card_Attr",
			"ArenaBuffData_Card_Function",
			"ArenaBuffData_DianWei",
			"ArenaBuffData_FinalMount",
			"ArenaBuffData_Mount",
			"ArenaBuffData_Prop",
			"ArenaBuffData_ShangGuanWanEr",
			"ArenaBuffData_XiangYu",
			"ArenaBuffData_ZhangLiang",
			"ArenaBuffData_ZhenJi",
			"ArenaBuffData_ailin",
			"ArenaBuffData_ake",
			"ArenaBuffData_anqila",
			"ArenaBuffData_bingyi",
			"ArenaBuffData_caiwenji",
			"ArenaBuffData_chengyaojin",
			"ArenaBuffData_daji",
			"ArenaBuffData_daqiao",
			"ArenaBuffData_diaochan",
			"ArenaBuffData_direnjie",
			"ArenaBuffData_dongfangyao",
			"ArenaBuffData_donghuangtaiyi",
			"ArenaBuffData_dunshan",
			"ArenaBuffData_duoliya",
			"ArenaBuffData_fashuzhaohuanshou_RandomEvent",
			"ArenaBuffData_gongbenwuzang",
			"ArenaBuffData_gongsunli",
			"ArenaBuffData_haiyue",
			"ArenaBuffData_hokBalanceBuff",
			"ArenaBuffData_houyi",
			"ArenaBuffData_houyi_RandomEvent",
			"ArenaBuffData_huamulan",
			"ArenaBuffData_jialuo",
			"ArenaBuffData_kai",
			"ArenaBuffData_kai_RandomEvent",
			"ArenaBuffData_lanlingwang",
			"ArenaBuffData_libai",
			"ArenaBuffData_liubei",
			"ArenaBuffData_liushan",
			"ArenaBuffData_lubanqihao",
			"ArenaBuffData_lubanqihao_RandomEvent",
			"ArenaBuffData_luopuxia",
			"ArenaBuffData_lvbu",
			"ArenaBuffData_makeboluo",
			"ArenaBuffData_maolingshaonv",
			"ArenaBuffData_mingshiyin",
			"ArenaBuffData_mozi",
			"ArenaBuffData_paopao",
			"ArenaBuffData_renzhe",
			"ArenaBuffData_summonerspells",
			"ArenaBuffData_sunce",
			"ArenaBuffData_sunshangxiang",
			"ArenaBuffData_sunwukong",
			"ArenaBuffData_test",
			"ArenaBuffData_tuteng_RandomEvent",
			"ArenaBuffData_wangzhaojun",
			"ArenaBuffData_wangzhaojun_RandomEvent",
			"ArenaBuffData_wulizhaohuanshou_RandomEvent",
			"ArenaBuffData_wusula",
			"ArenaBuffData_wuzetian",
			"ArenaBuffData_xiahoudun",
			"ArenaBuffData_xiaohonghu",
			"ArenaBuffData_xiaoqiao",
			"ArenaBuffData_xishi",
			"ArenaBuffData_yangyuhuan",
			"ArenaBuffData_yao",
			"ArenaBuffData_yase",
			"ArenaBuffData_yuji",
			"ArenaBuffData_yunying",
			"ArenaBuffData_yunzhongjun",
			"ArenaBuffData_zhaoyun",
			"ArenaBuffData_zhongkui",
			"ArenaBuffData_zhongkui_RandomEvent",
			"ArenaBuffData_zhugeliang",
			"BSBuffData_Prop",
			"HOKBuffData",
			"HOKBuffData_summoner",
			"HOKCardBuffData",
        }
    },
    ["table_ArenaBulletSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBulletSet",
			"HOKBulletSet",
        }
    },
    ["table_ArenaCardBSKillLootData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardBSKillLootData",
        }
    },
    ["table_ArenaCardCatgoryData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardCatgoryData",
        }
    },
    ["table_ArenaCardData"] = {
        TableKey = "id,inTableGameType",
        SubTableNames = {
			"ArenaCardData",
			"ArenaCardData_5v5",
        }
    },
    ["table_ArenaCardFilterData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardFilterData",
        }
    },
    ["table_ArenaCardFilterDataV3"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardFilterDataV3",
        }
    },
    ["table_ArenaCardHeroRandomGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardHeroRandomGroupData",
        }
    },
    ["table_ArenaCardJumpTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardJumpTextData",
        }
    },
    ["table_ArenaCardPackData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPackData",
        }
    },
    ["table_ArenaCardPackGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPackGroupData",
        }
    },
    ["table_ArenaCardPackRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPackRuleData",
        }
    },
    ["table_ArenaCardPackUpgradeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPackUpgradeData",
        }
    },
    ["table_ArenaCardPocketRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPocketRuleData",
        }
    },
    ["table_ArenaCardQualityData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardQualityData",
        }
    },
    ["table_ArenaCardRandomData"] = {
        TableKey = "key",
        SubTableNames = {
			"ArenaCardRandomData",
        }
    },
    ["table_ArenaCardRandomGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardRandomGroupData",
        }
    },
    ["table_ArenaCardRandomWeight"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardRandomWeight",
        }
    },
    ["table_ArenaCardResourceData"] = {
        TableKey = "cardId",
        SubTableNames = {
			"ArenaCardResourceData",
        }
    },
    ["table_ArenaCardShowingGroupData"] = {
        TableKey = "key",
        SubTableNames = {
			"ArenaCardShowingGroupData",
        }
    },
    ["table_ArenaCardShowingGroupDataV3"] = {
        TableKey = "key",
        SubTableNames = {
			"ArenaCardShowingGroupDataV3",
        }
    },
    ["table_ArenaCardSuitBuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardSuitBuffData",
        }
    },
    ["table_ArenaCardTagWeight"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardTagWeight",
        }
    },
    ["table_ArenaCombatEffectivenessAcceleration"] = {
        TableKey = "targetGroup",
        SubTableNames = {
			"ArenaCombatEffectivenessAccelerationData",
			"HOKCombatEffectivenessAccelerationData",
        }
    },
    ["table_ArenaCombatEffectivenessBadge"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCombatEffectivenessBadgeData",
			"HOKCombatEffectivenessBadgeData",
        }
    },
    ["table_ArenaCombatEffectivenessGroup"] = {
        TableKey = "group",
        SubTableNames = {
			"ArenaCombatEffectivenessGroupData",
        }
    },
    ["table_ArenaCombatEffectivenessHeroBattle"] = {
        TableKey = "heroBattleRank",
        SubTableNames = {
			"ArenaCombatEffectivenessHeroBattleData",
			"HOKCombatEffectivenessHeroBattleData",
        }
    },
    ["table_ArenaCombatEffectivenessHeroGlobal"] = {
        TableKey = "heroGlobalRank",
        SubTableNames = {
			"ArenaCombatEffectivenessHeroGlobalData",
			"HOKCombatEffectivenessHeroGlobalData",
        }
    },
    ["table_ArenaCombatEffectivenessLowAcceleration"] = {
        TableKey = "lowAccId",
        SubTableNames = {
			"ArenaCombatEffectivenessLowAccelerationData",
			"HOKCombatEffectivenessLowAccelerationData",
        }
    },
    ["table_ArenaCombatEffectivenessMisc"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCombatEffectivenessMiscData",
			"HOKCombatEffectivenessMiscData",
        }
    },
    ["table_ArenaCombatEffectivenessProtection"] = {
        TableKey = "protectionId",
        SubTableNames = {
			"ArenaCombatEffectivenessProtectionData",
			"HOKCombatEffectivenessProtectionData",
        }
    },
    ["table_ArenaCombatEffectivenessRank"] = {
        TableKey = "group",
        SubTableNames = {
			"ArenaCombatEffectivenessRankData",
			"HOKCombatEffectivenessRankData",
        }
    },
    ["table_ArenaCombatEffectivenessTeam"] = {
        TableKey = "teamBattleRank",
        SubTableNames = {
			"ArenaCombatEffectivenessTeamData",
			"HOKCombatEffectivenessTeamData",
        }
    },
    ["table_ArenaDamageEffect"] = {
        TableKey = "id,level,in_table_game_type",
        SubTableNames = {
			"ArenaDamageEffectData",
			"ArenaDamageEffectData_FinalMount",
			"ArenaDamageEffectData_Hero",
			"ArenaDamageEffectData_Mount",
			"ArenaDamageEffectData_ShangGuanWanEr",
			"ArenaDamageEffectData_ailin",
			"ArenaDamageEffectData_ake",
			"ArenaDamageEffectData_anqila",
			"ArenaDamageEffectData_bingyi",
			"ArenaDamageEffectData_caiwenji",
			"ArenaDamageEffectData_chengyaojin",
			"ArenaDamageEffectData_daji",
			"ArenaDamageEffectData_daqiao",
			"ArenaDamageEffectData_dianwei",
			"ArenaDamageEffectData_diaochan",
			"ArenaDamageEffectData_direnjie",
			"ArenaDamageEffectData_dongfangyao",
			"ArenaDamageEffectData_donghuangtaiyi",
			"ArenaDamageEffectData_duoliya",
			"ArenaDamageEffectData_fashuzhaohuanshou",
			"ArenaDamageEffectData_gongbenwuzang",
			"ArenaDamageEffectData_gongsunli",
			"ArenaDamageEffectData_haiyue",
			"ArenaDamageEffectData_houyi",
			"ArenaDamageEffectData_huamulan",
			"ArenaDamageEffectData_jialuo",
			"ArenaDamageEffectData_kai",
			"ArenaDamageEffectData_lanlingwang",
			"ArenaDamageEffectData_libai",
			"ArenaDamageEffectData_liubei",
			"ArenaDamageEffectData_liushan",
			"ArenaDamageEffectData_lubanqihao",
			"ArenaDamageEffectData_luopuxia",
			"ArenaDamageEffectData_lvbu",
			"ArenaDamageEffectData_makeboluo",
			"ArenaDamageEffectData_maolingshaonv",
			"ArenaDamageEffectData_mingshiyin",
			"ArenaDamageEffectData_mozi",
			"ArenaDamageEffectData_paopao",
			"ArenaDamageEffectData_qingshuang",
			"ArenaDamageEffectData_renzhe",
			"ArenaDamageEffectData_sunce",
			"ArenaDamageEffectData_sunshangxiang",
			"ArenaDamageEffectData_sunwukong",
			"ArenaDamageEffectData_tongyong",
			"ArenaDamageEffectData_tuteng",
			"ArenaDamageEffectData_wangzhaojun",
			"ArenaDamageEffectData_wulizhaohuanshou",
			"ArenaDamageEffectData_xiangyu",
			"ArenaDamageEffectData_xiaohonghu",
			"ArenaDamageEffectData_xiaohoudun",
			"ArenaDamageEffectData_xiaoqiao",
			"ArenaDamageEffectData_xishi",
			"ArenaDamageEffectData_yangyuhuan",
			"ArenaDamageEffectData_yao",
			"ArenaDamageEffectData_yase",
			"ArenaDamageEffectData_yuji",
			"ArenaDamageEffectData_yunying",
			"ArenaDamageEffectData_zhangliang",
			"ArenaDamageEffectData_zhaoyun",
			"ArenaDamageEffectData_zhenji",
			"ArenaDamageEffectData_zhongkui",
			"ArenaDamageEffectData_zhugeliang",
			"HOKDamageEffectData_Hero",
        }
    },
    ["table_ArenaEnemyStackConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaEnemyStackConf",
        }
    },
    ["table_ArenaGlobalConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaGlobalConf",
        }
    },
    ["table_ArenaHUDButton"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHUDButton",
        }
    },
    ["table_ArenaHeroAttr"] = {
        TableKey = "id,in_table_game_type",
        SubTableNames = {
			"ArenaHeroAttrData",
			"BSHeroAttrData",
			"HOKHeroAttrData",
        }
    },
    ["table_ArenaHeroAttrName"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroAttrNameData",
        }
    },
    ["table_ArenaHeroAvatarData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroAvatarData",
			"HOKMonsterAvatarData",
        }
    },
    ["table_ArenaHeroCard"] = {
        TableKey = "id,inTableGameType",
        SubTableNames = {
			"ArenaHeroCard_ailin",
			"ArenaHeroCard_ake",
			"ArenaHeroCard_anqila",
			"ArenaHeroCard_bingyi",
			"ArenaHeroCard_caiwenji",
			"ArenaHeroCard_chengyaojin",
			"ArenaHeroCard_daji",
			"ArenaHeroCard_daqiao",
			"ArenaHeroCard_dianwei",
			"ArenaHeroCard_diaochan",
			"ArenaHeroCard_direnjie",
			"ArenaHeroCard_dongfangyao",
			"ArenaHeroCard_donghuangtaiyi",
			"ArenaHeroCard_duoliya",
			"ArenaHeroCard_gongbenwuzang",
			"ArenaHeroCard_gongsunli",
			"ArenaHeroCard_haiyue",
			"ArenaHeroCard_houyi",
			"ArenaHeroCard_huamulan",
			"ArenaHeroCard_jialuo",
			"ArenaHeroCard_kai",
			"ArenaHeroCard_lanlingwang",
			"ArenaHeroCard_libai",
			"ArenaHeroCard_liubei",
			"ArenaHeroCard_liushan",
			"ArenaHeroCard_lubanqihao",
			"ArenaHeroCard_luopuxia",
			"ArenaHeroCard_lvbu",
			"ArenaHeroCard_makeboluo",
			"ArenaHeroCard_maolingshaonv",
			"ArenaHeroCard_mingshiyin",
			"ArenaHeroCard_mozi",
			"ArenaHeroCard_paopao",
			"ArenaHeroCard_renzhe",
			"ArenaHeroCard_shangguanwaner",
			"ArenaHeroCard_sunce",
			"ArenaHeroCard_sunshangxiang",
			"ArenaHeroCard_sunwukong",
			"ArenaHeroCard_wangzhaojun",
			"ArenaHeroCard_wusula",
			"ArenaHeroCard_wuzetian",
			"ArenaHeroCard_xiahoudun",
			"ArenaHeroCard_xiangyu",
			"ArenaHeroCard_xiaohonghu",
			"ArenaHeroCard_xiaoqiao",
			"ArenaHeroCard_xishi",
			"ArenaHeroCard_yangyuhuan",
			"ArenaHeroCard_yao",
			"ArenaHeroCard_yase",
			"ArenaHeroCard_yuji",
			"ArenaHeroCard_yunying",
			"ArenaHeroCard_zhangliang",
			"ArenaHeroCard_zhaoyun",
			"ArenaHeroCard_zhenji",
			"ArenaHeroCard_zhongkui",
			"ArenaHeroCard_zhugeliang",
        }
    },
    ["table_ArenaHeroClassData"] = {
        TableKey = "heroClass",
        SubTableNames = {
			"ArenaHeroClassData",
        }
    },
    ["table_ArenaHeroDeraultCardData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroDeraultCardData",
        }
    },
    ["table_ArenaHeroLevelUp"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroLevelUpData",
        }
    },
    ["table_ArenaHeroOpenData"] = {
        TableKey = "heroId",
        SubTableNames = {
			"ArenaHeroOpenData",
        }
    },
    ["table_ArenaHeroPersonInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroPersonInfo",
        }
    },
    ["table_ArenaHeroRegisterData"] = {
        TableKey = "heroId",
        SubTableNames = {
			"ArenaHeroRegisterData",
        }
    },
    ["table_ArenaHeroSkillData"] = {
        TableKey = "id,in_table_game_type",
        SubTableNames = {
			"ArenaHeroSkillData_DianWei",
			"ArenaHeroSkillData_FinalMount",
			"ArenaHeroSkillData_HaiYue",
			"ArenaHeroSkillData_Mount",
			"ArenaHeroSkillData_ShangGuanWanEr",
			"ArenaHeroSkillData_XiangYu",
			"ArenaHeroSkillData_ZhangLiang",
			"ArenaHeroSkillData_ZhenJi",
			"ArenaHeroSkillData_ailin",
			"ArenaHeroSkillData_ake",
			"ArenaHeroSkillData_anqila",
			"ArenaHeroSkillData_bingyi",
			"ArenaHeroSkillData_caiwenji",
			"ArenaHeroSkillData_chengyaojin",
			"ArenaHeroSkillData_daji",
			"ArenaHeroSkillData_daqiao",
			"ArenaHeroSkillData_diaochan",
			"ArenaHeroSkillData_direnjie",
			"ArenaHeroSkillData_dongfangyao",
			"ArenaHeroSkillData_donghuangtaiyi",
			"ArenaHeroSkillData_duoliya",
			"ArenaHeroSkillData_fashuzhaohuanwu",
			"ArenaHeroSkillData_gongbenwuzang",
			"ArenaHeroSkillData_gongsunli",
			"ArenaHeroSkillData_houyi",
			"ArenaHeroSkillData_huamulan",
			"ArenaHeroSkillData_jialuo",
			"ArenaHeroSkillData_kai",
			"ArenaHeroSkillData_lanlingwang",
			"ArenaHeroSkillData_libai",
			"ArenaHeroSkillData_liubei",
			"ArenaHeroSkillData_liushan",
			"ArenaHeroSkillData_lubanqihao",
			"ArenaHeroSkillData_luopuxia",
			"ArenaHeroSkillData_lvbu",
			"ArenaHeroSkillData_makeboluo",
			"ArenaHeroSkillData_maolingshaonv",
			"ArenaHeroSkillData_mingshiyin",
			"ArenaHeroSkillData_mozi",
			"ArenaHeroSkillData_paopao",
			"ArenaHeroSkillData_renzhe",
			"ArenaHeroSkillData_sunce",
			"ArenaHeroSkillData_sunshangxiang",
			"ArenaHeroSkillData_sunwukong",
			"ArenaHeroSkillData_tuteng",
			"ArenaHeroSkillData_wangzhaojun",
			"ArenaHeroSkillData_wangzhaojun_RandomEvent",
			"ArenaHeroSkillData_wulizhaohuanwu",
			"ArenaHeroSkillData_wusula",
			"ArenaHeroSkillData_wuzetian",
			"ArenaHeroSkillData_xiahoudun",
			"ArenaHeroSkillData_xiaohonghu",
			"ArenaHeroSkillData_xiaoqiao",
			"ArenaHeroSkillData_xishi",
			"ArenaHeroSkillData_yangyuhuan",
			"ArenaHeroSkillData_yao",
			"ArenaHeroSkillData_yase",
			"ArenaHeroSkillData_yuji",
			"ArenaHeroSkillData_yunying",
			"ArenaHeroSkillData_zhaoyun",
			"ArenaHeroSkillData_zhongkui",
			"ArenaHeroSkillData_zhugeliang",
        }
    },
    ["table_ArenaHeroSkillTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroSkillTypeData",
        }
    },
    ["table_ArenaHeroSystemData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroSystemData",
        }
    },
    ["table_ArenaHeroUnlock"] = {
        TableKey = "heroid",
        SubTableNames = {
			"ArenaHeroUnlockData",
        }
    },
    ["table_ArenaHitData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHitData",
        }
    },
    ["table_ArenaHitEffectData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHitEffectData",
        }
    },
    ["table_ArenaHitEffectScaleData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHitEffectScaleData",
        }
    },
    ["table_ArenaInGamePortraitFrame"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaInGamePortraitFrame",
        }
    },
    ["table_ArenaIndicatorConf"] = {
        TableKey = "indicator_id",
        SubTableNames = {
			"ArenaIndicatorData",
        }
    },
    ["table_ArenaInteractMark"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaInteractMark",
        }
    },
    ["table_ArenaKillUI"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaKillUI",
        }
    },
    ["table_ArenaLevelFactor"] = {
        TableKey = "id,in_table_game_type",
        SubTableNames = {
			"ArenaLevelFactor",
			"HOKLevelFactor",
        }
    },
    ["table_ArenaLimitedTimeFreeHeroData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaLimitedTimeFreeHeroData",
        }
    },
    ["table_ArenaMagicFieldSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMagicFieldSet",
        }
    },
    ["table_ArenaMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMiscConfData",
        }
    },
    ["table_ArenaMonsterData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMonsterData",
        }
    },
    ["table_ArenaQuickMessageData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaQuickMessageBattleData",
			"ArenaQuickMessageLevelData",
        }
    },
    ["table_ArenaRandomEventConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRandomEventConfig",
        }
    },
    ["table_ArenaRandomEventWeight"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRandomEventWeight",
        }
    },
    ["table_ArenaResilientData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaResilientData",
        }
    },
    ["table_ArenaRobotBannedHero"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRobotBannedHero",
        }
    },
    ["table_ArenaRoundConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRoundConf",
        }
    },
    ["table_ArenaSignConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSignConf",
        }
    },
    ["table_ArenaSkillConditionType"] = {
        TableKey = "Index",
        SubTableNames = {
			"ArenaSkillConditionTypeData",
        }
    },
    ["table_ArenaSkillConf"] = {
        TableKey = "skillid,in_table_game_type",
        SubTableNames = {
			"ArenaSkillConfData",
			"ArenaSkillConfData_DianWei",
			"ArenaSkillConfData_FinalMount",
			"ArenaSkillConfData_HaiYue",
			"ArenaSkillConfData_Mount",
			"ArenaSkillConfData_XiangYu",
			"ArenaSkillConfData_ZhangLiang",
			"ArenaSkillConfData_ZhenJi",
			"ArenaSkillConfData_ailin",
			"ArenaSkillConfData_ake",
			"ArenaSkillConfData_anqila",
			"ArenaSkillConfData_bingyi",
			"ArenaSkillConfData_caiwenji",
			"ArenaSkillConfData_chengyaojin",
			"ArenaSkillConfData_daji",
			"ArenaSkillConfData_daqiao",
			"ArenaSkillConfData_diaochan",
			"ArenaSkillConfData_direnjie",
			"ArenaSkillConfData_dongfangyao",
			"ArenaSkillConfData_donghuangtaiyi",
			"ArenaSkillConfData_duoliya",
			"ArenaSkillConfData_fashuzhaohuanshou",
			"ArenaSkillConfData_gongbenwuzang",
			"ArenaSkillConfData_gongsunli",
			"ArenaSkillConfData_houyi",
			"ArenaSkillConfData_houyi_RandomEvent",
			"ArenaSkillConfData_huamulan",
			"ArenaSkillConfData_jialuo",
			"ArenaSkillConfData_kai",
			"ArenaSkillConfData_kai_RandomEvent",
			"ArenaSkillConfData_lanlingwang",
			"ArenaSkillConfData_libai",
			"ArenaSkillConfData_liubei",
			"ArenaSkillConfData_liushan",
			"ArenaSkillConfData_lubanqihao",
			"ArenaSkillConfData_lubanqihao_RandomEvent",
			"ArenaSkillConfData_luopuxia",
			"ArenaSkillConfData_lvbu",
			"ArenaSkillConfData_makeboluo",
			"ArenaSkillConfData_maolingshaonv",
			"ArenaSkillConfData_mingshiyin",
			"ArenaSkillConfData_mozi",
			"ArenaSkillConfData_paopao",
			"ArenaSkillConfData_renzhe",
			"ArenaSkillConfData_shangguanwaner",
			"ArenaSkillConfData_summonerspells",
			"ArenaSkillConfData_sunce",
			"ArenaSkillConfData_sunshangxiang",
			"ArenaSkillConfData_sunwukong",
			"ArenaSkillConfData_tuteng",
			"ArenaSkillConfData_wangzhaojun",
			"ArenaSkillConfData_wangzhaojun_RandomEvent",
			"ArenaSkillConfData_wulizhaohuanwu",
			"ArenaSkillConfData_wusula",
			"ArenaSkillConfData_wuzetian",
			"ArenaSkillConfData_xiahoudun",
			"ArenaSkillConfData_xiaohonghu",
			"ArenaSkillConfData_xiaoqiao",
			"ArenaSkillConfData_xishi",
			"ArenaSkillConfData_yangyuhuan",
			"ArenaSkillConfData_yao",
			"ArenaSkillConfData_yase",
			"ArenaSkillConfData_yuji",
			"ArenaSkillConfData_yunying",
			"ArenaSkillConfData_zhaoyun",
			"ArenaSkillConfData_zhongkui",
			"ArenaSkillConfData_zhongkui_RandomEvent",
			"ArenaSkillConfData_zhugeliang",
			"HOKSkillConfData",
			"HOKSkillConfData_summoner",
        }
    },
    ["table_ArenaSkinEffectData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSkinEffectData",
        }
    },
    ["table_ArenaSkinSystemData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSkinSystemData",
        }
    },
    ["table_ArenaSpecialMatchCardSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpecialMatchCardSet",
        }
    },
    ["table_ArenaSpecialMatchRoleSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpecialMatchRoleSet",
        }
    },
    ["table_ArenaSpecialMatchRoundDamageConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpecialMatchRoundDamageConfig",
        }
    },
    ["table_ArenaSpecialMatchRoundMapSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpecialMatchRoundMapSet",
        }
    },
    ["table_ArenaSpecialMatchRoundParamsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpecialMatchRoundParamsConfig",
        }
    },
    ["table_ArenaStackCountData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStackCountConf",
        }
    },
    ["table_ArenaStrategyMapPoint"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStrategyMapPoint",
        }
    },
    ["table_ArenaStrategyMapPointEntry"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStrategyMapPointEntry",
        }
    },
    ["table_ArenaTargetFilterData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaTargetFilterData",
        }
    },
    ["table_ArenaTargetFilterRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaTargetFilterRuleData",
        }
    },
    ["table_ArenaTestCardGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaTestCardGroup",
        }
    },
    ["table_ArenaTestDmgGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaTestDmgGroup",
        }
    },
    ["table_ArenaTipConfData"] = {
        TableKey = "matchType",
        SubTableNames = {
			"ArenaTipConfData",
        }
    },
    ["table_ArenaTipItemConfData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"ArenaTipItemConfData",
        }
    },
    ["table_ArenaVoice"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaVoice",
        }
    },
    ["table_ArenaVoiceTrigger"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaVoiceTrigger",
        }
    },
    ["table_ArenaWeaponSet"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaWeapon",
        }
    },
    ["table_BSGlobalEventConf"] = {
        TableKey = "EventType",
        SubTableNames = {
			"BSEvent_Global",
        }
    },
    ["table_BSMapEventConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BSEvent_Map",
        }
    },
    ["table_BSGlobalConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BSGlobalConf",
        }
    },
    ["table_BSHeroListConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BSHeroList",
        }
    },
    ["table_BSMapParam"] = {
        TableKey = "id",
        SubTableNames = {
			"BSMapParam_BS",
        }
    },
    ["table_BSPotionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BSPotionConf",
        }
    },
    ["table_MallExtraHotRecommendPageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ExtraHotRecommendPageConf_Arena",
        }
    },
    ["table_GameOptimizeSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"GameOptimizeSettingData_Arena",
			"GameOptimizeSettingData_HOK",
        }
    },
    ["table_HOKBuffAreaTrigger"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKBuffAreaTriggerRow",
        }
    },
    ["table_HOKCardData"] = {
        TableKey = "id,inTableGameType",
        SubTableNames = {
			"HOKCardData",
        }
    },
    ["table_HOKCardFilterConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKCardFilterConf",
        }
    },
    ["table_HOKCardShopConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKCardShopConf",
        }
    },
    ["table_HOKCardShopPackageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKCardShopPackageConf",
        }
    },
    ["table_HOKDeathExpMakeUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKDeathExpMakeUpConf",
        }
    },
    ["table_HOKDeathGoldMakeUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKDeathGoldMakeUpConf",
        }
    },
    ["table_HOKExpMakeUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKExpMakeUpConf",
        }
    },
    ["table_HOKGlobalConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKGlobalConf",
        }
    },
    ["table_HOKGoldExpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKGoldExpConf",
        }
    },
    ["table_HOKGoldExpMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKGoldExpMisc",
        }
    },
    ["table_HOKGoldExpTimeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKGoldExpTimeConf",
        }
    },
    ["table_HOKGoldMakeUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKGoldMakeUpConf",
        }
    },
    ["table_HOKHeroBalanceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroBalanceConf",
        }
    },
    ["table_HOKHeroCardPoolConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroCardPoolConf",
        }
    },
    ["table_HOKHeroCardShopConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroCardShopConf",
        }
    },
    ["table_HOKHeroGoldExpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroGoldExpConf",
        }
    },
    ["table_HOKHeroLevelUp"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroLevelUp",
        }
    },
    ["table_HOKHeroRebirthConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroRebirthConf",
        }
    },
    ["table_HOKHeroRobotConf"] = {
        TableKey = "heroId",
        SubTableNames = {
			"HOKHeroRobotConf",
        }
    },
    ["table_HOKHeroRobotIntensityConf"] = {
        TableKey = "intensityLv",
        SubTableNames = {
			"HOKHeroRobotIntensityConf",
        }
    },
    ["table_HOKHeroRobotMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroRobotMiscConf",
        }
    },
    ["table_HOKHeroRobotPlayScriptConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroRobotPlayScriptConf",
        }
    },
    ["table_HOKHeroUnlockConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroUnlockConf",
        }
    },
    ["table_HOKNewbieCardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKNewbieCardConf",
        }
    },
    ["table_HOKRiftPowerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKRiftPowerConf",
        }
    },
    ["table_HOKRobotDynamicBallanceConfData"] = {
        TableKey = "groupId",
        SubTableNames = {
			"HOKRobotDynamicBallanceConf",
        }
    },
    ["table_HOKSkillOverrideConf"] = {
        TableKey = "skillid",
        SubTableNames = {
			"HOKSkillOverrideConfData",
        }
    },
    ["table_HOKSoldierWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKSoldierWave",
        }
    },
    ["table_HOKTowerAtkGrowConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKTowerAtkGrowConf",
        }
    },
    ["table_HokIconData"] = {
        TableKey = "id",
        SubTableNames = {
			"HokIconData",
        }
    },
    ["table_HokMiscData"] = {
        TableKey = "key",
        SubTableNames = {
			"HokMiscData",
        }
    },
    ["table_MallExtraConf"] = {
        TableKey = "commodityId",
        SubTableNames = {
			"MallExtraConf_Arena",
        }
    },
    ["table_MiscConfArena"] = {
        TableKey = "id",
        SubTableNames = {
			"MiscConfArena",
        }
    },
    ["table_ProtectedScoreAdditionalData"] = {
        TableKey = "id",
        SubTableNames = {
			"ProtectedScoreAdditionalData_HOK",
			"ProtectedScoreAdditionalData_Moba",
        }
    },
    ["table_QualifyingLevelDimensionConditionData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionConditionData_HOK",
			"QualifyingLevelDimensionConditionData_Moba",
        }
    },
    ["table_QualifyingLevelDimensionScoreData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionScoreData_HOK",
        }
    },
    ["table_QualifyingPerfScore"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingPerfScoreData_HOK",
			"QualifyingPerfScoreData_Moba",
        }
    },
    ["table_HOKBattleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ResHOKBattleFOV",
        }
    },
    ["table_ResHOKMonsterLevel"] = {
        TableKey = "id",
        SubTableNames = {
			"ResHOKMonsterLevel",
        }
    },
    ["table_SeasonQualifyingMail"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonQualifyingMailData_HOK",
        }
    },
}

return TableHelperTables
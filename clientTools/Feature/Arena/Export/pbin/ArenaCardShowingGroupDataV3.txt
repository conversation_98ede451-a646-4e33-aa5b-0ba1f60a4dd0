com.tencent.wea.xlsRes.table_ArenaCardShowingGroupDataV3
excel/xls/Arena/K_Arena选秀表_3v3.xlsx sheet:选秀随机组合表
rows {
  key: 1
  id: 100002
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 2
  id: 100002
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 3
  id: 100002
  weight: 80
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
}
rows {
  key: 4
  id: 100002
  weight: 150
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 5
  id: 100002
  weight: 150
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
}
rows {
  key: 6
  id: 100002
  weight: 150
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 7
  id: 100002
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 8
  id: 100002
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 9
  id: 100002
  weight: 80
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 10
  id: 200001
  weight: 1
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 20061
    groupName: "专属"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 11
  id: 200001
  weight: 1
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 20061
    groupName: "专属"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 12
  id: 200001
  weight: 1
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 20061
    groupName: "专属"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 13
  id: 200001
  weight: 1
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 20061
    groupName: "专属"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 14
  id: 100003
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 15
  id: 100003
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 16
  id: 100003
  weight: 80
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 17
  id: 100003
  weight: 150
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 18
  id: 100003
  weight: 150
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 19
  id: 100003
  weight: 150
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 20
  id: 100003
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 21
  id: 100003
  weight: 80
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}
rows {
  key: 22
  id: 100003
  weight: 80
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Chroma
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500010
    groupName: "功能"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500009
    groupName: "防御"
    groupQuality: ACQE_Purple
  }
  cardPack {
    groupId: 500008
    groupName: "输出"
    groupQuality: ACQE_Purple
  }
}

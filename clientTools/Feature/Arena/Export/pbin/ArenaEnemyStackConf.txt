com.tencent.wea.xlsRes.table_ArenaEnemyStackConf
excel/xls/Arena/D_Arena对敌叠层配置表.xlsx sheet:对敌叠层配置表
rows {
  id: 1
  role_id: 1021
  avatar_id: 405069
  buff_group_id: 31021001
  umg_name: "UI_Arena_EnemyStack_GongSunLi_405069"
  switcher_name: "w_switcher_ChangeStackState"
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21021001
  is_default: true
}
rows {
  id: 2
  role_id: 1021
  avatar_id: 405070
  buff_group_id: 31021001
  umg_name: "UI_Arena_EnemyStack_GongSunLi_405070"
  switcher_name: "w_switcher_ChangeStackState"
  animations {
    one: "ani_gongsunli_01_loop"
    two: "ani_gongsunli_02_loop"
    three: "ani_gongsunli_03_loop"
  }
  is_anim_loop: true
  is_anim_loop: true
  is_anim_loop: true
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21021001
}
rows {
  id: 3
  role_id: 1041
  avatar_id: 405091
  buff_group_id: 31041001
  umg_name: "UI_Arena_EnemyStack_DiaoChan_403110"
  switcher_name: "w_switcher_ChangeStackState"
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21041001
  is_default: true
}
rows {
  id: 4
  role_id: 1041
  avatar_id: 405105
  buff_group_id: 31041001
  umg_name: "UI_Arena_EnemyStack_DiaoChan_405105"
  switcher_name: "w_switcher_ChangeStackState"
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21041001
}
rows {
  id: 5
  role_id: 1042
  avatar_id: 405092
  buff_group_id: 31042602
  umg_name: "UI_Arena_EnemyStack_LiuBei_405092"
  switcher_name: "w_switcher_ChangeStackState"
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21042001
  is_default: true
}
rows {
  id: 6
  role_id: 1017
  avatar_id: 405065
  buff_group_id: 31017003
  umg_name: "UI_Arena_SelfStack_YunYing_405065"
  switcher_name: "w_switcher_ChangeStackState"
  animations {
    one: "Ani_Yunying_01"
    two: "Ani_Yunying_02"
    three: "Ani_Yunying_03"
  }
  is_anim_loop: false
  is_anim_loop: false
  is_anim_loop: false
  target_type: Arena_EffectStackTarget_Friend
  is_show_character: true
  is_show_monster: false
  is_show_soldier: false
  custom_group_id: 21017001
  is_default: true
}
rows {
  id: 7
  role_id: 1049
  avatar_id: 405107
  buff_group_id: 31049542
  umg_name: "UI_Arena_EnemyStack_LuoPuXia_405107"
  switcher_name: "w_switcher_ChangeStackState"
  is_anim_loop: false
  is_anim_loop: false
  is_anim_loop: false
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21049001
  is_default: true
}
rows {
  id: 8
  role_id: 1049
  avatar_id: 405111
  buff_group_id: 31049542
  umg_name: "UI_Arena_EnemyStack_LuoPuXia_405111"
  switcher_name: "w_switcher_ChangeStackState"
  is_anim_loop: false
  is_anim_loop: false
  is_anim_loop: false
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21049001
}
rows {
  id: 9
  role_id: 1041
  avatar_id: 405120
  buff_group_id: 31041001
  umg_name: "UI_Arena_EnemyStack_DiaoChan_405120"
  switcher_name: "w_switcher_ChangeStackState"
  target_type: Arena_EffectStackTarget_Enemy
  is_show_character: true
  is_show_monster: true
  is_show_soldier: true
  custom_group_id: 21041001
}

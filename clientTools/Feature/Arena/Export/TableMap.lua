-- This file is automatically generated

local TableMap = {
	ArenaABTestGroupTable = {
		ArenaABTestGroupConfSubTables = {
			"ArenaABTestGroupConf",
		},
	},
	ArenaAIConfigTable = {
		ArenaAIConfigSubTables = {
			"ArenaAIConfig",
		},
		ArenaAIDynamicLevelChangeConfigSubTables = {
			"ArenaAIDynamicLevelChangeConfig",
		},
	},
	ArenaAttrTestTable = {
		ArenaTestCardGroupSubTables = {
			"ArenaTestCardGroup",
		},
		ArenaTestDmgGroupSubTables = {
			"ArenaTestDmgGroup",
		},
	},
	ArenaAttributeTable = {
		ArenaHeroAttrSubTables = {
			"ArenaHeroAttrData",
			"BSHeroAttrData",
			"HOKHeroAttrData",
		},
		ArenaHeroAttrNameSubTables = {
			"ArenaHeroAttrNameData",
		},
		ArenaHeroAvatarDataSubTables = {
			"ArenaHeroAvatarData",
			"HOKMonsterAvatarData",
		},
		ArenaHeroLevelUpSubTables = {
			"ArenaHeroLevelUpData",
		},
		ArenaHeroPersonInfoSubTables = {
			"ArenaHeroPersonInfo",
		},
		ArenaHeroSkillDataSubTables = {
			"ArenaHeroSkillData_DianWei",
			"ArenaHeroSkillData_FinalMount",
			"ArenaHeroSkillData_HaiYue",
			"ArenaHeroSkillData_Mount",
			"ArenaHeroSkillData_ShangGuanWanEr",
			"ArenaHeroSkillData_XiangYu",
			"ArenaHeroSkillData_ZhangLiang",
			"ArenaHeroSkillData_ZhenJi",
			"ArenaHeroSkillData_ailin",
			"ArenaHeroSkillData_ake",
			"ArenaHeroSkillData_anqila",
			"ArenaHeroSkillData_bingyi",
			"ArenaHeroSkillData_caiwenji",
			"ArenaHeroSkillData_chengyaojin",
			"ArenaHeroSkillData_daji",
			"ArenaHeroSkillData_daqiao",
			"ArenaHeroSkillData_diaochan",
			"ArenaHeroSkillData_direnjie",
			"ArenaHeroSkillData_dongfangyao",
			"ArenaHeroSkillData_donghuangtaiyi",
			"ArenaHeroSkillData_duoliya",
			"ArenaHeroSkillData_fashuzhaohuanwu",
			"ArenaHeroSkillData_gongbenwuzang",
			"ArenaHeroSkillData_gongsunli",
			"ArenaHeroSkillData_houyi",
			"ArenaHeroSkillData_huamulan",
			"ArenaHeroSkillData_jialuo",
			"ArenaHeroSkillData_kai",
			"ArenaHeroSkillData_lanlingwang",
			"ArenaHeroSkillData_libai",
			"ArenaHeroSkillData_liubei",
			"ArenaHeroSkillData_liushan",
			"ArenaHeroSkillData_lubanqihao",
			"ArenaHeroSkillData_luopuxia",
			"ArenaHeroSkillData_lvbu",
			"ArenaHeroSkillData_makeboluo",
			"ArenaHeroSkillData_maolingshaonv",
			"ArenaHeroSkillData_mingshiyin",
			"ArenaHeroSkillData_mozi",
			"ArenaHeroSkillData_paopao",
			"ArenaHeroSkillData_renzhe",
			"ArenaHeroSkillData_sunce",
			"ArenaHeroSkillData_sunshangxiang",
			"ArenaHeroSkillData_sunwukong",
			"ArenaHeroSkillData_tuteng",
			"ArenaHeroSkillData_wangzhaojun",
			"ArenaHeroSkillData_wangzhaojun_RandomEvent",
			"ArenaHeroSkillData_wulizhaohuanwu",
			"ArenaHeroSkillData_wusula",
			"ArenaHeroSkillData_wuzetian",
			"ArenaHeroSkillData_xiahoudun",
			"ArenaHeroSkillData_xiaohonghu",
			"ArenaHeroSkillData_xiaoqiao",
			"ArenaHeroSkillData_xishi",
			"ArenaHeroSkillData_yangyuhuan",
			"ArenaHeroSkillData_yao",
			"ArenaHeroSkillData_yase",
			"ArenaHeroSkillData_yuji",
			"ArenaHeroSkillData_yunying",
			"ArenaHeroSkillData_zhaoyun",
			"ArenaHeroSkillData_zhongkui",
			"ArenaHeroSkillData_zhugeliang",
		},
		ArenaHeroSkillTypeDataSubTables = {
			"ArenaHeroSkillTypeData",
		},
		ArenaHeroSystemDataSubTables = {
			"ArenaHeroSystemData",
		},
		ArenaResilientDataSubTables = {
			"ArenaResilientData",
		},
	},
	ArenaAttributeHOKOverrideTable = {
		ArenaAttributeHOKOverrideSubTables = {
			"ArenaAttributeHOKOverrideData",
		},
	},
	ArenaAvatarSkillTable = {
		ArenaAvatarSkillConfSubTables = {
			"ArenaAvatarSkillConfData",
			"HOKAvatarSkillConfData",
		},
	},
	ArenaBuffTable = {
		ArenaBuffAbnormalDataSubTables = {
			"ArenaBuffAbnormalData",
		},
		ArenaBuffAbnormalDisplayDataSubTables = {
			"ArenaBuffAbnormalDisplayData",
		},
		ArenaBuffAbnormalRelationDisplayDataSubTables = {
			"ArenaBuffAbnormalRelationDisplayData",
		},
		ArenaBuffAdvancedDisplayDataSubTables = {
			"ArenaBuffAdvancedDisplayData",
		},
		ArenaBuffDataSubTables = {
			"ArenaBuffData",
			"ArenaBuffData_Card_Attr",
			"ArenaBuffData_Card_Function",
			"ArenaBuffData_DianWei",
			"ArenaBuffData_FinalMount",
			"ArenaBuffData_Mount",
			"ArenaBuffData_Prop",
			"ArenaBuffData_ShangGuanWanEr",
			"ArenaBuffData_XiangYu",
			"ArenaBuffData_ZhangLiang",
			"ArenaBuffData_ZhenJi",
			"ArenaBuffData_ailin",
			"ArenaBuffData_ake",
			"ArenaBuffData_anqila",
			"ArenaBuffData_bingyi",
			"ArenaBuffData_caiwenji",
			"ArenaBuffData_chengyaojin",
			"ArenaBuffData_daji",
			"ArenaBuffData_daqiao",
			"ArenaBuffData_diaochan",
			"ArenaBuffData_direnjie",
			"ArenaBuffData_dongfangyao",
			"ArenaBuffData_donghuangtaiyi",
			"ArenaBuffData_dunshan",
			"ArenaBuffData_duoliya",
			"ArenaBuffData_fashuzhaohuanshou_RandomEvent",
			"ArenaBuffData_gongbenwuzang",
			"ArenaBuffData_gongsunli",
			"ArenaBuffData_haiyue",
			"ArenaBuffData_hokBalanceBuff",
			"ArenaBuffData_houyi",
			"ArenaBuffData_houyi_RandomEvent",
			"ArenaBuffData_huamulan",
			"ArenaBuffData_jialuo",
			"ArenaBuffData_kai",
			"ArenaBuffData_kai_RandomEvent",
			"ArenaBuffData_lanlingwang",
			"ArenaBuffData_libai",
			"ArenaBuffData_liubei",
			"ArenaBuffData_liushan",
			"ArenaBuffData_lubanqihao",
			"ArenaBuffData_lubanqihao_RandomEvent",
			"ArenaBuffData_luopuxia",
			"ArenaBuffData_lvbu",
			"ArenaBuffData_makeboluo",
			"ArenaBuffData_maolingshaonv",
			"ArenaBuffData_mingshiyin",
			"ArenaBuffData_mozi",
			"ArenaBuffData_paopao",
			"ArenaBuffData_renzhe",
			"ArenaBuffData_summonerspells",
			"ArenaBuffData_sunce",
			"ArenaBuffData_sunshangxiang",
			"ArenaBuffData_sunwukong",
			"ArenaBuffData_test",
			"ArenaBuffData_tuteng_RandomEvent",
			"ArenaBuffData_wangzhaojun",
			"ArenaBuffData_wangzhaojun_RandomEvent",
			"ArenaBuffData_wulizhaohuanshou_RandomEvent",
			"ArenaBuffData_wusula",
			"ArenaBuffData_wuzetian",
			"ArenaBuffData_xiahoudun",
			"ArenaBuffData_xiaohonghu",
			"ArenaBuffData_xiaoqiao",
			"ArenaBuffData_xishi",
			"ArenaBuffData_yangyuhuan",
			"ArenaBuffData_yao",
			"ArenaBuffData_yase",
			"ArenaBuffData_yuji",
			"ArenaBuffData_yunying",
			"ArenaBuffData_yunzhongjun",
			"ArenaBuffData_zhaoyun",
			"ArenaBuffData_zhongkui",
			"ArenaBuffData_zhongkui_RandomEvent",
			"ArenaBuffData_zhugeliang",
			"BSBuffData_Prop",
			"HOKBuffData",
			"HOKBuffData_summoner",
			"HOKCardBuffData",
		},
	},
	ArenaBulletSetTable = {
		ArenaBulletSetSubTables = {
			"ArenaBulletSet",
			"HOKBulletSet",
		},
	},
	ArenaCardTable = {
		ArenaCardBSKillLootDataSubTables = {
			"ArenaCardBSKillLootData",
		},
		ArenaCardCatgoryDataSubTables = {
			"ArenaCardCatgoryData",
		},
		ArenaCardDataSubTables = {
			"ArenaCardData",
			"ArenaCardData_5v5",
		},
		ArenaCardFilterDataSubTables = {
			"ArenaCardFilterData",
		},
		ArenaCardFilterDataV3SubTables = {
			"ArenaCardFilterDataV3",
		},
		ArenaCardHeroRandomGroupDataSubTables = {
			"ArenaCardHeroRandomGroupData",
		},
		ArenaCardJumpTextDataSubTables = {
			"ArenaCardJumpTextData",
		},
		ArenaCardQualityDataSubTables = {
			"ArenaCardQualityData",
		},
		ArenaCardRandomDataSubTables = {
			"ArenaCardRandomData",
		},
		ArenaCardRandomGroupDataSubTables = {
			"ArenaCardRandomGroupData",
		},
		ArenaCardRandomWeightSubTables = {
			"ArenaCardRandomWeight",
		},
		ArenaCardResourceDataSubTables = {
			"ArenaCardResourceData",
		},
		ArenaCardShowingGroupDataSubTables = {
			"ArenaCardShowingGroupData",
		},
		ArenaCardShowingGroupDataV3SubTables = {
			"ArenaCardShowingGroupDataV3",
		},
	},
	ArenaCardPackTable = {
		ArenaCardPackDataSubTables = {
			"ArenaCardPackData",
		},
		ArenaCardPackGroupDataSubTables = {
			"ArenaCardPackGroupData",
		},
		ArenaCardPackRuleDataSubTables = {
			"ArenaCardPackRuleData",
		},
		ArenaCardPackUpgradeDataSubTables = {
			"ArenaCardPackUpgradeData",
		},
		ArenaCardPocketRuleDataSubTables = {
			"ArenaCardPocketRuleData",
		},
	},
	ArenaCardSuitBuffTable = {
		ArenaCardSuitBuffDataSubTables = {
			"ArenaCardSuitBuffData",
		},
	},
	ArenaCardTagWeightTable = {
		ArenaCardTagWeightSubTables = {
			"ArenaCardTagWeight",
		},
	},
	ArenaCombatEffectivenessTable = {
		ArenaCombatEffectivenessAccelerationSubTables = {
			"ArenaCombatEffectivenessAccelerationData",
			"HOKCombatEffectivenessAccelerationData",
		},
		ArenaCombatEffectivenessBadgeSubTables = {
			"ArenaCombatEffectivenessBadgeData",
			"HOKCombatEffectivenessBadgeData",
		},
		ArenaCombatEffectivenessGroupSubTables = {
			"ArenaCombatEffectivenessGroupData",
		},
		ArenaCombatEffectivenessHeroBattleSubTables = {
			"ArenaCombatEffectivenessHeroBattleData",
			"HOKCombatEffectivenessHeroBattleData",
		},
		ArenaCombatEffectivenessHeroGlobalSubTables = {
			"ArenaCombatEffectivenessHeroGlobalData",
			"HOKCombatEffectivenessHeroGlobalData",
		},
		ArenaCombatEffectivenessLowAccelerationSubTables = {
			"ArenaCombatEffectivenessLowAccelerationData",
			"HOKCombatEffectivenessLowAccelerationData",
		},
		ArenaCombatEffectivenessMiscSubTables = {
			"ArenaCombatEffectivenessMiscData",
			"HOKCombatEffectivenessMiscData",
		},
		ArenaCombatEffectivenessProtectionSubTables = {
			"ArenaCombatEffectivenessProtectionData",
			"HOKCombatEffectivenessProtectionData",
		},
		ArenaCombatEffectivenessRankSubTables = {
			"ArenaCombatEffectivenessRankData",
			"HOKCombatEffectivenessRankData",
		},
		ArenaCombatEffectivenessTeamSubTables = {
			"ArenaCombatEffectivenessTeamData",
			"HOKCombatEffectivenessTeamData",
		},
	},
	ArenaDamageEffectTable = {
		ArenaDamageEffectSubTables = {
			"ArenaDamageEffectData",
			"ArenaDamageEffectData_FinalMount",
			"ArenaDamageEffectData_Hero",
			"ArenaDamageEffectData_Mount",
			"ArenaDamageEffectData_ShangGuanWanEr",
			"ArenaDamageEffectData_ailin",
			"ArenaDamageEffectData_ake",
			"ArenaDamageEffectData_anqila",
			"ArenaDamageEffectData_bingyi",
			"ArenaDamageEffectData_caiwenji",
			"ArenaDamageEffectData_chengyaojin",
			"ArenaDamageEffectData_daji",
			"ArenaDamageEffectData_daqiao",
			"ArenaDamageEffectData_dianwei",
			"ArenaDamageEffectData_diaochan",
			"ArenaDamageEffectData_direnjie",
			"ArenaDamageEffectData_dongfangyao",
			"ArenaDamageEffectData_donghuangtaiyi",
			"ArenaDamageEffectData_duoliya",
			"ArenaDamageEffectData_fashuzhaohuanshou",
			"ArenaDamageEffectData_gongbenwuzang",
			"ArenaDamageEffectData_gongsunli",
			"ArenaDamageEffectData_haiyue",
			"ArenaDamageEffectData_houyi",
			"ArenaDamageEffectData_huamulan",
			"ArenaDamageEffectData_jialuo",
			"ArenaDamageEffectData_kai",
			"ArenaDamageEffectData_lanlingwang",
			"ArenaDamageEffectData_libai",
			"ArenaDamageEffectData_liubei",
			"ArenaDamageEffectData_liushan",
			"ArenaDamageEffectData_lubanqihao",
			"ArenaDamageEffectData_luopuxia",
			"ArenaDamageEffectData_lvbu",
			"ArenaDamageEffectData_makeboluo",
			"ArenaDamageEffectData_maolingshaonv",
			"ArenaDamageEffectData_mingshiyin",
			"ArenaDamageEffectData_mozi",
			"ArenaDamageEffectData_paopao",
			"ArenaDamageEffectData_qingshuang",
			"ArenaDamageEffectData_renzhe",
			"ArenaDamageEffectData_sunce",
			"ArenaDamageEffectData_sunshangxiang",
			"ArenaDamageEffectData_sunwukong",
			"ArenaDamageEffectData_tongyong",
			"ArenaDamageEffectData_tuteng",
			"ArenaDamageEffectData_wangzhaojun",
			"ArenaDamageEffectData_wulizhaohuanshou",
			"ArenaDamageEffectData_xiangyu",
			"ArenaDamageEffectData_xiaohonghu",
			"ArenaDamageEffectData_xiaohoudun",
			"ArenaDamageEffectData_xiaoqiao",
			"ArenaDamageEffectData_xishi",
			"ArenaDamageEffectData_yangyuhuan",
			"ArenaDamageEffectData_yao",
			"ArenaDamageEffectData_yase",
			"ArenaDamageEffectData_yuji",
			"ArenaDamageEffectData_yunying",
			"ArenaDamageEffectData_zhangliang",
			"ArenaDamageEffectData_zhaoyun",
			"ArenaDamageEffectData_zhenji",
			"ArenaDamageEffectData_zhongkui",
			"ArenaDamageEffectData_zhugeliang",
			"HOKDamageEffectData_Hero",
		},
		ArenaLevelFactorSubTables = {
			"ArenaLevelFactor",
			"HOKLevelFactor",
		},
	},
	ArenaEnemyStackTable = {
		ArenaEnemyStackConfSubTables = {
			"ArenaEnemyStackConf",
		},
	},
	ArenaGlobalTable = {
		ArenaGlobalConfSubTables = {
			"ArenaGlobalConf",
		},
	},
	ArenaHUDButtonTable = {
		ArenaHUDButtonSubTables = {
			"ArenaHUDButton",
		},
	},
	ArenaHeroCardTable = {
		ArenaHeroCardSubTables = {
			"ArenaHeroCard_ailin",
			"ArenaHeroCard_ake",
			"ArenaHeroCard_anqila",
			"ArenaHeroCard_bingyi",
			"ArenaHeroCard_caiwenji",
			"ArenaHeroCard_chengyaojin",
			"ArenaHeroCard_daji",
			"ArenaHeroCard_daqiao",
			"ArenaHeroCard_dianwei",
			"ArenaHeroCard_diaochan",
			"ArenaHeroCard_direnjie",
			"ArenaHeroCard_dongfangyao",
			"ArenaHeroCard_donghuangtaiyi",
			"ArenaHeroCard_duoliya",
			"ArenaHeroCard_gongbenwuzang",
			"ArenaHeroCard_gongsunli",
			"ArenaHeroCard_haiyue",
			"ArenaHeroCard_houyi",
			"ArenaHeroCard_huamulan",
			"ArenaHeroCard_jialuo",
			"ArenaHeroCard_kai",
			"ArenaHeroCard_lanlingwang",
			"ArenaHeroCard_libai",
			"ArenaHeroCard_liubei",
			"ArenaHeroCard_liushan",
			"ArenaHeroCard_lubanqihao",
			"ArenaHeroCard_luopuxia",
			"ArenaHeroCard_lvbu",
			"ArenaHeroCard_makeboluo",
			"ArenaHeroCard_maolingshaonv",
			"ArenaHeroCard_mingshiyin",
			"ArenaHeroCard_mozi",
			"ArenaHeroCard_paopao",
			"ArenaHeroCard_renzhe",
			"ArenaHeroCard_shangguanwaner",
			"ArenaHeroCard_sunce",
			"ArenaHeroCard_sunshangxiang",
			"ArenaHeroCard_sunwukong",
			"ArenaHeroCard_wangzhaojun",
			"ArenaHeroCard_wusula",
			"ArenaHeroCard_wuzetian",
			"ArenaHeroCard_xiahoudun",
			"ArenaHeroCard_xiangyu",
			"ArenaHeroCard_xiaohonghu",
			"ArenaHeroCard_xiaoqiao",
			"ArenaHeroCard_xishi",
			"ArenaHeroCard_yangyuhuan",
			"ArenaHeroCard_yao",
			"ArenaHeroCard_yase",
			"ArenaHeroCard_yuji",
			"ArenaHeroCard_yunying",
			"ArenaHeroCard_zhangliang",
			"ArenaHeroCard_zhaoyun",
			"ArenaHeroCard_zhenji",
			"ArenaHeroCard_zhongkui",
			"ArenaHeroCard_zhugeliang",
		},
	},
	ArenaHeroDeraultCardTable = {
		ArenaHeroClassDataSubTables = {
			"ArenaHeroClassData",
		},
		ArenaHeroDeraultCardDataSubTables = {
			"ArenaHeroDeraultCardData",
		},
	},
	ArenaHeroOpenTable = {
		ArenaHeroOpenDataSubTables = {
			"ArenaHeroOpenData",
		},
	},
	ArenaHeroUnlockTable = {
		ArenaHeroUnlockSubTables = {
			"ArenaHeroUnlockData",
		},
		ArenaRobotBannedHeroSubTables = {
			"ArenaRobotBannedHero",
		},
	},
	ArenaHitTable = {
		ArenaHitDataSubTables = {
			"ArenaHitData",
		},
		ArenaHitEffectDataSubTables = {
			"ArenaHitEffectData",
		},
		ArenaHitEffectScaleDataSubTables = {
			"ArenaHitEffectScaleData",
		},
		ArenaTargetFilterDataSubTables = {
			"ArenaTargetFilterData",
		},
		ArenaTargetFilterRuleDataSubTables = {
			"ArenaTargetFilterRuleData",
		},
	},
	ArenaIndicatorTable = {
		ArenaIndicatorConfSubTables = {
			"ArenaIndicatorData",
		},
	},
	ArenaInteractMarkTable = {
		ArenaInteractMarkSubTables = {
			"ArenaInteractMark",
		},
	},
	ArenaLimitedTimeFreeHeroTable = {
		ArenaLimitedTimeFreeHeroDataSubTables = {
			"ArenaLimitedTimeFreeHeroData",
		},
	},
	ArenaMagicFieldTable = {
		ArenaMagicFieldSetSubTables = {
			"ArenaMagicFieldSet",
		},
	},
	ArenaMiscTable = {
		ArenaMiscConfDataSubTables = {
			"ArenaMiscConfData",
		},
	},
	ArenaMonsterTable = {
		ArenaMonsterDataSubTables = {
			"ArenaMonsterData",
		},
	},
	ArenaPaidFeatureTable = {
		ArenaInGamePortraitFrameSubTables = {
			"ArenaInGamePortraitFrame",
		},
		ArenaKillUISubTables = {
			"ArenaKillUI",
		},
	},
	ArenaQuickMessageTable = {
		ArenaQuickMessageDataSubTables = {
			"ArenaQuickMessageBattleData",
			"ArenaQuickMessageLevelData",
		},
	},
	ArenaRandomEventTable = {
		ArenaRandomEventConfigSubTables = {
			"ArenaRandomEventConfig",
		},
		ArenaRandomEventWeightSubTables = {
			"ArenaRandomEventWeight",
		},
	},
	ArenaRegisterTable = {
		ArenaHeroRegisterDataSubTables = {
			"ArenaHeroRegisterData",
		},
	},
	ArenaRoundTable = {
		ArenaRoundConfSubTables = {
			"ArenaRoundConf",
		},
	},
	ArenaSignTable = {
		ArenaSignConfSubTables = {
			"ArenaSignConf",
		},
	},
	ArenaSkillTable = {
		ArenaSkillConditionTypeSubTables = {
			"ArenaSkillConditionTypeData",
		},
		ArenaSkillConfSubTables = {
			"ArenaSkillConfData",
			"ArenaSkillConfData_DianWei",
			"ArenaSkillConfData_FinalMount",
			"ArenaSkillConfData_HaiYue",
			"ArenaSkillConfData_Mount",
			"ArenaSkillConfData_XiangYu",
			"ArenaSkillConfData_ZhangLiang",
			"ArenaSkillConfData_ZhenJi",
			"ArenaSkillConfData_ailin",
			"ArenaSkillConfData_ake",
			"ArenaSkillConfData_anqila",
			"ArenaSkillConfData_bingyi",
			"ArenaSkillConfData_caiwenji",
			"ArenaSkillConfData_chengyaojin",
			"ArenaSkillConfData_daji",
			"ArenaSkillConfData_daqiao",
			"ArenaSkillConfData_diaochan",
			"ArenaSkillConfData_direnjie",
			"ArenaSkillConfData_dongfangyao",
			"ArenaSkillConfData_donghuangtaiyi",
			"ArenaSkillConfData_duoliya",
			"ArenaSkillConfData_fashuzhaohuanshou",
			"ArenaSkillConfData_gongbenwuzang",
			"ArenaSkillConfData_gongsunli",
			"ArenaSkillConfData_houyi",
			"ArenaSkillConfData_houyi_RandomEvent",
			"ArenaSkillConfData_huamulan",
			"ArenaSkillConfData_jialuo",
			"ArenaSkillConfData_kai",
			"ArenaSkillConfData_kai_RandomEvent",
			"ArenaSkillConfData_lanlingwang",
			"ArenaSkillConfData_libai",
			"ArenaSkillConfData_liubei",
			"ArenaSkillConfData_liushan",
			"ArenaSkillConfData_lubanqihao",
			"ArenaSkillConfData_lubanqihao_RandomEvent",
			"ArenaSkillConfData_luopuxia",
			"ArenaSkillConfData_lvbu",
			"ArenaSkillConfData_makeboluo",
			"ArenaSkillConfData_maolingshaonv",
			"ArenaSkillConfData_mingshiyin",
			"ArenaSkillConfData_mozi",
			"ArenaSkillConfData_paopao",
			"ArenaSkillConfData_renzhe",
			"ArenaSkillConfData_shangguanwaner",
			"ArenaSkillConfData_summonerspells",
			"ArenaSkillConfData_sunce",
			"ArenaSkillConfData_sunshangxiang",
			"ArenaSkillConfData_sunwukong",
			"ArenaSkillConfData_tuteng",
			"ArenaSkillConfData_wangzhaojun",
			"ArenaSkillConfData_wangzhaojun_RandomEvent",
			"ArenaSkillConfData_wulizhaohuanwu",
			"ArenaSkillConfData_wusula",
			"ArenaSkillConfData_wuzetian",
			"ArenaSkillConfData_xiahoudun",
			"ArenaSkillConfData_xiaohonghu",
			"ArenaSkillConfData_xiaoqiao",
			"ArenaSkillConfData_xishi",
			"ArenaSkillConfData_yangyuhuan",
			"ArenaSkillConfData_yao",
			"ArenaSkillConfData_yase",
			"ArenaSkillConfData_yuji",
			"ArenaSkillConfData_yunying",
			"ArenaSkillConfData_zhaoyun",
			"ArenaSkillConfData_zhongkui",
			"ArenaSkillConfData_zhongkui_RandomEvent",
			"ArenaSkillConfData_zhugeliang",
			"HOKSkillConfData",
			"HOKSkillConfData_summoner",
		},
	},
	ArenaSkinEffectTable = {
		ArenaSkinEffectDataSubTables = {
			"ArenaSkinEffectData",
		},
		ArenaSkinSystemDataSubTables = {
			"ArenaSkinSystemData",
		},
	},
	ArenaSpecialMatchTable = {
		ArenaSpecialMatchCardSetSubTables = {
			"ArenaSpecialMatchCardSet",
		},
		ArenaSpecialMatchRoleSetSubTables = {
			"ArenaSpecialMatchRoleSet",
		},
		ArenaSpecialMatchRoundDamageConfigSubTables = {
			"ArenaSpecialMatchRoundDamageConfig",
		},
		ArenaSpecialMatchRoundMapSetSubTables = {
			"ArenaSpecialMatchRoundMapSet",
		},
		ArenaSpecialMatchRoundParamsConfigSubTables = {
			"ArenaSpecialMatchRoundParamsConfig",
		},
	},
	ArenaStackCountTable = {
		ArenaStackCountDataSubTables = {
			"ArenaStackCountConf",
		},
	},
	ArenaStrategyTable = {
		ArenaStrategyMapPointSubTables = {
			"ArenaStrategyMapPoint",
		},
		ArenaStrategyMapPointEntrySubTables = {
			"ArenaStrategyMapPointEntry",
		},
	},
	ArenaTipTable = {
		ArenaTipConfDataSubTables = {
			"ArenaTipConfData",
		},
		ArenaTipItemConfDataSubTables = {
			"ArenaTipItemConfData",
		},
	},
	ArenaVoiceTable = {
		ArenaVoiceSubTables = {
			"ArenaVoice",
		},
		ArenaVoiceTriggerSubTables = {
			"ArenaVoiceTrigger",
		},
	},
	ArenaWeaponTable = {
		ArenaWeaponSetSubTables = {
			"ArenaWeapon",
		},
	},
	BSEventTable = {
		BSGlobalEventConfSubTables = {
			"BSEvent_Global",
		},
		BSMapEventConfSubTables = {
			"BSEvent_Map",
		},
	},
	BSGlobalTable = {
		BSGlobalConfSubTables = {
			"BSGlobalConf",
		},
	},
	BSHeroListTable = {
		BSHeroListConfSubTables = {
			"BSHeroList",
		},
	},
	BSMapParamTable = {
		BSMapParamSubTables = {
			"BSMapParam_BS",
		},
	},
	BSPotionTable = {
		BSPotionConfSubTables = {
			"BSPotionConf",
		},
	},
	ExtraMallTable = {
		MallExtraHotRecommendPageConfSubTables = {
			"ExtraHotRecommendPageConf_Arena",
		},
		MallExtraConfSubTables = {
			"MallExtraConf_Arena",
		},
	},
	GameOptimizeSettingTable = {
		GameOptimizeSettingDataSubTables = {
			"GameOptimizeSettingData_Arena",
			"GameOptimizeSettingData_HOK",
		},
	},
	HOKBattleFOVTable = {
		HOKBattleConfSubTables = {
			"ResHOKBattleFOV",
		},
	},
	HOKBuffAreaTriggerTable = {
		HOKBuffAreaTriggerSubTables = {
			"HOKBuffAreaTriggerRow",
		},
	},
	HOKCardTable = {
		HOKCardDataSubTables = {
			"HOKCardData",
		},
	},
	HOKCardShopTable = {
		HOKCardFilterConfSubTables = {
			"HOKCardFilterConf",
		},
		HOKCardShopConfSubTables = {
			"HOKCardShopConf",
		},
		HOKCardShopPackageConfSubTables = {
			"HOKCardShopPackageConf",
		},
		HOKHeroCardPoolConfSubTables = {
			"HOKHeroCardPoolConf",
		},
		HOKHeroCardShopConfSubTables = {
			"HOKHeroCardShopConf",
		},
		HOKNewbieCardConfSubTables = {
			"HOKNewbieCardConf",
		},
	},
	HOKGlobalTable = {
		HOKGlobalConfSubTables = {
			"HOKGlobalConf",
		},
	},
	HOKGoldExpTable = {
		HOKDeathExpMakeUpConfSubTables = {
			"HOKDeathExpMakeUpConf",
		},
		HOKDeathGoldMakeUpConfSubTables = {
			"HOKDeathGoldMakeUpConf",
		},
		HOKExpMakeUpConfSubTables = {
			"HOKExpMakeUpConf",
		},
		HOKGoldExpConfSubTables = {
			"HOKGoldExpConf",
		},
		HOKGoldExpMiscConfSubTables = {
			"HOKGoldExpMisc",
		},
		HOKGoldExpTimeConfSubTables = {
			"HOKGoldExpTimeConf",
		},
		HOKGoldMakeUpConfSubTables = {
			"HOKGoldMakeUpConf",
		},
		HOKHeroGoldExpConfSubTables = {
			"HOKHeroGoldExpConf",
		},
	},
	HOKHeroBalanceTable = {
		HOKHeroBalanceConfSubTables = {
			"HOKHeroBalanceConf",
		},
	},
	HOKHeroLevelUpTable = {
		HOKHeroLevelUpSubTables = {
			"HOKHeroLevelUp",
		},
	},
	HOKHeroRebirthTable = {
		HOKHeroRebirthConfSubTables = {
			"HOKHeroRebirthConf",
		},
	},
	HOKHeroRobotTable = {
		HOKHeroRobotConfSubTables = {
			"HOKHeroRobotConf",
		},
		HOKHeroRobotIntensityConfSubTables = {
			"HOKHeroRobotIntensityConf",
		},
		HOKHeroRobotMiscConfSubTables = {
			"HOKHeroRobotMiscConf",
		},
		HOKHeroRobotPlayScriptConfSubTables = {
			"HOKHeroRobotPlayScriptConf",
		},
		HOKRobotDynamicBallanceConfDataSubTables = {
			"HOKRobotDynamicBallanceConf",
		},
	},
	HOKHeroUnlockTable = {
		HOKHeroUnlockConfSubTables = {
			"HOKHeroUnlockConf",
		},
	},
	HOKMonsterLevelTable = {
		ResHOKMonsterLevelSubTables = {
			"ResHOKMonsterLevel",
		},
	},
	HOKRiftPowerTable = {
		HOKRiftPowerConfSubTables = {
			"HOKRiftPowerConf",
		},
	},
	HOKSkillOverrideTable = {
		HOKSkillOverrideConfSubTables = {
			"HOKSkillOverrideConfData",
		},
	},
	HOKSoldierWaveTable = {
		HOKSoldierWaveDataSubTables = {
			"HOKSoldierWave",
		},
	},
	HOKTowerAtkGrowTable = {
		HOKTowerAtkGrowConfSubTables = {
			"HOKTowerAtkGrowConf",
		},
	},
	HokIconTable = {
		HokIconDataSubTables = {
			"HokIconData",
		},
	},
	HokMiscTable = {
		HokMiscDataSubTables = {
			"HokMiscData",
		},
	},
	MiscTable = {
		MiscConfArenaSubTables = {
			"MiscConfArena",
		},
	},
	ProtectedScoreTable = {
		ProtectedScoreAdditionalDataSubTables = {
			"ProtectedScoreAdditionalData_HOK",
			"ProtectedScoreAdditionalData_Moba",
		},
	},
	QualifyingTable = {
		QualifyingLevelDimensionConditionDataSubTables = {
			"QualifyingLevelDimensionConditionData_HOK",
			"QualifyingLevelDimensionConditionData_Moba",
		},
		QualifyingLevelDimensionScoreDataSubTables = {
			"QualifyingLevelDimensionScoreData_HOK",
		},
		QualifyingPerfScoreSubTables = {
			"QualifyingPerfScoreData_HOK",
			"QualifyingPerfScoreData_Moba",
		},
		SeasonQualifyingMailSubTables = {
			"SeasonQualifyingMailData_HOK",
		},
	},
}

return TableMap

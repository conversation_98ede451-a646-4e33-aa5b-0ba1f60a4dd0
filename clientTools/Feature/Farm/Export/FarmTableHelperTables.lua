--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_FarmAquariumSeatConf"] = {
        TableKey = "scale,idx",
        SubTableNames = {
			"FarmAquariumSeatConf",
        }
    },
    ["table_FarmBuffConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuffConf",
        }
    },
    ["table_FarmBuffEffectConf"] = {
        TableKey = "effectId",
        SubTableNames = {
			"FarmBuffEffectConf",
        }
    },
    ["table_FarmBuffFaceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuffFaceConf",
        }
    },
    ["table_FarmBuffSkillConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuffSkillConf",
        }
    },
    ["table_FarmBuildingDecorationEnvConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuildingDecorationEnvConf",
        }
    },
    ["table_FarmBuildingDecorationJumpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuildingDecorationJumpConf",
        }
    },
    ["table_FarmBuildingDecorationPrayConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuildingDecorationPrayConf",
        }
    },
    ["table_FarmBuildingLevelUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuildingLevelUpConf",
        }
    },
    ["table_FarmBuildingSkinConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuildingSkinConf",
        }
    },
    ["table_FarmBuildingTypeConf"] = {
        TableKey = "typeId",
        SubTableNames = {
			"FarmBuildingTypeConf",
        }
    },
    ["table_FarmCollectionHandbookConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCollectionHandbookConf",
        }
    },
    ["table_FarmCropConf"] = {
        TableKey = "cropType",
        SubTableNames = {
			"FarmCropConf_Animal",
			"FarmCropConf_Crop",
        }
    },
    ["table_FarmCropLevelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCropLevelConf_Animal",
			"FarmCropLevelConf_Crop",
        }
    },
    ["table_FarmCropMachineConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCropMachineConf",
        }
    },
    ["table_FarmDialogContent"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmDialogContent",
        }
    },
    ["table_FarmDialogContentUpdate"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmDialogContentUpdate",
        }
    },
    ["table_FarmDialogEffect"] = {
        TableKey = "effectID",
        SubTableNames = {
			"FarmDialogEffect",
        }
    },
    ["table_FarmDialogWildcard"] = {
        TableKey = "type",
        SubTableNames = {
			"FarmDialogWildcard",
        }
    },
    ["table_FarmFishBaitConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmFishBaitConf",
        }
    },
    ["table_FarmFishBowlConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmFishBowlConf",
        }
    },
    ["table_FarmFishCardPackConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmFishCardPackConf",
        }
    },
    ["table_FarmFishCardPackDropConf"] = {
        TableKey = "layer",
        SubTableNames = {
			"FarmFishCardPackDropConf",
        }
    },
    ["table_FarmFishConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmFishConf",
        }
    },
    ["table_FarmFishHandbookAwardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmFishHandbookAwardConf",
        }
    },
    ["table_FarmFishLevelConf"] = {
        TableKey = "fishType,level",
        SubTableNames = {
			"FarmFishLevelConf",
        }
    },
    ["table_FarmFishPeriodConf"] = {
        TableKey = "period",
        SubTableNames = {
			"FarmFishPeriodConf",
        }
    },
    ["table_FarmFishPoolLayerConf"] = {
        TableKey = "layer",
        SubTableNames = {
			"FarmFishPoolLayerConf",
        }
    },
    ["table_FarmFishPoolLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmFishPoolLevelConf",
        }
    },
    ["table_FarmFishScoreConf"] = {
        TableKey = "itemId",
        SubTableNames = {
			"FarmFishScoreConf",
        }
    },
    ["table_FarmGodFigureDropConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmGodFigureDropConfData",
        }
    },
    ["table_FarmGridLevelUpConf"] = {
        TableKey = "order",
        SubTableNames = {
			"FarmGridLevelUpConf",
        }
    },
    ["table_FarmGridLevelUpExpConf"] = {
        TableKey = "order",
        SubTableNames = {
			"FarmGridLevelUpExpConf",
        }
    },
    ["table_FarmItemConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmItemConf_Animal",
			"FarmItemConf_Base",
			"FarmItemConf_Building",
			"FarmItemConf_Crop",
			"FarmItemConf_Fish",
			"FarmItemConf_furniture",
        }
    },
    ["table_FarmKirinLevelUpConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmKirinLevelUpConf",
        }
    },
    ["table_FarmMonthcardDecoration"] = {
        TableKey = "ID",
        SubTableNames = {
			"FarmMonthcardDecoration",
        }
    },
    ["table_FarmNPCConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmNPCConf",
        }
    },
    ["table_FarmNPCDialogueConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmNPCDialogueConf",
        }
    },
    ["table_FarmNPCSerialConf"] = {
        TableKey = "serialId",
        SubTableNames = {
			"FarmNPCSerialConf",
        }
    },
    ["table_FarmNpcFish"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmNpcFish",
        }
    },
    ["table_FarmOperationConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmOperationConf",
        }
    },
    ["table_FarmPetClothingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmPetClothingConf_Body",
			"FarmPetClothingConf_Head",
			"FarmPetClothingConf_Neck",
			"FarmPetClothingConf_Suit",
        }
    },
    ["table_FarmPetClothingResourceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmPetClothingResourceConf",
        }
    },
    ["table_FarmPetConf"] = {
        TableKey = "confId",
        SubTableNames = {
			"FarmPetConf",
        }
    },
    ["table_FarmPrayToGodFigureResultConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmPrayToGodFigureResultConfData",
        }
    },
    ["table_FarmRoomConf"] = {
        TableKey = "templateId",
        SubTableNames = {
			"FarmRoomConf",
        }
    },
    ["table_FarmRoomDoorWallPaperConf"] = {
        TableKey = "wallpaperItemId,wallpaperSubState",
        SubTableNames = {
			"FarmRoomDoorWallPaperConf",
        }
    },
    ["table_FarmRoomExtendConf"] = {
        TableKey = "buildingTypeId,templateId,needMainLevel",
        SubTableNames = {
			"FarmRoomExtendConf",
        }
    },
    ["table_FarmRoomFurnitureConf"] = {
        TableKey = "itemId,subState",
        SubTableNames = {
			"FarmRoomFurnitureConf",
        }
    },
    ["table_FarmRoomFurnitureSkinConf"] = {
        TableKey = "itemId,subState",
        SubTableNames = {
			"FarmRoomFurnitureSkinConf",
        }
    },
    ["table_FarmRoomFurnitureSkinMapConf"] = {
        TableKey = "itemId",
        SubTableNames = {
			"FarmRoomFurnitureSkinMapConf",
        }
    },
    ["table_FarmRoomInitItem"] = {
        TableKey = "uniqueId",
        SubTableNames = {
			"FarmRoomInitItem",
        }
    },
    ["table_FarmRoomItemClassifyConf"] = {
        TableKey = "firstLevelType",
        SubTableNames = {
			"FarmRoomItemClassifyConf",
        }
    },
    ["table_FarmRoomItemModelSizeConf"] = {
        TableKey = "bpName",
        SubTableNames = {
			"FarmRoomItemModelSizeConf",
        }
    },
    ["table_FarmSysConf"] = {
        TableKey = "ID",
        SubTableNames = {
			"FarmSysConf",
        }
    },
    ["table_FarmTalentConf"] = {
        TableKey = "id,level",
        SubTableNames = {
			"FarmTalentConf",
        }
    },
    ["table_FarmTaskInfoConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmTaskInfoConf",
        }
    },
    ["table_FarmTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmTextConfData",
        }
    },
    ["table_FarmTileLevelConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmTileLevelConfig",
        }
    },
    ["table_FarmVileplumeLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmVileplumeLevelConf",
        }
    },
    ["table_FarmVillagerConf"] = {
        TableKey = "confId",
        SubTableNames = {
			"FarmVillagerConf",
        }
    },
    ["table_FarmVillagerDialogueConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmVillagerDialogueConf",
        }
    },
    ["table_FarmVillagerFestivalGiftConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmVillagerFestivalGiftConf",
        }
    },
    ["table_FarmVillagerInteractionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmVillagerInteractionConf",
        }
    },
    ["table_FarmVillagerLimitConf"] = {
        TableKey = "farmLv",
        SubTableNames = {
			"FarmVillagerLimitConf",
        }
    },
    ["table_FarmVillagerSpecialDialogueConf"] = {
        TableKey = "confId",
        SubTableNames = {
			"FarmVillagerSpecialDialogueConf",
        }
    },
    ["table_FarmWarningConf"] = {
        TableKey = "farmLv",
        SubTableNames = {
			"FarmWarningConf",
        }
    },
    ["table_FarmWeatherConf"] = {
        TableKey = "weatherId",
        SubTableNames = {
			"FarmWeatherConf",
        }
    },
}

return TableHelperTables
-- This file is automatically generated

local TableMap = {
	ChatTable = {
		FarmTextConfDataSubTables = {
			"FarmTextConfData",
		},
	},
	FarmBuffTable = {
		FarmBuffConfSubTables = {
			"FarmBuffConf",
		},
		FarmBuffEffectConfSubTables = {
			"FarmBuffEffectConf",
		},
		FarmBuffFaceConfSubTables = {
			"FarmBuffFaceConf",
		},
		FarmBuffSkillConfSubTables = {
			"FarmBuffSkillConf",
		},
	},
	FarmBuildingTable = {
		FarmBuildingLevelUpConfSubTables = {
			"FarmBuildingLevelUpConf",
		},
		FarmBuildingSkinConfSubTables = {
			"FarmBuildingSkinConf",
		},
		FarmBuildingTypeConfSubTables = {
			"FarmBuildingTypeConf",
		},
		FarmGridLevelUpConfSubTables = {
			"FarmGridLevelUpConf",
		},
		FarmGridLevelUpExpConfSubTables = {
			"FarmGridLevelUpExpConf",
		},
	},
	FarmBuildingDecorationTable = {
		FarmBuildingDecorationEnvConfSubTables = {
			"FarmBuildingDecorationEnvConf",
		},
		FarmBuildingDecorationJumpConfSubTables = {
			"FarmBuildingDecorationJumpConf",
		},
		FarmBuildingDecorationPrayConfSubTables = {
			"FarmBuildingDecorationPrayConf",
		},
	},
	FarmCollectionTable = {
		FarmCollectionHandbookConfSubTables = {
			"FarmCollectionHandbookConf",
		},
	},
	FarmCropTable = {
		FarmCropConfSubTables = {
			"FarmCropConf_Animal",
			"FarmCropConf_Crop",
		},
		FarmCropLevelConfSubTables = {
			"FarmCropLevelConf_Animal",
			"FarmCropLevelConf_Crop",
		},
		FarmVileplumeLevelConfSubTables = {
			"FarmVileplumeLevelConf",
		},
	},
	FarmCropMachineTable = {
		FarmCropMachineConfSubTables = {
			"FarmCropMachineConf",
		},
	},
	FarmDialogContentTable = {
		FarmDialogContentSubTables = {
			"FarmDialogContent",
		},
		FarmDialogContentUpdateSubTables = {
			"FarmDialogContentUpdate",
		},
		FarmDialogEffectSubTables = {
			"FarmDialogEffect",
		},
		FarmDialogWildcardSubTables = {
			"FarmDialogWildcard",
		},
	},
	FarmEventTable = {
		FarmNpcFishSubTables = {
			"FarmNpcFish",
		},
	},
	FarmFishTable = {
		FarmAquariumSeatConfSubTables = {
			"FarmAquariumSeatConf",
		},
		FarmFishBaitConfSubTables = {
			"FarmFishBaitConf",
		},
		FarmFishBowlConfSubTables = {
			"FarmFishBowlConf",
		},
		FarmFishCardPackConfSubTables = {
			"FarmFishCardPackConf",
		},
		FarmFishCardPackDropConfSubTables = {
			"FarmFishCardPackDropConf",
		},
		FarmFishConfSubTables = {
			"FarmFishConf",
		},
		FarmFishHandbookAwardConfSubTables = {
			"FarmFishHandbookAwardConf",
		},
		FarmFishLevelConfSubTables = {
			"FarmFishLevelConf",
		},
		FarmFishPeriodConfSubTables = {
			"FarmFishPeriodConf",
		},
		FarmFishPoolLayerConfSubTables = {
			"FarmFishPoolLayerConf",
		},
		FarmFishPoolLevelConfSubTables = {
			"FarmFishPoolLevelConf",
		},
		FarmFishRarityConfSubTables = {
			"FarmFishRarityConf",
		},
		FarmFishScoreConfSubTables = {
			"FarmFishScoreConf",
		},
	},
	FarmItemTable = {
		FarmItemConfSubTables = {
			"FarmItemConf_Animal",
			"FarmItemConf_Base",
			"FarmItemConf_Building",
			"FarmItemConf_Crop",
			"FarmItemConf_Fish",
			"FarmItemConf_furniture",
		},
		FarmRoomItemModelSizeConfSubTables = {
			"FarmRoomItemModelSizeConf",
		},
	},
	FarmKirinTable = {
		FarmKirinLevelUpConfSubTables = {
			"FarmKirinLevelUpConf",
		},
	},
	FarmMonthcardDecorationTable = {
		FarmMonthcardDecorationSubTables = {
			"FarmMonthcardDecoration",
		},
	},
	FarmNPCTable = {
		FarmGodFigureDropConfSubTables = {
			"FarmGodFigureDropConfData",
		},
		FarmNPCConfSubTables = {
			"FarmNPCConf",
		},
		FarmNPCDialogueConfSubTables = {
			"FarmNPCDialogueConf",
		},
		FarmNPCSerialConfSubTables = {
			"FarmNPCSerialConf",
		},
		FarmPrayToGodFigureResultConfSubTables = {
			"FarmPrayToGodFigureResultConfData",
		},
	},
	FarmOperationTable = {
		FarmOperationConfSubTables = {
			"FarmOperationConf",
		},
	},
	FarmPetTable = {
		FarmPetClothingConfSubTables = {
			"FarmPetClothingConf_Body",
			"FarmPetClothingConf_Head",
			"FarmPetClothingConf_Neck",
			"FarmPetClothingConf_Suit",
		},
		FarmPetClothingResourceConfSubTables = {
			"FarmPetClothingResourceConf",
		},
		FarmPetConfSubTables = {
			"FarmPetConf",
		},
	},
	FarmRoomTable = {
		FarmRoomConfSubTables = {
			"FarmRoomConf",
		},
		FarmRoomDoorWallPaperConfSubTables = {
			"FarmRoomDoorWallPaperConf",
		},
		FarmRoomExtendConfSubTables = {
			"FarmRoomExtendConf",
		},
		FarmRoomFurnitureConfSubTables = {
			"FarmRoomFurnitureConf",
		},
		FarmRoomFurnitureSkinConfSubTables = {
			"FarmRoomFurnitureSkinConf",
		},
		FarmRoomFurnitureSkinMapConfSubTables = {
			"FarmRoomFurnitureSkinMapConf",
		},
		FarmRoomInitItemSubTables = {
			"FarmRoomInitItem",
		},
		FarmRoomItemClassifyConfSubTables = {
			"FarmRoomItemClassifyConf",
		},
	},
	FarmSysConfTable = {
		FarmSysConfSubTables = {
			"FarmSysConf",
		},
	},
	FarmTalentTable = {
		FarmTalentConfSubTables = {
			"FarmTalentConf",
		},
	},
	FarmTaskTable = {
		FarmTaskInfoConfSubTables = {
			"FarmTaskInfoConf",
		},
	},
	FarmVillagerTable = {
		FarmVillagerConfSubTables = {
			"FarmVillagerConf",
		},
		FarmVillagerDialogueConfSubTables = {
			"FarmVillagerDialogueConf",
		},
		FarmVillagerFestivalGiftConfSubTables = {
			"FarmVillagerFestivalGiftConf",
		},
		FarmVillagerGiftDataConfSubTables = {
			"FarmVillagerGiftDataConf",
		},
		FarmVillagerInteractionConfSubTables = {
			"FarmVillagerInteractionConf",
		},
		FarmVillagerLimitConfSubTables = {
			"FarmVillagerLimitConf",
		},
		FarmVillagerSpecialDialogueConfSubTables = {
			"FarmVillagerSpecialDialogueConf",
		},
	},
	FarmWarningTable = {
		FarmWarningConfSubTables = {
			"FarmWarningConf",
		},
	},
	FarmWeatherTable = {
		FarmWeatherConfSubTables = {
			"FarmWeatherConf",
		},
	},
	TerrainTable = {
		FarmTileLevelConfigSubTables = {
			"FarmTileLevelConfig",
		},
	},
}

return TableMap

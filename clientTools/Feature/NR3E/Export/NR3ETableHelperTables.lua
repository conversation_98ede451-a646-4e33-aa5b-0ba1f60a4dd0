--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_NR3EGameConfigData"] = {
        TableKey = "ConfigName",
        SubTableNames = {
			"NR3E1GameConfig",
			"NR3E2GameConfig",
        }
    },
    ["table_NR3E1LevelPropData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E1LevelPropData",
        }
    },
    ["table_NR3E1LevelPropRefreshData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E1LevelPropRefreshData",
        }
    },
    ["table_NR3E1MapModuleData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E1MapModuleData",
        }
    },
    ["table_NR3E1MapModuleMapData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E1MapModuleMapData",
        }
    },
    ["table_NR3E1MapModulePoolData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E1MapModulePoolData",
        }
    },
    ["table_NR3E3Preparations_RandomPackData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3Preparations_RandomPackData",
        }
    },
    ["table_NR3E3RandomPackData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3RandomPackData",
        }
    },
    ["table_NR3E3RandomReturnData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3RandomReturnData",
        }
    },
    ["table_NR3E3TipsData"] = {
        TableKey = "index",
        SubTableNames = {
			"NR3E3TipsData",
        }
    },
    ["table_NR3E3VocationGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3VocationGroupData",
        }
    },
}

return TableHelperTables
-- This file is automatically generated

local TableMap = {
	NR3E1LevelPropTable = {
		NR3E1LevelPropDataSubTables = {
			"NR3E1LevelPropData",
		},
		NR3E1LevelPropRefreshDataSubTables = {
			"NR3E1LevelPropRefreshData",
		},
	},
	NR3E1MapModuleTable = {
		NR3E1MapModuleDataSubTables = {
			"NR3E1MapModuleData",
		},
		NR3E1MapModuleMapDataSubTables = {
			"NR3E1MapModuleMapData",
		},
		NR3E1MapModulePoolDataSubTables = {
			"NR3E1MapModulePoolData",
		},
	},
	NR3E3TipsTable = {
		NR3E3TipsDataSubTables = {
			"NR3E3TipsData",
		},
	},
	NR3E3VocationTable = {
		NR3E3Preparations_RandomPackDataSubTables = {
			"NR3E3Preparations_RandomPackData",
		},
		NR3E3RandomPackDataSubTables = {
			"NR3E3RandomPackData",
		},
		NR3E3RandomReturnDataSubTables = {
			"NR3E3RandomReturnData",
		},
		NR3E3VocationGroupDataSubTables = {
			"NR3E3VocationGroupData",
		},
	},
	NR3EGameConfigTable = {
		NR3EGameConfigDataSubTables = {
			"NR3E1GameConfig",
			"NR3E2GameConfig",
		},
	},
}

return TableMap

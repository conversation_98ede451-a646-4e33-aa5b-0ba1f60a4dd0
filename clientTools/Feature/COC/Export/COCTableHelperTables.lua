--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_COCBuffAudioConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuffAudioConfData",
        }
    },
    ["table_COCBuffConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuffConfData",
        }
    },
    ["table_COCBuildConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuildConf",
        }
    },
    ["table_COCBuildingAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuildingAudioConfig",
        }
    },
    ["table_COCBuildingAvatarEffectConf"] = {
        TableKey = "fashionScore",
        SubTableNames = {
			"COCBuildingAvatarEffectConf",
        }
    },
    ["table_COCBuildingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuildingConf",
        }
    },
    ["table_COCBuildingDisplayAttrConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuildingDisplayAttrConf",
        }
    },
    ["table_COCBuildingFunctionBtnConf"] = {
        TableKey = "functionId",
        SubTableNames = {
			"COCBuildingFunctionBtnConf",
        }
    },
    ["table_COCBuildingLevelConf"] = {
        TableKey = "buildingTypeId,level",
        SubTableNames = {
			"COCBuildingLevelConf",
        }
    },
    ["table_COCBuildingMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBuildingMiscConf",
        }
    },
    ["table_COCBuildingRevampConf"] = {
        TableKey = "revampID",
        SubTableNames = {
			"COCBuildingRevampConf",
        }
    },
    ["table_COCBuildingRevampLevelConf"] = {
        TableKey = "revampID,level",
        SubTableNames = {
			"COCBuildingRevampLevelConf",
        }
    },
    ["table_COCBuildingTypeConf"] = {
        TableKey = "typeId",
        SubTableNames = {
			"COCBuildingTypeConf",
        }
    },
    ["table_COCCampaginConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCCampaginConf",
        }
    },
    ["table_COCChallengeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCChallengeConf",
        }
    },
    ["table_COCEnvAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCEnvAudioConfig",
        }
    },
    ["table_COCGameTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCGameTypeData",
        }
    },
    ["table_COCItemConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCItemConf",
        }
    },
    ["table_COCJumpConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCJumpConfig",
        }
    },
    ["table_COCLevelDefenseBornPointConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCLevelDefenseBornPointConf",
        }
    },
    ["table_COCLevelDefenseCameraConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCLevelDefenseCameraConf",
        }
    },
    ["table_COCLevelDefenseConf"] = {
        TableKey = "levelId",
        SubTableNames = {
			"COCLevelDefenseConf",
        }
    },
    ["table_COCLevelDefenseMisConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCLevelDefenseMisConfData",
        }
    },
    ["table_COCLevelInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCLevelInfoData",
        }
    },
    ["table_COCMapConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMapConfig",
        }
    },
    ["table_COCMapMinorRegionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMapMinorRegionConf",
        }
    },
    ["table_COCMapPrimaryRegionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMapPrimaryRegionConf",
        }
    },
    ["table_COCMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMiscConfData",
        }
    },
    ["table_COCMonsterData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMonsterData",
        }
    },
    ["table_COCNumPropBufferBaseValueConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCNumPropBufferBaseValueConf",
        }
    },
    ["table_COCPrisonMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCPrisonMiscConf",
        }
    },
    ["table_COCProjectileAudioConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCProjectileAudioConfData",
        }
    },
    ["table_COCProjectileConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCProjectileConfData",
        }
    },
    ["table_COCPropsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCPropsConfData",
        }
    },
    ["table_COCProsperityLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"COCProsperityLevelConf",
        }
    },
    ["table_COCSfxAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCSfxAudioConfig",
        }
    },
    ["table_COCSkillAudioConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCSkillAudioConfData",
        }
    },
    ["table_COCSkillConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCSkillConfData",
        }
    },
    ["table_COCSoldierAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCSoldierAudioConfig",
        }
    },
    ["table_COCSpecialBuildingBuildCostConf"] = {
        TableKey = "buildingTypeId,index",
        SubTableNames = {
			"COCSpecialBuildingBuildCostConf",
        }
    },
    ["table_COCStrollerData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCStrollerData",
        }
    },
    ["table_COCTeamData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCTeamData",
        }
    },
    ["table_COCTriggerAudioConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCTriggerAudioConfData",
        }
    },
    ["table_COCTriggerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCTriggerConfData",
        }
    },
    ["table_COCUIAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"COCUIAudioConfig",
        }
    },
    ["table_COCVillagerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCVillagerConf",
        }
    },
    ["table_COCVillagerDialogueConf"] = {
        TableKey = "dialogueId",
        SubTableNames = {
			"COCVillagerDialogueConf",
        }
    },
    ["table_COCVillagerDressFashionValueConf"] = {
        TableKey = "villagerId,fashionLevel",
        SubTableNames = {
			"COCVillagerDressFashionValueConf",
        }
    },
    ["table_COCVillagerFavorConf"] = {
        TableKey = "villagerId,favorLevel",
        SubTableNames = {
			"COCVillagerFavorConf",
        }
    },
    ["table_COCVillagerMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCVillagerMiscConf",
        }
    },
    ["table_COCVillagerOfficialPosConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCVillagerOfficialPosConf",
        }
    },
    ["table_CocDialogueConf"] = {
        TableKey = "dialogueID",
        SubTableNames = {
			"CocDialogueConfData",
        }
    },
    ["table_CocFeatureUnlockConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"CocFeatureUnlockConfData",
        }
    },
    ["table_CocFightSettleRewardConf"] = {
        TableKey = "destroyId",
        SubTableNames = {
			"CocFightSettleRewardConfData",
        }
    },
    ["table_CocMatchRobotConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocMatchRobotConfData",
        }
    },
    ["table_CocMatchRobotTemplateConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocMatchRobotTemplateConfData",
        }
    },
    ["table_CocMonthCardData"] = {
        TableKey = "id",
        SubTableNames = {
			"CocMonthCardData",
        }
    },
    ["table_CocRankingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocRankingConfData",
        }
    },
    ["table_CocRankingMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocRankingMiscConfData",
        }
    },
    ["table_CocRewardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocRewardConfData",
        }
    },
    ["table_CocScienceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocScienceConfData",
        }
    },
    ["table_CocScienceMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocScienceMiscConfData",
        }
    },
    ["table_CocSoldierCapacityConf"] = {
        TableKey = "monsterId",
        SubTableNames = {
			"CocSoldierCapacityConfData",
        }
    },
    ["table_CocSoldierConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocSoldierConfData",
        }
    },
    ["table_CocSoldierMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocSoldierMiscConfData",
        }
    },
    ["table_CocTaskConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocTaskConfData",
        }
    },
    ["table_CocTaskListConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocTaskListConfData",
        }
    },
    ["table_CocTreasureBoxCapacityConf"] = {
        TableKey = "capacity",
        SubTableNames = {
			"CocTreasureBoxCapacityConfData",
        }
    },
    ["table_CocTreasureBoxConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CocTreasureBoxConfData",
        }
    },
}

return TableHelperTables
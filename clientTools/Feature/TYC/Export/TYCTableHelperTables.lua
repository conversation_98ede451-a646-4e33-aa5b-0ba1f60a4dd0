--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_StepGuideData"] = {
        TableKey = "StepID",
        SubTableNames = {
			"StepGuideData_tyc",
        }
    },
    ["table_TDMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDMiscConfData",
        }
    },
    ["table_TDSCoinDropData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSCoinDropData",
        }
    },
    ["table_TDSCoinGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSCoinGroupData",
        }
    },
    ["table_TDSMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSMiscConfData",
        }
    },
    ["table_TDSMonsterPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSMonsterPoolData",
        }
    },
    ["table_TDSMonsterWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSMonsterWaveData",
        }
    },
    ["table_TDSWeaponLevelUpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSWeaponLevelUpConfData",
        }
    },
    ["table_TDSWeaponWaveConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TDSWeaponWaveConfData",
        }
    },
    ["table_TYCCarVehicleData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCCarVehicleData",
        }
    },
    ["table_TYCCrystalsStoreConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCCrystalsStoreConf",
        }
    },
    ["table_TYCDroneClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCDroneClientConf",
        }
    },
    ["table_TYCFixWingVehicleData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCFixWingVehicleData",
        }
    },
    ["table_TYCHelicopterVehicleData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCHelicopterVehicleData",
        }
    },
    ["table_TYCItemChangeReasonConf"] = {
        TableKey = "reason",
        SubTableNames = {
			"TYCItemChangeReasonConf",
        }
    },
    ["table_TYCMapMonsterWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMapMonsterWaveData",
        }
    },
    ["table_TYCMapParaConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMapParaConf",
        }
    },
    ["table_TYCMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMiscConfData",
        }
    },
    ["table_TYCMonsterBigWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMonsterBigWaveData",
        }
    },
    ["table_TYCMonsterConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMonsterConfData",
        }
    },
    ["table_TYCMonsterData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMonsterData",
        }
    },
    ["table_TYCMonsterSmallWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCMonsterSmallWaveData",
        }
    },
    ["table_TYCNormalBuildingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCNormalBuildingConf",
        }
    },
    ["table_TYCRatingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCRatingConf",
        }
    },
    ["table_TYCRebirthBossWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCRebirthBossWaveData",
        }
    },
    ["table_TYCRebirthMonsterWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCRebirthMonsterWaveData",
        }
    },
    ["table_TYCReincarnateNormalLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"TYCReincarnateNormalLevelConf",
        }
    },
    ["table_TYCReincarnatePeakLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"TYCReincarnatePeakLevelConf",
        }
    },
    ["table_TYCScientistClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCScientistClientConf",
        }
    },
    ["table_TYCSkinConf"] = {
        TableKey = "skinId",
        SubTableNames = {
			"TYCSkinConf",
        }
    },
    ["table_TYCTowerClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCTowerClientConf",
        }
    },
    ["table_TYCWaveChartData"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCWaveChartData",
        }
    },
    ["table_TYCWeaponAttrConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCWeaponAttrConfData",
        }
    },
    ["table_TYCWeaponClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCWeaponClientConfData",
        }
    },
    ["table_TYCWeaponSkinConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCWeaponSkinData",
        }
    },
}

return TableHelperTables
# -*- coding: utf-8 -*-

import sys

sys.path.append("..")

import ResKeywords_pb2
import convUtils
import ResActivityBookOfFriends_pb2

def check(msg, **kwargs):
    pass

def assemble(msg, **kwargs):
    TypeAssesmble(msg)(**kwargs)

class TypeAssesmble(object):
    def __init__(self, msg):
        self._msg = msg

    def __call__(self, **kwargs):
        if self._checkIllegal():
            #raise Exception(f"{self} check illegal with meta: {kwargs}")
            convUtils.CheckError.record(kwargs.get('sheet', None), f"row:{convUtils.humaneRowMeta(**kwargs)} {self} check illegal")
            return
        
        try:
            typeName = ResActivityBookOfFriends_pb2.ActBookOfFriendsContractType.Name(self._msg.type)
        except:
            convUtils.CheckError.record(kwargs.get('sheet', None), f"row:{convUtils.humaneRowMeta(**kwargs)} {self} type illegal")
            return

        if self._msg.creditLowerBound < 0:
            convUtils.CheckError.record(kwargs.get('sheet', None), f"row:{convUtils.humaneRowMeta(**kwargs)} {self} has bad credit lower bound")
            return 
        
        if self._msg.creditUpperBound > 0 and self._msg.creditLowerBound > self._msg.creditUpperBound:
            convUtils.CheckError.record(kwargs.get('sheet', None), f"row:{convUtils.humaneRowMeta(**kwargs)} {self} has bad credit lower and upper bound")
            return
            
    def __str__(self):
        return convUtils.dumpPbMessage(self._msg)

    def _checkIllegal(self):
        return False 
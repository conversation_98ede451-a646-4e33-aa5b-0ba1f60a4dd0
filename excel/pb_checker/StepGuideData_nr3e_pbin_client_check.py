import ResCheckUtils
from StepGuideDataConfig import StepID_data
from GuideSwitchConfig import checkSwitchOn
def check(table, **kwargs):
    if checkSwitchOn:
        ResCheckUtils.check_field_uniq(table, "StepID")
        lowLimit = None
        upLimit = None 
        for data in StepID_data:
            if data['name'] == "nr3e":
                lowLimit = data['lower_limit']
                upLimit = data['upper_limit']
                break
        ResCheckUtils.check_id_range(table,"StepID",lowLimit,upLimit) 
    else:
        print('注意：checkSwitchOn参数设置为False，跳过'+table.DESCRIPTOR.name+'的检查！！')
    return True

syntax = "proto2";

option cc_generic_services = false;

package com.tencent.wea.xlsRes;

import "ResKeywords.proto";


message Mayday_TaskPoolConfData //任务池配置
{
    option (resKey) = "TaskID";
    optional int32 TaskID                         =1;//任务ID
    optional int32 PoolType                           =2;//所在任务池
    optional int32 TaskType                    =3;//任务类型
    optional int32 OccupationalType                           =4;//职业类型
    optional string Description                        =5;//任务描述
    optional int32 TaskDifficulty                        =6;//任务难度
    optional int32 TaskWeight                        =7;//随机权重
}

message table_Mayday_TaskPoolConfData
{
    repeated  Mayday_TaskPoolConfData rows = 1;
}

message Mayday_TaskWeightConfData //任务权重配置
{
    option (resKey) = "WeightID";
    optional int32 WeightID 		 =1;//权重编号
    optional int32 Weight                      =2;//任务权重
    optional int32 PDifficulty                         =3;//个人任务难度
    optional int32 HDifficulty                       =4;//互助任务难度
    optional int32 MDifficulty                       =5;//地图任务难度
}

message table_Mayday_TaskWeightConfData
{
    repeated Mayday_TaskWeightConfData rows = 1;
}







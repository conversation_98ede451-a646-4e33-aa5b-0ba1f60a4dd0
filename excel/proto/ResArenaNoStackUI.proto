syntax = "proto2";

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

enum ArenaNoStackUITargetType
{
  Arena_NoStackUITargetType_Enemy = 0 [(name) = "敌方"];
  Arena_NoStackUITargetType_Friend = 1 [(name) = "己方"];
}

message ArenaNoStackUIConf{// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 role_id = 2;                             // 英雄id
  optional int32 avatar_id = 3;                           // 皮肤id
  repeated int32 buff_group_id = 4;                       // 触发此效果的可能的buff组id
  optional string umg_name = 5; 	                        // 叠层umg资产名称
  repeated string animations = 7;                         // 动画的名称（当数组用）
  repeated bool is_anim_loop = 8;                         //对应动画是否循环播放
  optional ArenaNoStackUITargetType target_type = 9;   //目标类型
  optional bool is_show_character = 10;                   //是否在角色血条上显示
  optional bool is_show_monster = 11;                     //是否在怪物血条上显示
  optional bool is_show_soldier = 12;                     //是否在士兵血条上显示
  optional int32 custom_group_id = 13;                    // 自定义组id
  optional bool is_default = 14;                          // 是否英雄的默认配置
}

message table_ArenaNoStackUIConf {// @noSvr
  repeated ArenaNoStackUIConf rows = 1;
}
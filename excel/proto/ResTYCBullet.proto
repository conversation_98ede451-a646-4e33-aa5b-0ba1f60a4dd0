syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";

message TYCBulletConf{// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional float damage = 2; //伤害
  optional float speed = 3; // 速度
  optional float  g = 4; // 重力加速度
  optional float  radius = 5; // 子弹半径
  optional float maxRange = 6; // 最大飞行距离
  optional string resPath = 7; // 资源路径
  repeated int32 triggerId = 8; // triggerId
  optional int32 buffId = 9; // triggerId
  optional int32 flyAudioId = 10;// 飞跃声音
  optional float HitAudioId = 11;//击打音声
  optional float effectScale = 12;//特效缩放
  optional bool isFollow = 13;// 是否是跟踪型的子弹
  optional int32 attrType      = 14;   //伤害类型
  repeated int32 hitBuffList   = 15;   //弹射子弹击中buff列表
  repeated KeyValueIntFloat trapDamageMap = 16; // 陷阱伤害百分比
}

message table_TYCBulletConf { // @noSvr
  repeated TYCBulletConf rows = 1;
}
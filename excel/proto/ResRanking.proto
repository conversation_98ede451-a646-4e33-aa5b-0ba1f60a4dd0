syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "google/protobuf/timestamp.proto";
import "ResKeywords.proto";
import "ResCommon.proto";

message RankingDisplayConf {
  option (resKey) = "labelId";
  optional int32 labelId = 1;
  optional string name = 2;// @noSvr
  optional bool isActive = 3;
  optional int32 firstTabIndex = 4;// @noSvr
  optional int32 secondTabIndex = 5;// @noSvr
  optional int32 delayDays = 6;
  repeated int32 rankIds = 8;
  optional int32 tipsSwitch = 9;// @noSvr
  repeated string tipsId = 10;// @noSvr
  optional string lowerVersion = 11;
  optional string higherVersion = 12;
  optional bool timeLimitSwitch = 13;// @noSvr
  optional google.protobuf.Timestamp beginTime = 14;    // 兼容性配置, 后续使用timeConstraints
  optional google.protobuf.Timestamp endTime = 15;      // 兼容性配置, 后续使用timeConstraints
  repeated TimeConstraint timeConstraints = 16;
  optional string childWndName = 17;// @noSvr 子界面
  optional string childwidgetName = 18;// @noSvr 子界面逻辑
  optional int32 firstTabShowIndex = 19;// @noSvr firstTab显示顺序
  optional bool RankSeasonTips = 20;// @noSvr 赛季重置问题是否显示
  optional bool CanShowGlobal = 21; // @noSvr 是否显示全服页签
  optional bool CanShowFriend = 22; // @noSvr 是否显示好友页签
  optional bool CanShowGeo = 23; // @noSvr 是否显示地区
  optional int32 GeoLevel = 24; // @noSvr 地区显示的级数
  optional int32 pakCheck = 25; // @noSvr 是否要下载分包
  repeated RankingGroup rankingGroupList = 26; // @noSvr 排行榜分组
  optional string CustomUI = 27;// @noSvr 自定义UI
  optional string DataUI = 28;// @noSvr 数据显示UI
  optional string BottomUI = 29;// @noSvr 右下角UI
  repeated int32 MatchIDList = 30;// @noSvr 玩法ID
  optional bool isShowRewardPreview = 31;// @noSvr 是否展示奖励预览
  optional int32 maxFetchCount = 32;// @noSvr 榜单最大拉取数量
  optional string bottomTips = 33;// @noSvr 底部提示文本

  optional string reservedField0 = 90;  // 预留字段
  optional string reservedField1 = 91;
  optional string reservedField2 = 92;
  optional string reservedField3 = 93;
  optional string reservedField4 = 94;
}

message table_RankingDisplayConfData {
  repeated RankingDisplayConf rows = 1;
}

message RankSnapshotRange {
  optional int32 fromSeasonId = 1;
  optional int32 toSeasonId = 2;

  optional google.protobuf.Timestamp dailyFromSeasonId = 3;   // 映射策略为每日
  optional google.protobuf.Timestamp dailyToSeasonId = 4;     // 映射策略为每日

  optional int32 rewardConfId = 5;
}

message RankingConf {
  option (resKey) = "rankId";
  optional int32 rankId = 1;
  optional RankRule rule = 2;
  optional int32 levelId = 3;
  optional bool isShow = 5;
  repeated int32 apolloIds = 6;//@noCli
  optional RankingUgcIdConf ugc = 7;
  optional int32 apolloShard = 8;//@noCli
  optional int32 activityId = 9;
  optional int32 descId = 10;   // 自定义
  optional RankSeasonMap seasonMap = 11;

  optional RankSnapshotType snapshot = 12;   // 快照类型，已废弃
  repeated RankSnapshotRange snapshotRange = 13; // 快照覆盖赛季，已废弃
  optional RankingDailyZSetSnapshotConf dzSnapshot = 14;

  optional int32 friendBackend = 15;
  optional int32 globalBackend = 16;
  optional int32 geoBackend = 17;

  optional string reservedField0 = 90;  // 预留字段
  optional string reservedField1 = 91;
  optional string reservedField2 = 92;
  optional string reservedField3 = 93;
  optional string reservedField4 = 94;
}

message RankingDailyZSetSnapshotConf {
  optional int32 capacity = 1;
  optional DailyTimeInfo triggerTime = 2;
}

message table_RankingConfData {
  repeated RankingConf rows = 1;
}

message RankingRuleConf {//@noCli
  option (resKey) = "rule";
  optional RankRule rule = 1;
  optional RankOrderingType ordering = 2;       // 排序规则
  optional RankUpdatePolicy updatePolicy = 3;   // 更新规则
  optional bool refreshWhenSeasonChange = 4;
  optional int32 scoreFieldLength = 5;
  optional RankApolloIdMap apolloIdMap = 6;     // TopN/TopNext榜单ID映射方式
  optional BattleEventType levelScoreType = 8;  // 关卡结算数据类型
  optional RankOrderingType tsOrdering = 9;     // 同分情况下，时间戳的排序规则

  optional RankingReadWriteType friendRWT = 10;
  optional RankingReadWriteType globalRWT = 11;
  optional RankingReadWriteType geoRWT = 12;    // 地区排行榜GameSvr读写权限

  optional RankSeasonMap defaultSeasonMap = 13; // 默认赛季类型
  optional bool useSubScores = 14;

  optional int32 defaultFriendBackend = 15;
  optional int32 defaultGlobalBackend = 16;
  optional int32 defaultGeoBackend = 17;

  repeated int32 relatedPlayModes = 18;   // 关联的玩法模式
  optional RankingBatchSnapshotConf batchSnapshot = 19;

  optional string reservedField0 = 90;  // 预留字段
  optional string reservedField1 = 91;
  optional string reservedField2 = 92;
  optional string reservedField3 = 93;
  optional string reservedField4 = 94;
}

message RankingBatchSnapshotConf {
  optional int32 expireDays = 1;
  optional int32 delayShowHours = 2;
  repeated KeyValueInt32 capacities = 3;
  optional bool readPrevSeasonBeforeShow = 4;    // 结算结果展示之前，上个赛季的榜单是否可以读取
  optional bool updateCurSeasonBeforeShow = 5;   // 结算结果展示之前，当前赛季的榜单是否可以更新
}

enum RankingReadWriteType {
  RRWT_Skip = 0 [(name) = "跳过"];
  RRWT_ReadOnly = 1 [(name) = "只读"];
  RRWT_WriteOnly = 2 [(name) = "只写"];
  RRWT_ReadWrite = 4 [(name) = "读写"];
}

message table_RankingRuleConfData {//@noCli
  repeated RankingRuleConf rows = 1;
}

message RankingUgcIdConf {
  optional int32 friendId = 1;
  optional int32 globalId = 2;
  optional int32 geoId = 3;//@noCli
}

message RankRewardRange {
  optional int32 priority = 1;    // 优先级(1最高，值越小优先级越高)，如果重叠取值优先级最高的

  optional int32 fromIndex = 2;
  optional int32 fromPercent = 3; // 根据榜单大小计算
  optional string fromStr = 10;

  optional int32 toIndex = 4;
  optional int32 toPercent = 5;   // 根据榜单大小计算
  optional string toStr = 11;

  optional int32 mailId = 6;
  repeated Item rewards = 7;                // 道具奖励，已废弃
  repeated TimeLimitedItem rewardItems = 13;                // 道具奖励
  repeated KeyValueInt32 attrRewards = 8;   // 属性奖励, 键为RankRewardAttrType
  optional string describeText = 9;   //排行榜描述
  optional string rewardText = 12;  //奖励展示描述
  repeated string mailParams = 14;
}

message RankingRewardConf {
  option (resKey) = "id";
  optional int32 id = 1;

  optional int32 activityId = 2;
  optional int32 mailTemplateId = 3;
  repeated Item rewardInfo = 4;
  repeated int32 rankRange = 5;
}

message table_RankingRewardConfData {
  repeated RankingRewardConf rows = 1;
}

message RankingSnapshotRewardConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated RankRewardRange rewardRange = 2;
  optional int32 maxSize = 3;           // 最大上榜人数，如果上榜人数大于该值，则展示和排名百分比仍按照maxSize来算
  optional int32 expireDays = 4;        // 奖励最大保留天数
  optional int32 rankId = 5;
  optional int32 fromSeason = 6;
  optional int32 toSeason = 7;

  optional int32 minSize = 8;           // 最小上榜人数，如果上榜人数小于该值，则展示和排名百分比仍按照minSize来算

  optional string reservedField0 = 90;  // 预留字段
  optional string reservedField1 = 91;
  optional string reservedField2 = 92;
  optional string reservedField3 = 93;
  optional string reservedField4 = 94;
}

message table_RankingSnapshotRewardConfData {
  repeated RankingSnapshotRewardConf rows = 1;
}

message RankingPlatSwitchConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated RankRule rules = 3;

  optional RankingPlatSwitchType type = 4;
  optional RankingPlatSwitchSeasonConf season = 5;
  optional RankingPlatSwitchHistoryConf history = 6;
}

enum RankingPlatSwitchType {
  RPST_None = 0;
  RPST_Season = 1;
  RPST_History = 2;
}

message RankingPlatSwitchSeasonConf {
  optional bool enableGrey = 4;                         // 开启灰度
  optional google.protobuf.Timestamp startTime = 1;     // 灰度开始时间
  optional google.protobuf.Timestamp endTime = 2;       // 灰度结束时间
}

message RankingPlatSwitchHistoryConf {
  optional bool enableGrey = 4;                         // 开启灰度
  optional google.protobuf.Timestamp syncTime = 5;      // 数据同步时间
  optional google.protobuf.Timestamp endTime = 2;       // 灰度结束时间
}

message table_RankingPlatSwitchConfData {
  repeated RankingPlatSwitchConf rows = 1;
}

enum RankingGroup {
  RG_Main = 0 [(name) = "主玩法"];
  RG_Farm = 1 [(name) = "农场"];
  RG_OnlyFarm = 2 [(name) = "仅农场"];
  RG_Arena = 3 [(name) = "moba"];
  RG_OnlyArena = 4 [(name) = "仅moba"];
  RG_Chase = 5 [(name) = "chase"];
  RG_Chest = 6 [(name) = "chest"];
}

message UgcRankRewardConf {
  optional int64 rankId = 1;
  repeated RankRewardRange rewardRange = 2;
}

message UgcMapRankRewardConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int64 mapId = 2;
  optional int64 creatorId = 3;
  optional google.protobuf.Timestamp beginTime = 4;
  optional google.protobuf.Timestamp endTime = 5;
  repeated UgcRankRewardConf rankConf = 6;
}

message table_UgcMapRankRewardConfData {
  repeated UgcMapRankRewardConf rows = 1;
}

message RankingNationConf {
  option (resKey) = "id";
  optional int32 id = 1;    // id
  optional string name = 2;  // 名称
  optional string Aplha2Code = 3;  // 二位代码
  repeated int32 RegionalGroupCommon = 4;  // 通用分组
}

message table_RankingNationConfData {
  repeated RankingNationConf rows = 1;
}

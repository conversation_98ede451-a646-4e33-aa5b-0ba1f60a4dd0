syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;

import "ResKeywords.proto";
import "ResCommon.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/timestamp.proto";

message ChestMiscConfig {
	option (resKey) = "id";
	optional int32 id = 1;
	optional int32 cheskWeeklyRankShowTopN = 2; // 带出价值榜-周重置的全服榜单统计的前X名
	optional int32 cheskWeeklyLocalRankShowTopN = 3; // 带出价值榜-周重置的地区榜单统计的前X名
	optional int32 cheskWeeklyRankTopNTotalValue = 4; // 带出价值榜-统计场次带出最多价值的的前X局
	optional int64 cheskWeeklyRankMinValue = 5; // 带出价值榜-上榜门槛-每周至少累计带出的价值达X可上榜
	repeated int32 chestXingBaoWeeklyRankTitle = 6; // 带出价值榜-周重置的全服榜单统计的前X名-星宝奖励
	repeated int32 chestDarkStarWeeklyRankTitle = 7; // 带出价值榜-周重置的全服榜单统计的前X名-暗星奖励
	repeated int32 chestXingBaoWeeklyLocalRankTitle = 8; // 带出价值榜-周重置的地区榜单统计的前X名-星宝奖励
	repeated int32 chestDarkStarWeeklyLocalRankTitle = 9; // 带出价值榜-周重置的地区榜单统计的前X名-暗星奖励
	optional string chestWeeklyTitleExpireInfo = 10; // 限时称号发放后的相对有效期配置
	repeated int32 chestWeeklyTitleItemIds = 11; // 支持数字化数据继承读取的称号id组
	optional string chestWeeklyTitleRewardTime = 12; // 限时称号发放时间配置
}

message table_ChestMiscConf {
  repeated ChestMiscConfig rows = 1;
}
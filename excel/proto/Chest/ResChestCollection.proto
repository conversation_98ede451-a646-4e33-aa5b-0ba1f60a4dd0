syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";

//收藏品主表
message ChestCollectionItemConf {
  option (resKey) = "collectionId";
  optional int32 collectionId = 1; //收藏品ID
  optional int32 pageId = 2; //页签ID
  optional int32 sortId = 3; //排序ID @noSvr
  optional int32 chestItemId = 4; //引用道具ID
  optional int32 levelConfigId = 5; //等级配置ID
  optional int32 entryConfigId = 6; //词条洗练配置ID
  optional google.protobuf.Timestamp openTime = 7; //开放时间
  optional google.protobuf.Timestamp closeTime = 8; //关闭时间
  optional int32 MaxLevel = 9; //最大等级
  optional int32 rewardConfigId = 10; //奖励配置id
}

message table_ChestCollectionItemConf {
  repeated ChestCollectionItemConf rows = 1;
}

//收藏品等级配置
message CollectionLevelConf {
  optional int32 levelLeft = 1; //等级左区间
  optional int32 levelRight = 2; //等级右区间
  optional int32 levelExp = 3; //当前等级区间升级所需的经验值
  optional int32 coinId = 4; //当前等级区间升级所需金币id
  optional int32 coinNum = 5; //当前等级区间升级所需金币id数量
  optional int32 levelUpCostItemNum = 6; //当前等级区间升级所需的本体道具数量
  optional int32 levelUpNeedItemExpMin = 7; //当前等级区间投喂道具组后提供的经验值下限区间
  optional int32 levelUpNeedItemExpMax = 8; //当前等级区间投喂道具组后提供的经验值上限区间
  optional int32 levelUpNeedItemExpCritRate = 9; //当前等级区间投喂道具组后提供的经验值暴击概率(百分比值)
  optional int32 levelUpNeedItemExpCritMultiplier = 10; //当前等级区间投喂道具组后提供的经验值暴击倍率(百分比值)
  optional int32 levelUpExpDirectlyIncreaseRate = 11; //直接增加当前等级区间升级所需经验值概率(百分比值)
}

message ChestCollectionLevelConf {
  option (resKey) = "levelConfigId";
  optional int32 levelConfigId = 1; //等级配置id
  repeated CollectionLevelConf levelConf = 2; //等级配置
}

message table_ChestCollectionLevelConf {
  repeated ChestCollectionLevelConf rows = 1;
}



message CollectionEntryConf {
  optional int32 entryId = 1; //词条id
  optional int32 sortId = 2; //词条排序id(解锁后，越大越靠后)
  optional int32 entryLock = 3; //词条是否默认解锁(0:否)
  optional int32  entryTypeMena = 4;//词条类型所属菜单分区
  optional string entryType = 5; //词条类型(配中文)
  optional string entryTypeDesc = 6; //词条类型描述
  optional int32 entryLockSort = 7; //强制解锁顺序(越大越靠后)
  optional int32 entryUnlockRate = 8; //升级时该词条id解锁的概率
  optional int32 entryUnlockPreConditionValue = 9; //解锁前置条件参数(不满足则不解锁)
  optional int32 entryUnlockMustConditionValue = 10; //该词条id保底解锁条件参数(达成必解锁，若提前解锁则保底不再触发)
  optional string entryConditionDesc = 11; //解锁条件描述文本(未解锁词条上显示的文本)
  repeated int32 entryRandomValueGroup = 12; //词条解锁后的随机洗炼参数组(根据词条功能类型配置具体的功能id)
  repeated int32 entryRandomValueRateGroup = 13; //词条解锁后的随机洗炼参数组对应概率(概率为各配置分值/总和值)
  repeated string entryRandomValueTagGroup = 14; //词条解锁后的随机洗炼参数组对应文本组
  repeated string entryRandomValueTagIconGroup = 15; //词条解锁后的随机洗炼参数组对应tag品质底图页签组
  optional bool  entryIsShowInPage = 16;//收藏页是否展示词条
}

message ChestCollectionEntryConf {
  option (resKey) = "entryConfigId";
  optional int32 entryConfigId = 1; //词条配置id
  repeated CollectionEntryConf entryConf = 2; //词条配置
}

message table_ChestCollectionEntryConf {
  repeated ChestCollectionEntryConf rows = 1;
}

//收藏品奖励配置
message CollectionRewardConf {
  optional ChestCollectionRewardItemType rewardType = 1; //奖励类型(控制显示位置)
  optional int32 rewardItemId = 2; //奖励道具id
  optional int32 rewardItemNum = 3; //奖励道具数量
  optional int32 rewardConditionCollectionLevel = 4; //奖励解锁所需藏品等级
}

message ChestCollectionRewardConf {
  option (resKey) = "rewardConfigId";
  optional int32 rewardConfigId = 1; //奖励配置id
  repeated CollectionRewardConf rewardConf = 2; //奖励配置
  optional string rewardBubbleTxt = 3; //预览气泡文本
  optional string ConditionTxt = 4; //解锁条件文本
}

message table_ChestCollectionRewardConf {
  repeated ChestCollectionRewardConf rows = 1;
}

message ChestCollectionPageInfoConf {
  option (resKey) = "pageId";
  optional int32 pageId = 1;            // 收藏柜id
  optional string pageName = 2;         // 页签显示名字
  optional string pageChildWidget = 3;  // 子页签内容的组件名字
  repeated int32 taskGroupList = 4;     // 关联的任务组
  repeated int32 coinIdList = 5;        // 货币列表
}

message table_ChestCollectionPageInfoConf{
  repeated ChestCollectionPageInfoConf rows = 1;
}

message ChestCollectionQualityConf {
  option (resKey) = "qualityId";
  optional int32 qualityId = 1;             // 品级id
  optional string name = 2;                 // 品级名
  optional string nameplateIcon = 3;        // 主页面-品级铭牌Icon
  optional string nameplateRichtextType = 4; // 主页面-品级富文本类型
  optional string bubbleIcon = 5;            // 预览-品级气泡Icon
}

message table_ChestCollectionQualityConf{
  repeated ChestCollectionQualityConf rows = 1;
}


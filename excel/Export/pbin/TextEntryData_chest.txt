com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_chest.xlsx sheet:Sheet1
rows {
  content: "大王Chest玩法"
  switch: 1
  stringId: "CHEST_ModeName"
}
rows {
  content: "在大王Chest玩法中的违规行为将扣除信誉分"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_MyScore_LineOne"
}
rows {
  content: "信誉分低于70分将不能参与大王Chest玩法"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_MyScore_LineTwo"
}
rows {
  content: "<reputationScore23Red>中途退出</>：未被淘汰时，强制退出将扣除信誉分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_OffenseRule_LineOne"
}
rows {
  content: "<reputationScore23Red>托管挂机</>：未被淘汰时，局内长时间在托管状态下将扣除信誉分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_OffenseRule_LineTwo"
}
rows {
  content: "<reputationScore23Red>不当言论</>：局内辱骂他人，传播广告或违法信息等行为将扣除信誉分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_OffenseRule_LineThree"
}
rows {
  content: "<reputationScore23Red>举报核实</>：其他违规行为，被玩家举报核实后将扣除信誉分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_OffenseRule_LineFour"
}
rows {
  content: "<reputationScore23>正常对局</>：单人正常完成大王Chest玩法对局，没有违规行为，信誉分+{0}分，每日最多可恢复20分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_CreditRule_LineOne"
}
rows {
  content: "<reputationScore23>正常对局</>：组队正常完成大王Chest玩法对局，没有违规行为，信誉分+{0}分，每日最多可恢复20分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_CreditRule_LineTwo"
}
rows {
  content: "<reputationScore23>每日登录</>：每日首次登录，信誉分+{0}分。"
  switch: 1
  stringId: "CHEST_InLevel_Reputation_CreditRule_LineThree"
}
rows {
  content: "您的 <MessageTipYellow>{0}</>不足"
  switch: 1
  stringId: "Chest_MoneyNotEnough_01"
}
rows {
  content: "敬请期待！"
  switch: 1
  stringId: "Chest_Lock_Proficiency_01"
}
rows {
  content: "敬请期待！"
  switch: 1
  stringId: "Chest_Lock_Fightnum_01"
}
rows {
  content: "角色选择"
  switch: 1
  stringId: "Chest_GameCharacterSelectView_Title"
}
rows {
  content: "点击选择暗星阵营角色"
  switch: 1
  stringId: "Chest_FactionShortDesc_Boss"
}
rows {
  content: "点击选择星宝阵营角色"
  switch: 1
  stringId: "Chest_FactionShortDesc_Star"
}
rows {
  content: "暂未解锁当前藏品"
  switch: 1
  stringId: "Chest_Collectibles_ItemLocked"
}
rows {
  content: "还未选择角色，请先进入备战选择"
  switch: 1
  stringId: "Chest_Match_RoleDataNotValid"
}
rows {
  content: "已装备"
  switch: 1
  stringId: "UI_ChestBag_ItemEquipSuc"
}
rows {
  content: "已卸下"
  switch: 1
  stringId: "UI_ChestBag_ItemUnEquipSuc"
}
rows {
  content: "修复成功"
  switch: 1
  stringId: "UI_ChestBag_ItemRepairSuc"
}
rows {
  content: "购买成功"
  switch: 1
  stringId: "UI_ChestBag_ItemBuySuc"
}
rows {
  content: "已出售"
  switch: 1
  stringId: "UI_ChestBag_ItemSaleSuc"
}
rows {
  content: "装备栏已满"
  switch: 1
  stringId: "UI_ChestBag_NoGridIdLeft"
}
rows {
  content: "当前物品无法装备"
  switch: 1
  stringId: "UI_ChestBag_ItemCanNotEquip"
}
rows {
  content: "耐久度"
  switch: 1
  stringId: "UI_ChestBag_Desc_Durability"
}
rows {
  content: "次数"
  switch: 1
  stringId: "UI_ChestBag_Desc_Times"
}
rows {
  content: "背包有商品未购买，是否立即购买？"
  switch: 1
  stringId: "UI_ChestBag_MallBuy_StartGame_CheckContent"
}
rows {
  content: "当前无法购买商品"
  switch: 1
  stringId: "UI_ChestBag_CanNotBuyCommodity"
}
rows {
  content: "当前无法出售道具"
  switch: 1
  stringId: "UI_ChestBag_CanNotSaleItem"
}
rows {
  content: "当前无法卸下道具"
  switch: 1
  stringId: "UI_ChestBag_CanNotUnEquipItem"
}
rows {
  content: "当前无法修复道具"
  switch: 1
  stringId: "UI_ChestBag_CanNotRepairItem"
}
rows {
  content: "当前无法装备道具"
  switch: 1
  stringId: "UI_ChestBag_CanNotEquipItem"
}
rows {
  content: "配装备战"
  switch: 1
  stringId: "UI_ChestBag_BackTitle"
}
rows {
  content: "详情"
  switch: 1
  stringId: "UI_ChestBag_Button_Detail_Content"
}
rows {
  content: "一键整理"
  switch: 1
  stringId: "UI_ChestBag_Button_Resort_Content"
}
rows {
  content: "容量"
  switch: 1
  stringId: "UI_ChestBag_Text_Capacity_Content"
}
rows {
  content: "角色入场消耗"
  switch: 1
  stringId: "UI_ChestBag_Text_PlayerMapCoinCost"
}
rows {
  content: "装备"
  switch: 1
  stringId: "UI_ChestBag_Button_Equip_Content"
}
rows {
  content: "卸下"
  switch: 1
  stringId: "UI_ChestBag_Button_UnEquip_Content"
}
rows {
  content: "开始匹配"
  switch: 1
  stringId: "UI_ChestBag_Button_StartGame_Content"
}
rows {
  content: "购买"
  switch: 1
  stringId: "UI_ChestBag_Button_Buy_Content"
}
rows {
  content: "出售"
  switch: 1
  stringId: "UI_ChestBag_Button_Sale_Content"
}
rows {
  content: "预计花费"
  switch: 1
  stringId: "UI_ChestBag_Text_CommodityCoinCost"
}
rows {
  content: "一键全选"
  switch: 1
  stringId: "UI_ChestBag_Text_SelectedAll"
}
rows {
  content: "出售总价"
  switch: 1
  stringId: "UI_ChestBag_Text_SalePrice"
}
rows {
  content: "点击物品放入售卖区"
  switch: 1
  stringId: "UI_ChestBag_ClickItemToSaleBag"
}
rows {
  content: "*可点击商品放入购买区"
  switch: 1
  stringId: "UI_ChestBag_ClickItemToBuyBag"
}
rows {
  content: "*可拖拽左侧背包道具图标至此快速装备"
  switch: 1
  stringId: "UI_ChestBag_ClickItemToStorageBag"
}
rows {
  content: "维修"
  switch: 1
  stringId: "UI_ChestBag_Button_Repair_Content"
}
rows {
  content: "物品详情"
  switch: 1
  stringId: "UI_CheatBag_ItemDetailTitle"
}

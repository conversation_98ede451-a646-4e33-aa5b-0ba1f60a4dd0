com.tencent.wea.xlsRes.table_CleaningGroup
excel/xls/Q_清理条件表.xlsx sheet:组合
rows {
  id: "A1"
  desc: "在大版本更新时，清理所有30天内没玩过的玩法的Pak和缓存"
  time: "1"
  condition: "F2,30;F4,Game"
  action: "O6"
}
rows {
  id: "A2"
  desc: "在大版本更新时，清理时装"
  time: "1"
  condition: "F4,Avatar"
  action: "O5,72000|73000"
}
rows {
  id: "A3"
  desc: "在大版本更新时，清理系统缓存"
  time: "1"
  condition: "F4,System"
  action: "O2"
}
rows {
  id: "A4"
  desc: "在大版本更新时，清理SDK缓存"
  time: "1"
  condition: "F4,SDK"
  action: "O2"
}
rows {
  id: "A5"
  desc: "在大版本更新时，清理框架缓存"
  time: "1"
  condition: "F4,FrameWork"
  action: "O2"
}
rows {
  id: "A6"
  desc: "在大版本更新时，清理引擎缓存"
  time: "1"
  condition: "F4,Engine"
  action: "O2"
}
rows {
  id: "A7"
  desc: "在大版本更新时，清理UGC地图缓存"
  time: "1"
  condition: "F3,Game_UGC"
  action: "O8"
}
rows {
  id: "A8"
  desc: "在从玩法退到大厅时，若当前手机空间小于15%且可清理资源>1G时，弹窗提示玩家可以清理，弹窗CD为3天 "
  time: "2"
  condition: "F7,0.15;F9,1"
  action: "O1,当前存在<MantelCardCount>{0}</>缓存资源及近期未游玩的玩法资源，是否前往清理？!,3"
}
rows {
  id: "A9"
  desc: "在从玩法退到大厅时，若当前手机空间小于5GB且可清理资源>1G时，弹窗提示玩家可以清理，弹窗CD为3天 "
  time: "2"
  condition: "F8,5;F9,1"
  action: "O1,当前存在<MantelCardCount>{0}</>缓存资源及近期未游玩的玩法资源，是否前往清理？!,3"
}
rows {
  id: "A10"
  desc: "在从玩法退到大厅时，若可清理资源>2G时，"
  time: "2"
  condition: "F9,2"
  action: "O1,当前存在<MantelCardCount>{0}</>缓存资源及近期未游玩的玩法资源，是否前往清理？!,3"
}
rows {
  id: "A11"
  desc: "在从玩法退到大厅时，非新手（累计活跃天数大于7天）游戏包体首次超过8G且可清理资源>1G"
  time: "2"
  condition: "F9,1;F10,7;F11,8"
  action: "O1,当前存在<MantelCardCount>{0}</>缓存资源及近期未游玩的玩法资源，是否前往清理？!,3"
}
rows {
  id: "A12"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次清理时装"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F4,Avatar"
  action: "O5,72000|73000"
}
rows {
  id: "A13"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次清理系统缓存"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F4,System"
  action: "O2"
}
rows {
  id: "A14"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次清理SDK缓存"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F4,SDK"
  action: "O2"
}
rows {
  id: "A15"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次清理框架缓存"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F4,FrameWork"
  action: "O2"
}
rows {
  id: "A16"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次清理引擎缓存"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F4,Engine"
  action: "O2"
}
rows {
  id: "A17"
  desc: "进入登陆页面，检测某标记是否存在，存在则触发一次UGC地图缓存的全清除"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2;F3,Game_UGC"
  action: "O8"
}
rows {
  id: "A18"
  desc: "进入登陆页面，检测某标记是否存在，存在则取消该标记"
  time: "6"
  condition: "F20,AlreadyOnceAutoCleanedV2"
  action: "O7,AlreadyOnceAutoCleanedV2"
}
rows {
  id: "A19"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理7天内未游玩的玩法包"
  time: "2"
  condition: "F21,8;F2,7;F4,Game"
  action: "O6"
  platform: "-10"
}
rows {
  id: "A20"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理UGC除草稿地图外所有地图文件和数据"
  time: "2"
  condition: "F21,8;F3,Game_UGC"
  action: "O4"
  platform: "-10"
}
rows {
  id: "A21"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理所有时装"
  time: "2"
  condition: "F21,8;F4,Avatar"
  action: "O5"
  platform: "-10"
}
rows {
  id: "A22"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理所有系统缓存（CDN、PhotoIcon、moe）,保留0MCDN图片"
  time: "2"
  condition: "F21,8;F4,System"
  action: "O9,0"
  platform: "-10"
}
rows {
  id: "A23"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理所有SDK缓存"
  time: "2"
  condition: "F21,8;F4,SDK"
  action: "O2"
  platform: "-10"
}
rows {
  id: "A24"
  desc: "在从玩法退到大厅时，若游戏包体本地占用空间超过8G，强制清理所有框架缓存"
  time: "2"
  condition: "F21,8;F4,FrameWork"
  action: "O2"
  deleteIds: "A8"
  deleteIds: "A9"
  deleteIds: "A10"
  deleteIds: "A11"
  platform: "-10"
}

com.tencent.wea.xlsRes.table_ChatTypeConfigData
excel/xls/L_聊天频道.xlsx sheet:基础配置
rows {
  id: 1
  key: "CT_Private"
  chatTypeLatestMsgTabName: "私聊"
  chatTypeLatestMsgTabColor: "#F074BDFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Chat"
  mainImage: "T_Common_Img_ChatTabs_02"
  showName: "NewChat_Friend"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "私聊"
  isShowInMain: true
  order: 3
}
rows {
  id: 2
  key: "CT_TeamGroup"
  chatTypeLatestMsgTabName: "组队"
  chatTypeLatestMsgTabColor: "#FBD03BFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Team"
  mainImage: "T_Common_Img_ChatTabs_01"
  verticalImage: "T_VerticalChat_Img_Channel_Team"
  showName: "队伍"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "队伍内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 6
}
rows {
  id: 3
  key: "CT_SideGroup"
  chatTypeLatestMsgTabName: "阵营"
  chatTypeLatestMsgTabColor: "#FBD03BFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Team"
  desc: "阵营内聊天"
}
rows {
  id: 4
  key: "CT_BattleChat"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Chat"
  desc: "对局内聊天"
}
rows {
  id: 8
  key: "CT_World"
  chatTypeLatestMsgTabName: "世界"
  chatTypeLatestMsgTabColor: "#4BEB91FF"
  minSendInterval: 5000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_World"
  mainImage: "T_Common_Img_ChatTabs_03"
  verticalImage: "T_VerticalChat_Img_Channel_World"
  showName: "NewChat_World"
  isShowRed: false
  isShowRecruit: true
  tabType: 1
  desc: "世界内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 1
  isCheckDupText: true
  numOfTextMessageRecords: 10
  dupTextCheckInterval: 10
}
rows {
  id: 9
  key: "CT_Club"
  chatTypeLatestMsgTabName: "社团"
  chatTypeLatestMsgTabColor: "#FF7C2AFF"
  minSendInterval: 1000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Club"
  mainImage: "T_Common_Img_ChatTabs_09"
  verticalImage: "T_VerticalChat_Img_Channel_Club"
  showName: "Club_ChatTab"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "社团内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 7
}
rows {
  id: 12
  key: "CT_Lobby"
  chatTypeLatestMsgTabName: "广场"
  chatTypeLatestMsgTabColor: "#42B2F9FF"
  minSendInterval: 3000
  isShowPosition: true
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Square"
  mainImage: "T_Common_Img_ChatTabs_04"
  verticalImage: "T_VerticalChat_Img_Channel_Square"
  showName: "NewChat_Lobby"
  isShowRed: false
  isShowRecruit: true
  tabType: 1
  desc: "大厅内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 2
}
rows {
  id: 13
  key: "CT_LobbyFacility"
  chatTypeLatestMsgTabName: "聊天室"
  chatTypeLatestMsgTabColor: "#AFF54BFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Room"
  mainImage: "T_Common_Img_ChatTabs_05"
  verticalImage: "T_VerticalChat_Img_Channel_Chat"
  showName: "NewChat_ChatRoom"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "大厅设施内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 5
}
rows {
  id: 14
  key: "CT_NewStar"
  chatTypeLatestMsgTabName: "新人"
  chatTypeLatestMsgTabColor: "#4BEB91FF"
  minSendInterval: 6000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_New"
  mainImage: "T_Common_Img_ChatTabs_07"
  verticalImage: "T_VerticalChat_Img_Channel_New"
  showName: "NewChat_NewStar"
  isShowRed: false
  isShowRecruit: true
  tabType: 1
  desc: "新人聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 9
}
rows {
  id: 15
  key: "CT_CustomRoom"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Room"
  mainImage: "T_Common_Img_ChatTabs_06"
  verticalImage: "T_VerticalChat_Img_Channel_Room"
  showName: "NewChat_ChatHourse"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "多人房间内聊天"
  isShowInMain: true
  isShowInVertical: true
  order: 8
}
rows {
  id: 16
  key: "CT_Xiaowo"
  chatTypeLatestMsgTabName: "家园"
  chatTypeLatestMsgTabColor: "#BD7DFFFF"
  minSendInterval: 0
  isShowPosition: true
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Room"
  mainImage: "T_Common_Img_ChatTabs_08"
  showName: "NewChat_Home"
  isShowRed: false
  isShowRecruit: false
  tabType: 1
  desc: "家园聊天"
  isShowInMain: true
  order: 2
}
rows {
  id: 17
  key: "CT_UgcCustomRoom"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Room"
  mainImage: "T_Common_Img_ChatTabs_06"
  showName: "NewChat_ChatHourse"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "ugc多人房间内聊天"
  isShowInMain: true
  order: 8
}
rows {
  id: 18
  key: "CT_CompetitionRoom"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Room"
  mainImage: "T_Common_Img_ChatTabs_06"
  showName: "NewChat_ChatHourse"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "赛事系统房间内聊天"
  isShowInMain: true
  order: 8
}
rows {
  id: 19
  key: "CT_AIChat"
  chatTypeLatestMsgTabName: "私聊"
  chatTypeLatestMsgTabColor: "#F074BDFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Chat"
  mainImage: "T_Common_Img_ChatTabs_06"
  showName: "NewChat_AIChat"
  isShowRed: false
  isShowRecruit: false
  tabType: 1
  desc: "AI聊天"
  isShowInMain: true
  order: 10
}
rows {
  id: 20
  key: "CT_Farm"
  isIgnoreHistoryChatType: true
  ignoreChatTypeList: 25
  ignoreChatTypeList: 26
  ignoreChatTypeList: 27
  ignoreChatTypeList: 12
  ignoreChatTypeList: 13
  ignoreChatTypeList: 19
  chatTypeLatestMsgTabName: "农场"
  chatTypeLatestMsgTabColor: "#58FF54FF"
  minSendInterval: 1000
  isShowPosition: true
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_FarmA"
  mainImage: "T_Common_Img_ChatTabs_10"
  verticalImage: "T_VerticalChat_Img_Channel_Farm"
  showName: "NewChat_Farm"
  isShowRed: false
  isShowRecruit: false
  tabType: 1
  desc: "农场"
  isShowInMain: true
  isShowInVertical: true
  order: 2
}
rows {
  id: 22
  key: "CT_Stranger"
  chatTypeLatestMsgTabName: "陌生人"
  chatTypeLatestMsgTabColor: "#F074BDFF"
  minSendInterval: 0
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Stranger"
  mainImage: "T_Common_Img_ChatTabs_11"
  showName: "NewChat_SayHiChat"
  isShowRed: true
  isShowRecruit: false
  tabType: 1
  desc: "陌生人聊天"
  isShowInMain: true
  order: 4
}
rows {
  id: 23
  key: "CT_FarmHouse"
  chatTypeLatestMsgTabName: "农场"
  chatTypeLatestMsgTabColor: "#58FF54FF"
  minSendInterval: 0
  isShowPosition: true
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Farm"
  mainImage: "T_Common_Img_ChatTabs_10"
  verticalImage: "T_VerticalChat_Img_Channel_Farm"
  showName: "NewChat_Farm"
  isShowRed: false
  isShowRecruit: false
  tabType: 1
  desc: "农场小窝"
  isShowInMain: true
  isShowInVertical: true
  order: 2
}
rows {
  id: 24
  key: "CT_FarmCommunityChannel"
  chatTypeLatestMsgTabName: "农场世界"
  chatTypeLatestMsgTabColor: "#58FF54FF"
  minSendInterval: 3000
  isShowPosition: true
  showPositionFilter: 1
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_FarmA"
  showName: "农场世界"
  tabType: 2
  desc: "农场世界频道"
  order: 99
  isCheckDupText: true
  numOfTextMessageRecords: 10
  dupTextCheckInterval: 10
}
rows {
  id: 25
  key: "CT_ArenaCommunityChannel"
  minSendInterval: 3000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_XiaGu"
  qualifyType: QT_Arena
  qualifyType: QT_HOK
  showName: "峡谷世界"
  tabType: 2
  desc: "峡谷世界频道"
  order: 99
  isCheckDupText: true
  numOfTextMessageRecords: 10
  dupTextCheckInterval: 10
}
rows {
  id: 26
  key: "CT_WolfKillCommunityChannel"
  minSendInterval: 3000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_LangRen"
  showName: "狼人世界"
  tabType: 2
  desc: "狼人世界频道"
  order: 99
  isCheckDupText: true
  numOfTextMessageRecords: 10
  dupTextCheckInterval: 10
}
rows {
  id: 27
  key: "CT_TradingCardChannel"
  minSendInterval: 3000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Card"
  showName: "卡牌世界"
  tabType: 2
  desc: "卡牌世界频道"
  order: 99
  isCheckDupText: true
  numOfTextMessageRecords: 10
  dupTextCheckInterval: 10
}
rows {
  id: 28
  key: "CT_SpCommunityChannel"
  minSendInterval: 5000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Card"
  showName: "SP世界"
  tabType: 2
  desc: "SP世界频道"
  order: 99
}
rows {
  id: 29
  key: "CT_NR3E8Rich"
  chatTypeLatestMsgTabName: "Rich"
  chatTypeLatestMsgTabColor: "#58FF54FF"
  minSendInterval: 5000
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Chat"
  mainImage: "T_Common_Img_ChatTabs_Rich"
  showName: "当前城市"
  isShowRed: true
  tabType: 1
  desc: "Rich城市内聊天"
  isShowInMain: true
  order: 11
}
rows {
  id: 30
  key: "CT_FarmCook"
  chatTypeLatestMsgTabName: "农场"
  chatTypeLatestMsgTabColor: "#58FF54FF"
  minSendInterval: 0
  isShowPosition: true
  shareGiftChatTypeIcon: "T_GiftSharing_SelectChannel_Farm"
  mainImage: "T_Common_Img_ChatTabs_10"
  verticalImage: "T_VerticalChat_Img_Channel_Farm"
  showName: "NewChat_Farm"
  isShowRed: false
  isShowRecruit: false
  tabType: 1
  desc: "农场餐厅"
  isShowInMain: true
  isShowInVertical: true
  order: 2
}

com.tencent.wea.xlsRes.Table_GameModeReturnCheckConf
excel/xls/W_玩法回流.xlsx sheet:开启结束条件
rows {
  id: 100101
  checkType: 1001
  checkParam2: 5
}
rows {
  id: 100201
  checkType: 1002
  checkParam2: 30
}
rows {
  id: 100301
  checkType: 1003
  checkParam1: 1
  checkParam2: 30
}
rows {
  id: 100401
  checkType: 1004
  checkParam1: 1801101
  checkParam1: 1801102
  checkParam1: 1801103
}
rows {
  id: 100501
  checkType: 1005
  checkParam1: 7
}
rows {
  id: 100601
  checkType: 1006
  checkParam1: 5
}
rows {
  id: 100701
  checkType: 1007
  checkParam1: 5
}
rows {
  id: 100801
  checkType: 1008
  checkParam1: 1000
}
rows {
  id: 100901
  checkType: 1009
}
rows {
  id: 101001
  checkType: 1010
}
rows {
  id: 101101
  checkType: 1011
}
rows {
  id: 101301
  checkType: 1013
}
rows {
  id: 100102
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100202
  checkType: 1002
  checkParam2: 30
}
rows {
  id: 100302
  checkType: 1003
  checkParam1: 2
  checkParam2: 30
}
rows {
  id: 100402
  checkType: 1004
  checkParam1: 1802101
  checkParam1: 1802102
  checkParam1: 1802103
}
rows {
  id: 100502
  checkType: 1005
  checkParam1: 7
}
rows {
  id: 100103
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100203
  checkType: 1002
  checkParam2: 30
}
rows {
  id: 100303
  checkType: 1003
  checkParam1: 3
  checkParam2: 30
}
rows {
  id: 100403
  checkType: 1004
  checkParam1: 1803101
  checkParam1: 1803102
  checkParam1: 1803103
}
rows {
  id: 100503
  checkType: 1005
  checkParam1: 7
}
rows {
  id: 100104
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100204
  checkType: 1002
  checkParam2: 30
}
rows {
  id: 100304
  checkType: 1003
  checkParam1: 1
  checkParam2: 30
}
rows {
  id: 100404
  checkType: 1004
  checkParam1: 1804101
  checkParam1: 1804102
  checkParam1: 1804103
}
rows {
  id: 100504
  checkType: 1005
  checkParam1: 7
}
rows {
  id: 100105
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100205
  checkType: 1002
  checkParam2: 30
}
rows {
  id: 100305
  checkType: 1003
  checkParam1: 2
  checkParam2: 30
}
rows {
  id: 100405
  checkType: 1004
  checkParam1: 1805101
  checkParam1: 1805102
  checkParam1: 1805103
}
rows {
  id: 100505
  checkType: 1005
  checkParam1: 7
}
rows {
  id: 100106
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100206
  checkType: 1002
  checkParam2: 7
}
rows {
  id: 100306
  checkType: 1003
  checkParam1: 4
  checkParam2: 14
}
rows {
  id: 100307
  checkType: 1003
  checkParam1: 5
  checkParam2: 14
}
rows {
  id: 100506
  checkType: 1005
  checkParam1: 21
}
rows {
  id: 101302
  checkType: 1013
}
rows {
  id: 100507
  checkType: 1005
  checkParam1: 14
}
rows {
  id: 100308
  checkType: 1003
  checkParam1: 6
  checkParam2: 4
}
rows {
  id: 101401
  checkType: 1014
  checkParam1: 4
}
rows {
  id: 100107
  checkType: 1001
  checkParam2: 1
}
rows {
  id: 100508
  checkType: 1005
  checkParam1: 3
}

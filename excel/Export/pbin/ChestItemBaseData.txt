com.tencent.wea.xlsRes.table_ChestItemBaseData
excel/xls/Chest/D_道具表_CHEST.xlsx sheet:CHEST道具基础参数表
rows {
  id: 4890001
  baseValue: 6000
  supplyBPName: "BP_Chest_SupplyBag_001"
  salePriceDecay: 5000
  extendedBagNum: 1
  extendedBagItemType: ItemType_Chest_Prop
  extendedBagItemType: ItemType_Chest_Treasure
  effectiveMethod: CTDDT_Initiative
}
rows {
  id: 4890002
  baseValue: 50000
  supplyBPName: "BP_Chest_SupplyBag_002"
  salePriceDecay: 5000
  extendedBagNum: 3
  extendedBagItemType: ItemType_Chest_Treasure
  effectiveMethod: CTDDT_Initiative
}
rows {
  id: 4890011
  maxDurability: 20
  baseValue: 5000
  supplyBPName: "BP_Chset_Supply_Shoes_01"
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 1000
  skillId: 40003
  skillLevelVariable: 1
  repairCoefficient: 5000
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4890021
  maxDurability: 6
  baseValue: 3000
  supplyBPName: "BP_Chset_Supply_Crowbar_01"
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 600
  skillLevelVariable: 1
  repairCoefficient: 5000
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4890031
  maxDurability: 1
  baseValue: 5000
  supplyBPName: "BP_Chest_Supply_IceGrenade_01"
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 40002
  skillLevelVariable: 1
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4890041
  maxDurability: 1
  baseValue: 5000
  supplyBPName: "BP_Chest_Supply_Bandage_01"
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 40007
  skillLevelVariable: 1
  effectiveMethod: CTDDT_Initiative
}
rows {
  id: 4890051
  maxDurability: 180
  baseValue: 3000
  supplyBPName: "BP_Chset_Supply_FlashLight_01"
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 600
  skillId: 40008
  skillLevelVariable: 1
  repairCoefficient: 5000
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4890061
  maxDurability: 1
  baseValue: 4000
  supplyBPName: "BP_Chest_Supply_HolyWater_01"
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 40005
  skillLevelVariable: 1
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4890071
  maxDurability: 120
  baseValue: 40000
  supplyBPName: "BP_Chest_Supply_Motorcycle"
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 8000
  skillId: 40004
  skillLevelVariable: 1
  repairCoefficient: 5000
  effectiveMethod: CTDDT_Initiative
}
rows {
  id: 4890081
  maxDurability: 1
  baseValue: 10000
  supplyBPName: "BP_Chest_Supply_Cube"
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillLevelVariable: 1
  effectiveMethod: CTDDT_Initiative
}
rows {
  id: 4890091
  maxDurability: 1
  baseValue: 8000
  supplyBPName: "BP_Chest_InvisibleCloak"
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 40006
  skillLevelVariable: 1
  effectiveMethod: CTDDT_QuickUsage
}
rows {
  id: 4891011
  maxDurability: 3
  baseValue: 5000
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 1000
  skillId: 30005
  skillLevelVariable: 1
  repairCoefficient: 5000
}
rows {
  id: 4891021
  maxDurability: 6
  baseValue: 5000
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 30004
  skillLevelVariable: 1
}
rows {
  id: 4891031
  maxDurability: 3
  baseValue: 8000
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillLevelVariable: 1
}
rows {
  id: 4891041
  maxDurability: 2
  baseValue: 8000
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillId: 30003
  skillLevelVariable: 1
}
rows {
  id: 4891051
  maxDurability: 120
  baseValue: 10000
  durabilityDisplay: CTDDT_Line
  durabilityExhaustion: true
  salePriceDecay: 5000
  emptyDurabilityValue: 2000
  skillId: 30010
  skillLevelVariable: 1
  repairCoefficient: 5000
}
rows {
  id: 4891061
  maxDurability: 3
  baseValue: 20000
  durabilityDisplay: CTDDT_Number
  durabilityExhaustion: true
  salePriceDecay: 5000
  skillLevelVariable: 1
}
rows {
  id: 4892001
  baseValue: 1000000
  supplyBPName: "BP_Chest_SupplyItem_4892001"
}
rows {
  id: 4892002
  baseValue: 300000
  supplyBPName: "BP_Chest_SupplyItem_4892002"
}
rows {
  id: 4892003
  baseValue: 80000
  supplyBPName: "BP_Chest_SupplyItem_4892003"
}
rows {
  id: 4892004
  baseValue: 50000
  supplyBPName: "BP_Chest_SupplyItem_4892004"
}
rows {
  id: 4892005
  baseValue: 70000
  supplyBPName: "BP_Chest_SupplyItem_4892005"
}
rows {
  id: 4892006
  baseValue: 100000
  supplyBPName: "BP_Chest_SupplyItem_4892006"
}
rows {
  id: 4892007
  baseValue: 60000
  supplyBPName: "BP_Chest_SupplyItem_4892007"
}
rows {
  id: 4892008
  baseValue: 50000
  supplyBPName: "BP_Chest_SupplyItem_4892008"
}
rows {
  id: 4892009
  baseValue: 90000
  supplyBPName: "BP_Chest_SupplyItem_4892009"
}
rows {
  id: 4892010
  baseValue: 50000
  supplyBPName: "BP_Chest_SupplyItem_4892010"
}
rows {
  id: 4892011
  baseValue: 18000
  supplyBPName: "BP_Chest_SupplyItem_4892011"
}
rows {
  id: 4892012
  baseValue: 10000
  supplyBPName: "BP_Chest_SupplyItem_4892012"
}
rows {
  id: 4892013
  baseValue: 10000
  supplyBPName: "BP_Chest_SupplyItem_4892013"
}
rows {
  id: 4892014
  baseValue: 12000
  supplyBPName: "BP_Chest_SupplyItem_4892014"
}
rows {
  id: 4892015
  baseValue: 14000
  supplyBPName: "BP_Chest_SupplyItem_4892015"
}
rows {
  id: 4892016
  baseValue: 20000
  supplyBPName: "BP_Chest_SupplyItem_4892016"
}
rows {
  id: 4892017
  baseValue: 10000
  supplyBPName: "BP_Chest_SupplyItem_4892017"
}
rows {
  id: 4892018
  baseValue: 16000
  supplyBPName: "BP_Chest_SupplyItem_4892018"
}
rows {
  id: 4892019
  baseValue: 4000
  supplyBPName: "BP_Chest_SupplyItem_4892019"
}
rows {
  id: 4892020
  baseValue: 5500
  supplyBPName: "BP_Chest_SupplyItem_4892020"
}
rows {
  id: 4892021
  baseValue: 5000
  supplyBPName: "BP_Chest_SupplyItem_4892021"
}
rows {
  id: 4892022
  baseValue: 4500
  supplyBPName: "BP_Chest_SupplyItem_4892022"
}
rows {
  id: 4892023
  baseValue: 3000
  supplyBPName: "BP_Chest_SupplyItem_4892023"
}
rows {
  id: 4892024
  baseValue: 3500
  supplyBPName: "BP_Chest_SupplyItem_4892024"
}
rows {
  id: 4892025
  baseValue: 3000
  supplyBPName: "BP_Chest_SupplyItem_4892025"
}
rows {
  id: 4892026
  baseValue: 1500
  supplyBPName: "BP_Chest_SupplyItem_4892026"
}
rows {
  id: 4892027
  baseValue: 1400
  supplyBPName: "BP_Chest_SupplyItem_4892027"
}
rows {
  id: 4892028
  baseValue: 1300
  supplyBPName: "BP_Chest_SupplyItem_4892028"
}
rows {
  id: 4892029
  baseValue: 1200
  supplyBPName: "BP_Chest_SupplyItem_4892029"
}
rows {
  id: 4892030
  baseValue: 1100
  supplyBPName: "BP_Chest_SupplyItem_4892030"
}
rows {
  id: 4892031
  baseValue: 1000
  supplyBPName: "BP_Chest_SupplyItem_4892031"
}
rows {
  id: 4892032
  baseValue: 6000
  supplyBPName: "BP_Chest_SupplyItem_4892032"
}

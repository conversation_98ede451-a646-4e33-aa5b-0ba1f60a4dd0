com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_System.xlsx sheet:文本配置
rows {
  content: "<Recruit23>一周一次</>的<Recruit23>全民赛事</>，谁会是万里挑一的靓丽星宝呢？"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content1"
}
rows {
  content: "报名即可参赛，经过<Recruit23>海选赛、淘汰赛、巅峰对决</>，决出最终冠军。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content2"
}
rows {
  content: "根据最终排名，你将获得丰富奖励，更有赛事专属积分。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content3"
}
rows {
  content: "报名时间：每<Recruit23>周二8:00</>至<Recruit23>周五20:20</>"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content4"
}
rows {
  content: "报名条件：段位达到<Recruit23>白银启明星及以上</>，无人数限制"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content5"
}
rows {
  content: "报名费用：30星愿币"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content6"
}
rows {
  content: "海选赛时间：<Recruit23>每周五20:00</>至<Recruit23>21:00</>"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content7"
}
rows {
  content: "玩家们将在<Recruit23>指定的8张地图</>中进行海选比赛"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content8"
}
rows {
  content: "根据每场比赛的排名，玩家将获得<Recruit23>海选积分</>，积分排名靠前的玩家将<Recruit23>晋级淘汰赛</>。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content9"
}
rows {
  content: "报名参赛的<Recruit23>玩家越多</>，晋级淘汰赛的<Recruit23>名额也会越多</>。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content10"
}
rows {
  content: "淘汰赛时间：<Recruit23>每周六20:05</>至<Recruit23>21:00</>"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content11"
}
rows {
  content: "淘汰赛分为<Recruit23>淘汰阶段</>和<Recruit23>巅峰对决</>"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content12"
}
rows {
  content: "晋级淘汰赛的玩家将被<Recruit23>分为32个小组</>，每组<Recruit23>进行6-7轮比赛</>，决出最终的32名小组冠军，晋级巅峰对决。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content13"
}
rows {
  content: "淘汰赛制<Recruit23>包含32进16，16进8，8进1等形式</>，胜者晋级下一轮，败者立刻被淘汰。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content14"
}
rows {
  content: "淘汰赛32个小组冠军将进行最终的巅峰对决。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content15"
}
rows {
  content: "巅峰对决将只进行<Recruit23>一场比赛</>，该比赛有<Recruit23>7个回合</>。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content16"
}
rows {
  content: "每个回合中，玩家们将在不同的地图中进行比赛，并逐步淘汰部分玩家，比如32进28,28进24···"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content17"
}
rows {
  content: "最终的胜者将成为本届赛事总冠军。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content18"
}
rows {
  content: "根据赛事中的最终排名，玩家将获得相应的赛事积分。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content19"
}
rows {
  content: "玩家可累计赛事积分，兑换<Recruit23>积分奖励</>。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content20"
}
rows {
  content: "每隔一段时间，赛事积分和积分奖励都<Recruit23>将重置</>。"
  switch: 1
  stringId: "UI_Competition_ExplainMain_content21"
}
rows {
  content: "{0}猜对了{1}名玩家的身份"
  switch: 1
  stringId: "NR3E_EndReason_103_Tips"
}
rows {
  content: "用来拼接胜利原因值"
  switch: 1
  stringId: "NR3E_EndReason"
}
rows {
  content: "暗杀"
  switch: 1
  stringId: "MeetingActionName9"
}
rows {
  content: "红包雨"
  switch: 1
  stringId: "UI_RedPacket_ActivityName"
}
rows {
  content: "点击开启({0})"
  switch: 1
  stringId: "UI_Activity_OpenPacket_OneParam"
}
rows {
  content: "一键提现"
  switch: 1
  stringId: "UI_BtnName_AllCash"
}
rows {
  content: "活动期间，每天{0}轮红包雨"
  switch: 1
  stringId: "UI_RedPacket_ActivityDes"
}
rows {
  content: "当前第{0}轮"
  switch: 1
  stringId: "UI_RedPacket_CurrTimes"
}
rows {
  content: "本轮剩余时间"
  switch: 1
  stringId: "UI_RedPacket_CurrTimesDes"
}
rows {
  content: "（距离下一轮开启）"
  switch: 1
  stringId: "UI_RedPacket_NextTimesDes"
}
rows {
  content: "第{0}轮"
  switch: 1
  stringId: "UI_RedPacket_TimesTh"
}
rows {
  content: "我的红包记录"
  switch: 1
  stringId: "UI_RedPacket_Record"
}
rows {
  content: "进行中"
  switch: 1
  stringId: "UI_RedPacket_OnGoing"
}
rows {
  content: "即将开始"
  switch: 1
  stringId: "UI_RedPacket_Begin"
}
rows {
  content: "历史记录"
  switch: 1
  stringId: "UI_RedPacket_History"
}
rows {
  content: "提现"
  switch: 1
  stringId: "UI_RedPacket_Cash"
}
rows {
  content: "本轮已领取{0}/{1}"
  switch: 1
  stringId: "UI_RedPacket_CurrStage_TwoParam"
}
rows {
  content: "获得奖励：{0}"
  switch: 1
  stringId: "UI_RedPacket_GetReward_OneParam"
}
rows {
  content: "还有{0}个"
  switch: 1
  stringId: "UI_RedPacket_Season_Left"
}
rows {
  content: "请先下载玩法资源包"
  switch: 1
  stringId: "Rank_ResourceDownLoadTips"
}
rows {
  content: "UI_Setting_PopLink的描述"
  switch: 1
  stringId: "UI_Setting_PopLink_Desc"
}
rows {
  content: "微信游戏动态"
  switch: 1
  stringId: "SettingTip_WeChatGameDynamic"
}
rows {
  content: "非常抱歉，我们现在无法为您提供服务，请同意协议方可继续"
  switch: 1
  stringId: "Login_Overseas_Agreement"
}
rows {
  content: "%Y年%m月%d日"
  switch: 1
  stringId: "TimeUtils_YMD"
}
rows {
  content: "确定选择这{0}件心愿礼物吗？"
  switch: 1
  stringId: "Activity_Exchange_PlayerReturn_ConfirmGift"
}
rows {
  content: "确定选择（{0}）"
  switch: 1
  stringId: "Activity_Exchange_PlayerReturn_CurGiftNum"
}
rows {
  content: "退出"
  switch: 1
  stringId: "Common_Quit"
}
rows {
  content: "重新考虑"
  switch: 1
  stringId: "Common_Reconsider"
}
rows {
  content: "撤销"
  switch: 1
  stringId: "Common_Revocation"
}
rows {
  content: "海外登录成功"
  switch: 1
  stringId: "Common_OverseaLoginSuccess"
}
rows {
  content: "活动未开启"
  switch: 1
  stringId: "Common_ActivityNotOpen"
}
rows {
  content: "秒"
  switch: 1
  stringId: "Common_Sec_NoParam"
}
rows {
  content: "已领完"
  switch: 1
  stringId: "Common_GetOver"
}
rows {
  content: "未领取"
  switch: 1
  stringId: "Common_WaitGet"
}
rows {
  content: "关卡类型错误，需要更新配表"
  switch: 1
  stringId: "Net_CommunityLevelTypeError"
}
rows {
  content: "组合中存在物件不能作为模板"
  switch: 1
  stringId: "UGC_ErrorMsg_CanNotAsTemplateInGroup"
}
rows {
  content: "已复制链接到剪切板"
  switch: 1
  stringId: "Common_CopyToClipboard"
}
rows {
  content: "本地通知权限申请失败"
  switch: 1
  stringId: "Permission_LocalNotify_Failed"
}
rows {
  content: "删除后，资源不会出现在资源库中\n也无法在编辑器中查看和使用"
  switch: 1
  stringId: "UGC_Resource_Delete_Confirm"
}
rows {
  content: "商品找不到，请后面再试"
  switch: 1
  stringId: "UGC_Shop_Goods_NotExist"
}
rows {
  content: "你们不是同平台好友，无法邀请对方上线"
  switch: 1
  stringId: "UI_ConvenedRecordItemInviteOnlineFaile_NotPlatFriend"
}
rows {
  content: "价值<WeekendLuckyStarTips2>{0}</>元"
  switch: 1
  stringId: "UI_LuckyWeekendValueText"
}
rows {
  content: "加入失败，是否重试"
  switch: 1
  stringId: "CustomRoom_PinJoinFailed"
}
rows {
  content: "达到数量上限"
  switch: 1
  stringId: "UGC_OMD_Crystal_NumCheck_Max_Tips"
}
rows {
  content: "没有地方放置"
  switch: 1
  stringId: "Tricycle_No_Place_For_Tricycle"
}
rows {
  content: "生成失败"
  switch: 1
  stringId: "Tricycle_Spawn_Failed"
}
rows {
  content: "生成达到数量上限"
  switch: 1
  stringId: "Tricycle_Too_Many"
}
rows {
  content: "你的礼物我收到了，非常感谢，我很喜欢！"
  switch: 1
  stringId: "GiftCard_Thanks_Tips"
}
rows {
  content: "缺少绿植兑换卷，暂无法解锁"
  switch: 1
  stringId: "UI_Nongchangyanglvzhi_Noticket_tips"
}
rows {
  content: "请在app端完成订阅"
  switch: 1
  stringId: "Calendar_Not_Support_Platform"
}
rows {
  content: "你的限定皮肤在等你登录领取！"
  switch: 1
  stringId: "Login_SystemNotice_Title"
}
rows {
  content: "次日登录游戏，立刻免费领取限定皮肤，快来吧！"
  switch: 1
  stringId: "Login_SystemNotice_Content"
}
rows {
  content: "获取友谊之火"
  switch: 1
  stringId: "FriendShipFire_GetFire"
}
rows {
  content: "好友列表"
  switch: 1
  stringId: "FriendShipFire_FriendList"
}
rows {
  content: "模拟器内无法使用该功能，请下载元梦之星app使用哦！"
  switch: 1
  stringId: "Share_PC_NotSupport_Tips"
}
rows {
  content: "高清模拟器版本暂不支持购买该商品，请前往元梦之星app进行购买噢~"
  switch: 1
  stringId: "PC_NotSupport_Recharget_Tips"
}
rows {
  content: "元解锁高级版"
  switch: 1
  stringId: "UI_UnlockAdvanceTab_Text"
}
rows {
  content: "解锁高级通行证可获得"
  switch: 1
  stringId: "UI_Season_UnlockAdvancedPass_Txt"
}
rows {
  content: "再升级{0}级可领取"
  switch: 1
  stringId: "UI_Season_UpgradeReceive_Txt"
}
rows {
  content: "进行祈愿有机会获得"
  switch: 1
  stringId: "UI_Season_LotteryReceive_Txt"
}
rows {
  content: "再祈愿{0}次后必得\n<Season09ReawardTips>至臻时装</>"
  switch: 1
  stringId: "UI_Season_LotteryReceiveHigh_Txt"
}
rows {
  content: "可兑换奖励"
  switch: 1
  stringId: "UI_Season_Redeemablerewards_Txt"
}
rows {
  content: "获取王冠币可兑换"
  switch: 1
  stringId: "UI_Season_EarnCrownCoinsPass_Txt"
}
rows {
  content: "{0}提升至{1}\n可获得<Season09ReawardTips>{2}奖励</>"
  switch: 1
  stringId: "UI_Season_RankedMatch_Txt"
}
rows {
  content: "换位频繁，请稍候"
  switch: 1
  stringId: "CustomRoom_ExchangeSeatFail_WaitCd"
}
rows {
  content: "已完成的案件可以重置再次解密，今日没有消耗的重置次数将累计到次日"
  switch: 1
  stringId: "UI_WerewolfGonzoDetective_ResetInfo"
}
rows {
  content: "奖池奖励选择后不可再更换，是否确定选择当前奖励？"
  switch: 1
  stringId: "Lottery_OwnSelect_Check"
}
rows {
  content: "重复获得已拥有的奖励，将会发放替换奖励，详见活动规则"
  switch: 1
  stringId: "Lottery_OwnSelect_Replace"
}
rows {
  content: "活动规则"
  switch: 1
  stringId: "Lottery_OwnSelect_rule"
}
rows {
  content: "邀请回归好友"
  switch: 1
  stringId: "FriendShipFire_ReturnList"
}
rows {
  content: "敌人已被麻醉"
  switch: 1
  stringId: "CaptureShadowTipShaodwHasBuffer"
}
rows {
  content: "元梦之星申请使用你的QQ头像、昵称和朋友关系"
  switch: 1
  stringId: "QQAuthority_Title"
}
rows {
  content: "助力失败.请星宝更新版本后再试试哦！"
  switch: 1
  stringId: "UI_GreenHouseNew_LowVerNotSupport"
}
rows {
  content: "不支持定位功能，请使用移动端"
  switch: 1
  stringId: "UI_PC_PasswordRoom_Not_Support_Hint"
}
rows {
  content: "活动暂时无法进入"
  switch: 1
  stringId: "UI_ConanActivityClose"
}
rows {
  content: "心愿单已满无法添加商品,是否前往修改心愿单？"
  switch: 1
  stringId: "UI_WishList_CantAddWishMaxTips"
}
rows {
  content: "关注成功,更新时会提醒哦"
  switch: 1
  stringId: "UI_WishList_FollowSuccess"
}
rows {
  content: "已取消关注,将不再收到该心愿单更新提醒"
  switch: 1
  stringId: "UI_WishList_UnfollowSucces"
}
rows {
  content: "成功添加商品至心愿单"
  switch: 1
  stringId: "UI_WishList_AddWishSuccess"
}

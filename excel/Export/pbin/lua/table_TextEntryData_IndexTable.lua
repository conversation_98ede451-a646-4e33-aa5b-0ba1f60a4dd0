local table_TextEntryData_IndexTable = {}
table_TextEntryData_IndexTable.TableName = {
    "TextEntryData_AC",
    "TextEntryData_Arena",
    "TextEntryData_Arena_AutoBreak_1",
    "TextEntryData_Arena_AutoBreak_2",
    "TextEntryData_BS",
    "TextEntryData_COC",
    "TextEntryData_COC_AutoBreak_1",
    "TextEntryData_COC_AutoBreak_2",
    "TextEntryData_Card",
    "TextEntryData_Card_AutoBreak_1",
    "TextEntryData_Card_AutoBreak_2",
    "TextEntryData_Club",
    "TextEntryData_Club_AutoBreak_1",
    "TextEntryData_Club_AutoBreak_2",
    "TextEntryData_FB",
    "TextEntryData_FPS",
    "TextEntryData_FPS_AutoBreak_1",
    "TextEntryData_HOK",
    "TextEntryData_HOK_AutoBreak_1",
    "TextEntryData_Level",
    "TextEntryData_Level_AutoBreak_1",
    "TextEntryData_Level_AutoBreak_2",
    "TextEntryData_Main",
    "TextEntryData_Main_AutoBreak_1",
    "TextEntryData_Main_AutoBreak_2",
    "TextEntryData_Main_AutoBreak_3",
    "TextEntryData_Main_AutoBreak_4",
    "TextEntryData_Main_AutoBreak_5",
    "TextEntryData_Main_AutoBreak_6",
    "TextEntryData_Main_AutoBreak_7",
    "TextEntryData_Main_AutoBreak_8",
    "TextEntryData_Main_AutoBreak_9",
    "TextEntryData_Main_AutoBreak_10",
    "TextEntryData_Main_AutoBreak_11",
    "TextEntryData_Main_AutoBreak_12",
    "TextEntryData_Main_AutoBreak_13",
    "TextEntryData_Main_AutoBreak_14",
    "TextEntryData_Main_AutoBreak_15",
    "TextEntryData_Main_AutoBreak_16",
    "TextEntryData_Main_AutoBreak_17",
    "TextEntryData_Main_AutoBreak_18",
    "TextEntryData_Main_AutoBreak_19",
    "TextEntryData_Main_AutoBreak_20",
    "TextEntryData_Main_AutoBreak_21",
    "TextEntryData_Main_AutoBreak_22",
    "TextEntryData_Main_AutoBreak_23",
    "TextEntryData_Main_AutoBreak_24",
    "TextEntryData_Main_AutoBreak_25",
    "TextEntryData_Main_AutoBreak_26",
    "TextEntryData_Main_AutoBreak_27",
    "TextEntryData_Main_AutoBreak_28",
    "TextEntryData_Main_AutoBreak_29",
    "TextEntryData_Main_AutoBreak_30",
    "TextEntryData_Main_AutoBreak_31",
    "TextEntryData_Main_AutoBreak_32",
    "TextEntryData_Main_AutoBreak_33",
    "TextEntryData_Main_AutoBreak_34",
    "TextEntryData_Main_AutoBreak_35",
    "TextEntryData_Main_AutoBreak_36",
    "TextEntryData_Main_AutoBreak_37",
    "TextEntryData_Main_AutoBreak_38",
    "TextEntryData_Main_AutoBreak_39",
    "TextEntryData_Main_AutoBreak_40",
    "TextEntryData_Main_AutoBreak_41",
    "TextEntryData_Main_AutoBreak_42",
    "TextEntryData_Main_AutoBreak_43",
    "TextEntryData_Main_AutoBreak_44",
    "TextEntryData_Mayday",
    "TextEntryData_Mayday_AutoBreak_1",
    "TextEntryData_Mayday_AutoBreak_2",
    "TextEntryData_Mayday_AutoBreak_3",
    "TextEntryData_Mayday_AutoBreak_4",
    "TextEntryData_Mayday_AutoBreak_5",
    "TextEntryData_Mayday_AutoBreak_6",
    "TextEntryData_Mayday_AutoBreak_7",
    "TextEntryData_Mayday_AutoBreak_8",
    "TextEntryData_Mayday_AutoBreak_9",
    "TextEntryData_Mayday_AutoBreak_10",
    "TextEntryData_Mayday_AutoBreak_11",
    "TextEntryData_Mayday_AutoBreak_12",
    "TextEntryData_Mayday_AutoBreak_13",
    "TextEntryData_Preload",
    "TextEntryData_Preload_AutoBreak_1",
    "TextEntryData_SuitStory",
    "TextEntryData_System",
    "TextEntryData_System_AutoBreak_1",
    "TextEntryData_System_AutoBreak_2",
    "TextEntryData_System_AutoBreak_3",
    "TextEntryData_System_AutoBreak_4",
    "TextEntryData_System_AutoBreak_5",
    "TextEntryData_System_AutoBreak_6",
    "TextEntryData_System_AutoBreak_7",
    "TextEntryData_System_AutoBreak_8",
    "TextEntryData_System_AutoBreak_9",
    "TextEntryData_System_AutoBreak_10",
    "TextEntryData_System_AutoBreak_11",
    "TextEntryData_System_AutoBreak_12",
    "TextEntryData_System_AutoBreak_13",
    "TextEntryData_System_AutoBreak_14",
    "TextEntryData_System_AutoBreak_15",
    "TextEntryData_System_AutoBreak_16",
    "TextEntryData_System_AutoBreak_17",
    "TextEntryData_System_AutoBreak_18",
    "TextEntryData_TYC",
    "TextEntryData_TYC_AutoBreak_1",
    "TextEntryData_TYC_AutoBreak_2",
    "TextEntryData_TYC_AutoBreak_3",
    "TextEntryData_TYC_AutoBreak_4",
    "TextEntryData_TYC_AutoBreak_5",
    "TextEntryData_Travel",
    "TextEntryData_UGCProgramme",
    "TextEntryData_UGCRPG",
    "TextEntryData_chase",
    "TextEntryData_chase_AutoBreak_1",
    "TextEntryData_chase_AutoBreak_2",
    "TextEntryData_chase_AutoBreak_3",
    "TextEntryData_chase_AutoBreak_4",
    "TextEntryData_chase_AutoBreak_5",
    "TextEntryData_chase_AutoBreak_6",
    "TextEntryData_chase_AutoBreak_7",
    "TextEntryData_chase_AutoBreak_8",
    "TextEntryData_chase_AutoBreak_9",
    "TextEntryData_chase_AutoBreak_10",
    "TextEntryData_chase_AutoBreak_11",
    "TextEntryData_chest"
}
table_TextEntryData_IndexTable.AutoBreakTables = {
    ["TextEntryData_Arena"] = 2,
    ["TextEntryData_COC"] = 2,
    ["TextEntryData_Card"] = 2,
    ["TextEntryData_Club"] = 2,
    ["TextEntryData_FPS"] = 1,
    ["TextEntryData_HOK"] = 1,
    ["TextEntryData_Level"] = 2,
    ["TextEntryData_Main"] = 44,
    ["TextEntryData_Mayday"] = 13,
    ["TextEntryData_Preload"] = 1,
    ["TextEntryData_System"] = 18,
    ["TextEntryData_TYC"] = 5,
    ["TextEntryData_chase"] = 11,
}
table_TextEntryData_IndexTable.Data = {
    ["DDB_InLevel_Tips_1"] = 1,
    ["DDB_InLevel_Counting_Title"] = 1,
    ["DDB_InLevel_ChasingTips"] = 1,
    ["DDB_InLevel_LeftMsgFmt_KO"] = 1,
    ["DDB_InLevel_LeftMsgFmt_SelfMistake"] = 1,
    ["DDB_InLevel_LeftMsgFmt_Winner"] = 1,
    ["DDB_InLevel_LeftMsgFmt_KO_Normal"] = 1,
    ["DDB_InLevel_PlayerOutBallTip"] = 1,
    ["DDB_InLevel_RoundStartBallTip"] = 1,
    ["DDB_InLevel_RespawnBallCommonTip"] = 1,
    ["DDB_Prop_ScreamingChicken_Name"] = 1,
    ["DDB_Prop_DuckKing_Name"] = 1,
    ["DDB_Prop_Poision_Name"] = 1,
    ["DDB_Prop_Shield_Name"] = 1,
    ["DDB_Prop_Backtrack_Name"] = 1,
    ["DDB_Prop_Rush_Name"] = 1,
    ["DDB_Prop_SpeedUpDDB_Name"] = 1,
    ["DDB_Prop_StrengthPotions_Name"] = 1,
    ["DDB_Prop_TomatoDDB_Name"] = 1,
    ["DDB_Prop_Freeze_Name"] = 1,
    ["DDB_Prop_Stealth_Name"] = 1,
    ["DDB_InLevel_Tips_FreezingBall"] = 1,
    ["DDB_InLevel_WinnerToast"] = 1,
    ["DDB_FinalAccount_TableTitle_1"] = 1,
    ["DDB_InLevel_Roud1"] = 1,
    ["DDB_InLevel_Roud2"] = 1,
    ["DDB_InLevel_Roud3"] = 1,
    ["DDB_InLevel_Roud4"] = 1,
    ["DDB_InLevel_Roud5"] = 1,
    ["DDB_InLevel_CenterMsgFmt_KO_Win"] = 1,
    ["DDB_InLevel_CenterMsgFmt_KO_Lose"] = 1,
    ["DDB_InLevel_CenterMsgFmt_SelfMistake"] = 1,
    ["DDB_InLevel_CenterMsgFmt_Winner"] = 1,
    ["DDB_GrowUp_Buff"] = 1,
    ["DDB_SpeedUp_Buff"] = 1,
    ["DDB_Rage_Buff"] = 1,
    ["DDB_Shield_Buff"] = 1,
    ["DDB_Stamina_Buff"] = 1,
    ["DDB_Choose_Rage"] = 1,
    ["DDB_Choose_Shield"] = 1,
    ["DDB_Choose_Freeze"] = 1,
    ["DDB_Choose_Tomato"] = 1,
    ["DDB_Choose_Backtracking"] = 1,
    ["Arena_RoundPrepare"] = 2,
    ["Arena_RoundBattle"] = 2,
    ["Arena_BattleDetail"] = 2,
    ["Arena_MyAttribute"] = 2,
    ["Arena_MySkill"] = 2,
    ["Arena_PoisonCircleRefreshTip"] = 2,
    ["Arena_PickRoleDeadline"] = 2,
    ["Arena_SkillCDContent"] = 2,
    ["Arena_ShowingPickCardTipStart"] = 2,
    ["Arena_ShowingPickCardTipDeadline"] = 2,
    ["Arena_ShowingPickCardTipReady"] = 2,
    ["Arena_ShowingPickCardTip"] = 2,
    ["Arena_ReadyPick"] = 2,
    ["Arena_Picked"] = 2,
    ["Arena_ShowingCardBroastTitle"] = 2,
    ["Arena_ShowingCardBroastContent"] = 2,
    ["Arena_FinalBroastTitle"] = 2,
    ["Arena_FinalBroastContent"] = 2,
    ["Arena_AllPickReadyToCombat"] = 2,
    ["Arena_Skill_Passive"] = 2,
    ["Arena_RoleSelectFailReason"] = 2,
    ["Arena_SkillCDSecond"] = 2,
    ["Arena_ShowingCardEliminated"] = 2,
    ["Arena_Intro_1"] = 2,
    ["Arena_Intro_1_Text"] = 2,
    ["Arena_Intro_2"] = 2,
    ["Arena_Intro_2_1"] = 2,
    ["Arena_Intro_2_1_Text"] = 2,
    ["Arena_Intro_2_2"] = 2,
    ["Arena_Intro_2_2_Text"] = 2,
    ["Arena_Intro_2_3"] = 2,
    ["Arena_Intro_2_3_Text"] = 2,
    ["Arena_Intro_3"] = 2,
    ["Arena_Intro_3_Text"] = 2,
    ["Arena_Intro_4"] = 2,
    ["Arena_Intro_4_Text"] = 2,
    ["Arena_Intro_5"] = 2,
    ["Arena_Intro_5_Text"] = 2,
    ["Arena_Intro_6"] = 2,
    ["Arena_Intro_6_1"] = 2,
    ["Arena_Intro_6_1_Text"] = 2,
    ["Arena_Intro_6_2"] = 2,
    ["Arena_Intro_6_2_Text"] = 2,
    ["Arena_Intro_7"] = 2,
    ["Arena_Intro_7_Text"] = 2,
    ["Arena_Honor_Limit"] = 2,
    ["Arena_Kill_Broadcast_Player_Revenge"] = 2,
    ["Arena_Kill_Broadcast_Team_Revenge"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_FirstBlood"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_DoubleKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_TripleKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_QuadraKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_PentaKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_GodLike"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_Legendary"] = 2,
    ["Arena_Kill_Broadcast_Kill_Slay"] = 2,
    ["Arena_FinalAccount_Detail_Text"] = 2,
    ["Arena_FinalAccount_Detail_Round"] = 2,
    ["Arena_HostingByAI_Tips_1"] = 2,
    ["Arena_HostingByAI_Tips_2"] = 2,
    ["Arena_CoinIsNotEnough"] = 2,
    ["Arena_BuyHeroFail"] = 2,
    ["Arena_CardGroup_Changed_Save"] = 2,
    ["Arena_CardGroup_Changed_Save_GiveUp"] = 2,
    ["Arena_CardGroup_Changed_SaveMessage"] = 2,
    ["Arena_Stage_Prepare"] = 2,
    ["Arena_Stage_Combat"] = 2,
    ["Arena_ItemUseNoPakGroup"] = 2,
    ["Arena_Herochoice"] = 2,
    ["Arena_Rank_1"] = 2,
    ["Arena_Rank_2"] = 2,
    ["Arena_Rank_3"] = 2,
    ["Arena_Rank_4"] = 2,
    ["Arena_Discount"] = 2,
    ["Arena_HeroCardNotOpen"] = 2,
    ["Arena_Discount_OverTime"] = 2,
    ["Arena_Rank_Not_Unlock"] = 2,
    ["Arena_Prepararion_Banner_OverTime"] = 2,
    ["Arena_SkillName_1"] = 2,
    ["Arena_SkillName_2"] = 2,
    ["Arena_SkillName_3"] = 2,
    ["Arena_SendQuickMessage"] = 2,
    ["Arena_CanNotUseInTheMode"] = 2,
    ["Arena_CanNotUseInTheModeToast"] = 2,
    ["Arena_ChatHeroName"] = 2,
    ["Arnea_GigtAddFriend"] = 2,
    ["Arena_CannotCharge_InBattle"] = 2,
    ["HotZone_ZoneRefreshCountDown"] = 2,
    ["HotZone_ZoneMoveCountDown"] = 2,
    ["Arena_DownloadCanSee"] = 2,
    ["Arena_Train_Card_Tab_1_1"] = 2,
    ["Arena_Train_Card_Tab_1_2"] = 2,
    ["Arena_Train_Card_Tab_1_3"] = 2,
    ["Arena_Train_Card_Max_Tip"] = 2,
    ["Arena_Train_Card_Tab_2_9"] = 2,
    ["Arena_Train_Card_Tab_2_10"] = 2,
    ["Arena_Train_Card_Tab_2_11"] = 2,
    ["Arena_Train_Card_Tab_2_12"] = 2,
    ["Arena_Train_Card_Sort_1"] = 2,
    ["Arena_Train_Card_Sort_2"] = 2,
    ["Arena_Train_Card_Title_1"] = 3,
    ["Arena_Train_Card_Title_2"] = 3,
    ["Set_EditKey_Arena3v3"] = 3,
    ["Arena_PerRankDetails_Title1"] = 3,
    ["Arena_PerRankDetails_Title2"] = 3,
    ["Arena_PerRankInfo_Title1"] = 3,
    ["Arena_PerRankInfo_Title2"] = 3,
    ["Arena_PerRankMark_Title1"] = 3,
    ["Arena_PerRankMark_Title2"] = 3,
    ["Arena_PerRankMark_OpenTime"] = 3,
    ["Arena_PerRankMark_SonalName"] = 3,
    ["Arena_PerRankDetailds_Ranking"] = 3,
    ["Arena_PerRankInfo_NoneTip"] = 3,
    ["Arena_PerRankMark_NoneTip"] = 3,
    ["Arena_Train_DMG_DPS"] = 3,
    ["Arena_Train_DMG_Physical"] = 3,
    ["Arena_Train_DMG_Magic"] = 3,
    ["Arena_Train_DMG_Real"] = 3,
    ["Arena_PerRankInfo_BattleCount"] = 3,
    ["Arena_PerRankInfo_WinCount"] = 3,
    ["Arena_PerRankInfo_WinRate"] = 3,
    ["Arena_PerRankInfo_MVP"] = 3,
    ["Arena_PerRankInfo_HeroWinCount"] = 3,
    ["Arena_PerRankInfo_HeroBattleCount"] = 3,
    ["Arena_GameOver"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Champion"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Level"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Eliminate"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_WinStreak"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_FinalLevelRank"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Protected"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Additional"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_FinalDimension"] = 3,
    ["Arena_HotZone_Win"] = 3,
    ["Arena_HotZone_Lose"] = 3,
    ["Arena_Training_CardClear"] = 3,
    ["Arena_Training_ConfirmClear"] = 3,
    ["HotZone_ZoneShow"] = 3,
    ["HotZone_ProgressTipSelf"] = 3,
    ["HotZone_ProgressTipEnemy"] = 3,
    ["Arena_HeroBan"] = 3,
    ["Arena_HeroPick"] = 3,
    ["Arena_HeroIsBan"] = 3,
    ["Arena_HeroIslock"] = 3,
    ["Arena_HeroPreFormTeam"] = 3,
    ["Arena_HeroTryBuyPop"] = 3,
    ["Arena_Imprint_Tips"] = 3,
    ["Arena_PleryInfo_Career_Title_1"] = 3,
    ["Arena_PleryInfo_Career_Title_2"] = 3,
    ["Arena_PleryInfo_Career_Title_3"] = 3,
    ["Arena_PleryInfo_Career_Title_4"] = 3,
    ["Arena_PleryInfo_Career_Title_5"] = 3,
    ["Arena_PleryInfo_Career_Title_6"] = 3,
    ["Arena_PleryInfo_Career_Title_7"] = 3,
    ["Arena_RoadToHonor_NewHero"] = 3,
    ["Arena_RoadToHonor_NewCard"] = 3,
    ["Arena_RoadToHonor_NewSkin"] = 3,
    ["Arena_RoadToHonor_NewSkin_1"] = 3,
    ["Arena_ShardExchange_Message_Sign"] = 3,
    ["Arena_Shenmibaoxiang_Title_1"] = 3,
    ["Arena_Shenmibaoxiang_Title_2"] = 3,
    ["Arena_Recommend_Couple"] = 3,
    ["Arena_Recommend_Chum"] = 3,
    ["Arena_Recommend_Brother"] = 3,
    ["Arena_Recommend_Girlfriends"] = 3,
    ["Arena_Recommend_Friend_HighRank"] = 3,
    ["Arena_Recommend_Friend_HighIntimate"] = 3,
    ["Arena_Recommend_Friend"] = 3,
    ["Arena_Recommend_LastMVP"] = 3,
    ["Arena_Recommend_Win100"] = 3,
    ["Arena_Recommend_Legendary"] = 3,
    ["Arena_Recommend_WinStreak5"] = 3,
    ["Arena_Ranking_None"] = 3,
    ["Arena_Hero_Star"] = 3,
    ["Arena_Ranking_Global"] = 3,
    ["Arena_Recommend_Lobby"] = 3,
    ["Arena_Honor_Stay_Tuned"] = 3,
    ["Arena_Customize_Broadcast"] = 3,
    ["Arena_Customize_Mood"] = 3,
    ["Arena_Customize_HeadFrame"] = 3,
    ["Arena_Customize_SparyPaint"] = 3,
    ["Arena_Honor_Three_One"] = 3,
    ["Arena_Honor_Two_One"] = 3,
    ["Arena_Honor_Cur_Star"] = 3,
    ["Arena_In_Level_Download_Tip"] = 3,
    ["Arena_DayBox_Tips"] = 3,
    ["Arena_Invite_Platform"] = 3,
    ["Arena_DayBox_Tips_HY"] = 3,
    ["Arena_DayBox_Tips_QS"] = 3,
    ["Arena_HeroTryBuyPopAndName"] = 3,
    ["Arena_Arena_BattleHighest_Tips1"] = 3,
    ["Arena_Arena_BattleHighest_Tips2"] = 3,
    ["Arena_HostingByAI_Tips_3"] = 3,
    ["Arena_MatchLocked_Tips"] = 3,
    ["Arena_ArenaCharacter_GameClientFail"] = 4,
    ["Arena_ArenaMainGamePlayerInfo_CD"] = 4,
    ["Arena_HOKGameInfo_TryJoinPoint"] = 4,
    ["Arena_UI_HOK_RoleBan_Main_OpenBan"] = 4,
    ["Arena_UI_HOK_Strategy_ShuntAvatarItem_HeroLock"] = 4,
    ["Arena_UI_HOK_Train_RoleSelect_CantGetHeroCfg"] = 4,
    ["Arena_ArenaPeripheralServerDatas_EquipmentSavedSuccessfully"] = 4,
    ["Arena_ArenaPeripheralServerDatas_EquipmentSavedFail"] = 4,
    ["Arena_ArenaPeripheralSystemDataModel_TrainingNotAllow"] = 4,
    ["Arena_UI_Arena_AvatarItem_NeedLock"] = 4,
    ["Arena_UI_Arena_CardSelection_GetCard"] = 4,
    ["Arena_UI_Arena_Preparations_Main_OpenBan"] = 4,
    ["Arena_UI_Arena_Preparations_Main_TeammateIsDiamondOpenBan"] = 4,
    ["Arena_UI_Arena_Preparations_Main_OnlyCaptain"] = 4,
    ["Arena_UI_Arena_Preparations_Main_NotAllowedInTMode"] = 4,
    ["Arena_UI_Arena_Preparations_Main_RankMismatch"] = 4,
    ["Arena_TeamBubble_InvitationSent"] = 4,
    ["Arena_TeamBubble_IsAlreadyInGame"] = 4,
    ["Arena_HeroMainView_SwitchingTooFast"] = 4,
    ["Arena_HeroSkinViewItem_ResDownloading"] = 4,
    ["Arena_RoadToHonorItem_NeedToReceivePreReward"] = 4,
    ["Arena_ExchangeView_InsufficientFragments"] = 4,
    ["Arena_GiftPackPickView_PleaseSelectTheReward"] = 4,
    ["Arena_CardEdit_HaventCard"] = 4,
    ["Arena_ModeSelect_ComingSoon"] = 4,
    ["Arena_BackPlayerInfo"] = 4,
    ["Arena_MaxPrower"] = 4,
    ["Arena_HeroPlayTimes"] = 4,
    ["Arena_HeroWinRate"] = 4,
    ["Arena_Common_All"] = 4,
    ["Arena_CardTypeDesc_NoSuit"] = 4,
    ["Arena_Hero_Lock"] = 4,
    ["Arena_GoldenEgg_ToDoTaskGoldenEgg"] = 4,
    ["Arena_GoldenEgg_ClickGoldenEgg"] = 4,
    ["Arena_GoldenEgg_OverClickGoldenEgg"] = 4,
    ["Arena_GoldenEgg_OverGoldenEgg"] = 4,
    ["Arena_GoldenEgg_ClickGoldenEgg_Tips"] = 4,
    ["Arena_EditCard_Title1"] = 4,
    ["Arena_EditCard_Title2"] = 4,
    ["Arena_battleFractionTips1"] = 4,
    ["Arena_battleFractionTips2"] = 4,
    ["Arena_WeekendWindow_Content_1000063"] = 4,
    ["Arena_WeekendWindow_TiTle_1000063"] = 4,
    ["Arena_WeekendWindow_Content_1000066"] = 4,
    ["Arena_WeekendWindow_TiTle_1000066"] = 4,
    ["BS_CardBeenLocked"] = 5,
    ["BS_Kill"] = 5,
    ["BS_Defeat"] = 5,
    ["BS_DefeatStreak"] = 5,
    ["BS_Revived"] = 5,
    ["BS_ScoreReach"] = 5,
    ["BS_Goal"] = 5,
    ["BS_Careful"] = 5,
    ["BS_Duel"] = 5,
    ["BS_ChestSpawn"] = 5,
    ["BS_ScoreReach1"] = 5,
    ["BS_ScoreReach2"] = 5,
    ["BS_WaitRevive"] = 5,
    ["BS_ScoreReach1Img"] = 5,
    ["BS_ScoreReach2Img"] = 5,
    ["BS_TOXICKILL"] = 5,
    ["Arena_Rank_5"] = 5,
    ["BS_CallHelp"] = 5,
    ["BS_CallHelpBack"] = 5,
    ["BS_PortalReady"] = 5,
    ["BS_PortalCD"] = 5,
    ["UI_COC_QuitSaveConfirm"] = 6,
    ["UI_COC_QuitSaveConfirm_Save"] = 6,
    ["UI_COC_QuitSaveConfirm_NoSave"] = 6,
    ["UI_COC_InCold"] = 6,
    ["UI_COC_PleaseChooseTeamFirst"] = 6,
    ["UI_COC_DeployMonster_Invalid"] = 6,
    ["UI_COC_DeployMonster_Tips"] = 6,
    ["UI_COC_MatchFailed"] = 6,
    ["BUILDING_COC_BuildLimit"] = 6,
    ["BUILDING_COC_ResNotEnough"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityLimitExceeded"] = 6,
    ["UI_COC_CocsoldierLevelError"] = 6,
    ["UI_COC_CocsoldierTrainingAlreadyAccelerated"] = 6,
    ["BUILDING_COC_ConcurrentUpgradeLimit"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityLimitExceeded2"] = 6,
    ["UI_COC_COC_DiamondNotEnough"] = 6,
    ["UI_COC_CocSoldierDelete1"] = 6,
    ["UI_COC_CocSoldierDelete2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityNotEnough"] = 6,
    ["UI_COC_DialogInvestigativeDetails_NotEnough"] = 6,
    ["UI_COC_CurrencyTitle"] = 6,
    ["UI_COC_CurrencyBuyDesc"] = 6,
    ["UI_COC_CurrencyBuyTips"] = 7,
    ["UI_COC_CurrencyShow1"] = 6,
    ["UI_COC_RescourceAcc"] = 6,
    ["UI_COC_RescourceAccDetail1"] = 6,
    ["UI_COC_CurrencyShow2"] = 6,
    ["UI_COC_RescourceAccDetail2"] = 6,
    ["UI_COC_RescourceDetailStore1"] = 6,
    ["UI_COC_RescourceDetailStore2"] = 6,
    ["UI_COC_RescourceDetailSpeed1"] = 6,
    ["UI_COC_RescourceDetailSpeed2"] = 6,
    ["UI_COC_BuildingFinish1"] = 6,
    ["UI_COC_BuildingFinish2"] = 6,
    ["UI_COC_HudInvestigativeCompleteTip"] = 6,
    ["UI_COC_BuildingLevel"] = 6,
    ["UI_COC_MailMainView_Tab1"] = 6,
    ["UI_COC_MailMainView_Tab2"] = 6,
    ["UI_COC_MailMainView_Tab3"] = 6,
    ["UI_COC_MailMainView_Num"] = 6,
    ["UI_COC_ItemInvestigative_NeedTip"] = 6,
    ["COCBuildMsgModel_ReqUpgradeScience_Full"] = 6,
    ["UI_COC_BuildingFinishImmediate"] = 6,
    ["UI_COC_CocSoldierDeleteCancel"] = 6,
    ["UI_COC_ResearchFinishImmediateContent1"] = 6,
    ["UI_COC_ResearchFinishImmediateContent2"] = 6,
    ["UI_COC_SoldierTrainFinishImmediateContent1"] = 6,
    ["UI_COC_SoldierTrainFinishImmediateContent2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTip"] = 6,
    ["UI_COC_CocVillagerDeleteCancel"] = 6,
    ["UI_COC_CocVillagerChooseCancel"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityNotEnough2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacitySet"] = 6,
    ["UI_COC_CocSoldierTrainingCapacitySet2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityBillboard"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityBillboard2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityReservist"] = 6,
    ["UI_COC_CocVillagerRecruitCancelTitle"] = 6,
    ["UI_COC_CocVillagerRecruitCancelDesc1"] = 6,
    ["UI_COC_CocVillagerRecruitCancelDesc2"] = 6,
    ["UI_COC_CocVillagerRenameCancelDesc"] = 6,
    ["UI_COC_MonthCardName"] = 6,
    ["UI_COC_MonthCardDesc"] = 6,
    ["UI_COC_MonthCardPrivilege1"] = 6,
    ["UI_COC_MonthCardPrivilege2_1"] = 6,
    ["UI_COC_MonthCardPrivilege2_2"] = 6,
    ["UI_COC_MonthCardPrivilegeDesc"] = 6,
    ["UI_COC_MonthCardDailyText1"] = 6,
    ["UI_COC_MonthCardDailyText2"] = 6,
    ["UI_COC_MonthCardDailyText3"] = 6,
    ["UI_COC_MonthCardDailyMax"] = 6,
    ["UI_COC_MonthCardDailyMaxDesc"] = 6,
    ["UI_COC_MonthCardDailyItemDesc"] = 6,
    ["UI_COC_MonthCardDailyItemDesc1"] = 6,
    ["UI_COC_MonthCardDailyItemDesc2"] = 6,
    ["UI_COC_MonthCardBuy"] = 6,
    ["UI_COC_MonthCardRenew"] = 6,
    ["UI_COC_MonthCardWay"] = 6,
    ["UI_COC_MonthCardBuyDesc"] = 6,
    ["UI_COC_Time_DayHour"] = 6,
    ["UI_COC_Time_MinuteSecond"] = 6,
    ["UI_COC_TrainAcc"] = 6,
    ["UI_COC_TrainAccDetail1"] = 6,
    ["UI_COC_BuildHomeCost"] = 6,
    ["UI_COC_BuildHomeCostDetail1"] = 6,
    ["UI_COC_TempBuildHomeCost"] = 6,
    ["UI_COC_TempBuildHomeCostDetail1"] = 6,
    ["UI_COC_PeopleUnlock"] = 6,
    ["UI_COC_PeopleUnlockDetail1"] = 6,
    ["UI_COC_PVERewardsAlreadyCollected"] = 6,
    ["UI_COC_MonthCardHelpTitle"] = 6,
    ["UI_COC_MonthCardHelpSubTitle"] = 6,
    ["UI_COC_MonthCardHelpContent"] = 6,
    ["UI_COC_VillagerBuild"] = 6,
    ["UI_COC_CocSoldierSkillOpen"] = 6,
    ["UI_COC_TempBuildHomeTips"] = 6,
    ["UI_COC_TempBuildHomeReplace"] = 6,
    ["UI_COC_NewQueueWay1"] = 6,
    ["UI_COC_NewQueueWay2"] = 6,
    ["UI_COC_WallUpdateDetail1"] = 6,
    ["UI_COC_WallUpdateDetail2"] = 6,
    ["UI_COC_WallUpdateTips1"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityName"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips1"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips2"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips3"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips4"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips5"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips6"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips7"] = 7,
    ["UI_COC_CocSoldierTrainingCapacityTips8"] = 7,
    ["UI_COC_ResearchTips1"] = 7,
    ["UI_COC_AppointName1"] = 7,
    ["UI_COC_AppointName2"] = 7,
    ["UI_COC_GroundMode"] = 7,
    ["UI_COC_GroundAirMode"] = 7,
    ["UI_COC_AirMode"] = 7,
    ["UI_COC_RescueTime"] = 7,
    ["UI_COC_RescueText"] = 7,
    ["UI_COC_InviteFriendSuccess"] = 7,
    ["UI_COC_RewardWithItemNum"] = 7,
    ["UI_COC_SwitchMode"] = 7,
    ["UI_COC_HelpClickTips"] = 7,
    ["UI_COC_Help1"] = 7,
    ["UI_COC_Help2"] = 7,
    ["UI_COC_Help3"] = 7,
    ["UI_COC_Help4"] = 7,
    ["UI_COC_HelpRecord"] = 7,
    ["UI_COC_QueueShow"] = 7,
    ["UI_COC_GroundTarget"] = 7,
    ["UI_COC_GroundAirTarget"] = 7,
    ["UI_COC_AirTarget"] = 7,
    ["UI_COC_CocVillagerUnlock"] = 7,
    ["UI_COC_PVEFailNoSoldier"] = 7,
    ["UI_COC_PVEFailTownCenter"] = 7,
    ["UI_COC_PVEFailLevel"] = 7,
    ["UI_COC_PVEFailConfig"] = 7,
    ["UI_COC_AllMainQuestFinished"] = 7,
    ["UI_COC_NoticeBuild"] = 7,
    ["UI_COC_NoticeTrain"] = 7,
    ["UI_COC_NoticeResearch"] = 7,
    ["UI_COC_NoticeTitle"] = 7,
    ["UI_COC_JAIL_upgradeinfo_1"] = 7,
    ["UI_COC_JAIL_upgradeinfo_2"] = 7,
    ["UI_COC_JAIL_upgradeinfo_3"] = 7,
    ["UI_COC_JAIL_upgradeinfo_4"] = 7,
    ["UI_COC_JAIL_upgradeinfo_5"] = 7,
    ["UI_COC_JAIL_upgradeinfo_6"] = 7,
    ["UI_COC_JAIL_upgradeinfo_7"] = 7,
    ["UI_COC_JAIL_upgradeinfo_8"] = 7,
    ["UI_COC_JAIL_inmateinfo_1"] = 7,
    ["UI_COC_JAIL_inmateinfo_2"] = 7,
    ["UI_COC_JAIL_inmateinfo_3"] = 7,
    ["UI_COC_JAIL_inmateinfo_4"] = 7,
    ["UI_COC_JAIL_inmateinfo_5"] = 7,
    ["UI_COC_JAIL_release_1"] = 7,
    ["UI_COC_JAIL_release_2"] = 7,
    ["Ul_COC_JAlL_guide_info"] = 7,
    ["UI_COC_MainBtnAttack"] = 7,
    ["UI_COC_MainBtnChallenge"] = 7,
    ["UI_COC_MainBtnDiscuss"] = 7,
    ["UI_COC_MainBtnGoHome"] = 7,
    ["UI_COC_StarAttackBtn_Lock"] = 7,
    ["UI_COC_StarManSkill"] = 7,
    ["UI_COC_BuildBuilding"] = 7,
    ["UI_COC_CaptureText"] = 7,
    ["Ul_COC_JAlL_prisoner_info_1"] = 7,
    ["Ul_COC_JAlL_prisoner_info_2"] = 7,
    ["Ul_COC_JAlL_prisoner_info_3"] = 7,
    ["Ul_COC_JAlL_prisoner_info_4"] = 7,
    ["Ul_COC_JAlL_accelerate_ban_info_1"] = 7,
    ["Ul_COC_JAlL_accelerate_ban_info_2"] = 7,
    ["Ul_COC_JAlL_accelerate_ban_info_3"] = 7,
    ["UI_COC_FeatureUnlockText"] = 7,
    ["UI_COC_DefenceBattleStartText"] = 7,
    ["UI_COC_DefenceBattleWaveText"] = 7,
    ["UI_COC_VillagerFashion1"] = 7,
    ["UI_COC_VillagerFashion2"] = 7,
    ["UI_COC_TrophyLevel"] = 7,
    ["UI_COC_TrophyLevelRule"] = 7,
    ["UI_COC_TrophyLevelRuleSpec1"] = 7,
    ["UI_COC_TrophyRank"] = 7,
    ["UI_COC_TrophyRankRule"] = 7,
    ["UI_COC_TrophyRankRuleSpec1"] = 7,
    ["UI_COC_ProsperityRank"] = 7,
    ["UI_COC_ProsperityRankRule"] = 7,
    ["UI_COC_ProsperityRankRuleSpec1"] = 7,
    ["UI_COC_NotEnoughStorageNotification"] = 7,
    ["UI_COC_ResearchNotEnoughNotification"] = 7,
    ["UI_COC_AlreadyReceiveGiftNotification"] = 7,
    ["UI_COC_NotEnoughFire"] = 7,
    ["UI_COC_BuildingFinish3"] = 7,
    ["UI_COC_BuildingFinish4"] = 7,
    ["UI_COC_BuildingRevamp"] = 7,
    ["UI_COC_BuildingUpgradeTips"] = 7,
    ["UI_COC_RevampUnlockCondition"] = 7,
    ["UI_COC_RevampNext"] = 7,
    ["UI_COC_RevampProcess"] = 7,
    ["UI_COC_RevampResNotEnough"] = 7,
    ["UI_COC_RevampTips1"] = 7,
    ["UI_COC_FastFinishTitle1"] = 8,
    ["UI_COC_FastFinishTitle2"] = 8,
    ["UI_COC_FastFinishTitle3"] = 8,
    ["UI_COC_FastFinishTips1"] = 8,
    ["UI_COC_FastFinishBuild1"] = 8,
    ["UI_COC_FastFinishBuild2"] = 8,
    ["UI_COC_FastFinishBuild3"] = 8,
    ["UI_COC_FastFinishScience1"] = 8,
    ["UI_COC_FastFinishScience2"] = 8,
    ["Card_Main_Name"] = 9,
    ["Card_Main_Title"] = 9,
    ["Card_Collection_All_Reward"] = 9,
    ["Card_Collection_Cup"] = 9,
    ["Card_Main_Deck_Button_Name"] = 9,
    ["Card_Package_Des"] = 9,
    ["Card_Main_Give_Button_Name"] = 9,
    ["Card_Main_History_Button_Name"] = 9,
    ["Card_Collection_Deck_Reward"] = 9,
    ["Card_Main_Help"] = 9,
    ["Card_Request_Base"] = 9,
    ["Card_Request_Ing_Self"] = 9,
    ["Card_Request_Player_Give_Self"] = 9,
    ["Card_Request_TimeOut_Self"] = 9,
    ["Card_Request_Answer_Other"] = 9,
    ["Card_Request_Count_Less_Other"] = 9,
    ["Card_Request_Player_Give_Other"] = 9,
    ["Card_Request_TimeOut_Other"] = 9,
    ["Card_Give_Base"] = 9,
    ["Card_Give_Ing_Self"] = 9,
    ["Card_Give_Player_Get_Self"] = 9,
    ["Card_Give_TimeOut_Self"] = 9,
    ["Card_Give_Answer_Other"] = 9,
    ["Card_Give_Player_Get_Other"] = 9,
    ["Card_Give_TimeOut_Other"] = 9,
    ["Card_Give_Other_Self"] = 9,
    ["Card_Request_Other_Self"] = 9,
    ["Card_Chat_Give_Other_Self"] = 9,
    ["Card_Chat_Reqeust_Self_Other"] = 9,
    ["Card_Confirm_Request_Single_Player"] = 9,
    ["Card_Confirm_Give_Players"] = 9,
    ["Card_Confirm_Request_Chat"] = 9,
    ["Card_Confirm_Give_Chat"] = 9,
    ["Card_Confirm_Button_Give_Name"] = 9,
    ["Card_Confirm_Button_Request_Name"] = 9,
    ["Card_History_Give_WaitAnswer_Other"] = 9,
    ["Card_History_Give_Chat"] = 9,
    ["Card_History_Give_WaitAnswer_Self"] = 9,
    ["Card_History_Request_WaitAnswer_Other"] = 9,
    ["Card_History_Request_WaitAnswer_Self"] = 9,
    ["Card_History_Give_TimeOut"] = 9,
    ["Card_History_Request_TimeOut"] = 9,
    ["Card_Give_Pop_Des"] = 9,
    ["Card_Tips_Request_Limit"] = 9,
    ["Card_Tips_Give_Limit"] = 9,
    ["Card_Tips_Request_Friend_Limit"] = 9,
    ["Card_Tips_Give_Max"] = 9,
    ["Card_Chat_Give_Limit"] = 9,
    ["Card_Preview_Glod"] = 9,
    ["Card_Preview_Give_Button_Name"] = 9,
    ["Card_Preview_Give_Button_Count"] = 9,
    ["Card_Preview_Request_Button_Name"] = 9,
    ["Card_Channel_Title_Give"] = 9,
    ["Card_Channel_Title_Request"] = 9,
    ["Card_Channel_Title_Chat"] = 9,
    ["Card_Channel_Title_Friend"] = 9,
    ["Card_History_Request_Private_Angain_Button"] = 9,
    ["Card_History_Request_Private_Angain"] = 9,
    ["Card_History_Request_Private_Other_Angain_Button"] = 9,
    ["Card_History_Request_Private_Other_Angain"] = 9,
    ["Card_History_Request_Private_Over"] = 9,
    ["Card_History_Request_Private_Other_Over"] = 9,
    ["Card_History_Request_Private_TimeOut"] = 9,
    ["Card_History_Request_Private_Other_TimeOut"] = 9,
    ["Card_History_Request_Chat_Angain_Button"] = 9,
    ["Card_History_Request_Chat_Angain"] = 9,
    ["Card_History_Request_Chat_Over"] = 9,
    ["Card_History_Request_Chat_TimeOut"] = 9,
    ["Card_History_Request_Chat_Answer_Over"] = 9,
    ["Card_History_Give_Private_Angain_Button"] = 9,
    ["Card_History_Give_Private_Angain"] = 9,
    ["Card_History_Give_Private_Other_Angain_Button"] = 9,
    ["Card_History_Give_Private_Other_Angain"] = 9,
    ["Card_History_Give_Private_Over"] = 9,
    ["Card_History_Give_Private_Other_Over"] = 9,
    ["Card_History_Give_Private_TimeOut"] = 9,
    ["Card_History_Give_Private_Other_TimeOut"] = 9,
    ["Card_History_Give_Chat_Angain_Button"] = 9,
    ["Card_History_Give_Chat_Angain"] = 9,
    ["Card_History_Give_Chat_Over"] = 9,
    ["Card_History_Give_Chat_TimeOut"] = 9,
    ["Card_History_Give_Chat_Answer_Over"] = 9,
    ["Card_History_Give_Title"] = 9,
    ["Card_History_Request_Title"] = 9,
    ["Card_Friend_Select"] = 9,
    ["Card_Friend_Time"] = 9,
    ["Card_Chat_Request_Color_Blue"] = 9,
    ["Card_Chat_Request_Color_Gold"] = 9,
    ["Card_GetCollection_Deck"] = 9,
    ["Card_GetCollection_Set"] = 9,
    ["Card_Friend_Request_Title"] = 9,
    ["Card_GetCollection_RewardTitle"] = 9,
    ["Card_Circle_Limit_Des"] = 9,
    ["Card_History_Exchange_Title"] = 9,
    ["Card_History_Exchange_Private_Self_To_Others_Again"] = 9,
    ["Card_History_Exchange_Private_Self_To_Other_Again"] = 9,
    ["Card_History_Exchange_Private_Other_To_Me_Again"] = 9,
    ["Card_History_Exchange_Private_Other_To_Me_Over"] = 9,
    ["Card_History_Exchange_Private_Other_To_Me_ByOne_Over"] = 9,
    ["Card_History_Exchange_Private_Me_To_Others_Over"] = 9,
    ["Card_History_Exchange_Private_Me_To_Other_Over"] = 10,
    ["Card_History_Exchange_Private_Me_To_Other_TimeOut"] = 10,
    ["Card_History_Exchange_Private_Me_To_Others_TimeOut"] = 10,
    ["Card_History_Exchange_Private_Other_To_Me_TimeOut"] = 10,
    ["Card_History_Exchange_Chat_Me_TimeOut"] = 10,
    ["Card_History_Exchange_Chat_Me_Over"] = 10,
    ["Card_History_Exchange_Chat_Me_Again"] = 10,
    ["Card_History_Exchange_Chat_Other_Over"] = 10,
    ["Card_History_Exchange_Chat_Again_Button"] = 10,
    ["Card_History_Exchange_Private_Again_Button"] = 10,
    ["Card_History_Exchange_For_Other_Button"] = 10,
    ["Card_History_Exchange_TimeOut"] = 10,
    ["Card_History_Exchange_Over_Button"] = 10,
    ["Card_History_Exchange_Over_Next_Button"] = 10,
    ["Card_Exchange_Title"] = 10,
    ["Card_Exchange_Button"] = 10,
    ["Card_Exchange_Des"] = 10,
    ["Card_Exchange_No_Cards"] = 10,
    ["Card_Exchange_Right_Des"] = 10,
    ["Card_Confirm_Exchange_Title"] = 10,
    ["Card_Confirm_Exchange_Content"] = 10,
    ["Card_Confirm_Exchange_Cancle"] = 10,
    ["Card_Confirm_Exchange_Sure"] = 10,
    ["Card_Preview_Exchange_Button_Name"] = 10,
    ["Card_Preview_Exchange_Button_Count"] = 10,
    ["Card_Channel_Title_Exchange"] = 10,
    ["Card_Channel_QQ"] = 10,
    ["Card_Channel_WX"] = 10,
    ["Card_Channel_Close"] = 10,
    ["Card_Exchange_Select_Friend_Title"] = 10,
    ["Card_Exchange_Select_Friend_Des"] = 10,
    ["Card_Exchange_Select_Friend_Already"] = 10,
    ["Card_Exchange_Select_Friend_Time"] = 10,
    ["Card_Confirm_Answer_Exchange_Title"] = 10,
    ["Card_Confirm_Answe_Exchange_Content"] = 10,
    ["Card_Confirm_Answe_Exchange_Cancle"] = 10,
    ["Card_Confirm_Answe_Exchange_Sure"] = 10,
    ["Card_Confirm_Answe_Tips"] = 10,
    ["Card_Confirm_Tips"] = 10,
    ["Card_WildCard_Title"] = 10,
    ["Card_WildCard_TimeOut"] = 10,
    ["Card_WildCard_Left_CardDeck"] = 10,
    ["Card_WildCard_Check"] = 10,
    ["Card_WildCard_Des"] = 10,
    ["Card_WildCard_Button_Dis_Name"] = 10,
    ["Card_WildCard_Button_Name"] = 10,
    ["Card_WildCard_Max_Tips"] = 10,
    ["Card_WildCard_Tips_Dis"] = 10,
    ["Card_WildCard_Main_TimeOut"] = 10,
    ["Card_History_Request_QQWX_Angain_Button"] = 10,
    ["Card_History_Request_QQWX_Angain"] = 10,
    ["Card_History_Request_QQWX_Over"] = 10,
    ["Card_History_Request_QQWX_TimeOut"] = 10,
    ["Card_History_Request_QQWX_Answer_Over"] = 10,
    ["Card_History_Give_QQWX_Angain_Button"] = 10,
    ["Card_History_Give_QQWX_Angain"] = 10,
    ["Card_History_Give_QQWX_Over"] = 10,
    ["Card_History_Give_QQWX_TimeOut"] = 10,
    ["Card_History_Give_QQWX_Answer_Over"] = 10,
    ["Card_History_Exchange_QQWX_Me_TimeOut"] = 10,
    ["Card_History_Exchange_QQWX_Me_Over"] = 10,
    ["Card_History_Exchange_QQWX_Me_Again"] = 10,
    ["Card_History_Exchange_QQWX_Other_Over"] = 10,
    ["Card_History_Exchange_QQWX_Again_Button"] = 10,
    ["Card_History_Exchange_QQWX_Other_Me_TimeOut"] = 10,
    ["Card_History_Exchange_QQWX_Other_Me_Again"] = 10,
    ["Card_History_Request_QQWX_Other_Me_TimeOut"] = 10,
    ["Card_History_Request_QQWX_Other_Me_Again"] = 10,
    ["Card_History_WXHead_Name"] = 10,
    ["Card_History_QQHead_Name"] = 10,
    ["Card_Confirm_Request_Title"] = 10,
    ["Card_Confirm_Give_Title"] = 10,
    ["Card_Chat_Exchange_Waiting"] = 10,
    ["Card_Chat_Give_Waiting"] = 10,
    ["Card_Chat_Exchange_Button"] = 10,
    ["Card_Preview_Share_Button_Name"] = 10,
    ["Card_Preview_GiveAndExchange_Des"] = 10,
    ["Card_Preview_GiveAndExchange_Des_Count"] = 10,
    ["Card_Chat_Exchange_Title"] = 10,
    ["Card_Tips_Exchange_Friend_Limit"] = 10,
    ["Card_History_Daily_Close_Check"] = 10,
    ["Card_Right_Request"] = 10,
    ["Card_Right_Exchange"] = 10,
    ["Card_Right_Check_Content"] = 10,
    ["Card_Right_Cancle_Btn_Name"] = 10,
    ["Card_Right_Request_Sure_Btn_Name"] = 10,
    ["Card_Right_Exchange_Sure_Btn_Name"] = 10,
    ["Card_Right_Request_Sure_Dis_Btn_Name"] = 10,
    ["Card_Right_Exchange_Sure_Dis_Btn_Name"] = 10,
    ["Card_Right_Request_Title"] = 10,
    ["Card_Right_Exchange_Title"] = 10,
    ["Card_All_Powerful_Pop_Title"] = 10,
    ["Card_All_Powerful_Pop_Hint"] = 10,
    ["Card_All_Powerful_Pop_Show_Have_Btn_Msg"] = 10,
    ["Card_All_Powerful_Pop_Convert_Btn_Msg"] = 10,
    ["Card_All_Powerful_Pop_Convert_Btn_Msg_Dis"] = 10,
    ["Card_All_Powerful_Pop_Click_Card"] = 10,
    ["Card_All_Powerful_Pop_Click_Convert_Hint"] = 10,
    ["Card_All_Powerful_Pop_Click_Convert_Hint2"] = 10,
    ["Card_Request_Button"] = 10,
    ["Card_Give_Title"] = 11,
    ["Card_Chat_Request_Finish_Base"] = 11,
    ["Card_Chat_Give_Finish_Base"] = 11,
    ["Card_Chat_Exchange_Finish_Base"] = 11,
    ["Card_Main_GetCard_ButtonName"] = 11,
    ["Card_GetWayTips_Title"] = 11,
    ["Card_GetWayTips_des"] = 11,
    ["Card_GetWayTips_Get_ButtonName"] = 11,
    ["Card_Record_Plat_Des"] = 11,
    ["Card_Record_Plat_WX"] = 11,
    ["Card_Record_Plat_QQ"] = 11,
    ["Card_GetWayTips_Cup_Title"] = 11,
    ["Card_GetWayTips_Cup_Content"] = 11,
    ["Card_GetWayTips_Cup_SureButton_Name"] = 11,
    ["Card_Exchange_Limit"] = 11,
    ["Card_Activity_Bubble"] = 11,
    ["Card_Exchange_Bubble"] = 11,
    ["Card_Bounds_Bubble"] = 11,
    ["Card_WildCard_Bubble"] = 11,
    ["Card_Activity_Bubble_1"] = 11,
    ["Card_Exchange_Bubble_1"] = 11,
    ["Card_Exchange_HitText"] = 11,
    ["Card_Exchange_Be_HitText"] = 11,
    ["Card_Main_Shop_Name"] = 11,
    ["SelfUmg_Card_Tips_1"] = 11,
    ["SelfUmg_Card_Tips_2"] = 11,
    ["Card_Welfare_Tips_1"] = 11,
    ["Card_Vertical_Tip"] = 11,
    ["Card_PrivateSetting_Tip"] = 11,
    ["Card_Version_Tip_Main"] = 11,
    ["Card_Version_Tip_Chat_Control"] = 11,
    ["Card_Version_Tip_CardGet"] = 11,
    ["Card_download_Tip"] = 11,
    ["Card_History_Give_Normal_TimeOut"] = 11,
    ["Card_History_Give_Normal_Angain"] = 11,
    ["Card_History_Request_Normal_Angain"] = 11,
    ["Card_CrownView_CardBagName"] = 11,
    ["Card_CloudToAPP_Title"] = 11,
    ["Card_CloudToAPP_Sure"] = 11,
    ["Card_CloudToAPP_Cancle"] = 11,
    ["Card_CloudToAPP_Main"] = 11,
    ["Card_ActivityTime"] = 11,
    ["Card_Main_CardSetHistory_Button_Name"] = 11,
    ["Card_Main_CardSetHistory_CurrentSet"] = 11,
    ["Card_Main_CardSetHistory_HistorySet"] = 11,
    ["Card_Main_CardSetHistory_HistorySet_Des"] = 11,
    ["Card_Preview_History_NoCard"] = 11,
    ["Card_GetCard_GoButtonName"] = 11,
    ["Card_Exchange_SecondTips_1"] = 11,
    ["Card_Exchange_SecondTips_2"] = 11,
    ["Card_Exchange_AutoSelect"] = 11,
    ["Card_Exchange_Limit_FriendNoCard"] = 11,
    ["Card_Exchange_Limit_FriendHasCard"] = 11,
    ["Card_Exchange_Limit_Confirm_Content"] = 11,
    ["Card_Exchange_Limit_Chat_NotCloudExchange"] = 11,
    ["Card_Exchange_Limit_Chat_NotEnoughCount"] = 11,
    ["Card_Exchange_Limit_Chat_LimitCount"] = 11,
    ["Card_Exchange_Limit_Give"] = 11,
    ["Card_Exchange_Limit_Want"] = 11,
    ["Card_Exchange_Limit_ButtonChange"] = 11,
    ["Card_Exchange_Limit_ButtonChoose"] = 11,
    ["Card_Exchange_Limit_Chat_Content"] = 11,
    ["Card_Exchange_Limit_Chat_MyGive"] = 11,
    ["Card_Exchange_Limit_Chat_MyWant"] = 11,
    ["Card_Exchange_Limit_Chat_OtherGive"] = 11,
    ["Card_Exchange_Limit_Chat_OtherWant"] = 11,
    ["Card_Exchange_Limit_Confirm_Title"] = 11,
    ["Card_Exchange_Limit_Choose_Recommend"] = 11,
    ["Card_Exchange_Limit_Invite_Des"] = 11,
    ["Card_Exchange_Limit_Invite_MyGive"] = 11,
    ["Card_Exchange_Limit_Invite_MyWant"] = 11,
    ["Card_Exchange_Limit_Max_SelectCard"] = 11,
    ["Card_Exchange_Limit_NoSelectContent"] = 11,
    ["Card_Exchange_Limit_SelectDes"] = 11,
    ["Card_Exchange_Limit_SelectTitle"] = 11,
    ["Card_ShareInPic_CardSet"] = 11,
    ["Card_ShareInPic_CardDeck"] = 11,
    ["Card_Give_Button_NoCardSelect"] = 11,
    ["LFT_CardTrade"] = 11,
    ["Card_Exchange_StarTips_1"] = 11,
    ["Card_Exchange_StarTips_2"] = 11,
    ["UI_Card_IP_Main_Content_Title"] = 11,
    ["UI_Card_IP_Main_Content"] = 11,
    ["UI_Card_IP_Tips_Content"] = 11,
    ["Card_CardOpen_Bubble"] = 11,
    ["Card_Unlock_Reward_Tips"] = 11,
    ["Card_Give_Limit_Cebianlan_Toast_Content"] = 11,
    ["Card_Exchange_Limit_Cebianlan_Toast_Content"] = 11,
    ["Card_Exchange_Appoint_Card_Toast"] = 11,
    ["Card_Exchange_Appoint_Number_Toast"] = 11,
    ["Card_Exchange_Appoint_Card"] = 11,
    ["Card_Exchange_Free_Card"] = 11,
    ["Card_Exchange_Appoint_Card_Time"] = 11,
    ["Card_Exchange_No_Times_Toast"] = 11,
    ["Card_Exchange_Specific_Title"] = 11,
    ["Card_Exchange_Free_Title"] = 11,
    ["Card_Exchange_Nochoose_Title"] = 11,
    ["Card_Send_Notimes_Title"] = 11,
    ["Club_MainTab"] = 12,
    ["Club_Members"] = 12,
    ["Club_GroupDailyCreateNumLimit"] = 12,
    ["Club_GroupAlreadyBound"] = 12,
    ["Club_GroupMaxCreateNumLimit"] = 12,
    ["Club_GroupFrequentLimit"] = 12,
    ["Club_GroupSecurityLimit"] = 12,
    ["Club_GroupJoinNumLimit"] = 12,
    ["Club_GroupCreateSuccess"] = 12,
    ["Club_GroupCreateFail"] = 12,
    ["Club_GroupJoinFailed"] = 12,
    ["Club_GroupJoinSuccess"] = 12,
    ["Club_GroupCreated"] = 12,
    ["Club_GroupJoined"] = 12,
    ["Club_GroupNotCreate"] = 12,
    ["Club_CreateCostCoinOwnNum"] = 12,
    ["Club_NameNull"] = 12,
    ["Club_Create"] = 12,
    ["Club_Setting"] = 12,
    ["Club_MemberStateXiaowo"] = 12,
    ["Club_MemberKickTips"] = 12,
    ["Club_MemberDissolve"] = 12,
    ["Club_UIDCopied"] = 12,
    ["Club_MemberLatelySaveXiaowo"] = 12,
    ["Club_MemberHourlyLayoutSaveXiaowo"] = 12,
    ["Club_MemberMinlyLayoutSaveXiaowo"] = 12,
    ["Club_MemberLeaveClub"] = 12,
    ["Club_HomeDataResponseFail"] = 12,
    ["Club_JoinApplied"] = 12,
    ["Club_JoinApplyFail"] = 12,
    ["Club_InviteMessageSended"] = 12,
    ["Club_ModifySettingSuccess"] = 12,
    ["Club_ModifySettingFail"] = 12,
    ["Club_NotOpen"] = 12,
    ["Club_SearchNoResult"] = 12,
    ["Club_NameAllSpace"] = 12,
    ["Club_MinTagLimit"] = 12,
    ["Club_InviteToWorldLeftCd"] = 12,
    ["Club_InviteToLobbyLeftCd"] = 12,
    ["Club_TitleAllMap"] = 12,
    ["Club_TitleAddMap"] = 12,
    ["Club_AddMapNum"] = 12,
    ["Club_MapNumLimit"] = 12,
    ["Club_MapSelectLimit"] = 12,
    ["Club_TagNumLimit"] = 12,
    ["Club_MapPinNumLimit"] = 12,
    ["Club_CreateCoinLimit"] = 12,
    ["Club_HeatTip"] = 12,
    ["Club_CreateLevelLimit"] = 12,
    ["Club_HomeTip"] = 12,
    ["Club_SearchPlayerNoResult"] = 12,
    ["Club_JoinToShareQRCode"] = 12,
    ["Club_NetErrorGetInfoFail"] = 12,
    ["Club_JoinToInvite"] = 12,
    ["Club_MapSaved"] = 12,
    ["ClubShareTitle"] = 12,
    ["ClubShareDescription"] = 12,
    ["Club_PromoteVicePresident"] = 12,
    ["Club_DemoteVicePresident"] = 12,
    ["Club_VicePresidentPromoteConfirm"] = 12,
    ["Club_VicePresidentDemoteConfirm"] = 12,
    ["Club_HandOverPresidentConfirm"] = 12,
    ["Club_President"] = 12,
    ["Club_VicePresident"] = 12,
    ["Club_HandOverPresidentBeforeQuit"] = 12,
    ["Club_AskGroupMemberToJoin"] = 12,
    ["Club_UnbindOldToCreateNewGroup"] = 12,
    ["Club_OwnerApproveApply_Success"] = 12,
    ["Club_Societies_UIHomepage"] = 12,
    ["Club_RankRule_SubTitle"] = 12,
    ["Club_RankRule_Content"] = 12,
    ["Club_RankSettlement_1"] = 12,
    ["Club_RankSettlement_2"] = 12,
    ["Club_RankSettlement_3"] = 12,
    ["Club_RankSettlement_Rule"] = 12,
    ["Club_LBS_Rule"] = 12,
    ["Club_JoinRank_Notice"] = 12,
    ["Club_JoinRank_LBS_Notice"] = 12,
    ["Club_JoinRank_LBS_System"] = 12,
    ["Club_Log_Log"] = 12,
    ["Club_Log_Course"] = 12,
    ["Club_Log_NoLog"] = 12,
    ["Club_Log_NoCourse"] = 12,
    ["Club_Log_CourseDesc"] = 12,
    ["Text_Societies_Rank_Des"] = 12,
    ["Text_Societies_Tags_Recommend"] = 12,
    ["Text_Societies_Tags_Rank"] = 12,
    ["BattleReMatch_WaitForPartner"] = 12,
    ["BattleReMatch_ProposalAgain"] = 12,
    ["BattleReMatch_ProposalAgainToLeader"] = 12,
    ["BattleReMatch_TimeCountDownSuffix"] = 12,
    ["BattleReMatch_CancelAgree"] = 12,
    ["BattleReMatch_BattleTogether"] = 12,
    ["BattleReMatch_WaitForTogether"] = 12,
    ["BattleReMatch_BattleAgain"] = 12,
    ["CaptureShadowItemUnlockTip"] = 12,
    ["Text_SFPlaze_Fuqi_Initiative_Tips"] = 12,
    ["Text_SFPlaze_Bainian_Initiative_Tips"] = 12,
    ["Text_SFPlaze_Huadeng_Initiative_Tips"] = 12,
    ["Text_SFPlaze_Fuqi_Passive_Tips"] = 12,
    ["Text_SFPlaze_Bainian_Passive_Tips"] = 13,
    ["Text_SFPlaze_Huadeng_Passive_Tips"] = 13,
    ["Text_SFPlaze_Need2beClose_Tips"] = 13,
    ["Text_SFPlaze_Need2Update_Tips"] = 13,
    ["Text_SFPlaze_InitiativeCD_Fuqi_Tips"] = 13,
    ["Text_SFPlaze_InitiativeCD_Bainian_Tips"] = 13,
    ["Text_SFPlaze_InitiativeCD_Huadeng_Tips"] = 13,
    ["Text_SFPlaze_LevelMax_Fuqi_Tips"] = 13,
    ["Text_SFPlaze_LevelMax_Bainian_Tips"] = 13,
    ["Text_SFPlaze_LevelMax_Huadeng_Tips"] = 13,
    ["WallaceVersionUpdateTips"] = 13,
    ["Text_Fuzi_NotEnough_Tips"] = 13,
    ["Text_General_ActionNegetive_Tips"] = 13,
    ["Text_SFPlaze_Name_Tiefuzi"] = 13,
    ["Text_SFPlaze_Name_Bainian"] = 13,
    ["Text_SFPlaze_Name_Dianhuadeng"] = 13,
    ["Text_SFPlaze_HeightLimiet_Tips"] = 13,
    ["Text_DoubleHigher_Intro_Tips"] = 13,
    ["Text_Wallace_Change_Tips_1"] = 13,
    ["Text_Wallace_Change_Tips_2"] = 13,
    ["Text_SFPlaze_LevelMax_Tiefuzi_Tips"] = 13,
    ["Text_SFPlaze_LevelMax_Bainian_Tips_1"] = 13,
    ["Text_SFPlaze_LevelMax_Dianhuadeng_Tips"] = 13,
    ["Text_SFPlaze_LevelMax_Button_Tips"] = 13,
    ["Text_SFPlaze_Name_Tiefuzi_Tips"] = 13,
    ["Text_SFPlaze_Name_Tiefuzi_MoreTips"] = 13,
    ["Text_SFPlaze_Name_Tiefuzi_Get_Tips"] = 13,
    ["Text_SFPlaze_Wulong_OverLimit_Tips"] = 13,
    ["Text_SFPlaze_Wulong_NotAccess_Tips"] = 13,
    ["UI_NewChat_PlayerNumTitle"] = 13,
    ["UI_NewChat_PlayerNumSwitch"] = 13,
    ["UI_NewChat_PlayerNumEmpty"] = 13,
    ["UI_NewChat_PlayerNumHome"] = 13,
    ["UI_NewChat_PlayerNumFarm"] = 13,
    ["UI_NewChatLobbyBottom_PlayerNum_Title"] = 13,
    ["UI_NewChatLobbyBottom_PlayerNum_Content"] = 13,
    ["Text_SFPlaze_HugNegative_Tips"] = 13,
    ["NewChat_SelfSharePosition"] = 13,
    ["Text_Club_WeixinGroup_CloudGame_Tips"] = 13,
    ["NewChat_TooManyDuplicateText"] = 13,
    ["Prop_MewMewBomb_Name"] = 13,
    ["Prop_MewMewBomb_Tips"] = 13,
    ["Prop_MewMewBomb_MoreTips"] = 13,
    ["UI_Club_NoneLocation"] = 13,
    ["NewChat_ClearAllNotRedDotMsg_Content"] = 13,
    ["Community_Conan_ChatBubble_CatchKid_1"] = 13,
    ["Community_Kid_Enter_Pretender"] = 13,
    ["Community_Conan_ChatBubble_CatchKid_2"] = 13,
    ["Community_Kid_Enter_Air"] = 13,
    ["Community_Conan_ChatBubble_CatchKid_3"] = 13,
    ["Prop_KidGlider_Normal_Name"] = 13,
    ["Prop_KidGlider_Normal_Tips"] = 13,
    ["Prop_KidGlider_Normal_MoreTips"] = 13,
    ["Prop_KidGlider_Advanced_Name"] = 13,
    ["Prop_KidGlider_Advanced_Tips"] = 13,
    ["Prop_KidGlider_Advanced_MoreTips"] = 13,
    ["Prop_KidRose_Normal_Name"] = 13,
    ["Prop_KidRose_Normal_Tips"] = 13,
    ["Prop_KidRose_Normal_MoreTips"] = 13,
    ["Prop_KidRose_Advanced_Name"] = 13,
    ["Prop_KidRose_Advanced_Tips"] = 13,
    ["Prop_KidRose_Advanced_MoreTips"] = 13,
    ["Community_Conan_EasterEgg_Key"] = 13,
    ["Community_Conan_EasterEgg_Content"] = 13,
    ["UI_NewChatCell_UpgradeContent"] = 13,
    ["Prop_Fulu_Advanced_Name"] = 13,
    ["Prop_Fulu_Advanced_Tips"] = 13,
    ["Prop_Fulu_Advanced_MoreTips"] = 13,
    ["Text_2025summer_Wuyou_ChaseJump_Tips"] = 13,
    ["Text_2025summer_Bagua_ChaseJump_Tips"] = 13,
    ["Text_2025summer_Wuyou_Eject_Tips"] = 13,
    ["Text_2025summer_Wuyou_Bullseye_Tips"] = 13,
    ["Text_2025summer_Wuyou_Drop_Tips"] = 13,
    ["Text_2025summer_Wuyou_Eject_Announce_1"] = 13,
    ["Text_2025summer_Wuyou_Eject_Announce_2"] = 13,
    ["Text_2025summer_Wuyou_Eject_Announce_3"] = 13,
    ["Text_2025summer_Wuyou_Failure_Announce_1"] = 13,
    ["Text_2025summer_Wuyou_Failure_Announce_2"] = 13,
    ["Text_2025summer_Wuyou_Failure_Announce_3"] = 13,
    ["Text_2025summer_Kid_Challenge_1"] = 13,
    ["Text_2025summer_Kid_Challenge_2"] = 13,
    ["Text_2025summer_Douluo_Awakening_Notyet_NoAim_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Notyet_AimNegative_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Done_Noaim_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Done_AimeNegative_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Notyet_Begin_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Done_Begin_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Notye_Finish_Tips"] = 13,
    ["Text_2025summer_Douluo_Awakening_Done_Finish_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Done_Finish_MaxLevel_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Done_Finish_AlreadyMaxLevel_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Notyet_Conquesting_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Notyet_Conquesting_Deny_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Notyet_Conquesting_Outtime_Tips"] = 14,
    ["Text_2025summer_Douluo_Awakening_Notyet_Conquesting_Agree_Tips"] = 14,
    ["Text_2025summer_Douluo_Surround_Begin_Tips"] = 14,
    ["Text_2025summer_Douluo_Surround_Finish_Tips"] = 14,
    ["Text_2025summer_Douluo_Surround_Awakening_Done_Tips"] = 14,
    ["Text_2025summer_Douluo_Surround_Awakening_Notyet_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Notyet_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Null_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Aimoff_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Success_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Success_MaxLevel_Tips"] = 14,
    ["Text_2025summer_Douluo_Catch_Success_AlreadyMaxLevel_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Begin_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Finish_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Getin_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Getout_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Exit_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Influence_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Notyet_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Awakedone_Help_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Awakedone_Catch_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Suggest_Help_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Suggest_Catch_Tips"] = 14,
    ["Text_2025summer_Douluo_Guide_Field_Tips"] = 14,
    ["Text_2025summer_Douluo_Attraction_Handhold_Xiaowu_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Handhold_Xiaowu_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Handhold_Tangsan_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Handhold_Tangsan_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Awakening_Tangsan_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Awakening_Tangsan_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Awakening_Xiaowu_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Awakening_Xiaowu_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Lanyincao_Tangsan_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Lanyincao_Tangsan_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Lanyincao_Xiaowu_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Lanyincao_Xiaowu_2"] = 14,
    ["Text_2025summer_Douluo_Attraction_Educationg_Tangsan_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Educationg_Xiaowu_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Field_Tangsan_1"] = 14,
    ["Text_2025summer_Douluo_Attraction_Field_Xiaowu_1"] = 14,
    ["Text_2025summer_Douluo_Lanyincao_Go_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Come_Tips"] = 14,
    ["Text_2025summer_Douluo_Field_Abnormal_Tips"] = 14,
    ["FB_Revive_CountDown"] = 15,
    ["RoguelikeTalent_Activate_Progress"] = 16,
    ["RoguelikeTalent_Need_UnlockPreTalent"] = 16,
    ["RoguelikeTalent_HadActivate"] = 16,
    ["RoguelikeTalent_UnlockSkill"] = 16,
    ["RoguelikeTalent_UpgradeSkill"] = 16,
    ["Set_EditKey_FPSRoguelikeGame"] = 16,
    ["RoguelikeTaskDifficulty_1"] = 16,
    ["RoguelikeTaskDifficulty_2"] = 16,
    ["RoguelikeTaskDifficulty_3"] = 16,
    ["RoguelikeTalent_Result_PreTalentLock"] = 16,
    ["RoguelikeTalent_Result_NoMoney"] = 16,
    ["RoguelikeTalent_Result_ConfigError"] = 16,
    ["RoguelikeTalent_Result_HadUnlock"] = 16,
    ["RoguelikeTalent_Result_Fail"] = 16,
    ["RoguelikeTask_RefreshTimes"] = 16,
    ["RoguelikeTask_ResetTime"] = 16,
    ["RoguelikeTask_NoCanRefreshTask"] = 16,
    ["RoguelikeTask_RefreshTimesLimit"] = 16,
    ["RoguelikeTask_TaskHadFinished"] = 16,
    ["RoguelikeTask_RefreshFail"] = 16,
    ["RoguelikeTask_RefreshSuccess"] = 16,
    ["BioChase_HeadIcon_Monster1002"] = 16,
    ["BioChase_HeadIcon_Monster1003"] = 16,
    ["BioChase_HeadIcon_Monster2001"] = 16,
    ["UI_BioChase_DeadTip"] = 16,
    ["UI_BioChase_DeadTip_1"] = 16,
    ["Level_BioChase_ShotGhost_Fight"] = 16,
    ["Level_BioChase_StartTips_Fight"] = 16,
    ["Level_BioChase_FinishiTips_Fight"] = 16,
    ["UI_FPSSetting_CommonItem_Tips"] = 16,
    ["UI_DfGame_Resurrection_BigTitle"] = 16,
    ["UI_DfGame_Resurrection_SmallTitle1"] = 16,
    ["UI_DfGame_Resurrection_SmallTitle2"] = 16,
    ["UI_DfGame_ResurrectionItem_LowQuality"] = 16,
    ["UI_DfGame_ResurrectionItem_MidQuality"] = 16,
    ["UI_DfGame_ResurrectionItem_HighQuality"] = 16,
    ["UI_DrGame_ResurrectionItem_AbandonRespawnTips"] = 16,
    ["UI_DrGame_ResurrectionItem_Free"] = 16,
    ["UI_DrGame_Time_BattleTime"] = 16,
    ["UI_DrGame_Time_LeaveTime"] = 16,
    ["UI_DrGame_PreEvent_BoxRefresh"] = 16,
    ["UI_DrGame_PreEvent_TreasureCrabRefresh"] = 16,
    ["UI_DrGame_PreEvent_EscapePointRefresh"] = 16,
    ["UI_DrGame_PreEvent_FinalEscapeTime"] = 16,
    ["UI_DrGame_RankItem_LeaveTip"] = 16,
    ["UI_DrGame_Get_Coin_Limit_Tips"] = 16,
    ["UI_DrGame_Get_Award_Skip_Animation_Tips"] = 16,
    ["UI_FPSGame_ChampionshipTeam"] = 16,
    ["UI_FPSGame_RunnerUpTeam"] = 16,
    ["UI_GunGameKC_RedBag"] = 16,
    ["UI_DFGame_LeaveSucc"] = 16,
    ["UI_DFGame_LeaveFail"] = 16,
    ["UI_DFGame_StartGame_BuyTips"] = 16,
    ["UI_DFGame_StartGame_TaskTips"] = 16,
    ["UI_GunGameKC_RankListTitle_Pick"] = 16,
    ["UI_FpsGame_VoiceRoom_Team"] = 16,
    ["UI_DFGame_DropAwardRule"] = 16,
    ["UI_DFGame_DropAwardRule_NoOverflow"] = 16,
    ["UI_KCGame_DropAwardRule"] = 16,
    ["UI_FPSGame_DropAwardDesc"] = 16,
    ["UI_FPSGame_DropAwardDescTitle"] = 16,
    ["UI_FPSGame_DropAwardDescContent"] = 16,
    ["UI_DFGame_DropAwardTitle"] = 16,
    ["UI_KCGame_DropAwardTitle"] = 16,
    ["UI_Setting_DFGame_CustomName"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_1"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_2"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_3"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_4"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_1"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_2"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_3"] = 16,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_4"] = 16,
    ["UI_FPS_Roguelike_RemainTime"] = 16,
    ["UI_FPS_Roguelike_SeasonLevel"] = 16,
    ["UI_FPS_Roguelike_RemainSeasonDays"] = 16,
    ["UI_FPS_Roguelike_EndlessPassNumber"] = 16,
    ["UI_FPS_Roguelike_FastStartTips"] = 16,
    ["UI_FPS_Roguelike_FastStartBtn"] = 16,
    ["UI_FPS_Roguelike_FastStartOver"] = 16,
    ["UI_FPS_Roguelike_FastFinishTips"] = 16,
    ["UI_FPS_Roguelike_EndlessContinue"] = 16,
    ["UI_FPS_Roguelike_EndlessIsSinglePlay"] = 16,
    ["UI_FPS_Roguelike_SeasonMaxRewardCondition"] = 16,
    ["UI_FPS_Roguelike_SeasonTaskTabName_2"] = 16,
    ["UI_FPS_Roguelike_SeasonTaskTabName_3"] = 16,
    ["UI_FPS_Roguelike_SeasonTaskTabName_4"] = 16,
    ["UI_FPS_Roguelike_SeasonTask_ResetTime"] = 16,
    ["UI_FPS_Roguelike_SeasonTask_EndTime"] = 16,
    ["UI_FPS_Roguelike_SeasonMilestoneName"] = 16,
    ["UI_FPS_Roguelike_SeasonTaskName"] = 16,
    ["UI_Roguelike_Rank"] = 16,
    ["UI_Roguelike_RankReward"] = 16,
    ["UI_BrGame_WaterPark"] = 16,
    ["UI_BrGame_SacredTree"] = 16,
    ["UI_BrGame_Relic"] = 16,
    ["UI_BrGame_Temple"] = 16,
    ["UI_BrGame_ChinaTown"] = 16,
    ["UI_BrGame_Village"] = 16,
    ["UI_BrGame_Idyllic"] = 16,
    ["UI_BrGame_CentralBase"] = 17,
    ["UI_BrGame_HeritagePark"] = 17,
    ["UI_BrGame_Corsair"] = 17,
    ["UI_BrGame_Laboratory"] = 17,
    ["UI_BrGame_BotanicalGarden"] = 17,
    ["UI_BrGame_WoodenHouse"] = 17,
    ["UI_FPSSetting_ChangeCameraTips"] = 17,
    ["UI_FPSSetting_DF_Title"] = 17,
    ["UI_Roguelike_Tutorial_PickReward"] = 17,
    ["UI_Roguelike_Tutorial_DifficultySelect"] = 17,
    ["UI_Roguelike_Tutorial_Talent"] = 17,
    ["UI_Roguelike_Tutorial_SkillSwitch"] = 17,
    ["UI_Roguelike_Tutorial_DifficultyEndless"] = 17,
    ["UI_Roguelike_Tutorial_MissionTarget"] = 17,
    ["UI_Roguelike_Tutorial_HealthBar"] = 17,
    ["UI_Roguelike_Tutorial_RewardConfirm"] = 17,
    ["UI_Roguelike_Tutorial_Bag"] = 17,
    ["UI_Roguelike_Tutorial_Collection"] = 17,
    ["UI_Roguelike_Tutorial_CollectionLvUp"] = 17,
    ["UI_Roguelike_Tutorial_CollectionLvUp2"] = 17,
    ["UI_Roguelike_Tutorial_CollectionSpecial"] = 17,
    ["UI_Roguelike_Tutorial_Artifact"] = 17,
    ["UI_Roguelike_Tutorial_LevelUp"] = 17,
    ["UI_Roguelike_Tutorial_PickLevel"] = 17,
    ["UI_Roguelike_Tutorial_PickLevelConfirm"] = 17,
    ["UI_Roguelike_Tutorial_TalentPage"] = 17,
    ["UI_Roguelike_Tutorial_UnlockTalent"] = 17,
    ["UI_Roguelike_Settlement_GetPuzzles"] = 17,
    ["UI_Roguelike_Tutorial_VoteToStart"] = 17,
    ["UI_Roguelike_Tutorial_RefreshItems"] = 17,
    ["UI_Roguelike_Tutorial_ShopBuyTip"] = 17,
    ["UI_Roguelike_Tutorial_Armory"] = 17,
    ["UI_Roguelike_Tutorial_RefreshShop"] = 17,
    ["UI_Roguelike_Tutorial_AfterShop"] = 17,
    ["UI_FPS_Weapon_Unlock_Official_1"] = 17,
    ["UI_FPS_Weapon_Unlock_Official_2"] = 17,
    ["UI_FPS_Weapon_Unlock_Official_3"] = 17,
    ["UI_Roguelike_PassLevelCount_EndLessLD"] = 17,
    ["UI_Roguelike_PassLevelCount"] = 17,
    ["UI_Roguelike_MarkUpgradMaterialName"] = 17,
    ["UI_Roguelike_TalentPointName"] = 17,
    ["UI_Roguelike_Tutorial_Lobby_Mark"] = 17,
    ["UI_Roguelike_Tutorial_Mark_NewSet"] = 17,
    ["UI_Roguelike_Tutorial_Mark_Desc"] = 17,
    ["UI_Roguelike_Tutorial_Mark_MoveToBoard"] = 17,
    ["UI_Roguelike_Tutorial_Mark_Combo"] = 17,
    ["UI_Roguelike_Tutorial_Mark_Upgrade"] = 17,
    ["UI_Roguelike_Tutorial_Mark_Delete"] = 17,
    ["UI_Roguelike_RulesTab_1"] = 17,
    ["UI_Roguelike_RulesTab_2"] = 17,
    ["UI_Roguelike_RulesTab_3"] = 17,
    ["UI_Roguelike_RulesTab_4"] = 17,
    ["UI_Roguelike_RulesTab_5"] = 17,
    ["UI_Roguelike_RulesTab_6"] = 17,
    ["UI_Roguelike_RulesTab_7"] = 17,
    ["UI_Roguelike_RulesTab_8"] = 17,
    ["UI_Roguelike_RulesTab_9"] = 17,
    ["UI_Roguelike_RulesTab_10"] = 17,
    ["UI_Roguelike_RulesTab_11"] = 17,
    ["UI_Roguelike_RulesTab_12"] = 17,
    ["UI_Roguelike_NextEnterFreshTask"] = 17,
    ["UI_Roguelike_Prepared"] = 17,
    ["UI_Roguelike_Unprepared"] = 17,
    ["UI_Roguelike_Talent_ActivateManyTitle"] = 17,
    ["UI_Roguelike_Talent_ActivateManyContent"] = 17,
    ["UI_Roguelike_Talent_ActivateAllTitle"] = 17,
    ["UI_Roguelike_Talent_ActivateAllContent"] = 17,
    ["UI_Roguelike_Talent_ActivateCost"] = 17,
    ["UI_Roguelike_Talent_ActivateTalentNum"] = 17,
    ["UI_Roguelike_Talent_UnlockSkillNum"] = 17,
    ["UI_Roguelike_Talent_UpgradeSkillNum"] = 17,
    ["RoguelikeTalent_Result_HadUnlock_ALL"] = 17,
    ["Set_EditKey_FPSBrGame_Vehicle"] = 17,
    ["HOK_FirstSoldierComeOn5"] = 18,
    ["HOK_FirstSoldierComeOn"] = 18,
    ["HOK_ArtilleryComeOn"] = 18,
    ["HOK_VanguardComeOn"] = 18,
    ["HOK_SuperSelfComeOn"] = 18,
    ["HOK_SuperEnemyComeOn"] = 18,
    ["HOK_MasterComeOn"] = 18,
    ["HOK_TyrantEnemyComeOn"] = 18,
    ["HOK_TyrantAndMasterEnemyComeOn"] = 18,
    ["HOK_SelfTowerOver"] = 18,
    ["HOK_EnemyTowerOver"] = 18,
    ["HOK_TowerProctedOver"] = 18,
    ["HOK_TotalKill_6"] = 18,
    ["HOK_TotalKill_7"] = 18,
    ["HOK_KillTarget_5"] = 18,
    ["Hok_Intro_1"] = 18,
    ["Hok_Intro_1_Text"] = 18,
    ["Hok_Intro_2"] = 18,
    ["Hok_Intro_2_1"] = 18,
    ["Hok_Intro_2_1_Text"] = 18,
    ["Hok_Intro_2_2"] = 18,
    ["Hok_Intro_2_2_Text"] = 18,
    ["Hok_Intro_2_3"] = 18,
    ["Hok_Intro_2_3_Text"] = 18,
    ["Hok_Intro_3"] = 18,
    ["Hok_Intro_3_Text"] = 18,
    ["Hok_Intro_4"] = 18,
    ["Hok_Intro_4_Text"] = 18,
    ["Hok_Intro_5"] = 18,
    ["Hok_Intro_5_Text"] = 18,
    ["HOK_Guide_DestroyEnemyCrystal"] = 18,
    ["HOK_Shop_RefreshHint"] = 18,
    ["HOK_Setting_Open"] = 18,
    ["HOK_Setting_Close"] = 18,
    ["HOK_Setting_Tip_HeadIcon"] = 18,
    ["HOK_Setting_Tip_BuyCard"] = 18,
    ["HOK_Setting_Tip_AddSkill"] = 18,
    ["HOK_Setting_Tip_Surrender"] = 18,
    ["HOK_Spectator_RoleSelect_Tips"] = 18,
    ["UI_HOK_Surrender_StartSurrenderBtnName"] = 18,
    ["UI_HOK_Surrender_EncourageText1"] = 18,
    ["UI_HOK_Surrender_EncourageText2"] = 18,
    ["UI_HOK_Surrender_EncourageText3"] = 18,
    ["UI_HOK_Surrender_EncourageText4"] = 18,
    ["Hok_Rank_Not_Unlock"] = 18,
    ["UI_HOK_SelectRole_LackHeroPosition_1"] = 18,
    ["UI_HOK_SelectRole_LackHeroPosition_2"] = 18,
    ["UI_HOK_SelectRole_LackHeroPosition_3"] = 18,
    ["UI_HOK_SelectRole_LackHeroPosition_4"] = 18,
    ["UI_HOK_SelectRole_LackHeroPosition_5"] = 18,
    ["UI_HOK_SelectRole_ShuntSelectText_1"] = 18,
    ["UI_HOK_SelectRole_ShuntSelectText_2"] = 18,
    ["UI_HOK_SelectRole_ShuntSelectText_3"] = 18,
    ["UI_HOK_SelectRole_ShuntSelectText_4"] = 18,
    ["UI_HOK_SelectRole_ShuntSelectText_5"] = 18,
    ["UI_HOK_SelectRole_HeroPositionName_1"] = 18,
    ["UI_HOK_SelectRole_HeroPositionName_2"] = 18,
    ["UI_HOK_SelectRole_HeroPositionName_3"] = 18,
    ["UI_HOK_SelectRole_HeroPositionName_4"] = 18,
    ["UI_HOK_SelectRole_HeroPositionName_5"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_1"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_2"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_3"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_4"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_5"] = 18,
    ["UI_HOK_SelectRole_HeroClassName_6"] = 18,
    ["HOK_TyrantAndMasterEnhance"] = 18,
    ["UI_InLevel_Hok_Reputation_MyScore_LineOne"] = 18,
    ["UI_InLevel_Hok_Reputation_MyScore_LineTwo"] = 18,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineOne"] = 18,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineTwo"] = 18,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineThree"] = 18,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineOne"] = 18,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineTwo"] = 18,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineThree"] = 18,
    ["UI_InLevel_Hok_Main_NoCompleteWithReputation_Box"] = 18,
    ["HOK_TransDoor_Description_Text"] = 18,
    ["HOK_TowerProctedOver_ArtilleryComeOn"] = 18,
    ["UI_HOK_Setting_SurrenderDisabled"] = 18,
    ["HOK_ShadowMasterComeOn"] = 18,
    ["HOK_ShadowTyrantEnemyComeOn"] = 18,
    ["HOK_ShowDefenceTowerRemind_Tips"] = 18,
    ["Set_EditKey_HOK"] = 18,
    ["HOK_Strategy1"] = 18,
    ["HOK_Strategy2"] = 18,
    ["HOK_Strategy3"] = 18,
    ["HOK_Strategy4"] = 18,
    ["HOK_Strategy5"] = 18,
    ["HOK_HomePortalstart"] = 18,
    ["InLevel_HOK_GameResult_Loser"] = 18,
    ["HOK_Player_Dressing"] = 18,
    ["HOK_Player_Undress"] = 18,
    ["HOK_BattleReport_SkillNormal"] = 18,
    ["HOK_BattleReport_SkillOne"] = 18,
    ["HOK_BattleReport_SkillTwo"] = 18,
    ["HOK_BattleReport_SkillThree"] = 18,
    ["HOK_BattleReport_SkillFour"] = 18,
    ["HOK_BattleReport_SkillSummoner"] = 18,
    ["HOK_SpectatorVanguardBlueComeOn"] = 18,
    ["HOK_SpectatorVanguardRedComeOn"] = 18,
    ["HOK_SpectatorSuperBlueComeOn"] = 19,
    ["HOK_SpectatorSuperRedComeOn"] = 19,
    ["HOK_Setting_Tip_PIP"] = 19,
    ["HOK_AssistHint1_Exp"] = 19,
    ["HOK_AssistHint2_Gold"] = 19,
    ["HOK_AssistHint3_Other"] = 19,
    ["HOK_BattleReport_Passive"] = 19,
    ["HOK_PIPHint_TowerDefense"] = 19,
    ["HOK_PIPHint_LongRefresh"] = 19,
    ["HOK_PIPHint_LongUpgrade"] = 19,
    ["HOK_BattleReport_Extra"] = 19,
    ["UI_HOK_ShopDrawRound"] = 19,
    ["HOK_GoldRebirth_Hint"] = 19,
    ["UI_HOK_ShopCardMax"] = 19,
    ["HOK_Mount_Born_Self"] = 19,
    ["HOK_Mount_Born_Enemy"] = 19,
    ["HOK_AssistHint_Chat"] = 19,
    ["HOK_PropCfgNextStage"] = 19,
    ["HOK_Setting_Tip_CardAttrPanel"] = 19,
    ["HOK_HomeoutLaunchDevicestart"] = 19,
    ["HOK_Mount_BeUsing_First_Friend"] = 19,
    ["HOK_Mount_BeUsing_First_Enemy"] = 19,
    ["HOK_Mount_BeUsing_Die_Friend"] = 19,
    ["HOK_Mount_BeUsing_Die_Enemy"] = 19,
    ["HOK_AreaMidleLaunchDevicedis"] = 19,
    ["HOK_Tip_NewbieGuide_AfterTutorialFreeHero"] = 19,
    ["HOK_BattleReport_Card"] = 19,
    ["HOK_HongSun_Enhance"] = 19,
    ["HOK_HongSun_Mutation"] = 19,
    ["HOK_BattleReport_Rebound"] = 19,
    ["HOK_HongSun_Enhance_ComeOn"] = 19,
    ["HOK_KongJianZhiLing_Enhance_ComeOn"] = 19,
    ["HOK_HongSun_Mutation_ComeOn"] = 19,
    ["HOK_KongJianZhiLing_Mutation_ComeOn"] = 19,
    ["HOK_BattleReport_HongSun"] = 19,
    ["HOK_GemStageWaitingEnd"] = 19,
    ["HOK_GemStageEnd"] = 19,
    ["HOK_Gameplay_Intro_Tips"] = 19,
    ["LevelScene_60050_1"] = 20,
    ["LevelScene_60050_2"] = 20,
    ["LevelScene_60050_3"] = 20,
    ["LevelScene_60050_4"] = 20,
    ["LevelScene_60050_5"] = 20,
    ["LevelScene_60053_1"] = 20,
    ["LevelScene_60053_2"] = 20,
    ["LevelScene_60053_3"] = 20,
    ["LevelScene_60053_4"] = 20,
    ["LevelScene_60133_1"] = 20,
    ["LevelScene_60133_2"] = 20,
    ["LevelScene_60133_3"] = 20,
    ["LevelScene_70000_1"] = 20,
    ["LevelScene_70002_1"] = 20,
    ["LevelScene_70002_2"] = 20,
    ["LevelScene_70002_3"] = 20,
    ["LevelScene_70003_1"] = 20,
    ["LevelScene_70016_1"] = 20,
    ["LevelScene_70017_1"] = 20,
    ["LevelScene_70018_1"] = 20,
    ["LevelScene_70018_2"] = 20,
    ["LevelScene_70018_3"] = 20,
    ["LevelScene_70022New_1"] = 20,
    ["LevelScene_70022New_2"] = 20,
    ["LevelScene_70034_1"] = 20,
    ["LevelScene_70038_1"] = 20,
    ["LevelScene_70038_2"] = 20,
    ["LevelScene_70038_3"] = 20,
    ["LevelScene_70038_4"] = 20,
    ["LevelScene_70038_5"] = 20,
    ["LevelScene_70038_6"] = 20,
    ["LevelScene_70038_7"] = 20,
    ["LevelScene_70038_8"] = 20,
    ["LevelScene_70038_9"] = 20,
    ["LevelScene_70038_10"] = 20,
    ["LevelScene_70059_1"] = 20,
    ["LevelScene_70059_2"] = 20,
    ["LevelScene_70059_3"] = 20,
    ["LevelScene_70059_4"] = 20,
    ["LevelScene_70060_1"] = 20,
    ["LevelScene_70060_2"] = 20,
    ["LevelScene_70060_3"] = 20,
    ["LevelScene_70064_1"] = 20,
    ["LevelScene_70064_2"] = 20,
    ["LevelScene_80001_1"] = 20,
    ["LevelScene_80001_2"] = 20,
    ["LevelScene_80001_3"] = 20,
    ["LevelScene_80003_1"] = 20,
    ["LevelScene_80003_2"] = 20,
    ["LevelScene_80003_3"] = 20,
    ["LevelScene_80004_1"] = 20,
    ["LevelScene_80004_2"] = 20,
    ["LevelScene_80004_3"] = 20,
    ["LevelScene_80005_1"] = 20,
    ["LevelScene_80005_2"] = 20,
    ["LevelScene_80005_3"] = 20,
    ["LevelScene_80006_1"] = 20,
    ["LevelScene_80006_2"] = 20,
    ["LevelScene_80006_3"] = 20,
    ["LevelScene_80009_1"] = 20,
    ["LevelScene_80009_2"] = 20,
    ["LevelScene_80009_3"] = 20,
    ["LevelScene_85003_1"] = 20,
    ["LevelScene_85003_2"] = 20,
    ["LevelScene_85003_3"] = 20,
    ["LevelScene_85005_1"] = 20,
    ["LevelScene_85005_2"] = 20,
    ["LevelScene_85005_3"] = 20,
    ["LevelScene_70023_1"] = 20,
    ["LevelScene_70023_2"] = 20,
    ["LevelScene_70023_3"] = 20,
    ["LevelScene_70023_4"] = 20,
    ["LevelScene_85013_1"] = 20,
    ["LevelScene_85013_2"] = 20,
    ["LevelScene_70062_1"] = 20,
    ["LevelScene_70062_2"] = 20,
    ["LevelScene_70062_3"] = 20,
    ["LevelScene_70062_4"] = 20,
    ["LevelScene_70062_5"] = 20,
    ["LevelScene_70037_1"] = 20,
    ["LevelScene_70037_2"] = 20,
    ["LevelScene_70037_3"] = 20,
    ["LevelScene_70037_4"] = 20,
    ["LevelScene_60057_1"] = 20,
    ["LevelScene_70013_1"] = 20,
    ["LevelScene_70013_2"] = 20,
    ["LevelScene_70065_1"] = 20,
    ["LevelScene_70066_1"] = 20,
    ["LevelScene_70066_2"] = 20,
    ["LevelScene_70066_3"] = 20,
    ["LevelScene_70067_1"] = 20,
    ["LevelScene_70068_1"] = 20,
    ["LevelScene_70068_2"] = 20,
    ["LevelScene_70068_3"] = 20,
    ["LevelScene_70069_1"] = 20,
    ["LevelScene_70069_2"] = 20,
    ["LevelScene_70075_1"] = 20,
    ["LevelScene_70075_2"] = 20,
    ["LevelScene_70075_3"] = 20,
    ["LevelScene_60055_1"] = 20,
    ["LevelScene_70081_0"] = 21,
    ["LevelScene_70081_1"] = 21,
    ["LevelScene_70081_2"] = 21,
    ["LevelScene_70081_3"] = 21,
    ["LevelScene_70083_0"] = 21,
    ["LevelScene_80011_0"] = 21,
    ["LevelScene_70088_1"] = 21,
    ["LevelScene_70087_0"] = 21,
    ["LevelScene_70087_1"] = 21,
    ["LevelScene_70090_0"] = 21,
    ["LevelScene_70090_1"] = 21,
    ["LevelScene_70093_1"] = 21,
    ["LevelScene_70085_1"] = 21,
    ["LevelScene_80017_0"] = 21,
    ["LevelScene_80017_1"] = 21,
    ["LevelScene_80017_2"] = 21,
    ["LevelScene_80017_3"] = 21,
    ["LevelScene_80017_4"] = 21,
    ["LevelScene_70096_1"] = 21,
    ["LevelScene_70096_2"] = 21,
    ["LevelScene_70094_0"] = 21,
    ["LevelScene_70094_1"] = 21,
    ["LevelScene_70094_2"] = 21,
    ["LevelScene_70105_0"] = 21,
    ["LevelScene_70105_1"] = 21,
    ["LevelScene_70105_2"] = 21,
    ["LevelScene_70105_3"] = 21,
    ["LevelScene_70105_4"] = 21,
    ["LevelScene_70105_5"] = 21,
    ["LevelScene_70105_6"] = 21,
    ["LevelScene_70105_7"] = 21,
    ["LevelScene_70105_8"] = 21,
    ["LevelScene_70106_0"] = 21,
    ["LevelScene_70106_1"] = 21,
    ["LevelScene_70106_2"] = 21,
    ["LevelScene_70106_3"] = 21,
    ["LevelScene_70106_4"] = 21,
    ["LevelScene_70086_0"] = 21,
    ["LevelScene_70086_1"] = 21,
    ["LevelScene_70086_2"] = 21,
    ["LevelScene_70086_3"] = 21,
    ["LevelScene_70104_0"] = 21,
    ["LevelScene_70104_1"] = 21,
    ["LevelScene_80018_0"] = 21,
    ["LevelScene_70110_1"] = 21,
    ["LevelScene_70113_0"] = 21,
    ["LevelScene_70113_1"] = 21,
    ["LevelScene_70113_2"] = 21,
    ["LevelScene_70113_3"] = 21,
    ["LevelScene_70113_4"] = 21,
    ["LevelScene_70112_1"] = 21,
    ["LevelScene_70112_2"] = 21,
    ["LevelScene_70112_3"] = 21,
    ["LevelScene_70119_0"] = 21,
    ["LevelScene_70119_1"] = 21,
    ["LevelScene_70119_2"] = 21,
    ["LevelScene_70120_0"] = 21,
    ["LevelScene_80020_0"] = 21,
    ["LevelScene_80020_1"] = 21,
    ["LevelScene_80020_2"] = 21,
    ["LevelScene_80020_3"] = 21,
    ["LevelScene_60232_0"] = 21,
    ["LevelScene_70111_0"] = 21,
    ["LevelScene_70111_1"] = 21,
    ["LevelScene_70222_0"] = 21,
    ["LevelScene_70232_0"] = 21,
    ["LevelScene_70232_1"] = 21,
    ["LevelScene_70234_0"] = 21,
    ["LevelScene_70234_1"] = 21,
    ["LevelScene_70234_2"] = 21,
    ["LevelScene_70234_3"] = 21,
    ["LevelScene_70235_0"] = 21,
    ["LevelScene_76084_1"] = 21,
    ["LevelScene_76086_1"] = 21,
    ["LevelScene_66017_1"] = 21,
    ["LevelScene_66019_1"] = 21,
    ["LevelScene_66019_4"] = 21,
    ["LevelScene_66019_5"] = 21,
    ["LevelScene_66019_6"] = 21,
    ["LevelScene_66019_7"] = 21,
    ["LevelSecne_80022_1"] = 21,
    ["LevelSecne_80022_2"] = 21,
    ["LevelScene_66026_1"] = 21,
    ["LevelScene_70239_0"] = 21,
    ["LevelScene_70240_0"] = 21,
    ["LevelScene_70240_1"] = 21,
    ["LevelScene_70240_2"] = 21,
    ["LevelScene_70241_0"] = 21,
    ["LevelScene_70243_0"] = 21,
    ["LevelScene_80023_0"] = 21,
    ["LevelScene_70245_1"] = 21,
    ["LevelScene_70245_2"] = 22,
    ["LevelScene_70247_0"] = 21,
    ["LevelScene_70247_1"] = 21,
    ["LevelScene_70247_2"] = 21,
    ["LevelScene_70248_0"] = 22,
    ["LevelScene_70249_0"] = 21,
    ["LevelScene_70250_0"] = 22,
    ["LevelScene_70250_1"] = 22,
    ["LevelScene_70250_2"] = 22,
    ["LevelScene_70254_0"] = 22,
    ["LevelScene_70254_1"] = 22,
    ["LevelScene_70254_2"] = 22,
    ["LevelScene_70254_3"] = 22,
    ["LevelScene_70255_0"] = 21,
    ["LevelScene_70255_1"] = 21,
    ["LevelScene_70255_2"] = 21,
    ["LevelScene_70255_3"] = 22,
    ["LevelScene_70255_4"] = 22,
    ["LevelScene_70255_5"] = 22,
    ["LevelScene_70256_1"] = 22,
    ["LevelScene_70256_2"] = 22,
    ["LevelScene_70257_0"] = 22,
    ["LevelScene_70258_0"] = 22,
    ["LevelScene_70259_0"] = 22,
    ["LevelScene_70259_1"] = 22,
    ["LevelScene_60269_0"] = 22,
    ["LevelScene_60269_1"] = 22,
    ["LevelScene_70261_1"] = 22,
    ["LevelScene_70261_2"] = 22,
    ["LevelScene_70261_3"] = 22,
    ["LevelScene_70261_4"] = 22,
    ["LevelScene_70263_0"] = 22,
    ["Live_Confirm"] = 23,
    ["Live_Confirm2"] = 23,
    ["Live_GiveLive"] = 23,
    ["Live_GameName"] = 23,
    ["Live_OpenWxLiveWebTips"] = 23,
    ["Live_LiveLinkStart"] = 23,
    ["GM_level_Info"] = 23,
    ["GM_DS_Version"] = 23,
    ["Friend_Unbind_Intimate"] = 23,
    ["Friend_Confirm_Unbind"] = 23,
    ["Friend_SendApply"] = 23,
    ["Friend_Added"] = 23,
    ["Rank_AllServer"] = 23,
    ["Rank_Friend"] = 23,
    ["Rank_Self"] = 23,
    ["Rank_ChangeAreaSuccess"] = 23,
    ["Rank_ChangeInfoMax"] = 23,
    ["Rank_GpsIsClose"] = 23,
    ["Rank_HidePlayerInfo"] = 23,
    ["Rank_MapName_OneParam"] = 23,
    ["Model_IsClose"] = 23,
    ["Model_RewardTips"] = 23,
    ["Model_CurrPlayIsLock"] = 23,
    ["Model_CurrTeam_PlayerCountNotEnough"] = 23,
    ["Model_CurrTeam_NotOne"] = 23,
    ["Model_CurrTeam_NotTwo"] = 23,
    ["Model_CurrTeam_NotFour"] = 23,
    ["Activity_Month_CollectionItemed"] = 23,
    ["Activity_Month_CollectionItemed1"] = 23,
    ["Activity_Month_DrawPoolTitle"] = 23,
    ["Activith_Common_DrawCountIsMax"] = 23,
    ["Activith_Common_DayDrawCountIsMax"] = 23,
    ["Common_HideInfo_Name"] = 23,
    ["Common_HideInfo_ShortName"] = 23,
    ["Common_PlayerDataError"] = 23,
    ["Common_EveryDayMax_OneParam"] = 23,
    ["Common_ItemCount_NotEnough"] = 23,
    ["DrawReward_DrawBtnNoTimes"] = 23,
    ["DrawReward_DrawBtnNoEnoughDailyTimes"] = 23,
    ["DrawReward_DrawBtnNoDailyTimes"] = 23,
    ["DrawReward_MallViewUnshelve"] = 23,
    ["DrawReward_MallViewToUnshelve"] = 23,
    ["DrawReward_MallViewNoExchangeCount"] = 23,
    ["DrawReward_MallViewOwnedTurnInto"] = 23,
    ["DrawReward_MallConfirm"] = 23,
    ["DrawReward_AccessoriesViewNoTimes"] = 23,
    ["DrawReward_AccessoriesViewUpdateTime"] = 23,
    ["DrawReward_CardViewStepForward"] = 23,
    ["DrawReward_CardViewUpdateTime"] = 23,
    ["DrawReward_MainViewUpdateTime"] = 23,
    ["DrawReward_MultiRaffleUpdateTime"] = 23,
    ["DrawReward_MultiRaffleDrawOnce"] = 23,
    ["DrawReward_MultiRaffleDrawRound"] = 23,
    ["DrawReward_MultiRaffleBackTo"] = 23,
    ["DrawReward_MultiRaffleNoTimes"] = 23,
    ["DrawReward_SeasonViewBigReward"] = 23,
    ["DrawReward_SeasonViewUpdateTime"] = 23,
    ["DrawReward_SeasonViewButterflyDraw"] = 23,
    ["DrawReward_DrawAgain"] = 23,
    ["DrawReward_SocialViewFreeNum"] = 23,
    ["DrawReward_SocialViewFreeOpen"] = 23,
    ["DrawReward_SocialViewOpen"] = 23,
    ["DrawReward_SocialViewPayConfirm"] = 23,
    ["DrawReward_SocialViewDrawConfirm"] = 23,
    ["DrawReward_UpdateTime"] = 23,
    ["DrawReward_DayHour"] = 23,
    ["DrawReward_HourMinute"] = 23,
    ["DrawReward_CoinSupplyVoucher"] = 23,
    ["DrawReward_ActivationViewFreeCountDown"] = 23,
    ["DrawReward_DontRemindAnymore"] = 23,
    ["DrawReward_ExchangeBoxCurrentCurrency"] = 23,
    ["DrawReward_AutoExchangeFailed"] = 23,
    ["DrawReward_WrongVersion"] = 23,
    ["DrawReward_SocialViewDiscountNum"] = 23,
    ["TokenView_Invite"] = 23,
    ["TokenView_Assist"] = 23,
    ["TokenAssistConfirm"] = 23,
    ["TokenAssistNum"] = 23,
    ["TokenAssistSuccess"] = 23,
    ["TokenAssistNoNum"] = 23,
    ["TokenFailToCreate"] = 23,
    ["TokenNoInput"] = 23,
    ["UI_Setting_PrivacyAuthority_GotoChangeNameTitle"] = 23,
    ["UI_Setting_PrivacyAuthority_SearchInfoTitle"] = 23,
    ["UI_Setting_Frame_LogoutTip"] = 23,
    ["UI_Setting_Frame_Title"] = 23,
    ["UI_Setting_Voice_Title"] = 23,
    ["UI_Setting_Privacy_Title"] = 23,
    ["UI_Setting_DownloadManagement_Title"] = 23,
    ["UI_Recharge_Vip_Tip"] = 23,
    ["UI_Recharge_SeasonRecharge_IsOpening"] = 23,
    ["UI_Recharge_SeasonRecharge_BuyCountTime"] = 23,
    ["UI_Recharge_SeasonRecharge_GetCountTime"] = 23,
    ["UI_Recharge_SeasonRecharge_BuyAgainTip_Small"] = 23,
    ["UI_Recharge_SeasonRecharge_BuyAgainTip_Large"] = 23,
    ["UI_Recharge_Recharge_LevelupCanGetTip"] = 23,
    ["UI_Recharge_Recharge_MaxLevelGetTip"] = 23,
    ["UI_Recharge_Recharge_LevelGetTip"] = 23,
    ["UI_Recharge_Recharge_MaxLevelTitle"] = 23,
    ["UI_Recharge_Recharge_LevelUpTip"] = 23,
    ["UI_Recharge_MonthCard_BuyRewardTip"] = 24,
    ["Common_BuyConfirm"] = 24,
    ["UI_Recharge_MonthCard_BuyMonthCardConfirmDesc"] = 24,
    ["Common_CutDesc"] = 24,
    ["UI_Recharge_MonthCard_SeasonLimitDesc"] = 24,
    ["UI_Recharge_MonthCard_WeekLimitDesc"] = 24,
    ["UI_Recharge_MonthCard_BuyAgainDesc"] = 24,
    ["UI_Recharge_MonthCard_BuyDesc"] = 24,
    ["Common_TakeEffect"] = 24,
    ["UI_Recharge_Recharge_Title"] = 24,
    ["UI_Recharge_Vip_Title"] = 24,
    ["UI_Recharge_CutGift_Title"] = 24,
    ["UI_Recharge_FirstChargeMain_Title"] = 24,
    ["UI_Recharge_SeasonRecharge_Title"] = 24,
    ["UI_Recharge_MonthCard_Title"] = 24,
    ["UI_Recharge_UI_ChargeRebate_Main_Title"] = 24,
    ["UI_Recharge_ChargeRebate_OpenBetaRebate_Title"] = 24,
    ["Common_RMBDesc"] = 24,
    ["Mall_Limit_Buy"] = 24,
    ["Mall_QualitySort"] = 24,
    ["Mall_TimeSort"] = 24,
    ["Mall_BuyFailed"] = 24,
    ["Mall_Buy"] = 24,
    ["Mall_BuyConfirm"] = 24,
    ["Mall_NotFindAny_AndChange"] = 24,
    ["Mall_NotHasBaseColor"] = 24,
    ["Mall_IsBuy"] = 24,
    ["Mall_ShopAlreadyHas_NotBuy"] = 24,
    ["Mall_CurrencyNotEnough"] = 24,
    ["Task_Open"] = 24,
    ["Task_NotCompleted"] = 24,
    ["Task_AfterOpen"] = 24,
    ["Task_ActivityHasEnded"] = 24,
    ["Task_ActivityEnded_ComeBackNextTime"] = 24,
    ["Task_NextRankReward"] = 24,
    ["Task_DistanceNewbieEnded"] = 24,
    ["Task_NewbieHasEnded"] = 24,
    ["Task_ImpactTask"] = 24,
    ["Task_ExperienceTask"] = 24,
    ["Task_AchieveRankGetSkin"] = 24,
    ["Task_EndOfCurrentSeason"] = 24,
    ["Task_UGCTaskEnded"] = 24,
    ["Task_AwardReceived"] = 24,
    ["CustomRoom_InviteMsg"] = 24,
    ["CustomRoom_ApplyToChangeSeatMsg"] = 24,
    ["CustomRoom_RejectInviteInTeam"] = 24,
    ["CustomRoom_FullyTip"] = 24,
    ["CustomRoom_InviteCDTip"] = 24,
    ["CustomRoom_NotInviteByState"] = 24,
    ["CustomRoom_InviteSendTip"] = 24,
    ["CustomRoom_WaitPlayer"] = 24,
    ["CustomRoom_AllMode"] = 24,
    ["CustomRoom_AllMatch"] = 24,
    ["CustomRoom_RoomIDHint"] = 24,
    ["CustomRoom_PasswordHint"] = 24,
    ["CustomRoom_RoomName"] = 24,
    ["CustomRoom_JoinRoomChatTip"] = 24,
    ["CustomRoom_LeaveRoomChatTip"] = 24,
    ["CustomRoom_ChaterName"] = 24,
    ["CustomRoom_NoVoicePermission"] = 24,
    ["CustomRoom_NoPassword"] = 24,
    ["CustomRoom_LeaveRoomTip"] = 24,
    ["CustomRoom_StartRoomMinCountTip"] = 24,
    ["CustomRoom_RoomIDCopiedTip"] = 24,
    ["CustomRoom_RobotName"] = 24,
    ["CustomRoom_KickOut"] = 24,
    ["CustomRoom_KickOutCancel"] = 24,
    ["CustomRoom_RobotCover"] = 24,
    ["CustomRoom_RobotRemove"] = 24,
    ["CustomRoom_Invite"] = 24,
    ["CustomRoom_Start"] = 24,
    ["CustomRoom_WaitStart"] = 24,
    ["CustomRoom_KickOutOne"] = 24,
    ["CustomRoom_KickNoInvite"] = 24,
    ["UGCRoom_PreparationReminderAllPrepared"] = 24,
    ["UGCRoom_PreparationReminderCD"] = 24,
    ["UGCRoom_WaitReady"] = 24,
    ["UGCRoom_CancelReady"] = 24,
    ["UGCRoom_RemindReady"] = 24,
    ["Player_BanTip"] = 24,
    ["Player_BanTipWithReason"] = 24,
    ["Player_GetInfoError"] = 24,
    ["Player_HideProfileTip"] = 24,
    ["Player_ProfileTabName"] = 24,
    ["Player_UGCTabName"] = 24,
    ["Player_HistoryTabName"] = 24,
    ["Player_LevelIllustrationTabName"] = 24,
    ["Player_SuitTabName"] = 24,
    ["Player_AchievementTabName"] = 24,
    ["Player_ShareState"] = 24,
    ["Player_NoStateTip"] = 24,
    ["Player_NoTitleTip"] = 24,
    ["Player_NickName"] = 24,
    ["Player_Gender"] = 24,
    ["Player_HeadBG"] = 24,
    ["Player_NickNameBG"] = 24,
    ["Player_Title"] = 24,
    ["Player_RankShow"] = 24,
    ["Player_ChangeGenderTotalCDTip"] = 24,
    ["Player_ChangeGenderCurrentCDTip"] = 24,
    ["Player_ChangeGenderSuccess"] = 25,
    ["Player_ChangerGenderTip"] = 25,
    ["Player_NoGenderSelectTip"] = 25,
    ["Player_GenderChangedCDTip"] = 25,
    ["Player_FullyLabelTip"] = 25,
    ["Player_ChangeNickNameTip"] = 25,
    ["Player_ChangeNickNameSuccess"] = 25,
    ["Player_NoChangeNameCard"] = 25,
    ["Player_SuitAll"] = 25,
    ["Player_SuitTheme"] = 25,
    ["Player_SuitSeason"] = 25,
    ["Player_SuitCollection"] = 25,
    ["Player_HaveSuitTip"] = 25,
    ["Currency_NotEnoughTip"] = 25,
    ["Currency_ItemInfoError"] = 25,
    ["Currency_BuySuccessTip"] = 25,
    ["Currency_BuyFailTip"] = 25,
    ["UI_Login_AccountDiffDesc"] = 25,
    ["Common_Warning"] = 25,
    ["friend_list"] = 25,
    ["friend_islands"] = 25,
    ["friend_nearby"] = 25,
    ["friend_search"] = 25,
    ["friend_unknown"] = 25,
    ["friend_minute"] = 25,
    ["friend_hour"] = 25,
    ["friend_day"] = 25,
    ["friend_month"] = 25,
    ["friend_RequestSent"] = 25,
    ["friend_ServerError"] = 25,
    ["friend_SuccessfulRelationship"] = 25,
    ["friend_BuildRelationships"] = 25,
    ["friend_MaximumLimit"] = 25,
    ["friend_OppositeSex"] = 25,
    ["friend_IntimacyRequestSent"] = 25,
    ["friend_RelationshipConversionRequestSent"] = 25,
    ["friend_QuantityRemaining"] = 25,
    ["friend_applyforintimacy"] = 25,
    ["friend_conversionrelationship"] = 25,
    ["friend_Applicationforterminationofrelationship"] = 25,
    ["friend_Intimaterelationshiprights"] = 25,
    ["friend_Intimacyrewards"] = 25,
    ["friend_meter"] = 25,
    ["friend_alreadydeliveredcotton"] = 25,
    ["friend_cottongifted"] = 25,
    ["friend_brother"] = 25,
    ["friend_squarePlayer"] = 25,
    ["friend_clickRecruitment"] = 25,
    ["friend_goAdd"] = 25,
    ["friend_noPlayers"] = 25,
    ["friend_noPlayersNearby"] = 25,
    ["friend_development"] = 25,
    ["friend_intimacy"] = 25,
    ["friend_notFound"] = 25,
    ["friend_enterSearch"] = 25,
    ["friend_nodata"] = 25,
    ["friend_nonHomeowner"] = 25,
    ["friend_InTheGame"] = 25,
    ["friend_pullToHim"] = 25,
    ["friend_increaseIntimacy"] = 25,
    ["friend_RepeatApplication"] = 25,
    ["Common_MCL_DailyLimit"] = 25,
    ["Common_MCL_MonthlyLimit"] = 25,
    ["Common_MCL_WeeklyLimit"] = 25,
    ["Common_MCL_YearlyLimit"] = 25,
    ["Common_MCL_LifeLongLimit"] = 25,
    ["Common_MCL_SeasonLimit"] = 25,
    ["Common_MCL_DailyLimit2"] = 25,
    ["Common_MCL_MonthlyLimit2"] = 25,
    ["Common_MCL_WeeklyLimit2"] = 25,
    ["Common_MCL_YearlyLimit2"] = 25,
    ["Common_MCL_LifeLongLimit2"] = 25,
    ["Common_MCL_SeasonLimit2"] = 25,
    ["InLevel_QST_Champion"] = 25,
    ["InLevel_QST_Rank"] = 25,
    ["InLevel_QST_LevelPass"] = 25,
    ["InLevel_QST_FinalLevelRank"] = 25,
    ["InLevel_QST_FirstDegreeUp"] = 25,
    ["InLevel_QST_LevelPerformance"] = 25,
    ["InLevel_QST_FriendCooperation"] = 25,
    ["InLevel_QST_DailyFirstWin"] = 25,
    ["InLevel_QST_ProtectedScoreAdditional"] = 25,
    ["InLevel_QST_ProtectedScoreProtected"] = 25,
    ["InLevel_QST_ReturingCooperation"] = 25,
    ["InLevel_QST_ReturingPrivilege"] = 25,
    ["InLevel_QST_TeamRankActivityMultiScore"] = 25,
    ["Bag_UseItemSuccess"] = 25,
    ["Bag_UseItemFail"] = 25,
    ["Bag_DelItemSuccess"] = 25,
    ["Bag_DelItemFail"] = 25,
    ["Bag_SelItemSuccess"] = 25,
    ["Bag_SelItemFail"] = 25,
    ["Bag_ItemExpire"] = 25,
    ["Bag_SlotSaveSuccess"] = 25,
    ["Bag_SlotSaveSuccess2"] = 25,
    ["Bag_EmotionSaveSuccess"] = 25,
    ["Team_MemberFull"] = 25,
    ["Team_InviteSendFail"] = 25,
    ["Team_InviteCanNotSend"] = 25,
    ["Team_PlayerInTeam"] = 25,
    ["Team_InviteSendFinish"] = 26,
    ["Common_NetConnectError"] = 26,
    ["Team_JoinReqSendFinish"] = 26,
    ["Team_MemberFull2"] = 26,
    ["Team_RecruitCD"] = 26,
    ["Team_RecruitSendSuccess"] = 26,
    ["Team_CanNotJoinTeam"] = 26,
    ["Team_JoinTeamSuccess"] = 26,
    ["Team_InviteJoinTeam"] = 26,
    ["Team_ReqJoinTeam"] = 26,
    ["Team_CanNotAcceptInvite"] = 26,
    ["Team_CanNotAddMember"] = 26,
    ["Team_DownloadInWifi"] = 26,
    ["Team_DownloadInNet"] = 26,
    ["Team_DownloadInWifi2"] = 26,
    ["Team_DownloadInNet2"] = 26,
    ["Team_WaitMemberDownload"] = 26,
    ["Team_SelfCancelReady"] = 26,
    ["Team_PlayerCancelReady"] = 26,
    ["Team_CanNotCancelMatch"] = 26,
    ["Pak_XSDownloadTips"] = 26,
    ["Pak_PLDownloadTips"] = 26,
    ["Pak_RemoveCleanupTip1"] = 26,
    ["Pak_RemoveCleanupTip2"] = 26,
    ["Pak_RemoveCleanupGeneralTip"] = 26,
    ["Pak_StartDownload"] = 26,
    ["Pak_StopDownload"] = 26,
    ["Pak_EndDownload"] = 26,
    ["Pak_DownloadTip"] = 26,
    ["Mode_ModeSelectNoOpen"] = 26,
    ["Mode_CanNotChangeMode"] = 26,
    ["Mode_CanNotChangeMode2"] = 26,
    ["Mode_HasMemberInLevel"] = 26,
    ["Mode_MatchTypeNoOpen"] = 26,
    ["Common_MoneyNotEnough"] = 26,
    ["Common_MoneyNotEnough2"] = 26,
    ["Guide_CreateRole_Name1"] = 26,
    ["Guide_CreateRole_Name2"] = 26,
    ["Guide_CreateRole_Name3"] = 26,
    ["Guide_CreateRole_Name4"] = 26,
    ["Guide_CreateRole_Name5"] = 26,
    ["Guide_CreateRole_Name6"] = 26,
    ["Guide_CreateRole_Name7"] = 26,
    ["Guide_CreateRole_Name8"] = 26,
    ["Guide_CreateRole_Name9"] = 26,
    ["Guide_CreateRole_Name10"] = 26,
    ["Guide_CreateRole_Name11"] = 26,
    ["Guide_CreateRole_Name12"] = 26,
    ["Guide_CreateRole_Name13"] = 26,
    ["Guide_CreateRole_Name14"] = 26,
    ["Guide_CreateRole_Name15"] = 26,
    ["Guide_CreateRole_Name16"] = 26,
    ["Guide_CreateRole_Name17"] = 26,
    ["Guide_CreateRole_Name18"] = 26,
    ["Guide_CreateRole_Name19"] = 26,
    ["Guide_CreateRole_Name20"] = 26,
    ["Guide_CreateRole_Name21"] = 26,
    ["Guide_CreateRole_Name22"] = 26,
    ["Guide_CreateRole_Name23"] = 26,
    ["Guide_CreateRole_Name24"] = 26,
    ["Guide_CreateRole_Name25"] = 26,
    ["Guide_CreateRole_Name26"] = 26,
    ["Guide_CreateRole_Name27"] = 26,
    ["Guide_CreateRole_Name28"] = 26,
    ["Guide_CreateRole_Name29"] = 26,
    ["Guide_CreateRole_Name30"] = 26,
    ["Guide_CreateRole_Name31"] = 26,
    ["Guide_CreateRole_Name32"] = 26,
    ["Guide_CreateRole_Name33"] = 26,
    ["Guide_CreateRole_Name34"] = 26,
    ["Guide_CreateRole_Name35"] = 26,
    ["Guide_CreateRole_Name36"] = 26,
    ["Guide_CreateRole_Name37"] = 26,
    ["Guide_CreateRole_Name38"] = 26,
    ["Guide_CreateRole_Name39"] = 26,
    ["Guide_CreateRole_Name40"] = 26,
    ["Guide_CreateRole_Name41"] = 26,
    ["Guide_CreateRole_Name42"] = 26,
    ["Guide_CreateRole_Name43"] = 26,
    ["Guide_CreateRole_Name44"] = 26,
    ["Guide_CreateRole_Name45"] = 26,
    ["Guide_CreateRole_Name46"] = 26,
    ["Guide_CreateRole_Name47"] = 26,
    ["Guide_CreateRole_Name48"] = 26,
    ["Guide_CreateRole_Name49"] = 26,
    ["Guide_CreateRole_Name50"] = 26,
    ["Guide_CreateRole_Name51"] = 26,
    ["Guide_CreateRole_Name52"] = 26,
    ["Guide_CreateRole_Name53"] = 26,
    ["Guide_CreateRole_Name54"] = 26,
    ["Guide_CreateRole_Name55"] = 26,
    ["Guide_CreateRole_Name56"] = 26,
    ["Guide_CreateRole_Name57"] = 26,
    ["Guide_CreateRole_Name58"] = 26,
    ["Guide_CreateRole_Name59"] = 26,
    ["Guide_CreateRole_Name60"] = 26,
    ["Guide_CreateRole_Name61"] = 26,
    ["Guide_CreateRole_Name62"] = 26,
    ["Guide_CreateRole_Name63"] = 26,
    ["Guide_CreateRole_Name64"] = 26,
    ["Guide_CreateRole_Name65"] = 27,
    ["Guide_CreateRole_Name66"] = 27,
    ["Guide_CreateRole_Name67"] = 27,
    ["Guide_CreateRole_Name68"] = 27,
    ["Guide_CreateRole_Name69"] = 27,
    ["Guide_CreateRole_Name70"] = 27,
    ["Guide_CreateRole_Name71"] = 27,
    ["Guide_CreateRole_Name72"] = 27,
    ["Guide_CreateRole_Name73"] = 27,
    ["Guide_CreateRole_Name74"] = 27,
    ["Guide_CreateRole_Name75"] = 27,
    ["Guide_CreateRole_Name76"] = 27,
    ["Guide_CreateRole_Name77"] = 27,
    ["Guide_CreateRole_Name78"] = 27,
    ["Guide_CreateRole_Name79"] = 27,
    ["Guide_CreateRole_Name80"] = 27,
    ["Guide_CreateRole_Name81"] = 27,
    ["Guide_CreateRole_Name82"] = 27,
    ["Guide_CreateRole_Name83"] = 27,
    ["Guide_CreateRole_Name84"] = 27,
    ["Guide_CreateRole_Name85"] = 27,
    ["Guide_CreateRole_Name86"] = 27,
    ["Guide_CreateRole_Name87"] = 27,
    ["Guide_CreateRole_Name88"] = 27,
    ["Guide_CreateRole_Name89"] = 27,
    ["Guide_CreateRole_Name90"] = 27,
    ["Guide_CreateRole_Name91"] = 27,
    ["Guide_CreateRole_Name92"] = 27,
    ["Guide_CreateRole_Name93"] = 27,
    ["Guide_CreateRole_Name94"] = 27,
    ["Guide_CreateRole_Name95"] = 27,
    ["Guide_CreateRole_Name96"] = 27,
    ["Guide_CreateRole_Name97"] = 27,
    ["Guide_CreateRole_Name98"] = 27,
    ["Guide_CreateRole_Name99"] = 27,
    ["Guide_CreateRole_Name100"] = 27,
    ["Guide_CreateRole_Name101"] = 27,
    ["Guide_CreateRole_Name102"] = 27,
    ["Guide_CreateRole_Name103"] = 27,
    ["Guide_CreateRole_Name104"] = 27,
    ["Guide_CreateRole_Name105"] = 27,
    ["Guide_CreateRole_Name106"] = 27,
    ["Guide_CreateRole_Name107"] = 27,
    ["Guide_CreateRole_Name108"] = 27,
    ["Guide_CreateRole_Name109"] = 27,
    ["Guide_CreateRole_Name110"] = 27,
    ["Guide_CreateRole_Name111"] = 27,
    ["Guide_CreateRole_Name112"] = 27,
    ["Guide_CreateRole_Name113"] = 27,
    ["Guide_CreateRole_Name114"] = 27,
    ["Guide_CreateRole_Name115"] = 27,
    ["Guide_CreateRole_Name116"] = 27,
    ["Guide_CreateRole_Name117"] = 27,
    ["Guide_CreateRole_Name118"] = 27,
    ["Guide_CreateRole_Name119"] = 27,
    ["Guide_CreateRole_Name120"] = 27,
    ["Guide_CreateRole_Name121"] = 27,
    ["Guide_CreateRole_Name122"] = 27,
    ["Guide_CreateRole_Name123"] = 27,
    ["Guide_CreateRole_Name124"] = 27,
    ["Guide_CreateRole_Name125"] = 27,
    ["Guide_CreateRole_Name126"] = 27,
    ["Guide_CreateRole_Name127"] = 27,
    ["Guide_CreateRole_Name128"] = 27,
    ["Guide_CreateRole_Name129"] = 27,
    ["Guide_CreateRole_Name130"] = 27,
    ["Guide_CreateRole_Name131"] = 27,
    ["Guide_CreateRole_Name132"] = 27,
    ["Guide_CreateRole_Name133"] = 27,
    ["Guide_CreateRole_Name134"] = 27,
    ["Guide_CreateRole_Name135"] = 27,
    ["Guide_CreateRole_Name136"] = 27,
    ["Guide_CreateRole_Name137"] = 27,
    ["Guide_CreateRole_Name138"] = 27,
    ["Guide_CreateRole_Name139"] = 27,
    ["Guide_CreateRole_Name140"] = 27,
    ["Guide_CreateRole_Name141"] = 27,
    ["Guide_CreateRole_Name142"] = 27,
    ["Guide_CreateRole_Name143"] = 27,
    ["Guide_CreateRole_Name144"] = 27,
    ["Guide_CreateRole_Name145"] = 27,
    ["Guide_CreateRole_Name146"] = 27,
    ["Guide_CreateRole_Name147"] = 27,
    ["Guide_CreateRole_Name148"] = 27,
    ["Guide_CreateRole_Name149"] = 27,
    ["Guide_CreateRole_Name150"] = 27,
    ["Guide_CreateRole_Name151"] = 27,
    ["Guide_CreateRole_Name152"] = 27,
    ["Guide_CreateRole_Name153"] = 27,
    ["Guide_CreateRole_Name154"] = 27,
    ["Guide_CreateRole_Name155"] = 27,
    ["Guide_CreateRole_Name156"] = 27,
    ["Guide_CreateRole_Name157"] = 27,
    ["Guide_CreateRole_Name158"] = 27,
    ["Guide_CreateRole_Name159"] = 27,
    ["Guide_CreateRole_Name160"] = 27,
    ["Guide_CreateRole_Name161"] = 27,
    ["Guide_CreateRole_Name162"] = 27,
    ["Guide_CreateRole_Name163"] = 27,
    ["Guide_CreateRole_Name164"] = 27,
    ["Guide_CreateRole_Name165"] = 28,
    ["Guide_CreateRole_Name166"] = 28,
    ["Guide_CreateRole_Name167"] = 28,
    ["Guide_CreateRole_Name168"] = 28,
    ["Guide_CreateRole_Name169"] = 28,
    ["Guide_CreateRole_Name170"] = 28,
    ["Guide_CreateRole_Name171"] = 28,
    ["Guide_CreateRole_Name172"] = 28,
    ["Guide_CreateRole_Name173"] = 28,
    ["Guide_CreateRole_Name174"] = 28,
    ["Guide_CreateRole_Name175"] = 28,
    ["Guide_CreateRole_Name176"] = 28,
    ["Guide_CreateRole_Name177"] = 28,
    ["Guide_CreateRole_Name178"] = 28,
    ["Guide_CreateRole_Name179"] = 28,
    ["Guide_CreateRole_Name180"] = 28,
    ["Guide_CreateRole_Name181"] = 28,
    ["Guide_CreateRole_Name182"] = 28,
    ["Guide_CreateRole_Name183"] = 28,
    ["Guide_CreateRole_Name184"] = 28,
    ["Guide_CreateRole_Name185"] = 28,
    ["Guide_CreateRole_Name186"] = 28,
    ["Guide_CreateRole_Name187"] = 28,
    ["Guide_CreateRole_Name188"] = 28,
    ["Guide_CreateRole_Name189"] = 28,
    ["Guide_CreateRole_Name190"] = 28,
    ["Guide_CreateRole_Name191"] = 28,
    ["Guide_CreateRole_Name192"] = 28,
    ["Guide_CreateRole_Name193"] = 28,
    ["Guide_CreateRole_Name194"] = 28,
    ["Guide_CreateRole_Name195"] = 28,
    ["Guide_CreateRole_Name196"] = 28,
    ["Guide_CreateRole_Name197"] = 28,
    ["Guide_CreateRole_Name198"] = 28,
    ["Guide_CreateRole_Name199"] = 28,
    ["Guide_CreateRole_Name200"] = 28,
    ["Guide_CreateRole_Name201"] = 28,
    ["Guide_CreateRole_Name202"] = 28,
    ["Guide_CreateRole_Name203"] = 28,
    ["Guide_CreateRole_Name204"] = 28,
    ["Guide_CreateRole_Name205"] = 28,
    ["Guide_CreateRole_Name206"] = 28,
    ["Guide_CreateRole_Name207"] = 28,
    ["Guide_CreateRole_Name208"] = 28,
    ["Guide_CreateRole_Name209"] = 28,
    ["Guide_CreateRole_Name210"] = 28,
    ["Guide_CreateRole_Name211"] = 28,
    ["Guide_CreateRole_Name212"] = 28,
    ["Guide_CreateRole_Name213"] = 28,
    ["Guide_CreateRole_Name214"] = 28,
    ["Guide_CreateRole_Name215"] = 28,
    ["Guide_CreateRole_Name216"] = 28,
    ["Guide_CreateRole_Name217"] = 28,
    ["Guide_CreateRole_Name218"] = 28,
    ["Guide_CreateRole_Name219"] = 28,
    ["Guide_CreateRole_Name220"] = 28,
    ["Guide_CreateRole_Name221"] = 28,
    ["Guide_CreateRole_Name222"] = 28,
    ["Guide_CreateRole_Name223"] = 28,
    ["Guide_CreateRole_Name224"] = 28,
    ["Guide_CreateRole_Name225"] = 28,
    ["Guide_CreateRole_Name226"] = 28,
    ["Guide_CreateRole_Name227"] = 28,
    ["Guide_CreateRole_Name228"] = 28,
    ["Guide_CreateRole_Name229"] = 28,
    ["Guide_CreateRole_Name230"] = 28,
    ["Guide_CreateRole_Name231"] = 28,
    ["Guide_CreateRole_Name232"] = 28,
    ["Guide_CreateRole_Name233"] = 28,
    ["Guide_CreateRole_Name234"] = 28,
    ["Guide_CreateRole_Name235"] = 28,
    ["Guide_CreateRole_Name236"] = 28,
    ["Guide_CreateRole_Name237"] = 28,
    ["Guide_CreateRole_Name238"] = 28,
    ["Guide_CreateRole_Name239"] = 28,
    ["Guide_CreateRole_Name240"] = 28,
    ["Guide_CreateRole_Name241"] = 28,
    ["Guide_CreateRole_Name242"] = 28,
    ["Guide_CreateRole_Name243"] = 28,
    ["Guide_CreateRole_Name244"] = 28,
    ["Guide_CreateRole_Name245"] = 28,
    ["Guide_CreateRole_Name246"] = 28,
    ["Guide_CreateRole_Name247"] = 28,
    ["Guide_CreateRole_Name248"] = 28,
    ["Guide_CreateRole_Name249"] = 28,
    ["Guide_CreateRole_Name250"] = 28,
    ["Guide_CreateRole_Name251"] = 28,
    ["Guide_CreateRole_Name252"] = 28,
    ["Guide_CreateRole_Name253"] = 28,
    ["Guide_CreateRole_Name254"] = 28,
    ["Guide_CreateRole_Name255"] = 28,
    ["Guide_CreateRole_Name256"] = 28,
    ["Guide_CreateRole_Name257"] = 28,
    ["Guide_CreateRole_Name258"] = 28,
    ["Guide_CreateRole_Name259"] = 28,
    ["Guide_CreateRole_Name260"] = 28,
    ["Guide_CreateRole_Name261"] = 28,
    ["Guide_CreateRole_Name262"] = 28,
    ["Guide_CreateRole_Name263"] = 28,
    ["Guide_CreateRole_Name264"] = 28,
    ["Guide_CreateRole_Name265"] = 29,
    ["Guide_CreateRole_Name266"] = 29,
    ["Guide_CreateRole_Name267"] = 29,
    ["Guide_CreateRole_Name268"] = 29,
    ["Guide_CreateRole_Name269"] = 29,
    ["Guide_CreateRole_Name270"] = 29,
    ["Guide_CreateRole_Name271"] = 29,
    ["Guide_CreateRole_Name272"] = 29,
    ["Guide_CreateRole_Name273"] = 29,
    ["Guide_CreateRole_Name274"] = 29,
    ["Guide_CreateRole_Name275"] = 29,
    ["Guide_CreateRole_Name276"] = 29,
    ["Guide_CreateRole_Name277"] = 29,
    ["Guide_CreateRole_Name278"] = 29,
    ["Guide_CreateRole_Name279"] = 29,
    ["Guide_CreateRole_Name280"] = 29,
    ["Guide_CreateRole_Name281"] = 29,
    ["Guide_CreateRole_Name282"] = 29,
    ["Guide_CreateRole_Name283"] = 29,
    ["Guide_CreateRole_Name284"] = 29,
    ["Guide_CreateRole_Name285"] = 29,
    ["Guide_CreateRole_Name286"] = 29,
    ["Guide_CreateRole_Name287"] = 29,
    ["Guide_CreateRole_Name288"] = 29,
    ["Guide_CreateRole_Name289"] = 29,
    ["Guide_CreateRole_Name290"] = 29,
    ["Guide_CreateRole_Name291"] = 29,
    ["Guide_CreateRole_Name292"] = 29,
    ["Guide_CreateRole_Name293"] = 29,
    ["Guide_CreateRole_Name294"] = 29,
    ["Guide_CreateRole_Name295"] = 29,
    ["Guide_CreateRole_Name296"] = 29,
    ["Guide_CreateRole_Name297"] = 29,
    ["Guide_CreateRole_Name298"] = 29,
    ["Guide_CreateRole_Name299"] = 29,
    ["Guide_CreateRole_Name300"] = 29,
    ["Guide_CreateRole_Name301"] = 29,
    ["Guide_CreateRole_Name302"] = 29,
    ["Guide_CreateRole_Name303"] = 29,
    ["Guide_CreateRole_Name304"] = 29,
    ["Guide_CreateRole_Name305"] = 29,
    ["Guide_CreateRole_Name306"] = 29,
    ["Guide_CreateRole_Name307"] = 29,
    ["Guide_CreateRole_Name308"] = 29,
    ["Guide_CreateRole_Name309"] = 29,
    ["QQMusicEmpty"] = 29,
    ["Replay_ReplayFail"] = 29,
    ["Replay_NotFindReplayFile"] = 29,
    ["Replay_BadFile"] = 29,
    ["Replay_GetFileFail"] = 29,
    ["Replay_DownloadFileFail"] = 29,
    ["InLevel_HeartBeatTimeOut"] = 29,
    ["Activity_RemainCount"] = 29,
    ["Activity_Shared"] = 29,
    ["Activity_AfterUnlocking"] = 29,
    ["Activity_Received"] = 29,
    ["Activity_NotReceived"] = 29,
    ["Activity_ShareCanReceive"] = 29,
    ["Activity_RedPacketUnlock"] = 29,
    ["Activity_LoadPicFail"] = 29,
    ["Activity_Remaining"] = 29,
    ["Activity_SureSpendReSigning"] = 29,
    ["Activity_NotEnough"] = 29,
    ["Activity_ThisWeekSignNotCount"] = 29,
    ["Activity_ServerDataException"] = 29,
    ["Activity_DrawNotEnough"] = 29,
    ["Activity_ActivityTime"] = 29,
    ["Activity_SureToSpend"] = 29,
    ["Activity_MakeSignature"] = 29,
    ["Activity_ThisWeekCanSignTime"] = 29,
    ["Activity_Signature"] = 29,
    ["Activity_DepositNotEnough"] = 29,
    ["Activity_CheckInAndReceiveDeposit"] = 29,
    ["Activity_WeekendCanReceiveDeposit"] = 29,
    ["Activity_Unlock"] = 29,
    ["Activity_Receive"] = 29,
    ["Activity_SuperRedPacketShare"] = 29,
    ["Activity_ShareRedPacket"] = 29,
    ["Activity_ReceiveRedPacket"] = 29,
    ["RealTimeIntervene_Send"] = 29,
    ["RealTimeIntervene_Get"] = 29,
    ["ChargeRebate_Opening"] = 29,
    ["ChargeRebate_ReadyOpen"] = 29,
    ["ChargeRebate_Rebate"] = 29,
    ["Season_HasNum"] = 29,
    ["Season_HighBpCanExchange"] = 29,
    ["Season_HighBpLevelLimit"] = 29,
    ["Season_ExchangeTimeOut"] = 29,
    ["Season_ExchangeBuyCountOver"] = 29,
    ["Season_Left"] = 29,
    ["Season_Round"] = 29,
    ["Season_NextRoundTime"] = 29,
    ["Season_CurrentRoundEndTime"] = 29,
    ["Mail_DeleteAllRead"] = 29,
    ["Mail_TimeOut"] = 29,
    ["Mail_OnekeyGet"] = 29,
    ["Mail_OnekeyRead"] = 29,
    ["Mail_ConfirmDeleteAll"] = 29,
    ["Mail_ConfirmDelete"] = 29,
    ["BP_UpToLevelAndUnlock"] = 29,
    ["BP_CostBuy"] = 30,
    ["BP_AlreadyHas"] = 30,
    ["BP_HighBpCanExchange"] = 30,
    ["BP_ExchangeTimeOut"] = 30,
    ["BP_ExchangeBuyCountOver"] = 30,
    ["BP_Award"] = 30,
    ["BP_Task"] = 30,
    ["BP_Exchange"] = 30,
    ["BP_BPAward"] = 30,
    ["BP_EveryDayTask"] = 30,
    ["BP_SeasonTask"] = 30,
    ["BP_WeekTaskTarget"] = 30,
    ["BP_WeekTask"] = 30,
    ["BP_CostUnlockNormal"] = 30,
    ["BP_CostUnlockHigh"] = 30,
    ["NewChat_NotFriend"] = 30,
    ["NewChat_SendFailed"] = 30,
    ["NewChat_VoiceMessage"] = 30,
    ["NewChat_NoInput"] = 30,
    ["NewChat_SendCD"] = 30,
    ["NewChat_NotInGroup"] = 30,
    ["NewChat_All"] = 30,
    ["NewChat_World"] = 30,
    ["NewChat_TotalPeople"] = 30,
    ["NewChat_NewStar"] = 30,
    ["NewChat_Friend"] = 30,
    ["NewChat_Lobby"] = 30,
    ["NewChat_ChatRoom"] = 30,
    ["NewChat_Team"] = 30,
    ["NewChat_AIChat"] = 30,
    ["NewChat_ChatHourse"] = 30,
    ["NewChat_NowRecording"] = 30,
    ["NewChat_MicroOpenFailed"] = 30,
    ["NewChat_MySelf"] = 30,
    ["NewChat_EnterTeam"] = 30,
    ["NewChat_LeaveTeam"] = 30,
    ["NewChat_ReachMaxInput"] = 30,
    ["NewChat_NoMicroPermission"] = 30,
    ["NewChat_ChoseFriend"] = 30,
    ["NewChat_NoFriend"] = 30,
    ["NewChat_NoMessage"] = 30,
    ["NewChat_NotReadPlus"] = 30,
    ["NewChat_NotRead"] = 30,
    ["NewChat_CanSendAfter"] = 30,
    ["NewChat_InputMessage"] = 30,
    ["NewChat_IsUploading"] = 30,
    ["NewChat_CantUseVoice"] = 30,
    ["NewChat_NotKnowPlayer"] = 30,
    ["NewChat_MinuteBefore"] = 30,
    ["NewChat_HourBefore"] = 30,
    ["NewChat_DayBefore"] = 30,
    ["NewChat_MonthBefore"] = 30,
    ["NewChat_SendMyPosition"] = 30,
    ["NewChat_VoiceSendFailed"] = 30,
    ["NewChat_PositionInfoInvalied"] = 30,
    ["NewChat_TeamEnterTeam"] = 30,
    ["NewChat_TeamLeaveTeam"] = 30,
    ["NewChat_TeamMySelf"] = 30,
    ["NewChat_VoicePlayStart"] = 30,
    ["NewChat_VoiceDownloadFailed"] = 30,
    ["NewChat_VoicePlayEnd"] = 30,
    ["NewChat_StopPlayVoice"] = 30,
    ["NewChat_TimeShow"] = 30,
    ["NewChat_TeamDecorator"] = 30,
    ["NewChat_QuickChat"] = 30,
    ["NewChat_Chat"] = 30,
    ["NewChat_NoChatContent"] = 30,
    ["NewChat_VoiceToWordFailed"] = 30,
    ["NewChat_SayHiChat"] = 30,
    ["Common_Tips_MoreApply"] = 30,
    ["InLevel_OpenMonsterArea"] = 30,
    ["InLevel_ShootGhost"] = 30,
    ["InLevel_AsHero"] = 30,
    ["InLevel_HeroAppear"] = 30,
    ["InLevel_SteelAppear"] = 30,
    ["InLevel_AsSteel"] = 30,
    ["InLevel_Weapon_MachineGun"] = 30,
    ["InLevel_Weapon_M249Grenade"] = 30,
    ["InLevel_Weapon_M4A1"] = 30,
    ["InLevel_Weapon_Bazooka"] = 30,
    ["InLevel_Weapon_SniperGun"] = 30,
    ["InLevel_Weapon_ShotGun"] = 30,
    ["InLevel_ButtleIsMax"] = 30,
    ["InLevel_Reloading"] = 30,
    ["InLevel_GetButtle"] = 30,
    ["InLevel_SkillName_Rampage"] = 30,
    ["InLevel_SkillName_Shield"] = 30,
    ["InLevel_SkillName_SteelRampage"] = 30,
    ["InLevel_SkillName_Grenade"] = 30,
    ["InLevel_Hero_TitleCenter"] = 30,
    ["InLevel_StarBaby_TitleCenter"] = 30,
    ["InLevel_GameResult_Win"] = 30,
    ["InLevel_GameResult_Loser"] = 30,
    ["InLevel_GameResult_Draw"] = 30,
    ["InLevel_Weapon_TargetTips"] = 30,
    ["InLevel_RankNoun"] = 30,
    ["InLevel_Defeat_OneParam"] = 30,
    ["InLevel_GunGame_FefeatFrist"] = 30,
    ["InLevel_Common_Defeat"] = 30,
    ["InLevel_Common_TakeDown"] = 30,
    ["InLevel_Common_Integral"] = 31,
    ["InLevel_Common_Infect"] = 31,
    ["InLevel_Common_Eliminate"] = 31,
    ["InLevel_Common_NotChangeView"] = 31,
    ["Common_Min_NoParam"] = 31,
    ["Common_Count"] = 31,
    ["Common_NoVoiceAuthorit"] = 31,
    ["Common_GameOver_Time"] = 31,
    ["Common_GameStart_SecOnrParam"] = 31,
    ["Common_RankDesc"] = 31,
    ["Common_Infect"] = 31,
    ["Common_Score"] = 31,
    ["Common_WipeOut"] = 31,
    ["Common_FallDown"] = 31,
    ["Common_Beat"] = 31,
    ["UI_Recharge_ReceiveRightNow"] = 31,
    ["Recharge_HowManyDays"] = 31,
    ["Activity_Monday"] = 31,
    ["Activity_Tuesday"] = 31,
    ["Activity_Wednesday"] = 31,
    ["Activity_Thursday"] = 31,
    ["Activity_Friday"] = 31,
    ["Activity_Saturday"] = 31,
    ["Activity_Sunday"] = 31,
    ["DDP_InLevelTips1"] = 31,
    ["DDP_InLevelTips2"] = 31,
    ["DDP_InLevelTips3"] = 31,
    ["DDP_InLevelTips4"] = 31,
    ["DDP_InLevelTips5"] = 31,
    ["DDP_InLevelTips6"] = 31,
    ["DDP_InLevelTips_Default"] = 31,
    ["InLevel_ResultTip1"] = 31,
    ["InLevel_ResultTip2"] = 31,
    ["InLevel_ResultTip3"] = 31,
    ["InLevel_ResultTip4"] = 31,
    ["Login_BackToHome"] = 31,
    ["UGC_ErrorMsg_NoSwitchArea"] = 31,
    ["UGC_ErrorMsg_SwitchAreaSelf"] = 31,
    ["UGC_ErrorMsg_NoMovable"] = 31,
    ["UGC_ErrorMsg_NoCanSwitch"] = 31,
    ["UGC_ErrorMsg_IsMySlaver"] = 31,
    ["Achievement_FilterMode_ToStr1"] = 31,
    ["Achievement_FilterMode_ToStr2"] = 31,
    ["Achievement_FilterMode_ToStr3"] = 31,
    ["Achievement_FilterMode_ToStr4"] = 31,
    ["Achievement_FilterMode_ToStr5"] = 31,
    ["Achievement_NoRecord"] = 31,
    ["Team_InMatching"] = 31,
    ["Common_FuncNoOpen"] = 31,
    ["Common_FuncNoOpen2"] = 31,
    ["Common_UnlockSystemByLevel"] = 31,
    ["Activity_TheFirstWeek"] = 31,
    ["Activity_TheSecondWeek"] = 31,
    ["Activity_TheThirdWeek"] = 31,
    ["Activity_TheFourthWeek"] = 31,
    ["Activity_FifthWeek"] = 31,
    ["Activity_SixthWeek"] = 31,
    ["Activity_SeventhWeek"] = 31,
    ["Report_ReportRequestFailed"] = 31,
    ["Report_ScreenshotUploadAndTry"] = 31,
    ["Questionnaire_HasExpired"] = 31,
    ["Common_FunctionClosed"] = 31,
    ["Common_DS_Exception"] = 31,
    ["Common_CanNotDoAction"] = 31,
    ["Player_InviteDoAction"] = 31,
    ["Player_PlayerCancelDoAction"] = 31,
    ["Player_PlayerRejectYourAction"] = 31,
    ["Player_InteractiveActionStop"] = 31,
    ["CommunityClient_ResReload"] = 31,
    ["UGC_Map_Drafts"] = 31,
    ["UGC_Map_Release"] = 31,
    ["UGC_Map_MapRelease"] = 31,
    ["UGC_Map_LocalMap"] = 31,
    ["UGC_Map_Recycle"] = 31,
    ["UGC_CanNotDoThis"] = 31,
    ["UGC_Map_DownloadMapFail"] = 31,
    ["UGC_Map_TryLater"] = 31,
    ["UGC_Map_IsDeleteMap"] = 31,
    ["UGC_Map_IsCopyMap"] = 31,
    ["UGC_Map_IsRecoverMap"] = 31,
    ["UGC_Map_IsRemoveMap"] = 31,
    ["Common_RuleDesc"] = 31,
    ["PlayerInfo_UGCInfo_Subscribe"] = 31,
    ["PlayerInfo_UGCInfo_Publish"] = 31,
    ["PlayerInfo_UGCInfo_Collect"] = 31,
    ["PlayerInfo_UGCInfo_History"] = 31,
    ["PlayerInfo_UGCInfo_PublicTime"] = 31,
    ["PlayerInfo_UGCInfo_GetMapFail"] = 31,
    ["PlayerInfo_UGCInfo_GetCollectMapFail"] = 31,
    ["PlayerInfo_UGCInfo_NoSubscribe"] = 31,
    ["PlayerInfo_UGCInfo_GoSubscribe"] = 31,
    ["PlayerInfo_UGCInfo_NoMap"] = 31,
    ["PlayerInfo_UGCInfo_GoMadeMap"] = 31,
    ["PlayerInfo_UGCInfo_NoCollect"] = 31,
    ["PlayerInfo_UGCInfo_NoHistory"] = 31,
    ["UGC_ErrorMsg_OnlyGeneralSwitch"] = 31,
    ["UGC_ErrorMsg_SwitchAreaIsMy"] = 31,
    ["Common_UndefineContent"] = 31,
    ["UGC_Program_SelectTemplate"] = 31,
    ["UGC_Program_ClickToSelectTemplate"] = 31,
    ["Common_ResLoadComplete"] = 32,
    ["Activity_FriendShip_InviteTitle"] = 32,
    ["UGC_Map_MainLayer"] = 32,
    ["UGC_Map_ExitEdit"] = 32,
    ["UGC_Map_isExitEdit"] = 32,
    ["UGC_Editor_TabFunc"] = 32,
    ["UGC_Editor_PutStartPoint"] = 32,
    ["UGC_Map_PlayerMap"] = 32,
    ["UGC_Editor_MapList"] = 32,
    ["UGC_Editor_GameCorridor"] = 32,
    ["UGC_Editor_DailyLevel"] = 32,
    ["UGC_Photo_CreateGroup"] = 32,
    ["UGC_Editor_NoRoomCanEnter"] = 32,
    ["UGC_Editor_RoomDissolve"] = 32,
    ["UGC_Editor_GetKeyFail"] = 32,
    ["UGC_Editor_ReadyStartFail"] = 32,
    ["UGC_Editor_PreLoadMapFail"] = 32,
    ["UGC_Editor_Sort_Recommand"] = 32,
    ["UGC_Editor_Sort_Latest"] = 32,
    ["UGC_Editor_Sort_GiveLike"] = 32,
    ["UGC_Editor_CanNotUse"] = 32,
    ["UGC_Editor_UploadPicFail"] = 32,
    ["UGC_Editor_ReportStartFail"] = 32,
    ["UGC_Editor_MapNoPublish"] = 32,
    ["UGC_Editor_MapNoExist"] = 32,
    ["UGC_Editor_GroupMaxSize"] = 32,
    ["UGC_Editor_CanNotToBind"] = 32,
    ["UGC_Editor_CleanSuccess"] = 32,
    ["UGC_AI_IsRequesting"] = 32,
    ["UGC_AI_NoColorable"] = 32,
    ["UGC_AI_NetError"] = 32,
    ["UGC_AI_ServerBusy"] = 32,
    ["UGC_AI_CoolDown"] = 32,
    ["UGC_AI_GenerateFail"] = 32,
    ["UGC_Editor_DownloadGroupFail"] = 32,
    ["UGC_Editor_GroupDataLoadFail"] = 32,
    ["UGC_Editor_AddLocomotorFail"] = 32,
    ["UGC_Editor_MaxLocomotor"] = 32,
    ["UGC_Editor_LocomotorCell"] = 32,
    ["UGC_Editor_Creator_Custom"] = 32,
    ["UGC_Editor_Creator_OnewayMove"] = 32,
    ["UGC_Editor_Creator_LoopAndBackMove"] = 32,
    ["UGC_Editor_Creator_SimpleRotation"] = 32,
    ["UGC_Editor_Creator_Swing"] = 32,
    ["UGC_Editor_Creator_WayPoint"] = 32,
    ["UGC_Editor_Creator_OnewayScale"] = 32,
    ["UGC_Editor_SimulateColor"] = 32,
    ["UGC_Editor_AddSimulateColorFail"] = 32,
    ["UGC_Editor_AddSimulateColorFail2"] = 32,
    ["UGC_Editor_Palette_Collection"] = 32,
    ["UGC_Editor_Musical_GuZheng"] = 32,
    ["UGC_Editor_Musical_PiPa"] = 32,
    ["UGC_Editor_Musical_BianZhong"] = 32,
    ["UGC_Editor_Musical_ElecVocal"] = 32,
    ["UGC_Editor_Musical_ElecGuitar"] = 32,
    ["UGC_Editor_Musical_Saw"] = 32,
    ["UGC_Editor_Musical_Square"] = 32,
    ["UGC_Editor_Musical_YangQin"] = 32,
    ["UGC_Editor_Musical_ElecPiano"] = 32,
    ["UGC_Editor_Musical_Lead"] = 32,
    ["UGC_Editor_Musical_Piano"] = 32,
    ["UGC_Editor_MusicGroup_Little"] = 32,
    ["UGC_Editor_MusicGroup_LittleOne"] = 32,
    ["UGC_Editor_MusicGroup_LittleTwo"] = 32,
    ["UGC_Editor_MusicGroup_LittleThree"] = 32,
    ["UGC_Editor_MusicGroup_Big"] = 32,
    ["UGC_Editor_MusicGroup_BigOne"] = 32,
    ["UGC_Map_Upload_Fail"] = 32,
    ["UGC_Map_Save_UpLoad_Fail"] = 32,
    ["UGC_LocomotorName"] = 32,
    ["UGC_BindingLogic_Success"] = 32,
    ["UGC_BindingLogic_Fail"] = 32,
    ["UGC_OperateBind_Error201"] = 32,
    ["UGC_OperateBind_Error202"] = 32,
    ["UGC_OperateBind_CanNotSelectSelf"] = 32,
    ["UGC_CanNot_JoinTemplate"] = 32,
    ["UGC_CanNot_JoinGroup"] = 32,
    ["UGC_CanNot_Unique"] = 32,
    ["UGC_BindingMovement_Success"] = 32,
    ["UGC_Skybox_Name_1"] = 32,
    ["UGC_Skybox_Name_2"] = 32,
    ["UGC_Skybox_Name_3"] = 32,
    ["UGC_Skybox_Name_4"] = 32,
    ["UGC_Skybox_Name_5"] = 32,
    ["UGC_Skybox_Name_6"] = 32,
    ["UGC_Skybox_Name_7"] = 32,
    ["UGC_Skybox_Name_8"] = 32,
    ["UGC_Skybox_Name_9"] = 32,
    ["UGC_Skybox_Name_10"] = 32,
    ["UGC_Skybox_Name_11"] = 32,
    ["UGC_Skybox_Name_12"] = 32,
    ["UGC_Skybox_Name_13"] = 32,
    ["UGC_Skybox_Name_14"] = 32,
    ["UGC_Skybox_Name_15"] = 32,
    ["UGC_Skybox_Name_16"] = 32,
    ["UGC_Skybox_Name_17"] = 32,
    ["UGC_Skybox_Name_18"] = 32,
    ["UGC_Skybox_Name_19"] = 32,
    ["UGC_Skybox_Name_20"] = 32,
    ["UGC_Skybox_Name_21"] = 32,
    ["UGC_Skybox_Name_22"] = 33,
    ["UGC_Skybox_Name_23"] = 33,
    ["UGC_Skybox_Name_24"] = 33,
    ["UGC_Skybox_Name_25"] = 33,
    ["UGC_Skybox_Name_26"] = 33,
    ["UGC_Skybox_Name_27"] = 33,
    ["UGC_Skybox_Name_28"] = 33,
    ["UGC_Skybox_Name_29"] = 33,
    ["UGC_Skybox_Name_30"] = 33,
    ["UGC_Skybox_Name_31"] = 33,
    ["UGC_Skybox_Name_32"] = 33,
    ["UGC_Skybox_Name_33"] = 33,
    ["UGC_Skybox_Name_34"] = 33,
    ["UGC_Skybox_Name_35"] = 33,
    ["UGC_Skybox_Name_36"] = 33,
    ["UGC_Skybox_Name_37"] = 33,
    ["UGC_Skybox_Name_38"] = 33,
    ["UGC_Skybox_Name_39"] = 33,
    ["UGC_Skybox_Name_40"] = 33,
    ["UGC_Skybox_Name_41"] = 33,
    ["UGC_Skybox_Name_42"] = 33,
    ["UGC_Skybox_Name_43"] = 33,
    ["UGC_Skybox_Name_44"] = 33,
    ["UGC_Skybox_Name_45"] = 33,
    ["UGC_Skybox_Name_46"] = 33,
    ["UGC_Skybox_Name_47"] = 33,
    ["UGC_Skybox_Name_48"] = 33,
    ["UGC_Skybox_Name_49"] = 33,
    ["UGC_Skybox_Name_50"] = 33,
    ["UGC_Skybox_Name_51"] = 33,
    ["UGC_Skybox_Name_52"] = 33,
    ["UGC_Skybox_Name_53"] = 33,
    ["UGC_Skybox_Name_54"] = 33,
    ["UGC_Skybox_Name_55"] = 33,
    ["UGC_Skybox_Name_56"] = 33,
    ["UGC_Skybox_Name_57"] = 33,
    ["UGC_Skybox_Name_58"] = 33,
    ["UGC_Skybox_Name_59"] = 33,
    ["UGC_Skybox_Name_60"] = 33,
    ["UGC_Skybox_Name_61"] = 33,
    ["UGC_Skybox_Name_62"] = 33,
    ["UGC_Skybox_Name_63"] = 33,
    ["UGC_Skybox_Name_64"] = 33,
    ["UGC_Skybox_Name_65"] = 33,
    ["UGC_Skybox_Name_66"] = 33,
    ["UGC_Skybox_Name_67"] = 33,
    ["UGC_Skybox_Name_68"] = 33,
    ["UGC_Skybox_Name_69"] = 33,
    ["UGC_Skybox_Name_70"] = 33,
    ["UGC_Skybox_Name_71"] = 33,
    ["UGC_Skybox_Name_72"] = 33,
    ["UGC_Skybox_Name_73"] = 33,
    ["UGC_Skybox_Name_74"] = 33,
    ["UGC_Skybox_Name_75"] = 33,
    ["UGC_Skybox_Name_76"] = 33,
    ["UGC_Skybox_Name_77"] = 33,
    ["UGC_Skybox_Name_78"] = 33,
    ["UGC_Skybox_Name_79"] = 33,
    ["UGC_Effect_Name_1"] = 33,
    ["UGC_Effect_Name_2"] = 33,
    ["UGC_Skybox_Filter_Name_0"] = 33,
    ["UGC_Skybox_Filter_Name_1"] = 33,
    ["UGC_Skybox_Filter_Name_2"] = 33,
    ["UGC_Skybox_Filter_Name_3"] = 33,
    ["UGC_Skybox_Filter_Name_4"] = 33,
    ["UGC_Skybox_Filter_Name_5"] = 33,
    ["UGC_Skybox_Filter_Name_6"] = 33,
    ["UGC_Skybox_Filter_Name_7"] = 33,
    ["UGC_Skybox_Filter_Name_8"] = 33,
    ["UGC_Skybox_Filter_Name_9"] = 33,
    ["UGC_Skybox_Filter_Name_10"] = 33,
    ["UGC_Skybox_Filter_Name_11"] = 33,
    ["UGC_Skybox_Filter_Name_12"] = 33,
    ["UGC_Skybox_Filter_Name_13"] = 33,
    ["UGC_Skybox_Filter_Name_14"] = 33,
    ["UGC_Skybox_Filter_Name_15"] = 33,
    ["UGC_Skybox_Filter_Name_16"] = 33,
    ["UGC_Skybox_Filter_Name_17"] = 33,
    ["UGC_Skybox_Filter_Name_18"] = 33,
    ["UGC_Skybox_Filter_Name_19"] = 33,
    ["UGC_Skybox_Filter_Name_20"] = 33,
    ["UGC_Skybox_Filter_Name_21"] = 33,
    ["UGC_Skybox_Filter_Name_22"] = 33,
    ["UGC_Skybox_Filter_Name_23"] = 33,
    ["UGC_GroupPublish_NoticeCheck"] = 33,
    ["UGC_GroupPublish_Notice"] = 33,
    ["UGC_BindingMovement_Fail"] = 33,
    ["Team_NoLimit"] = 33,
    ["Team_PublicRecruit"] = 33,
    ["Team_RefreshInCD"] = 33,
    ["Team_CanNotJoinSelfTeam"] = 33,
    ["Team_SelfInTeam"] = 33,
    ["Team_InviteAddFriend"] = 33,
    ["Team_AlreadyAddFriend"] = 33,
    ["Team_ChangeLeader"] = 33,
    ["Team_LeaveTeam"] = 33,
    ["Team_CanNotDisbandTeam"] = 33,
    ["Team_ConfirmDisband"] = 33,
    ["Common_Invincible"] = 33,
    ["Set_FuncNoDevelop"] = 33,
    ["Set_isUnbind"] = 34,
    ["Set_Unbind"] = 34,
    ["Set_CostChangeCardTip"] = 34,
    ["Set_ChangeNameSuccess"] = 34,
    ["Set_NoChangeCard"] = 34,
    ["Lobby_CanNotDoAction"] = 34,
    ["Lobby_CanNotDoDoubleAction"] = 34,
    ["Lobby_PleaseSelectPlayer"] = 34,
    ["Lobby_StatusCanNotDoAction"] = 34,
    ["Lobby_StatusCanNotUseItem"] = 34,
    ["Lobby_Piano_Play"] = 34,
    ["Lobby_Piano_Listen"] = 34,
    ["Set_EditKey_Classic"] = 34,
    ["Set_EditKey_DaLuanDou"] = 34,
    ["Set_EditKey_Arena"] = 34,
    ["Set_EditKey_RoundGameRunning"] = 34,
    ["Set_EditKey_RoundGameFPS"] = 34,
    ["Set_EditKey_SaveKeySuccess"] = 34,
    ["Set_EditKey_SaveKeyFail"] = 34,
    ["Set_EditKey_isSaveCurrentKey"] = 34,
    ["Set_EditKey_isSaveCurrentKeyToExit"] = 34,
    ["Set_EditKey_ResetToDefault"] = 34,
    ["Level_BioChaseFeedback_Name1"] = 34,
    ["Level_BioChaseFeedback_Name2"] = 34,
    ["Level_BioChaseFeedback_Name3"] = 34,
    ["Level_BioChaseFeedback_Name4"] = 34,
    ["Level_BioChaseFeedback_Name5"] = 34,
    ["Level_BioChaseFeedback_Name6"] = 34,
    ["Level_BioChaseFeedback_Name7"] = 34,
    ["Level_GoalText_CanePromoted"] = 34,
    ["Level_GoalText_Out"] = 34,
    ["Level_GoalText_OutTeam"] = 34,
    ["Level_GoalText_Promoted"] = 34,
    ["Level_GoalText_UgcPlay"] = 34,
    ["Lobby_SceneConnectFail"] = 34,
    ["Lobby_ClickToEnterLobby"] = 34,
    ["Plaza_ReconnectInPlaza"] = 34,
    ["Plaza_ReconnectInPlazaSuccess"] = 34,
    ["Plaza_ReconnectInPlazaFail"] = 34,
    ["Item_PickUpWithCount"] = 34,
    ["Item_RandomGet"] = 34,
    ["Item_MustGet"] = 34,
    ["Item_ConfirmDeleteItem"] = 34,
    ["Item_SortQuality"] = 34,
    ["Item_SortTime"] = 34,
    ["Item_MaxRewardCount"] = 34,
    ["Item_ItemNotEnough"] = 34,
    ["Item_GiveFriendItem"] = 34,
    ["Bag_IsSaveSlot"] = 34,
    ["Item_ItemEmpty"] = 34,
    ["Bag_NeedUnlockSkinToSave"] = 34,
    ["Report_MostSelect"] = 34,
    ["Report_EnterReason_UpTo60Words"] = 34,
    ["Report_ReportSuccessful"] = 34,
    ["Report_EnterUpTo60Words"] = 34,
    ["Report_SelectReportReason"] = 34,
    ["Activity_StarRecruitmentInvitation"] = 34,
    ["Activity_ReachedLimit_AndChooseOther"] = 34,
    ["Activity_ChapterNotOpened"] = 34,
    ["Activity_RemianTime"] = 34,
    ["Activity_IsJoinTheTeam"] = 34,
    ["Activity_RecruitmentList"] = 34,
    ["Activity_InviteFriendsLevelToComplete"] = 34,
    ["Activity_InformYourFriends"] = 34,
    ["Activity_SuccessfullyRecruited"] = 34,
    ["Activity_CurrentLevel"] = 34,
    ["Activity_SuccessfullyRecruitedCount"] = 34,
    ["Activity_InviteFriendsReachLevel"] = 34,
    ["Activity_GetNewbieSuit"] = 34,
    ["Activity_SuccessfullyInvitedCount"] = 34,
    ["Activity_InviteFriendsToComplete"] = 34,
    ["Activity_GoalHasCompleted"] = 34,
    ["Battle_PoisonWillTrigger"] = 34,
    ["Level_DDP_ExitRagdoll"] = 34,
    ["Level_DDP_InRagdoll"] = 34,
    ["Level_BioChase_Ghost"] = 34,
    ["Level_BioChase_ShotGhost"] = 34,
    ["Level_BioChase_StarBaby"] = 34,
    ["Level_BioChase_GhostTips_Chase"] = 34,
    ["Level_BioChase_GhostTips_Fight"] = 34,
    ["Level_BioChase_HumanTips_Chase"] = 34,
    ["Level_BioChase_HumanTips_Fight"] = 34,
    ["Level_GhostCatch_PlayerTips"] = 34,
    ["Level_GhostCatch_GhostTips"] = 34,
    ["InLevel_GameOver"] = 34,
    ["Bag_NoSearchResult"] = 34,
    ["Friend_NoFriend"] = 34,
    ["Common_UnlockByLevel"] = 34,
    ["Bag_OpenVipToUnlock"] = 34,
    ["Bag_SlotLock"] = 34,
    ["InLevel_SkillName_Attack"] = 34,
    ["InLevel_RoleName_Killer"] = 34,
    ["InLevel_RoleName_Guard"] = 34,
    ["InLevel_RoleName_Doll"] = 34,
    ["InLevel_GameRule_Killer"] = 34,
    ["InLevel_GameRule_Guard"] = 34,
    ["InLevel_GameRule_Doll"] = 34,
    ["InLevel_Common_GameBeginSec_OneParam"] = 34,
    ["InLevel_GameRule_Killer1"] = 34,
    ["InLevel_GameRule_Guard1"] = 34,
    ["InLevel_GameRule_Doll1"] = 35,
    ["InLevel_RoleTips_Doll"] = 35,
    ["InLevel_RoleTips_Killer"] = 35,
    ["InLevel_RoleTips_Guard"] = 35,
    ["InLevel_RoleRule_Guard"] = 35,
    ["InLevel_RoleRule_Killer"] = 35,
    ["InLevel_Common_MyScoreTips"] = 35,
    ["InLevel_Common_RoleRankTips"] = 35,
    ["InLevel_RoleRank_Param"] = 35,
    ["InLevel_RoleRule_Killer1"] = 35,
    ["InLevel_RoleRule_Guard1"] = 35,
    ["InLevel_Common_Out"] = 35,
    ["InLevel_Common_Live"] = 35,
    ["InLevel_GameRule_Guard2"] = 35,
    ["InLevel_GameRule_Doll2"] = 35,
    ["InLevel_GameRule_Killer2"] = 35,
    ["InLevel_GameRule_Guard3"] = 35,
    ["InLevel_GameRule_Doll3"] = 35,
    ["InLevel_GameRule_Tips"] = 35,
    ["InLevel_GameRule_Guard4"] = 35,
    ["InLevel_GameRule_Tips1"] = 35,
    ["InLevel_GameRule_Tips2"] = 35,
    ["InLevel_GameRule_Tips3"] = 35,
    ["InLevel_GameRule_Guard5"] = 35,
    ["Common_Report"] = 35,
    ["InLevel_RoleRule_Killer3"] = 35,
    ["InLevel_RoleRule_Killer4"] = 35,
    ["InLevel_RoleRule_Killer5"] = 35,
    ["InLevel_GameRule_GameOverTips"] = 35,
    ["InLevel_MapTips_AttackSuccess"] = 35,
    ["InLevel_MapTips_AttackFail"] = 35,
    ["InLevel_Common_Camp"] = 35,
    ["Common_ShareSuccess"] = 35,
    ["Common_ShareFail"] = 35,
    ["Common_SaveSuccess"] = 35,
    ["Common_SaveFail"] = 35,
    ["DDP_SeparatedComa"] = 35,
    ["DDP_InComa"] = 35,
    ["DDP_InLevel_BackToCommunityTip"] = 35,
    ["DDP_InLevel_BackToCommunityTipBeWinner"] = 35,
    ["DDP_InLevel_ChampionTip"] = 35,
    ["DDP_InLevel_PoisonCircleRefreshTip"] = 35,
    ["Common_StartTip"] = 35,
    ["DDP_InLevel_TimeFormat"] = 35,
    ["Common_DontAddFriendAgain"] = 35,
    ["Common_DontKnownWinOrDefeat"] = 35,
    ["Common_YouArePass"] = 35,
    ["Common_YouAreOut"] = 35,
    ["Common_YourTeamIsOut"] = 35,
    ["Net_ReconnectToLevelFail"] = 35,
    ["Net_SecondsLaterTry"] = 35,
    ["Common_ExchangeTargetInfo"] = 35,
    ["Level_BioChase_PreKillZone"] = 35,
    ["Level_BioChase_OpenReady"] = 35,
    ["InLevel_PlayerNotInWhiteList"] = 35,
    ["DDP_InLevel_BackTimeFormat"] = 35,
    ["DDP_InLevel_OneIsOutTip"] = 35,
    ["DDP_InLevel_PoisonCircleBecomeSmaller"] = 35,
    ["Common_OutGameTip"] = 35,
    ["Common_BackToCommunityTip"] = 35,
    ["Common_BackToCommunityTipBeWinner"] = 35,
    ["Common_BackToMatchRoomTip"] = 35,
    ["TDM_Park_CreateMapTabName"] = 35,
    ["TDM_CreateMap_TabName"] = 35,
    ["TDM_MapList_DraftsTabName"] = 35,
    ["TDM_MapList_ReleaseTabName"] = 35,
    ["TDM_GameCorridor_MapSearchTabName"] = 35,
    ["TDM_GameCorridor_FastRoomListTabName"] = 35,
    ["TDM_FastRoomList_RefreshTabName"] = 35,
    ["TDM_FastRoomList_FastRoomItemTabName"] = 35,
    ["TDM_FastRoomList_FastJoinTabName"] = 35,
    ["TDM_RoomList_RefreshTabName"] = 35,
    ["TDM_RoomList_RoomItemTabName"] = 35,
    ["TDM_RoomList_RandomJoinTabName"] = 35,
    ["TDM_RoomList_CreateRoomTabName"] = 35,
    ["TDM_PlayerUgcInfo_UgcInfoTabTabName"] = 35,
    ["TDM_PlayerUgcInfo_UgcInfoToSubTabName"] = 35,
    ["TDM_PlayerUgcInfo_UgcInfoToMakeTabName"] = 35,
    ["GameCorridor_MST_Recommend"] = 35,
    ["GameCorridor_MST_Latest"] = 35,
    ["GameCorridor_MST_Popularity"] = 35,
    ["GameCorridor_MST_LastSeasonHot"] = 35,
    ["GameCorridor_MST_AllTimeHot"] = 35,
    ["GameCorridor_MST_Collect"] = 35,
    ["GameCorridor_MST_Recommend_Members"] = 64,
    ["GameCorridor_MST_Recommend_Official"] = 64,
    ["InLevel_ChangeLevel_Cruise"] = 35,
    ["Common_Tips_OpenVoiceChat"] = 35,
    ["Common_Tips_OPenTeamVoice"] = 35,
    ["Common_Tips_OPenTeamVoice1"] = 35,
    ["Common_GameShow_CurrGame"] = 35,
    ["InLevel_RankNo_GradeYellow24"] = 35,
    ["InLevel_Compliance"] = 35,
    ["InLevel_IsQuitCurrLevel"] = 35,
    ["InLevel_PlayerState_Promoted"] = 35,
    ["InLevel_PlayerState_Out"] = 35,
    ["InLevel_PlayerState_Passed"] = 35,
    ["InLevel_TeamState_Out"] = 35,
    ["InLevel_CommonState_Outcome"] = 35,
    ["InLevel_CommonsState_TeamOut"] = 35,
    ["InLevel_CommonsState_PlayerOut"] = 35,
    ["Common_LevelType_Speed"] = 35,
    ["Common_LevelInfo"] = 36,
    ["Common_RushForward"] = 36,
    ["Common_Begin"] = 36,
    ["Friend_MyIntimacy"] = 36,
    ["Friend_BuildIntimacy"] = 36,
    ["Friend_ApplyIntimacy"] = 36,
    ["Friend_ChangeIntimacy"] = 36,
    ["Friend_BreakIntimacy"] = 36,
    ["Friend_BreakIntimacyTip"] = 36,
    ["RealTimeIntervene_Title"] = 36,
    ["RealTimeIntervene_Play"] = 36,
    ["Player_DefaultName"] = 36,
    ["Player_Mine"] = 36,
    ["MessageBoard_Newest"] = 36,
    ["MessageBoard_Hotest"] = 36,
    ["MessageBoard_Mine"] = 36,
    ["MessageBoard_PlayerHideProfileTip"] = 36,
    ["Common_UserRuleTitle"] = 36,
    ["Common_PrivacyRuleTitle"] = 36,
    ["Common_PleaseConfirmRuleTip"] = 36,
    ["Common_PleaseConfirmRuleTip2"] = 36,
    ["Common_Agree"] = 36,
    ["Common_ChangeLanguageTip"] = 36,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat"] = 36,
    ["Common_CantBuyTip"] = 36,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat2"] = 36,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat3"] = 36,
    ["Common_ShareLinkTitle"] = 36,
    ["Common_ShareLinkDesc"] = 36,
    ["Common_ShareTitle"] = 36,
    ["Common_ShareDesc"] = 36,
    ["Common_ShareMusicTitle"] = 36,
    ["Common_ShareMusicDesc"] = 36,
    ["Common_InviteTitle"] = 36,
    ["Common_InviteDesc"] = 36,
    ["Common_ArkTitle"] = 36,
    ["Common_ArkDesc"] = 36,
    ["Mall_CurrentIsMax"] = 36,
    ["Team_PST_Offline"] = 36,
    ["Team_PST_Online"] = 36,
    ["Team_PST_Lobby"] = 36,
    ["Team_PST_Battle"] = 36,
    ["Team_PST_Matching"] = 36,
    ["Team_PST_Team"] = 36,
    ["Team_PST_TeamReady"] = 36,
    ["Team_PST_TeamMatching"] = 36,
    ["Team_PST_TeamMatching_Leave"] = 36,
    ["PlayerInfo_PLC_Unknow"] = 36,
    ["PlayerInfo_PLC_Mood"] = 36,
    ["PlayerInfo_PLC_Constellation"] = 36,
    ["PlayerInfo_PLC_Hobby"] = 36,
    ["PlayerInfo_PLC_State"] = 36,
    ["Activity_CanIncrease_AndReceiveMost"] = 36,
    ["PlayerInfo_BaseInfo_HasCopyNickName"] = 36,
    ["UI_Recharge_SeasonRecharge_XingxiuNotEnough"] = 36,
    ["Common_PayCancel"] = 36,
    ["Player_InteractiveOverMaxDistance"] = 36,
    ["Player_InteractiveWaitAccept"] = 36,
    ["Player_InteractiveInviteeLogout"] = 36,
    ["Currency_ReachedLimitCount"] = 36,
    ["PlayerInfo_UGCInfo_LikeCount"] = 36,
    ["PlayerInfo_UGCInfo_PlayCount"] = 36,
    ["PlayerInfo_UGCInfo_PlayTime"] = 36,
    ["Lobby_InviteDoAction"] = 36,
    ["Mall_IsGotoGet"] = 36,
    ["PlayerInfo_UGCInfo_OtherPublish"] = 36,
    ["PlayerInfo_UGCInfo_OtherCollect"] = 36,
    ["Grade_Task_Level"] = 36,
    ["Grade_Task_CurLevel"] = 36,
    ["FriendModel_ReplyNtf"] = 36,
    ["FriendModel_ReplyNtf_1"] = 36,
    ["FriendModel_RemoveFriend"] = 36,
    ["FriendModel_BanFriend"] = 36,
    ["FriendModel_RemoveBanFriend"] = 36,
    ["FriendModel_CheckFriendState"] = 36,
    ["FriendModel_CheckFriendState_1"] = 36,
    ["PlayerInfo_InitLevel"] = 36,
    ["Model_Single_TimeTips1"] = 36,
    ["Model_Single_TimeTips2"] = 36,
    ["Model_Single_TimeTips3"] = 36,
    ["Task_NewBiePage_Progress"] = 36,
    ["Setting_Lang"] = 36,
    ["PlayerInfo_AccountBinding"] = 36,
    ["UIInLevelFinishTip_Rank"] = 36,
    ["UIInLevelFinishTip_FriendRank"] = 36,
    ["Team_PlayerInfo"] = 36,
    ["InLevel_FinalAccount_CountDown"] = 36,
    ["PlayerInfo_ChangeState_UseTime"] = 36,
    ["NewChat_Main_ChatFriendName"] = 36,
    ["NewChat_Main_ChatFriendNameWithRemarkName"] = 36,
    ["CommonModel_NoGet"] = 36,
    ["PlayerModel_NoOperation"] = 36,
    ["Login_Agreement1"] = 36,
    ["Login_Agreement2"] = 36,
    ["Login_Agreement3"] = 36,
    ["Lobby_NotMeetFriendIntimate"] = 36,
    ["UI_Setting_Game_Title"] = 36,
    ["Common_NumberFormat"] = 36,
    ["InLevel_FPS_Auto"] = 36,
    ["InLevel_FPS_Manual"] = 36,
    ["InLevel_FPS_Shoot"] = 37,
    ["InLevel_FPS_Attack"] = 37,
    ["InLevel_FPS_SettingTip"] = 37,
    ["InLevel_FPS_SettingTip_1_3P"] = 37,
    ["InLevel_FPS_SettingTip_FireMode"] = 37,
    ["InLevel_FPS_ViewChange"] = 37,
    ["InLevel_FPS_RoguelikeDead"] = 37,
    ["UI_BrGame_HelpTips_Dying"] = 37,
    ["UI_BrGame_HelpTips_Heal"] = 37,
    ["UI_BrGame_HelpTips_Respawn"] = 37,
    ["UI_BrGame_HelpTips_Help"] = 37,
    ["UI_BrGame_HelpItem_Help"] = 37,
    ["UI_BrGame_HelpItem_Revive"] = 37,
    ["UI_BrGame_HelpItem_Out"] = 37,
    ["UI_BrGame_HelpState_Abandon_Tip"] = 37,
    ["UI_BrGame_HelpState_Abandon"] = 37,
    ["UI_BrGame_HelpState_Wait"] = 37,
    ["UI_BrGame_HelpState_Help"] = 37,
    ["UI_BrGame_HelpState_Revive"] = 37,
    ["UI_BrGame_TutorialTips_UseMedicine"] = 37,
    ["UI_BrGame_TutorialTips_SwitchWeapon"] = 37,
    ["UI_BrGame_DeadMoment_Desc"] = 37,
    ["UI_BrGame_DeadMoment_Watch"] = 37,
    ["UI_BrGame_RespawnTip_Close"] = 37,
    ["UI_BrGame_InPoisonCircle_Tip"] = 37,
    ["UI_BrGame_KillTip_KillTeam"] = 37,
    ["UI_BrGame_RulesTab_Parachuting"] = 37,
    ["UI_BrGame_RulesTab_Prop"] = 37,
    ["UI_BrGame_RulesTab_Electricity"] = 37,
    ["UI_BrGame_RulesTab_Help"] = 37,
    ["UI_BrGame_RulesTab_Bag"] = 37,
    ["UI_BrGame_RulesTab_Poison"] = 37,
    ["UI_BrGame_RulesTab_Victory"] = 37,
    ["UI_BrGame_RulesTab_Revive"] = 37,
    ["UI_DfGame_RulesTab_Ready"] = 37,
    ["UI_DfGame_RulesTab_Start"] = 37,
    ["UI_DfGame_RulesTab_Search"] = 37,
    ["UI_DfGame_RulesTab_Resurrection"] = 37,
    ["UI_DfGame_RulesTab_Boss"] = 37,
    ["UI_DfGame_RulesTab_Evacuate"] = 37,
    ["UI_DfGame_RulesTab_Reward"] = 37,
    ["UI_BrGame_KillInfoSelf_Kill"] = 37,
    ["UI_BrGame_KillInfoSelf_Dying"] = 37,
    ["UI_BrGame_TeamInfoItem_Revive"] = 37,
    ["UI_BrGame_TeamInfoItem_ReviveTime"] = 37,
    ["UI_BrGame_Weapon_NotEnough_Ammo"] = 37,
    ["UI_BrGame_Weapon_NotEnough_Ammo_Tip"] = 37,
    ["UI_BrGame_RankView_Title"] = 37,
    ["UI_BrGame_RankView_PlayerName"] = 37,
    ["UI_BrGame_RankView_KillNum"] = 37,
    ["UI_BrGame_RankView_HelpNum"] = 37,
    ["UI_BrGame_RankView_DamageNum"] = 37,
    ["UI_BrGame_RankView_LifeTime"] = 37,
    ["UI_FPSSetting_Common_Title"] = 37,
    ["UI_FPSSetting_Gun_Title"] = 37,
    ["UI_FPSSetting_BioChase_Title"] = 37,
    ["UI_FPSSetting_BR_Title"] = 37,
    ["UI_FPSSetting_RogueLike_Title"] = 37,
    ["UI_FPSSetting_CommonItem_Title"] = 37,
    ["UI_FPSSetting_PlayingItem_Title"] = 37,
    ["UI_FPSSetting_SniperItem_Title"] = 37,
    ["UI_FPSSetting_GyroscopeItem_Title"] = 37,
    ["UI_InLevel_Br_ArriveInfo_KillDes"] = 37,
    ["UI_InLevel_Br_ArriveInfo_AliveNum"] = 37,
    ["Login_FaceBook_CancelLogin"] = 37,
    ["Login_Google_LoginFailedCode1"] = 37,
    ["Login_Google_LoginFailedCode2"] = 37,
    ["Login_Google_LoginFailedCode3"] = 37,
    ["Login_Google_LoginFailedCode9"] = 37,
    ["Login_Google_LoginFailedCode18"] = 37,
    ["Mall_AvatarGiftHasExpire"] = 37,
    ["Chunk_Download_SpaceNotEnough"] = 37,
    ["Common_NotInstallQQTip"] = 37,
    ["Common_NotInstallWXTip"] = 37,
    ["TaskView_TimeUpdate"] = 37,
    ["NewChat_BlackFriend"] = 37,
    ["DDP_InLevel_StartTip"] = 37,
    ["Common_GetShareRewardSuccessTip"] = 37,
    ["UGC_GroupDelete_Title"] = 37,
    ["UGC_GroupDelete_Tips"] = 37,
    ["UGC_GroupUnShelve_Title"] = 37,
    ["UGC_GroupUnShelve_Tips"] = 37,
    ["FriendModel_RemoveIntimateFriend"] = 37,
    ["FriendModel_BanIntimateFriend"] = 37,
    ["Friend_LevelUp"] = 37,
    ["ScoringSystem_Submit"] = 37,
    ["ScoringSystem_Notice_One"] = 37,
    ["ScoringSystem_Notice_Two"] = 37,
    ["ScoringSystem_Simple"] = 37,
    ["ScoringSystem_Normal"] = 37,
    ["ScoringSystem_Difficulty"] = 37,
    ["ScoringSystem_SuperDifficult"] = 37,
    ["ScoringSystem_VeryPoor"] = 37,
    ["ScoringSystem_Dissatisfied"] = 37,
    ["ScoringSystem_Satisfy"] = 37,
    ["ScoringSystem_Awesome"] = 37,
    ["ScoringSystem_Ploy"] = 37,
    ["ScoringSystem_Difficulties"] = 37,
    ["ScoringSystem_Beautiful"] = 37,
    ["ScoringSystem_ordinary"] = 37,
    ["friend_ChangeRelationships"] = 38,
    ["CustomRoom_IsInTeamTip"] = 38,
    ["UgcRoom_ChangeMapSuc"] = 38,
    ["UgcRoom_LeaderChangedMap"] = 38,
    ["CustomRoom_InviteMsgTitle"] = 38,
    ["friend_InviteMsg"] = 38,
    ["Mall_CurrentHasCoinName"] = 38,
    ["UGC_Place_Occupy_OutOfRange"] = 38,
    ["UGC_Place_Available_OutOfRange"] = 38,
    ["UGC_ErrorMsg_TemplateCountOutOfRange"] = 38,
    ["UGC_Group_OutOfRange"] = 38,
    ["UGC_Map_Save_Success"] = 38,
    ["UGC_Map_Save_Fail"] = 38,
    ["UGC_Map_Save_Error"] = 38,
    ["UGC_Group_Name_Tips"] = 38,
    ["UGC_Popup_Rule_Title"] = 38,
    ["UGC_Popup_Rule_Content"] = 38,
    ["UGC_Group_DefaultName"] = 38,
    ["UGC_Copy_CountOutOfRange"] = 38,
    ["UGC_Copy_Lock"] = 38,
    ["UGC_Cannot_Group"] = 38,
    ["UGC_Group_Name_Check_Fail"] = 38,
    ["UGC_Net_Fail"] = 38,
    ["UGC_Group_Upload_Fail"] = 38,
    ["UGC_Group_Download_Fail"] = 38,
    ["UGC_Map_Save_LoadError"] = 38,
    ["UGC_Copy_Cannot_Copy"] = 38,
    ["UGC_CommunityGroup_Cannot_Edit"] = 38,
    ["UGC_CommunityGroup_Locked"] = 38,
    ["UGC_DailyLevel_Tips_1"] = 38,
    ["UGC_DailyLevel_Tips_2"] = 38,
    ["UGC_DailyLevel_Tips_3"] = 38,
    ["UGC_DailyLevel_Tips_4"] = 38,
    ["UGC_DailyLevel_Tips_5"] = 38,
    ["UGC_Input_Desc"] = 38,
    ["UGC_Output_Desc"] = 38,
    ["UGC_OutOf_Map_Range"] = 38,
    ["UGC_GroupObj_Cant_Bind"] = 38,
    ["UGC_GroupObj_Cant_Group"] = 38,
    ["UGC_Role_Pointer_Cant_Group"] = 38,
    ["UGC_Object_Cant_Bind"] = 38,
    ["UGC_Object_Cant_CycleBind"] = 38,
    ["UGC_Object_Cant_BindSelf"] = 38,
    ["UGC_Object_Cant_BindGroup"] = 38,
    ["UGC_BindLayer_OutOfRange"] = 38,
    ["UGC_Object_BindReachLimit"] = 38,
    ["UGC_Object_BindLimit0"] = 38,
    ["UGC_Object_Lock"] = 38,
    ["UGC_Object_Operating"] = 38,
    ["UGC_Rebirth_Mast_PlaceIn_Actor"] = 38,
    ["UGC_SpawnPoint_PlaceIn_Actor"] = 38,
    ["UGC_AtLess_One_RebirthPoint"] = 38,
    ["UGC_ToMuch_RebirthPoint"] = 38,
    ["UGC_AtLess_One_VectoryArea"] = 38,
    ["UGC_Cannot_Flavor_SelfGroup"] = 38,
    ["UGC_Map_Version_Not_Match"] = 38,
    ["UGC_MapPNG_Uplaod_Fail"] = 38,
    ["UGC_MapPNG_Uplaod_Success"] = 38,
    ["UGC_MapPNG_Cannot_Use"] = 38,
    ["UGC_Object_Drag_OutOfRange"] = 38,
    ["UGC_Star_Cannot_Copy"] = 38,
    ["UGC_Copy_Ocuupy_OutOfRange"] = 38,
    ["UGC_Copy_Map_OutOfRange"] = 38,
    ["UGC_GroupLayer_OutOfRange"] = 38,
    ["UGC_Group_Occupy_OutOfRange"] = 38,
    ["UGC_Objec_Bind_Success"] = 38,
    ["UGC_Objec_Bind_Error_Select_Zero"] = 38,
    ["UGC_Objec_UnBind_Success"] = 38,
    ["UGC_Cannot_Undo"] = 38,
    ["UGC_Cannot_Redo"] = 38,
    ["UI_ModelOpenTime1"] = 38,
    ["UI_ModelOpenTime2"] = 38,
    ["UI_ModelOpenTime3"] = 38,
    ["UGC_Editor_ChangeMapFailByPlayerCount"] = 38,
    ["UGC_AI_PromptCannotUse"] = 38,
    ["UGC_AI_SelectLabelOrInputText"] = 38,
    ["UGC_AI_PromptLengthLimit"] = 38,
    ["UGC_AI_PromptLanguageNotSuport"] = 38,
    ["UGC_AI_ChangeColorComplete"] = 38,
    ["UGC_AI_ThanksToFeedback"] = 38,
    ["UGC_AI_SelectColorTheme"] = 38,
    ["UGC_AI_SelectColorBoard"] = 38,
    ["UGC_AI_ExtractPaletteSuccess"] = 38,
    ["UGC_AI_HistoryPaletteApply"] = 38,
    ["UGC_AI_NoPermissionNotice"] = 38,
    ["UGC_AI_GoPermissionSetting"] = 38,
    ["UGC_AI_AdjsutScreenShot"] = 38,
    ["UGC_AI_HasUnColorableNotice"] = 38,
    ["UGC_AI_ColorInGenerateNotice"] = 38,
    ["UGC_AI_ReferenceInGenerateNotice"] = 38,
    ["UGC_AI_ColorUndo"] = 38,
    ["UGC_AI_Tab_ReferenceGenerate"] = 38,
    ["UGC_AI_Tab_ReferenceHistory"] = 38,
    ["UGC_AI_TabSub_ReferenceRecently"] = 38,
    ["UGC_AI_TabSub_ReferenceHistory"] = 38,
    ["UGC_AI_Tab_ColorTheme"] = 38,
    ["UGC_AI_Tab_ColorBoard"] = 38,
    ["UGC_AI_TabSub_ColorPalette"] = 38,
    ["UGC_AI_TabSub_ColorHistory"] = 38,
    ["UGC_AI_ColorChanging"] = 38,
    ["UGC_Group_PhotoBeforeSave"] = 39,
    ["UGC_Group_NameLengthLimit"] = 39,
    ["UGC_GroupPublish_CoverUnValid"] = 39,
    ["UGC_GroupPublish_NameIsNull"] = 39,
    ["UGC_GroupPublish_DescIsNull"] = 39,
    ["UGC_Group_OutOfSize"] = 39,
    ["UGC_GroupPublish_NameHasSpecialChar"] = 39,
    ["UGC_GroupPublish_DescHasSpecialChar"] = 39,
    ["UGC_GroupPublish_NoTag"] = 39,
    ["UGC_GroupPublish_PublishFail"] = 39,
    ["UGC_GroupPublish_PublishSuccess"] = 39,
    ["UGC_GroupPublish_DescNum"] = 39,
    ["UGC_Editor_SensitiveWord"] = 39,
    ["UGC_MapLaunch_NoPhoto"] = 39,
    ["UGC_MapLaunch_PhotoUploading"] = 39,
    ["UGC_MapLaunch_Fail"] = 39,
    ["UgcRoom_FullCommission"] = 39,
    ["friend_RelationshipRemoveRequestSent"] = 39,
    ["friend_AddFriendTips"] = 39,
    ["NewChat_JoinVoiceRoomSucceed"] = 39,
    ["NewChat_JoinVoiceRoomFailed"] = 39,
    ["UGC_ProtoSave_Fail"] = 39,
    ["UI_InLevel_Experience_Level"] = 39,
    ["UI_LuckBuy_RewardTips"] = 39,
    ["UI_LuckBuy_LockRewardTips"] = 39,
    ["UI_LuckBuy_ActivityName"] = 39,
    ["UI_LuckBuy_HotRewardTips"] = 39,
    ["UI_LuckBuy_RewardTips1"] = 39,
    ["UI_LuckBuy_ProgressTips"] = 39,
    ["UI_Common_PreviewReward"] = 39,
    ["UI_InLevel_TransCd"] = 39,
    ["Team_DisbandTeam"] = 39,
    ["UI_LuckBuy_Draw"] = 39,
    ["UI_LuckBuy_ViewName"] = 39,
    ["UGC_Waypoint_Cannot_MultipleSelect"] = 39,
    ["UGC_Waypoint_Cannot_Attach"] = 39,
    ["UGC_Waypoint_Cannot_Copy"] = 39,
    ["NewChat_VoiceForbidden"] = 39,
    ["UGC_Editor_Sort_Relevance"] = 39,
    ["Common_ZeroHourDesc"] = 39,
    ["BP_SeasonTitle"] = 39,
    ["BP_PreviewSeasonTitle"] = 39,
    ["UgcRoom_InTheRoom"] = 39,
    ["friend_removeIntimated"] = 39,
    ["UGC_ErrorMsg_SelectActorMaxNum"] = 39,
    ["UGC_Map_Save_Fail_FileOvercapacity"] = 39,
    ["UGC_Map_Clipboard_Share_Text_1"] = 39,
    ["UGC_Map_Clipboard_Share_Text_2"] = 39,
    ["UGC_Map_Clipboard_Share_Tips"] = 39,
    ["UGC_Map_Share_Title"] = 39,
    ["UGC_Map_Share_Wx_Circle_Title"] = 39,
    ["UGC_Map_Share_WxNative_Title"] = 39,
    ["UGC_Map_ShareContent"] = 39,
    ["UGC_Map_ShareContent_Less"] = 39,
    ["UGC_Map_CantShareInTestServer"] = 39,
    ["Item_ItemExpireTime"] = 39,
    ["UGC_Map_Actor_Overcapacity"] = 39,
    ["Player_Head"] = 39,
    ["UGC_Novice_Difficulty"] = 39,
    ["UGC_Simple_Difficulty"] = 39,
    ["UGC_Normal_Difficulty"] = 39,
    ["UGC_Hard_Difficulty"] = 39,
    ["UGC_Hell_Difficulty"] = 39,
    ["UGC_DreamTravel_Tip"] = 39,
    ["UGC_NumFormat_Wan_D"] = 39,
    ["UGC_NumFormat_Wan_F"] = 39,
    ["UGC_NumFormat_Yi_D"] = 39,
    ["UGC_NumFormat_Yi_F"] = 39,
    ["StringUtils_10000"] = 39,
    ["StringUtils_100000000"] = 39,
    ["Team_InviteQQ_Friend"] = 39,
    ["Team_InviteWX_Friend"] = 39,
    ["Team_InviteFail"] = 39,
    ["PlatPrivilege_WX_Tip"] = 39,
    ["FriendPlatPrivilege_WX_Tip"] = 39,
    ["CustomRoom_RoomNotExited"] = 39,
    ["PlatPrivilege_QQ_Tip"] = 39,
    ["FriendPlatPrivilege_QQ_Tip"] = 39,
    ["UGC_GrideOpacity_Open"] = 39,
    ["UGC_MirrorLimit_MirrorLock"] = 39,
    ["UGC_MirrorLimit_RotateLock"] = 39,
    ["UGC_MirrorTotal_ValueOverflow"] = 39,
    ["Login_OtherUserLoginSuccess"] = 39,
    ["UI_BlessBag_Expired"] = 39,
    ["Activity_ClickShare"] = 39,
    ["Activity_ReceivedParam"] = 39,
    ["DrawReward_MallViewToShelve"] = 39,
    ["UI_BlessBag_Date"] = 39,
    ["UI_Recruit_Target"] = 39,
    ["UI_Recruit_Num"] = 39,
    ["UI_Recruit_InviteNum"] = 39,
    ["UI_Recruit_ReachLevelNum"] = 39,
    ["UI_Recruit_TargetDesc"] = 39,
    ["UI_Pilot_NotStart"] = 39,
    ["Common_FormatInt1"] = 39,
    ["Common_FormatInt2"] = 39,
    ["CustomRoom_StartDownload"] = 39,
    ["UGC_SkyBox_Trans"] = 39,
    ["UGC_SkyBox_Background"] = 39,
    ["UGC_SkyBox_Trans_MaxNum_Hint"] = 39,
    ["UGC_SkyBox_Txt_Light"] = 40,
    ["UGC_SkyBox_Txt_Trans"] = 40,
    ["UI_Model_GamingCalendar"] = 40,
    ["UI_Model_NewGame_Status"] = 40,
    ["Activity_CheckIn_LotteryDes"] = 40,
    ["SDK_Message_StrContent"] = 40,
    ["SDK_Message_Cancel"] = 40,
    ["SDK_Message_Confirm"] = 40,
    ["SDK_Message_Input"] = 40,
    ["SDK_Message_Error1"] = 40,
    ["SDK_Message_Error2"] = 40,
    ["SDK_Message_Error3"] = 40,
    ["Home_Player_NoCreate"] = 40,
    ["Home_StatyInCurScene"] = 40,
    ["Home_Visitor_Limit"] = 40,
    ["Activity_Takeaway_BoxUnLocking"] = 40,
    ["Activity_Takeaway_BoxUnLockNotFinish"] = 40,
    ["Common_MCL_DailyLimit3"] = 40,
    ["Common_MCL_MonthlyLimit3"] = 40,
    ["Common_MCL_WeeklyLimit3"] = 40,
    ["Common_MCL_YearlyLimit3"] = 40,
    ["Common_MCL_LifeLongLimit3"] = 40,
    ["Common_MCL_SeasonLimit3"] = 40,
    ["Common_GameQuality"] = 40,
    ["Common_GameQuality_1"] = 40,
    ["Common_GameQuality_2"] = 40,
    ["Common_GameQuality_3"] = 40,
    ["Common_GameQuality_4"] = 40,
    ["Common_FunctionNotAvailable"] = 40,
    ["UGC_Map_Can_Not_Share"] = 40,
    ["AccountAdditionTip"] = 40,
    ["Mall_AskFotItem"] = 40,
    ["Mall_Gift"] = 40,
    ["Mall_Click_AskForItem"] = 40,
    ["Mall_Click_Gift"] = 40,
    ["Mall_Gift_Notice01"] = 40,
    ["Mall_Gift_Notice02"] = 40,
    ["Mall_Gift_Notice03"] = 40,
    ["Mail_Gift"] = 40,
    ["Mail_ReceiveGift"] = 40,
    ["Mail_AskForGift"] = 40,
    ["Mail_GiftLog"] = 40,
    ["Mall_Gift_Tab"] = 40,
    ["Mail_System"] = 40,
    ["Common_SeverDataError"] = 40,
    ["Bag_IPOutfitRefuseToJoinGame"] = 40,
    ["Bag_IPOutfitRefuseToSaveAsSpareOutfit"] = 40,
    ["Bag_IPOutfitRefuseToWearTogether"] = 40,
    ["UGC_Editor_Widget_Group"] = 40,
    ["UGC_Editor_Enter_CanMoveCanvas"] = 40,
    ["UGC_Waypoint_Cannot_Operate"] = 40,
    ["UGC_Waypoint_Reach_MaxNumber"] = 40,
    ["UGC_Waypoint_Reset"] = 40,
    ["UGC_Map_Save_OccupyOutOfRange"] = 40,
    ["UGC_Editor_ScaleInteractive_Text"] = 40,
    ["UGC_Map_Cooperation"] = 40,
    ["UGC_Map_TabName_Recycle"] = 40,
    ["UGC_MapList_CountTitle_DraftCommon"] = 40,
    ["UGC_MapList_CountTitle_Cooperation"] = 40,
    ["UGC_MapList_CountTitle_Release"] = 40,
    ["UGC_MapList_CountTitle_Recycler"] = 40,
    ["UGC_Author_Title"] = 40,
    ["UGC_Editor_TextInputMaxLimit"] = 40,
    ["UGC_Editor_CanvasResetDefault"] = 40,
    ["UGC_Editor_GroupChildAutoClear"] = 40,
    ["UGC_Editor_Widget_Text"] = 40,
    ["UGC_Editor_Widget_Button"] = 40,
    ["UGC_MapList_CountTips_DraftCommon"] = 40,
    ["UGC_MapList_CountTips_Cooperation"] = 40,
    ["UGC_MapList_CountTips_Release"] = 40,
    ["UGC_MapList_CountTips_Recycler"] = 40,
    ["UGC_MapList_MaxLimit_DraftCommon"] = 40,
    ["UGC_MapList_MaxLimit_Cooperation"] = 40,
    ["UGC_MapList_MaxLimit_Release"] = 40,
    ["UGC_MapList_MaxLimit_Recycler"] = 40,
    ["UGC_MapList_Edit"] = 40,
    ["UGC_MapList_Detail"] = 40,
    ["UGC_MapList_TimeDesc_Create"] = 40,
    ["UGC_MapList_TimeDesc_Modify"] = 40,
    ["UGC_MapList_TimeDesc_Update"] = 40,
    ["UGC_MapList_Sort_CreateTime"] = 40,
    ["UGC_MapList_Sort_ModifyTime"] = 40,
    ["UGC_MapList_Sort_MapName"] = 40,
    ["UGC_MapList_Sort_UpdateTime"] = 40,
    ["UGC_MapList_Sort_DeleteTime"] = 40,
    ["UGC_MapList_Filter_All"] = 40,
    ["UGC_GroupPublish_EditOption_Title"] = 40,
    ["UGC_GroupPopUp_EditOption_Title"] = 40,
    ["UGC_GroupPopUp_EditOption_Base"] = 40,
    ["UGC_GroupPopUp_EditOption_Appearance"] = 40,
    ["UGC_GroupPopUp_EditOption_Motion"] = 40,
    ["UGC_MusicSetting_Tip"] = 40,
    ["UGC_TextMask_Tips"] = 40,
    ["UGC_MapList_Play"] = 40,
    ["UGC_MapList_Destroy_LeftTime"] = 40,
    ["UGC_MapList_Publish_PreReview"] = 40,
    ["UGC_MapList_Publish_Rejected"] = 40,
    ["UGC_MapList_Publish_TakeOff"] = 40,
    ["UGC_MapList_TimeDesc_Upload"] = 40,
    ["UGC_MapList_TimeDesc_Publish"] = 40,
    ["UGC_MapList_Sort_PublishTime"] = 41,
    ["UGC_Cannot_Create_First_Layer"] = 41,
    ["Prop_Fireworks_Bullet"] = 41,
    ["UI_StarCruise_DailyRewardDesc"] = 41,
    ["Team_LeaveTeamSuccess"] = 41,
    ["Activity_Takeaway_NotEnoughKey"] = 41,
    ["Activity_GoTo_Finish"] = 41,
    ["EmitDoubleCheckText"] = 41,
    ["FireworkPartyNotOpen"] = 41,
    ["FireworkRelease_Unable"] = 41,
    ["SaveFailToast"] = 41,
    ["SaveTextTooLang"] = 41,
    ["PlayTextNotVaildText"] = 41,
    ["PlayFireworkItemNotEnough"] = 41,
    ["PlayFireworkItemError"] = 41,
    ["PlayTextNotInCommunity"] = 41,
    ["SaveFailToUnSupport"] = 41,
    ["PlayFireworkSuccess"] = 41,
    ["FireworkAuthorNamePrefix"] = 41,
    ["UGC_CoCreate_OnlyCreatorEditMap"] = 41,
    ["UGC_CoCreate_OnlyCreatorPublish"] = 41,
    ["UGC_CoCreate_ExitEdit"] = 41,
    ["UGC_CoCreate_ExitError"] = 41,
    ["UGC_CoCreate_InviteSucced"] = 41,
    ["UGC_CoCreate_TeamMember"] = 41,
    ["UGC_CoCreate_Creator"] = 41,
    ["UGC_CoCreate_SubSucceed"] = 41,
    ["UGC_CoCreate_CancelSubSucceed"] = 41,
    ["UGC_CoCreate_RemoveTeamMember"] = 41,
    ["UGC_Editor_UIWidget_Default_Text"] = 41,
    ["UGC_ChangeMapTip"] = 41,
    ["UGC_ChangeMap_Limit"] = 41,
    ["Activity_GoTo_Share"] = 41,
    ["ItemChangeReason_MonthCardBuy"] = 41,
    ["TDM_Room_BtnReadyTips"] = 41,
    ["PlayerInfo_SeasonName"] = 41,
    ["PlayerInfo_SeasonSuitCollection"] = 41,
    ["Model_MatchType_PreDesc"] = 41,
    ["Mail_Gift_Text"] = 41,
    ["Mail_AakForItem_Text"] = 41,
    ["UI_VoiceSetting_RecommandMusicTip"] = 41,
    ["Player_LevelMap_All"] = 41,
    ["Player_LevelMap_Race"] = 41,
    ["Player_LevelMap_Survival"] = 41,
    ["Player_LevelMap_SingleScore"] = 41,
    ["Player_LevelMap_TeamScore"] = 41,
    ["UGC_VERIFICATION_UNABLE_PLAY"] = 41,
    ["UGC_VERIFICATION_UNABLE_SHARE"] = 41,
    ["UGC_VERIFICATION_UNABLE_CREATE_ROOM"] = 41,
    ["UGC_CoCreate_InviteTitle"] = 41,
    ["UGC_CoCreate_DingTitle"] = 41,
    ["GameCorridor_MST_EveryonePlaying"] = 41,
    ["GameCorridor_FriendPlaying"] = 41,
    ["friend_InGame"] = 41,
    ["friend_DefaultPlatform"] = 41,
    ["Recharge_RechargeNotEnough_Tip"] = 41,
    ["Recharge_RechargeIsRunning_Tip"] = 41,
    ["Net_ConnectServerSuccess"] = 41,
    ["Common_TreasureBox"] = 41,
    ["Arena_ChestUpgrade_GetNum"] = 63,
    ["TDM_GameCorridor_Tag"] = 41,
    ["TDM_GameCorridor_FastTag"] = 41,
    ["UI_Recharge_SeasonRechargeXingyuanbiCount"] = 41,
    ["UI_Recharge_SeasonRechargeSingleGameGetDesc"] = 41,
    ["GameLive_Init_Hint"] = 41,
    ["Quit_Text"] = 41,
    ["Player_InteractiveActionPause"] = 41,
    ["UGC_Map_Share_Game_Friend_Self"] = 41,
    ["UGC_Map_Share_Game_Friend_Others"] = 41,
    ["Level_BioChase_HeroProgress"] = 41,
    ["Battle_TornadoWillTrigger"] = 41,
    ["DDP_InLevel_TornadoRefreshTip"] = 41,
    ["DDP_Team_InLevelTips1"] = 41,
    ["friend_normal_intimaty"] = 41,
    ["friend_multiplayer_intimaty"] = 41,
    ["friend_intimaty_reward_level"] = 41,
    ["CustomRoom_LevelSelectCount"] = 41,
    ["Team_ChangeTeamConfirm"] = 41,
    ["NewChat_Nearby"] = 41,
    ["Set_EditKey_FPSClassic"] = 41,
    ["Set_EditKey_FPSBioChase"] = 41,
    ["Set_EditKey_FPSBrGame"] = 41,
    ["Set_EditKey_FPSTycGame"] = 41,
    ["Set_EditKey_FPSDFGame"] = 41,
    ["Set_EditKey_FPSOmdGame"] = 41,
    ["Activity_CantDig_Msg"] = 41,
    ["Lobby_CanNotDoSingleAction"] = 41,
    ["Activity_Takeaway_BoxStartLock"] = 41,
    ["Prop_Ball_Tips"] = 41,
    ["Prop_Banana_Tips"] = 41,
    ["Prop_Bomb_Tips"] = 41,
    ["Prop_Boomerang_Tips"] = 41,
    ["Prop_Big_Tips"] = 41,
    ["Prop_Small_Tips"] = 41,
    ["Prop_Shuttle_Tips"] = 41,
    ["Prop_Cloud_Tips"] = 41,
    ["Prop_HangGlider_Tips"] = 41,
    ["Prop_Ice_Tips"] = 41,
    ["Prop_Jetpack_Tips"] = 41,
    ["Prop_Landmine_Tips"] = 41,
    ["Prop_Pole_Tips"] = 41,
    ["Prop_Roadblock_Tips"] = 42,
    ["Prop_SpeedUp_Tips"] = 42,
    ["Prop_Springboard_Tips"] = 42,
    ["Prop_Substitute_Tips"] = 42,
    ["Prop_ScreamingChicken_Tips"] = 42,
    ["Prop_Fishing_Tips"] = 42,
    ["Prop_Fireworks_Tips"] = 42,
    ["Prop_Portal_Tips"] = 42,
    ["Prop_VacuumCleaner_Tips"] = 42,
    ["Prop_RotateHammer_Tips"] = 42,
    ["Prop_TimeStave_Tips"] = 42,
    ["Prop_MucusTrap_Tips"] = 42,
    ["Prop_StealthBoom_Tips"] = 42,
    ["Prop_FishHook_Tips"] = 42,
    ["Prop_CloudFlappyBird_Tips"] = 42,
    ["AB_Backtrack_Tips"] = 42,
    ["AB_SuperBoomerang_Tips"] = 42,
    ["AB_BigHammer_Tips"] = 42,
    ["AB_SnowBall_Tips"] = 42,
    ["AB_Stealth_Tips"] = 42,
    ["AB_BigRotateHammer_Tips"] = 42,
    ["AB_JetpackGlider_Tips"] = 42,
    ["AB_Superman_Tips"] = 42,
    ["AB_Meteorite_Tips"] = 42,
    ["AB_ShapeshiftingBoom_Tips"] = 42,
    ["AB_PalmLeafFan_Tips"] = 42,
    ["AB_Lightning_Tips"] = 42,
    ["AB_Shield_Tips"] = 42,
    ["AB_StickyBomb_Tips"] = 42,
    ["AB_Ghost_Tips"] = 42,
    ["AB_RandomProp_Tips"] = 42,
    ["AB_RocketJump_Tips"] = 42,
    ["AB_HugeCloud_Tips"] = 42,
    ["AB_Vine_Tips"] = 42,
    ["AB_IcePrincess_Tips"] = 42,
    ["Prop_Ball_MoreTips"] = 42,
    ["Prop_Banana_MoreTips"] = 42,
    ["Prop_Bomb_MoreTips"] = 42,
    ["Prop_Boomerang_MoreTips"] = 42,
    ["Prop_Big_MoreTips"] = 42,
    ["Prop_Small_MoreTips"] = 42,
    ["Prop_Shuttle_MoreTips"] = 42,
    ["Prop_Cloud_MoreTips"] = 42,
    ["Prop_HangGlider_MoreTips"] = 42,
    ["Prop_Ice_MoreTips"] = 42,
    ["Prop_Jetpack_MoreTips"] = 42,
    ["Prop_Landmine_MoreTips"] = 42,
    ["Prop_Pole_MoreTips"] = 42,
    ["Prop_Roadblock_MoreTips"] = 42,
    ["Prop_SpeedUp_MoreTips"] = 42,
    ["Prop_Springboard_MoreTips"] = 42,
    ["Prop_Substitute_MoreTips"] = 42,
    ["Prop_ScreamingChicken_MoreTips"] = 42,
    ["Prop_Fishing_MoreTips"] = 42,
    ["Prop_Fireworks_MoreTips"] = 42,
    ["Prop_Portal_MoreTips"] = 42,
    ["Prop_VacuumCleaner_MoreTips"] = 42,
    ["Prop_RotateHammer_MoreTips"] = 42,
    ["Prop_MucusTrap_MoreTips"] = 42,
    ["Prop_StealthBoom_MoreTips"] = 42,
    ["Prop_CloudFlappyBird_MoreTips"] = 42,
    ["AB_Backtrack_MoreTips"] = 42,
    ["AB_SuperBoomerang_MoreTips"] = 42,
    ["AB_BigHammer_MoreTips"] = 42,
    ["AB_SnowBall_MoreTips"] = 42,
    ["AB_Stealth_MoreTips"] = 42,
    ["AB_BigRotateHammer_MoreTips"] = 42,
    ["AB_JetpackGlider_MoreTips"] = 42,
    ["AB_Superman_MoreTips"] = 42,
    ["AB_Meteorite_MoreTips"] = 42,
    ["AB_ShapeshiftingBoom_MoreTips"] = 42,
    ["AB_PalmLeafFan_MoreTips"] = 42,
    ["AB_Lightning_MoreTips"] = 42,
    ["AB_Shield_MoreTips"] = 42,
    ["AB_StickyBomb_MoreTips"] = 42,
    ["AB_Ghost_MoreTips"] = 42,
    ["AB_RandomProp_MoreTips"] = 42,
    ["AB_RocketJump_MoreTips"] = 42,
    ["AB_HugeCloud_MoreTips"] = 42,
    ["AB_Vine_MoreTips"] = 42,
    ["AB_IcePrincess_MoreTips"] = 42,
    ["Prop_InfiniteName"] = 42,
    ["Prop_Ball_Name"] = 42,
    ["Prop_Banana_Name"] = 42,
    ["Prop_Bomb_Name"] = 42,
    ["Prop_Boomerang_Name"] = 42,
    ["Prop_Big_Name"] = 42,
    ["Prop_Small_Name"] = 42,
    ["Prop_Shuttle_Name"] = 42,
    ["Prop_Cloud_Name"] = 42,
    ["Prop_HangGlider_Name"] = 42,
    ["Prop_Ice_Name"] = 42,
    ["Prop_Jetpack_Name"] = 42,
    ["Prop_Landmine_Name"] = 42,
    ["Prop_Pole_Name"] = 42,
    ["Prop_Roadblock_Name"] = 42,
    ["Prop_SpeedUp_Name"] = 42,
    ["Prop_Springboard_Name"] = 42,
    ["Prop_Substitute_Name"] = 42,
    ["Prop_ScreamingChicken_Name"] = 42,
    ["Prop_Fishing_Name"] = 43,
    ["Prop_Fireworks_Name"] = 43,
    ["Prop_Portal_Name"] = 43,
    ["Prop_VacuumCleaner_Name"] = 43,
    ["Prop_RotateHammer_Name"] = 43,
    ["Prop_TimeStave_Name"] = 43,
    ["Prop_MucusTrap_Name"] = 43,
    ["Prop_StealthBoom_Name"] = 43,
    ["Prop_FishHook_Name"] = 43,
    ["Prop_CloudFlappyBird_Name"] = 43,
    ["AB_Backtrack_Name"] = 43,
    ["AB_SuperBoomerang_Name"] = 43,
    ["AB_BigHammer_Name"] = 43,
    ["AB_SnowBall_Name"] = 43,
    ["AB_Stealth_Name"] = 43,
    ["AB_BigRotateHammer_Name"] = 43,
    ["AB_JetpackGlider_Name"] = 43,
    ["AB_Superman_Name"] = 43,
    ["AB_Meteorite_Name"] = 43,
    ["AB_ShapeshiftingBoom_Name"] = 43,
    ["AB_PalmLeafFan_Name"] = 43,
    ["AB_Lightning_Name"] = 43,
    ["AB_Shield_Name"] = 43,
    ["AB_StickyBomb_Name"] = 43,
    ["AB_Ghost_Name"] = 43,
    ["AB_RandomProp_Name"] = 43,
    ["AB_RocketJump_Name"] = 43,
    ["AB_HugeCloud_Name"] = 43,
    ["AB_Vine_Name"] = 43,
    ["AB_IcePrincess_Name"] = 43,
    ["NewChat_ChatRoomTitle"] = 43,
    ["Bnb_Count_Tips"] = 43,
    ["Bnb_Power_Tips"] = 43,
    ["Bnb_Speed_Tips"] = 43,
    ["Bnb_Limitation_Tips"] = 43,
    ["Common_ClickLikeSuccess"] = 43,
    ["PlayerInfo_NoDataForSuitSearch"] = 43,
    ["PlayerInfo_BaseInfo_HasCopyUID"] = 43,
    ["Common_SeasonNotOpenRank"] = 43,
    ["CustomRoom_CustomRoom"] = 43,
    ["Activity_CheckIn_CanSignTime"] = 43,
    ["Activity_CheckIn_SignNotCount"] = 43,
    ["Home_System_Close"] = 43,
    ["Home_VisitSystem_Close"] = 43,
    ["CustomRoom_TeamMemberLeaveRoomTip"] = 43,
    ["Lobby_CanNotBagPropItem"] = 43,
    ["friend_RecommenStr_MayBeIntersted"] = 43,
    ["friend_ApplyStr_WantToKnow"] = 43,
    ["friend_ApplyStr_Search"] = 43,
    ["Common_GetLocation"] = 43,
    ["friend_State_text01"] = 43,
    ["friend_State_text02"] = 43,
    ["friend_State_text03"] = 43,
    ["friend_Online_Notice"] = 43,
    ["CustomRoom_LevelSelect_AllRandom"] = 43,
    ["CustomRoom_LevelSelect_SomeRandom"] = 43,
    ["CustomRoom_LevelSelect_TargetOne"] = 43,
    ["UI_Recharge_CutGiftTotalCanBuy"] = 43,
    ["UGC_Editor_MapRecmond"] = 43,
    ["UGC_Editor_MapRecmond_Hot"] = 43,
    ["UI_Recharge_WxMiniGamePayFail"] = 43,
    ["UGC_WarningMsg_EventExceedLimit"] = 43,
    ["Mode_InCustomRoom"] = 43,
    ["UGCRoom_AutoExit_Unready"] = 43,
    ["UGCRoom_Exit_Countdown"] = 43,
    ["UGCRoom_AutoBeginClose"] = 43,
    ["Mall_gift_share_text"] = 43,
    ["Friend_intimate_bottom_notice"] = 43,
    ["BP_mission_start_time"] = 43,
    ["UGCRoom_Ready_Invite"] = 43,
    ["ItemChangeReason_EmailReceive"] = 43,
    ["UGC_AtLess_Two_SpawnPoint"] = 43,
    ["UGC_AtLess_Two_Side"] = 43,
    ["UGC_MapTip_DesEmpty_Tips"] = 43,
    ["Notice_LimitTimeTitle"] = 43,
    ["DDP_NetState_Weak"] = 43,
    ["Team_KickSuccess"] = 43,
    ["MCL_LifeLongLimit"] = 43,
    ["MCL_DailyLimit"] = 43,
    ["MCL_WeeklyLimit"] = 43,
    ["MCL_MonthlyLimit"] = 43,
    ["MCL_SeasonLimit"] = 65,
    ["MCL_YearlyLimit"] = 43,
    ["Friend_intimate_gift_text"] = 43,
    ["UgcRoom_Camp"] = 43,
    ["UGC_GroupPublish_EditOption_TipsInfo"] = 43,
    ["GameCorridor_MST_Subscribe"] = 43,
    ["UgcRoom_InsufficientPosition"] = 43,
    ["__NR3E_All_Text_Begin__"] = 43,
    ["InLevel_NR3E_GiveUpVote"] = 43,
    ["InLevel_NR3E0_Pretender"] = 43,
    ["InLevel_NR3E1_Searcher"] = 43,
    ["InLevel_NR3E0_PretenderGameTarget"] = 43,
    ["InLevel_NR3E0_SearcherGameTarget"] = 43,
    ["InLevel_NR3E0_PretenderSkillDes"] = 43,
    ["InLevel_NR3E0_PretenderGameTips"] = 43,
    ["InLevel_NR3E0_PretenderGameTips1"] = 43,
    ["InLevel_NR3E0_SearcherGameTips"] = 43,
    ["InLevel_NR3E0_UserItem_Scan"] = 43,
    ["InLevel_NR3E0_UserItem_Exploration"] = 43,
    ["InLevel_NR3E1_ResiduePretender"] = 43,
    ["InLevel_NR3E1_Pertendered"] = 44,
    ["InLevel_NR3E1_SearcherArrestCount"] = 44,
    ["InLevel_NR3E0_Rule"] = 44,
    ["InLevel_NR3E1_SearcherTarget"] = 44,
    ["InLevel_NR3E1_PretenderTarget"] = 44,
    ["InLevel_NR3E1_SearcherGameTips"] = 44,
    ["InLevel_NR3E1_PretenderGameTips"] = 44,
    ["InLevel_NR3E1_PretendSkillName"] = 44,
    ["InLevel_NR3E1_HideSkillName"] = 44,
    ["InLevel_NR3E1_SearcherSkillName"] = 44,
    ["InLevel_NR3E1_DetectSkillName"] = 44,
    ["InLevel_NR3E1_OnlySearcherGet"] = 44,
    ["InLevel_NR3E1_SkillCD"] = 44,
    ["InLevel_NR3E1_PowerMax"] = 44,
    ["InLevel_NR3E1_DetctTargetArrested"] = 44,
    ["InLevel_NR3E1_GameFinishTips1"] = 44,
    ["InLevel_NR3E1_GameFinishTips2"] = 44,
    ["InLevel_NR3E1_FindDetectItem"] = 44,
    ["InLevel_NR3E1_DefineBackSquare"] = 44,
    ["InLevel_NR3E1_BackSquare_WinTips"] = 44,
    ["InLevel_NR3E1_Arrested"] = 44,
    ["InLevel_NR3E1_ArrestPlayer"] = 44,
    ["InLevel_NR3E2_ThrowSkillName"] = 44,
    ["InLevel_NR3E2_TarpSkillName"] = 44,
    ["InLevel_NR3E2_AidSkillName"] = 44,
    ["InLevel_NR3E2_ShootSkillName"] = 44,
    ["InLevel_NR3E2_PropSkillName"] = 44,
    ["InLevel_NR3E2_TrapNull"] = 44,
    ["InLevel_NR3E2_DollAllOut"] = 44,
    ["InLevel_NR3E2_BadGuyAllOut"] = 44,
    ["NR3E_EndReason_1"] = 44,
    ["NR3E_EndReason_2"] = 44,
    ["NR3E_EndReason_3"] = 65,
    ["NR3E_EndReason_51"] = 44,
    ["NR3E_EndReason_52"] = 44,
    ["NR3E_EndReason_53"] = 44,
    ["InLevel_NR3E_OutTips1"] = 44,
    ["InLevel_NR3E_OutTips2"] = 44,
    ["InLevel_NR3E_OutTips3"] = 44,
    ["InLevel_NR3E_Talking"] = 44,
    ["InLevel_NR3E_Voting"] = 44,
    ["InLevel_NR3E_Voted"] = 44,
    ["InLevel_NR3E_IsGiveUpVoted"] = 44,
    ["InLevel_NR3E_TalkState"] = 44,
    ["InLevel_NR3E_VoteState"] = 44,
    ["InLevel_NR3E_BeginSay"] = 44,
    ["InLevel_NR3E_ChooseOutDoll"] = 44,
    ["InLevel_NR3ERoleDes_Hunter"] = 44,
    ["InLevel_NR3ERoleDes_Host"] = 44,
    ["InLevel_NR3ERoleDes_Angle"] = 44,
    ["InLevel_NR3ERoleDes_People"] = 44,
    ["InLevel_NR3ERoleName_Hunter"] = 44,
    ["InLevel_NR3ERoleName_Angle"] = 44,
    ["InLevel_NR3ERoleName_Host"] = 44,
    ["InLevel_NR3ERoleTarget_Hunter"] = 44,
    ["InLevel_NR3ERoleTarget_Common"] = 44,
    ["InLevel_NR3ESkillDes_Hunter"] = 44,
    ["InLevel_NR3ESkillDes_Host"] = 44,
    ["InLevel_NR3ESkillDes_Angle"] = 44,
    ["InLevel_NR3E_SliderSpeed_OneParam"] = 44,
    ["InLevel_NR3E_TaskDes"] = 44,
    ["InLevel_NR3E_CompleteFireMission"] = 44,
    ["InLevel_NR3E_CompleteFogMission"] = 44,
    ["InLevel_NR3E_CampInfo"] = 44,
    ["InLevel_NR3E_ChampionTitle"] = 44,
    ["InLevel_NR3E_ChampionDes"] = 44,
    ["InLevel_NR3E_ScoreProtect"] = 44,
    ["InLevel_NR3E_RankProtect"] = 44,
    ["InLevel_NR3E_FristGameRankProtect"] = 44,
    ["InLevel_NR3E2_Settings1"] = 44,
    ["InLevel_NR3E2_Settings2"] = 44,
    ["InLevel_NR3E3_ControlPad_SideA1"] = 44,
    ["InLevel_NR3E3_ControlPad_SideOther1"] = 44,
    ["InLevel_NR3E3_ControlPad_SideOther2"] = 44,
    ["InLevel_NR3E3_ControlPad_SideOther3"] = 44,
    ["InLevel_NR3E3_ControlPad_SideOther4"] = 44,
    ["InLevel_NR3E3_Rule1"] = 44,
    ["InLevel_NR3E3_Rule2"] = 44,
    ["InLevel_NR3E3_Rule3"] = 44,
    ["InLevel_NR3E3_Rule4"] = 44,
    ["InLevel_NR3E3_Rule5"] = 44,
    ["InLevel_NR3E3_Rule6"] = 44,
    ["InLevel_NR3E3_Rule7"] = 44,
    ["InLevel_NR3E3_Rule8"] = 44,
    ["InLevel_NR3E3_VoiceMicroCell"] = 44,
    ["InLevel_NR3E31"] = 44,
    ["InLevel_NR3E32"] = 44,
    ["InLevel_NR3E3_GameFinish"] = 44,
    ["InLevel_Chat_NR3E3Meeting"] = 44,
    ["InLevel_Chat_NR3E3MeetingInput"] = 44,
    ["InLevel_NR3E3_Meeting"] = 44,
    ["InLevel_NR3E3_MeetingStateChange"] = 44,
    ["InLevel_NR3E3_MeetingStateChange1"] = 44,
    ["InLevel_NR3E3_MeetingStateChange2"] = 44,
    ["InLevel_NR3E3_MeetingStateChange3"] = 44,
    ["InLevel_NR3E3_MeetingStateChange4"] = 44,
    ["InLevel_NR3E3_ChildTask_103"] = 44,
    ["InLevel_NR3E3_ChildTask_103_1"] = 44,
    ["InLevel_NR3E3_Task_1"] = 44,
    ["InLevel_NR3E3_Task_2"] = 44,
    ["InLevel_NR3E3_TaskPanel_EmergentMeeting"] = 44,
    ["InLevel_NR3E3_GameStart"] = 45,
    ["InLevel_NR3E3_GameStart1"] = 45,
    ["InLevel_NR3E3_GameStart2"] = 45,
    ["InLevel_NR3E3_JobInfo"] = 45,
    ["InLevel_NR3E3_JobInfo_1"] = 45,
    ["InLevel_NR3E3_JobInfo_2"] = 45,
    ["InLevel_NR3E3_JobInfo_3"] = 45,
    ["InLevel_NR3E3_JobInfo_4"] = 45,
    ["InLevel_NR3E3_JobInfo_5"] = 45,
    ["InLevel_NR3E3_JobInfo_6"] = 45,
    ["InLevel_NR3E3_JobInfo_7"] = 45,
    ["InLevel_NR3E3_KnockedOut"] = 45,
    ["InLevelNR3E2FinishName1"] = 45,
    ["InLevelNR3E2FinishName2"] = 45,
    ["InLevelNR3E1FinishName1"] = 45,
    ["InLevelNR3E1FinishName2"] = 45,
    ["InLevelNR3E1FinishWin1"] = 45,
    ["InLevelNR3E1FinishWin2"] = 45,
    ["InLevelNR3E3FinishWinCamp1"] = 45,
    ["InLevelNR3E3FinishWinCamp2"] = 45,
    ["BP_NR3E3MeetingManager"] = 45,
    ["NR3E2_DollsRunComponent1"] = 45,
    ["NR3E2_DollsRunComponent2"] = 45,
    ["NR3E2_DollsRunComponent3"] = 45,
    ["NR3E2_DollsRunComponent4"] = 45,
    ["NR3E2_DollsRunComponent5"] = 45,
    ["NR3E2_DollsRunComponent6"] = 45,
    ["NR3E2_DollsRunComponent7"] = 45,
    ["NR3E2_DollsRunComponent8"] = 45,
    ["NR3E2_DollsRunComponent9"] = 45,
    ["NR3E2_AIWarningBubbleTips"] = 45,
    ["NR3E3_BaseComponent"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel1"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel2"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel3"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel4"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel5"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel6"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel7"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel8"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel9"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel10"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel11"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel12"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel13"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel14"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel15"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel16"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel17"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel18"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel19"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel20"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel21"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel22"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel23"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel24"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel25"] = 45,
    ["UI_InLevel_NR3E3_TaskPanel26"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes1"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes2"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes3"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes4"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes5"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes6"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes7"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes8"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes9"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes10"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes11"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes12"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes13"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes14"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes15"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes16"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes17"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes18"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes19"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes20"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes21"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes22"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes23"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes24"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes25"] = 45,
    ["UI_InLevel_NR3E3_TaskPanelDes26"] = 45,
    ["UI_InLevel_NR3E3_HoleName1"] = 45,
    ["UI_InLevel_NR3E3_HoleName2"] = 45,
    ["UI_InLevel_NR3E3_HoleName3"] = 45,
    ["UI_InLevel_NR3E3_HoleName4"] = 45,
    ["UI_InLevel_NR3E3_HoleName5"] = 45,
    ["UI_InLevel_NR3E3_HoleName6"] = 45,
    ["UI_InLevel_NR3E3_HoleName7"] = 45,
    ["UI_InLevel_NR3E3_HoleName8"] = 45,
    ["UI_InLevel_NR3E3_MapName1"] = 45,
    ["UI_InLevel_NR3E3_MapName2"] = 45,
    ["UI_InLevel_NR3E3_MapName3"] = 45,
    ["UI_InLevel_NR3E3_MapName4"] = 45,
    ["UI_InLevel_NR3E3_MapName5"] = 45,
    ["UI_InLevel_NR3E3_MapName6"] = 45,
    ["UI_InLevel_NR3E3_MapName7"] = 45,
    ["UI_InLevel_NR3E3_MapName8"] = 45,
    ["UI_InLevel_NR3E3_MapName9"] = 46,
    ["UI_InLevel_NR3E3_MapName10"] = 46,
    ["UI_InLevel_NR3E3_MapName11"] = 46,
    ["UI_InLevel_NR3E3_MapName12"] = 46,
    ["UI_InLevel_NR3E3_MapName13"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_1_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_1_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_1_3"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_2_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_2_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_2_3"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_3_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_3_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_3_3"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_4_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_4_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_4_3"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_5_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_5_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_6_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_6_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Taking_6_3"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_1_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_2_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_2_2"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_4_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_5_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_6_1"] = 46,
    ["NR3E3_AIMessage_FirstReport_Voting_6_2"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_1_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_1_2"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_1_3"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_2_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_2_2"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_2_3"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_3_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_3_2"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_3_3"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_4_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Taking_4_2"] = 46,
    ["NR3E3_AIMessage_SecondReport_Voting_1_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Voting_2_1"] = 46,
    ["NR3E3_AIMessage_SecondReport_Voting_4_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_1_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_1_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_2_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_2_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_3"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_4_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_4_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_5_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_5_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_6_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_6_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Taking_7_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_1_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_1_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_2_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_2_2"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_3_1"] = 46,
    ["NR3E3_AIMessage_PlayerReport_Voting_4_1"] = 46,
    ["UI_InLevel_NR3E3_Meeting1"] = 46,
    ["NR3E3_NoVoiceAuthorit"] = 46,
    ["NR3E3_PermissionPrompt"] = 46,
    ["NR3E3_PermissionConfirm"] = 46,
    ["NR3E3Meeting_Discuss"] = 46,
    ["NR3E3Meeting_DeathTip"] = 46,
    ["InLevel_NR3E2_VoiceMicroCell"] = 46,
    ["NR3E2_DollsRunComponent10"] = 46,
    ["UI_InLevel_NR3E3_UrgentTips"] = 46,
    ["NR3E_EndReason_101_Tips"] = 46,
    ["NR3E_EndReason_102_Tips"] = 46,
    ["NR3E_EndReason_104_Tips"] = 46,
    ["NR3E_EndReason_101"] = 46,
    ["NR3E_EndReason_102"] = 46,
    ["NR3E_EndReason_103"] = 46,
    ["NR3E_EndReason_104"] = 46,
    ["NR3E_EndReasonType1"] = 46,
    ["NR3E_EndReasonType2"] = 46,
    ["NR3E_EndReasonType3"] = 46,
    ["NR3E_EndReasonType4"] = 46,
    ["NR3E_TransferBomb"] = 46,
    ["NR3E_TransferBomb_IconText"] = 46,
    ["NR3E_PlacedBomb_IconText"] = 46,
    ["InLevel_NR3E3_Rule9"] = 46,
    ["InLevel_NR3E3_Rule10"] = 46,
    ["InLevel_NR3E3_Rule11"] = 46,
    ["InLevel_NR3E3_Rule12"] = 46,
    ["InLevel_NR3E3_Rule13"] = 46,
    ["InLevel_NR3E3_Rule14"] = 46,
    ["InLevel_NR3E3_Rule15"] = 46,
    ["InLevel_NR3E3_Rule16"] = 46,
    ["InLevel_NR3E3_Rule17"] = 46,
    ["InLevel_NR3E3_Rule18"] = 46,
    ["InLevel_NR3E3_Rule19"] = 46,
    ["InLevel_NR3E3_Rule20"] = 46,
    ["InLevel_NR3E3_Rule21"] = 46,
    ["InLevel_NR3E3_Rule22"] = 46,
    ["InLevel_NR3E3_Rule23"] = 47,
    ["InLevel_NR3E3_Rule24"] = 47,
    ["InLevel_NR3E3_Rule25"] = 47,
    ["InLevel_NR3E3_Rule26"] = 47,
    ["InLevel_NR3E3_Rule27"] = 47,
    ["InLevel_NR3E3_Rule28"] = 47,
    ["InLevel_NR3E3_Rule29"] = 47,
    ["InLevel_NR3E3_Rule30"] = 47,
    ["InLevel_NR3E3_Rule31"] = 47,
    ["InLevel_NR3E3_Rule32"] = 47,
    ["InLevel_NR3E3_Rule33"] = 47,
    ["InLevel_NR3E3_Rule34"] = 47,
    ["InLevel_NR3E3_Rule35"] = 47,
    ["InLevel_NR3E3_Rule36"] = 47,
    ["InLevel_NR3E3_Rule37"] = 47,
    ["UI_InLevel_NR3E3_Bell"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskTips1"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskTips2"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem1"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem2"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem3"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem4"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem5"] = 47,
    ["UI_InLevel_NR3E3_UrgentTaskItem6"] = 47,
    ["UI_InLevel_NR3E3_VocationItemTip"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft1"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft2"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft3"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft4"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft5"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft6"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft7"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft8"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft9"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft10"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft11"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft12"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft13"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft14"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft15"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft16"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft17"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft18"] = 47,
    ["UI_InLevel_NR3E3_MeetingLeft19"] = 47,
    ["UI_InLevel_NR3E3_VocationTitle"] = 47,
    ["UI_InLevel_NR3E3_SheriffAttackDoll"] = 47,
    ["UI_InLevel_NR3E3_Task1"] = 47,
    ["UI_InLevel_NR3E3_Task2"] = 47,
    ["UI_InLevel_NR3E3_Task3"] = 47,
    ["UI_InLevel_NR3E3_Task4"] = 47,
    ["UI_InLevel_NR3E3_Task5"] = 47,
    ["UI_InLevel_NR3E3_Task6"] = 47,
    ["UI_InLevel_NR3E3_DetectTargetDisappear"] = 47,
    ["UI_InLevel_NR3E3_DetectTargetTooFarAway"] = 47,
    ["UI_InLevel_NR3E3_DetectPleaseComeCloser"] = 47,
    ["UI_InLevel_NR3E3_DetectResult"] = 47,
    ["UI_InLevel_NR3E3_DetectNotTarget"] = 47,
    ["UI_InLevel_NR3E2_UndercoverTips"] = 47,
    ["UI_InLevel_NR3E2_UndercoverMoment1"] = 47,
    ["UI_InLevel_NR3E2_UndercoverMoment"] = 47,
    ["UI_InLevel_NR3E3_GameStartTip"] = 47,
    ["UI_InLevel_NR3E3_Bell1"] = 47,
    ["UI_InLevel_NR3E3_UrgentTask1"] = 47,
    ["UI_InLevel_NR3E3_NotAttackTarget"] = 47,
    ["UI_InLevel_NR3E3_Detect"] = 47,
    ["InLevel_NR3E3_IdentityInformation1"] = 47,
    ["InLevel_NR3E3_IdentityInformation2"] = 47,
    ["InLevel_NR3E3_IdentityInformation3"] = 47,
    ["InLevel_NR3E3_IdentityInformation4"] = 47,
    ["InLevel_NR3E3_IdentityInformation5"] = 47,
    ["InLevel_NR3E3_IdentityInformation6"] = 47,
    ["InLevel_NR3E3_IdentityInformation7"] = 47,
    ["InLevel_NR3E3_IdentityInformation8"] = 47,
    ["InLevel_NR3E3_NR3ESkillDes1"] = 47,
    ["InLevel_NR3E3_NR3ESkillDes2"] = 47,
    ["InLevel_NR3E3_MeetingSysTip1"] = 47,
    ["InLevel_NR3E3_MeetingSysTip2"] = 47,
    ["InLevel_NR3E3_MeetingSysTip3"] = 47,
    ["InLevel_NR3E3_MeetingSysTip4"] = 47,
    ["InLevel_NR3E3_MeetingSysTip5"] = 47,
    ["InLevel_NR3E3_MeetingSysTip6"] = 47,
    ["InLevel_NR3E3_MeetingSysTip7"] = 47,
    ["NR3E2_DollsRunComponent11"] = 47,
    ["NR3E2_DollsRunComponent12"] = 47,
    ["NR3E1_SkillName"] = 47,
    ["UI_InLevel_NR3E3_MarkNotTarget"] = 47,
    ["UI_InLevel_NR3E3_WarriorTip"] = 47,
    ["InLevel_NR3E3_Rule38"] = 47,
    ["InLevel_NR3E3_Rule39"] = 47,
    ["InLevel_NR3E3_Rule40"] = 47,
    ["InLevel_NR3E3_Rule41"] = 47,
    ["InLevel_NR3E3_Rule42"] = 47,
    ["InLevel_NR3E3_Rule43"] = 47,
    ["InLevel_NR3E3_IdentityInformation9"] = 47,
    ["InLevel_NR3E3_IdentityInformation10"] = 47,
    ["InLevel_NR3E3_IdentityInformation11"] = 47,
    ["InLevel_NR3E3_IdentityInformation12"] = 47,
    ["InLevel_NR3E3_IdentityInformation13"] = 47,
    ["InLevel_NR3E3_IdentityInformation14"] = 47,
    ["InLevel_NR3E3_IdentityInformation15"] = 47,
    ["UI_InLevel_NR3E3_Rule1_GameInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule1_HideWinInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo3"] = 48,
    ["UI_InLevel_NR3E3_Rule1_SeekWinInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo3"] = 48,
    ["UI_InLevel_NR3E3_Rule2_GameInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule2_HideWinInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo3"] = 48,
    ["UI_InLevel_NR3E3_Rule2_SeekWinInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo3"] = 48,
    ["UI_InLevel_NR3E3_LoseOut"] = 48,
    ["InLevel_NR3E3_Rule44"] = 48,
    ["InLevel_NR3E3_Rule45"] = 48,
    ["InLevel_NR3E3_Rule46"] = 48,
    ["InLevel_NR3E3_Rule47"] = 48,
    ["InLevel_NR3E3_Rule48"] = 48,
    ["InLevel_NR3E3_Rule49"] = 48,
    ["InLevel_NR3E3_Rule50"] = 48,
    ["InLevel_NR3E3_JobInfo_Zoli"] = 48,
    ["UI_InLevel_NR3E3_NotTarget"] = 48,
    ["UI_InLevel_NR3E3_FakerTaskTip"] = 48,
    ["UI_InLevel_NR3E3_BombFire"] = 48,
    ["InLevel_NR3E3_BountyTarget_Changed"] = 48,
    ["InLevel_NR3E3_NeutralCamp_CompleteMissionFailed"] = 48,
    ["InLevel_NR3E3_NeutralCamp_DeathNotify"] = 48,
    ["UI_InLevel_NR3E3_Rule3_GameInfo"] = 48,
    ["UI_InLevel_NR3E3_Rule3_GameTypeInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule3_GameTypeInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Target_Normal"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Target_Killer"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Target_Neutral"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Normal_Task"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Urgency_Task"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Report"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Meeting"] = 48,
    ["UI_InLevel_NR3E3_Rule3_SoulState"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Bell"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Broadcas"] = 48,
    ["UI_InLevel_NR3E3_Rule3_Radar"] = 48,
    ["UI_InLevel_NR3E3_Rule3_HiddenPath"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo1"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo2"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo3"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo4"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo5"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo6"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo7"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo8"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo9"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo10"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo11"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo12"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo13"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo14"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo15"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo16"] = 48,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo17"] = 48,
    ["UI_InLevel_NR3E3_NoSelect"] = 48,
    ["NR3E3_AIMessage_SendChat_1"] = 48,
    ["NR3E3_AIMessage_SendChat_2"] = 48,
    ["NR3E3_AIMessage_SendChat_3"] = 48,
    ["NR3E3_AIMessage_SendChat_4"] = 48,
    ["NR3E3_AIMessage_SendChat_5"] = 48,
    ["NR3E3_AIMessage_SendChat_6"] = 48,
    ["CustomRoom_NR3E3_RoomTitle1"] = 48,
    ["CustomRoom_NR3E3_RoomTitle2"] = 48,
    ["CustomRoom_NR3E3_RoomTitle3"] = 48,
    ["CustomRoom_NR3E3_Rule1"] = 48,
    ["CustomRoom_NR3E3_Rule2"] = 48,
    ["CustomRoom_NR3E3_Rule3"] = 48,
    ["CustomRoom_NR3E3_Rule4"] = 48,
    ["CustomRoom_NR3E3_Rule5"] = 48,
    ["CustomRoom_NR3E3_Rule6"] = 48,
    ["CustomRoom_NR3E3_JobTitle1"] = 48,
    ["CustomRoom_NR3E3_JobTitle2"] = 48,
    ["CustomRoom_NR3E3_JobBtn"] = 48,
    ["CustomRoom_NR3E3_Tip1"] = 48,
    ["CustomRoom_NR3E3_Tip2"] = 48,
    ["__NR3E_All_Text_End__"] = 48,
    ["UGC_LocationCopySuccess"] = 48,
    ["UGC_Place_Occupy_OutOfOnceRange"] = 48,
    ["UI_RedEnvelope_ActivityDescription"] = 48,
    ["UI_RedEnvelope_NextTurnTimeInterval"] = 48,
    ["UI_RedEnvelope_ThisTurnTimeInterval"] = 48,
    ["UI_RedEnvelope_WaitWhichTurn"] = 48,
    ["UI_RedEnvelope_CurrentTurn"] = 48,
    ["UI_RedEnvelope_OwnQuantity"] = 48,
    ["UI_RedEnvelope_TurnTitle"] = 48,
    ["UI_RedEnvelope_TurnTime"] = 48,
    ["UI_GunGame_Killed_Content"] = 48,
    ["UI_GunGame_ArriveInfo_RankStr"] = 48,
    ["Friend_AddQQFriendTitle"] = 48,
    ["Friend_AddQQFriendDesc"] = 49,
    ["Friend_Send_QQ"] = 49,
    ["Friend_Send_WX"] = 49,
    ["Friend_Share_QQ"] = 49,
    ["Friend_Share_WX"] = 49,
    ["Net_NetDriverAlreadyExists"] = 49,
    ["Net_NetDriverCreateFailure"] = 49,
    ["Net_NetDriverListenFailure"] = 49,
    ["Net_ConnectionLost"] = 49,
    ["Net_ConnectionTimeout"] = 49,
    ["Net_FailureReceived"] = 49,
    ["Net_OutdatedClient"] = 49,
    ["Net_OutdatedServer"] = 49,
    ["Net_PendingConnectionFailure"] = 49,
    ["Net_NetGuidMismatch"] = 49,
    ["Net_NetChecksumMismatch"] = 49,
    ["Xiaowo_MoneyTree_OutOfRange"] = 49,
    ["UI_PlayerInfo_ModRecord_Draw"] = 49,
    ["UI_Recharge_SeasonRecharge_CountNotEnough"] = 49,
    ["Team_In_Match_Invite"] = 49,
    ["UGC_CoCreate_TeamMember_Simple"] = 49,
    ["UGC_CoCreate_Creator_Simple"] = 49,
    ["Mall_Use_Period"] = 49,
    ["ItemChangeReason_QQCash"] = 49,
    ["ItemChangeReason_WXCash"] = 49,
    ["UGC_Object_Cant_SelfBind"] = 49,
    ["UI_UGCFPS_Result_Player_UnRanked"] = 49,
    ["UI_UGCFPS_Result_Team_1"] = 49,
    ["UI_UGCFPS_Result_Team_2"] = 49,
    ["SettingTip_JotstickMode"] = 49,
    ["SettingTip_CharacterFlexibility"] = 49,
    ["UGCRoom_Ready"] = 49,
    ["UGCRoom_GoToRoom_Ready"] = 49,
    ["UI_Login_WxQRCodeDesc"] = 49,
    ["InGameScene_CanNotJump"] = 49,
    ["Common_LoginNetError"] = 49,
    ["ExchangeSeatFail_In"] = 49,
    ["ExchangeSeatFail_WaitCd"] = 49,
    ["UGC_ErrorMsg_LoopControl"] = 49,
    ["UGC_GroupCollect_Successful"] = 49,
    ["UGC_GroupCollect_UpperLimit"] = 49,
    ["UI_RedEnvelope_ReceiveTimeOut"] = 49,
    ["Activity_NotEnough2"] = 49,
    ["NewChat_ForbindHint"] = 49,
    ["UGC_Map_Share_WxCircle_Title"] = 49,
    ["Credit_Message"] = 49,
    ["Credit_Hint_CantSpeak"] = 49,
    ["Credit_ShowDetail"] = 49,
    ["UI_Recharge_Navigation_Title"] = 49,
    ["Activity_LockForCondition"] = 49,
    ["BoxCommunityLoadingHint"] = 49,
    ["BoxCommunityLoadFailed"] = 49,
    ["FriendModel_BatchBanFriend"] = 49,
    ["Emulator_Not_Tencent"] = 49,
    ["Friend_BatchBan_Notice"] = 49,
    ["Friend_BatchBan_NoFriendNotice"] = 49,
    ["Friend_BatchBan_SuccessNotice"] = 49,
    ["UGC_Map_DontSupportTranslate"] = 49,
    ["UGC_Group_DontSupportTranslate"] = 49,
    ["Credit_Message1"] = 49,
    ["Credit_Message2"] = 49,
    ["Credit_Message3"] = 49,
    ["Credit_Message4"] = 49,
    ["Credit_Message5"] = 49,
    ["Credit_Message6"] = 49,
    ["Credit_Message7"] = 49,
    ["platPrivilegesQQAdditional"] = 49,
    ["platPrivilegesWXAdditional"] = 49,
    ["Team_IsJoingingRoom"] = 49,
    ["Team_JoingRoomFailed"] = 49,
    ["NewChat_Translate"] = 49,
    ["NewChat_Translated"] = 49,
    ["NewChat_TranslateFrom"] = 49,
    ["NewChat_TranslateFailed"] = 49,
    ["UGC_Noattachmentsallowed_mirroring"] = 49,
    ["UI_UGCDailyLevelItem_1"] = 49,
    ["UI_Lottery_SeasonSubView_1"] = 49,
    ["Level_GoalText_TeamGoal"] = 49,
    ["UI_UGCEditorCommon_OperateView_1"] = 49,
    ["UI_Mall_Exchange_1"] = 49,
    ["UI_RedEnvelope_Remind_QQ"] = 49,
    ["UI_RedEnvelope_Remind_WX"] = 49,
    ["TimeUtils_M_Const"] = 49,
    ["UI_RedEnvelope_OpenTimeOut"] = 49,
    ["BattlePass_Unlock_ShowLevel"] = 49,
    ["Platform_CanNotEnterGameLive"] = 49,
    ["Activity_Takeaway_ReverseSuccess"] = 49,
    ["ChannelEntranceTips_1"] = 49,
    ["UGC_Editor_LocomotorInteractive_Text"] = 49,
    ["Common_CopyToClipboard_Text"] = 49,
    ["Activity_KingLinkageComing"] = 49,
    ["Activity_SharedAndReceiveQiaoSuit"] = 49,
    ["Activity_SharedAndReceiveSRP"] = 49,
    ["Activity_KingLinkageSuit"] = 49,
    ["UI_Recharge_SeasonRecharge_ActivityNotOpen"] = 49,
    ["UI_Recharge_SeasonRecharge_ActivityConditionNoPass"] = 49,
    ["UI_Recharge_SeasonRecharge_ActivityIsOver"] = 49,
    ["UGC_Editor_HomeElementLocked_Text"] = 49,
    ["Bnb_life_Tips"] = 49,
    ["MonkiBubbleAcquireSuccess"] = 49,
    ["MonkiBubbleAcquireFail"] = 50,
    ["InLevel_FPS_GameResult_TeamWin"] = 50,
    ["InLevel_FPS_GameResult_End"] = 50,
    ["InLevel_FPS_GameResult_SingleWin"] = 50,
    ["UGC_Waypoint_Cannot_Mirror"] = 50,
    ["Bnb_fail_Tips"] = 50,
    ["InLevel_FPS_FinalAcountRank"] = 50,
    ["Pandora_OpenApp_Fail_Tips"] = 50,
    ["UI_Common_BackToGame"] = 50,
    ["UI_UGC_MapVersion_Abort_iOS"] = 50,
    ["UI_UGC_MapVersion_PassButTips_iOS"] = 50,
    ["UI_UGC_MapVersion_Abort_Android"] = 50,
    ["UI_UGC_MapVersion_PassButTips_Android"] = 50,
    ["UI_UGC_MapVersion_Abort_ConfirmBtn"] = 50,
    ["UI_UGC_MapVersion_Abort_RebootBtn"] = 50,
    ["Tips_Gameplay_WaitingTime"] = 50,
    ["UI_Gameplay_Waiting"] = 50,
    ["UI_Gameplay_WaitingPlayerNumber"] = 50,
    ["UI_Gameplay_WaitingTime"] = 50,
    ["UI_Gameplay_ReadyForPlay"] = 50,
    ["Tips_UGC_ActorManager_CreateTag_Error"] = 50,
    ["UGC_Map_AutoSave_Fail"] = 50,
    ["Common_Minute"] = 50,
    ["UGC_CoCreate_HeartBeat_NoEditing"] = 50,
    ["UGC_CoCreate_Member_KickOut"] = 50,
    ["UGC_Map_AutoSave_Success"] = 50,
    ["UGC_Map_AutoSave_ConflictResolution_Reconfirm"] = 50,
    ["UGC_Map_AutoSave_ConflictResolution_Continue"] = 50,
    ["UGC_Map_AutoSave_ConflictResolution_Reselect"] = 50,
    ["UGC_Feautre_Cannot_Open"] = 50,
    ["UGC_GroupLike_ResVersionCheck_NotPass"] = 50,
    ["UGC_GroupCollect_ResVersionCheck_NotPass"] = 50,
    ["DrawReward_MultiRaffleKeyTip"] = 50,
    ["UGC_Editor_NotPublishMap"] = 50,
    ["UGC_Editor_Exit_TaskTips"] = 50,
    ["UI_Gameplay_WaitingTips"] = 50,
    ["UI_Team_Invite_BackSend_Succeed"] = 50,
    ["UI_Team_Invite_Send_Failed"] = 50,
    ["UI_Not_Support_Hint"] = 50,
    ["UGC_CopyCannotOverLimit"] = 50,
    ["UGC_CopyInPlaceCannotOverLimit"] = 50,
    ["UGC_CombinedMirrorExamine"] = 50,
    ["UGC_BindingMirrorExamine"] = 50,
    ["UGC_Editor_CollisionPreview_Text"] = 50,
    ["Permission_Live_Voice_Hint"] = 50,
    ["Permission_Save_Pic_Hint"] = 50,
    ["Permission_Camera_Hint"] = 50,
    ["Permission_Camera_Failed"] = 50,
    ["UGC_Search_Clear_History"] = 50,
    ["UGC_Editor_SetFixedXBMsg"] = 50,
    ["UGC_Editor_TranslucentSelection_ForbidMsg"] = 50,
    ["UGC_Editor_Selection_ForbidMsg"] = 50,
    ["UGC_Transform_NOParToPaste"] = 50,
    ["UGC_Transform_SucCopiedAllPar"] = 50,
    ["UGC_Transform_SucPastedAllPar"] = 50,
    ["UGC_Transform_SucPastedLocaPar"] = 50,
    ["UGC_Transform_SucPastedRotPar"] = 50,
    ["UGC_AnimMove_SucAllAinimPar"] = 50,
    ["UGC_AnimMove_SucPastedAnimPar"] = 50,
    ["UGC_AnimMove_SucCopAnimPar"] = 50,
    ["UGC_AnimMove_KeyframItemMaxLimit"] = 50,
    ["UGC_AnimMove_AdvanceItemMaxLimit"] = 50,
    ["UGC_Transform_SucPastedScalePar"] = 50,
    ["UGC_Transform_CannotNegScale"] = 50,
    ["UI_UGC_StarCruise_Version_Item"] = 50,
    ["UI_UGC_StarCruise_Version_Map"] = 50,
    ["UGC_StarCruise_SelfExit"] = 50,
    ["UGC_StarCruise_TimeOver"] = 50,
    ["UGCVehicle_OperateStatus1"] = 50,
    ["UGCVehicle_OperateStatus2"] = 50,
    ["UGCVehicle_OperateStatus3"] = 50,
    ["UGCVehicle_OperateStatus4"] = 50,
    ["UGCVehicle_OperateStatus5"] = 50,
    ["UGCVehicle_OperateStatus6"] = 50,
    ["UGCVehicle_OperateStatus7"] = 50,
    ["UGCVehicle_OperateStatus8"] = 50,
    ["UGCVehicle_NoOperateOnVehicle"] = 50,
    ["Activity_SuperLinear_IsInEnd"] = 50,
    ["Activity_SuperLinear_CurMile"] = 50,
    ["UGC_Editor_CollisionOverflow_Text"] = 50,
    ["UGC_Editor_CollisionBatchSet_Text"] = 50,
    ["UGC_Locomotion_NopParToPaste"] = 50,
    ["UGC_Locomotion_SucCopiedAllPar"] = 50,
    ["UGC_Locomotion_SucCopiedPar"] = 50,
    ["UGC_Locomotion_SucPastedAllPar"] = 50,
    ["Common_String_WB_Not_Installed"] = 50,
    ["Common_String_XHS_Not_Installed"] = 50,
    ["Common_String_Net_Error"] = 50,
    ["UI_Activity_Zupai_GainExtraPointsCount"] = 50,
    ["UI_Activity_Zupai_GainExtraPointsCount_ReturingCooperation"] = 50,
    ["UI_Activity_Zupai_GainExtraPointsCount_ReturingPrivilege"] = 50,
    ["UI_Activity_Zupai_Grading"] = 50,
    ["UI_Activity_Zupai_NotForNow"] = 50,
    ["UI_Activity_Zupai_NotLosePointsCount"] = 50,
    ["UI_Activity_Zupai_Timing"] = 50,
    ["UI_Activity_Zupai_NotLosePoints"] = 50,
    ["UI_Activity_Zupai_GainExtraPoints"] = 50,
    ["UI_Activity_Zupai_GainMultiPoints"] = 50,
    ["UGC_Editor_AdvanceItemMaxLimit"] = 50,
    ["UGC_WayPoint_Cannot_Operate_MultiSelect"] = 50,
    ["UGC_WayPoint_Cannot_SelectOther_MultiSelect"] = 51,
    ["FireworksUseCreditCheckFail"] = 51,
    ["UGC_Creator_AndOthers"] = 51,
    ["UGC_GatherStar_RefreshTime"] = 51,
    ["UGC_GatherStar_MapRefreshed"] = 51,
    ["GiftGiveLevelRequest"] = 51,
    ["Ugc_MapLaunch_Topic_Sensitive"] = 51,
    ["UGC_MultiplePlayer_Tab_MatchHall"] = 51,
    ["UGC_MultiplePlayer_Tab_RoomHall"] = 51,
    ["UGC_Home_Element_Lock"] = 51,
    ["ModuleCommunity_HotRecommend"] = 51,
    ["ModuleCommunity_ModuleResource"] = 51,
    ["UGC_Resource_GiveLike"] = 51,
    ["UGC_Resource_Use"] = 51,
    ["UGC_SearchResult_Empty"] = 51,
    ["UGC_Resource_AddBag"] = 51,
    ["UGC_Resource_RemoveBag"] = 51,
    ["UGC_Resource_LablesLimit"] = 51,
    ["UGC_Resource_DelLimit"] = 51,
    ["UGC_Resource_PasteEmpty"] = 51,
    ["UGC_ResourceDetailPanel_UGCIDStrReplicated"] = 51,
    ["UGC_ResourcePublishedInfoModify_Successfully"] = 51,
    ["UGC_ResType_CantUse"] = 51,
    ["UI_UGCEditor_Bag_Empty_ResMine"] = 51,
    ["UI_UGCEditor_Bag_Empty_Default"] = 51,
    ["UI_UGCEditor_Bag_Empty_ResLib"] = 51,
    ["UI_Countdown_Main_Paper"] = 51,
    ["UGC_Roadpoint_Cannot_Mirror"] = 51,
    ["UI_Activity_Zupai_ChangciCount"] = 51,
    ["UI_Activity_Zupai_ChangciTequan"] = 51,
    ["UI_Activity_Zupai_ChangciShengyu"] = 51,
    ["UGC_Editor_StarParty"] = 51,
    ["UGC_Editor_StarParty_MapOpen"] = 51,
    ["UGC_Editor_StarParty_MapClose"] = 51,
    ["UGC_Object_In_FilterMode"] = 51,
    ["UGC_LockMap_Edit"] = 51,
    ["UGC_LockMap_Outshelf"] = 51,
    ["UGC_LockMap_Exit"] = 51,
    ["UGC_Search_Map_Count"] = 51,
    ["UGC_SkyBox_Txt_Fog"] = 51,
    ["ChatModule_WorldAndLobbyNotOpenTips1"] = 51,
    ["ChatModule_WorldAndLobbyNotOpenTips2"] = 51,
    ["UGC_Editor_Widget_Image"] = 51,
    ["LuckyStar_CoinNotEngough"] = 51,
    ["LuckyStar_RewardDes_1"] = 51,
    ["LuckyStar_AlreadyReceiveByOthers"] = 51,
    ["LuckyStar_NotUnluckRound"] = 51,
    ["LuckyStar_StarNotHave"] = 51,
    ["LuckyStar_AlreadyReceiveFromOthers"] = 51,
    ["UGC_ErrorMsg_CanNotAsTemplate"] = 51,
    ["UGC_RoomList_PassMarks1"] = 51,
    ["UGC_RoomList_PassMarks2"] = 51,
    ["UGC_RoomList_PlayDuration1"] = 51,
    ["UGC_RoomList_PlayDuration2"] = 51,
    ["UGC_RoomList_PlayDuration3"] = 51,
    ["UGC_RoomList_PlayDuration4"] = 51,
    ["UGC_RoomList_PlayDuration5"] = 51,
    ["UI_RedEnvelope_SharedSuccessfully"] = 51,
    ["UI_RedEnvelope_GotChanceByClickLinker"] = 51,
    ["UI_RedEnvelope_YourShareBeenClicked"] = 51,
    ["Matching_Room_Intercept"] = 51,
    ["Matching_Leader_Change"] = 51,
    ["Matching_Leader_Start"] = 51,
    ["Matching_Number_Over"] = 51,
    ["Download_Map_Wait"] = 51,
    ["Download_Map_Fail_Self"] = 51,
    ["Download_Map_Fail_Leader"] = 51,
    ["Client_Low_Leader"] = 51,
    ["Downloading_UGC_Leader"] = 51,
    ["Matching_Faction_Number_Over"] = 51,
    ["UI_RedEnvelope_CountInfo"] = 51,
    ["UI_RedEnvelope_ShareCountInfo"] = 51,
    ["UI_RedEnvelope_ShareLinkCountInfo"] = 51,
    ["UGC_Editor_Prefab_ApplyToAll_Title"] = 51,
    ["UGC_Editor_Prefab_ApplyToAll_Tips"] = 51,
    ["UGC_Editor_Prefab_SaveToCustom_Title"] = 51,
    ["UGC_Editor_Prefab_SaveToCustom_Tips"] = 51,
    ["UGC_Editor_Prefab_Delete_Tips"] = 51,
    ["UGC_Editor_Prefab_Reset_Title"] = 51,
    ["UGC_Editor_Prefab_Reset_Tips"] = 51,
    ["UI_RedEnvelope_ActivityOver"] = 51,
    ["LuckyStar_GiftStar"] = 51,
    ["LuckyStar_RequestStar"] = 51,
    ["LuckyStar_RewardDes_2"] = 51,
    ["LuckyStar_RewardViewTitle_1"] = 51,
    ["LuckyStar_UnlockTip_2"] = 51,
    ["LuckyStar_RewardViewTitle_2"] = 51,
    ["DrawReward_BiDiscountTitle"] = 51,
    ["DrawReward_BiDiscount_Tips1"] = 51,
    ["UI_Raffle_BiDiscount_Tips2"] = 51,
    ["UI_Raffle_BiDiscount_CountDown"] = 51,
    ["UI_Raffle_ExtraReward_NotGet"] = 51,
    ["UI_Raffle_ExtraReward_Get"] = 51,
    ["UGC_StarCruise_DailyRewardDesc"] = 51,
    ["UGC_StarCruise_PrizeTips"] = 51,
    ["UGC_Object_Cant_Bind_Custom"] = 51,
    ["UGC_MapSearch_NoResult"] = 51,
    ["IQ_Game_To_Be_Start"] = 51,
    ["IQ_Game_Register_Success"] = 51,
    ["IQ_Game_Waiting"] = 51,
    ["IQ_Game_Finished"] = 52,
    ["IQ_Game_Started"] = 52,
    ["IQ_Game_Remove_Card_Desc"] = 52,
    ["IQ_Game_Revive_Card_Desc"] = 52,
    ["IQ_Game_Start_Time"] = 52,
    ["IQ_Game_Has_Started"] = 52,
    ["IQ_Help_Record_Rule"] = 52,
    ["IQ_Help_Record_Rule_1"] = 52,
    ["IQ_Game_Enter_PopUp_Txt"] = 52,
    ["UGC_Park_HotActivity"] = 52,
    ["UGC_Object_Cant_Bind_Limit"] = 52,
    ["QRScan_Fail_InUGCEditor"] = 52,
    ["QRScan_Fail_InHomeEditor"] = 52,
    ["QRScan_Fail_InGame"] = 52,
    ["QRScan_Fail_InPlayingHome"] = 52,
    ["QRScan_Fail_NotInLobby"] = 52,
    ["UGC_Prefab_DeleteCustom"] = 52,
    ["UGC_Prefab_ResetToDefault"] = 52,
    ["UGC_Prefab_ApplyToScene"] = 52,
    ["IQ_Game_Resurrection_Click_Hint"] = 52,
    ["IQ_Game_Use_RemoveWrong_Prop_Failed"] = 52,
    ["IQ_Game_Use_Resurrection_Prop_Failed"] = 52,
    ["IQ_Game_Current_WinNum"] = 52,
    ["IQ_Game_Resurrection_Dialog_Content"] = 52,
    ["IQ_Game_Resurrection_Dialog_CountDown"] = 52,
    ["UGC_Map_TestMapName"] = 52,
    ["UGC_Map_TestRoomName"] = 52,
    ["UGC_Editor_TestRoomDissolve"] = 52,
    ["UGC_Editor_TestRoomAutoDissolve"] = 52,
    ["CustomRoom_NoticeForCreator_InviteAllow"] = 52,
    ["CustomRoom_NoticeForCreator_InviteFull"] = 52,
    ["CustomRoom_NoticeForCreator_TestRule"] = 52,
    ["CustomRoom_InviteTestMsg"] = 52,
    ["CustomRoom_AtLessPlayerTip"] = 52,
    ["InLevel_AllCompleteToRelease"] = 52,
    ["InLevel_TestCompleteToReturn"] = 52,
    ["InLevel_TestCompleteToRelease"] = 52,
    ["InLevel_TestDisable"] = 52,
    ["CustomRoom_GeneralTips_FuncBlock"] = 52,
    ["CustomRoom_InvitationFull"] = 52,
    ["UGC_Editor_WaitForExceptionTips"] = 52,
    ["UGC_Editor_Prefab_SyncToCustom_Tips"] = 52,
    ["UGC_Editor_Prefab_SaveAsCustom_Tips"] = 52,
    ["UGC_RoomList_PlayTogether_PlayTogether"] = 52,
    ["UGC_RoomList_PlayTogether_Match"] = 52,
    ["UGC_RoomList_PlayTogether_SingleHint"] = 52,
    ["UGC_RoomList_PlayTogetherNoMatch_Wait"] = 52,
    ["UGC_RoomList_PlayTogetherNoMatch_Hint"] = 52,
    ["UGC_Roadpoint_CannotLess3"] = 52,
    ["UGC_Roadpoint_Reach_MaxNumber"] = 52,
    ["UGC_Roadpoint_Cannot_Operate"] = 52,
    ["UGC_Cant_Attach"] = 52,
    ["UGC_Cant_Attach_Model"] = 52,
    ["UGC_Attach_Max"] = 52,
    ["UGC_Cant_Custom_MeshModing"] = 52,
    ["UGC_Cant_AddMesh_Limited"] = 52,
    ["UGC_Cant_ChangeModelParam"] = 52,
    ["UGC_Cant_TooManyBooleanModelInMap"] = 52,
    ["UGC_Cant_TooManyBooleanModelAttached"] = 52,
    ["UGC_Map_IsResumeMapFromPublish"] = 52,
    ["UGC_Modeling_NoPhyxWhenComplexCollision"] = 52,
    ["UGC_Modeling_NoComplexCollisionWhenPhyx"] = 52,
    ["PlayerReturn_PopTop"] = 52,
    ["UI_Return_PopTopS3_01"] = 52,
    ["UI_Return_PopTopS3_02"] = 52,
    ["FriendShipFire_WeekLimit"] = 52,
    ["FriendShipFire_Team"] = 52,
    ["FriendShipFire_Team_Return"] = 52,
    ["Community_Dragon_Full"] = 52,
    ["Community_Dragon_Kick"] = 52,
    ["Community_Dragon_Disable"] = 52,
    ["Community_Dragon_View_Free"] = 52,
    ["Community_Dragon_View_Force"] = 52,
    ["UGC_Alignment_CannotAliRot"] = 52,
    ["UGC_Alignment_CannotAliScal"] = 52,
    ["UGC_Alignment_CannotAliScalLimit"] = 52,
    ["UGC_Alignment_CannotAliGroup"] = 52,
    ["UGC_Transform_SucCopiedsizePar"] = 52,
    ["UGC_Alignment_CannotAliLockedObj"] = 52,
    ["UGC_AIGroup_SeedCheck"] = 52,
    ["UGC_AIGen_SelectedImageIsTooSmall"] = 52,
    ["UGC_AIGenEditor_BuildSeedHintText"] = 52,
    ["UGC_AIGenEditor_UnselectedGraph"] = 52,
    ["UGC_AIGen_ImageOutOfMaxAspectRatio"] = 52,
    ["UGC_AIGen_ImagePreprocessingSingleStageTimeout"] = 52,
    ["UGC_AIGen_ImageFormatErrrorOpenFailed"] = 52,
    ["UGC_AIGenEditor_GraphAuditFailed"] = 52,
    ["UI_SKILL_CASTING"] = 52,
    ["UI_SKILL_BARRIER"] = 52,
    ["UI_UGC_UserSpecificaton"] = 52,
    ["IQ_Game_Ready_Exit"] = 52,
    ["IQ_Game_Anwser_Exit"] = 52,
    ["IQ_Game_Ready_Error"] = 52,
    ["IQ_Game_Result_Winner_count"] = 52,
    ["Return_OutfitDelivery_SignIn_Day"] = 52,
    ["Ultraman_Create_Team"] = 52,
    ["Ultraman_Team_Reward"] = 52,
    ["Ultraman_Exit_Team"] = 52,
    ["Ultraman_Kick_Out_Team"] = 52,
    ["Ultraman_InviteToClubLeftCD"] = 52,
    ["Ultraman_NoClub"] = 53,
    ["Ultraman_InviteMessageSend"] = 53,
    ["Ultraman_Task_Lock"] = 53,
    ["Ultraman_Task_Tab_1"] = 53,
    ["Ultraman_Task_Tab_2"] = 53,
    ["Ultraman_Team_Full"] = 53,
    ["Ultraman_Team_Already"] = 53,
    ["Ultraman_Team_Tips"] = 53,
    ["Ultraman_Plot_Lock"] = 53,
    ["Ultraman_Progress"] = 53,
    ["Ultraman_Join_Team"] = 53,
    ["Ultraman_Team_Empty"] = 53,
    ["Ultraman_Task_Pass"] = 53,
    ["Ultraman_Team_Exit_Fail"] = 53,
    ["UI_MiniGame_iOS_Not_Support_Hint"] = 53,
    ["UGC_TravelTogether_MatchTime"] = 53,
    ["UGC_TravelTogether_MatchSuccess"] = 53,
    ["UGC_TravelTogether_CountDown"] = 53,
    ["Common_NextStep_WithTime"] = 53,
    ["UGC_TogetherRecords_Success"] = 53,
    ["UGC_TogetherRecords_PlayTimes"] = 53,
    ["UGC_TravelTogether_InviteTips"] = 53,
    ["UGC_TravelTogether_CountdownText"] = 53,
    ["UGC_TravelTogether_RoomDestroyed"] = 53,
    ["UGC_TravelTogether_MapDownload_Fail"] = 53,
    ["UGC_TravelTogether_Title"] = 53,
    ["UGC_TravelTogether_MatchBtn"] = 53,
    ["UGC_TravelTogether_Match_TeamError"] = 53,
    ["UGC_TravelTogether_EnterCountdownText"] = 53,
    ["UGC_TravelTogether_EndVoteCountDown"] = 53,
    ["UGC_TravelTogether_Tip"] = 53,
    ["UGC_TravelTogether_EditorRoomCountDown"] = 53,
    ["UGC_TravelTogether_Tiptitle"] = 53,
    ["UGC_TravelTogether_CountDown_Again"] = 53,
    ["Activity_Team_Already"] = 53,
    ["Activity_Team_Empty"] = 53,
    ["Add_Player_Friend"] = 53,
    ["Come_Experience_Mathch"] = 53,
    ["Common_MaxWinCount"] = 53,
    ["Common_Screened"] = 53,
    ["Common_TrophyCount"] = 53,
    ["Common_WinCount"] = 53,
    ["Common_ContinuesWin"] = 53,
    ["ConcertMain_Close"] = 53,
    ["Enter_Room_Number"] = 53,
    ["Event_Farmingnotteam"] = 53,
    ["Exclusive_Vehicle_HasNoEmptySeat"] = 53,
    ["Exclusive_Vehicle_IllegalLocation"] = 53,
    ["Exclusive_Vehicle_IllegalMap"] = 53,
    ["Exclusive_Vehicle_IllegalState"] = 53,
    ["Exclusive_Vehicle_Invite_Fail"] = 53,
    ["Exclusive_Vehicle_Invite_MissingTarget"] = 53,
    ["Exclusive_Vehicle_Invite_Sent"] = 53,
    ["Exclusive_Vehicle_Passenger_IllegalState"] = 53,
    ["Exclusive_Vehicle_Passenger_Reject"] = 53,
    ["Exclusive_Vehicle_TooMany"] = 53,
    ["Exclusive_Vehicle_SuitSkill_IllegalLocation"] = 64,
    ["Exclusive_Vehicle_SuitSkill_IllegalMap"] = 64,
    ["Exclusive_Vehicle_SuitSkill_IllegalCommon"] = 64,
    ["Exclusive_Vehicle_SuitSkill_TooMany"] = 64,
    ["Exclusive_Vehicle_SuitSkill_IllegalState"] = 64,
    ["SuitSkill_Waiting_Pak_Ready"] = 65,
    ["Farmyard_Social_Button_1"] = 53,
    ["FirearmOccupancyValueIsFull"] = 53,
    ["FriendOfflineTip"] = 53,
    ["Home_Delete_SpawnPoint_Tips"] = 53,
    ["InLevel_GoalGuideLock"] = 53,
    ["InLevel_GoalGuideTips"] = 53,
    ["InLevel_QST_IntegralCommonAdditionalItem"] = 53,
    ["InLevel_QST_IntegralCommonProtectItem"] = 53,
    ["InLevel_QST_IntegralTeamAdditionalItem"] = 53,
    ["InLevel_QST_IntegralTeamProtectItem"] = 53,
    ["InLevel_RankDataNullTips"] = 53,
    ["InLevel_SingleTestRankingTips"] = 53,
    ["InLevel_TaskComplete"] = 53,
    ["InLevel_TaskNetConnectBusy"] = 53,
    ["InLevel_TaskReward"] = 53,
    ["InLevel_TaskRewardTips"] = 53,
    ["InLevel_TaskRewardLapse"] = 53,
    ["InLevel_TaskStart"] = 53,
    ["InLevel_TaskWaitForSubmit"] = 53,
    ["IQ_Game_Result_Error"] = 53,
    ["IQ_Game_Use_RemoveWrong_Num_Not_Enough"] = 53,
    ["KeepAtLeastOneAction"] = 53,
    ["Lobby_EnterFailed_LackOfChunk"] = 53,
    ["Lobby_InviteGetOnVehicle"] = 53,
    ["Lobby_UGCLobbyNotOpened"] = 53,
    ["Newcomer_tips"] = 53,
    ["MatchType_Lightning"] = 53,
    ["No_Tips_Today"] = 53,
    ["NoDrop_Dead"] = 53,
    ["PlayerInfo_MyRecord"] = 53,
    ["Please_Generate_Group_Photo"] = 53,
    ["PreDownload_DownloadFinished"] = 53,
    ["PreDownload_PreDownloadBookTip1"] = 53,
    ["PreDownload_PreDownloadBookTip2"] = 53,
    ["PreDownload_WaitOther"] = 53,
    ["PreDownload_Del"] = 53,
    ["Prop_LifeSaver_Name"] = 53,
    ["Prop_LifeSaver_Tips"] = 53,
    ["QST_Champion"] = 53,
    ["QST_Draw"] = 53,
    ["QST_LevelDimensionScore"] = 53,
    ["QST_LevelKill"] = 53,
    ["QST_LevelPass"] = 53,
    ["QST_levelSurvive"] = 53,
    ["QST_LevelTeamRank"] = 54,
    ["QST_Rank"] = 54,
    ["Scenegiftpackagetips"] = 54,
    ["Send_Squad_Invitations"] = 54,
    ["Set_EditKey_Classic_FinalAbility"] = 54,
    ["SheJi_duiju1"] = 54,
    ["Friend_MatchTime_OverOneMinute"] = 54,
    ["Subscribe_Self_Fail"] = 54,
    ["Task_MarukoDiscountText"] = 54,
    ["Task_Subscribe_QQrobot"] = 54,
    ["Task_Subscribe_Qqrobot_Failed"] = 54,
    ["Task_Subscribe_Qqrobot_Success"] = 54,
    ["Team_Invite_Tips"] = 54,
    ["TellMsgSendSucceed"] = 54,
    ["UGC LastWeapon Mustkeep"] = 54,
    ["UGC_AI_TextToPic_Tips"] = 54,
    ["UGC_Asset_MapData_SizeOverLimit_Tip"] = 54,
    ["UGC_Asset_Resource_QuantityOverLimit_Tip"] = 54,
    ["UGC_Asset_Resource_SizeOverLimit_Tip"] = 54,
    ["UGC_CanPhysics_DisableLocomotor"] = 54,
    ["UGC_Check_Developer_Agreement_Option"] = 54,
    ["UGC_Common_CoCreate"] = 54,
    ["UGC_Common_Creator"] = 54,
    ["UGC_Cover_CannotUse"] = 54,
    ["UGC_Cover_SelectExceedRank"] = 54,
    ["UGC_Cover_SelectLeastOne"] = 54,
    ["UGC_Cover_SelectMaximum"] = 54,
    ["UGC_Cover_SetFail"] = 54,
    ["UGC_Cover_SetSuccess"] = 54,
    ["UGC_CustomImage_Delete"] = 54,
    ["UGC_CustomImage_Download"] = 54,
    ["UGC_CustomImage_LackName"] = 54,
    ["UGC_CustomImage_LoadFail"] = 54,
    ["UGC_CustomImage_NeedLoad"] = 54,
    ["UGC_CustomImage_NoPass"] = 54,
    ["UGC_CustomImage_NotAvailable"] = 54,
    ["UGC_CustomImage_Pass"] = 54,
    ["UGC_Discard_Disable"] = 54,
    ["UGC_DiscardWeapon_Disable"] = 54,
    ["UGC_DynamicMerge_BanIssue"] = 54,
    ["UGC_DynamicMerge_BanSave"] = 54,
    ["UGC_DynamicMerge_CloseAllMerge"] = 54,
    ["UGC_DynamicMerge_CloseMerge"] = 54,
    ["UGC_DynamicMerge_OpenAllMerge"] = 54,
    ["UGC_DynamicMerge_OpenMerge"] = 54,
    ["UGC_ActorMerge_OverOccupy"] = 54,
    ["UGC_ActorMerge_CloseMergeButton"] = 54,
    ["UGC_Editor_BagPackIsFull"] = 54,
    ["UGC_Editor_CloseSingleSaveFail"] = 54,
    ["UGC_Editor_CopyDataTips"] = 54,
    ["UGC_Editor_CreateDataTips"] = 54,
    ["UGC_Editor_CreateGroup_DownloadSuccess"] = 54,
    ["UGC_Editor_DataInvalidTips"] = 54,
    ["UGC_Editor_DeleteDataTips"] = 54,
    ["UGC_Editor_DialogueLinkIsNull"] = 54,
    ["UGC_Editor_DialogueLinkTips"] = 54,
    ["UGC_Editor_DialogueOccupiedTips"] = 54,
    ["UGC_Editor_MultiFingerWhenCopy"] = 54,
    ["UGC_Editor_MutiDeleteTips"] = 54,
    ["UGC_Editor_OldGroup_DownloadFail"] = 54,
    ["UGC_Editor_Prefab_Save_ConfirmChange"] = 54,
    ["UGC_Editor_Prefab_Save_Preset"] = 54,
    ["UGC_Editor_Prefab_Save_Tip"] = 54,
    ["UGC_Editor_CopyAchievementDataTips"] = 54,
    ["UGC_Editor_CreateAchievementDataTips"] = 54,
    ["UGC_Editor_SaveAchievementDataTips"] = 54,
    ["UGC_Editor_AchievementNameIsNull"] = 54,
    ["UGC_Editor_AchievementDeleteDataTips"] = 54,
    ["UGC_Editor_AchievementMutiDeleteTips"] = 54,
    ["UGC_Editor_OriginalAchievementDeleteFail"] = 54,
    ["UGC_Editor_AchievementIsFull"] = 54,
    ["UGC_Editor_AchievementDataIsNull"] = 54,
    ["UGC_Editor_PreTaskName"] = 54,
    ["UGC_Editor_RankDataChangeTips"] = 54,
    ["UGC_Editor_RankDataDeleteTips"] = 54,
    ["UGC_Editor_RankDataErrorTips"] = 54,
    ["UGC_Editor_RankDataFullTips"] = 54,
    ["UGC_Editor_RankNameInvalidTips"] = 54,
    ["UGC_Editor_ReceiveTaskLinkFailTips"] = 54,
    ["UGC_Editor_SaveDataTips"] = 54,
    ["UGC_Editor_SubmitTaskLinkFailTips"] = 54,
    ["UGC_Editor_TaskConditionFullTips"] = 54,
    ["UGC_Editor_TaskConditionTips"] = 54,
    ["UGC_Editor_TaskDataIsNull"] = 54,
    ["UGC_Editor_TaskDescriptionTips"] = 54,
    ["UGC_Editor_TaskIsFull"] = 54,
    ["UGC_Editor_TaskLineLimitTips"] = 54,
    ["UGC_Editor_TaskNameIsNull"] = 54,
    ["UGC_Editor_TaskNormalDelete"] = 54,
    ["UGC_Editor_TaskProgressTips"] = 54,
    ["UGC_Editor_TaskRewardDes"] = 54,
    ["UGC_Editor_TaskRewardTips"] = 54,
    ["UGC_Editor_TaskSameConditionTips"] = 54,
    ["UGC_Editor_TooMuchDataTips"] = 54,
    ["UGC_Editor_UIEditor_CurrentCanvasRatio"] = 54,
    ["UGC_Editor_UIEditor_GroupLimit_Tip"] = 54,
    ["UGC_Editor_UIEditor_SwitchingCanvasRatio"] = 54,
    ["UGC_Effector_CustomNoSupport"] = 54,
    ["UGC_Effector_ExceedTheLimit"] = 54,
    ["UGC_Effector_HomeLimit"] = 54,
    ["UGC_Effector_IncreaseTheVolume"] = 55,
    ["UGC_Effector_NoEffectToPaste"] = 55,
    ["UGC_Effector_SucCopiedAllEffect"] = 55,
    ["UGC_Effector_SucCopiedEffect"] = 55,
    ["UGC_Effector_SucPastedAllEffect"] = 55,
    ["UGC_Effector_TurnOnTheVolume"] = 55,
    ["UGC_Eveluate_ConfirmNegative"] = 55,
    ["UGC_Eveluate_ConfirmSubmit"] = 55,
    ["UGC_Eveluate_ConfirmSubmit_Remark"] = 55,
    ["UGC_Eveluate_ConfirmSubmit_Score"] = 55,
    ["UGC_Eveluate_Eveluated"] = 55,
    ["UGC_Eveluate_NotChangeEveluat"] = 55,
    ["UGC_Group_CantContainAsset"] = 55,
    ["UGC_GroupPublish_CanUseInPayMapOption_TipsInfo"] = 55,
    ["UGC_identity_Contract_FinishScroll"] = 55,
    ["UGC_identity_Contract_Signing"] = 55,
    ["UGC_identity_Contract_Signing_Check"] = 55,
    ["UGC_identity_Contract_Signing_Content"] = 55,
    ["UGC_identity_Contract_Signing_Fail"] = 55,
    ["UGC_identity_Contract_Signing_Rollback"] = 55,
    ["UGC_identity_Contract_Signing_Start"] = 55,
    ["UGC_Info_AddDirectorTemplateSucc"] = 55,
    ["UGC_LastWeapon_Mustkeep"] = 55,
    ["UGC_Map_IsRemoveExcellentMap"] = 55,
    ["UGC_Map_MapData_SizeOverLimit_Tip"] = 55,
    ["UGC_Map_Resource_illegal_PreventLoad_Tip"] = 55,
    ["UGC_Map_Resource_illegal_PreventSave_Tip"] = 55,
    ["UGC_Map_Resource_QuantityOverLimit_Tip"] = 55,
    ["UGC_Map_Resource_SizeOverLimit_Tip"] = 55,
    ["UGC_MapLaunch_SizeLimit"] = 55,
    ["UGC_MapSave_SizeLimit"] = 55,
    ["UGC_MidJoin_ExitRoom"] = 55,
    ["UGC_MidJoin_JoinBattleFailTip"] = 55,
    ["UGC_MidJoin_NoInvite"] = 55,
    ["UGC_MidJoin_StayRoom"] = 55,
    ["UGC_Pause_NotEffect"] = 55,
    ["UGC_Photo_DeleteFail"] = 55,
    ["UGC_Photo_DeleteSuccess"] = 55,
    ["UGC_Photo_IsUploading"] = 55,
    ["UGC_Photo_ReplaceCancelNotice"] = 55,
    ["UGC_Photo_ReplaceConfirmNotice"] = 55,
    ["UGC_Photo_ReplaceFail"] = 55,
    ["UGC_Photo_ReplaceSuccess"] = 55,
    ["UGC_Photo_SaveFail"] = 55,
    ["UGC_Photo_SaveSuccess"] = 55,
    ["UGC_Photo_Saving"] = 55,
    ["UGC_Runtime_CanNotDragFloor"] = 55,
    ["UGC_Runtime_CanNotInteractGrabbed"] = 55,
    ["UGC_Runtime_OtherInteractTheActor"] = 55,
    ["UGC_SaveRecord_Failed"] = 55,
    ["UGC_SaveRecord_Success"] = 55,
    ["UGC_Scene_Paused"] = 55,
    ["UGC_Scene_Resumed"] = 55,
    ["UGC_Shop_AddGoodsFailed"] = 55,
    ["UGC_Shop_AddShopFailed"] = 55,
    ["UGC_Shop_AddShopShelfFailed"] = 55,
    ["UGC_Shop_BuyFail_BagNotEnough"] = 55,
    ["UGC_Shop_BuyFail_MoneyNotEnough"] = 55,
    ["UGC_Shop_CoatStarDiamondGoodAudit_NotReady"] = 55,
    ["UGC_Shop_Currency_Gold"] = 55,
    ["UGC_Shop_Currency_SilverCoin"] = 55,
    ["UGC_Shop_Currency_StarCoin"] = 55,
    ["UGC_Shop_Currency_StarDiamond"] = 55,
    ["UGC_Shop_DefaultShopName"] = 55,
    ["UGC_Shop_DefaultShopShelfName"] = 55,
    ["UGC_Shop_DeleteItemConfirmTip"] = 55,
    ["UGC_Shop_Draft_BuyStarDiamondGoodTip"] = 55,
    ["UGC_Shop_GoddsDesc_NotValid"] = 55,
    ["UGC_Shop_GoddsName_NotValid"] = 55,
    ["UGC_Shop_GoodsCount"] = 55,
    ["UGC_Shop_ItemsCount"] = 55,
    ["UGC_Shop_Name_NotValid"] = 55,
    ["UGC_Shop_OpenEvent_CanNotRepeat"] = 55,
    ["UGC_Shop_RemoveGoodsVertify"] = 55,
    ["UGC_Shop_RemoveShopShelfVertify"] = 55,
    ["UGC_Shop_RemoveShopVertify"] = 55,
    ["UGC_Shop_ShopShelfName_NotValid"] = 55,
    ["UGC_Shop_SinglePlayer_NotAllowToBuyStarDiamondGood"] = 55,
    ["UGC_Shop_Tab_Custom"] = 55,
    ["UGC_Shop_Tab_Offical"] = 55,
    ["UGC_Shop_TotalGoodsCount"] = 55,
    ["UGC_ShopBuy_Fail"] = 55,
    ["UGC_ShopBuy_HasGroupForbidUseInPayMap_Tip"] = 55,
    ["UGC_ShopBuy_Restart_Tip"] = 55,
    ["UGC_ShopBuy_Start_Notarize_Tip"] = 55,
    ["UGC_ShopBuy_Succeed"] = 55,
    ["UGC_SimpleEditMode_Attach_Forbidden"] = 55,
    ["UGC_SimpleEditMode_Default_Home"] = 55,
    ["UGC_SimpleEditMode_Default_UGCEditor"] = 55,
    ["UGC_SimpleEditMode_FixedXB_Disable"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Delete"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Hide"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Mirror"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Move"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Rotate"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Scale"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_UndoRedo"] = 55,
    ["UGC_SimpleEditMode_LockUnderfoot_Other"] = 55,
    ["UGC_SimpleEditMode_StartPoint_Illegal"] = 55,
    ["UGC_SpawnPoint_TeamSet_Less_MapCamp_Tips"] = 55,
    ["UGC_StaticMeshFlyModeTip"] = 56,
    ["UGC_TestTab_Check_Fail"] = 56,
    ["UGC_TestTab_GroupCheck_Fail"] = 56,
    ["UGC_UpgradeMap_DoubleCheckTips"] = 56,
    ["UGC_UpgradeMap_Fail"] = 56,
    ["UGC_UpgradeMap_NotEnoughSpace"] = 56,
    ["UGC_UpgradeMap_Success"] = 56,
    ["UGC_UpgradePublishedMap_Success"] = 56,
    ["UGC_UpgradeUniCreateMap_Success"] = 56,
    ["UGC_UpgradeMap_LowVersionTips"] = 56,
    ["UGC_SkillEditor_CloneSkill_Success"] = 65,
    ["UGC_SkillEditor_CloneMultiSkill_Success"] = 65,
    ["UGC_SkillEditor_CloneProjectile_Success"] = 65,
    ["UGC_SkillEditor_CloneBuff_Success"] = 65,
    ["UGC_SkillEditor_CloneSkillEffect_Success"] = 65,
    ["UGC_SkillEditor_CloneFxSound_Success"] = 65,
    ["UGC_SkillEditor_Clone_SkillNum_Fail"] = 65,
    ["UGC_SkillEditor_Clone_MultiSkillNum_Fail"] = 65,
    ["UGC_SkillEditor_Clone_ProjectileNum_Fail"] = 65,
    ["UGC_SkillEditor_Clone_BuffNum_Fail"] = 65,
    ["UGC_SkillEditor_Clone_SkillEffectNum_Fail"] = 65,
    ["UGC_SkillEditor_Clone_FxSoundNum_Fail"] = 66,
    ["UGC_SkillEditor_Clone_NameLength_Fail"] = 66,
    ["UGCLobbyNav_ConfirmGoToLobby"] = 56,
    ["UGCProto_ProtosPage_Develop_ProtoTabTitle"] = 56,
    ["UGCProto_ProtosPage_Develop_ProtoTitle"] = 56,
    ["UGCProto_ProtosPage_InAppPurchase_ProtoTabTitle"] = 56,
    ["UGCProto_ProtosPage_InAppPurchase_ProtoTitle"] = 56,
    ["UGCProto_ProtosPage_PanelTitle"] = 56,
    ["UGCProto_SingleProto_Develop_CheckBoxTip"] = 56,
    ["UGCProto_SingleProto_Develop_PanelTitle"] = 56,
    ["UGCProto_SingleProto_Develop_ProtoTitle"] = 56,
    ["UGCProto_SingleProto_InAppPurchase_CheckBoxTip"] = 56,
    ["UGCProto_SingleProto_InAppPurchase_PanelTitle"] = 56,
    ["UGCProto_SingleProto_InAppPurchase_ProtoTitle"] = 56,
    ["UI_Activity_QQSpeed_GetAccelerator"] = 56,
    ["UI_Activity_QQSpeed_GetAllRewardsSuccessTip"] = 56,
    ["UI_Activity_QQSpeed_LotteryFullTip"] = 56,
    ["UI_Activity_QQSpeed_LotteryTip"] = 56,
    ["UI_Activity_QQSpeed_RemainingLotteryTip"] = 56,
    ["UI_Activity_QQSpeed_SpeedUpOnce"] = 56,
    ["UI_Change_Catchphrase"] = 56,
    ["UI_Lottery_KongFuPanda_TitleZhen"] = 56,
    ["UI_Lottery_KongFuPanda_Zhen"] = 56,
    ["UI_Lottery_KongFuPanda_ZhenBtn"] = 56,
    ["UI_Qualifying"] = 56,
    ["Unpickable_Number"] = 56,
    ["Unpickable_Space"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content1"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content10"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content11"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content12"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content13"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content14"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content15"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content16"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content17"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content18"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content19"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content2"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content20"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content3"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content4"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content5"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content6"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content7"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content8"] = 56,
    ["UI_InLevel_Integral_ReputationScore_content9"] = 56,
    ["UI_InLevel_FinalAccount_ReputationScore_1"] = 56,
    ["UI_InLevel_FinalAccount_ReputationScore_2"] = 56,
    ["UI_InLevel_FinalAccount_ReputationScore_3"] = 56,
    ["UI_InLevel_FinalAccount_ReputationScore_4"] = 56,
    ["UI_InLevel_Integral_ReputationScore_mail1"] = 56,
    ["UI_InLevel_Integral_ReputationScore_mail2"] = 56,
    ["UI_InLevel_Integral_ReputationScore_title"] = 56,
    ["UI_InLevel_Integral_ReputationScore_title1"] = 56,
    ["UI_InLevel_Integral_ReputationScore_title2"] = 56,
    ["UI_InLevel_Integral_ReputationScore_title3"] = 56,
    ["UI_InLevel_Integral_ReputationScore_title4"] = 56,
    ["UI_Integral_Reputation_Points_hangup_Hint"] = 56,
    ["UI_Integral_Reputation_Points_Quit_Button"] = 56,
    ["UI_Integral_Reputation_Points_Quit_Hint"] = 56,
    ["UI_Preparations_MapItem1"] = 56,
    ["UI_Preparations_MapSelectItem1"] = 56,
    ["UI_Preparations_MapSelectItem2"] = 56,
    ["UI_Preparations_MapSelectItem3"] = 56,
    ["WeaponModuleModificationTips"] = 56,
    ["WeaponHitPointTips"] = 56,
    ["UGC_CompCapLimit_Tips"] = 56,
    ["UGC_CompNumLimit_Tips"] = 56,
    ["UGC_MapCapLimit_Tips"] = 56,
    ["UGC_ResCapLimit_Tips"] = 56,
    ["UGC_LaunchLimit_RichText"] = 56,
    ["FinalAblity_Tip1"] = 56,
    ["Common_DownloadPak"] = 56,
    ["UI_Prop_EnergyDisable"] = 56,
    ["UGC_MapCollection_Create_Limit"] = 56,
    ["UGC_MapCollection_Add_Suf"] = 56,
    ["UGC_MapCollection_Add_Repeat"] = 56,
    ["UGC_MapCollection_Add_Limit"] = 56,
    ["LightningGameCommonQuitTip"] = 56,
    ["LightningGameQuitTip"] = 56,
    ["UGC_MapDislike_Suf"] = 56,
    ["UGC_MapDislike_NotSelected"] = 56,
    ["PlayerInfo_Photo_Check_Tip"] = 56,
    ["UGC_RoomList_PlayTogether_UGCSaveDB"] = 56,
    ["UGC_RoomList_OnlyMultiSaveDB_Desc"] = 56,
    ["UGC_RoomList_CanSaveDB_Desc"] = 56,
    ["UGC_StandaloneSave_GoodsLimitWorkTips"] = 56,
    ["UGC_StandaloneSave_EditrorSwitchTips"] = 56,
    ["RandomJumpDontFindCanPlayGame"] = 56,
    ["RandomJumpDontFindGameAdaptTeamNum"] = 56,
    ["RandomJumpPlayGameFinishTask"] = 56,
    ["RandomJumpChangeGameAndBeginToFinishTask"] = 57,
    ["RandomJumpBeginMatch"] = 57,
    ["UGC_BagSpaceLimit_Failed"] = 57,
    ["MilestoneGift_limit"] = 57,
    ["MallBuyLimit"] = 57,
    ["MallBuyLimit_Only"] = 57,
    ["UGC_Skill_Refresh_Weapon_Hiding"] = 57,
    ["ClearLotteryRedDotTip"] = 57,
    ["UI_ActivityMusicOrderGetWayTitle"] = 57,
    ["UI_ActivityMusicOrderTodayCollect"] = 57,
    ["UI_ActivityMusicOrderTodayCollectLimit"] = 57,
    ["UGC_Shop_BuyFail_StockNotEnough"] = 57,
    ["UGC_Shop_BuySuccess_StockNum"] = 57,
    ["UGC_Signal_SignalCopyTip"] = 57,
    ["UGC_Signal_SignalDetail"] = 57,
    ["UGC_Signal_SignalQuery"] = 57,
    ["UGC_Signal_SignalQuerySendRecFilter"] = 57,
    ["UGC_Signal_ChangeNameTip"] = 57,
    ["UGC_Signal_ChangeNameTip1"] = 57,
    ["UGC_Signal_ChangeNameTip2"] = 57,
    ["UGC_Signal_ChangeNameTip3"] = 57,
    ["UGC_Signal_SignalQueryTip1"] = 57,
    ["UGC_Signal_SignalQueryTip2"] = 57,
    ["UGC_SaleItem_Disable"] = 57,
    ["UGC_SimpleEditMode_Function_Disable"] = 57,
    ["UGC_StarParty_New_Matching"] = 57,
    ["UGC_StarParty_New_MatchTime"] = 57,
    ["UGC_StarParty_New_MatchSuccess"] = 57,
    ["UGC_StarParty_New_MatchFailTips"] = 57,
    ["UGC_StarParty_New_Will_Choose_Map"] = 57,
    ["UGC_StarParty_New_Team_Waiting_Confirm"] = 57,
    ["UGC_StarParty_New_Random_Choosing_Map"] = 57,
    ["UGC_StarParty_New_Current_Map"] = 57,
    ["UGC_StarParty_New_Waiting_DownloadMap"] = 57,
    ["UGC_StarParty_New_Will_Go_Battle"] = 57,
    ["UGC_StarParty_New_Go_Battle_CountDown"] = 57,
    ["Lightning_CurrentSeasonMaxMedal"] = 57,
    ["UI_Teleport_InvalidTargetLocation"] = 57,
    ["UGC_EditorEdit_AlignFail"] = 57,
    ["UGC_EditorEdit_AlignCanvas"] = 57,
    ["UGC_EditorEdit_AlignGroup"] = 57,
    ["UI_InLevelInvitePlayer"] = 57,
    ["UGC_Map_Demo_SHARE"] = 57,
    ["UI_SysSetting_Password_Window_Title_Change"] = 57,
    ["UI_SysSetting_Password_Window_Title_Set"] = 57,
    ["UI_SysSetting_Password_Tab_Title"] = 57,
    ["UI_SysSetting_Password_Toast_Success"] = 57,
    ["UI_SysSetting_Password_Toast_Change"] = 57,
    ["UI_SysSetting_Password_Toast_Close"] = 57,
    ["UI_SysSetting_Password_Application_Content"] = 57,
    ["UI_SysSetting_Password_Toast_Revoke"] = 57,
    ["UI_SysSetting_Password_Window_Btn_Revoke"] = 57,
    ["UI_SysSetting_Password_Window_Btn_Send"] = 57,
    ["UI_SysSetting_Password_Application_Time"] = 57,
    ["UI_SysSetting_Password_Open_Content"] = 57,
    ["UI_SysSetting_Password_Close_Content"] = 57,
    ["UI_SysSetting_Password_Toast_OpenPass"] = 57,
    ["UI_SysSetting_Password_Pass_Content"] = 57,
    ["InLevel_QST_ContinueWin"] = 57,
    ["UGC_MapRes_ViolationsLimit"] = 57,
    ["UGC_SocialTab_Empty"] = 57,
    ["UGC_SocialTab_EditTip"] = 57,
    ["Prop_Lovechannel_Name"] = 57,
    ["Prop_Cupidarrow_Name"] = 57,
    ["Prop_Lovebomb_Name"] = 57,
    ["Prop_Lovefireworks_Name"] = 57,
    ["Prop_Lovechannel_Tips"] = 57,
    ["Prop_Cupidarrow_Tips"] = 57,
    ["Prop_Lovebomb_Tips"] = 57,
    ["Prop_Lovefireworks_Tips"] = 57,
    ["UGC_MapRiseTip_StillPlaying"] = 57,
    ["UGC_MapRiseTip_ThinkAgain"] = 57,
    ["UGC_MapRiseTip_NoRemind"] = 57,
    ["Activity_Flychess_DataLoading"] = 57,
    ["Activity_Flychess_BackName"] = 57,
    ["NewChat_TimeLastDay"] = 57,
    ["NewChat_TimeMonthDay"] = 57,
    ["NewChat_TimeYearMonthDay"] = 57,
    ["System_NewVersion_Subscribe_Btn_Title"] = 57,
    ["System_NewVersion_Share_Btn_Title"] = 57,
    ["System_NewVersion_Video_Title"] = 57,
    ["QRCode_Scan_Bottom_Tips"] = 57,
    ["DeleteGiftLog_Tips"] = 57,
    ["Direction_Tip_1"] = 57,
    ["Direction_Tip_2"] = 57,
    ["LuckyRemainQuantity"] = 57,
    ["MonthCardRecviveEveryDay"] = 57,
    ["MonthCardRecviveFirstDay"] = 57,
    ["MonthCardRecviveToday"] = 57,
    ["MonthCardAlreadyCacheDays"] = 57,
    ["UGC_MultiScene_SubSceneEdit_Tips"] = 57,
    ["Orange_Suit_Preview_Title"] = 57,
    ["Orange_Suit_Title"] = 57,
    ["UGC_Shop_RemoveMapApply_Reason"] = 57,
    ["UGC_Shop_RemoveMapConfirmTip"] = 57,
    ["UGC_Shop_RemoveMapApply_Succeed"] = 57,
    ["UGC_Shop_RemoveMapApply_Repeated"] = 57,
    ["UGC_Shop_MapAudit_EditForbidden"] = 57,
    ["UGC_StarParty_Reward_title"] = 57,
    ["UGC_StarParty_Reward_desc"] = 57,
    ["QRCode_Gallery_Permission_Tips"] = 58,
    ["Task_DailyExpireText"] = 58,
    ["Task_WeekExpireText"] = 58,
    ["Task_DailyExpireText_2"] = 58,
    ["Task_LevelExpireSoon"] = 58,
    ["Dance_ResLoad_Tips"] = 58,
    ["UI_ResLoad_Tips"] = 58,
    ["UI_WxgameCollectGuide_Tips"] = 58,
    ["PermanentExchange_Entry"] = 58,
    ["Prop_Dart_Name"] = 58,
    ["Prop_Dart_Tips"] = 58,
    ["Common_Confirm_Retry"] = 58,
    ["Common_Confirm_Auto"] = 58,
    ["Common_Confirm_Use"] = 58,
    ["Common_Cancel_2"] = 58,
    ["UI_Setting_AutoSetGraph"] = 58,
    ["UI_Setting_Recommend"] = 58,
    ["UI_Setting_Recommend_1"] = 58,
    ["UI_Setting_Recommend_2"] = 58,
    ["UI_Toast_Recommend"] = 58,
    ["UI_Setting_GraphWarning"] = 58,
    ["UI_Setting_GraphBanned"] = 58,
    ["System_Athlete_Stamina_Title"] = 58,
    ["System_Athlete_Items_Insufficient"] = 58,
    ["System_Athlete_Be_Helped_Title"] = 58,
    ["System_Athlete_Help_Title"] = 58,
    ["SportsmanRadarChatBaseScore"] = 58,
    ["FarmCoin_Cannot_get"] = 58,
    ["UGC_Watch_ItemFail_Tips"] = 58,
    ["UGC_MapList_Publish_Maintain"] = 58,
    ["UGC_Map_Maintain_ForbiddenPlay"] = 58,
    ["Text_ChatUI_JourneyOfCups_Des"] = 58,
    ["Text_ChatUI_ReturnPlayer_Des"] = 58,
    ["Text_ChatUI_Group_Des"] = 58,
    ["UGC_MultiScene_Add_SubScene"] = 58,
    ["UGC_MultiScene_Delete_SubScene"] = 58,
    ["UGC_MultiScene_Edit_SubScene"] = 58,
    ["UGC_MultiScene_SpawnPoint_Delete"] = 58,
    ["UI_MessageBox_GoToBattleRoom"] = 58,
    ["ChastCanotOpen"] = 58,
    ["ChastCanOpen"] = 58,
    ["UI_SimpleGiftText"] = 58,
    ["UGC_StarParty_New_Reward_ThisWeek_Title"] = 58,
    ["UGC_StarParty_New_Reward_ThisWeek_PlayTimes"] = 58,
    ["UGC_StarParty_New_Match_NotInTime"] = 58,
    ["UI_InLevel_Arena_Reputation_MyScore_LineOne"] = 58,
    ["UI_InLevel_Arena_Reputation_MyScore_LineTwo"] = 58,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineOne"] = 58,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineTwo"] = 58,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineThree"] = 58,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineOne"] = 58,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineTwo"] = 58,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineThree"] = 58,
    ["UI_InLevel_Arena_Main_NoCompleteWithReputation_Box"] = 58,
    ["Common_FreeDiscount"] = 58,
    ["UGC_PreventCropping_CrossOverLimit"] = 58,
    ["UGC_PreventCropping_CopyOverLimit"] = 58,
    ["GetAllTaskReward_Cup"] = 58,
    ["NewChat_SystemAdministrator"] = 58,
    ["Item_SortDefault"] = 58,
    ["Prop_SpringPunch_Name"] = 58,
    ["SpringPunch_LevelUp_Success"] = 58,
    ["SpringPunch_LevelUp_Fail"] = 58,
    ["SpringPunch_LevelLabel_1"] = 58,
    ["SpringPunch_LevelLabel_2"] = 58,
    ["SpringPunch_LevelLabel_3"] = 58,
    ["SpringPunch_LevelLabel_4"] = 58,
    ["BP_PA_PR_StarHammer_Name"] = 58,
    ["Setting_Game_MultiScene_Title"] = 58,
    ["UGC_NPC_Exit_Puzzle_Tips"] = 58,
    ["UI_UnableTo_TransmitTo_TheOther_Location"] = 58,
    ["DrawReward_FristText"] = 58,
    ["DrawReward_SecondText"] = 58,
    ["DrawReward_ThridText"] = 58,
    ["DrawReward_MOBATHIRDText"] = 58,
    ["System_ModelSelect_TypeSelectOption_RankMatch_Suffix"] = 58,
    ["System_ModelSelect_TypeSelectOption_AllMatch"] = 58,
    ["Team_StarWorldMap"] = 58,
    ["Mall_Closed"] = 58,
    ["CloudGame_WifiAutoDownload"] = 58,
    ["CloudGame_LoadingDownload"] = 58,
    ["VA_WifiAutoDownload"] = 58,
    ["Common_CantUseInCurrentScene"] = 58,
    ["Set_EditKey_Chase1"] = 58,
    ["Set_EditKey_Chase2"] = 58,
    ["Set_EditKey_NR3E3"] = 58,
    ["Set_EditKey_NR3E2"] = 58,
    ["Set_EditKey_NR3E1"] = 58,
    ["Lobby_StatusCanNotTrain"] = 58,
    ["Lobby_StatusCanNotBackpack"] = 58,
    ["Pak_GameModeDownloadText"] = 58,
    ["Pak_FirstRewardText"] = 58,
    ["Pak_LocalNetworkWIFIStatusText"] = 58,
    ["Pak_LocalNetworkNetStatusText"] = 58,
    ["Pak_AskForDownloadText"] = 58,
    ["Pak_PackageDownloadText"] = 58,
    ["Pak_VisualEffectsText"] = 58,
    ["ScoringSystem_NeedDownloadRes"] = 58,
    ["Text_ChatUI_Month_Card"] = 58,
    ["Text_ChatUI_MVP"] = 58,
    ["Text_ChatUI_Farm_Level"] = 59,
    ["Text_ChatUI_Arena_Hero"] = 59,
    ["Text_ChatUI_Wolf_Identity"] = 59,
    ["CommunityDress_Change_Success"] = 59,
    ["CommunityDress_Change_Revert"] = 59,
    ["CommunityDress_UI_Select_First"] = 59,
    ["CommunityDress_UI_Generate_First"] = 59,
    ["CommunityDress_UI_Change_Tip"] = 59,
    ["CommunityDress_UI_Leave_Tip"] = 59,
    ["CommunityDress_UI_Time_Format"] = 59,
    ["CommunityDress_UI_Keyword_Good"] = 59,
    ["CommunityDress_UI_Keyword_Great"] = 59,
    ["UI_ClothShow_Share_Fix"] = 59,
    ["CommunityDress_Item_Final_Confirm_Tip"] = 59,
    ["CommunityDress_Item_Final_Confirm"] = 59,
    ["CommunityDress_UI_Change_Tip_Dress"] = 59,
    ["CommunityDress_UI_Change_Tip_Nodress"] = 59,
    ["Toast_GetDressItem_Tips"] = 59,
    ["InLevel_MainGame_SceneCollectionTip"] = 59,
    ["CommunityDress_UI_Tips_Opentime"] = 59,
    ["UI_Friend_MatchTime_Short"] = 59,
    ["UI_Friend_MatchTime_OverOneMinute_WithTime"] = 59,
    ["GameCorridor_MST_CollectionRecommend"] = 59,
    ["GameCorridor_MST_CollectionGuessLike"] = 59,
    ["UGC_Park_Common_NetError"] = 59,
    ["UGC_FamousMap_Filter_All"] = 59,
    ["UGC_FamousMap_Filter_UnPlayed"] = 59,
    ["UGC_FamousMap_Filter_UnPassed"] = 59,
    ["UGC_FamousMap_DetailBtn_Text"] = 59,
    ["UGC_FamousMap_MatchBtn_SinglePlay"] = 59,
    ["UGC_FamousMap_MatchBtn_GoMatch"] = 59,
    ["UGC_FamousMap_MatchBtn_CreateRoom"] = 59,
    ["GameCorridor_HotRecommend"] = 59,
    ["UGC_Minimap_Image_Save_Success"] = 59,
    ["UGC_Minimap_Image_Save_Fail"] = 59,
    ["UGC_Minimap_Data_Save_Success"] = 59,
    ["UGC_Minimap_Data_Save_Fail"] = 59,
    ["Common_PlayerHideName"] = 59,
    ["UGC_WeaponDrag_Tips"] = 59,
    ["UGC_Homemadeweapon_Tips"] = 59,
    ["Lottery_FirstTab0"] = 59,
    ["Lottery_FirstTab1"] = 59,
    ["Lottery_FirstTab2"] = 59,
    ["Lottery_FirstTab3"] = 59,
    ["Lottery_FirstTab4"] = 59,
    ["UGC_OMD_Crystal_NumCheck_Min_Tips"] = 59,
    ["UGC_OMD_SpawnPoint_NumCheck_Tips"] = 59,
    ["UGC_OMD_RebirthPoint_PosCheck_Tips"] = 59,
    ["UGC_OMD_MonsterWave_ConfigCheck_Tips"] = 59,
    ["UGC_OMD_SpwanStartPointCantDelete"] = 59,
    ["UGC_OMD_GroundOverAdsorption"] = 59,
    ["UGC_OMD_PointCantRun_Tips"] = 59,
    ["UGC_OMD_GroundPoindIllegal"] = 59,
    ["UGC_OMD_NavMesh_Show"] = 59,
    ["UGC_OMD_NavMesh_Hide"] = 59,
    ["UGC_OMD_SwitchMonsterType"] = 59,
    ["UGC_OMD_SpawnPoint_PosCheck_Tips"] = 59,
    ["UGC_OMD_Difficulty_Easy"] = 59,
    ["UGC_OMD_Difficulty_Challenge"] = 59,
    ["UGC_OMD_Difficulty_Trouble"] = 59,
    ["UGC_OMD_Difficulty_Smart"] = 59,
    ["UGC_OMD_Difficulty_Abreact"] = 59,
    ["UGC_OMD_Difficulty_Oldsix"] = 59,
    ["UGC_OMD_Difficulty_Null_Tips"] = 59,
    ["UGC_OMD_SpwanEndPointCantDelete"] = 59,
    ["UGC_OMD_SpwanActorCantDelete"] = 59,
    ["Prop_Banana_N_Name"] = 59,
    ["Prop_Big_N_Name"] = 59,
    ["Prop_Small_N_Name"] = 59,
    ["Prop_Speedup_N_Name"] = 59,
    ["Prop_ScreamingChicken_N_Name"] = 59,
    ["Prop_StealthBoom_N_Name"] = 59,
    ["Prop_Banana_N_Tips"] = 59,
    ["Prop_Big_N_Tips"] = 59,
    ["Prop_Small_N_Tips"] = 59,
    ["Prop_Speedup_N_Tips"] = 59,
    ["Prop_ScreamingChicken_N_Tips"] = 59,
    ["Prop_StealthBoom_N_Tips"] = 59,
    ["Prop_Banana_N_MoreTips"] = 59,
    ["Prop_Big_N_MoreTips"] = 59,
    ["Prop_Small_N_MoreTips"] = 59,
    ["Prop_Speedup_N_MoreTips"] = 59,
    ["Prop_ScreamingChicken_N_MoreTips"] = 59,
    ["Prop_StealthBoom_N_MoreTips"] = 59,
    ["Activity_GrowthPath_Remind_Update"] = 59,
    ["Activity_GrowthPath_RemindToast_GoUnopened"] = 59,
    ["Activity_GrowthPath_RemindToast_OutTime"] = 59,
    ["Activity_GrowthPath_Remind_Undownload"] = 59,
    ["Common_Resource_Loading"] = 59,
    ["PlayerInfo_Suit"] = 59,
    ["PlayerInfo_ShowSuit"] = 59,
    ["PlayerInfo_SomeOneSuit"] = 59,
    ["UGC_Search_TopicDesc_FollowCount"] = 59,
    ["UGC_Search_TopicDesc_ViewActivity"] = 59,
    ["UGC_Search_TopicDesc_FollowBtn_Follow"] = 59,
    ["UGC_Search_TopicDesc_FollowBtn_UnFollow"] = 59,
    ["UGC_Search_TopicDesc_FollowTips_Followed"] = 59,
    ["UGC_Search_TopicDesc_FollowTips_UnFollowed"] = 59,
    ["UnPickable_Invalid_PlayerState"] = 59,
    ["PlayerInfo_ChangeSuit"] = 59,
    ["DrawReward_WitchText"] = 60,
    ["UGC_Vehicle_Forbidden"] = 60,
    ["Home_Vehicle_Forbidden"] = 60,
    ["UI_FPSSetting_CustomWeapon_Title"] = 60,
    ["Player_Firework_Use_Max_Tips"] = 60,
    ["UI_PC_QRScan_Not_Support_Hint"] = 60,
    ["UGC_MultiScene_Rename_Empty_Tips"] = 60,
    ["UGC_MultiScene_Add_SubScene_Success_Tips"] = 60,
    ["UGC_MultiScene_Rename_Occupied_Tips"] = 60,
    ["Mall_Gift_Notice04"] = 60,
    ["Mall_Gift_Tips01"] = 60,
    ["Mall_Gift_Tips02"] = 60,
    ["UI_SevenDayCheckIn_RemainingTime"] = 60,
    ["SettingTip_ShowMouseTip"] = 60,
    ["SettingTip_ShowMouseShortcutTip"] = 60,
    ["Sharing_Tips"] = 60,
    ["UI_ConnanCard_FullRewardGeted"] = 60,
    ["UI_ConnanCard_FullRewardCanGet"] = 60,
    ["UI_ConnanCard_FullReward_Normal"] = 60,
    ["UI_ConnanCard_SelectSendCard"] = 60,
    ["UI_ConnanCard_SendCardNumEmpty"] = 60,
    ["UI_ConnanCard_CardSendLimitNum"] = 60,
    ["UI_ConnanCard_ClusterRandom"] = 60,
    ["Prop_RainBow_Name"] = 60,
    ["Prop_DuckRocket_Name"] = 60,
    ["AB_Steal_Name"] = 60,
    ["AB_BounceBack_Name"] = 60,
    ["AB_BounceBack_Tips"] = 60,
    ["AB_BounceBack_MoreTips"] = 60,
    ["AB_Steal_Tips"] = 60,
    ["UGC_AiAssistant_Welcome01"] = 60,
    ["CAN_NOT_OPEN_ARMORY_BY_ATTR"] = 60,
    ["Pak_AutoRemoveCleanUpTip"] = 60,
    ["Pak_RemoveCleanupTip3"] = 60,
    ["Mall_Gift_Tips03"] = 60,
    ["SimpleModelChangeMatch"] = 60,
    ["CLOUD_TO_NATIVE_TIPS_MOBILE"] = 60,
    ["CLOUD_TO_NATIVE_TIPS_PC"] = 60,
    ["UGC_FPS_WEAPON_OneShotFragmentNum_Limit"] = 60,
    ["Prop_ShuttleNew_Tips"] = 60,
    ["Prop_ShuttleNew_MoreTips"] = 60,
    ["Prop_Cake_Name"] = 60,
    ["Prop_Cake_Tips"] = 60,
    ["UI_Teletubbies_Tips"] = 60,
    ["UGC_Skybox_Name_80"] = 60,
    ["UGC_StaticBatch_Tips01"] = 60,
    ["Prop_RainBow_Tips"] = 60,
    ["Prop_RainBow_MoreTips"] = 60,
    ["Prop_DuckRocket_Tips"] = 60,
    ["Prop_DuckRocket_MoreTips"] = 60,
    ["DrawReward_FourthText"] = 60,
    ["Common_Continue"] = 60,
    ["Delete_ChatRecord_Fail"] = 60,
    ["UGC_DownFootComp_IsLock"] = 60,
    ["Delete_Photo_CantRecover"] = 60,
    ["New_Intelligence"] = 60,
    ["Activity_RankNum_HasReward"] = 60,
    ["Rank_Dont_Have_Rank"] = 60,
    ["UI_KongFuPanda_Fastest"] = 60,
    ["UI_KongFuPanda_EmptyRank"] = 60,
    ["UI_KongFuPanda_RankTime"] = 60,
    ["UI_KongFuPanda_OldTime"] = 60,
    ["UI_KongFuPanda_IsRankNum"] = 60,
    ["UGC_AiAssistant_Welcome02"] = 60,
    ["UGC_AiAssistant_Block"] = 60,
    ["Player_ChangeInviteFrame"] = 60,
    ["Prop_80022Vacuum_Name"] = 60,
    ["Prop_80022Vacuum_Tips"] = 60,
    ["UI_SysSetting_Password_Pass_Tips"] = 60,
    ["Exclusive_Vehicle_ForbidSpecialSkillSlotTips"] = 60,
    ["UI_Share_Not_Support"] = 60,
    ["UI_Countdown_Main_Unlock"] = 60,
    ["SystemMessageNextDayLoginTitle"] = 60,
    ["SystemMessageNextDayLoginContent"] = 60,
    ["UGC_AiAssistant_TextLimit"] = 60,
    ["UGC_AiAssistant_EmptyMapError"] = 60,
    ["MD5Test"] = 60,
    ["Retrieve_Entrance"] = 60,
    ["Retrieve_Exchange_info"] = 60,
    ["Retrieve_Oneclick"] = 60,
    ["Retrieve_Way_info"] = 60,
    ["Retrieve_None_info"] = 60,
    ["Retrieve_Money_info"] = 60,
    ["Retrieve_Button"] = 60,
    ["Retrieve_Ratio_1"] = 60,
    ["Retrieve_Ratio_2"] = 60,
    ["Retrieve_Time"] = 60,
    ["Retrieve_All_info"] = 60,
    ["Retrieve_Unlock_All_info"] = 60,
    ["Cupgame_weekend_Duplicate_tag"] = 60,
    ["Cuptask_weekend_Duplicate_tag"] = 60,
    ["Retrieve_Seek_Hint"] = 60,
    ["GotAllRaffleGrandReward"] = 60,
    ["CupTask_None_info"] = 60,
    ["Addtype_tag1"] = 60,
    ["Addtype_tag2"] = 60,
    ["Addtype_tag3"] = 60,
    ["LimitedType_tag1"] = 60,
    ["LimitedType_tag2"] = 60,
    ["LimitedType_tag3"] = 60,
    ["Cup_Duplicate_tag"] = 61,
    ["TDM_AIRecommend_TabName"] = 61,
    ["Pak_PackageDownload_Tips"] = 61,
    ["Exclusive_Vehicle_BatchInviteTips"] = 61,
    ["Exclusive_Vehicle_SwitchSeatTips"] = 61,
    ["Exclusive_Vehicle_ForbidInviteNoSeatTips"] = 61,
    ["Exclusive_Vehicle_NoSeatTips"] = 61,
    ["Exclusive_Vehicle_ForbidPartySkillTips"] = 61,
    ["Exclusive_Vehicle_ForbidJetSkillTips"] = 61,
    ["Exclusive_Vehicle_ForbidSwitchMainSeatTips"] = 61,
    ["Exclusive_Vehicle_SwitchSeatSuccessNameTips"] = 61,
    ["Exclusive_Vehicle_SwitchSeatSuccessTips"] = 61,
    ["Exclusive_Vehicle_ScoreTips"] = 61,
    ["Exclusive_Vehicle_BatchInvitationTips"] = 61,
    ["Exclusive_Vehicle_BatchInvitationRepeatTips"] = 61,
    ["Exclusive_Vehicle_InvitationExpired"] = 61,
    ["UI_CustomLayout_Plan"] = 61,
    ["Set_EditKey_UseKeySuccess"] = 61,
    ["Set_EditKey_SaveAndUseKeySuccess"] = 61,
    ["Set_EditKey_isSaveAndUseCurrentKeyToExit"] = 61,
    ["Retrieve_Unlock_All_info1"] = 61,
    ["Set_EditKey_SaveAndUse"] = 61,
    ["Tips_DecorationRank_1"] = 61,
    ["Tips_DecorationRank_2"] = 61,
    ["Tips_DecorationRank_3"] = 61,
    ["Active_Remaining_Time"] = 61,
    ["UGC_Editor_InputScopeTip"] = 61,
    ["ResetToSelectCombineWnd"] = 61,
    ["UGC_OMD_SpwanActorCantDeleteDetail"] = 61,
    ["UGC_OMD_BigWaveInvalid"] = 61,
    ["UGC_OMD_EffectOrdinaryMode_NumCheck_Min_Tips"] = 61,
    ["UGC_OMD_EffectEndlessMode_NumCheck_Min_Tips"] = 61,
    ["UGC_OMD_SmallWavePathConfigInvalid"] = 61,
    ["UGC_OMD_MiniMapCheck"] = 61,
    ["UGC_OMD_SmallWaveLackMonster"] = 61,
    ["UGC_OMD_HasMonsterCheckPublish"] = 61,
    ["UGC_OMD_BigSmallWaveDiffType"] = 61,
    ["Tips_CardRank_1"] = 61,
    ["Tips_CardRank_2"] = 61,
    ["Tips_Theme_Firstshow"] = 61,
    ["Tips_ClickToSkipPV"] = 61,
    ["Lottery_WerewolfLuckCard_1"] = 61,
    ["Lottery_WerewolfLuckCard_2"] = 61,
    ["Wansong_Dance_Leave_Tip"] = 61,
    ["Wansong_Dance_Other_Cancel"] = 61,
    ["Set_EditKey_HotZone"] = 61,
    ["UGC_NPC_Exit_Argue_Tips"] = 61,
    ["Maingame_Bp_HuoQU"] = 61,
    ["Maingame_Bp_ZhuangShi"] = 61,
    ["UGC_MultiCamp_NotExistCamp"] = 61,
    ["UGC_Match_CanTeamNum"] = 61,
    ["UGC_MultiCamp_CantSameColor"] = 61,
    ["UGC_MultiCamp_CantSameTeamName"] = 61,
    ["UGC_Lobby_Already_In_Review"] = 61,
    ["UGC_Lobby_Can_Not_Withdraw_During_Reviewing"] = 61,
    ["UGC_Lobby_Reviewing"] = 61,
    ["UGC_Lobby_Review_Passed"] = 61,
    ["UGC_Lobby_Testing"] = 61,
    ["UGC_Lobby_Review_Limit_Exceeded"] = 61,
    ["UGC_Lobby_Review_Blocked_By_Safity_Check"] = 61,
    ["ConnanCard_NoSupportSendCard"] = 61,
    ["Wansong_Match_Not_Open"] = 61,
    ["Wansong_Match_Time_Tips"] = 61,
    ["Wansong_Match_In_Team"] = 61,
    ["Wansong_Match_In_Room"] = 61,
    ["Wansong_Match_In_Match"] = 61,
    ["UGC_IsQuit_Finished"] = 61,
    ["UGC_IsQuit_Unfinished"] = 61,
    ["AIGCNPC_Exit_CreatePartner_Confirm_Tips"] = 61,
    ["AIGCNPC_Exit_TempPalChat_Confirm_Tips"] = 61,
    ["MiniGame_InMatch_Tip"] = 61,
    ["Pak_PackageDownloadCommonText"] = 61,
    ["Pak_PackageDownloadCommonText1"] = 61,
    ["Wansong_Match_TimeDownFormat"] = 61,
    ["UGC_MultiCamp_LeastOneTeam"] = 61,
    ["Common_PlatformErrorTip"] = 61,
    ["UGC_PerformanceTips_1"] = 61,
    ["Prop_70240CurveWeapon_Name"] = 61,
    ["Prop_70240ThreeCurveWeapon_Name"] = 61,
    ["Prop_70240CurveWeapon_Tips"] = 61,
    ["Prop_70240ThreeCurveWeapon_Tips"] = 61,
    ["ScoreGuide_Feedback_MaxWords"] = 61,
    ["Pak_RemoveCleanupTip4"] = 61,
    ["UGC_MapDescription_SpaceEdit"] = 61,
    ["UGC_MapDescription_SpacePreviw"] = 61,
    ["UGC_MapDescription_SpaceSave"] = 61,
    ["UGC_MapDescription_EditSucceed"] = 61,
    ["UGC_MapDescription_Delete"] = 61,
    ["UGC_MapDescription_DeleteYes"] = 61,
    ["UGC_MapDescription_DeleteNot"] = 61,
    ["UGC_MapDescription_SaveNot"] = 61,
    ["UGC_MapDescription_SaveAbandon"] = 61,
    ["UGC_MapDescription_SaveConfirm"] = 61,
    ["Pak_HeroCardDownloadText"] = 61,
    ["DrawReward_FifthText"] = 61,
    ["FindPoketGameList"] = 61,
    ["UI_ShareChannel_SafetyText"] = 61,
    ["Bag_ShuttleEquipSuccess"] = 61,
    ["Common_UseNow"] = 61,
    ["UI_Activity_NewYearParty2025_Tips"] = 61,
    ["UGC_PartyGame_NextMap"] = 62,
    ["UGC_PartyGame_AwardMap"] = 62,
    ["UGC_PartyGame_WaitingNextMap"] = 62,
    ["UGC_PartyGame_PlayerMissingWarning"] = 62,
    ["UGC_VisionSettings_Desc"] = 62,
    ["ItemType_Suit"] = 62,
    ["ItemType_BackOrnament"] = 62,
    ["ItemType_HeadWear"] = 62,
    ["ItemType_HandOrnament"] = 62,
    ["ItemType_FaceOrnament"] = 62,
    ["ItemType_Emoji"] = 62,
    ["Group_Invitation_Restrictions"] = 62,
    ["Exclusive_Vehicle_IllegalState_Using"] = 62,
    ["Task_Activity_HasExpired"] = 62,
    ["UGC_UGCEditor_NumberKeyboard_OpenTips"] = 62,
    ["UGC_UGCEditor_NumberKeyboard_Disable"] = 62,
    ["Cup_Collect_Main_Title"] = 62,
    ["Cup_Collect_Main_Game_Title"] = 62,
    ["Cup_Collect_Main_Game_Add"] = 62,
    ["Cup_Collect_Main_Task_Title"] = 62,
    ["Cup_Collect_Main_Cup_Pre_Num"] = 62,
    ["Cup_Collect_Main_Cup_Rank_Title"] = 62,
    ["Cup_Collect_Main_Cup_Rank_Btn"] = 62,
    ["Cup_Collect_Main_Cup_Rank_Str1"] = 62,
    ["Cup_Collect_Main_Cup_Rank_Str2"] = 62,
    ["UGC_CoCreate_RefundPermission"] = 62,
    ["UGC_CoCreate_MapRemoved"] = 62,
    ["UGC_MapTransToLobby_Draft_Full"] = 62,
    ["UGC_MapTransToLobby_Failed"] = 62,
    ["UGC_MapTransToLobby_Success"] = 62,
    ["UGC_AiAssistant_LoadingTimeTips"] = 62,
    ["UGC_PerformanceAlert_LowRisk"] = 62,
    ["UGC_PerformanceAlert_HighRisk"] = 62,
    ["Pak_IOSArraignment_PromptText1"] = 62,
    ["Pak_IOSArraignment_PromptText2"] = 62,
    ["ItemLimit_MainGame"] = 62,
    ["ItemRedBag_TCVip_Desc"] = 62,
    ["UGC_MapDescription_PhotoLimit"] = 62,
    ["Mail_ConfirmStarDelete"] = 62,
    ["Mail_StarTip"] = 62,
    ["UGC_Skybox_Name_81"] = 62,
    ["UGC_Skybox_Name_82"] = 62,
    ["Share_PicDownloading"] = 62,
    ["UGC_LoadDiagram_Edit"] = 62,
    ["UGC_LoadAndNavigateDiagram_Edit"] = 62,
    ["UGC_MainLoadDiagram"] = 62,
    ["UGC_SubsceneLoadDiagram"] = 62,
    ["UGC_NavigateDiagram"] = 62,
    ["UGC_MapAlbum"] = 62,
    ["UGC_CustomImage_Select"] = 62,
    ["UGC_MapAlbumImage_Select"] = 62,
    ["UGC_IconImage_Select"] = 62,
    ["UGC_UploadImage_Tip"] = 62,
    ["UGC_UploadImage_Fail"] = 62,
    ["UGC_UploadImage_Success"] = 62,
    ["UGC_UploadImage_Cancel"] = 62,
    ["UGC_NavigateDiagramAndIcon_IsNull"] = 62,
    ["UGC_MapAlbumAndCustomize"] = 62,
    ["UGC_NavigateDiagram_Basemap"] = 62,
    ["UGC_NavigateDiagram_Icon"] = 62,
    ["UGC_DefaultImageAndCustomize"] = 62,
    ["UGC_LoadDiagramUpload_Preview"] = 62,
    ["UGC_NavigateDiagramUpload_Preview"] = 62,
    ["UGC_IconUpload_Preview"] = 62,
    ["Prop_MagicCard_Tips"] = 62,
    ["Prop_MagicCard_MoreTips"] = 62,
    ["Prop_MagicCard_Name"] = 62,
    ["UGC_NPC_Quadruped_Help"] = 62,
    ["OutOfMemory_Tip"] = 62,
    ["TryLater_Text"] = 62,
    ["Prop_SuperJump_Name"] = 62,
    ["Prop_SuperJump_Tips"] = 62,
    ["Prop_Teleport_Name"] = 62,
    ["Prop_Teleport_Tips"] = 62,
    ["Prop_HookLink_Name"] = 62,
    ["Prop_HookLink_Tips"] = 62,
    ["CLOUD_TO_NATIVE_TIPS_WX"] = 62,
    ["UGC_Fairyland_Testing_Tips"] = 62,
    ["ActivityNeedCommunityScene"] = 62,
    ["ActivityNeedWerewolf"] = 62,
    ["WerewolfNotOpenJumpFailed"] = 62,
    ["FarmLevelNeed3"] = 62,
    ["Skill_BasicAttack_Name1"] = 62,
    ["Skill_CountAttack_Name2"] = 62,
    ["Skill_Flash_Name3"] = 62,
    ["Skill_BasicAOEAttack_Name4"] = 62,
    ["Skill_ShotgunaAttack_Name5"] = 62,
    ["Main_HostingByAI_Tips_1"] = 62,
    ["Main_HostingByAI_Tips_2"] = 62,
    ["PlayModeReturn_LittleTitle1"] = 63,
    ["PlayModeReturn_LittleTitle2"] = 63,
    ["PlayModeReturn_LittleTitle3"] = 63,
    ["Prop_Furniture_Name"] = 63,
    ["Prop_Furniture_Tips"] = 63,
    ["Prop_Furniture_MoreTips"] = 63,
    ["Prop_TeleportUp_Name"] = 63,
    ["Prop_TeleportUp_Tips"] = 63,
    ["Prop_TeleportUp_MoreTips"] = 63,
    ["Handhold_MusicConcert_ForbidGetOn"] = 63,
    ["Handhold_MusicConcert_DJToning"] = 64,
    ["Handhold_MusicConcert_DrumToning"] = 64,
    ["Handhold_MusicConcert_GuitarToning"] = 64,
    ["Handhold_MusicConcert_BassToning"] = 64,
    ["UGC_Skybox_Name_83"] = 63,
    ["UGC_Quick_Join_Invite_Confirm_Title_Default"] = 63,
    ["UGC_Quick_Join_Invite_Confirm_Hint"] = 63,
    ["Prop_70250Skill1_Name"] = 63,
    ["Prop_70250Skill2_Name"] = 63,
    ["Prop_70250Skill3_Name"] = 63,
    ["Prop_70250Skill4_Name"] = 63,
    ["Prop_70250Flash_Name"] = 63,
    ["Prop_70254Skill1_Name"] = 63,
    ["Prop_70254Skill4_Name"] = 63,
    ["Prop_70254Flash_Name"] = 63,
    ["AB_70250Skill1_Tips"] = 63,
    ["AB_70250Skill2_Tips"] = 63,
    ["AB_70250Skill3_Tips"] = 63,
    ["AB_70250Skill4_Tips"] = 63,
    ["AB_70250Flash_Tips"] = 63,
    ["UGC_Jion_Halfway_Switch_off"] = 63,
    ["UGC_SkyBox_Txt_FilterAdjustments"] = 63,
    ["UGC_SkyBox_Txt_Effect"] = 63,
    ["FridayBuff_OutOfTime"] = 63,
    ["Location_None"] = 63,
    ["Predownloadtip1"] = 63,
    ["Predownloadtip2"] = 63,
    ["PlayerInfo_SomeOneInfo"] = 63,
    ["PlayerInfo_SomeOneFashion"] = 63,
    ["VehicleSkill_Region_Overlap"] = 63,
    ["Item_SortCount"] = 63,
    ["PakCleanUpText"] = 63,
    ["Activity_FeaturedGameplay_Tips"] = 63,
    ["WerewolfRecall_TipContent"] = 63,
    ["WerewolfRecall_MessageBoxTitle"] = 63,
    ["WerewolfRecall_MessageBoxContent"] = 63,
    ["WerewolfRecall_SendChatMessage"] = 63,
    ["BroadcastText_Main_01"] = 63,
    ["BroadcastText_Main_02"] = 63,
    ["BroadcastText_Main_03"] = 63,
    ["BroadcastText_Main_04"] = 63,
    ["BroadcastText_Main_05"] = 63,
    ["BroadcastText_Main_06"] = 63,
    ["BroadcastText_Main_07"] = 63,
    ["BroadcastText_Main_08"] = 63,
    ["BroadcastText_Main_09"] = 63,
    ["BroadcastText_Main_10"] = 63,
    ["BroadcastText_Main_11"] = 63,
    ["BroadcastText_Main_12"] = 63,
    ["BroadcastText_Main_13"] = 63,
    ["BroadcastText_Main_14"] = 63,
    ["BroadcastText_Main_15"] = 63,
    ["BroadcastText_Main_16"] = 63,
    ["BroadcastText_Main_17"] = 63,
    ["BroadcastText_Main_18"] = 63,
    ["BroadcastText_Main_19"] = 63,
    ["BroadcastText_Main_20"] = 63,
    ["BroadcastText_Main_21"] = 63,
    ["BroadcastText_Main_22"] = 63,
    ["BroadcastText_Main_23"] = 63,
    ["WolfKill_MonthCard_Reward"] = 64,
    ["Title_TakeALook"] = 63,
    ["Title_QuickEntrance"] = 63,
    ["Title_RecentPlay"] = 63,
    ["Title_MyCollections"] = 63,
    ["Cook_Report_1"] = 63,
    ["UGC_MultiScene_Edit_SpeedCheck_Tips"] = 63,
    ["Handhold_Common_ForbidenWhenTooClose"] = 63,
    ["GiveAway_GetTaskRewards"] = 62,
    ["GiveAway_GetTaskRewards_GoChange"] = 62,
    ["GiveAway_GetTaskRewards_Get"] = 62,
    ["GiveAway_ChangeTaskReward"] = 62,
    ["Return_BookFullLevel"] = 62,
    ["Return_GiftTitle"] = 62,
    ["Return_TaskTitle"] = 62,
    ["Return_WeekTaskTitle"] = 62,
    ["Return_AccumulateTaskTitle"] = 62,
    ["Return_BookSelectSkin"] = 62,
    ["Return_OfflineExpDesc"] = 63,
    ["Return_BigRewardDescribe"] = 63,
    ["Return_Activity_ReturnLink_Page1"] = 63,
    ["Return_Activity_ReturnLink_Page2"] = 63,
    ["Return_Activity_ReturnLink_Page3"] = 63,
    ["Return_Activity_ReturnLink_Page4"] = 63,
    ["Return_Activity_ReturnLink_Page5"] = 63,
    ["Return_NoOfflineExperience"] = 62,
    ["Photo_Reset_Success"] = 63,
    ["Video_Mode_Switch"] = 63,
    ["Video_Mode_Saved"] = 63,
    ["Video_Mode_SavedShort"] = 63,
    ["UGC_MyRes_LimitTips"] = 63,
    ["UGC_MyInterfaceRes_LimitTips"] = 63,
    ["UGC_InterfaceRes_CantSaveTips"] = 63,
    ["UGC_InterfaceRes_CantEditTips"] = 63,
    ["Pak_CleanupCompletionTip"] = 63,
    ["UGC_Room_Change_Map_Tips"] = 63,
    ["UGC_Room_Recommend_Map_Tips"] = 63,
    ["UGC_CoCreate_EditRequest_Sent"] = 63,
    ["UGC_CoCreate_EditRequest_Refuse"] = 63,
    ["UGC_CoCreate_EditRequest_Cooling"] = 63,
    ["UGC_CoCreate_EditRequest_Await"] = 63,
    ["UGC_CoCreate_EditRequest_Completed"] = 64,
    ["UGC_CoCreate_PrePelease_Refuse"] = 63,
    ["UGC_CoCreate_MultiPersonEdit_PublishRefuse"] = 63,
    ["UGC_CoCreate_EditRequest_NotJoin"] = 63,
    ["UGC_CoCreate_SceneMode_CodingDataUpdateTips"] = 63,
    ["UGC_CoCreate_SceneMode_CodingDataDescription"] = 64,
    ["UGC_CoCreate_SceneMode_CodingDataDownloadTips"] = 64,
    ["UGC_CoCreate_SceneMode_CodingDataSaveTips"] = 64,
    ["UGC_CoCreate_SceneMode_CodingDataNotSaveTips"] = 64,
    ["UGC_CoCreate_SceneMode_Description"] = 64,
    ["UGC_CoCreate_SceneMode_SceneDataUploadTips"] = 64,
    ["UGC_CoCreate_CodingMode_SceneDataUpdateTips"] = 64,
    ["UGC_CoCreate_CodingMode_SceneDataDescription"] = 64,
    ["UGC_CoCreate_CodingMode_SceneDataDownloadConfirm"] = 64,
    ["UGC_CoCreate_CodingMode_Description"] = 64,
    ["UGC_CoCreate_CodingMode_CodingDataUploadTips"] = 65,
    ["UGC_CoCreate_MultiPersonEdit_AwaitTips"] = 64,
    ["UGC_CoCreate_MultiPersonEdit_DingTips"] = 64,
    ["UGC_CoCreate_DataSync_DataChange"] = 64,
    ["UGC_CoCreate_DataSync_DataNoChange"] = 64,
    ["UGC_CoCreate_DataSync_UploadTips1"] = 64,
    ["UGC_CoCreate_DataSync_UploadTips2"] = 64,
    ["UGC_CoCreate_DataSync_DownloadTips1"] = 64,
    ["UGC_CoCreate_DataSync_DownloadTips2"] = 64,
    ["UGC_CoCreate_ModeSelect_Tips1"] = 64,
    ["UGC_CoCreate_ModeSelect_Tips2"] = 64,
    ["UGC_CoCreate_ModeSelect_SceneModeDesc"] = 64,
    ["UGC_CoCreate_ModeSelect_CodingModeDesc"] = 64,
    ["UGC_CoCreate_ModeSelect_SceneModeFullDes"] = 64,
    ["UGC_CoCreate_ModeSelect_CodingModeFullDes"] = 64,
    ["UGC_CoCreate_ModeName_Scene"] = 64,
    ["UGC_CoCreate_ModeName_Coding"] = 64,
    ["UGC_CoCreate_Cooperate_Terminate"] = 64,
    ["InLevel_QST_RandomEvent"] = 64,
    ["UGC_MultiScene_PreserveScene_Tips"] = 64,
    ["UGC_MultiScene_SceneFully_Tips"] = 64,
    ["UGC_MultiScene_JionHalfway_PreserveScene_Tips"] = 64,
    ["UGC_MultiScene_JumpLock_EndingSoon_Tips"] = 64,
    ["UGC_MultiScene_JumpLock_Countdown_Tips"] = 64,
    ["UGC_MultiScene_JumpCD_Tips"] = 64,
    ["UGC_MultiScene_JumpFailed_Tips1"] = 64,
    ["UGC_MultiScene_JumpFailed_Tips2"] = 64,
    ["UGC_MultiScene_SceneDestroyed_Settlement_Tips"] = 64,
    ["UGC_MultiScene_DetectSpawnPoint_Tips"] = 66,
    ["UGC_MultiScene_JumpFailed_OutOfRange_Tips"] = 64,
    ["UGC_MultiScene_JumpFailed_Tips3"] = 65,
    ["UGC_Programme_Multi_Scene_Map_Setting_Error"] = 65,
    ["UGC_Programme_Multi_Scene_Trial_Play_Cant_Jump"] = 65,
    ["UGC_MultiScene_JumpWay_Name1"] = 65,
    ["UGC_MultiScene_JumpWay_Name2"] = 65,
    ["UGC_MultiScene_PreserveScene_SwitchDescription"] = 64,
    ["UGC_MultiScene_OnlyScene_SwitchDescription"] = 64,
    ["UGC_MultiScene_Conversion_Tips"] = 65,
    ["UGC_MultiScene_ConversionSuccessful_Tips"] = 65,
    ["UGC_MultiScene_Cant_Conversion_Tips"] = 65,
    ["UGC_MultiScene_ConversionFail_Tips"] = 65,
    ["UGC_MultiScene_Cant_Conversion_Editing"] = 65,
    ["UGC_MultiScene_CreationFail_Tips"] = 65,
    ["UGC_MultiScene_Cant_Copy_OutOfRange"] = 65,
    ["UGC_MultiScene_Cant_Copy_Occupy"] = 65,
    ["UGC_MultiScene_CoverUnValid"] = 65,
    ["UGC_MultiScene_Copy_SubScene"] = 66,
    ["UGC_MultiScene_Edit_NoPermission"] = 67,
    ["UGC_MultiScene_JumpFailed_Tips4"] = 67,
    ["UGC_MultiScene_Cover_SwitchScenes_Tips"] = 65,
    ["Subscribe_AddToMyMiniProgram"] = 64,
    ["Subscribe_ServiceNotify"] = 64,
    ["Subscribe_AddToDesktop"] = 64,
    ["SettingTip_RoleOutline"] = 64,
    ["FarmReturn_LevelChallengeMax"] = 64,
    ["FarmReturn_LevelChallenge"] = 64,
    ["UGC_Skybox_Name_84"] = 64,
    ["Bp_Other_Download"] = 64,
    ["Bp_Other_Condition_Wolves"] = 64,
    ["Activity_FarmPass_WeekTask"] = 64,
    ["Activity_FarmPass_CyclicalTask"] = 64,
    ["Activity_FarmPass_WeekIdx"] = 64,
    ["Cup_Collect_Multiple_Tips1"] = 64,
    ["Cup_Collect_Multiple_Tips2"] = 64,
    ["Cup_Collect_Multiple_Tips3"] = 64,
    ["Cup_Collect_Multiple_Tips4"] = 64,
    ["UGC_ModeSelect_MapPool_GoMatch"] = 64,
    ["SettingTip_CupShow"] = 64,
    ["Cup_Collect_Multiple_Tips5"] = 64,
    ["Cup_Collect_Multiple_Tips6"] = 64,
    ["Cup_Collect_Multiple_Tips7"] = 64,
    ["Cup_Collect_Multiple_Tips8"] = 64,
    ["Cup_Collect_Multiple_Tips9"] = 65,
    ["Cup_Collect_Multiple_Tips10"] = 65,
    ["Cup_Collect_Multiple_Tips11"] = 66,
    ["Pak_PV_DownLoad"] = 64,
    ["Pak_Delete_Tips"] = 64,
    ["Pak_InterfaceName1"] = 64,
    ["Pak_InterfaceName2"] = 64,
    ["Pak_InterfaceName3"] = 64,
    ["Pak_InterfaceTip1"] = 64,
    ["Pak_WindowDescription"] = 64,
    ["Pak_LineUp_Tip"] = 64,
    ["Pak_Interface_Tip1"] = 64,
    ["Pak_Interface_Tip2"] = 64,
    ["Pak_Interface_Tip3"] = 64,
    ["Pak_CollectionInterface_Text1"] = 64,
    ["Pak_CollectionInterface_Text2"] = 64,
    ["Pak_AllDownload_Tip"] = 64,
    ["Pak_WVAInterface_Tip1"] = 64,
    ["Pak_WVAInterface_Tip2"] = 64,
    ["UGC_AIGCModelDelete_Title"] = 64,
    ["UGC_AIGCModelDelete_Tips"] = 64,
    ["System_Bag_Slot_Ready_To_Clear"] = 64,
    ["System_Bag_CanNotRemove"] = 66,
    ["Pak_LowSpace_Tip1"] = 64,
    ["Pak_LowSpace_Tip2"] = 64,
    ["Pak_LowSpace_Tip3"] = 64,
    ["Pak_LowSpace_Tip4"] = 64,
    ["UGC_InterfaceCover_CantPublish"] = 64,
    ["UGC_InterfaceRes_CantPickForPub"] = 64,
    ["SummerVacation_Decoration_Txt"] = 65,
    ["App_Icon_Change"] = 65,
    ["Pak_PV_DownLoad_Toast"] = 65,
    ["App_Icon_Ios"] = 65,
    ["App_Icon_Android"] = 65,
    ["UGC_AiAssistant_ErrorCodeTips"] = 65,
    ["UGC_StatusCanNotUseBag"] = 65,
    ["Pak_Recommended_Download1"] = 65,
    ["Pak_Recommended_Download2"] = 65,
    ["Pak_Recommended_Download"] = 65,
    ["Set_EditKey_FB"] = 65,
    ["NotInAnyVoiceRoom"] = 65,
    ["Pak_CollectionInterface_Text3"] = 65,
    ["Pak_CollectionInterface_Text4"] = 65,
    ["Pak_CollectionInterface_Text5"] = 65,
    ["Pak_CollectionInterface_Text6"] = 65,
    ["Pak_CollectionInterface_Text7"] = 65,
    ["Pak_CollectionInterface_Text8"] = 65,
    ["Pak_CollectionInterface_Text9"] = 65,
    ["Pak_CollectionInterface_Text10"] = 65,
    ["Pak_CollectionInterface_Text11"] = 65,
    ["Pak_CollectionInterface_Text12"] = 65,
    ["Pak_CollectionInterface_Text13"] = 65,
    ["Pak_CollectionInterface_Text14"] = 65,
    ["Pak_CollectionInterface_Text15"] = 65,
    ["Pak_CollectionInterface_Text16"] = 65,
    ["InLevel_QST_GameModeReturn"] = 65,
    ["Returning_Buff_Addition"] = 65,
    ["Arena_Unlock_Need_Count"] = 65,
    ["Photo_Jump_AlreadyIn"] = 65,
    ["Chase_Custom_Voice_SaveTip"] = 65,
    ["Chase_Custom_Voice_GiveUpTip"] = 65,
    ["Chase_Custom_Voice_GiveUpTxt"] = 65,
    ["Chase_Custom_Voice_SaveExitTxt"] = 65,
    ["Pak_CollectionInterface_Text17"] = 65,
    ["Pak_CollectionInterface_Text18"] = 65,
    ["Pak_CollectionInterface_Text19"] = 65,
    ["Pak_CollectionInterface_Text20"] = 65,
    ["Pak_CollectionInterface_Text21"] = 65,
    ["PlayerReturn_DragChangeReward"] = 65,
    ["LeaveTeamTips_UGC_SinglePlay"] = 65,
    ["LeaveTeamTips_UGC_CreateRoom"] = 65,
    ["LeaveTeamTips_UGC_EnterRoom"] = 65,
    ["LeaveTeamTips_UGC_StarCruise"] = 65,
    ["HandHold_Open"] = 65,
    ["HandHold_Close"] = 65,
    ["Mail_Map_Outtime_01"] = 65,
    ["Mail_Map_Outtime_02"] = 65,
    ["Player_Gift_NotAccept"] = 65,
    ["Player_Gift_Level_UnderFive"] = 65,
    ["Player_Gift_OnlyCloseFriend"] = 65,
    ["Friend_GiftAndAsk_Closed"] = 65,
    ["Friend_Gift_Closed"] = 65,
    ["Friend_Ask_Closed"] = 65,
    ["FarmShopLevelNeed3"] = 65,
    ["UI_Lottery_PushingBoard_AwardRecordBox_Title"] = 65,
    ["UI_Lottery_PushingBoard_AwardRecordBox_GetHint"] = 65,
    ["UI_Lottery_PushingBoard_MainView_UseBtn"] = 65,
    ["UI_Lottery_PushingBoard_MainView_GetBtn"] = 65,
    ["UI_Lottery_PushingBoard_MainView_GetScoreBtn"] = 65,
    ["UI_Lottery_PushingBoard_MainView_StrHint1"] = 65,
    ["UI_Lottery_PushingBoard_MainView_StrHint2"] = 65,
    ["UI_Lottery_PushingBoard_MainView_GetUseNoNum"] = 65,
    ["UGC_ModuleAndUI_CoverHelpTips"] = 65,
    ["UI_Lottery_PushingBoard_MainView_ToGetScoreTitle"] = 66,
    ["UI_Lottery_PushingBoard_MainView_NoDisplayBoard"] = 66,
    ["AIGCNPC_Exit_PlayerSet_Confirm_Tips"] = 66,
    ["AIGCNPC_Exit_PlayerSet_Confirm_Tips2"] = 66,
    ["AIGCC_Open_DoNotDisturb"] = 66,
    ["AIGCC_Close_DoNotDisturb"] = 66,
    ["AIGC_OpenDisturbFail"] = 66,
    ["AIGC_CloseDisturbFail"] = 66,
    ["AIGC_RedDotNum"] = 66,
    ["System_CommonGetExtraAward"] = 66,
    ["Vehicle_Btm_Tips"] = 66,
    ["UI_SPGame_Building_Count_Limit"] = 66,
    ["UI_DoNotOpen"] = 66,
    ["UI_ConfirmOpen"] = 66,
    ["YMSK_Delete_Text"] = 66,
    ["Video_Has_Saved"] = 66,
    ["YMSK_Open_Tips"] = 66,
    ["Pak_Mall_Download_Tip1"] = 66,
    ["Pak_Mall_Download_Tip2"] = 66,
    ["Pak_Mall_Download_Toast"] = 66,
    ["UGC_CoCreate_SceneMode_UIDataUpdateTips"] = 66,
    ["UGC_CoCreate_CodingMode_UIDataUpdateTips"] = 66,
    ["UGC_CoCreate_SceneMode_UIDataDescription"] = 66,
    ["UGC_CoCreate_CodingMode_UIDataDescription"] = 66,
    ["UGC_CoCreate_UIMode_SceneDataDescription"] = 66,
    ["UGC_CoCreate_UIMode_CodingDataDescription"] = 66,
    ["UGC_CoCreate_UIMode_UIDataUploadTips"] = 66,
    ["UGC_CoCreate_UIMode_SceneDataUpdateTips"] = 66,
    ["UGC_CoCreate_UIMode_SceneDataDownloadConfirm"] = 66,
    ["UGC_CoCreate_UIMode_CodingDataUpdateTips"] = 66,
    ["UGC_CoCreate_UIMode_CodingDataDownloadTips"] = 66,
    ["UGC_CoCreate_UIMode_CodingDataSaveTips"] = 66,
    ["UGC_CoCreate_UIMode_CodingDataNotSaveTips"] = 66,
    ["UGC_CoCreate_UIMode_Description"] = 66,
    ["UGC_CoCreate_ModeSelect_UIModeDesc"] = 66,
    ["UGC_CoCreate_ModeSelect_UIModeFullDes"] = 66,
    ["UGC_CoCreate_ModeName_UI"] = 66,
    ["UGC_CoCreate_DataSync_DownloadTips3"] = 66,
    ["UGC_CoCreate_ModeSelect_Tips3"] = 66,
    ["UGC_CoCreate_DataUpdate_Tips"] = 66,
    ["UGC_CoCreate_DataDownloadFail_Tips"] = 66,
    ["SummerFlash_Photo_Tip1"] = 66,
    ["SummerFlash_Photo_Tip2"] = 66,
    ["SummerFlash_Photo_Upload"] = 66,
    ["SummerFlashingPhotoModel_Toast_1"] = 66,
    ["SummerFlashingPhotoModel_Toast_2"] = 66,
    ["SummerFlashingPhotoModel_Toast_3"] = 66,
    ["SummerFlashingPhotoModel_CantJump"] = 66,
    ["SummerFlashingPopJoin_ToJoin"] = 66,
    ["SummerFlashingPopJoin_ToGroupPhoto"] = 66,
    ["Mall_PriceSortText"] = 66,
    ["Mall_SoldOutTips"] = 66,
    ["WerewolfDownloadCheck"] = 66,
    ["Video_Save"] = 66,
    ["UGC_Commercial_MapReview_CantSubmit"] = 66,
    ["UGC_Commercial_OtherReview_CantSubmit"] = 66,
    ["UGC_Commercial_NotSelected"] = 66,
    ["UGC_Commercial_NoEdit_CantSubmit"] = 66,
    ["UGC_Commercial_SubmitSuccess"] = 66,
    ["UGC_Commercial_IAP_Intro"] = 66,
    ["UGC_Commercial_IAA_Intro"] = 66,
    ["UGC_Commercial_IAP_Title"] = 66,
    ["UGC_Commercial_IAA_Title"] = 66,
    ["UGC_HighligntOpen_Fail"] = 66,
    ["UGC_HighligntOpen_Copy_Fail"] = 66,
    ["UGC_SwitchCollisions_Anticropping_Fail"] = 66,
    ["UGC_SwitchCollisions_HighlightOpen_Fail"] = 66,
    ["Jump_closed"] = 66,
    ["Clear_Plan_Successful"] = 66,
    ["UGC_S2M_Suc"] = 66,
    ["UGC_S2M_Fail"] = 66,
    ["NewChat_StarWorld"] = 66,
    ["UGC_CanNotBagPropItem"] = 66,
    ["CloudVATransferDownloading"] = 66,
    ["UGC_FPS_WEAPON_LAST_ONE_ACTION"] = 66,
    ["SummerNavigationBubble_LessExchangeText"] = 66,
    ["SummerNavigationBubble_NoExchangeText"] = 65,
    ["SummerNavigationBubble_CanExchangeText"] = 65,
    ["SummerNavigationBubble_HasExchangeText"] = 65,
    ["SummerVacation_Task_Txt"] = 65,
    ["SummerNavigationBubble_CarouselText1"] = 66,
    ["SummerNavigationBubble_CarouselText2"] = 66,
    ["SummerNavigationBubble_CarouselText3"] = 66,
    ["SummerNavigationBubble_CarouselText4"] = 66,
    ["SummerNavigationBubble_CarouselText5"] = 66,
    ["Pak_WIFIDownloadTips"] = 66,
    ["Pak_MobileWebDownloadTips"] = 66,
    ["Pak_CollectionAllDownload1"] = 66,
    ["Pak_CollectionAllDownload2"] = 66,
    ["Pak_CollectionTips"] = 66,
    ["Pak_CollectionDownloading"] = 66,
    ["Pak_CleanTips"] = 66,
    ["Pak_Clean_PopUp_text"] = 66,
    ["Pak_Clean_PopUp_text1"] = 66,
    ["Pak_Clean_PopUp_text2"] = 66,
    ["Pak_BubbleText1"] = 66,
    ["Pak_BubbleText2"] = 66,
    ["Pak_ScenarioDownloadPrompt1"] = 67,
    ["Pak_ScenarioDownloadPrompt2"] = 67,
    ["Pak_WVA_CleanTips"] = 67,
    ["Pak_Download_Time_Text"] = 66,
    ["Pak_Download_Time_Text1"] = 67,
    ["Pak_Download_Time_Text2"] = 67,
    ["Pak_Download_Time_Text3"] = 67,
    ["System_SummerFlashing_Items_JumpLock"] = 67,
    ["System_SummerFlashing_Items_Finish"] = 67,
    ["Pak_CleanUpToast"] = 67,
    ["Mayday_ModeName"] = 68,
    ["Mayday_Map_Rainforest"] = 68,
    ["Mayday_Map_Rainforest_Info"] = 68,
    ["Mayday_Map_KingsPalace"] = 68,
    ["Mayday_Map_KingsPalace_Info"] = 68,
    ["Mayday_Map_KingsPalace_Shangjiao"] = 68,
    ["Mayday_Map_SunDesert"] = 68,
    ["Mayday_Map_SunDesert_Info"] = 68,
    ["Mayday_Map_Tips1"] = 68,
    ["Mayday_InGame_MapInfo_Time"] = 68,
    ["Mayday_InGame_MapInfo_Time_DistantPast"] = 68,
    ["Mayday_InGame_MapInfo_Time_Ancient"] = 68,
    ["Mayday_InGame_MapInfo_Time_Modern"] = 68,
    ["Mayday_InGame_MapInfo_Time_Future"] = 68,
    ["Mayday_InGame_MapInfo_Weather"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Sunny"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Rain"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Cloudy"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Fog"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Flood"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Thunder"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Sandstorm"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Blizzard"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Eclipse"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Flood_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Thunder_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Sandstorm_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Blizzard_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Weather_Eclipse_Warning"] = 68,
    ["Mayday_InGame_MapInfo_Null"] = 68,
    ["Mayday_InGame_MapInfo_Environment"] = 68,
    ["Mayday_InGame_MapInfo_Environment_Rainforest"] = 68,
    ["Mayday_InGame_MapInfo_Environment_Desert"] = 68,
    ["Mayday_InGame_MapInfo_Environment_Cold"] = 68,
    ["Mayday_InGame_MapInfo_Unknown"] = 68,
    ["Mayday_InGame_MapInfo_RiskLevel"] = 68,
    ["Mayday_InGame_MapInfo_High"] = 68,
    ["Mayday_InGame_MapInfo_High_Text"] = 68,
    ["Mayday_InGame_MapInfo_Middle"] = 68,
    ["Mayday_InGame_MapInfo_Middle_Text"] = 68,
    ["Mayday_InGame_MapInfo_Low"] = 68,
    ["Mayday_InGame_MapInfo_Low_Text"] = 68,
    ["Mayday_InGame_MapInfo_Null_Text"] = 68,
    ["Mayday_InGame_MapInfo_Level_High"] = 68,
    ["Mayday_InGame_MapInfo_Level_Middle"] = 68,
    ["Mayday_InGame_MapInfo_Level_Low"] = 68,
    ["Mayday_InGame_MapInfo_Level_SSS"] = 68,
    ["Mayday_InGame_MapInfo_Level_SS"] = 68,
    ["Mayday_InGame_MapInfo_Level_S"] = 68,
    ["Mayday_InGame_MapInfo_Level_A"] = 68,
    ["Mayday_InGame_MapInfo_Level_B"] = 68,
    ["Mayday_InGame_MapInfo_Level_C"] = 68,
    ["Mayday_InGame_MapInfo_Level_D"] = 68,
    ["Mayday_InGame_MapInfo_Specialty"] = 68,
    ["Mayday_InGame_MapInfo_Specialty2"] = 68,
    ["Mayday_InGame_MapInfo_PalaceOwner"] = 68,
    ["Mayday_InGame_MapInfo_PalaceOwner_Meiying"] = 68,
    ["Mayday_InGame_MapInfo_PalaceOwner_Jufeng"] = 68,
    ["Mayday_InGame_MapInfo_PalaceOwner_Tianxin"] = 68,
    ["Mayday_InGame_MapInfo_BossLike"] = 68,
    ["Mayday_InGame_MapInfo_MapCost"] = 68,
    ["Mayday_InGame_SystemNotice_Notice"] = 68,
    ["Mayday_InGame_SystemNotice_Time2Leave1"] = 68,
    ["Mayday_InGame_SystemNotice_NeedBack"] = 68,
    ["Mayday_InGame_SystemNotice_Time2Leave2"] = 68,
    ["Mayday_InGame_SystemNotice_Time2Leave3"] = 68,
    ["Mayday_InGame_SystemNotice_AllDead2Leave"] = 68,
    ["Mayday_InGame_TimeMachine_Hydraulic"] = 68,
    ["Mayday_InGame_TimeMachine_DoorOpen"] = 68,
    ["Mayday_InGame_TimeMachine_DoorClose"] = 68,
    ["Mayday_InGame_TimeMachine_DoorOpenKey"] = 68,
    ["Mayday_InGame_TimeMachine_DoorOpenKey_Toast1"] = 68,
    ["Mayday_InGame_TimeMachine_DoorOpenKey_Toast2"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Go_Press"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Go"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Press"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Go_Tips01"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Go_Tips02"] = 68,
    ["Mayday_InGame_TimeMachine_Tips01"] = 68,
    ["Mayday_InGame_TimeMachine_Tips02"] = 68,
    ["Mayday_InGame_TimeMachine_Tips03"] = 68,
    ["Mayday_InGame_TimeMachine_Button_GoTip01"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Emergency"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Monitor"] = 68,
    ["Mayday_InGame_TimeMachine_Button_ChangeMap"] = 68,
    ["Mayday_InGame_TimeMachine_Button_TargetMap"] = 68,
    ["Mayday_InGame_TimeMachine_Button_TeleportOut"] = 68,
    ["Mayday_InGame_TimeMachine_Button_TeleportBack"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Radar"] = 68,
    ["Mayday_InGame_TimeMachine_Button_Broadcast"] = 68,
    ["Mayday_InGame_TimeMachine_Broadcast_Tips"] = 68,
    ["Mayday_InGame_TimeMachine_Goal"] = 68,
    ["Mayday_InGame_TimeMachine_Location"] = 68,
    ["Mayday_InGame_TimeMachine_Value"] = 68,
    ["Mayday_InGame_TimeMachine_DaysRemain"] = 68,
    ["Mayday_InGame_TimeMachine_BeBusy_Toast"] = 68,
    ["Mayday_InGame_TimeMachine_StartTravel"] = 68,
    ["Mayday_InGame_TimeMachine_FindNewItem"] = 68,
    ["Mayday_InGame_TimeMachine_Toast_NoFlyKPI"] = 68,
    ["Mayday_InGame_DaysRemain"] = 69,
    ["Mayday_InGame_LastDay"] = 69,
    ["Mayday_InGame_Button_Confirm"] = 69,
    ["Mayday_InGame_Button_Cancel"] = 69,
    ["Mayday_InGame_Button_Power"] = 69,
    ["Mayday_InGame_Button_Open"] = 69,
    ["Mayday_InGame_Button_Close"] = 69,
    ["Mayday_InGame_Button_Pick"] = 69,
    ["Mayday_InGame_Button_Drop"] = 69,
    ["Mayday_InGame_Button_Scan"] = 69,
    ["Mayday_InGame_Scan_Item"] = 69,
    ["Mayday_InGame_Scan_Ghost"] = 69,
    ["Mayday_InGame_Scan_RiskLevel"] = 69,
    ["Mayday_InGame_Scan_ItemPick1"] = 69,
    ["Mayday_InGame_Scan_ItemPick2"] = 69,
    ["Mayday_InGame_Scan_Ship"] = 69,
    ["Mayday_InGame_Scan_Door1"] = 69,
    ["Mayday_InGame_Scan_HighValue"] = 69,
    ["Mayday_InGame_Scan_Door2"] = 69,
    ["Mayday_InGame_TotalValue"] = 69,
    ["Mayday_InGame_Button_Talk"] = 69,
    ["Mayday_InGame_VoiceChannel_Close"] = 69,
    ["Mayday_InGame_VoiceChannel_Near"] = 69,
    ["Mayday_InGame_VoiceChannel_OB"] = 69,
    ["Mayday_InGame_VoiceChannel_OB_Tips"] = 69,
    ["Mayday_InGame_OB_Recheck"] = 69,
    ["Mayday_InGame_Tips_OpenMic"] = 69,
    ["Mayday_InGame_Tips_TurnOnSpeaker"] = 69,
    ["Mayday_InGame_OpenDoor"] = 69,
    ["Mayday_InGame_OpenDoor_Progress"] = 69,
    ["Mayday_InGame_ClimbUp"] = 69,
    ["Mayday_InGame_Fall"] = 69,
    ["Mayday_InGame_Back"] = 69,
    ["Mayday_InGame_Alive"] = 69,
    ["Mayday_InGame_Dead"] = 69,
    ["Mayday_InGame_Dead2"] = 69,
    ["Mayday_InGame_WaitingAfterDead"] = 69,
    ["Mayday_InGame_Body"] = 69,
    ["Mayday_InGame_Body_Button_Check"] = 69,
    ["Mayday_InGame_Body_Button_Report"] = 69,
    ["Mayday_InGame_Body_Button_Move"] = 69,
    ["Mayday_InGame_Body_New"] = 69,
    ["Mayday_InGame_Body_Info"] = 69,
    ["Mayday_InGame_Body_Info2"] = 69,
    ["Mayday_InGame_Body_Location"] = 69,
    ["Mayday_InGame_Body_Location_Room"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom01"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom02"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom03"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom04"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom05"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom06"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom07"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom08"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom09"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom10"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom11"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom12"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom13"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom14"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom15"] = 69,
    ["Mayday_InGame_Body_Location_RainRoom16"] = 69,
    ["Mayday_InGame_Body_Location_Forbidden"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest01"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest02"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest03"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest04"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest05"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest06"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest07"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest08"] = 69,
    ["Mayday_InGame_Body_Location_Rainforest09"] = 69,
    ["Mayday_InGame_Need2Guys"] = 69,
    ["Mayday_InGame_Body_DeathTime"] = 69,
    ["Mayday_InGame_Body_DeathLocale"] = 69,
    ["Mayday_InGame_Body_DeathInfo"] = 69,
    ["Mayday_InGame_Body_DeathInfo_Nobody"] = 69,
    ["Mayday_InGame_Body_Reason"] = 69,
    ["Mayday_InGame_Body_Reason_Default"] = 69,
    ["Mayday_InGame_Body_Reason_Fall"] = 69,
    ["Mayday_InGame_Body_Reason_Monster"] = 69,
    ["Mayday_InGame_Body_Reason_Teammate"] = 69,
    ["Mayday_InGame_Body_Reason_AIInjured"] = 69,
    ["Mayday_InGame_Body_Reason_Water"] = 69,
    ["Mayday_InGame_Body_Reason_Vote"] = 69,
    ["Mayday_InGame_Body_Reason_Accident"] = 69,
    ["Mayday_InGame_Body_Reason_MoleAttack"] = 69,
    ["Mayday_InGame_Body_Reason_DeathZone"] = 69,
    ["Mayday_InGame_Body_Reason_Fly"] = 69,
    ["Mayday_InGame_Body_Reason_Share"] = 69,
    ["Mayday_InGame_Body_Reason_Unkown"] = 69,
    ["Mayday_InGame_Body_Toast_NewOne"] = 69,
    ["Mayday_InGame_Item"] = 69,
    ["Mayday_InGame_Item_ItemPick_Tips1"] = 69,
    ["Mayday_InGame_Item_ItemPick_Tips2"] = 69,
    ["Mayday_InGame_Item_ItemPick_Toast"] = 69,
    ["Mayday_InGame_Weight"] = 69,
    ["Mayday_InGame_Item_Value"] = 69,
    ["Mayday_InGame_Item_Value_High"] = 69,
    ["Mayday_InGame_Twohands"] = 69,
    ["Mayday_InGame_Item_Flashlight"] = 70,
    ["Mayday_InGame_Item_Flashlight_Info"] = 70,
    ["Mayday_InGame_Item_FlashlightPro"] = 70,
    ["Mayday_InGame_Item_FlashlightPro_Info"] = 70,
    ["Mayday_InGame_Item_Stick"] = 70,
    ["Mayday_InGame_Item_Stick_Info"] = 70,
    ["Mayday_InGame_Item_WalkieTalkie"] = 70,
    ["Mayday_InGame_Item_WalkieTalkie_Info"] = 70,
    ["Mayday_InGame_Item_WalkieTalkie_UseText"] = 70,
    ["Mayday_InGame_Item_Stereo"] = 70,
    ["Mayday_InGame_Item_Stereo_Info"] = 70,
    ["Mayday_InGame_Item_Stereo_BtnOn"] = 70,
    ["Mayday_InGame_Item_Stereo_BtnOff"] = 70,
    ["Mayday_InGame_Item_Stereo_BtnSkip"] = 70,
    ["Mayday_InGame_Item_Stereo_BtnDance"] = 70,
    ["Mayday_InGame_Item_Ladder"] = 70,
    ["Mayday_InGame_Item_Ladder_Info"] = 70,
    ["Mayday_InGame_Item_Ladder_ToastNo"] = 70,
    ["Mayday_InGame_Item_Jetpack"] = 70,
    ["Mayday_InGame_Item_Jetpack_Info"] = 70,
    ["Mayday_InGame_Item_Jetpack_UseText"] = 70,
    ["Mayday_InGame_Item_Lockpick"] = 70,
    ["Mayday_InGame_Item_Lockpick_Info"] = 70,
    ["Mayday_InGame_Item_Lockpick_UseText"] = 70,
    ["Mayday_InGame_Item_Shotgun"] = 70,
    ["Mayday_InGame_Item_Bullet"] = 70,
    ["Mayday_InGame_Item_TimeGem"] = 70,
    ["Mayday_InGame_Item_TimeGem_Info"] = 70,
    ["Mayday_InGame_Item_GoldBrick"] = 70,
    ["Mayday_InGame_Item_GoldBrick_Info"] = 70,
    ["Mayday_InGame_Item_DivinationCrystal"] = 70,
    ["Mayday_InGame_Item_DivinationCrystal_Info"] = 70,
    ["Mayday_InGame_Item_SweetPoison"] = 70,
    ["Mayday_InGame_Item_SweetPoison_Info"] = 70,
    ["Mayday_InGame_Item_OwlRobot"] = 70,
    ["Mayday_InGame_Item_AntiqueVase"] = 70,
    ["Mayday_InGame_Item_HotPot"] = 70,
    ["Mayday_InGame_Item_HotPot_Info"] = 70,
    ["Mayday_InGame_Item_TableLamp"] = 70,
    ["Mayday_InGame_Item_DemonMask"] = 70,
    ["Mayday_InGame_Item_DemonMask_Info"] = 70,
    ["Mayday_InGame_Item_DemonMask_UseText"] = 70,
    ["Mayday_InGame_Item_DrinkingSet"] = 70,
    ["Mayday_InGame_Item_PigNose"] = 70,
    ["Mayday_InGame_Item_Cookies"] = 70,
    ["Mayday_InGame_Item_KeyRuin"] = 70,
    ["Mayday_InGame_Item_Key_Info"] = 70,
    ["Mayday_InGame_DoorNeedKey"] = 70,
    ["Mayday_InGame_Item_CrystalSkull"] = 70,
    ["Mayday_InGame_Item_CrystalSkull_Info"] = 70,
    ["Mayday_InGame_Item_GoldMask"] = 70,
    ["Mayday_InGame_Item_GoldMask_Info"] = 70,
    ["Mayday_InGame_Item_GoldNecklace"] = 70,
    ["Mayday_InGame_Item_DeerDecoration"] = 70,
    ["Mayday_InGame_Item_SpiceJar"] = 70,
    ["Mayday_InGame_Item_DivinationTablet"] = 70,
    ["Mayday_InGame_Item_CeramicFigure"] = 70,
    ["Mayday_InGame_Item_SweetBread"] = 70,
    ["Mayday_InGame_Item_EarthenwareJar"] = 70,
    ["Mayday_InGame_Item_OilLamp"] = 70,
    ["Mayday_InGame_Item_TimeGem2"] = 70,
    ["Mayday_InGame_Item_GoldBrick2"] = 70,
    ["Mayday_InGame_Item_DivinationCrystal2"] = 70,
    ["Mayday_InGame_Item_SweetPoison2"] = 70,
    ["Mayday_InGame_Item_OwlRobot2"] = 70,
    ["Mayday_InGame_Item_AntiqueVase2"] = 70,
    ["Mayday_InGame_Item_HotPot2"] = 70,
    ["Mayday_InGame_Item_TableLamp2"] = 70,
    ["Mayday_InGame_Item_DemonMask2"] = 70,
    ["Mayday_InGame_Item_DrinkingSet2"] = 70,
    ["Mayday_InGame_Item_KeyRuin2"] = 70,
    ["Mayday_InGame_Item_CrystalSkull2"] = 70,
    ["Mayday_InGame_Item_GoldMask2"] = 70,
    ["Mayday_InGame_Item_GoldNecklace2"] = 70,
    ["Mayday_InGame_Item_DeerDecoration2"] = 70,
    ["Mayday_InGame_Item_SpiceJar2"] = 70,
    ["Mayday_InGame_Item_DivinationTablet2"] = 70,
    ["Mayday_InGame_Item_CeramicFigure2"] = 70,
    ["Mayday_InGame_Item_SweetBread2"] = 70,
    ["Mayday_InGame_Item_EarthenwareJar2"] = 70,
    ["Mayday_InGame_Item_OilLamp2"] = 70,
    ["Mayday_InGame_Item_TimeGem3"] = 70,
    ["Mayday_InGame_Item_GoldBrick3"] = 70,
    ["Mayday_InGame_Item_DivinationCrystal3"] = 70,
    ["Mayday_InGame_Item_SweetPoison3"] = 70,
    ["Mayday_InGame_Item_OwlRobot3"] = 70,
    ["Mayday_InGame_Item_AntiqueVase3"] = 70,
    ["Mayday_InGame_Item_HotPot3"] = 70,
    ["Mayday_InGame_Item_TableLamp3"] = 70,
    ["Mayday_InGame_Item_DemonMask3"] = 70,
    ["Mayday_InGame_Item_DrinkingSet3"] = 70,
    ["Mayday_InGame_Item_KeyRuin3"] = 70,
    ["Mayday_InGame_Item_CrystalSkull3"] = 70,
    ["Mayday_InGame_Item_GoldMask3"] = 70,
    ["Mayday_InGame_Item_GoldNecklace3"] = 70,
    ["Mayday_InGame_Item_DeerDecoration3"] = 70,
    ["Mayday_InGame_Item_SpiceJar3"] = 70,
    ["Mayday_InGame_Item_DivinationTablet3"] = 70,
    ["Mayday_InGame_Item_CeramicFigure3"] = 70,
    ["Mayday_InGame_Item_SweetBread3"] = 70,
    ["Mayday_InGame_Item_EarthenwareJar3"] = 71,
    ["Mayday_InGame_Item_OilLamp3"] = 71,
    ["Mayday_InGame_Item_TimeGem4"] = 71,
    ["Mayday_InGame_Item_GoldBrick4"] = 71,
    ["Mayday_InGame_Item_DivinationCrystal4"] = 71,
    ["Mayday_InGame_Item_SweetPoison4"] = 71,
    ["Mayday_InGame_Item_OwlRobot4"] = 71,
    ["Mayday_InGame_Item_AntiqueVase4"] = 71,
    ["Mayday_InGame_Item_HotPot4"] = 71,
    ["Mayday_InGame_Item_TableLamp4"] = 71,
    ["Mayday_InGame_Item_DemonMask4"] = 71,
    ["Mayday_InGame_Item_DrinkingSet4"] = 71,
    ["Mayday_InGame_Item_KeyRuin4"] = 71,
    ["Mayday_InGame_Item_CrystalSkull4"] = 71,
    ["Mayday_InGame_Item_GoldMask4"] = 71,
    ["Mayday_InGame_Item_GoldNecklace4"] = 71,
    ["Mayday_InGame_Item_DeerDecoration4"] = 71,
    ["Mayday_InGame_Item_SpiceJar4"] = 71,
    ["Mayday_InGame_Item_DivinationTablet4"] = 71,
    ["Mayday_InGame_Item_CeramicFigure4"] = 71,
    ["Mayday_InGame_Item_SweetBread4"] = 71,
    ["Mayday_InGame_Item_EarthenwareJar4"] = 71,
    ["Mayday_InGame_Item_OilLamp4"] = 71,
    ["Mayday_InGame_Item_Monster01"] = 71,
    ["Mayday_InGame_Item_Monster02"] = 71,
    ["Mayday_InGame_Item_Monster03"] = 71,
    ["Mayday_InGame_Item_Monster04"] = 71,
    ["Mayday_InGame_Item_TeleportPlatform"] = 71,
    ["Mayday_InGame_Item_TeleportPlatform_Info"] = 71,
    ["Mayday_InGame_Item_TeleportPlatform_Btn"] = 71,
    ["Mayday_InGame_Item_TeleportPlatform_Toast"] = 71,
    ["Mayday_InGame_Item_TeleportPlatform_ToastNo"] = 71,
    ["Mayday_InGame_Item_Shutgun"] = 71,
    ["Mayday_InGame_Item_Shutgun_Info"] = 71,
    ["Mayday_InGame_Item_MoveBox"] = 71,
    ["Mayday_InGame_Item_MoveBox_Info1"] = 71,
    ["Mayday_InGame_Item_MoveBox_Info2"] = 71,
    ["Mayday_InGame_Item_MoveBox_ButtonSave"] = 71,
    ["Mayday_InGame_Item_MoveBox_ButtonTake"] = 71,
    ["Mayday_InGame_Item_MoveBox_ButtonPull"] = 71,
    ["Mayday_InGame_Item_MoveBox_ButtonPush"] = 71,
    ["Mayday_InGame_Item_MoveBox_ButtonLeave"] = 71,
    ["Mayday_InGame_Item_MoveBox_GoodsInBox"] = 71,
    ["Mayday_InGame_Item_MoveBox_TakeGoodsInBox"] = 71,
    ["Mayday_InGame_Item_MoveBox_Capacity"] = 71,
    ["Mayday_InGame_Item_Toast_Save"] = 71,
    ["Mayday_InGame_Item_Toast_Take"] = 71,
    ["Mayday_InGame_Item_Toast_Full"] = 71,
    ["Mayday_InGame_Item_Tips_RestRange_Pack"] = 71,
    ["Mayday_InGame_Item_Tips_Distance_MoveBox"] = 71,
    ["Mayday_InGame_ItemTag"] = 71,
    ["Mayday_InGame_ItemTag_Gold"] = 71,
    ["Mayday_InGame_ItemTag_Energe"] = 71,
    ["Mayday_InGame_ItemTag_Sacrifice"] = 71,
    ["Mayday_InGame_ItemTag_Mask"] = 71,
    ["Mayday_InGame_ItemTag_Animal"] = 71,
    ["Mayday_InGame_ItemTag_Jewelry"] = 71,
    ["Mayday_InGame_ItemTag_Decoration"] = 71,
    ["Mayday_InGame_ItemTag_Food"] = 71,
    ["Mayday_InGame_ItemTag_Spicy"] = 71,
    ["Mayday_InGame_ItemTag_Sweet"] = 71,
    ["Mayday_InGame_ItemTag_Divination"] = 71,
    ["Mayday_InGame_ItemTag_Effigy"] = 71,
    ["Mayday_InGame_ItemTag_Life"] = 71,
    ["Mayday_InGame_Item_Tips_Pick_Me"] = 71,
    ["Mayday_InGame_Item_Tips_Pick_Member"] = 71,
    ["Mayday_InGame_Item_Tips_Pick_Self"] = 71,
    ["Mayday_InGame_Item_Tips_CannotPick_Hand"] = 71,
    ["Mayday_InGame_Item_Tips_CannotPick_Pack"] = 71,
    ["Mayday_InGame_Money"] = 71,
    ["Mayday_InGame_PickThingsValue"] = 71,
    ["Mayday_InGame_Scene_MainEntrance"] = 71,
    ["Mayday_InGame_Scene_Terminal"] = 71,
    ["Mayday_InGame_Scene_Terminal_Info"] = 71,
    ["Mayday_InGame_DoorMonitor"] = 71,
    ["Mayday_InGame_DoorMonitor_Info"] = 71,
    ["Mayday_InGame_Store"] = 71,
    ["Mayday_InGame_Store_Info"] = 71,
    ["Mayday_InGame_Store_Sell"] = 71,
    ["Mayday_InGame_Store_Sell_Press"] = 71,
    ["Mayday_InGame_Store_SellConfirmed"] = 71,
    ["Mayday_InGame_Store_SellText"] = 71,
    ["Mayday_InGame_Store_MoneyNotEnough"] = 71,
    ["Mayday_InGame_Store_TeamsMoney"] = 71,
    ["Mayday_InGame_Store_Team"] = 71,
    ["Mayday_InGame_Store_Recycle"] = 71,
    ["Mayday_InGame_Store_GotMoneyToast"] = 71,
    ["Mayday_InGame_Store_Buy"] = 71,
    ["Mayday_InGame_Store_Buy_Limited01"] = 71,
    ["Mayday_InGame_Store_Buy_Limited02"] = 71,
    ["Mayday_InGame_Store_GoodsName"] = 71,
    ["Mayday_InGame_Store_GoodsQuantity"] = 71,
    ["Mayday_InGame_Store_GoodsPrice"] = 71,
    ["Mayday_InGame_Store_GoodsTotalValue"] = 71,
    ["Mayday_InGame_Store_GoodsSellPrice"] = 71,
    ["Mayday_InGame_Store_Tatal"] = 71,
    ["Mayday_InGame_Store_SellDiscount01"] = 71,
    ["Mayday_InGame_Store_SellDiscount02"] = 71,
    ["Mayday_InGame_Store_SellDiscount03"] = 71,
    ["Mayday_InGame_Store_IsFinish"] = 71,
    ["Mayday_InGame_Store_Wait"] = 72,
    ["Mayday_InGame_Store_Express00"] = 72,
    ["Mayday_InGame_Store_ExpressToast01"] = 72,
    ["Mayday_InGame_Store_ExpressToast02"] = 72,
    ["Mayday_InGame_Store_ExpressToast03"] = 72,
    ["Mayday_InGame_Store_ExpressToast04"] = 72,
    ["Mayday_InGame_Store_ExpressToast05"] = 72,
    ["Mayday_InGame_Store_ExpressToast06"] = 72,
    ["Mayday_InGame_Store_ExpressToast_NoMoney"] = 72,
    ["Mayday_InGame_Store_Express_Wait"] = 72,
    ["Mayday_InGame_Store_Express_Count"] = 72,
    ["Mayday_InGame_Store_Express_CountDown"] = 72,
    ["Mayday_InGame_Store_Bought"] = 72,
    ["Mayday_InGame_Store_BuyDiscount"] = 72,
    ["Mayday_InGame_Store_Sale"] = 72,
    ["Mayday_InGame_KingsPalace_HandIn1"] = 72,
    ["Mayday_InGame_KingsPalace_HandIn2"] = 72,
    ["Mayday_Ingame_TeamRank"] = 72,
    ["Mayday_Ingame_TeamRank_SSS"] = 72,
    ["Mayday_Ingame_TeamRank_SS"] = 72,
    ["Mayday_Ingame_TeamRank_S"] = 72,
    ["Mayday_Ingame_TeamRank_A"] = 72,
    ["Mayday_Ingame_TeamRank_B"] = 72,
    ["Mayday_Ingame_TeamRank_C"] = 72,
    ["Mayday_Ingame_TeamRank_D"] = 72,
    ["Mayday_Ingame_TeamRank_E"] = 72,
    ["Mayday_Ingame_TeamRank_F"] = 72,
    ["Mayday_Ingame_TeamPickValue"] = 72,
    ["Mayday_Ingame_LocationValue"] = 72,
    ["Mayday_InGame_PlayerState_Die"] = 72,
    ["Mayday_InGame_PlayerState_DieReason"] = 72,
    ["Mayday_InGame_OB"] = 72,
    ["Mayday_InGame_Vote"] = 72,
    ["Mayday_InGame_Vote_Text"] = 72,
    ["Mayday_InGame_Fines"] = 72,
    ["Mayday_InGame_DeadPlayerCount"] = 72,
    ["Mayday_InGame_GetBackBody"] = 72,
    ["Mayday_InGame_DUE"] = 72,
    ["Mayday_InGame_DayEnd"] = 72,
    ["Mayday_InGame_DayEnd_GoalProgress"] = 72,
    ["Mayday_InGame_DayEnd_WorkHardLines01"] = 72,
    ["Mayday_InGame_DayEnd_WorkHardLines02"] = 72,
    ["Mayday_InGame_DayEnd_WorkHardLines03"] = 72,
    ["Mayday_InGame_DayEnd_LastDayLines01"] = 72,
    ["Mayday_InGame_DayEnd_LastDayLines02"] = 72,
    ["Mayday_InGame_DayEnd_DoneLines01"] = 72,
    ["Mayday_InGame_DayEnd_PalaceLines01"] = 72,
    ["Mayday_InGame_GameStart_BossLines01"] = 72,
    ["Mayday_InGame_GameStart_BossLines02"] = 72,
    ["Mayday_InGame_GameStart_BossLines03"] = 72,
    ["Mayday_InGame_GameStart_BossLines04"] = 72,
    ["Mayday_InGame_GameStart_BossLines05"] = 72,
    ["Mayday_InGame_GameStart_BossLines06"] = 72,
    ["Mayday_InGame_Guide_Target"] = 72,
    ["Mayday_InGame_Guide_Gather01"] = 72,
    ["Mayday_InGame_Guide_Gather02"] = 72,
    ["Mayday_InGame_Guide_Go"] = 72,
    ["Mayday_InGame_Guide_Back"] = 72,
    ["Mayday_InGame_Guide_MeetBoss"] = 72,
    ["Mayday_InGame_Guide_PutAllGoods"] = 72,
    ["Mayday_InGame_Guide_End"] = 72,
    ["Mayday_EndGame_Seccess"] = 72,
    ["Mayday_EndGame_Seccess_Info"] = 72,
    ["Mayday_EndGame_Failed"] = 72,
    ["Mayday_EndGame_Failed_Info"] = 72,
    ["Mayday_EndGame_StarSeccess"] = 72,
    ["Mayday_EndGame_SpySeccess"] = 72,
    ["Mayday_EndGame_StarFailed"] = 72,
    ["Mayday_EndGame_SpyFailed"] = 72,
    ["Mayday_EndGame_Reason_Spy01"] = 72,
    ["Mayday_EndGame_Reason_Spy02"] = 72,
    ["Mayday_EndGame_Reason_Spy03"] = 72,
    ["Mayday_EndGame_Reason_Spy04"] = 72,
    ["Mayday_Monster_ShadowGhost"] = 72,
    ["Mayday_Monster_T009"] = 72,
    ["Mayday_Monster_ScarletSpider"] = 72,
    ["Mayday_Monster_Mole"] = 72,
    ["Mayday_Monster_AbyssalLizard"] = 72,
    ["Mayday_Monster_BatApe"] = 72,
    ["Mayday_Monster_BatApe_QTE"] = 72,
    ["Mayday_Monster_BatApe_ToastHelp01"] = 72,
    ["Mayday_Monster_BatApe_ToastHelp02"] = 72,
    ["Mayday_Monster_DemonMask"] = 72,
    ["Mayday_Monster_StoneGuard"] = 72,
    ["Mayday_Monster_LordAbyss"] = 72,
    ["Mayday_Monster_ClockGhost"] = 72,
    ["Mayday_Monster_RaccoonToy"] = 72,
    ["Mayday_Monster_BatSwarm"] = 72,
    ["Mayday_Monster_SlimeMimics"] = 72,
    ["Mayday_Monster_GiantGargoyle"] = 72,
    ["Mayday_Monster_GiantGargoyle_QTE"] = 72,
    ["Mayday_Monster_SandLocust"] = 72,
    ["Mayday_InGame_Quit_Recheck"] = 72,
    ["Mayday_InGame_NoOxygen"] = 72,
    ["Mayday_FTUE_Step01_Title"] = 72,
    ["Mayday_FTUE_Step01_Text"] = 72,
    ["Mayday_FTUE_Step02_Title"] = 72,
    ["Mayday_FTUE_Step02_Text"] = 72,
    ["Mayday_FTUE_Step03_Title"] = 72,
    ["Mayday_FTUE_Step03_Text"] = 72,
    ["Mayday_FTUE_Step04_Title"] = 73,
    ["Mayday_FTUE_Step04_Text"] = 73,
    ["Mayday_FTUE_Step05_Title"] = 73,
    ["Mayday_FTUE_Step05_Text"] = 73,
    ["Mayday_FTUESpy_Step01_Title"] = 73,
    ["Mayday_FTUESpy_Step01_Text"] = 73,
    ["Mayday_FTUESpy_Step02_Title"] = 73,
    ["Mayday_FTUESpy_Step02_Text"] = 73,
    ["Mayday_FTUESpy_Step03_Title"] = 73,
    ["Mayday_FTUESpy_Step03_Text"] = 73,
    ["Mayday_FTUESpy_Step04_Title"] = 73,
    ["Mayday_FTUESpy_Step04_Text"] = 73,
    ["Mayday_FTUESpy_Step05_Title"] = 73,
    ["Mayday_FTUESpy_Step05_Text"] = 73,
    ["Mayday_Spy"] = 73,
    ["Mayday_Spy_WhoAmI"] = 73,
    ["Mayday_Spy_IdentityInfo"] = 73,
    ["Mayday_Spy_Good"] = 73,
    ["Mayday_Spy_GoodMissionInfo"] = 73,
    ["Mayday_Spy_GoodHint"] = 73,
    ["Mayday_Spy_GoodHintInfo"] = 73,
    ["Mayday_Spy_GoodMissionInfo01"] = 73,
    ["Mayday_Spy_GoodMissionInfo02"] = 73,
    ["Mayday_Spy_GoodMissionTitle"] = 73,
    ["Mayday_Spy_GoodMissionText01"] = 73,
    ["Mayday_Spy_GoodMissionText02"] = 73,
    ["Mayday_Spy_GoodWin_Tips01"] = 73,
    ["Mayday_Spy_Spy"] = 73,
    ["Mayday_Spy_SpyMissionTitle"] = 73,
    ["Mayday_Spy_SpyMissionInfo"] = 73,
    ["Mayday_Spy_SpyMissionInfo01"] = 73,
    ["Mayday_Spy_SpyMissionInfo02"] = 73,
    ["Mayday_Spy_SpyPowerInfo"] = 73,
    ["Mayday_Spy_SpyMissionTitile"] = 73,
    ["Mayday_Spy_SpyMissionText01"] = 73,
    ["Mayday_Spy_SpyMissionText02"] = 73,
    ["Mayday_Spy_SpyMissionText03"] = 73,
    ["Mayday_Spy_IdentityConfirmed"] = 73,
    ["Mayday_Spy_SpyPower"] = 73,
    ["Mayday_Spy_SpyPowerList"] = 73,
    ["Mayday_Spy_SpyPower01"] = 73,
    ["Mayday_Spy_SpyPower01Icon"] = 73,
    ["Mayday_Spy_SpyPower01Text"] = 73,
    ["Mayday_Spy_SpyPower01Text2"] = 73,
    ["Mayday_Spy_SpyPower01ChooseMonster"] = 73,
    ["Mayday_Spy_SpyPower01Toast01"] = 73,
    ["Mayday_Spy_SpyPower01Toast02"] = 73,
    ["Mayday_Spy_SpyPower02"] = 73,
    ["Mayday_Spy_SpyPower02Icon"] = 73,
    ["Mayday_Spy_SpyPower02Text"] = 73,
    ["Mayday_Spy_SpyPower02Text2"] = 73,
    ["Mayday_Spy_SpyPower02Toast"] = 73,
    ["Mayday_Spy_SpyPower03"] = 73,
    ["Mayday_Spy_SpyPower03Icon"] = 73,
    ["Mayday_Spy_SpyPower03Text"] = 73,
    ["Mayday_Spy_SpyPower03Text2"] = 73,
    ["Mayday_Spy_SpyPower_NotEnough"] = 73,
    ["Mayday_Spy_SpyPower_CD"] = 73,
    ["Mayday_Spy_SpyPower_Boat"] = 73,
    ["Mayday_Spy_MeetingStart"] = 73,
    ["Mayday_Spy_Meeting_Tips1"] = 73,
    ["Mayday_Spy_Meeting_Tips2"] = 73,
    ["Mayday_Spy_Meeting_Tips3"] = 73,
    ["Mayday_Spy_Meeting_Tips4"] = 73,
    ["Mayday_Spy_Meeting_Tips5"] = 73,
    ["Mayday_Spy_Meeting_Tips6"] = 73,
    ["Mayday_Spy_FoundBody"] = 73,
    ["Mayday_Spy_FoundBodyText"] = 73,
    ["Mayday_Spy_EmergencyMeetingText"] = 73,
    ["Mayday_Spy_Reporter"] = 73,
    ["Mayday_Spy_Discussion"] = 73,
    ["Mayday_Spy_Discussion_CannotDiscuss1"] = 73,
    ["Mayday_Spy_Discussion_CannotDiscuss2"] = 73,
    ["Mayday_Spy_Discussion_CannotDiscuss3"] = 73,
    ["Mayday_Spy_Discussion_Tips01"] = 73,
    ["Mayday_Spy_Discussion_Close"] = 73,
    ["Mayday_Spy_Discussion_StartDiscuss"] = 73,
    ["Mayday_Spy_Discussion_PlayersTurn"] = 73,
    ["Mayday_Spy_Discussion_YourTurnTips"] = 73,
    ["Mayday_Spy_Discussion_Saying"] = 73,
    ["Mayday_Spy_Discussion_Meeting"] = 73,
    ["Mayday_Spy_Discussion_Pass"] = 73,
    ["Mayday_Spy_Discussion_Button_Over"] = 73,
    ["Mayday_Spy_Discussion_Button_Say"] = 73,
    ["Mayday_Spy_Discussion_FoundBody"] = 73,
    ["Mayday_Spy_Discussion_FoundBodyText"] = 73,
    ["Mayday_Spy_Discussion_BtnInfo"] = 73,
    ["Mayday_Spy_Vote"] = 73,
    ["Mayday_Spy_Vote2"] = 73,
    ["Mayday_Spy_Vote_Text"] = 73,
    ["Mayday_Spy_Vote_Progress"] = 73,
    ["Mayday_Spy_Vote_Finish"] = 73,
    ["Mayday_Spy_Vote_Button_Vote"] = 73,
    ["Mayday_Spy_Vote_Button_Cancel"] = 73,
    ["Mayday_Spy_Vote_Recheck"] = 73,
    ["Mayday_Spy_Vote_Recheck_Text"] = 73,
    ["Mayday_Spy_Vote_Recheck_Yes"] = 73,
    ["Mayday_Spy_Vote_Recheck_No"] = 73,
    ["Mayday_Spy_Vote_Voted"] = 73,
    ["Mayday_Spy_Vote_Result_NoOne"] = 73,
    ["Mayday_Spy_Vote_Result_NoOneText"] = 74,
    ["Mayday_Spy_Vote_Result_SomeOne"] = 74,
    ["Mayday_Spy_Vote_Result_SomeOne01"] = 74,
    ["Mayday_Spy_Vote_Result_SomeOne02"] = 74,
    ["Mayday_Spy_Vote_Toast"] = 74,
    ["Mayday_Spy_DeadNotice"] = 74,
    ["Mayday_ItemBox"] = 74,
    ["Mayday_ItemBox_Button_HandIn"] = 74,
    ["Mayday_ItemBox_Content"] = 74,
    ["Mayday_ItemBox_Content_Btn"] = 74,
    ["Mayday_ItemBox_Info"] = 74,
    ["Mayday_ItemBox_Button_Confirmed"] = 74,
    ["Mayday_ItemBox_Discount"] = 74,
    ["Mayday_ItemBox_Value"] = 74,
    ["Mayday_ItemBox_Toast"] = 74,
    ["Mayday_ItemBox_Progress"] = 74,
    ["Mayday_ItemBox_Full"] = 74,
    ["Mayday_ItemBox_Spy"] = 74,
    ["Mayday_ItemBox_WillGet"] = 74,
    ["Mayday_ItemBox_Got"] = 74,
    ["Mayday_ItemBox_GotItems"] = 74,
    ["Mayday_InGame_ItemBox_Tips1"] = 74,
    ["Mayday_InGame_ItemBox_Tips2"] = 74,
    ["Mayday_InGame_ItemBox_Tips3"] = 74,
    ["Mayday_InGame_ItemBox_Tips4"] = 74,
    ["Mayday_InGame_ItemBox_Tips5"] = 74,
    ["Mayday_Occupation"] = 74,
    ["Mayday_Occupation_WhoAmI"] = 74,
    ["Mayday_Occupation_SkillList"] = 74,
    ["Mayday_Occupation_SkillCD"] = 74,
    ["Mayday_Occupation_SpySkill"] = 74,
    ["Mayday_Occupation_OccupationSkill"] = 74,
    ["Mayday_Occupation_Info"] = 74,
    ["Mayday_Occupation_Economics"] = 74,
    ["Mayday_Occupation_Assist"] = 74,
    ["Mayday_Occupation_SkillUp"] = 74,
    ["Mayday_Occupation01"] = 74,
    ["Mayday_Occupation01Skill01"] = 74,
    ["Mayday_Occupation01Skill01_Info"] = 74,
    ["Mayday_Occupation01Skill02_toast1"] = 74,
    ["Mayday_Occupation_toast_Land1"] = 74,
    ["Mayday_InGame_Item_Beacon"] = 74,
    ["Mayday_InGame_Item_Beacon_Info"] = 74,
    ["Mayday_InGame_Item_Beacon_ButtonPlace"] = 74,
    ["Mayday_InGame_Item_Beacon_ButtonRecovery"] = 74,
    ["Mayday_InGame_Item_Beacon_ButtonRotate"] = 74,
    ["Mayday_InGame_Item_Beacon_Tips1"] = 74,
    ["Mayday_InGame_Item_Pad"] = 74,
    ["Mayday_InGame_Item_Pad_Info"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_Room"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_Wild"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_Normal"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_Map"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_Teleport"] = 74,
    ["Mayday_InGame_Item_Pad_Btn_TeleportInfo"] = 74,
    ["Mayday_Occupation01_Toast01"] = 74,
    ["Mayday_Occupation01_Toast02"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff01"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff02"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff03"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff04"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff05"] = 74,
    ["Mayday_Occupation01_Unlimited_SkillBuff10"] = 74,
    ["Mayday_Occupation02"] = 74,
    ["Mayday_Occupation02Skill01"] = 74,
    ["Mayday_Occupation02Skill01_Info"] = 74,
    ["Mayday_Occupation02Skill01_InfoSpy"] = 74,
    ["Mayday_InGame_Item_RopeGun"] = 74,
    ["Mayday_InGame_Item_RopeGun_Info"] = 74,
    ["Mayday_InGame_Item_RopeGun_ButtonShoot"] = 74,
    ["Mayday_InGame_Item_RopeGun_ButtonPull"] = 74,
    ["Mayday_InGame_Item_RopeGun_ButtonBreak"] = 74,
    ["Mayday_InGame_Item_RopeGun_ButtonImprison"] = 74,
    ["Mayday_Occupation02_ToastImprison"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff01"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff02"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff03"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff04"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff05"] = 74,
    ["Mayday_Occupation02_Unlimited_SkillBuff10"] = 74,
    ["Mayday_Occupation03"] = 74,
    ["Mayday_Occupation03Skill01Self"] = 74,
    ["Mayday_Occupation03Skill01"] = 74,
    ["Mayday_Occupation03Skill01Self_Info"] = 74,
    ["Mayday_Occupation03Skill01_Info"] = 74,
    ["Mayday_Occupation03Skill01_InfoSpy"] = 74,
    ["Mayday_InGame_Item_MedicineGun"] = 74,
    ["Mayday_InGame_Item_MedicineGun_Info"] = 74,
    ["Mayday_InGame_Item_MedicineGun_BtnHealth"] = 74,
    ["Mayday_InGame_Item_MedicineGun_BtnKill"] = 74,
    ["Mayday_Occupation03_Toast01"] = 74,
    ["Mayday_Occupation03_Toast02"] = 74,
    ["Mayday_Occupation03_Toast03"] = 74,
    ["Mayday_Occupation03_Toast04"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff01"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff02"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff03"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff04"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff05"] = 74,
    ["Mayday_Occupation03_Unlimited_SkillBuff10"] = 74,
    ["Mayday_Occupation04"] = 75,
    ["Mayday_Occupation04Skill01"] = 75,
    ["Mayday_Occupation04Skill01_Info"] = 75,
    ["Mayday_Occupation04Skill01_InfoSpy"] = 75,
    ["Mayday_InGame_Item_InsectBottle"] = 75,
    ["Mayday_InGame_Item_InsectBottle_Info"] = 75,
    ["Mayday_InGame_Item_InsectBottle_BtnSmoke"] = 75,
    ["Mayday_InGame_Item_InsectBottle_BtnKill"] = 75,
    ["Mayday_Occupation04_Toast01"] = 75,
    ["Mayday_Occupation04_Toast02"] = 75,
    ["Mayday_Occupation04_Toast03"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff01"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff02"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff03"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff04"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff05"] = 75,
    ["Mayday_Occupation04_Unlimited_SkillBuff10"] = 75,
    ["Mayday_Occupation05"] = 75,
    ["Mayday_Occupation05Skill01"] = 75,
    ["Mayday_Occupation05Skill01_Info"] = 75,
    ["Mayday_Occupation05Skill01_Tips"] = 75,
    ["Mayday_Occupation05Skill01_Toast"] = 75,
    ["Mayday_Occupation05Skill02"] = 75,
    ["Mayday_Occupation05Skill02_Info"] = 75,
    ["Mayday_Occupation05Skill02_Tips"] = 75,
    ["Mayday_Occupation05Skill02_Toast"] = 75,
    ["Mayday_InGame_Item_Wanted"] = 75,
    ["Mayday_InGame_Item_Wanted_Info"] = 75,
    ["Mayday_Occupation05_BtnRefresh"] = 75,
    ["Mayday_Occupation05_NewTarget"] = 75,
    ["Mayday_Occupation05_BtnKill"] = 75,
    ["Mayday_Occupation05_Toast01"] = 75,
    ["Mayday_Occupation05_Toast02"] = 75,
    ["Mayday_Occupation05_Toast03"] = 75,
    ["Mayday_Occupation05_Toast04"] = 75,
    ["Mayday_Occupation05_Toast05"] = 75,
    ["Mayday_Occupation05_Toast06"] = 75,
    ["Mayday_Occupation05_Toast01Spy"] = 75,
    ["Mayday_Occupation05_Toast02Spy"] = 75,
    ["Mayday_Occupation05_Toast03Spy"] = 75,
    ["Mayday_Occupation05_Toast04Spy"] = 75,
    ["Mayday_Occupation05_Toast05Spy"] = 75,
    ["Mayday_Occupation05_Toast06Spy"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff01"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff02"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff03"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff04"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff05"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff09"] = 75,
    ["Mayday_Occupation05_Unlimited_SkillBuff10"] = 75,
    ["Mayday_Occupation06"] = 75,
    ["Mayday_Occupation06Skill01"] = 75,
    ["Mayday_Occupation06Skill01_Info"] = 75,
    ["Mayday_Occupation06Skill01_Btn"] = 75,
    ["Mayday_InGame_Item_Invisible"] = 75,
    ["Mayday_InGame_Item_Invisible_Info"] = 75,
    ["Mayday_Occupation06_BtnKill"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff01"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff02"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff03"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff04"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff05"] = 75,
    ["Mayday_Occupation06_Unlimited_SkillBuff10"] = 75,
    ["Mayday_Occupation07"] = 75,
    ["Mayday_Occupation07Skill01"] = 75,
    ["Mayday_Occupation07Skill01_Progress"] = 75,
    ["Mayday_Occupation07Skill01_Info"] = 75,
    ["Mayday_Occupation07Skill01_Toast01"] = 75,
    ["Mayday_Occupation07Skill01_Toast02"] = 75,
    ["Mayday_Occupation07Skill02"] = 75,
    ["Mayday_Occupation07Skill02_Info"] = 75,
    ["Mayday_Occupation07Skill02_New"] = 75,
    ["Mayday_Occupation07Skill02_Add"] = 75,
    ["Mayday_Occupation07Skill02_Del"] = 75,
    ["Mayday_Occupation07Skill02_Done"] = 75,
    ["Mayday_Occupation07Skill02_Toast01"] = 75,
    ["Mayday_Occupation07Skill02_Toast02"] = 75,
    ["Mayday_Occupation07Skill02_Toast03"] = 75,
    ["Mayday_Occupation07_BtnKill"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff01"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff02"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff03"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff04"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff05"] = 75,
    ["Mayday_Occupation07_Unlimited_SkillBuff10"] = 75,
    ["Mayday_Occupation08"] = 75,
    ["Mayday_Occupation08Skill01"] = 75,
    ["Mayday_Occupation08Skill01_Info"] = 75,
    ["Mayday_Occupation08Skill01_InfoSpy"] = 75,
    ["Mayday_Occupation08Skill01_BtnOn"] = 75,
    ["Mayday_Occupation08Skill01_BtnOff"] = 75,
    ["Mayday_Occupation08Skill01_PlayerBag"] = 75,
    ["Mayday_Occupation08_BtnKill"] = 75,
    ["Mayday_Occupation08_BtnSpyskill"] = 75,
    ["Mayday_Occupation08Skill01_Toast01"] = 75,
    ["Mayday_Occupation08Skill01_Toast02"] = 75,
    ["Mayday_Occupation08Skill01_Toast03"] = 75,
    ["Mayday_Occupation08_Unlimited_SkillBuff01"] = 75,
    ["Mayday_Occupation08_Unlimited_SkillBuff02"] = 75,
    ["Mayday_Occupation08_Unlimited_SkillBuff03"] = 75,
    ["Mayday_Occupation08_Unlimited_SkillBuff04"] = 76,
    ["Mayday_Occupation08_Unlimited_SkillBuff05"] = 76,
    ["Mayday_Occupation08_Unlimited_SkillBuff10"] = 76,
    ["Mayday_Occupation_Task"] = 76,
    ["Mayday_Occupation_Task_PersonalRewards"] = 76,
    ["Mayday_Occupation_Task_PersonalRewardsInfo"] = 76,
    ["Mayday_Occupation_Task_TeamRewards"] = 76,
    ["Mayday_Occupation01_Task01"] = 76,
    ["Mayday_Occupation01_Task02"] = 76,
    ["Mayday_Occupation01_Task03"] = 76,
    ["Mayday_Occupation01_Task04"] = 76,
    ["Mayday_Occupation01_Task05"] = 76,
    ["Mayday_Occupation02_Task01"] = 76,
    ["Mayday_Occupation02_Task02"] = 76,
    ["Mayday_Occupation02_Task03"] = 76,
    ["Mayday_Occupation02_Task04"] = 76,
    ["Mayday_OccupationAll_Task01"] = 76,
    ["Mayday_OccupationAll_Task02"] = 76,
    ["Mayday_OccupationAll_Task03"] = 76,
    ["Mayday_OccupationAll_Task04"] = 76,
    ["Mayday_OccupationAll_Task05"] = 76,
    ["Mayday_OccupationAll_Task06"] = 76,
    ["Mayday_OccupationAll_Task07"] = 76,
    ["Mayday_OccupationAll_Task08"] = 76,
    ["Mayday_Occupation_Full"] = 76,
    ["Mayday_Occupation_WaitOthers"] = 76,
    ["Mayday_Occupation_Ready"] = 76,
    ["Mayday_HUD_TotalValue"] = 76,
    ["Mayday_InGame_Confirmed"] = 76,
    ["Mayday_HUD_Config"] = 76,
    ["Mayday_HUD_PersonalConfig"] = 76,
    ["Mayday_HUD_Common"] = 76,
    ["Mayday_HUD_Sensitivity"] = 76,
    ["Mayday_HUD_SensitivityInfo"] = 76,
    ["Mayday_HUD_Rush"] = 76,
    ["Mayday_HUD_Rush_Switch01"] = 76,
    ["Mayday_HUD_Rush_Switch02"] = 76,
    ["Mayday_HUD_RushSensitivity"] = 76,
    ["Mayday_HUD_RushSensitivityInfo"] = 76,
    ["Mayday_HUD_Vision"] = 76,
    ["Mayday_HUD_Vision_1P"] = 76,
    ["Mayday_HUD_Vision_3P"] = 76,
    ["Mayday_HUD_Btn_Soul"] = 76,
    ["Mayday_HUD_System"] = 76,
    ["Mayday_HUD_System_Btn"] = 76,
    ["Mayday_HUD_System_Btn_Success"] = 76,
    ["Mayday_HUD_System_BtnCD_Toast"] = 76,
    ["Mayday_HUD_Unlimited_Exit_Text01"] = 76,
    ["Mayday_HUD_Unlimited_Exit_Text02"] = 76,
    ["Mayday_HUD_Unlimited_Exit_Text03"] = 76,
    ["Mayday_HUD_Unlimited_Exit_SaveBtn01"] = 76,
    ["Mayday_HUD_Unlimited_Exit_SaveBtn02"] = 76,
    ["Mayday_HUD_Unlimited_Exit_RestartBtn"] = 76,
    ["Mayday_HUD_Unlimited_Exit_Title"] = 76,
    ["Mayday_HUD_Unlimited_Exit_SaveText01"] = 76,
    ["Mayday_HUD_Unlimited_Exit_SaveText02"] = 76,
    ["Mayday_InGame_Wardrobe"] = 76,
    ["Mayday_InGame_Wardrobe_BtnOpen"] = 76,
    ["Mayday_InGame_Wardrobe_BtnDress"] = 76,
    ["Mayday_InGame_Wardrobe_BtnRevert"] = 76,
    ["Mayday_InGame_Btn_Teleport"] = 76,
    ["Mayday_InGame_Evaluation"] = 76,
    ["Mayday_InGame_Evaluation_All_P001"] = 76,
    ["Mayday_InGame_Evaluation_All_P002"] = 76,
    ["Mayday_InGame_Evaluation_All_P003"] = 76,
    ["Mayday_InGame_Evaluation_All_P004"] = 76,
    ["Mayday_InGame_Evaluation_All_P005"] = 76,
    ["Mayday_InGame_Evaluation_All_P006"] = 76,
    ["Mayday_InGame_Evaluation_All_I001"] = 76,
    ["Mayday_InGame_Evaluation_All_I002"] = 76,
    ["Mayday_InGame_Evaluation_All_I003"] = 76,
    ["Mayday_InGame_Evaluation_All_I004"] = 76,
    ["Mayday_InGame_Evaluation_All_I005"] = 76,
    ["Mayday_InGame_Evaluation_All_I006"] = 76,
    ["Mayday_InGame_Evaluation_All_N001"] = 76,
    ["Mayday_InGame_Evaluation_All_N002"] = 76,
    ["Mayday_InGame_Evaluation_All_N003"] = 76,
    ["Mayday_InGame_Evaluation_All_N004"] = 76,
    ["Mayday_InGame_Evaluation_All_N005"] = 76,
    ["Mayday_InGame_Evaluation_All_N006"] = 76,
    ["Mayday_InGame_Evaluation_All_N007"] = 76,
    ["Mayday_InGame_Evaluation_All_N008"] = 76,
    ["Mayday_InGame_Evaluation_All_N009"] = 76,
    ["Mayday_InGame_Evaluation_All_N010"] = 76,
    ["Mayday_InGame_Evaluation_All_N011"] = 76,
    ["Mayday_InGame_Evaluation_All_N012"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P001"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P002"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P003"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P004"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P005"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P006"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P007"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P008"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P009"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P010"] = 76,
    ["Mayday_InGame_Evaluation_Spy_P011"] = 76,
    ["Mayday_InGame_Evaluation_Spy_I001"] = 76,
    ["Mayday_InGame_Evaluation_Spy_I002"] = 76,
    ["Mayday_InGame_Evaluation_Spy_I003"] = 76,
    ["Mayday_InGame_Evaluation_Spy_I004"] = 77,
    ["Mayday_InGame_Evaluation_Spy_I005"] = 77,
    ["Mayday_InGame_Evaluation_Spy_I006"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N001"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N002"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N003"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N004"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N005"] = 77,
    ["Mayday_InGame_Evaluation_Spy_N006"] = 77,
    ["Mayday_InGame_Body_ReasonBook"] = 77,
    ["Mayday_InGame_Body_ReasonList"] = 77,
    ["Mayday_InGame_Body_Reason01"] = 77,
    ["Mayday_InGame_Body_Reason01_Desc"] = 77,
    ["Mayday_InGame_Body_Reason02"] = 77,
    ["Mayday_InGame_Body_Reason02_Desc"] = 77,
    ["Mayday_InGame_Body_Reason03"] = 77,
    ["Mayday_InGame_Body_Reason03_Desc"] = 77,
    ["Mayday_InGame_Body_Reason04"] = 77,
    ["Mayday_InGame_Body_Reason04_Desc"] = 77,
    ["Mayday_InGame_Body_Reason05"] = 77,
    ["Mayday_InGame_Body_Reason05_Desc"] = 77,
    ["Mayday_InGame_Body_Reason06"] = 77,
    ["Mayday_InGame_Body_Reason06_Desc"] = 77,
    ["Mayday_InGame_Body_Reason07"] = 77,
    ["Mayday_InGame_Body_Reason07_Desc"] = 77,
    ["Mayday_InGame_Body_Reason08"] = 77,
    ["Mayday_InGame_Body_Reason08_Desc"] = 77,
    ["Mayday_InGame_Body_Reason09"] = 77,
    ["Mayday_InGame_Body_Reason09_Desc"] = 77,
    ["Mayday_InGame_Body_Reason10"] = 77,
    ["Mayday_InGame_Body_Reason10_Desc"] = 77,
    ["Mayday_InGame_Body_Action"] = 77,
    ["Mayday_InGame_Body_ActionOccup00"] = 77,
    ["Mayday_InGame_Body_ActionOccup01"] = 77,
    ["Mayday_InGame_Body_ActionOccup02"] = 77,
    ["Mayday_InGame_Body_ActionOccup03"] = 77,
    ["Mayday_InGame_Body_ActionOccup04"] = 77,
    ["Mayday_InGame_Body_ActionOccup05"] = 77,
    ["Mayday_InGame_Body_ActionOccup06"] = 77,
    ["Mayday_InGame_Body_ActionMonster01"] = 77,
    ["Mayday_InGame_Body_ActionMonster02"] = 77,
    ["Mayday_InGame_Body_ActionMonster03"] = 77,
    ["Mayday_InGame_Body_ActionMonster04"] = 77,
    ["Mayday_InGame_Body_ActionMonster05"] = 77,
    ["Mayday_InGame_Body_ActionMonster06"] = 77,
    ["Mayday_InGame_Body_ActionMonster07"] = 77,
    ["Mayday_InGame_Body_ActionMonster08"] = 77,
    ["Mayday_InGame_Body_ActionMonster09"] = 77,
    ["Mayday_InGame_Body_ActionMonster10"] = 77,
    ["Mayday_InGame_Body_ActionMonster11"] = 77,
    ["Mayday_InGame_Body_ActionMonster12"] = 77,
    ["Mayday_InGame_Body_ActionMonster13"] = 77,
    ["Mayday_InGame_Body_ActionTrap01"] = 77,
    ["Mayday_InGame_Body_ActionEnvir01"] = 77,
    ["Mayday_InGame_Body_ActionEnvir02"] = 77,
    ["Mayday_InGame_Body_ActionEnvir03"] = 77,
    ["Mayday_InGame_Body_ActionEnvir04"] = 77,
    ["Mayday_InGame_Body_ActionItem01"] = 77,
    ["Mayday_InGame_Body_ActionItem02"] = 77,
    ["Mayday_InGame_Body_ActionItem03"] = 77,
    ["Mayday_InGame_Body_ActionFall01"] = 77,
    ["Mayday_InGame_Book"] = 77,
    ["Mayday_InGame_Book_BodyInfo"] = 77,
    ["Mayday_InGame_Book_MyInfo"] = 77,
    ["Mayday_InGame_Book_Instruction"] = 77,
    ["Mayday_InGame_Book_New"] = 77,
    ["Mayday_InGame_Book_ThisTime"] = 77,
    ["Mayday_InGame_Book_OldInfo"] = 77,
    ["Mayday_InGame_Book_Finder"] = 77,
    ["Mayday_InGame_Book_Btn_ShowInfo"] = 77,
    ["Mayday_InGame_Book_Btn_OldInfo"] = 77,
    ["Mayday_InGame_Book_UnknownInfo"] = 77,
    ["Mayday_InGame_Book_PlayerShowInfo"] = 77,
    ["Mayday_InGame_Book_PlayerDead"] = 77,
    ["Mayday_InGame_Book_RecordInfo"] = 77,
    ["Mayday_InGame_BodyInfoReport_Tips"] = 77,
    ["Mayday_InGame_Ring"] = 77,
    ["Mayday_InGame_Ring_Btn"] = 77,
    ["Mayday_InGame_Ring_Tips"] = 77,
    ["Mayday_InGame_Ring_Tips2"] = 77,
    ["Mayday_InGame_Ring_Tips3"] = 77,
    ["Mayday_InGame_Ring_Toast"] = 77,
    ["Mayday_HUD_Champion_Title"] = 77,
    ["Mayday_HUD_Champion_Info"] = 77,
    ["Mayday_HUD_GameOver"] = 77,
    ["Mayday_InGame_Coffin"] = 77,
    ["Mayday_InGame_Coffin_Btn_Hide"] = 77,
    ["Mayday_InGame_Coffin_Btn_Leave"] = 77,
    ["Mayday_InGame_Paused"] = 77,
    ["Mayday_ModePedia_Title"] = 77,
    ["Mayday_ModePedia_Instruction"] = 77,
    ["Mayday_ModePedia_Bonus"] = 77,
    ["Mayday_ModePedia_FindNewGhost"] = 77,
    ["Mayday_ModePedia_GhostNumber"] = 77,
    ["Mayday_ModePedia_GhostName"] = 77,
    ["Mayday_ModePedia_RiskLevel"] = 77,
    ["Mayday_ModePedia_GetBonusTime"] = 77,
    ["Mayday_ModePedia_Location"] = 77,
    ["Mayday_ModePedia_GhostInfo"] = 77,
    ["Mayday_ModePedia_WillGet"] = 77,
    ["Mayday_ModeInstruction_Ghost1"] = 78,
    ["Mayday_ModeInstruction_Ghost1_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost1_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost1_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost2"] = 78,
    ["Mayday_ModeInstruction_Ghost2_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost2_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost2_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost3"] = 78,
    ["Mayday_ModeInstruction_Ghost3_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost3_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost3_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost4"] = 78,
    ["Mayday_ModeInstruction_Ghost4_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost4_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost4_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost5"] = 78,
    ["Mayday_ModeInstruction_Ghost5_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost5_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost5_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost6"] = 78,
    ["Mayday_ModeInstruction_Ghost6_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost6_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost6_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost7"] = 78,
    ["Mayday_ModeInstruction_Ghost7_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost7_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost7_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost8"] = 78,
    ["Mayday_ModeInstruction_Ghost8_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost8_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost8_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost9"] = 78,
    ["Mayday_ModeInstruction_Ghost9_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost9_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost9_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost10"] = 78,
    ["Mayday_ModeInstruction_Ghost10_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost10_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost10_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost11"] = 78,
    ["Mayday_ModeInstruction_Ghost11_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost11_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost11_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost12"] = 78,
    ["Mayday_ModeInstruction_Ghost12_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost12_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost12_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost13"] = 78,
    ["Mayday_ModeInstruction_Ghost13_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost13_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost13_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost14"] = 78,
    ["Mayday_ModeInstruction_Ghost14_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost14_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost14_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost15"] = 78,
    ["Mayday_ModeInstruction_Ghost15_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost15_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost15_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost16"] = 78,
    ["Mayday_ModeInstruction_Ghost16_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost16_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost16_Info3"] = 78,
    ["Mayday_ModeInstruction_Ghost17"] = 78,
    ["Mayday_ModeInstruction_Ghost17_Info"] = 78,
    ["Mayday_ModeInstruction_Ghost17_Info2"] = 78,
    ["Mayday_ModeInstruction_Ghost17_Info3"] = 78,
    ["Mayday_ModeInstruction_MainRule"] = 78,
    ["Mayday_ModeInstruction_MainRule_Text"] = 78,
    ["Mayday_ModeInstruction_PlayRule"] = 78,
    ["Mayday_ModeInstruction_StoreBuy"] = 78,
    ["Mayday_ModeInstruction_StoreBuy_Text"] = 78,
    ["Mayday_ModeInstruction_DriveShip"] = 78,
    ["Mayday_ModeInstruction_DriveShip_Text"] = 78,
    ["Mayday_ModeInstruction_GotItems"] = 78,
    ["Mayday_ModeInstruction_GotItems_Text"] = 78,
    ["Mayday_ModeInstruction_BackShip"] = 78,
    ["Mayday_ModeInstruction_BackShip_Text"] = 78,
    ["Mayday_ModeInstruction_ItemValue"] = 78,
    ["Mayday_ModeInstruction_ItemValue_Text"] = 78,
    ["Mayday_ModeInstruction_KingsPalace"] = 78,
    ["Mayday_ModeInstruction_KingsPalace_Text"] = 78,
    ["Mayday_ModeInstruction_HandIn"] = 78,
    ["Mayday_ModeInstruction_HandIn_Text"] = 78,
    ["Mayday_ModeInstruction_MoreRule"] = 78,
    ["Mayday_ModeInstruction_Level"] = 78,
    ["Mayday_ModeInstruction_Level_Text"] = 78,
    ["Mayday_ModeInstruction_Money"] = 78,
    ["Mayday_ModeInstruction_Money_Text"] = 78,
    ["Mayday_ModeInstruction_GotMoney"] = 78,
    ["Mayday_ModeInstruction_GotMoney_Text"] = 78,
    ["Mayday_ModeInstruction_PlayTime"] = 78,
    ["Mayday_ModeInstruction_PlayTime_Text"] = 78,
    ["Mayday_ModeInstruction_DeadPenalty"] = 78,
    ["Mayday_ModeInstruction_DeadPenalty_Text"] = 78,
    ["Mayday_ModeInstruction_Map"] = 78,
    ["Mayday_ModeInstruction_Room"] = 78,
    ["Mayday_ModeInstruction_Room_Text"] = 78,
    ["Mayday_ModeInstruction_RoomDoor"] = 78,
    ["Mayday_ModeInstruction_RoomDoor_Text"] = 79,
    ["Mayday_ModeInstruction_WildPlay"] = 79,
    ["Mayday_ModeInstruction_WildPlay_Text"] = 79,
    ["Mayday_ModeInstruction_WildDouble"] = 79,
    ["Mayday_ModeInstruction_WildDouble_Text"] = 79,
    ["Mayday_ModeInstruction_WildJump"] = 79,
    ["Mayday_ModeInstruction_WildJump_Text"] = 79,
    ["Mayday_ModeInstruction_GhostInfo"] = 79,
    ["Mayday_ModeInstruction_Career"] = 79,
    ["Mayday_ModeInstruction_Ship"] = 79,
    ["Mayday_ModeInstruction_ShipTakeOff"] = 79,
    ["Mayday_ModeInstruction_ShipTakeOff_Text"] = 79,
    ["Mayday_ModeInstruction_ShipLand"] = 79,
    ["Mayday_ModeInstruction_ShipLand_Text"] = 79,
    ["Mayday_ModeInstruction_Store"] = 79,
    ["Mayday_ModeInstruction_Store_Text"] = 79,
    ["Mayday_ModeInstruction_TeleportBack"] = 79,
    ["Mayday_ModeInstruction_TeleportBack_Text"] = 79,
    ["Mayday_ModeInstruction_TeleportOut"] = 79,
    ["Mayday_ModeInstruction_TeleportOut_Text"] = 79,
    ["Mayday_ModeInstruction_DoorControl"] = 79,
    ["Mayday_ModeInstruction_DoorControl_Text"] = 79,
    ["Mayday_ModeInstruction_Deck"] = 79,
    ["Mayday_ModeInstruction_Deck_Text"] = 79,
    ["Mayday_ModeInstruction_Wardrobe"] = 79,
    ["Mayday_ModeInstruction_Wardrobe_Text"] = 79,
    ["Mayday_ModeInstruction_MainRule_Text2"] = 79,
    ["Mayday_ModeInstruction_StarRule"] = 79,
    ["Mayday_ModeInstruction_WinCondition"] = 79,
    ["Mayday_ModeInstruction_WinCondition_Text"] = 79,
    ["Mayday_ModeInstruction_GotItems_Text2"] = 79,
    ["Mayday_ModeInstruction_HandIn_Text2"] = 79,
    ["Mayday_ModeInstruction_SpyDeath"] = 79,
    ["Mayday_ModeInstruction_SpyDeath_Text"] = 79,
    ["Mayday_ModeInstruction_SpyRule"] = 79,
    ["Mayday_ModeInstruction_WinCondition_Text2"] = 79,
    ["Mayday_ModeInstruction_SpyDisturb"] = 79,
    ["Mayday_ModeInstruction_SpyDisturb_Text"] = 79,
    ["Mayday_ModeInstruction_SpyLieLow"] = 79,
    ["Mayday_ModeInstruction_SpyLieLow_Text"] = 79,
    ["Mayday_ModeInstruction_Clue"] = 79,
    ["Mayday_ModeInstruction_AboutClue"] = 79,
    ["Mayday_ModeInstruction_AboutClue_Text"] = 79,
    ["Mayday_ModeInstruction_GotClue"] = 79,
    ["Mayday_ModeInstruction_GotClue_Text"] = 79,
    ["Mayday_ModeInstruction_ShowClue"] = 79,
    ["Mayday_ModeInstruction_ShowClue_Text"] = 79,
    ["Mayday_ModeInstruction_Discussion"] = 79,
    ["Mayday_ModeInstruction_FieldReport"] = 79,
    ["Mayday_ModeInstruction_FieldReport_Text"] = 79,
    ["Mayday_ModeInstruction_RingReport"] = 79,
    ["Mayday_ModeInstruction_RingReport_Text"] = 79,
    ["Mayday_ModeInstruction_ShowClue2"] = 79,
    ["Mayday_ModeInstruction_ShowClue2_Text"] = 79,
    ["Mayday_ModeInstruction_VoteRule"] = 79,
    ["Mayday_ModeInstruction_VoteRule_Text"] = 79,
    ["Mayday_ModeInstruction_ItemBox"] = 79,
    ["Mayday_ModeInstruction_ItemBox_Text"] = 79,
    ["Mayday_ModeInstruction_HandheldMap"] = 79,
    ["Mayday_ModeInstruction_HandheldMap_Text"] = 79,
    ["Mayday_ModeInstruction_UnlimitedLevel"] = 79,
    ["Mayday_ModeInstruction_UnlimitedLevel_Text"] = 79,
    ["Mayday_ModeInstruction_FirstMoney"] = 79,
    ["Mayday_ModeInstruction_FirstMoney_Text"] = 79,
    ["Mayday_ModeInstruction_LevelSelect"] = 79,
    ["Mayday_ModeInstruction_LevelSelect_Text"] = 79,
    ["Mayday_ModeInstruction_Keyword"] = 79,
    ["Mayday_ModeInstruction_Keyword_Text"] = 79,
    ["Mayday_ModeInstruction_OccupationLvUp"] = 79,
    ["Mayday_ModeInstruction_OccupationLvUp_Text"] = 79,
    ["Mayday_ModeGuide_Title"] = 79,
    ["Mayday_ModeGuide_GameStart"] = 79,
    ["Mayday_ModeGuide_GameStart_Text"] = 79,
    ["Mayday_ModeGuide_MissionInfo"] = 79,
    ["Mayday_ModeGuide_MissionInfo_Text"] = 79,
    ["Mayday_ModeGuide_Depart"] = 79,
    ["Mayday_ModeGuide_Depart_Text"] = 79,
    ["Mayday_ModeGuide_FindRoom"] = 79,
    ["Mayday_ModeGuide_FindRoom_Text"] = 79,
    ["Mayday_ModeGuide_Rainforest"] = 79,
    ["Mayday_ModeGuide_Rainforest_Text"] = 79,
    ["Mayday_ModeGuide_GotItems"] = 79,
    ["Mayday_ModeGuide_GotItems_Text"] = 79,
    ["Mayday_ModeGuide_Light"] = 79,
    ["Mayday_ModeGuide_Light_Text"] = 79,
    ["Mayday_ModeGuide_Battle"] = 79,
    ["Mayday_ModeGuide_Battle_Text"] = 79,
    ["Mayday_ModeGuide_HandIn"] = 79,
    ["Mayday_ModeGuide_HandIn_Text"] = 79,
    ["Mayday_ModeGuide_HandIn_Text2"] = 79,
    ["Mayday_ModeGuide_StarWin"] = 79,
    ["Mayday_ModeGuide_StarWin_Text"] = 79,
    ["Mayday_ModeGuide_FindSpy"] = 79,
    ["Mayday_ModeGuide_FindSpy_Text"] = 79,
    ["Mayday_ModeGuide_SpyWin"] = 79,
    ["Mayday_ModeGuide_SpyWin_Text"] = 79,
    ["Mayday_ModeGuide_SpyLieLow"] = 79,
    ["Mayday_ModeGuide_SpyLieLow_Text"] = 79,
    ["Mayday_ModeGuide_GotItems_Text2"] = 79,
    ["Mayday_ModeGuide_AttentionSpy"] = 79,
    ["Mayday_ModeGuide_AttentionSpy_Text"] = 80,
    ["Mayday_ModeGuide_SpyMission"] = 80,
    ["Mayday_ModeGuide_SpyMission_Text"] = 80,
    ["Mayday_ModeGuide_SpySkill"] = 80,
    ["Mayday_ModeGuide_SpySkill_Text"] = 80,
    ["Mayday_ModeMission1_Text"] = 80,
    ["Mayday_ModeMission2_Text"] = 80,
    ["Mayday_ModeMission3_Text"] = 80,
    ["Mayday_ModeMission4_Text"] = 80,
    ["Mayday_ModeMission5_Text"] = 80,
    ["Mayday_ModeMission6_Text"] = 80,
    ["Mayday_ModeMission7_Text"] = 80,
    ["Mayday_ModeMission8_Text"] = 80,
    ["Mayday_ModeMission9_Text"] = 80,
    ["Mayday_ModeMission10_Text"] = 80,
    ["Mayday_ModeMission11_Text"] = 80,
    ["Mayday_ModeMission12_Text"] = 80,
    ["Mayday_ModeMission13_Text"] = 80,
    ["Mayday_ModeMission14_Text"] = 80,
    ["Mayday_ModeMission15_Text"] = 80,
    ["Mayday_Unlimited"] = 80,
    ["Mayday_Unlimited_Go_Tips1"] = 80,
    ["Mayday_Unlimited_Go_Tips2"] = 80,
    ["Mayday_Unlimited_Go_Tips3"] = 80,
    ["Mayday_Unlimited_Go_LocationChange"] = 80,
    ["Mayday_Unlimited_JobReselect"] = 80,
    ["Mayday_Unlimited_Rank"] = 80,
    ["Mayday_Unlimited_TeamMoney"] = 80,
    ["Mayday_Unlimited_SkillPointUsed"] = 80,
    ["Mayday_Unlimited_Position"] = 80,
    ["Mayday_Unlimited_SkillPointRemain"] = 80,
    ["Mayday_Unlimited_JobLevel"] = 80,
    ["Mayday_Unlimited_Map01"] = 80,
    ["Mayday_Unlimited_Map02"] = 80,
    ["Mayday_Unlimited_Map03"] = 80,
    ["Mayday_Unlimited_DangerRank"] = 80,
    ["Mayday_Unlimited_DailyValue"] = 80,
    ["Mayday_Unlimited_TransPay"] = 80,
    ["Mayday_Unlimited_Short"] = 80,
    ["Mayday_Unlimited_Level1"] = 80,
    ["Mayday_Unlimited_Level2"] = 80,
    ["Mayday_Unlimited_Level3"] = 80,
    ["Mayday_Unlimited_TeamRecord"] = 80,
    ["Mayday_Unlimited_TeamRecord_Level"] = 80,
    ["Mayday_Unlimited_NewGoal"] = 80,
    ["Mayday_Unlimited_NewGoal2"] = 80,
    ["Mayday_Unlimited_LimitTime"] = 80,
    ["Mayday_Unlimited_NewRecord"] = 80,
    ["Mayday_Unlimited_RewardTeamMoney1"] = 80,
    ["Mayday_Unlimited_RewardTeamMoney2"] = 80,
    ["Mayday_Unlimited_UseTime"] = 80,
    ["Mayday_Unlimited_SkillBook"] = 80,
    ["Mayday_Unlimited_SkillBook_Info"] = 80,
    ["Mayday_Unlimited_SkillBook_Tips"] = 80,
    ["Mayday_Unlimited_SkillPointHave"] = 80,
    ["Mayday_Unlimited_SkillPointNeed"] = 80,
    ["Mayday_Unlimited_SkillLevelUp_Btn"] = 80,
    ["Mayday_Unlimited_SkillLevelUp_Toast01"] = 80,
    ["Mayday_Unlimited_SkillLevelUp_Toast02"] = 80,
    ["Mayday_Unlimited_SkillLevelUp_Toast03"] = 80,
    ["Mayday_Unlimited_SkillLevel_Max"] = 80,
    ["Mayday_Unlimited_SkillLevel_Max2"] = 80,
    ["Mayday_Unlimited_SkillLevel_Condition"] = 80,
    ["Mayday_Unlimited_LevelSelect"] = 80,
    ["Mayday_Unlimited_LocationLevel"] = 80,
    ["Mayday_Unlimited_TeleportConfirm01"] = 80,
    ["Mayday_Unlimited_TeleportConfirm02"] = 80,
    ["Mayday_Unlimited_SelectLocationLevel"] = 80,
    ["Mayday_Unlimited_Occupation_Title"] = 80,
    ["Mayday_Unlimited_Goal"] = 80,
    ["Mayday_Unlimited_Level"] = 80,
    ["Mayday_Unlimited_Recommend"] = 80,
    ["Mayday_Unlimited_Keyword"] = 80,
    ["Mayday_Unlimited_Keyword_ComingSoon"] = 80,
    ["Mayday_Unlimited_Keyword1_Title"] = 80,
    ["Mayday_Unlimited_Keyword1_Text"] = 80,
    ["Mayday_Unlimited_Keyword2_Title"] = 80,
    ["Mayday_Unlimited_Keyword2_Text"] = 80,
    ["Mayday_Unlimited_Keyword3_Title"] = 80,
    ["Mayday_Unlimited_Keyword3_Text"] = 80,
    ["Mayday_Unlimited_Keyword4_Title"] = 80,
    ["Mayday_Unlimited_Keyword4_Text"] = 80,
    ["Mayday_Unlimited_Keyword5_Title"] = 80,
    ["Mayday_Unlimited_Keyword5_Text"] = 80,
    ["Mayday_Unlimited_Keyword6_Title"] = 80,
    ["Mayday_Unlimited_Keyword6_Text"] = 80,
    ["Mayday_Unlimited_Keyword7_Title"] = 80,
    ["Mayday_Unlimited_Keyword7_Text"] = 80,
    ["Mayday_Unlimited_Keyword8_Title"] = 80,
    ["Mayday_Unlimited_Keyword8_Text"] = 80,
    ["Mayday_Unlimited_Keyword9_Title"] = 80,
    ["Mayday_Unlimited_Keyword9_Text"] = 80,
    ["Mayday_Unlimited_Keyword10_Title"] = 80,
    ["Mayday_Unlimited_Keyword10_Text"] = 80,
    ["Mayday_Unlimited_Keyword11_Title"] = 80,
    ["Mayday_Unlimited_Keyword11_Text"] = 80,
    ["Mayday_Unlimited_Keyword12_Title"] = 80,
    ["Mayday_Unlimited_Keyword12_Text"] = 80,
    ["Mayday_Unlimited_Keyword13_Title"] = 80,
    ["Mayday_Unlimited_Keyword13_Text"] = 80,
    ["Mayday_Unlimited_Keyword14_Title"] = 81,
    ["Mayday_Unlimited_Keyword14_Text"] = 81,
    ["Mayday_Unlimited_Keyword15_Title"] = 81,
    ["Mayday_Unlimited_Keyword15_Text"] = 81,
    ["Mayday_Unlimited_Keyword16_Title"] = 81,
    ["Mayday_Unlimited_Keyword16_Text"] = 81,
    ["Mayday_Unlimited_Keyword17_Title"] = 81,
    ["Mayday_Unlimited_Keyword17_Text"] = 81,
    ["Mayday_Unlimited_Keyword18_Title"] = 81,
    ["Mayday_Unlimited_Keyword18_Text"] = 81,
    ["Mayday_Unlimited_Keyword19_Title"] = 81,
    ["Mayday_Unlimited_Keyword19_Text"] = 81,
    ["Mayday_Unlimited_Keyword20_Title"] = 81,
    ["Mayday_Unlimited_Keyword20_Text"] = 81,
    ["Mayday_Unlimited_Keyword21_Title"] = 81,
    ["Mayday_Unlimited_Keyword21_Text"] = 81,
    ["Mayday_Unlimited_Keyword22_Title"] = 81,
    ["Mayday_Unlimited_Keyword22_Text"] = 81,
    ["Mayday_Unlimited_Keyword23_Title"] = 81,
    ["Mayday_Unlimited_Keyword23_Text"] = 81,
    ["Mayday_Unlimited_Keyword24_Title"] = 81,
    ["Mayday_Unlimited_Keyword24_Text"] = 81,
    ["Mayday_Unlimited_Keyword25_Title"] = 81,
    ["Mayday_Unlimited_Keyword25_Text"] = 81,
    ["Mayday_Unlimited_Keyword26_Title"] = 81,
    ["Mayday_Unlimited_Keyword26_Text"] = 81,
    ["Mayday_Unlimited_Keyword27_Title"] = 81,
    ["Mayday_Unlimited_Keyword27_Text"] = 81,
    ["Mayday_Unlimited_Keyword28_Title"] = 81,
    ["Mayday_Unlimited_Keyword28_Text"] = 81,
    ["Mayday_Unlimited_Keyword29_Title"] = 81,
    ["Mayday_Unlimited_Keyword29_Text"] = 81,
    ["Mayday_Unlimited_Keyword30_Title"] = 81,
    ["Mayday_Unlimited_Keyword30_Text"] = 81,
    ["Mayday_Unlimited_Keyword31_Title"] = 81,
    ["Mayday_Unlimited_Keyword31_Text"] = 81,
    ["Mayday_Unlimited_Keyword32_Title"] = 81,
    ["Mayday_Unlimited_Keyword32_Text"] = 81,
    ["Mayday_InGame_Speech"] = 81,
    ["Mayday_InGame_CannotUseEmoji_Toast"] = 81,
    ["Text_First"] = 82,
    ["DS_NoMatch"] = 82,
    ["Common_Confirm"] = 82,
    ["Common_Confirm2"] = 82,
    ["Common_Confirm3"] = 82,
    ["Common_Cancel"] = 82,
    ["Common_Prompt"] = 82,
    ["Common_Error"] = 82,
    ["Common_Success"] = 82,
    ["Common_Fail"] = 82,
    ["Common_Day"] = 82,
    ["Common_ShortHour"] = 82,
    ["Common_Hour"] = 82,
    ["Common_Min"] = 82,
    ["Common_Sec"] = 82,
    ["Common_QualityName_1"] = 82,
    ["Common_QualityName_2"] = 82,
    ["Common_QualityName_3"] = 82,
    ["Common_QualityName_4"] = 82,
    ["Common_QualityName_5"] = 82,
    ["Common_Accept"] = 82,
    ["Common_Reject"] = 82,
    ["Common_Forever"] = 82,
    ["Common_Yes"] = 82,
    ["Common_No"] = 82,
    ["Common_Save"] = 82,
    ["Common_NoSave"] = 82,
    ["Common_Return"] = 82,
    ["Common_Giveup"] = 82,
    ["Common_Close"] = 82,
    ["Common_Reconnect"] = 82,
    ["Common_PleaseInputNum"] = 82,
    ["Common_LimitCount"] = 82,
    ["Common_ReplaceRewardText"] = 82,
    ["Common_GotNewSkin"] = 82,
    ["Common_GotNewOrnament"] = 82,
    ["Common_ItemUse"] = 82,
    ["Common_ItemDel"] = 82,
    ["Common_ItemSell"] = 82,
    ["Common_Item"] = 82,
    ["Common_Expired"] = 82,
    ["Common_Discount"] = 82,
    ["Login_CreateRoleFail"] = 82,
    ["Login_NeedSelectAccount"] = 82,
    ["Lobby_EnterLobby"] = 82,
    ["Lobby_ReTryTimes"] = 82,
    ["TimeUtils_H_1"] = 82,
    ["TimeUtils_D_1"] = 82,
    ["TimeUtils_DH_1"] = 82,
    ["TimeUtils_TimeLength2_1"] = 82,
    ["TimeUtils_M"] = 82,
    ["TimeUtils_M_1"] = 82,
    ["TimeUtils_M_Const_1"] = 82,
    ["TimeUtils_MD"] = 82,
    ["TimeUtils_YMD_1"] = 82,
    ["TimeUtils_MD_HM_1"] = 82,
    ["TimeUtils_D_2"] = 82,
    ["TimeUtils_Month_2"] = 82,
    ["TimeUtils_Y_2"] = 82,
    ["TimeUtils_H_2"] = 82,
    ["TimeUtils_Min_2"] = 82,
    ["TimeUtils_S_2"] = 82,
    ["TimeUtils_Today"] = 82,
    ["TimeUtils_DH_3"] = 82,
    ["TimeUtils_DM_3"] = 82,
    ["TimeUtils_MS_3"] = 82,
    ["TimeUtils_S_3"] = 82,
    ["TimeUtils_DHMS_3"] = 82,
    ["TimeUtils_HMS_3"] = 82,
    ["TimeUtils_HM_3"] = 82,
    ["TimeUtils_DHM_3"] = 82,
    ["TimeUtils_D_TimeLangth3_1"] = 82,
    ["TimeUtils_D_TimeLangth2_1"] = 82,
    ["TimeUtils_H_4"] = 82,
    ["TimeUtils_H_Const_1"] = 82,
    ["TimeUtils_M_4"] = 82,
    ["TimeUtils_D"] = 82,
    ["TimeUtils_Monday"] = 82,
    ["TimeUtils_Tuesday"] = 82,
    ["TimeUtils_Wednesday"] = 82,
    ["TimeUtils_Thursday"] = 82,
    ["TimeUtils_Friday"] = 82,
    ["TimeUtils_Saturday"] = 82,
    ["TimeUtils_Sunday"] = 82,
    ["MoveTextConfig_Tips"] = 82,
    ["UI_Login_VersionDesc"] = 82,
    ["Common_LoginTimeout"] = 82,
    ["Common_LoginQRCodeWX"] = 82,
    ["Common_LoginQRCodeQQ"] = 82,
    ["UI_Login_CheckRuleTip"] = 82,
    ["UI_Login_LogoutDesc"] = 82,
    ["Common_LogoutFail"] = 82,
    ["UI_Login_LoginStatusErrorBackToLogin"] = 82,
    ["Common_LoginSuccess"] = 82,
    ["Common_YouNotInvited"] = 82,
    ["Common_LoginFail"] = 82,
    ["Common_CancelWXLogin"] = 82,
    ["Common_CancelQQLogin"] = 82,
    ["Common_LoginErrorTryLoginAgain"] = 82,
    ["Common_GuestLoginDesc"] = 82,
    ["UI_Login_ServerIsNull"] = 83,
    ["Guide_CreateRole_ShowText1"] = 83,
    ["Guide_CreateRole_ShowText2"] = 83,
    ["Guide_CreateRoleFinish_ShowText1"] = 83,
    ["Guide_CreateRoleFinish_ShowText2"] = 83,
    ["Guide_CreateRoleFinish_ShowText3"] = 83,
    ["Guide_CreateRole_Fail"] = 83,
    ["Guide_CreateRole_Fail_891"] = 83,
    ["Guide_CreateRole_NetFail"] = 83,
    ["Team_CurrentNoWifi"] = 83,
    ["Team_Ds_LoadFail"] = 83,
    ["Team_WaitConfirm"] = 83,
    ["Team_CanNotStartMatch"] = 83,
    ["Net_ServerConnectFail"] = 83,
    ["Net_ReTryConnect"] = 83,
    ["Net_ReturnLogin"] = 83,
    ["Net_ServerConnectFailDS"] = 83,
    ["Net_WaitConnectDS"] = 83,
    ["Net_ReturnLoginDS"] = 83,
    ["Net_ReturnBattleFail"] = 83,
    ["Net_IsNeedReConnect"] = 83,
    ["Net_PlazaReconnectTimesOut"] = 83,
    ["Net_GiveUpBattleFail"] = 83,
    ["Net_DSNotMatchClient"] = 83,
    ["Guide_GuideConfigError"] = 83,
    ["Login_LoginFail"] = 83,
    ["Net_NetError"] = 83,
    ["Net_ServerError"] = 83,
    ["Net_KickPlayer_Banned"] = 83,
    ["Login_AuthFail"] = 83,
    ["Login_CheckName"] = 83,
    ["Login_SetName"] = 83,
    ["Login_SetGender"] = 83,
    ["Login_NetException"] = 83,
    ["Login_NoNameCanUse"] = 83,
    ["Net_DS_Error"] = 83,
    ["Login_TokenExpired"] = 83,
    ["Net_Proto_Error"] = 83,
    ["Net_Server_Error"] = 83,
    ["Login_PlayerInfoNoInit"] = 83,
    ["Task_Unopened"] = 83,
    ["Common_Null"] = 83,
    ["UGC_String_E"] = 83,
    ["UGC_String_W"] = 83,
    ["UGC_Map_IsCompleteAll"] = 83,
    ["UGC_Map_NoTimes"] = 83,
    ["UGC_Map_isMatching"] = 83,
    ["UGC_Map_NoMapData"] = 83,
    ["UGC_Map_EnterMapFail"] = 83,
    ["UGC_Map_ReqResultFail"] = 83,
    ["UGC_Map_MapDelete"] = 83,
    ["UGC_Map_NoHpConfig"] = 83,
    ["UGC_Map_GetMapDataFail"] = 83,
    ["UGC_Map_EnterMapFail_WithParam"] = 83,
    ["UGC_Map_ChangeMapFail"] = 83,
    ["Device_Not_Support"] = 83,
    ["Pak_InitPufferModule"] = 83,
    ["Pak_PauseAllDownloads"] = 83,
    ["Pak_NoDownloadingCurrently"] = 83,
    ["Pak_WaitForMapResDownload"] = 83,
    ["Pak_WiFiDownloadALL"] = 83,
    ["Pak_MobileNetDownloadALL"] = 83,
    ["Pak_FinishDownloadToGetReward"] = 83,
    ["Pak_FinishDownloadAndLoginToGetReward"] = 83,
    ["Pak_WiFiDownloadHomeRes"] = 83,
    ["Pak_MobileNetDownloadHomeRes"] = 83,
    ["Pak_WiFiDownloadUGCMapRes"] = 83,
    ["Pak_MobileNetDownloadUGCMapRes"] = 83,
    ["Pak_DownloadConnectorMapRes"] = 83,
    ["Pak_PufferDownloadError"] = 83,
    ["Pak_MountPakFail"] = 83,
    ["Pak_DownloadAfterLogin"] = 83,
    ["Pak_InitPufferAfterReconnect"] = 83,
    ["Pak_ConfirmDeleteFiles"] = 83,
    ["Pak_CanntDeleteReferedPak"] = 83,
    ["Pak_WiFiDownloadExpansionPacks"] = 83,
    ["Pak_MobileNetDownloadExpansionPacks"] = 83,
    ["Pak_MoreOnThatLater"] = 83,
    ["Pak_GoNow"] = 83,
    ["Pak_Tip"] = 83,
    ["Pak_UpdateTips"] = 83,
    ["CutGitft_Discount"] = 83,
    ["CutGitft_MoneyYuan"] = 83,
    ["CutGitft_TimeOver"] = 83,
    ["CutGitft_BuyCountOver"] = 83,
    ["CutGitft_BuyConfirm"] = 83,
    ["Notice_NoticeTitle"] = 83,
    ["Common_ClickFast"] = 83,
    ["SUIT_STORY_APPEND"] = 84,
    ["SUIT_STORY_1"] = 84,
    ["Bag_SuitTips"] = 85,
    ["Bag_SeasonTips"] = 85,
    ["SettingTip_SpeedSensitivity"] = 85,
    ["SettingTip_RockerSensitivity"] = 85,
    ["SettingTip_AirInertia"] = 85,
    ["SettingTip_VehicleControlType"] = 85,
    ["CustomRoom_CannotJoinRoomInTeam"] = 85,
    ["TaskStar_Stage1"] = 85,
    ["TaskStar_Stage2"] = 85,
    ["TaskStar_Stage3"] = 85,
    ["TaskStar_Stage4"] = 85,
    ["TaskStar_NoFinishCurrStage"] = 85,
    ["TaskStar_NextStageNoOpen"] = 85,
    ["TaskStar_TimeDesc"] = 85,
    ["UI_RecruitItem_Level"] = 85,
    ["Friend_BanNotice"] = 85,
    ["PlayerInfo_DefaultHead"] = 85,
    ["PlayerInfo_DefaultHeadBG"] = 85,
    ["PlayerInfo_DefaultNicknameBG"] = 85,
    ["PlayerInfo_DefaultTitle"] = 85,
    ["Task_WeekTask"] = 85,
    ["Task_CommonRewardDesc"] = 85,
    ["PermissionHint_Voice"] = 85,
    ["PermissionHint_Location"] = 85,
    ["PermissionHint_Storage"] = 85,
    ["Common_ReceiveReward"] = 85,
    ["ModelSelect_PlayModelReward"] = 85,
    ["ModelSelect_PlayModelRewardDesc"] = 85,
    ["ModelSelect_SeasonRewardOneParam"] = 85,
    ["Common_Go"] = 85,
    ["Season_CurrSeasonIsClose"] = 85,
    ["Version_LowestVersion"] = 85,
    ["Version_HighestVersion"] = 85,
    ["Version_ExcludeVersions"] = 85,
    ["LevelFinal_RecommendPlayers"] = 85,
    ["LevelFinal_TeamPlayers"] = 85,
    ["UI_Unable_Go"] = 85,
    ["Team_InUGCTeam"] = 85,
    ["Common_ActivityIsClose"] = 85,
    ["QQQuickTeam_Fail"] = 85,
    ["Task_ActivityResourceUpdate_TryAgainLater"] = 85,
    ["Friend_IsBanState"] = 85,
    ["Team_JoinFromShare_InTeam"] = 85,
    ["Team_JoinFromShare_InRoom"] = 85,
    ["Team_JoinFromShare_InUGCEditor"] = 85,
    ["Team_JoinFromShare_InHome"] = 85,
    ["Common_ItemTagDefaultName"] = 85,
    ["Common_Empower_Tips"] = 85,
    ["Common_Confirm_Empower"] = 85,
    ["Common_Cancel_Empower"] = 85,
    ["LevelFinal_RecommendPlayersRea"] = 85,
    ["LevelFinal_TeamPlayersRea"] = 85,
    ["TaskParty_Hint"] = 85,
    ["CDK_ReceiveSuccessfully"] = 85,
    ["Team_MatchServicesBusy"] = 85,
    ["NewChat_SendMessageNeedLevel"] = 85,
    ["Team_CanNotAcceptStartMatch"] = 85,
    ["NewChat_VoiceReportSucceed"] = 85,
    ["NewChat_VoiceReportNoMessage"] = 85,
    ["NewChat_VoiceReportCD"] = 85,
    ["NewChat_VoiceReportFailed"] = 85,
    ["NewChat_VoiceReportNotOpen"] = 85,
    ["Team_TeamAutoMatch"] = 85,
    ["PlayerInfo_DefaultChatBubble"] = 85,
    ["Player_ChatBubble"] = 85,
    ["UI_Recharge_OpeningRebate_Title"] = 85,
    ["Login_TokenExpired_PleaseLoginAgain"] = 85,
    ["UI_Recharge_OpeningRebate_ActivityTipFormat"] = 85,
    ["Activity_Convened_RecallOldFriend"] = 85,
    ["Activity_Convened_InviteNewPlayer"] = 85,
    ["Leaderboard_Tips_Title_StarWorld"] = 85,
    ["Leaderboard_Tips_Desc_StarWorld"] = 85,
    ["UI_Common_PopRewardView_GoToDraw"] = 85,
    ["UI_Common_PopRewardView_GoToSend"] = 85,
    ["UI_Common_PopRewardView_GoToThank"] = 85,
    ["SpringRedPacket_Got"] = 85,
    ["SpringRedPacket_Sent"] = 85,
    ["friend_RecentPlay"] = 85,
    ["friend_Union"] = 85,
    ["QRCode_Room"] = 85,
    ["QRCode_Club"] = 85,
    ["QRCode_Friend"] = 85,
    ["QRCode_Team"] = 85,
    ["UI_RankCompleteLevel"] = 85,
    ["NewChat_TeamInLevel"] = 85,
    ["Activity_Levelup"] = 85,
    ["Club_ChatTab"] = 85,
    ["Club_EnterClub"] = 85,
    ["Club_ExitClub"] = 85,
    ["Leaderboard_Tips_Title_StarCreate"] = 85,
    ["Leaderboard_Tips_Desc_StarCreate"] = 85,
    ["Activity_Timer"] = 85,
    ["LuckyBallDraw"] = 85,
    ["LuckyBallTitle"] = 85,
    ["LuckyBallBlessTitle"] = 85,
    ["LuckyBallGetCoin"] = 85,
    ["LuckyBallBlessNum"] = 85,
    ["RewardNormal"] = 85,
    ["LuckyBallGetAll"] = 85,
    ["LuckyBall_CoinNotEnough"] = 85,
    ["LuckBall_RewardToast"] = 86,
    ["Spectator_Normal"] = 86,
    ["Spectator_Side"] = 86,
    ["Spectator_Top"] = 86,
    ["Spectator_Front"] = 86,
    ["Spectator_Free"] = 86,
    ["Activity_ItemNum_Small"] = 86,
    ["Activity_ItemNum_Middle"] = 86,
    ["Activity_ItemNum_Higher"] = 86,
    ["UI_Recharge_OpeningRebateMakeUpTip"] = 86,
    ["Activity_InviteSuccessCount"] = 86,
    ["UI_RankTag_QualityRule"] = 86,
    ["UI_RankTag_QualityDesc"] = 86,
    ["UI_RankTag_FashionRule"] = 86,
    ["UI_RankTag_FashionDesc"] = 86,
    ["UI_RankTag_LevelMpaRule"] = 86,
    ["UI_RankTag_LevelMpaDesc"] = 86,
    ["UI_YM_HighRecordNum"] = 86,
    ["UI_YM_HighRecordName"] = 86,
    ["TaskStar_PreTaskUnLock"] = 86,
    ["LuckyBall_UnKnowBless"] = 86,
    ["UI_WolfKillNotMatch"] = 86,
    ["Mail_UGC"] = 86,
    ["BoxDelivery_Push_Title"] = 86,
    ["BoxDelivery_Push_Content"] = 86,
    ["Bag_SeasonFashionTips"] = 86,
    ["Bag_HistoryAllFashionTips"] = 86,
    ["UI_VoiceSetting_MagicVoice"] = 86,
    ["MicroMagicVoiceCell_Normal"] = 86,
    ["Text_Stealth_Start_Notice"] = 86,
    ["Text_Stealth_Close_Notice"] = 86,
    ["Text_Stealth_Week_Times"] = 86,
    ["BP_WeekTaskTargetSecond"] = 86,
    ["Common_ScreenShotTip"] = 86,
    ["NewChat_OtherSharePosition_Normal"] = 86,
    ["NewChat_OtherSharePosition_Home"] = 86,
    ["NewChat_SelfSharePosition_Home"] = 86,
    ["UI_PublicWelfare_LockStatus"] = 86,
    ["UI_PublicWelfare_TenThousand"] = 86,
    ["UI_PublicWelfare_XHH_WX"] = 86,
    ["UI_PublicWelfare_Medal_WX"] = 86,
    ["UI_PublicWelfare_XHH_QQ"] = 86,
    ["UI_PublicWelfare_Medal_QQ"] = 86,
    ["UI_PublicWelfare_Lock_Headline"] = 86,
    ["UI_PublicWelfare_Lock_Description"] = 86,
    ["AssistBless_AssistedText"] = 86,
    ["Assisted_Failure"] = 86,
    ["AssistBless_NoFriendSlotsAvailable"] = 86,
    ["AssistBless_TipContent"] = 86,
    ["AssistBless_TipBottomContent"] = 86,
    ["AssistBless_DateFormat"] = 86,
    ["AssistBless_RewardContent"] = 86,
    ["AssistBless_LoginDay"] = 86,
    ["AssistBless_OpenRewardTime"] = 86,
    ["AssistBless_TimeStamp"] = 86,
    ["AssistBless_OpenReward"] = 86,
    ["UI_NewYearWish_Common_Content"] = 86,
    ["NicknameTooLong"] = 86,
    ["CustomRoom_AutoLeaveRoom"] = 86,
    ["UI_Match_GoMate"] = 86,
    ["UI_Match_MoreParty"] = 86,
    ["Activity_TakeawayNotifyTipTitle"] = 86,
    ["Activity_TakeawayNotifyTipContent"] = 86,
    ["Permission_LocalNotify_Hint"] = 86,
    ["Activity_Takeaway_CanNotHelpSelf"] = 86,
    ["Common_UpgradeVersion"] = 86,
    ["Bag_UpgradeVersionToSaveSlot"] = 86,
    ["UI_Rank_SeasonIsClose"] = 86,
    ["TeamInvite_Friend"] = 86,
    ["TeamInvite_Home"] = 86,
    ["TeamInvite_StarWorld"] = 86,
    ["TeamInvite_Recent"] = 86,
    ["TeamInvite_Near"] = 86,
    ["TeamInvite_Society"] = 86,
    ["TokenAssistHasAssised"] = 86,
    ["TokenAssistTimesIsFull"] = 86,
    ["Lobby_BecomingLocomotive"] = 86,
    ["Player_SameNickName"] = 86,
    ["VisitInProcess"] = 86,
    ["Activity_SuperLinear_GoGetCoin"] = 86,
    ["InLevel_FinaLQualiying_ExchangeLimit"] = 86,
    ["InLevel_FinaLQualiying_ExchangeRate"] = 86,
    ["InLevel_FinaLQualiying_QPST_None"] = 86,
    ["InLevel_FinaLQualiying_QPST_Champion"] = 86,
    ["InLevel_FinaLQualiying_QPST_Level"] = 86,
    ["InLevel_FinaLQualiying_QPST_Eliminate"] = 86,
    ["InLevel_FinaLQualiying_QPST_FinalLevelRank"] = 86,
    ["InLevel_FinaLQualiying_MainViewScore"] = 86,
    ["InviteFriendVisit"] = 86,
    ["VisitFriendInvalidLocation"] = 86,
    ["VisitFriendSuccess"] = 86,
    ["BP_BuySuperPass"] = 86,
    ["CustomRoom_ViewInfoTip"] = 86,
    ["CustomRoom_AddFriendTip"] = 86,
    ["Common_RankDataIsNullTip"] = 86,
    ["Common_RankRefreshing"] = 86,
    ["NewChat_LobbyRedPacketReceive"] = 86,
    ["NewChat_LobbyPositionShare"] = 86,
    ["NewChat_LobbyClubShare"] = 86,
    ["NewChat_LobbyShareBaseInfo"] = 86,
    ["NewChat_LobbyShareJoinTeam"] = 87,
    ["NewChat_LobbyShareJoinRoom"] = 87,
    ["NewChat_LobbyRedPacketSend"] = 87,
    ["CalendarModelFail"] = 87,
    ["RedEnvelope_OnlyLobby"] = 87,
    ["RedEnvelope_OnlyHome"] = 87,
    ["RedEnvelope_LobbyOrHome"] = 87,
    ["RedEnvelope_OnlyPlatFriendOpen"] = 87,
    ["RedEnvelope_OnlyGameFriendOpen"] = 87,
    ["RedEnvelope_OnlyFriendOpen"] = 87,
    ["RedEnvelope_OthersOpened"] = 87,
    ["RedEnvelope_SelfOpened"] = 87,
    ["RedEnvelope_UnEquipRedEnvelope"] = 87,
    ["Rank_Area"] = 87,
    ["VisitingHome"] = 87,
    ["ModelSelectHomeNameOneParam"] = 87,
    ["UI_BattlePass_TaskItemDescFormat"] = 87,
    ["Permission_TencentMap_Hint"] = 87,
    ["UI_NewYearWishes_Time_Tip"] = 87,
    ["UI_NewYearWishes_Fail_Tip"] = 87,
    ["NewChat_ShareToChannel"] = 87,
    ["NewChat_LobbyPositionShareNoSender"] = 87,
    ["NewChat_LobbyClubShareNoSender"] = 87,
    ["NewChat_LobbyShareBaseInfoNoSender"] = 87,
    ["NewChat_LobbyShareJoinTeamNoSender"] = 87,
    ["NewChat_LobbyShareJoinRoomNoSender"] = 87,
    ["NewChat_LobbyRedPacketSendNoSender"] = 87,
    ["QRCode_SubTitle_Team"] = 87,
    ["QRCode_SubTitle_TeamNotice"] = 87,
    ["QRCode_SubTitle_UGC"] = 87,
    ["QRCode_SubTitle_Club"] = 87,
    ["QRCode_SubTitle_Room"] = 87,
    ["QRCode_RoomId_Room"] = 87,
    ["QRCode_RoomId_Club"] = 87,
    ["QRCode_SubTitle_AddFriend"] = 87,
    ["CommonAssistHasAssised"] = 87,
    ["QRCode_Chat_Club_Error"] = 87,
    ["RaffleTap_LockTips"] = 87,
    ["RaffleAccessCfgData_DiscountTips"] = 87,
    ["QRScan_Fail_InRoom"] = 87,
    ["RedEnvelope_CannotEquiped"] = 87,
    ["Room_JoinRoomSuccess"] = 87,
    ["Room_CanNotJoinRoom"] = 87,
    ["Returning_Privilege_Over"] = 87,
    ["Common_Assist_LevelLimit"] = 87,
    ["AssistBless_CenterTilte1"] = 87,
    ["AssistBless_CenterTilte2"] = 87,
    ["Common_MonthDay"] = 87,
    ["Activity_StarLuck_LeftRedPack"] = 87,
    ["Activity_StarLuck_RedPackNotEnough"] = 87,
    ["Activity_LuckyStar_ItemChangeReason_Task"] = 87,
    ["Activity_LuckyStar_ItemChangeReason_Mail"] = 87,
    ["Activity_LuckyStar_ItemChangeReason_Other"] = 87,
    ["luckystar_1"] = 87,
    ["TeamInviteItem_StarWorld"] = 87,
    ["TeamInviteItem_Recent"] = 87,
    ["TeamInviteItem_Near"] = 87,
    ["TeamInviteItem_Society"] = 87,
    ["RedEnvelope_EquipRedEnvWithRedEnvInHand"] = 87,
    ["RedEnvelope_EquipOtherWithRedEnvInHand"] = 87,
    ["RedEnvelope_EquipRedEnvWithOtherInHand"] = 87,
    ["Returning_SelfPlayer_Addition"] = 87,
    ["Returning_PlayWith_Addition"] = 87,
    ["LBS_LocationNotOpen_Tips"] = 87,
    ["Activity_InvitePlayer"] = 87,
    ["Activity_ReCallPlayer"] = 87,
    ["Activity_ReCallLogin"] = 87,
    ["Team_InBattle"] = 87,
    ["AssistBless_RewardFinished"] = 87,
    ["Activity_LuckyStar_ArkShare_Prompt"] = 87,
    ["Activity_LuckyStar_ArkShare_Desc"] = 87,
    ["Team_Invite_CustomRoomPlay"] = 87,
    ["Team_Invite_UgcRoommPlay"] = 87,
    ["RedEnvelope_RedEnvelopeName"] = 87,
    ["RedEnvelope_RedEnvelopeNameInAward"] = 87,
    ["Common_HasCount"] = 87,
    ["UI_Player_Return_TaskDesc"] = 87,
    ["UI_Player_Return_MainView_Time"] = 87,
    ["Team_JoinRecruitFail"] = 87,
    ["Friend_InAddQQFriendWhiteList"] = 87,
    ["NewChat_IsSpeechToText"] = 87,
    ["NewChat_SearchFailed"] = 87,
    ["NewChat_MaxFriendSearch"] = 87,
    ["FriendModel_StickFriendTopSuccess"] = 87,
    ["FriendModel_CancelStickFriendTopSuccess"] = 87,
    ["FriendModel_MaxStickFriendTips"] = 87,
    ["ChatModel_StickFriendTopSuccess"] = 87,
    ["ChatModel_CancelStickFriendTopSuccess"] = 87,
    ["NewChat_RealTimeRecording"] = 87,
    ["RemarkNameNotValid"] = 87,
    ["RemarkNameSameAsOld"] = 87,
    ["Mall_Name"] = 87,
    ["Mall_Gift_Level_Notice"] = 87,
    ["Mall_AskForGift_Notice"] = 87,
    ["Common_ActivityIsOver"] = 87,
    ["Mall_AskForGift_Success"] = 87,
    ["Bag_Equip_Interactive_Fail1"] = 87,
    ["Bag_Equip_Interactive_Fail2"] = 87,
    ["Team_Reservation_WaitEnd"] = 87,
    ["Team_Reservation_NextTime"] = 87,
    ["Team_Reservation_Accept"] = 88,
    ["Team_Reservation_Reject"] = 88,
    ["Team_Reservation_WaitResult"] = 88,
    ["Team_Reservation_SendSuccess"] = 88,
    ["GiftSelfLevelRequest"] = 88,
    ["GiftFriendLevelRequest"] = 88,
    ["Team_Reservation_Player"] = 88,
    ["Common_Reservation"] = 88,
    ["Common_ReservationDone"] = 88,
    ["Common_RejectDone"] = 88,
    ["Common_AcceptDone"] = 88,
    ["Common_Download"] = 88,
    ["Team_Reservation_MoreThanThree"] = 88,
    ["Team_Reservation_DoNotForget"] = 88,
    ["Setting_Game_MultiScene_Explain"] = 88,
    ["Setting_Game_MultiScene_Game"] = 88,
    ["Setting_Game_MultiScene_Social"] = 88,
    ["Setting_Game_MultiScene_Ugc"] = 88,
    ["Club_NewOwner"] = 88,
    ["Club_AddManager"] = 88,
    ["Team_Reservation_PlayTogether"] = 88,
    ["Team_AutoMatch_SceneError"] = 88,
    ["Team_AutoMatch_NotLeader"] = 88,
    ["Team_AutoMatch_InBattle"] = 88,
    ["Team_AutoMatch_InMatch"] = 88,
    ["Team_AutoMatch_InHome"] = 88,
    ["Team_AutoMatch_InRoom"] = 88,
    ["Team_AutoMatch_InSpecificUI"] = 88,
    ["Mall_Gift_NeedCoin_Notice"] = 88,
    ["Activity_SuperLinear_DrawRange"] = 88,
    ["ModeSelect_SelectFactionFail"] = 88,
    ["ModeSelect_NoFaction"] = 88,
    ["ModeSelect_AutoChangeFaction"] = 88,
    ["Common_Random"] = 88,
    ["Mall_Gift_NotAskForItem"] = 88,
    ["Mall_Gift_Money_Enough"] = 88,
    ["Mall_Gift_No_Money"] = 88,
    ["SettingTip_UGCWorldRecord"] = 88,
    ["SettingTip_RankDisplay"] = 88,
    ["SettingTip_SuitBook"] = 88,
    ["SettingTip_ShowBelongClub"] = 88,
    ["SettingTip_DoubleAction"] = 88,
    ["SettingTip_PersonalizedRecommendation"] = 88,
    ["SettingTip_LobbyOrWorldChat"] = 88,
    ["SettingTip_AcceptTeamInvite"] = 88,
    ["ModelSelect_ActivityGoTo"] = 88,
    ["UI_Recharge_QQMiniGamePayFail"] = 88,
    ["Mall_GiftTitle"] = 88,
    ["Mall_DirectBuy_AskForGift_Notice"] = 88,
    ["BP_Title"] = 88,
    ["UI_Recharge_MonthCard_GiveMoreThanMaxDays_Tips"] = 88,
    ["DirectBuy_GiftOrDemand_GiveMoreThanMaxNum"] = 88,
    ["DirectBuy_GiftOrDemand_ConditionIsNotOK"] = 88,
    ["UI_Recharge_MonthCard_BuyMoreThanMaxDays_Tips"] = 88,
    ["Team_AutoMatch_SelfDownload"] = 88,
    ["FriendModel_StickStr"] = 88,
    ["ChatModel_StickStr"] = 88,
    ["QRCode_Friend_AddSelf_Error"] = 88,
    ["Team_AutoMatch_ChangeMatchType"] = 88,
    ["CustomRoom_InTeam"] = 88,
    ["Bag_WaiguanYuMoshiBupipei"] = 88,
    ["Bag_BeiyongwaiguanTishi"] = 88,
    ["Bag_YulanBushipeiTishi"] = 88,
    ["Chat_UGCMap_KongTaiTiShi"] = 88,
    ["MatchQualifyTime_Start"] = 88,
    ["MatchQualifyTime_End"] = 88,
    ["Activity_OldActivityError"] = 88,
    ["Common_SelfBuy"] = 88,
    ["Common_GotoMail"] = 88,
    ["UI_BattlePass_UnlockPreCantBuyTips"] = 88,
    ["KungFuPanda_NoNoodles"] = 88,
    ["KungFuPanda_Rebirth"] = 88,
    ["KungFuPanda_LongTimeStand"] = 88,
    ["KungFuPanda_PlayerClose"] = 88,
    ["KungFuPanda_FeedFull"] = 88,
    ["KungFuPanda_FeedSuccess"] = 88,
    ["KungFuPanda_CanntStart"] = 88,
    ["KungFuPanda_Use"] = 88,
    ["Gongfuxiongmao_huodongjieshao"] = 88,
    ["shenlongdaxiaabao"] = 88,
    ["KungFuPanda_FeedSuccess_UnderTime"] = 88,
    ["KungFuPanda_FeedFull_UnderTime"] = 88,
    ["UI_LuckBuy_GiveContent"] = 88,
    ["UI_Model_LimitedQualifyTime"] = 88,
    ["UI_Moeel_QualifyStartToEndDay"] = 88,
    ["UI_Model_QualifyWeekDayOpen"] = 88,
    ["UI_Model_AddCollect"] = 88,
    ["UI_Model_RemoveCollect"] = 88,
    ["KungFuPanda_yangguangpuzhao"] = 88,
    ["Model_CurrentModelCantTeam"] = 88,
    ["UI_Common_Quarantine"] = 88,
    ["Concert_StarDetail"] = 88,
    ["Concert_NotInTime"] = 88,
    ["UI_Recharge_UI_SecondChargeRebate_Main_Title"] = 88,
    ["Concert_GetTicket"] = 88,
    ["NewChat_TeamVoiceRestored"] = 88,
    ["NewChat_TeamVoiceNormal"] = 88,
    ["NewChat_TeamVoiceCantUse"] = 88,
    ["NewChat_TeamVoiceOpenSpeakerHint"] = 88,
    ["NewChat_TeamVoiceOpenMicroHint"] = 88,
    ["NewChat_TeamVoiceHintCd"] = 89,
    ["UGC_CANNOT_USE_VEHICLE"] = 89,
    ["Friend_RecommendRelation"] = 89,
    ["NewChat_TeamVoiceOpenSpeakerAsked"] = 89,
    ["NewChat_TeamVoiceOpenMicroAsked"] = 89,
    ["CustomMovement_Title"] = 89,
    ["CustomMovement_ChooseFriend"] = 89,
    ["CustomMovement_DoubleCheck"] = 89,
    ["CustomMovement_NoIntimateRelationship"] = 89,
    ["CustomMovement_NoSearchResult"] = 89,
    ["CustomMovement_FullMovement"] = 89,
    ["Activity_TeamPhoto_SaveSucceed"] = 89,
    ["Activity_TeamPhoto_SaveFailed"] = 89,
    ["Activity_TeamPhoto_ChangeSucceed"] = 89,
    ["Activity_TeamPhoto_ChangeFailed"] = 89,
    ["Activity_TeamPhoto_NotAllowNull"] = 89,
    ["Activity_TeamPhoto_MyTeam"] = 89,
    ["Activity_TeamPhoto_ChangeNameSucceed"] = 89,
    ["Activity_TeamPhoto_Pos"] = 89,
    ["Activity_TeamPhoto_Pos1"] = 89,
    ["Activity_TeamPhoto_Pos2"] = 89,
    ["Activity_TeamPhoto_Pos3"] = 89,
    ["Activity_TeamPhoto_Pos4"] = 89,
    ["Activity_TeamPhoto_Photo"] = 89,
    ["Activity_TeamPhoto_PhotoTime"] = 89,
    ["Team_PST_Xiaowo"] = 89,
    ["Team_PST_InSettlement"] = 89,
    ["Team_PST_UnPrepare"] = 89,
    ["Team_PST_Prepare"] = 89,
    ["Team_PST_CancelPrepare"] = 89,
    ["friend_OnlineReminderTip"] = 89,
    ["Chat_UGCCollectionMap_MapCount"] = 89,
    ["Chat_UGCCollectionMap_MapTitle"] = 89,
    ["Chat_UGCCollectionMap_Invalid"] = 89,
    ["Chat_UGCCollectionMap_CantShareEmpty"] = 89,
    ["Friend_Intimate_Gift"] = 89,
    ["Friend_Intimate_Team"] = 89,
    ["Friend_Intimate_GoMall"] = 89,
    ["Friend_Intimate_ShowTips"] = 89,
    ["NewChat_TeamVoiceOpenMicroAskSucceed"] = 89,
    ["NewChat_TeamVoiceOpenSpeakerAskSucceed"] = 89,
    ["UI_Recharge_MonthCard_GetMoreThanMaxDays_Tips"] = 89,
    ["Bag_SlotEquiped"] = 89,
    ["Bag_Slot_Backup"] = 89,
    ["Mall_CloudCoin"] = 89,
    ["Mall_StarDiamond"] = 89,
    ["Mall_Activity"] = 89,
    ["UI_Recharge_Ul_SecondChargeRebate_Main_Title"] = 89,
    ["Mode_TabSelect_Qualify"] = 89,
    ["Mode_TabSelect_Casual"] = 89,
    ["PlayerInfo_NoDataForSlot"] = 89,
    ["PlayerInfo_EditSlotConfirmTip"] = 89,
    ["PlayerInfo_SlotName"] = 89,
    ["PlayerInfo_BackupSlotName"] = 89,
    ["NewChat_SuperCore"] = 89,
    ["NewChat_Home"] = 89,
    ["SettingTip_ShowSeasonFashion"] = 89,
    ["NewChat_DeleteRecord_Confirm"] = 89,
    ["NewChat_DeleteRecord_Success"] = 89,
    ["NewChat_DeleteRecord_Error_Empty"] = 89,
    ["CustomRoom_NoneLifeRecord"] = 89,
    ["CustomRoom_LifeRecord"] = 89,
    ["Team_IsInFarmScene"] = 89,
    ["Player_PhotoAlbumTabName"] = 89,
    ["Activity_TeamPhoto_ActionTemplate"] = 89,
    ["Activity_TeamPhoto_Background"] = 89,
    ["Activity_TeamPhoto_Expression"] = 89,
    ["Activity_TeamPhoto_TemplateNotAvailable"] = 89,
    ["Clup_NoticeJoinGroup"] = 89,
    ["Clup_NoticeJoinGroupWX"] = 89,
    ["Clup_NoticeJoinGroupQQ"] = 89,
    ["PlayerInfo_DefaultProfileThemeName"] = 89,
    ["PlayerInfo_DefaultProfileThemeDes"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_DeleteTips"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_DeleteSuccessTips"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_SaveMoreThanMaxTips"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_SaveAgainTips"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_SaveSuccessTips"] = 89,
    ["NewChat_TeamRecruit"] = 89,
    ["Bag_Combination_Count"] = 89,
    ["Bag_Combination_Name"] = 89,
    ["Bag_Combination_ErrorInfo"] = 89,
    ["Bag_Combination_ErrorPreview"] = 89,
    ["Bag_Combination_NameEmpty"] = 89,
    ["Bag_Combination_ChatMsgEmpty"] = 89,
    ["Bag_Combination_NotSave"] = 89,
    ["Bag_Combination_DeleteCombination"] = 89,
    ["Clup_JoinGroupWXSuccess"] = 89,
    ["Clup_JoinGroupQQSuccess"] = 89,
    ["Clup_NoticeUnbindGroup"] = 89,
    ["Club_NoticeUnbindGroupText"] = 89,
    ["Club_NoticeCreateGroupText"] = 89,
    ["Club_NoticeCreateGroupTips"] = 89,
    ["Club_Button_Close"] = 89,
    ["Club_Button_Confirm"] = 89,
    ["PlayerInfo_DunDes"] = 89,
    ["PlayerInfo_CannotPreviewSuit"] = 89,
    ["UI_PlayerInfo_PhotoAlbum_SaveFailedTips"] = 89,
    ["PlayerInfo_FashionValueInPreviewSuit"] = 89,
    ["Club_Button_WxGroup"] = 89,
    ["Club_Button_QQGroup"] = 90,
    ["friend_targetLobbyNotDownloaded"] = 90,
    ["friend_targetLobbyNotJump"] = 90,
    ["friend_notJump"] = 90,
    ["GameGotoTask_TS_Init"] = 90,
    ["GameGotoTask_TS_Completed"] = 90,
    ["GameGotoTask_TS_Rewarded"] = 90,
    ["GameGotoActivity_TS_Init"] = 90,
    ["GameGotoActivity_TS_Completed"] = 90,
    ["GameGotoActivity_TS_Rewarded"] = 90,
    ["Team_PST_Farm"] = 90,
    ["Team_ShowInfoInTeam"] = 90,
    ["Team_ShowInfoInRoom"] = 90,
    ["Lobby_StatusCanNotUseHandHold"] = 90,
    ["Rank_Gps_Location"] = 90,
    ["Club_NoticeCreateGroupQQText"] = 90,
    ["NewChat_Stranger_ChatOver"] = 90,
    ["NewChat_Stranger_ChatBusy"] = 90,
    ["NewChat_Stranger_ChatWaitResp"] = 90,
    ["NewChat_Stranger_ChatNeedResp"] = 90,
    ["NewChat_Stranger_ChatManualOver"] = 90,
    ["NewChat_Stranger_Fobbiden"] = 90,
    ["NewChat_Stranger_TooMuch"] = 90,
    ["NewChat_Stranger_Error"] = 90,
    ["NewChat_Stranger_DefaultMessage"] = 90,
    ["NewChat_Stranger_ChatOver_Confirm"] = 90,
    ["NewChat_Stranger_Tag"] = 90,
    ["NewChat_Stranger_AlreadyFriend"] = 90,
    ["NewChat_Stranger_PermitStrangerSayHi"] = 90,
    ["NewChat_Stranger_LimitBeforeResp"] = 90,
    ["Billboard_Voice_No_Text"] = 90,
    ["Activity_TeamPhoto_EmojiInoperable"] = 90,
    ["Activity_TeamPhoto_EmojiUnableToLock"] = 90,
    ["Activity_TeamPhoto_ReachTheUpperLimit"] = 90,
    ["Team_SimpleShowInfoInTeam"] = 90,
    ["Team_SimpleShowInfoInRoom"] = 90,
    ["AcceptJoinError_1"] = 90,
    ["AcceptJoinError_2"] = 90,
    ["AcceptJoinError_3"] = 90,
    ["AcceptJoinError_4"] = 90,
    ["AcceptJoinError_5"] = 90,
    ["Invite_Reject_ServerTip"] = 90,
    ["Team_WantJoinSendFinish"] = 90,
    ["Team_WantJoinSendFailed"] = 90,
    ["Team_IsInTargetTeam"] = 90,
    ["Team_IsInTargetRoom"] = 90,
    ["RH_Tab_MatchRecruit"] = 90,
    ["RH_Tab_UGCRecruit"] = 90,
    ["RH_SubTab_RoomRecruit"] = 90,
    ["RH_SubTab_TeamRecruit"] = 90,
    ["Friend_Later_add"] = 90,
    ["UI_ScreenSnapshot_Normal"] = 90,
    ["UI_ScreenSnapshot_UGCCommunityScene"] = 90,
    ["UI_ScreenSnapshot_CommunityScene"] = 90,
    ["UI_ScreenSnapshot_UGCMapScene"] = 90,
    ["UI_ScreenSnapshot_HomeScene"] = 90,
    ["UI_ScreenSnapshot_FarmScene"] = 90,
    ["UI_ScreenSnapshot_InGameScene"] = 90,
    ["Bag_RentFriendFashionTip"] = 90,
    ["Bag_RentFriendFashionTip_1"] = 90,
    ["SettingTip_RecommendFriend"] = 90,
    ["SettingTip_ShowTeamExtraInfo"] = 90,
    ["SettingTip_baby_funSetting"] = 90,
    ["SettingTip_baby_closeChat"] = 90,
    ["SettingTip_baby_closeTeamInvite"] = 90,
    ["SettingTip_baby_closeStrangerChat"] = 90,
    ["SettingTip_baby_closeVisitHome"] = 90,
    ["Setting_Tips_GoToBabyProtectFun"] = 90,
    ["SettingTip_baby_funSetting_all"] = 90,
    ["NewChat_WantToPlay"] = 90,
    ["Club_Activity"] = 90,
    ["NewChat_Stranger_CantUseVoice"] = 90,
    ["NewChat_Stranger_AlreadySayHiWaitResp"] = 90,
    ["Team_InWantToPlayCD"] = 90,
    ["Team_IsInMineRoom"] = 90,
    ["CustomRoom_ChangeLeaderTip"] = 90,
    ["CustomRoom_BeLeaderTip"] = 90,
    ["NewChat_Farm"] = 90,
    ["RaffleAccessCfgData_SwanDiscountTips"] = 90,
    ["QQVideoCantPlayWhenSwitchScene"] = 90,
    ["Mail_Gift_Notice"] = 90,
    ["Activity_WishesCameTrue_SelectGift"] = 90,
    ["Activity_WishesCameTrue_ConfirmGift"] = 90,
    ["Activity_WishesCameTrue_WishRecord"] = 90,
    ["Activity_WishesCameTrue_RewardRecord"] = 90,
    ["Activity_WishesCameTrue_NoLotteryCoin"] = 90,
    ["Activity_WishesCameTrue_NeedLockAllGifts"] = 90,
    ["Activity_WishesCameTrue_CopyTokenSucess"] = 90,
    ["Activity_WishesCameTrue_GetWishCoinSucess"] = 90,
    ["Activity_WishesCameTrue_NoRepeatHelp"] = 90,
    ["Activity_WishesCameTrue_NoRepeatHelp2"] = 90,
    ["Activity_WishesCameTrue_LotteryOpenTime"] = 90,
    ["Activity_WishesCameTrue_RewardNotification"] = 90,
    ["Activity_WishesCameTrue_InvalidToken"] = 90,
    ["Activity_WishesCameTrue_CannotHelpSelf"] = 90,
    ["Activity_WishesCameTrue_CannotShareToken"] = 90,
    ["Activity_WishesCameTrue_CannotHelp"] = 90,
    ["Activity_WishesCameTrue_HelpGetWishCoin"] = 90,
    ["Activity_WishesCameTrue_ReachHelpLimitTip"] = 90,
    ["Activity_WishesCameTrue_TaskGetWishCoin"] = 90,
    ["Activity_WishesCameTrue_LotteryGetReward"] = 91,
    ["Activity_WishesCameTrue_GetGift"] = 91,
    ["Activity_WishesCameTrue_WarmTip"] = 91,
    ["Activity_WishesCameTrue_GetFreeWishCoin"] = 91,
    ["Activity_WishesCameTrue_Known"] = 91,
    ["Activity_WishesCameTrue_ReachHelpLimitTip2"] = 91,
    ["Activity_WishesCameTrue_NoRepeatHelp3"] = 91,
    ["Activity_WishesCameTrue_OtherHelpErrorTip"] = 91,
    ["Activity_HYWarmUp_Day"] = 91,
    ["Activity_HYWarmUp_HYBegin"] = 91,
    ["Common_TipSign"] = 91,
    ["Common_GoNow"] = 91,
    ["Activity_HYWarmUp_FinishTaskTip"] = 91,
    ["Mall_NotOpenZeroBuy"] = 91,
    ["Mode_TimeLimitMatch"] = 91,
    ["Mode_SomeTimeLaterMatchClose"] = 91,
    ["Prohibit_Editing"] = 91,
    ["System_PlayerReturn_Recharge_BigReward"] = 91,
    ["System_PlayerReturn_Recharge_DailyReward"] = 91,
    ["SettingTip_ShowInfoInList"] = 91,
    ["Club_Disable_Name"] = 91,
    ["Club_Disable_Dsec"] = 91,
    ["Club_Disable_Icon"] = 91,
    ["Common_CurrentTrophyCount"] = 91,
    ["Common_WinCoun_Short"] = 91,
    ["Common_WXLoginFailWithReason"] = 91,
    ["Common_QQLoginFailWithReason"] = 91,
    ["Common_WXLoginFailWithCode"] = 91,
    ["Common_QQLoginFailWithCode"] = 91,
    ["Team_CanNotRecommendInMatch"] = 91,
    ["Team_CanNotJoinRoomInMatch"] = 91,
    ["Team_CanNotChangeModeInMatch"] = 91,
    ["Team_CanNotChangeMatchInMatch"] = 91,
    ["SettingTip_ToFriendShowInfo"] = 91,
    ["SettingTip_ToStrangerShowInfo"] = 91,
    ["CustomRoom_ChangeMatchTip"] = 91,
    ["System_PlayerReturn_ActivityIsDone"] = 91,
    ["Friend_AlreadyIntimate"] = 91,
    ["Friend_IntimateNumMax"] = 91,
    ["Friend_OnlyFriendCanChangeName"] = 91,
    ["Friend_NewChatFriendCellNewFriend"] = 91,
    ["NewChat_Club_ShareImageSuccess"] = 91,
    ["CustomRoom_UpdateLocationFail"] = 91,
    ["Team_OtherJoinSuccessFromOne"] = 91,
    ["Team_SelfJoinSuccess"] = 91,
    ["Team_OtherJoinSuccessFromTeam"] = 91,
    ["Team_SelfLeaveSuccess"] = 91,
    ["Team_OtherLeaveSuccessFromTeam"] = 91,
    ["Activity_WaitChecked"] = 91,
    ["Friend_IntimateNotEnough"] = 91,
    ["IAA_PlayAD_Fail"] = 91,
    ["IAA_PlayAD_Success"] = 91,
    ["IAA_LotteryView_CountTips"] = 91,
    ["IAA_FinalAccount_Tips"] = 91,
    ["System_PlayerReturn_InvalidJump"] = 91,
    ["Club_Rank"] = 91,
    ["IAA_LotteryView_Enter_CountTips"] = 91,
    ["Activity_CanReceiveReward"] = 91,
    ["Activity_SureToClearAllReddots"] = 91,
    ["Team_CannotUsePropInTeamShow"] = 91,
    ["CustomRoom_ChangeModeTipInLobby"] = 91,
    ["Team_CannotJoinRoomInTeam"] = 91,
    ["CustomRoom_ForbidenOperation"] = 91,
    ["CustomAction_Iknow"] = 91,
    ["CustomAction_GotoBind"] = 91,
    ["CustomAction_BindFriendTips"] = 91,
    ["Team_ReturnPlayerInTeam"] = 91,
    ["QQVideoProtect_Setting_Tips"] = 91,
    ["QQVideoProtect_Setting_ForgetPasswordTimeFormat"] = 91,
    ["QQVideoProtect_Setting_PasswordNotEnoughTips"] = 91,
    ["QQVideoProtect_Setting_UnlockSuccessTips"] = 91,
    ["QQVideoProtect_Setting_UnlockFailTips"] = 91,
    ["QQVideoProtect_Setting_ForgetPasswordTips"] = 91,
    ["QQVideoProtect_Setting_ForgetPasswordSuccessTips"] = 91,
    ["QQVideoProtect_Setting_UnlockEmptyInputTips"] = 91,
    ["QQVideoProtect_Setting_LockNotEnoughTips"] = 91,
    ["QQVideoProtect_Setting_UnlockOpenQQVideoNotEnoughTips"] = 91,
    ["QQVideoProtect_Setting_UnlockOpenQQVideoPasswordWrongTips"] = 91,
    ["QQVideoProtect_Setting_LockEmptyTips"] = 91,
    ["QQVideoProtect_Setting_LockAgainPasswordNotSameTips"] = 91,
    ["QQVideoProtect_Setting_LockSuccessTips"] = 91,
    ["QQVideoProtect_Setting_LockConfirm"] = 91,
    ["QQVideoProtect_Setting_LockAgainNotEnoughTips"] = 91,
    ["QQVideoProtect_Setting_LockAgainEmptyTips"] = 91,
    ["QQVideoProtect_Setting_OpenQQVideoLockTips"] = 91,
    ["QQVideoProtect_Setting_TipUserLockTips"] = 91,
    ["QQVideoProtect_Setting_TipUserConfirm"] = 91,
    ["QQVideoProtect_Setting_TipUserCancel"] = 91,
    ["NumericKeypad_MaxLength_Tips"] = 91,
    ["NumericKeypad_MinLength_Tips"] = 91,
    ["QQVideo_TipCloseTips"] = 91,
    ["CloudEnv_RecentPlay"] = 91,
    ["Cup_Bg_Title_Main"] = 91,
    ["Cup_Bg_Title_Task"] = 91,
    ["Cup_Bg_AwardWeekMaxHint"] = 91,
    ["Friend_Intimate_Farm"] = 91,
    ["UI_Friend_Days"] = 91,
    ["UI_Team_Nums"] = 91,
    ["UI_Speak_Nums"] = 91,
    ["UI_Coin_Nums"] = 91,
    ["UI_XiaoWo_Nums"] = 92,
    ["UI_Farm_Nums"] = 92,
    ["UI_SendGift_Nums"] = 92,
    ["Activity_Monopoly_MissDice"] = 92,
    ["Activity_Monopoly_TreasureChestsReward"] = 92,
    ["Activity_Monopoly_TreasureChestsRewardTip"] = 92,
    ["Activity_Monopoly_TreasureChestsRewardTip2"] = 92,
    ["Activity_Monopoly_RewardPreview"] = 92,
    ["Activity_Monopoly_AdvanceXStep"] = 92,
    ["Activity_Monopoly_AdvanceXStep2"] = 92,
    ["Activity_Monopoly_CannotDiceWhenMoving"] = 92,
    ["Activity_Monopoly_StopOnEmptyGrid"] = 92,
    ["Activity_Monopoly_StopOnAdvanceGrid"] = 92,
    ["Activity_Monopoly_StopOnConfirmedDiceGrid"] = 92,
    ["Activity_Monopoly_StopOnDoubleDiceGrid"] = 92,
    ["Activity_Monopoly_ClickTreasureGridTip"] = 92,
    ["Activity_Monopoly_PassTreasureGrid"] = 92,
    ["Activity_Monopoly_StopOnStartPoint"] = 92,
    ["Activity_Monopoly_PassStartPoint"] = 92,
    ["Activity_Monopoly_StopOnJumpGrid"] = 92,
    ["Activity_Monopoly_AutoLotteryTip"] = 92,
    ["Activity_Monopoly_LotteryPreviewTitle"] = 92,
    ["Activity_Monopoly_RemainsX"] = 92,
    ["Activity_Monopoly_ClickToTake"] = 92,
    ["Activity_Monopoly_TookAll"] = 92,
    ["Activity_Monopoly_TreasureFull"] = 92,
    ["Activity_Monopoly_LotteryTitle"] = 92,
    ["Activity_Monopoly_LotteryPoolTip"] = 92,
    ["Rank_ID_Mastery"] = 92,
    ["UI_PlayerInfo_CupAward_Tip"] = 92,
    ["ExclusiveVehicle_ForbidSpecialSkillSlotTips"] = 92,
    ["Cup_Bg_Title_LeftUp"] = 92,
    ["CustomAction_Vehicle1P"] = 92,
    ["CustomAction_Vehicle2P"] = 92,
    ["AnimalHandbook_FeedConfirm"] = 92,
    ["AnimalHandbook_FeedTips"] = 92,
    ["AnimalHandbook_DonateTimesTips"] = 92,
    ["ActNavigation_UnlockSystemByLevel"] = 92,
    ["Cup_NotUpToLevel"] = 92,
    ["Cup_NotUpToLevelHint"] = 92,
    ["NewChat_ClubScreenShare"] = 92,
    ["UI_ActivityFridayCollectionItemNotStarted"] = 92,
    ["UI_ActivityFridayCollectionTimeStart"] = 92,
    ["HandHold_ExtraEffect_LimitInCurren"] = 92,
    ["HandHold_ExtraEffect_InCD"] = 92,
    ["HandHold_ExtraEffect_ReachMaxCount"] = 92,
    ["HandHold_ExtraEffect_ExistOtherExtraEffect"] = 92,
    ["HandHold_ExtraEffect_UnknownReason"] = 92,
    ["Handbook_Record_Donate"] = 92,
    ["Handbook_Record_Receive"] = 92,
    ["Team_ReturnPlayerInTeamResult"] = 92,
    ["NewChat_Vertical_InviteTeam"] = 92,
    ["Cup_LevelEndTag"] = 92,
    ["Cup_MainSelfCupWeekMaxTag"] = 92,
    ["MainGame_SystemMusicDownload_Tip"] = 92,
    ["MainGame_SystemMusicDownload_Cancel"] = 92,
    ["MainGame_SystemMusicDownload_Download"] = 92,
    ["MainGame_NoDownloadMusicTip"] = 92,
    ["MainGame_RecommendDownloadMusicTip"] = 92,
    ["Friend_GotoIntimateShowTips"] = 92,
    ["Cup_MaxTipHint"] = 92,
    ["UI_PlayerInfo_CupAward_Title"] = 92,
    ["NewChat_Vertical_JoinClubRankNotice_Bottom"] = 92,
    ["NewChat_Vertical_JoinClubRank_Bottom"] = 92,
    ["NewChat_Vertical_SystemJoinClubRank_Bottom"] = 92,
    ["NewChat_Vertical_ClubWeekSettle_Bottom"] = 92,
    ["Cup_ShetuanTitle"] = 92,
    ["Rank_Common_Tips"] = 92,
    ["Rank_ID_Mastery_Tips"] = 92,
    ["UI_Please_MakeFriend"] = 92,
    ["Activity_ReserveLiveRoomFailed"] = 92,
    ["NewChat_ClubScreenShare_LobbyLeft"] = 92,
    ["NewChat_ClubWeekSettle_LobbyLeft"] = 92,
    ["NewChat_JoinClubRankNotice_LobbyLeft"] = 92,
    ["NewChat_SystemJoinClubRank_LobbyLeft"] = 92,
    ["NewChat_JoinClubRank_LobbyLeft"] = 92,
    ["NewChat_Club_ShareClubRankSuccess"] = 92,
    ["UI_Activity_FarmActivity_GetCount"] = 92,
    ["UI_Activity_FarmActivity_DrawOnce"] = 92,
    ["UI_Activity_FarmActivity_LotteryTip"] = 92,
    ["Task_TabNotExist"] = 92,
    ["Cup_NotReady"] = 92,
    ["Arena_CardPackOverflowReturn"] = 92,
    ["AnimalHandbook_CatchTips"] = 92,
    ["QQVideoCantPlayWhenScreenDirChange"] = 92,
    ["Text_TeamJoin_Notice"] = 92,
    ["Bag_SeasonTipsNum"] = 92,
    ["CupRankRule"] = 92,
    ["CupRankRuleDesc"] = 92,
    ["UI_PermitAdvanceUnlock_BtnTxt"] = 92,
    ["UI_ConvenedRecallTab_Txt"] = 92,
    ["UI_ConvenedInviteTab_Txt"] = 92,
    ["UI_ConvenedRecallLoginTab_Txt"] = 92,
    ["UI_ConvenedInviteLoginTab_Txt"] = 92,
    ["UI_ConvenedRecallLoginTips_Txt"] = 92,
    ["UI_ConvenedInviteLoginTips_Txt"] = 92,
    ["UI_ConvenedRecallEmptyTips_Txt"] = 92,
    ["UI_ConvenedInviteEmptyTips_Txt"] = 92,
    ["MatchQualifyTime_Start_ShowName"] = 92,
    ["MatchQualifyTime_End_ShowName"] = 92,
    ["BP_BuySuperPass_TipsText"] = 93,
    ["Cup_DailyTaskHint"] = 93,
    ["Common_QQBackToCommunityTip"] = 93,
    ["Common_GoToCommunity"] = 93,
    ["Activity_PleaseCheckedAndReceive"] = 93,
    ["Team_PST_TeamShow"] = 93,
    ["Team_PST_TeamShowLobbyTip"] = 93,
    ["Team_PST_NormalLobbyTip"] = 93,
    ["Team_MatchNotUnlockTip"] = 93,
    ["Team_MatchNotDownloadTip"] = 93,
    ["UI_ExpansionSuccessful"] = 93,
    ["Farmyard_QQShare_Tips"] = 93,
    ["Club_UGCMapCollected"] = 93,
    ["Club_PersonalPhotoAlbum"] = 93,
    ["Player_ChatFont"] = 93,
    ["ColorFont_BaseText"] = 93,
    ["Douyin_ShareNotInstallApp"] = 93,
    ["IAA_BalancePop_Tips"] = 93,
    ["CustomRoom_ExitRoomTip"] = 93,
    ["Club_SendToClub"] = 93,
    ["Club_RankNotOpen"] = 93,
    ["Team_ExitTeamTip"] = 93,
    ["ReChoose_Exit"] = 93,
    ["ReChoose_Confirm"] = 93,
    ["ReChoose_Succeed"] = 93,
    ["ClubRankRule"] = 93,
    ["ClubRankRuleDesc"] = 93,
    ["ReChoose_GiveUp"] = 93,
    ["UI_PrivacyIsSet"] = 93,
    ["UI_NoWayTeleportToLocation"] = 93,
    ["TeamShow_LobbyChanging"] = 93,
    ["TeamShow_LobbyChangedCD"] = 93,
    ["Activity_Exchange_PlayerReturn_SelectGift"] = 93,
    ["PlayerReturn_Calendar_Tip"] = 93,
    ["UI_PlayerInfo_StarPInfo_Text"] = 93,
    ["UI_PlayerInfo_StarPInfo_Tips"] = 93,
    ["Cup_Bg_Title_ModelSelect"] = 93,
    ["UI_GreenHouse_Result_Normal_1"] = 93,
    ["UI_GreenHouse_Result_Normal_2"] = 93,
    ["UI_GreenHouse_Result_Normal_3"] = 93,
    ["UI_GreenHouse_Result_Normal_1_Uid"] = 93,
    ["UI_GreenHouse_Result_Normal_2_Uid"] = 93,
    ["UI_GreenHouse_Result_Normal_3_Uid"] = 93,
    ["UI_GreenHouse_Result_Normal_Btn"] = 93,
    ["UI_GreenHouse_Result_New"] = 93,
    ["UI_GreenHouse_Result_New_Uid"] = 93,
    ["UI_GreenHouse_Result_New_Btn"] = 93,
    ["UI_GreenHouse_Result_Smurf"] = 93,
    ["UI_GreenHouse_Result_Smurf_Uid"] = 93,
    ["UI_GreenHouse_Result_Black"] = 93,
    ["UI_GreenHouse_Result_Already"] = 93,
    ["UI_GreenHouse_Result_Limit"] = 93,
    ["UI_GreenHouse_Result_End"] = 93,
    ["UI_GreenHouse_Result_Error"] = 93,
    ["UI_GreenHouse_Result_Same"] = 93,
    ["Lobby_InviteTrain"] = 93,
    ["Team_HasMemberMatchLocked"] = 93,
    ["Team_HasMemberMatchUnDownload"] = 93,
    ["UI_HonoroKing_SuitTitleFinish"] = 93,
    ["UI_HonoroKing_SuitTitleSelect"] = 93,
    ["UI_HonoroKing_OrnamentTitleFinish"] = 93,
    ["UI_HonoroKing_OrnamentTitleSelect"] = 93,
    ["UI_HonoroKing_SuitHasTips"] = 93,
    ["UI_HonoroKing_OrnamentHasTips"] = 93,
    ["UI_HonoroKing_SuitWishDoneTips"] = 93,
    ["UI_HonoroKing_OrnamentWishDoneTips"] = 93,
    ["Pak_WiFiDownloadPreviewRes"] = 93,
    ["Pak_MobileNetDownloadPreviewRes"] = 93,
    ["UI_BattlePass_IAA_CountTips"] = 93,
    ["Mall_NotDownloaded_GoToMain"] = 93,
    ["UI_ShowCup"] = 93,
    ["UI_ShowCupHelp"] = 93,
    ["Tips_BagChat_NoPassWorldCheck"] = 93,
    ["UI_NotEstablished"] = 93,
    ["UI_Couple"] = 93,
    ["UI_Brothers"] = 93,
    ["UI_Chums"] = 93,
    ["UI_Girlfriends"] = 93,
    ["System_MineSweeper_Items_Insufficient"] = 93,
    ["System_MineSweeper_Stamina_Title"] = 93,
    ["System_MineSweeper_Event_OpenRow"] = 93,
    ["System_MineSweeper_Event_OpenColumn"] = 93,
    ["System_MineSweeper_Event_ShowReward"] = 93,
    ["System_MineSweeper_ConsumeTicket_EveryTime"] = 93,
    ["System_MineSweeper_Slogan"] = 93,
    ["System_MineSweeper_Event_OpenRowToast"] = 93,
    ["System_MineSweeper_Event_OpenColumnToast"] = 93,
    ["System_MineSweeper_Event_ShowRewardToast"] = 93,
    ["System_MineSweeper_Is_Playing_Animation"] = 93,
    ["System_MineSweeper_Number_ShowAround"] = 93,
    ["UI_RefuseLuckyMission"] = 93,
    ["UI_Setting_Predown_Tips_On"] = 93,
    ["UI_Setting_Predown_Tips_Off"] = 93,
    ["UI_Langrenqixi_Process_Notfull_tips"] = 93,
    ["UI_Langrenqixi_Nokey_tips"] = 93,
    ["UI_Langrenqixi_NeitherofBoth_tips"] = 93,
    ["Team_ChangeLeaderTip"] = 93,
    ["Team_BeLeaderTip"] = 93,
    ["UI_TopIntimacyFriend"] = 93,
    ["MiniGame_DouYin_Not_Support"] = 93,
    ["ReChoose_ChangeNameFreeHint"] = 94,
    ["ReChoose_ChangeCharacterFreeHint"] = 94,
    ["UI_Mission_IsFinish"] = 94,
    ["UI_Mission_IsClose"] = 94,
    ["UI_WXGameHUD_CollectTip"] = 94,
    ["UI_Mission_HaveTime"] = 94,
    ["UI_Mission_HaveMission"] = 94,
    ["Mall_SuccessfullyCard_CheckToFarmCard"] = 94,
    ["Activity_GroupReturning_InviteLimitTip"] = 94,
    ["Activity_GroupReturning_BeLeaderTip"] = 94,
    ["Activity_GroupReturning_TickedTip"] = 94,
    ["Activity_GroupReturning_ExitedTip"] = 94,
    ["Activity_GroupReturning_BuyBeforeGetTip"] = 94,
    ["Activity_GroupReturning_BuySucessTip"] = 94,
    ["Activity_GroupReturning_NoPlayerTip"] = 94,
    ["Activity_GroupReturning_SentInviteTip"] = 94,
    ["Activity_GroupReturning_WaitTip"] = 94,
    ["Activity_GroupReturning_JoinedOtherTip"] = 94,
    ["Activity_GroupReturning_JoinedThisTip"] = 94,
    ["Activity_GroupReturning_FullTip"] = 94,
    ["Activity_GroupReturning_JoinSucessTip"] = 94,
    ["Activity_GroupReturning_Known"] = 94,
    ["Activity_GroupReturning_ViewInfo"] = 94,
    ["Activity_GroupReturning_Kick"] = 94,
    ["Activity_GroupReturning_Grouped"] = 94,
    ["Activity_GroupReturning_Invite"] = 94,
    ["Activity_GroupReturning_AskInvite"] = 94,
    ["Activity_GroupReturning_ExitGroupTip"] = 94,
    ["Activity_GroupReturning_AskJoin"] = 94,
    ["Activity_GroupReturning_JoinWarningTip"] = 94,
    ["Activity_GroupReturning_ExitConfirmTip"] = 94,
    ["Activity_GroupReturning_KickConfirmTip"] = 94,
    ["Activity_GroupReturning_Chat_JoinNoSender"] = 94,
    ["Activity_GroupReturning_Chat_JoinWithSender"] = 94,
    ["Activity_GroupReturning_BeKickedTip"] = 94,
    ["Activity_GroupReturning_InvalidInvate"] = 94,
    ["UI_LikeHistoryItemShow"] = 94,
    ["LFT_PersonalMain"] = 94,
    ["LFT_PersonalCard"] = 94,
    ["UI_Mission_LackSome"] = 94,
    ["UI_Mission_InviteFriend"] = 94,
    ["UI_WishingTree_NoWishing"] = 94,
    ["UI_WishingTree_NoChoose"] = 94,
    ["WXMoreGamePlay_BackToLobby"] = 94,
    ["Team_DownloadPercent"] = 94,
    ["NoPictureAccessTips"] = 94,
    ["GuideApp_Check_Fail"] = 94,
    ["GuideApp_Settlement"] = 94,
    ["GuideApp_MsgBox_Main"] = 94,
    ["GuideApp_MsgBox_Download"] = 94,
    ["GuideApp_PopAdsBtn_Normal"] = 94,
    ["GuideApp_PopAdsBtn_Countdown"] = 94,
    ["GuideApp_PopAdsBtn_Close"] = 94,
    ["GuideApp_MsgBox_Activity_Main"] = 94,
    ["GuideApp_MsgBox_Activity_Download"] = 94,
    ["Common_ShowAfterDownload"] = 94,
    ["UI_LikeHistoryHasFollowBack"] = 94,
    ["UI_CantChangeBackground"] = 94,
    ["UI_LeaderChangeBackground"] = 94,
    ["Team_CannotUsePropInMatching"] = 94,
    ["Common_DownloadFirst"] = 94,
    ["Activity_Easy_LockTips"] = 94,
    ["Activity_EasyReserve_MainTips"] = 94,
    ["Activity_EasyReserve_SuccessTips"] = 94,
    ["Activity_EasyTask_MainTips"] = 94,
    ["Activity_EasyTask_TotalTips"] = 94,
    ["Activity_EasyTask_CDTips"] = 94,
    ["Activity_EasyTask_ItemLeft"] = 94,
    ["ActivityEasyTeamTitle"] = 94,
    ["Activity_TabSubName_Lock"] = 94,
    ["Activity_IsJoinTheEasyTeam"] = 94,
    ["Activity_Easy_InTeamtips"] = 94,
    ["Activity_ShareTitle"] = 94,
    ["Activity_ShareSubTitle"] = 94,
    ["Activity_ShareFriendTitle"] = 94,
    ["Activity_ShareSubFriendTitle"] = 94,
    ["Activity_ShareSubTitleOk"] = 94,
    ["Activity_ShareSubFriendTitleOk"] = 94,
    ["UI_Unable_Go_CallBackToLobby"] = 94,
    ["Team_PST_House"] = 94,
    ["UI_WishingTree_CheckChoose"] = 94,
    ["Activity_GoFishing_Catties"] = 94,
    ["Activity_GoFishing_TimeBucket"] = 94,
    ["UI_QQAuthorizationRuleText"] = 94,
    ["UI_QQAuthorizationRejectText"] = 94,
    ["UI_QQAuthorizationFriendNumText"] = 94,
    ["DrawReward_MidAutumnText"] = 94,
    ["Player_Suit"] = 94,
    ["Player_Vehicle"] = 94,
    ["Recharge_LuckyTurntable_BtnOpen"] = 94,
    ["Recharge_LuckyTurntable_LockTips"] = 94,
    ["Activity_NationalDayHundredDraw_Tip"] = 94,
    ["Common_Exit"] = 94,
    ["Common_BackToAuthorization"] = 94,
    ["TeamShow_ExitConfirmTip"] = 94,
    ["TeamShow_KeepTeamTip"] = 94,
    ["TeamShow_ExitTeamTip"] = 94,
    ["GoToWXSubscription"] = 94,
    ["OpenWXSubscription"] = 94,
    ["Mall_MiniGameIosMoneyDeficitNotice"] = 94,
    ["Mall_DownloadApp"] = 95,
    ["Mall_MiniGameIosNotifyNotice"] = 95,
    ["Mall_WaitNotice"] = 95,
    ["Mall_RightNowNotice"] = 95,
    ["Mall_AskForGift_Notice_IOS"] = 95,
    ["Activity_WolfKillTeam_NoPlayerTip"] = 95,
    ["Activity_WolfKillTeam_SentInviteTip"] = 95,
    ["Activity_WolfKillTeam_JoinedOtherTip"] = 95,
    ["Activity_WolfKillTeam_JoinedThisTip"] = 95,
    ["Activity_WolfKillTeam_FullTip"] = 95,
    ["Activity_WolfKillTeam_JoinSucessTip"] = 95,
    ["Activity_WolfKillTeam_Invite"] = 95,
    ["Activity_WolfKillTeam_AskInvite"] = 95,
    ["Activity_WolfKillTeam_AskJoin"] = 95,
    ["Activity_WolfKillTeam_Chat_JoinNoSender"] = 95,
    ["Activity_WolfKillTeam_Chat_JoinWithSender"] = 95,
    ["Activity_WolfKillTeam_InvalidInvate"] = 95,
    ["Activity_WolfKillTeam_SentInviteToClubTip"] = 95,
    ["Activity_WolfKillTeam_SentInviteToWorldTip"] = 95,
    ["Activity_WolfKillTeam_SentInviteToLobbyTip"] = 95,
    ["Activity_WolfKillTeam_TeamNotFormedTip"] = 95,
    ["Activity_WolfKillTeam_InvalidTeamTip"] = 95,
    ["Activity_WolfKillTeam_ActivityTip"] = 95,
    ["UI_NationalDayWarm_SignSuccess"] = 95,
    ["NewChat_Arena_NotSetting"] = 95,
    ["NewChat_RemoveInterestChannel"] = 95,
    ["NewChat_StickChannel"] = 95,
    ["NewChat_CancelStickChannel"] = 95,
    ["NewChat_Channel_JoinSuccess"] = 95,
    ["NewChat_Channel_StickSuccess"] = 95,
    ["NewChat_Channel_CancelStickSuccess"] = 95,
    ["NewChat_Channel_SelfTag"] = 95,
    ["UI_CostumeBall_Tips"] = 95,
    ["UI_ClothShow_Share"] = 95,
    ["UI_WeekendLuckyStar_Tips"] = 95,
    ["UI_WeekendLuckyStar_ButtonText"] = 95,
    ["UI_Fixed_Success"] = 95,
    ["UI_Fixed_Fail"] = 95,
    ["UI_GetAllClothNum"] = 95,
    ["Friend_QQNewGuideTip"] = 95,
    ["Friend_WXNewGuideTip"] = 95,
    ["UI_ScoringSystemOptionOther"] = 95,
    ["Bag_ItemTypeSort"] = 95,
    ["Bag_ItemTimeSort"] = 95,
    ["Bag_FavoriteOk"] = 95,
    ["Bag_FavoriteBtnFalse"] = 95,
    ["Bag_Tips_AddFavorite"] = 95,
    ["Bag_Tips_RemoveFavorite"] = 95,
    ["Bag_Tips_SettingActionLoopSuccess"] = 95,
    ["Bag_Tips_SettingActionNoLoopSuccess"] = 95,
    ["Calendar_Permission_Tips"] = 95,
    ["DrawReward_NationalDayDrawConfirm"] = 95,
    ["UI_ScoringSystemExit"] = 95,
    ["UI_IllustrationScoringSystemLockTip"] = 95,
    ["UI_ScoringSystemLevelLockTip"] = 95,
    ["UI_ScoringSystemNotInputTip"] = 95,
    ["UI_ScoringSystemInputText"] = 95,
    ["TeamShow_GotoInviteCloseTip"] = 95,
    ["TeamShow_GotoCloseTip"] = 95,
    ["TeamShow_InviteCloseTip"] = 95,
    ["TeamShow_PlayerCloseTip"] = 95,
    ["Bag_Tips_ActionLoopCanNotSetting"] = 95,
    ["UI_Recharge_OverExchangeLimit"] = 95,
    ["Mall_CollectionTitieName"] = 95,
    ["System_LuckyStar_ActivityIsDone"] = 95,
    ["Bag_Favorite_Empty"] = 95,
    ["Bag_MyFavoriteTitle"] = 95,
    ["Bag_NormalTitle"] = 95,
    ["Bag_BackNormalTitle"] = 95,
    ["UI_Team_Invitetxt"] = 95,
    ["TeamShow_MatchNoPlayTip"] = 95,
    ["Recharge_LuckyFree_StatisticEndTime"] = 95,
    ["Recharge_LuckyFree_BtnWinners"] = 95,
    ["Recharge_LuckyFree_PrizeCD"] = 95,
    ["Recharge_LuckyFree_Prizeing"] = 95,
    ["Recharge_LuckyFree_PrizeResult"] = 95,
    ["Recharge_LuckyFree_RewardTips"] = 95,
    ["QRCode_ClubName"] = 95,
    ["QRCode_RoomName"] = 95,
    ["UI_NoFindPlayer"] = 95,
    ["Mall_Selling"] = 95,
    ["Mall_SaleTimeHM"] = 95,
    ["Mall_SaleTimeHMRange"] = 95,
    ["Mall_SaleTimeAfterDay"] = 95,
    ["ActivityLottery_CriticalTips"] = 95,
    ["DrawReward_MallViewNotInBuyTimeRange"] = 95,
    ["DrawReward_MallNextFreeTips"] = 95,
    ["DrawReward_NotOpenTip"] = 95,
    ["Bag_HaveOriginalColorItem"] = 95,
    ["UI_SETTING_ChangeNameConfirmStrContent"] = 95,
    ["UI_SETTING_ChangeNameConfirmBtnContent"] = 95,
    ["UI_Activity_WerewolfDart_GetCount"] = 95,
    ["UI_Activity_WerewolfDart_DrawOnce"] = 95,
    ["UI_Activity_WerewolfDart_Tips"] = 95,
    ["UILobbyChooseMatchTips"] = 95,
    ["UIWXLobbyChooseMatchTips"] = 95,
    ["UI_RankAchieve"] = 95,
    ["UI_RecruitItem_Title_StarWorld"] = 95,
    ["UI_RecruitItem_Title_RankModeSuffix"] = 95,
    ["UGC_PublishMap_HasNoDestination"] = 95,
    ["UGC_Cannot_Publish_NoAt_First_Layer"] = 96,
    ["Report_tab_Recommand"] = 96,
    ["Report_tab_Collect"] = 96,
    ["CaptureShadowFinalRewardCanGet"] = 96,
    ["CaptureShadowNextCaptureRewardShow"] = 96,
    ["CaptureShadowEndCaptureRewardShow"] = 96,
    ["CaptureShadowRewardTitle"] = 96,
    ["UI_RankTag_ArenaHeroRule"] = 96,
    ["UI_RankTag_ArenaHeroDesc"] = 96,
    ["UI_Rank_DownLoadTips"] = 96,
    ["NoCaptureRewardCanGet"] = 96,
    ["NewShadowAppear"] = 96,
    ["Activity_GoFishing_ShareDesc"] = 96,
    ["UI_Intimate_DoubleTips"] = 96,
    ["LFT_GoFishing"] = 96,
    ["UseChanceInPray"] = 96,
    ["UI_GreenHouse_RecordItemText"] = 96,
    ["UI_GreenHouseNew_RecordItemTextRecall"] = 96,
    ["UI_GreenHouseNew_RecordItemTextGift"] = 96,
    ["CaptureShadowClickedCoin"] = 96,
    ["UI_NoCanEnterRoom"] = 96,
    ["UI_GreenHouseNew_Result_Normal_1"] = 96,
    ["UI_GreenHouseNew_Result_Normal_2"] = 96,
    ["UI_GreenHouseNew_Result_Normal_3"] = 96,
    ["UI_GreenHouseNew_Result_Normal_1_Uid"] = 96,
    ["UI_GreenHouseNew_Result_Normal_2_Uid"] = 96,
    ["UI_GreenHouseNew_Result_Normal_3_Uid"] = 96,
    ["UI_GreenHouseNew_Result_Normal_Btn"] = 96,
    ["UI_GreenHouseNew_Result_New"] = 96,
    ["UI_GreenHouseNew_Result_New_Uid"] = 96,
    ["UI_GreenHouseNew_Result_New_Btn"] = 96,
    ["UI_GreenHouseNew_Result_Smurf"] = 96,
    ["UI_GreenHouseNew_Result_Smurf_Uid"] = 96,
    ["UI_GreenHouseNew_Result_Black"] = 96,
    ["UI_GreenHouseNew_Result_Already"] = 96,
    ["UI_GreenHouseNew_Result_Limit"] = 96,
    ["UI_GreenHouseNew_Result_End"] = 96,
    ["UI_GreenHouseNew_Result_Error"] = 96,
    ["UI_GreenHouseNew_Result_Same"] = 96,
    ["UI_GreenHouseNew_FarmVisitDisableTip"] = 96,
    ["UI_GreenHouseNew_RecallNotPlatTip"] = 96,
    ["UI_GreenHouseNew_RecallCDTip"] = 96,
    ["UI_Share_Gift_HideSender"] = 96,
    ["UI_Share_Gift_HideOwner"] = 96,
    ["UI_GreenHouseNew_GiftNoFarm"] = 96,
    ["Report_ModelName"] = 96,
    ["Lottery_BtnText_FreeAppend"] = 96,
    ["Lottery_BtnText_ProtectAppend"] = 96,
    ["Lottery_BtnText_EnableAppend"] = 96,
    ["Lottery_CollectConfirm"] = 96,
    ["Lottery_FreeConfirm"] = 96,
    ["Lottery_FreeAppendFail01"] = 96,
    ["Lottery_FreeAppendFail02"] = 96,
    ["Lottery_ProtectAppendFail"] = 96,
    ["Lottery_ProtectAppendTips"] = 96,
    ["Lobby_StrangerGrabLimit"] = 96,
    ["CDKEY_No_Auth"] = 96,
    ["CDKEY_No_InTime"] = 96,
    ["CDKEY_No_Valid"] = 96,
    ["CDKEY_Is_Used"] = 96,
    ["CDKEY_No_Exist"] = 96,
    ["CDKEY_Use_Limit"] = 96,
    ["CDKEY_Already_Used"] = 96,
    ["CDKEY_Failed"] = 96,
    ["CDKEY_Use_Suncess"] = 96,
    ["UI_UGC_MapVersion_Abort"] = 96,
    ["UI_UGC_MapVersion_PassButTips"] = 96,
    ["UGC_NPCEditor_InteractionInfoName"] = 96,
    ["Calender_RunMonth"] = 96,
    ["UGC_SKILL_CANNOT_LOOP_SKILL"] = 96,
    ["UGC_RoomList_GoodMarks"] = 96,
    ["UGC_RoomList_PassMarks"] = 96,
    ["TDM_FastRoomList_EditTextBoxTabName"] = 96,
    ["TDM_FastRoomList_CreateRoomTabName"] = 96,
    ["TDM_Room_SettingTabName"] = 96,
    ["TDM_Room_ChatTabName"] = 96,
    ["TDM_Room_RecruitTabName"] = 96,
    ["UI_Competition_InLevelStartHint"] = 96,
    ["UI_Competition_InLevelStartHint1"] = 96,
    ["UI_Competition_InLevelStartHint2"] = 96,
    ["UI_Competition_InLevelStartHint3"] = 96,
    ["UI_Competition_InLevelStartHint4"] = 96,
    ["UI_Competition_InLevelStartHint5"] = 96,
    ["UI_Competition_InLevelStartHint6"] = 96,
    ["UI_Competition_InLevelStartHint7"] = 96,
    ["UI_Competition_InLevelStartHint8"] = 96,
    ["UI_Competition_InLevelStartHint9"] = 96,
    ["UI_Competition_Main"] = 96,
    ["UI_Competition_Main1"] = 96,
    ["UI_Competition_Main2"] = 96,
    ["UI_Competition_Main3"] = 96,
    ["UI_Competition_Main4"] = 96,
    ["UI_Competition_Main5"] = 96,
    ["UI_Competition_Main6"] = 96,
    ["UI_Competition_Main7"] = 96,
    ["UI_Competition_Main8"] = 96,
    ["UI_Competition_Main9"] = 96,
    ["UI_Competition_Main10"] = 96,
    ["UI_Competition_Main11"] = 96,
    ["UI_Competition_Main12"] = 96,
    ["UI_Competition_Main13"] = 97,
    ["UI_Competition_Main14"] = 97,
    ["UI_Competition_Main15"] = 97,
    ["UI_Competition_Main16"] = 97,
    ["UI_Competition_Main17"] = 97,
    ["UI_Competition_Main18"] = 97,
    ["UI_Competition_Main19"] = 97,
    ["UI_Competition_Main20"] = 97,
    ["UI_Competition_Main21"] = 97,
    ["UI_Competition_Main22"] = 97,
    ["UI_Competition_Main23"] = 97,
    ["UI_Competition_Main24"] = 97,
    ["UI_Competition_Main25"] = 97,
    ["UI_Competition_Main26"] = 97,
    ["UI_Competition_Main27"] = 97,
    ["UI_Competition_Main28"] = 97,
    ["UI_Competition_Main29"] = 97,
    ["UI_Competition_Main30"] = 97,
    ["UI_Competition_Main31"] = 97,
    ["UI_Competition_Main32"] = 97,
    ["UI_Competition_Main33"] = 97,
    ["UI_Competition_Main34"] = 97,
    ["UI_Competition_Main35"] = 97,
    ["UI_Competition_Main36"] = 97,
    ["UI_Competition_Main37"] = 97,
    ["UI_Competition_Main38"] = 97,
    ["UI_Competition_Main39"] = 97,
    ["UI_Competition_Main40"] = 97,
    ["UI_Competition_Main41"] = 97,
    ["UI_Competition_Main42"] = 97,
    ["UI_Competition_Main43"] = 97,
    ["UI_Competition_Main44"] = 97,
    ["UI_Competition_Main45"] = 97,
    ["UI_Competition_Main46"] = 97,
    ["UI_Competition_Main47"] = 97,
    ["UI_Competition_Main48"] = 97,
    ["UI_Competition_Main49"] = 97,
    ["UI_Competition_Main50"] = 97,
    ["UI_Competition_Main51"] = 97,
    ["UI_Competition_Main52"] = 97,
    ["UI_Competition_InLevel_Reconnect"] = 97,
    ["UI_Competition_InLevel_Reconnect1"] = 97,
    ["UI_Competition_InLevel_Reconnect2"] = 97,
    ["UI_Competition_InLevelRank"] = 97,
    ["UI_Competition_InLevelRank1"] = 97,
    ["UI_Competition_InLevelRank2"] = 97,
    ["UI_Competition_InLevelRank3"] = 97,
    ["UI_Competition_InLevelRank4"] = 97,
    ["UI_Competition_InLevelRank5"] = 97,
    ["UI_Competition_InLevelRank6"] = 97,
    ["UI_Competition_InLevelFinalAccount"] = 97,
    ["UI_Competition_InLevelFinalAccount1"] = 97,
    ["UI_Competition_InLevelFinalAccount2"] = 97,
    ["UI_Competition_InLevelFinalAccount3"] = 97,
    ["UI_Competition_InLevelFinalAccount4"] = 97,
    ["UI_Competition_InLevelFinalAccount5"] = 97,
    ["UI_Competition_InLevelFinalAccount6"] = 97,
    ["UI_Competition_InLevelFinalAccount7"] = 97,
    ["UI_Competition_InLevelFinalAccount8"] = 97,
    ["UI_Competition_InLevelFinalAccount9"] = 97,
    ["UI_Competition_InLevelFinalAccount10"] = 97,
    ["UI_Competition_InLevelFinalAccount11"] = 97,
    ["UI_Competition_InLevelFinalAccount12"] = 97,
    ["UI_Competition_InLevelFinalAccount13"] = 97,
    ["UI_Competition_InLevelFinalAccount14"] = 97,
    ["UI_Competition_RoomInfo"] = 97,
    ["UI_Competition_RoomInfo1"] = 97,
    ["UI_Competition_RoomInfo2"] = 97,
    ["UI_Competition_RoomInfo3"] = 97,
    ["UI_Competition_RoomInfo4"] = 97,
    ["UI_Competition_RoomInfo5"] = 97,
    ["UI_Competition_RoomInfo6"] = 97,
    ["UI_Competition_RoomInfo7"] = 97,
    ["UI_Competition_RoomInfo8"] = 97,
    ["UI_Competition_RoomInfo9"] = 97,
    ["UI_Competition_RoomInfo10"] = 97,
    ["UI_Competition_Integral_Award"] = 97,
    ["UI_Competition_Integral_Award1"] = 97,
    ["UI_Competition_Integral_Award2"] = 97,
    ["UI_Competition_GroupPanel"] = 97,
    ["UI_Competition_EliminationListView"] = 97,
    ["UI_Competition_EliminationListView1"] = 97,
    ["UI_Competition_EliminationListView2"] = 97,
    ["UI_Competition_EliminationListView3"] = 97,
    ["UI_Competition_Integral"] = 97,
    ["UI_Competition_Integral1"] = 97,
    ["UI_Competition_Integral2"] = 97,
    ["UI_Competition_Integral3"] = 97,
    ["UI_Competition_Integral4"] = 97,
    ["UI_Competition_InLevelStageHint"] = 97,
    ["UI_Competition_InLevelStageHint1"] = 97,
    ["UI_Competition_InLevelStageHint2"] = 97,
    ["UI_Competition_InLevelStageHint3"] = 97,
    ["UI_Competition_ExplainMain_title1"] = 97,
    ["UI_Competition_ExplainMain_title2"] = 97,
    ["UI_Competition_ExplainMain_title3"] = 97,
    ["UI_Competition_ExplainMain_title4"] = 97,
    ["UI_Competition_ExplainMain_title5"] = 97,
    ["UI_Competition_ExplainMain_title6"] = 97,
    ["UI_Competition_ExplainMain_title7"] = 97,
    ["UI_Competition_ExplainMain_content1"] = 98,
    ["UI_Competition_ExplainMain_content2"] = 98,
    ["UI_Competition_ExplainMain_content3"] = 98,
    ["UI_Competition_ExplainMain_content4"] = 98,
    ["UI_Competition_ExplainMain_content5"] = 98,
    ["UI_Competition_ExplainMain_content6"] = 98,
    ["UI_Competition_ExplainMain_content7"] = 98,
    ["UI_Competition_ExplainMain_content8"] = 98,
    ["UI_Competition_ExplainMain_content9"] = 98,
    ["UI_Competition_ExplainMain_content10"] = 98,
    ["UI_Competition_ExplainMain_content11"] = 98,
    ["UI_Competition_ExplainMain_content12"] = 98,
    ["UI_Competition_ExplainMain_content13"] = 98,
    ["UI_Competition_ExplainMain_content14"] = 98,
    ["UI_Competition_ExplainMain_content15"] = 98,
    ["UI_Competition_ExplainMain_content16"] = 98,
    ["UI_Competition_ExplainMain_content17"] = 98,
    ["UI_Competition_ExplainMain_content18"] = 98,
    ["UI_Competition_ExplainMain_content19"] = 98,
    ["UI_Competition_ExplainMain_content20"] = 98,
    ["UI_Competition_ExplainMain_content21"] = 98,
    ["NR3E_EndReason_103_Tips"] = 98,
    ["NR3E_EndReason"] = 98,
    ["MeetingActionName9"] = 98,
    ["UI_RedPacket_ActivityName"] = 98,
    ["UI_Activity_OpenPacket_OneParam"] = 98,
    ["UI_BtnName_AllCash"] = 98,
    ["UI_RedPacket_ActivityDes"] = 98,
    ["UI_RedPacket_CurrTimes"] = 98,
    ["UI_RedPacket_CurrTimesDes"] = 98,
    ["UI_RedPacket_NextTimesDes"] = 98,
    ["UI_RedPacket_TimesTh"] = 98,
    ["UI_RedPacket_Record"] = 98,
    ["UI_RedPacket_OnGoing"] = 98,
    ["UI_RedPacket_Begin"] = 98,
    ["UI_RedPacket_History"] = 98,
    ["UI_RedPacket_Cash"] = 98,
    ["UI_RedPacket_CurrStage_TwoParam"] = 98,
    ["UI_RedPacket_GetReward_OneParam"] = 98,
    ["UI_RedPacket_Season_Left"] = 98,
    ["Rank_ResourceDownLoadTips"] = 98,
    ["UI_Setting_PopLink_Desc"] = 98,
    ["SettingTip_WeChatGameDynamic"] = 98,
    ["Login_Overseas_Agreement"] = 98,
    ["TimeUtils_YMD"] = 98,
    ["Activity_Exchange_PlayerReturn_ConfirmGift"] = 98,
    ["Activity_Exchange_PlayerReturn_CurGiftNum"] = 98,
    ["Common_Quit"] = 98,
    ["Common_Reconsider"] = 98,
    ["Common_Revocation"] = 98,
    ["Common_OverseaLoginSuccess"] = 98,
    ["Common_ActivityNotOpen"] = 98,
    ["Common_Sec_NoParam"] = 98,
    ["Common_GetOver"] = 98,
    ["Common_WaitGet"] = 98,
    ["Net_CommunityLevelTypeError"] = 98,
    ["UGC_ErrorMsg_CanNotAsTemplateInGroup"] = 98,
    ["Common_CopyToClipboard"] = 98,
    ["Permission_LocalNotify_Failed"] = 98,
    ["UGC_Resource_Delete_Confirm"] = 98,
    ["UGC_Shop_Goods_NotExist"] = 98,
    ["UI_ConvenedRecordItemInviteOnlineFaile_NotPlatFriend"] = 98,
    ["UI_LuckyWeekendValueText"] = 98,
    ["CustomRoom_PinJoinFailed"] = 98,
    ["UGC_OMD_Crystal_NumCheck_Max_Tips"] = 98,
    ["Tricycle_No_Place_For_Tricycle"] = 98,
    ["Tricycle_Spawn_Failed"] = 98,
    ["Tricycle_Too_Many"] = 98,
    ["GiftCard_Thanks_Tips"] = 98,
    ["UI_Nongchangyanglvzhi_Noticket_tips"] = 98,
    ["Calendar_Not_Support_Platform"] = 98,
    ["Login_SystemNotice_Title"] = 98,
    ["Login_SystemNotice_Content"] = 98,
    ["FriendShipFire_GetFire"] = 98,
    ["FriendShipFire_FriendList"] = 98,
    ["Share_PC_NotSupport_Tips"] = 98,
    ["PC_NotSupport_Recharget_Tips"] = 98,
    ["UI_UnlockAdvanceTab_Text"] = 98,
    ["UI_Season_UnlockAdvancedPass_Txt"] = 98,
    ["UI_Season_UpgradeReceive_Txt"] = 98,
    ["UI_Season_LotteryReceive_Txt"] = 98,
    ["UI_Season_LotteryReceiveHigh_Txt"] = 98,
    ["UI_Season_Redeemablerewards_Txt"] = 98,
    ["UI_Season_EarnCrownCoinsPass_Txt"] = 98,
    ["UI_Season_RankedMatch_Txt"] = 98,
    ["CustomRoom_ExchangeSeatFail_WaitCd"] = 98,
    ["UI_WerewolfGonzoDetective_ResetInfo"] = 98,
    ["Lottery_OwnSelect_Check"] = 98,
    ["Lottery_OwnSelect_Replace"] = 98,
    ["Lottery_OwnSelect_rule"] = 98,
    ["FriendShipFire_ReturnList"] = 98,
    ["CaptureShadowTipShaodwHasBuffer"] = 98,
    ["QQAuthority_Title"] = 98,
    ["UI_GreenHouseNew_LowVerNotSupport"] = 98,
    ["UI_PC_PasswordRoom_Not_Support_Hint"] = 98,
    ["UI_ConanActivityClose"] = 98,
    ["UI_WishList_CantAddWishMaxTips"] = 98,
    ["UI_WishList_FollowSuccess"] = 98,
    ["UI_WishList_UnfollowSucces"] = 98,
    ["UI_WishList_AddWishSuccess"] = 98,
    ["UI_WishList_RemoveWishTips"] = 99,
    ["UI_WishList_RemoveWishSuccess"] = 99,
    ["UI_WishList_CantGiftWish"] = 99,
    ["UI_RankTag_AchieveRule"] = 99,
    ["UI_RankTag_AchieveDesc"] = 99,
    ["Player_FaceOrnament"] = 99,
    ["Player_HeadWear"] = 99,
    ["Player_BackOrnament"] = 99,
    ["Player_HandOrnament"] = 99,
    ["Player_Surroundings"] = 103,
    ["NewChat_SurpassFriend"] = 99,
    ["RedEnvelop_NoThanksToSelf"] = 99,
    ["UI_Season_RankedFeatureMatch_Txt"] = 99,
    ["MaxNum_OneTime_Operation"] = 99,
    ["Lottery_LoversSubView_Gift"] = 99,
    ["AppearanceFashion_RoadLevel_Upgrade"] = 99,
    ["UI_SurpassFriend_ShowedRecord"] = 99,
    ["UI_Share_PreviewCardName"] = 99,
    ["UI_Share_PreviewCardProgress"] = 99,
    ["Team_MemberCountOverMaxTip"] = 99,
    ["AppearanceFashion_RoadLevel_Upgrade_1"] = 99,
    ["UI_WishList_GiftTips"] = 99,
    ["UI_Birthday_Set"] = 99,
    ["UI_Birthday_Record"] = 99,
    ["UI_Birthday_AllPlayer"] = 99,
    ["UI_Birthday_AllFriend"] = 99,
    ["UI_Birthday_IntimateFriend"] = 99,
    ["UI_Birthday_OnlySelf"] = 99,
    ["UI_Birthday_SetDayConfirm"] = 99,
    ["UI_Birthday_SetDayTips"] = 99,
    ["UI_Birthday_FirstTips"] = 99,
    ["UI_Birthday_SendCard"] = 99,
    ["UI_Birthday_MyHoney"] = 99,
    ["UI_Birthday_Yours"] = 99,
    ["UI_Birthday_SendTips"] = 99,
    ["UI_Birthday_AlreadySend"] = 99,
    ["Cup_DailyTaskHint1"] = 99,
    ["Cup_DailyTaskHint2"] = 99,
    ["UI_CardSystem_SendGiveSuccess"] = 99,
    ["UI_CardSystem_SendAskSuccess"] = 99,
    ["UI_CardSystem_SendExchangeSuccess"] = 99,
    ["CustomRoom_InvalidPlatformTips"] = 99,
    ["NewChat_ChatTypeForbid"] = 99,
    ["RedEnvelop_ThankedTip"] = 99,
    ["UI_Birthday_SendSucceedNow"] = 99,
    ["UI_Birthday_SendSucceedLatter"] = 99,
    ["UI_Birthday_SendSucceedEnd"] = 99,
    ["UI_Birthday_ChangeSucceed"] = 99,
    ["UI_Birthday_ChangeTip"] = 99,
    ["UI_Birthday_SendCardTip"] = 99,
    ["UI_Birthday_TimeEnd"] = 99,
    ["UI_Birthday_SendCardTimeEnd"] = 99,
    ["UI_Birthday_SendCardMaxNum"] = 99,
    ["UI_Birthday_NotFriend"] = 99,
    ["UI_Birthday_NotShow"] = 99,
    ["UI_Birthday_NotInTime"] = 99,
    ["UI_Birthday_MoreTime"] = 99,
    ["UI_Birthday_RemoveCard"] = 99,
    ["JumpToast_common"] = 99,
    ["UI_Birthday_ActivityContent"] = 99,
    ["AppearanceFashion_RoadLevel_Upgrade_2"] = 99,
    ["UI_LoginReminders_Success"] = 99,
    ["UI_LoginReminders_Fail"] = 99,
    ["Task_ActivityEnded_InPublicity"] = 99,
    ["UI_Dance_Start"] = 99,
    ["UI_Dance_End"] = 99,
    ["UI_PlayerInfo_ChangeInvite"] = 99,
    ["UI_Friend_ChangeIntimacy"] = 99,
    ["UI_Lightning_LevelInfo"] = 99,
    ["UI_Lightning_EventList"] = 99,
    ["UI_Lightning_Blessing"] = 99,
    ["UI_WishList_SelfLevelLimit_Tips"] = 99,
    ["UI_WishList_OtherLevelLimit_Tips"] = 99,
    ["UI_WishList_GiftLevelLimit_Tips"] = 99,
    ["MatchPreparation_BPLevel"] = 99,
    ["UI_FarmBlessingTeam_SingleTask"] = 99,
    ["UI_FarmBlessingTeam_TeamTask"] = 99,
    ["UI_FarmBlessingTeam_RecommendList"] = 99,
    ["UI_FarmBlessingTeam_FriendList"] = 99,
    ["CommonModeJump"] = 99,
    ["AppearanceFashion_EmptyBag"] = 99,
    ["AppearanceFashion_UnExist"] = 99,
    ["HotkeyChangeKeyStroke"] = 99,
    ["HotkeyConflictModifications"] = 99,
    ["HotkeylsOccupied"] = 99,
    ["HotkeyNoMoreReminders"] = 99,
    ["WXCollectAddFailed"] = 99,
    ["WXCollectAddSuccess"] = 99,
    ["UI_ReSign_Tips_Content"] = 99,
    ["UI_ReSign_Tips_Title"] = 99,
    ["MGP_CannotChangeModeInMatch"] = 99,
    ["UI_Activity_WerewolfShowdown_RewardTips"] = 99,
    ["UI_Activity_WerewolfShowdown_SelectTips"] = 99,
    ["vaCollectErrorLowQQVersion"] = 99,
    ["knockoutTips1"] = 99,
    ["knockoutTips2"] = 99,
    ["knockoutTips3"] = 99,
    ["UI_Lottery_NewYearCourtyard_MainView_1"] = 99,
    ["InValidTimeToStartGame"] = 99,
    ["UI_FarmBlessingTeam_Tips1"] = 99,
    ["UI_FarmBlessingTeam_Tips2"] = 99,
    ["UI_Lottery_NewYearCourtyard_MainView_2"] = 100,
    ["UI_Birthday_CardItemDesc"] = 100,
    ["Team_PST_Offline2"] = 100,
    ["Team_PST_OfflineStartGameTip1"] = 100,
    ["Team_PST_OfflineStartGameTip2"] = 100,
    ["UI_Birthday_SelectSendNow"] = 100,
    ["UI_Birthday_SelectSendBefore"] = 100,
    ["UI_FarmBlessingTeam_ChatDesc"] = 100,
    ["UI_TeamInviteLaterDispose"] = 100,
    ["UI_TeamInviteGive"] = 100,
    ["UI_TeamInviteExchange"] = 100,
    ["UI_Birthday_MakeCardTips"] = 100,
    ["UI_Birthday_BirthdayCardThanks"] = 100,
    ["UI_Birthday_ChangeBirthdayHelp"] = 100,
    ["UI_Birthday_ChatSendCard"] = 100,
    ["NewChat_AICommentary"] = 100,
    ["UI_PlayerInfo_ChangeInviteTip"] = 100,
    ["ShareGift_Cut_Button_Title"] = 100,
    ["ShareGift_Record_Left_Btn1"] = 100,
    ["ShareGift_Record_Left_Btn2"] = 100,
    ["ShareGift_Record_Top_Des_Share"] = 100,
    ["ShareGift_Record_Top_Des_Get"] = 100,
    ["ShareGift_Record_Content_Item_Time"] = 100,
    ["ShareGift_Record_Content_Item_Num"] = 100,
    ["ShareGift_Record_Content_Item_No"] = 100,
    ["ShareGift_Record_Content_Btn_Name"] = 100,
    ["ShareGift_Record_Content_Item_Get_Time"] = 100,
    ["ShareGift_Record_Content_Time_No"] = 100,
    ["ShareGift_Record_Title"] = 100,
    ["ShareGift_Get_Title"] = 100,
    ["UI_ClearingAwards_Back"] = 100,
    ["UI_FashionFund_BuyTips"] = 100,
    ["ShareGift_Time_No"] = 100,
    ["ShareGift_Item_No"] = 100,
    ["ShareGift_Item_Got"] = 100,
    ["ShareGift_Player_No"] = 100,
    ["UI_Birthday_ComeFrom"] = 100,
    ["ShareGift_Record_Top_Des_Share0"] = 100,
    ["ShareGift_Record_Top_Des_Share1"] = 100,
    ["ShareGift_Record_Top_Des_Share2"] = 100,
    ["ShareGift_Record_Top_Des_Share3"] = 100,
    ["ShareGift_Record_Top_Des_Share4"] = 100,
    ["ShareGift_Record_Top_Des_Share5"] = 100,
    ["ShareGift_Record_Top_Des_Share6"] = 100,
    ["ShareGift_Record_Top_Des_Get0"] = 100,
    ["ShareGift_Record_Top_Des_Get1"] = 100,
    ["ShareGift_Record_Top_Des_Get2"] = 100,
    ["ShareGift_Record_Top_Des_Get3"] = 100,
    ["ShareGift_Record_Top_Des_Get4"] = 100,
    ["ShareGift_Record_Top_Des_Get5"] = 100,
    ["ShareGift_Record_Top_Des_Get6"] = 100,
    ["Team_PST_HUD"] = 100,
    ["UI_FarmBlessingTeam_ShareChatDesc"] = 100,
    ["Team_WXMemberReminder"] = 100,
    ["Team_QQMemberReminder"] = 100,
    ["UI_Birthday_ChangeBirthdayVis"] = 100,
    ["UI_Birthday_ChatSendCardMy"] = 100,
    ["UI_Birthday_CanNotLook"] = 100,
    ["UI_Birthday_SendCardNow"] = 100,
    ["ShareGift_NewChat_ShareTitle"] = 100,
    ["ShareGift_Click_Share_No_Num_Tip1"] = 100,
    ["ShareGift_Click_Share_No_Num_Tip2"] = 100,
    ["ShareGift_Click_Share_No_Num_Tip3"] = 100,
    ["ShareGift_Click_get_No_Num_Tip1"] = 100,
    ["ShareGift_Click_get_No_Num_Tip2"] = 100,
    ["ShareGift_Click_get_No_Num_Tip3"] = 100,
    ["ShareGift_Record_No_Share_Des"] = 100,
    ["ShareGift_Record_No_Get_Des"] = 100,
    ["UI_Collection_NotificationPopup"] = 100,
    ["UI_Collection_ReceiveCount"] = 100,
    ["UI_Collection_MoreTip"] = 100,
    ["UI_Collection_CurrentTip"] = 100,
    ["UI_Collection_TotalTip"] = 100,
    ["ShareGift_Record_Rob_No"] = 100,
    ["ShareGift_Record_Rob_Minutes_No"] = 100,
    ["ShareGift_Record_Rob_Second_No"] = 100,
    ["UI_Birthday_CardDelete"] = 100,
    ["NewChat_ShareGift_ShowFormat"] = 100,
    ["Share_Gift_Record_Get_Tag"] = 100,
    ["ShareGift_UseItemFailed"] = 100,
    ["UI_Birthday_CardNotSend"] = 100,
    ["Share_Gift_Main_ShareBtn_Msg"] = 100,
    ["UI_Activity_WerewolfTrap_GetCount"] = 100,
    ["UI_NewChat_ShareGift_Success"] = 100,
    ["ShareGift_Record_Top_Des_Share7"] = 100,
    ["ShareGift_Record_Top_Des_Share8"] = 100,
    ["ShareGift_Record_Top_Des_Get7"] = 100,
    ["ShareGift_Record_Top_Des_Get8"] = 100,
    ["UI_Birthday_MailContent"] = 100,
    ["ShareGift_Click_Share_No_Num_Tip4"] = 100,
    ["ShareGift_Click_get_No_Num_Tip4"] = 100,
    ["UI_Activity_NewYearParty2025_Tips_Fix"] = 100,
    ["Friend_Info_EnterFarm_NotFriend"] = 100,
    ["Friend_Info_EnterFarm_FriendNoFarm"] = 100,
    ["Friend_Info_EnterFarm_YouNoFarm"] = 100,
    ["Friend_Info_EnterFarm_YouFriendBlack"] = 100,
    ["UI_Birthday_SendBlessingSucces"] = 100,
    ["UI_FarmBlessingTeam_NoFarm"] = 100,
    ["UI_ChooseCharacter_ChooseNoSelectTip"] = 100,
    ["UI_NewChat_JoinInterestChannel_First"] = 100,
    ["Accelerating_prompt_use"] = 101,
    ["Accelerating_prompt_lack"] = 101,
    ["UP_ModeSeleclNoMatchInRank"] = 101,
    ["UP_ModeSeleclNoMatchInCasual"] = 101,
    ["Lobby_NoDisturbState_MsgBoxTips"] = 101,
    ["Lobby_NoDisturbState_BubbleTips"] = 101,
    ["Lobby_NoDisturbState_SelfInNoDisturbTip"] = 101,
    ["Lobby_NoDisturbState_TargetInNoDisturbTip"] = 101,
    ["Team_NotInviteOfflineMemberTip"] = 101,
    ["UI_ActivityLottery_BaseView_0"] = 101,
    ["UI_GiftShare_RewardCountLimit"] = 101,
    ["UI_UPTip_OnSideChanged"] = 101,
    ["Cup_DailyTaskHint3"] = 101,
    ["UI_GameType_One"] = 101,
    ["UI_GameType_Two"] = 101,
    ["UI_GameType_Four"] = 101,
    ["TeamMatchSelectTips"] = 101,
    ["Common_National"] = 101,
    ["ThreeRoundTestTip"] = 101,
    ["TemporaryMailboxNotice"] = 101,
    ["PhotoAlbumFullTips1"] = 101,
    ["PhotoAlbumFullTips2"] = 101,
    ["PhotoAlbumFullTips3"] = 101,
    ["PhotoAlbumFullTips4"] = 101,
    ["PhotoAlbumSaveTips"] = 101,
    ["PhotoAlbumTaskTips1"] = 101,
    ["PhotoAlbumTaskTips2"] = 101,
    ["PhotoAlbumTaskTips3"] = 101,
    ["PhotoAlbumEditExitTips"] = 101,
    ["Common_SelfRecord"] = 101,
    ["Leaderboard_Tips_Title_StarWorld_Mix"] = 101,
    ["Leaderboard_Tips_Desc_StarWorld_Mix"] = 101,
    ["UI_RankTag_NR3E3_FinalPoints_Rule"] = 101,
    ["UI_RankTag_NR3E3_FinalPoints_Desc"] = 101,
    ["UI_FinishBreakRecord_AreaRank"] = 101,
    ["UI_FinishBreakRecord_CountryRank"] = 101,
    ["UI_FinishBreakRecord_AreaTitle"] = 101,
    ["UI_FinishBreakRecord_CountryTitle"] = 101,
    ["UI_FinishBreakRecord_CurrentTimeFormat"] = 101,
    ["UI_FinishBreakRecord_SingleModeTitle"] = 101,
    ["UI_Bag_GiveGiftLevelTips"] = 101,
    ["PhotoAlbumHideTips1"] = 101,
    ["PhotoAlbumHideTips2"] = 101,
    ["UI_RankTag_ChaseFinalPointsRule"] = 101,
    ["UI_RankTag_ChaseFinalPointsDesc"] = 101,
    ["UI_RightOrUp_Newbie_Today"] = 101,
    ["UI_RightOrUp_Newbie_Tomorrow"] = 101,
    ["UI_RightOrUp_Newbie_RewardTips"] = 101,
    ["Album_Space_Not_Enough_Multiple"] = 101,
    ["Album_Space_Not_Enough"] = 101,
    ["Album_Save_Fail"] = 101,
    ["Album_Save_Fail_Temporary"] = 101,
    ["Album_TemporaryEmpty_Tips"] = 101,
    ["Album_Friends_Notice"] = 101,
    ["Album_Noticed"] = 101,
    ["Album_Notice_Others"] = 101,
    ["UI_Lottery_ConfirmText"] = 101,
    ["UI_Lottery_Continue"] = 101,
    ["UI_Lottery_Choose"] = 101,
    ["UI_FarmPuzzle_ConfirmTips"] = 101,
    ["UI_FarmPuzzle_GoGetCoin"] = 101,
    ["Team_ConfirmCancelGameTip"] = 101,
    ["Team_ConfirmCancelGameOK"] = 101,
    ["Team_ConfirmCancelGameNO"] = 101,
    ["Album_Remind_ViewPhoto"] = 101,
    ["Album_Remind_OverLimit"] = 101,
    ["Album_Photo_DeleteTips"] = 101,
    ["OneRoundTestTip"] = 101,
    ["UI_ModelSelect_UnFold"] = 101,
    ["UI_ModelSelect_Fold"] = 101,
    ["LFT_UgcCreatorPage"] = 101,
    ["Video_Not_Open_Time"] = 101,
    ["Use_Prop_Item_Title_PackageType_Common"] = 101,
    ["Use_Prop_Item_Title_PackageType_Random"] = 101,
    ["Use_Prop_Item_Title_PackageType_Pick"] = 101,
    ["Team_NotStartReasonLeaderTip"] = 101,
    ["Team_NotStartReasonMemberTip"] = 101,
    ["GiftSharing_GetRecord_Button_Name"] = 101,
    ["Album_VerticalAlign_Top"] = 101,
    ["Album_VerticalAlign_Center"] = 101,
    ["Album_VerticalAlign_Bottom"] = 101,
    ["Album_HorizontalAlign_Left"] = 101,
    ["Album_HorizontalAlign_Center"] = 101,
    ["Album_HorizontalAlign_Right"] = 101,
    ["Album_EditSaveSuccessTips"] = 101,
    ["UI_ChangeMoodState"] = 101,
    ["UI_DoubleFormation_Online"] = 101,
    ["UI_DoubleFormation_Offline"] = 101,
    ["UI_DoubleFormation_ShareTip"] = 101,
    ["UI_DoubleFormation_MakeTip"] = 101,
    ["UI_DoubleFormation_ProgressName"] = 101,
    ["UI_DoubleFormation_TeamFull"] = 101,
    ["UI_DoubleFormation_JoinTeam"] = 101,
    ["UI_DoubleFormation_JoinTeamTip"] = 101,
    ["NRE3Activity_In_ShopTitle"] = 101,
    ["NRE3Activity_In_ShopUpTitle"] = 101,
    ["Mail_Askfor_Activityclosed_Insufficient"] = 101,
    ["Mail_Askfor_Commodity_Unable"] = 101,
    ["Lobby_NoDisturbState_BubbleTips_PassiveInteract"] = 101,
    ["Cup_Bg_Title_Daily"] = 101,
    ["Cup_Bg_Title_Total"] = 102,
    ["UI_GiftSharing_Details_GiftBtns_Title"] = 102,
    ["UI_Gift_dress"] = 102,
    ["shareHint_1"] = 102,
    ["Cup_DailyTaskHint4"] = 102,
    ["UI_MonthlyChallenge_Start"] = 102,
    ["UI_MonthlyChallenge_Complete"] = 102,
    ["UI_MonthlyChallenge_LackOfTime"] = 102,
    ["UI_MonthlyChallenge_Confirm"] = 102,
    ["UI_MonthlyChallenge_Cancel"] = 102,
    ["UI_MonthlyChallenge_Title"] = 102,
    ["UI_MonthlyChallenge_WeeklyTips"] = 102,
    ["UI_MonthlyChallenge_WeekendTips"] = 102,
    ["UI_MonthlyChallenge_WeeklyTimeTips"] = 102,
    ["UI_MonthlyChallenge_WeekendTimeTips"] = 102,
    ["UI_BattlePass_UnlockAdvanced"] = 102,
    ["UI_BattlePass_UnlockDeluxe"] = 102,
    ["Cup_Bg_Title_NormalTaskName"] = 102,
    ["NewChat_MusicConcertPositionShare"] = 102,
    ["NewChat_MusicConcertPositionShareNoSender"] = 102,
    ["TIPS_MOVECANTINTERRUPTSOLOACTION"] = 102,
    ["UI_GameReturnIsClose"] = 102,
    ["Album_PlayMode_Name"] = 102,
    ["Album_PlayMode_StarWorld"] = 102,
    ["Album_PlayMode_ParkHome"] = 102,
    ["Album_PlayMode_StarParty"] = 102,
    ["Album_PlayMode_Tips"] = 102,
    ["Album_StarWorld_Tips"] = 102,
    ["Album_ParkHome_Tips"] = 102,
    ["Album_StarParty_Tips"] = 102,
    ["RecentPlay"] = 102,
    ["Activity_OnlyShowInFarmScene"] = 102,
    ["Team_InviteJoinUGCRoomFromLink"] = 102,
    ["Team_InviteJoinUGCTeamFromLink"] = 102,
    ["friend_normal_only_intimaty"] = 102,
    ["friend_multiplayer_only_intimaty"] = 102,
    ["friend_normal_only_coin"] = 102,
    ["friend_multiplayer_only_coin"] = 102,
    ["photo_setting_item_block"] = 102,
    ["photo_setting_invite_block"] = 102,
    ["SettingTip_ShowKeyOnButtonTip"] = 102,
    ["SettingTip_ShowKeyOnButtonShortcutTip"] = 102,
    ["Team_PST_Cook"] = 102,
    ["VoiceKeyWords_VoiceSendTooShort"] = 102,
    ["VoiceKeyWords_VoiceSendFailed"] = 102,
    ["VoiceKeyWords_EmojiShowSuccess"] = 102,
    ["VoiceKeyWords_Action1PSendSuccess"] = 102,
    ["VoiceKeyWords_Action2PSendFailed"] = 102,
    ["VoiceKeyWords_DoNotHasItem"] = 102,
    ["VoiceKeyWords_Emoji"] = 102,
    ["VoiceKeyWords_Action"] = 102,
    ["VoiceKeyWords_MatchFailed_ForService"] = 102,
    ["VoiceKeyWords_MatchFailed"] = 102,
    ["VoiceKeyWords_Title"] = 102,
    ["VoiceKeyWords_Content"] = 102,
    ["Team_PST_Photo"] = 102,
    ["UI_InflateHongBao_ReceiveTip1"] = 102,
    ["UI_InflateHongBao_ReceiveTip2"] = 102,
    ["UI_InflateHongBao_Insufficient"] = 102,
    ["UI_InflateHongBao_AvailableCount"] = 102,
    ["UI_InflateHongBao_UnlockCount"] = 102,
    ["Cup_DailyTaskHint5"] = 102,
    ["WXGame_NewHud_TaskItemTitle"] = 102,
    ["UI_PlayerInfo_Title"] = 102,
    ["UI_Artist_RewardPopup_TimeText"] = 102,
    ["UI_Artist_RewardPopup_OrginPriceText"] = 102,
    ["UI_Lottery_Artist_HaveCountText"] = 102,
    ["UI_Lottery_Artist_GuaranteeItemText"] = 102,
    ["UI_Artist_RewardItem_Tips"] = 102,
    ["UI_Lottery_Artist_ConfimText"] = 102,
    ["UI_Lottery_Artist_CancelText"] = 102,
    ["UI_Lottery_Artist_StrContentText"] = 102,
    ["UI_Player_BattlePassUnlocked"] = 102,
    ["VoiceKeyWords_StepTipsContent"] = 102,
    ["BattlePass_WereWolfNotUnlocked"] = 102,
    ["UI_SimpleGiftText2"] = 102,
    ["PakDetail_UnDownloadTips"] = 102,
    ["UI_PlayerInfo_QualifyHistory"] = 102,
    ["UI_PlayerInfo_QualifyCur"] = 102,
    ["UI_CannotFind_MySelf"] = 102,
    ["UI_ModelSelect_ScrollUpTips"] = 102,
    ["UI_ModelSelect_ScrollBottomTips"] = 102,
    ["TR_AnyMatchDes"] = 102,
    ["SP_LeaveTeamTip"] = 102,
    ["SP_LeaveRoomTip"] = 102,
    ["SettingTip_Invisible"] = 102,
    ["UI_BeBlockedToBanFriend"] = 102,
    ["UI_IsBanFriend"] = 102,
    ["Activity_LightningMatch_Vote_Content"] = 102,
    ["Activity_LightningMatch_Vote_Success"] = 102,
    ["Activity_LightningMatch_Vote_NotEnoughTips"] = 102,
    ["VIP_Photo_Up"] = 102,
    ["ModelSelect_RecommendRecentTip"] = 102,
    ["UI_Vehicle_Information"] = 102,
    ["UI_Vehicle_Accessories"] = 102,
    ["UI_Vehicle_Save_Accessories"] = 102,
    ["UI_Vehicle_LevelUp_Accessories"] = 102,
    ["UI_Vehicle_Skill_Unlock"] = 102,
    ["UPGameSetting_HideFashionScore"] = 102,
    ["CommodityBuyCondition1"] = 102,
    ["CommodityBuyCondition2"] = 103,
    ["UI_Season_EarnIntimateCoinsPass_Txt"] = 103,
    ["UI_Season_EarnIntimateLevelPass_Txt"] = 103,
    ["UI_MatchPakDetail_DownloadTip"] = 103,
    ["UI_MatchPakDetail_LeftDownloadTime"] = 103,
    ["CallUpActivity_TabTitle_Invite"] = 103,
    ["CallUpActivity_TabTitle_Login"] = 103,
    ["CallUpActivity_ReturnTabName_New"] = 103,
    ["CallUpActivity_ReturnTabName_Return"] = 103,
    ["CallUpActivity_TabTitle_ReturnPlayer"] = 103,
    ["CallUpActivity_TabTitle_NewPlayer"] = 103,
    ["CallUpActivity_SubTitle_Invite"] = 103,
    ["CallUpActivity_SubTitle_Login"] = 103,
    ["CallUpActivity_TabTitle_LoginEmpty"] = 103,
    ["UI_MatchPakDetail_DownloadPauseTip"] = 103,
    ["UI_MatchPakDetail_DownloadWaitTip"] = 103,
    ["UI_ReturnBuff_CountDownShow"] = 103,
    ["UI_Activity_MatchReturn_UPNextReward"] = 103,
    ["UI_Activity_MatchReturn_UPLeaveMsg_NoRewardTip"] = 103,
    ["UI_Activity_MatchReturn_UPLeaveMsg_RewardGuide"] = 103,
    ["UI_Activity_MatchReturn_UPLeaveMsg_LeftBtn"] = 103,
    ["UI_Activity_MatchReturn_UPLeaveMsg_RightBtn"] = 103,
    ["UI_UPComponent_Buff_CountDownText"] = 103,
    ["Team_PST_Rich"] = 103,
    ["Rank_Rich"] = 103,
    ["Rank_Rich_Desc"] = 103,
    ["Pak_DownLoadTimeTooLong_Bar"] = 103,
    ["Pak_DownloadTimeTooLong_Text"] = 103,
    ["UI_ActivityInstance_Linear04_SubView"] = 103,
    ["TaskProgressTip_MaxLimit"] = 103,
    ["ItaBag_BadgeNotEnoughTip"] = 103,
    ["UI_MatchPop_Wait"] = 103,
    ["UI_MatchPop_Confirm"] = 103,
    ["UI_MatchPop_Content"] = 103,
    ["UI_IsShowNext_Dialogue"] = 103,
    ["UI_IsDownloading_Season"] = 103,
    ["Lobby_JumpToAnotherLobby_MsgBox_Content"] = 103,
    ["ColorFont_BaseShowText"] = 103,
    ["Lottery_HitOnNextTip"] = 103,
    ["Lottery_HitOnNextSelect"] = 103,
    ["Lottery_HitOnNextConfirmation"] = 103,
    ["JumpPage_ChaseRoleViews_TeamStatusFailed"] = 103,
    ["MatchCheckLowVersionTips"] = 103,
    ["Gift_Denied_Intimacy_Not_Enough"] = 103,
    ["Mail_Expiration_Date_Max_01"] = 103,
    ["UI_Bag_RentName"] = 103,
    ["UI_Bag_RentTip"] = 103,
    ["UI_Bag_RentSuccess"] = 103,
    ["GetPurchaseValuCardNtf"] = 103,
    ["ValuCardNotNtfAnymore"] = 103,
    ["PurchaseValuCardDescription"] = 103,
    ["HaveNotPurchaseValuCard"] = 103,
    ["PurchaseValuCardPriceInfo"] = 103,
    ["UI_Ban_NotFriendPlayer"] = 103,
    ["ChangeModeTitle_BackEndLevel"] = 103,
    ["friend_OtherLeveLobbyTip"] = 103,
    ["PeakGameTimeCondition"] = 103,
    ["PeakGameQualifyCondition"] = 103,
    ["PeakGameOpenTipStr"] = 103,
    ["Team_SelectMatchTip"] = 103,
    ["Team_CurSeasonPlayedMatchTip"] = 103,
    ["UI_NewChat_ItemTips_NotSupportInVerticalScreen"] = 103,
    ["DownloadHint_IsDownloading"] = 103,
    ["DownloadHint_Function"] = 103,
    ["DownloadHint_ItemShow"] = 103,
    ["DownloadHint_ItemUse"] = 103,
    ["Cup_DailyTaskHint6"] = 103,
    ["Rank_Chest_Value"] = 103,
    ["Rank_Chest_Value_Desc"] = 103,
    ["UI_TYC_Reincarnation_BuildProgress"] = 104,
    ["UI_TYC_Reincarnation_WelfareNum"] = 104,
    ["UI_TYC_Reincarnation_WelfareNumLess"] = 104,
    ["UI_TYC_Reincarnation_FullLevel"] = 104,
    ["UI_TYC_Reincarnation_CurrencyNotEnough"] = 104,
    ["UI_TYC_Reincarnation_BuildingNotFull"] = 104,
    ["UI_TYC_Reincarnation_ConfirmContent"] = 104,
    ["UI_TYC_Reincarnation"] = 104,
    ["UI_TYC_PlayerKillMonster"] = 104,
    ["UI_TYC_BankReceive"] = 104,
    ["UI_TYC_NomalGet"] = 104,
    ["UI_TYC_Wave_Coming"] = 104,
    ["UI_TYC_Killed_Defeat"] = 104,
    ["UI_TYC_Killed_Play"] = 104,
    ["UI_TYC_Killed_Monster"] = 104,
    ["UI_TYC_Wave_Relax"] = 104,
    ["UI_TYC_Weapon_Pistol"] = 104,
    ["UI_TYC_Weapon_MeleeWeapons"] = 104,
    ["UI_TYC_Weapon_SMG"] = 104,
    ["UI_TYC_Weapon_Shotgun"] = 104,
    ["UI_TYC_Weapon_AssaultRifle"] = 104,
    ["UI_TYC_Weapon_LMG"] = 104,
    ["UI_TYC_Weapon_SniperRifle"] = 104,
    ["UI_TYC_Weapon_Launcher"] = 104,
    ["UI_TYC_Weapon_Toy"] = 104,
    ["UI_TYC_Building_Flag"] = 104,
    ["UI_TYC_Building_Ornament"] = 104,
    ["UI_TYC_Building_Ornament2"] = 104,
    ["UI_TYC_Skin_Test_Name_Deserteagle"] = 104,
    ["UI_TYC_Skin_Test_Name_M1897"] = 104,
    ["UI_TYC_Skin_Test_Name_UZI"] = 104,
    ["UI_TYC_Skin_Test_Name_AK47"] = 104,
    ["UI_TYC_Skin_Test_Name_M4"] = 104,
    ["UI_TYC_Skin_Test_Name_SCAR"] = 104,
    ["UI_TYC_Skin_Test_Name_M82"] = 104,
    ["UI_TYC_Skin_Test_Name_M249"] = 104,
    ["UI_TYC_Skin_Test_Name_RPG"] = 104,
    ["UI_TYC_Skin_Test_Name_AMW"] = 104,
    ["UI_TYC_Skin_Test_Name_RPK"] = 104,
    ["UI_TYC_Skin_Test_Name_MP5"] = 104,
    ["UI_TYC_Skin_Test_Name_BaseballBat"] = 104,
    ["UI_TYC_Skin_Test_Name_Taser2"] = 104,
    ["UI_TYC_Skin_Test_Name_Basket"] = 104,
    ["UI_TYC_Skin_Test_Name_M1911"] = 104,
    ["UI_TYC_Skin_Test_Name_TEC9"] = 104,
    ["UI_TYC_Skin_Test_Name_AA12"] = 104,
    ["UI_TYC_Skin_Test_Name_UMP"] = 104,
    ["UI_TYC_Skin_Test_Name_Tommygun"] = 104,
    ["UI_TYC_Skin_Test_Name_P90"] = 104,
    ["UI_TYC_Skin_Test_Name_FAMAS"] = 104,
    ["UI_TYC_Skin_Test_Name_AUG"] = 104,
    ["UI_TYC_Skin_Test_Name_M1"] = 104,
    ["UI_TYC_Skin_Test_Name_SVD"] = 104,
    ["UI_TYC_Skin_Test_Name_Mgl"] = 104,
    ["UI_TYC_Skin_Test_Name_M202"] = 104,
    ["UI_TYC_Skin_Test_Desc_Deserteagle"] = 104,
    ["UI_TYC_Skin_Test_Desc_M1897"] = 104,
    ["UI_TYC_Skin_Test_Desc_UZI"] = 104,
    ["UI_TYC_Skin_Test_Desc_AK47"] = 104,
    ["UI_TYC_Skin_Test_Desc_M4"] = 104,
    ["UI_TYC_Skin_Test_Desc_SCAR"] = 104,
    ["UI_TYC_Skin_Test_Desc_M82"] = 104,
    ["UI_TYC_Skin_Test_Desc_M249"] = 104,
    ["UI_TYC_Skin_Test_Desc_RPG"] = 104,
    ["UI_TYC_Skin_Test_Desc_AMW"] = 104,
    ["UI_TYC_Skin_Test_Desc_RPK"] = 104,
    ["UI_TYC_Skin_Test_Desc_MP5"] = 104,
    ["UI_TYC_Skin_Test_Desc_BaseballBat"] = 104,
    ["UI_TYC_Skin_Test_Desc_Taser2"] = 104,
    ["UI_TYC_Skin_Test_Desc_Basket"] = 104,
    ["UI_TYC_Skin_Test_Desc_M1911"] = 104,
    ["UI_TYC_Skin_Test_Desc_TEC9"] = 104,
    ["UI_TYC_Skin_Test_Desc_AA12"] = 104,
    ["UI_TYC_Skin_Test_Desc_UMP"] = 104,
    ["UI_TYC_Skin_Test_Desc_Tommygun"] = 104,
    ["UI_TYC_Skin_Test_Desc_P90"] = 104,
    ["UI_TYC_Skin_Test_Desc_FAMAS"] = 104,
    ["UI_TYC_Skin_Test_Desc_AUG"] = 104,
    ["UI_TYC_Skin_Test_Desc_M1"] = 104,
    ["UI_TYC_Skin_Test_Desc_SVD"] = 104,
    ["UI_TYC_Skin_Test_Desc_Mgl"] = 104,
    ["UI_TYC_Skin_Test_Desc_M202"] = 104,
    ["UI_TYC_Skin_Test_Desc"] = 104,
    ["UI_TYC_Skin_Test_Name_Deserteagle_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_M1897_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_UZI_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_AK47_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_M4_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_SCAR_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_M82_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_M249_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_RPG_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_AMW_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_RPK_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_MP5_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_BaseballBat_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_Taser2_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_Basket_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_M1911_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_TEC9_Star"] = 104,
    ["UI_TYC_Skin_Test_Name_AA12_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_UMP_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_Tommygun_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_P90_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_FAMAS_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_AUG_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_M1_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_SVD_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_Mgl_Star"] = 105,
    ["UI_TYC_Skin_Test_Name_M202_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_Deserteagle_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M1897_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_UZI_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_AK47_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M4_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_SCAR_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M82_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M249_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_RPG_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_AMW_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_RPK_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_MP5_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_BaseballBat_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_Taser2_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_Basket_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M1911_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_TEC9_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_AA12_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_UMP_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_Tommygun_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_P90_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_FAMAS_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_AUG_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M1_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_SVD_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_Mgl_Star"] = 105,
    ["UI_TYC_Skin_Test_Desc_M202_Star"] = 105,
    ["UI_TYC_Store_Confirm"] = 105,
    ["UI_TYC_CrystalNotEnough"] = 105,
    ["UI_TYC_Store_UseSucces"] = 105,
    ["UI_TYC_Store_UnUseSucces"] = 105,
    ["UI_TYC_Store_BuySucces"] = 105,
    ["UI_TYC_Store_SystemHelpTitle"] = 105,
    ["UI_TYC_Store_SystemHelpText"] = 105,
    ["UI_TYC_Store_CrystalGetHelpTitle"] = 105,
    ["UI_TYC_Store_CrystalGetHelpText"] = 105,
    ["UI_TYC_Monster_Guild_Tips"] = 105,
    ["UI_TYC_Monster_Wave_Reward_Btn"] = 105,
    ["UI_TYC_Wave_Reward_Eliminate"] = 105,
    ["UI_TYC_Wave_Finish"] = 105,
    ["UI_TYC_Wave_Reward_String"] = 105,
    ["UI_TYC_GetCoin"] = 105,
    ["UI_TYC_TD_BackToCommunityTip"] = 105,
    ["UI_TYC_TDS_BackToCommunityTip"] = 105,
    ["UI_TYC_Wave_Explore"] = 105,
    ["UI_TYC_Skin_Test_Name_building1"] = 105,
    ["UI_TYC_Skin_Test_Name_building2"] = 105,
    ["UI_TYC_Skin_Test_Name_building3"] = 105,
    ["UI_TYC_Skin_Test_Name_building4"] = 105,
    ["UI_TYC_Skin_Test_Name_building5"] = 105,
    ["UI_TYC_Skin_Test_Name_building6"] = 105,
    ["UI_TYC_Skin_Test_Name_building7"] = 105,
    ["UI_TYC_Skin_Test_Name_building8"] = 105,
    ["UI_TYC_Skin_Test_Name_building9"] = 105,
    ["UI_TYC_Skin_Test_Name_building10"] = 105,
    ["UI_TYC_Skin_Test_Name_building11"] = 105,
    ["UI_TYC_Skin_Test_Name_building12"] = 105,
    ["UI_TYC_Skin_Test_Name_building13"] = 105,
    ["UI_TYC_Skin_Test_Name_building14"] = 105,
    ["UI_TYC_Skin_Test_Desc_building1"] = 105,
    ["UI_TYC_Skin_Test_Desc_building2"] = 105,
    ["UI_TYC_Skin_Test_Desc_building3"] = 105,
    ["UI_TYC_Skin_Test_Desc_building4"] = 105,
    ["UI_TYC_Skin_Test_Desc_building5"] = 105,
    ["UI_TYC_Skin_Test_Desc_building6"] = 105,
    ["UI_TYC_Skin_Test_Desc_building7"] = 105,
    ["UI_TYC_Skin_Test_Desc_building8"] = 105,
    ["UI_TYC_Skin_Test_Desc_building9"] = 105,
    ["UI_TYC_Skin_Test_Desc_building10"] = 105,
    ["UI_TYC_Skin_Test_Desc_building11"] = 105,
    ["UI_TYC_Skin_Test_Desc_building12"] = 105,
    ["UI_TYC_Skin_Test_Desc_building13"] = 105,
    ["UI_TYC_Skin_Test_Desc_building14"] = 105,
    ["UI_TYC_Skin_Test_Name_flag1"] = 105,
    ["UI_TYC_Skin_Test_Name_flag2"] = 105,
    ["UI_TYC_Skin_Test_Name_flag3"] = 105,
    ["UI_TYC_Skin_Test_Name_flag4"] = 105,
    ["UI_TYC_Skin_Test_Name_flag5"] = 105,
    ["UI_TYC_Skin_Test_Name_flag6"] = 105,
    ["UI_TYC_Skin_Test_Name_flag7"] = 105,
    ["UI_TYC_Skin_Test_Name_flag8"] = 105,
    ["UI_TYC_Skin_Test_Name_flag9"] = 105,
    ["UI_TYC_Skin_Test_Name_flag10"] = 105,
    ["UI_TYC_Skin_Test_Name_flag11"] = 105,
    ["UI_TYC_Skin_Test_Name_flag12"] = 105,
    ["UI_TYC_Skin_Test_Name_flag13"] = 105,
    ["UI_TYC_Skin_Test_Name_flag14"] = 105,
    ["UI_TYC_Skin_Test_Name_flag15"] = 105,
    ["UI_TYC_Skin_Test_Name_flag16"] = 105,
    ["UI_TYC_Skin_Test_Name_flag17"] = 105,
    ["UI_TYC_Skin_Test_Name_flag18"] = 106,
    ["UI_TYC_Skin_Test_Name_flag19"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag1"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag2"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag3"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag4"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag5"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag6"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag7"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag8"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag9"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag10"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag11"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag12"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag13"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag14"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag15"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag16"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag17"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag18"] = 106,
    ["UI_TYC_Skin_Test_Desc_flag19"] = 106,
    ["UI_TYC_Boss_Wave_Finish_Broadcast"] = 106,
    ["UI_TYC_Wave_Suspend_Tips"] = 106,
    ["UI_TYC_Wave_Suspend_End_Content"] = 106,
    ["UI_TYC_Wave_Finish_Broadcast"] = 106,
    ["UI_TD_MonitorSkill_NoEnoughGold"] = 106,
    ["UI_TD_MonitorSkill_InCd"] = 106,
    ["UI_TD_MonitorSkill_ForbiddenPeriod"] = 106,
    ["UI_TYC_Offline_Reward_Tips"] = 106,
    ["UI_TYC_Common_LongMin"] = 106,
    ["UI_TYC_Offline_Diamond_Reward_Tips"] = 106,
    ["UI_TDS_Wave_Pvp_Pre_Tips_Prefix"] = 106,
    ["UI_TDS_Wave_Pvp_Pre_Tips_Content"] = 106,
    ["UI_TDS_Wave_Pvp_tips"] = 106,
    ["UI_TDS_Wave_Endless_Pre_Tips_Prefix"] = 106,
    ["UI_TDS_Wave_Endless_Pre_Tips_Content"] = 106,
    ["UI_TDS_Wave_Endless_Pre_Tips_Suffix"] = 106,
    ["UI_TDS_Wave_Endless_Tips"] = 106,
    ["UI_TDS_Wave_Pvp_Pre_CountDown_Content"] = 106,
    ["UI_TD_Broadcast_WaveBegin"] = 106,
    ["UI_TD_Broadcast_WaveEnd"] = 106,
    ["UI_TD_Broadcast_CastSkill_Mine"] = 106,
    ["UI_TD_Broadcast_CastSkill_Enemy"] = 106,
    ["UI_TD_Broadcast_UnlockWeapon_Mine"] = 106,
    ["UI_TD_Broadcast_UpgradeWeapon_Mine"] = 106,
    ["UI_TD_Broadcast_UpgradeWeapon_Screen_Sub"] = 106,
    ["UI_TD_Broadcast_UpgradeWeapon_Screen"] = 106,
    ["UI_TD_Broadcast_MonsterEscape_Mine"] = 106,
    ["UI_TD_Broadcast_MonsterEscape_Enemy"] = 106,
    ["UI_TD_Monitor_Skill_Name_100004"] = 106,
    ["UI_TD_Monitor_Skill_Name_100002"] = 106,
    ["UI_TD_Monitor_Skill_Name_100003"] = 106,
    ["UI_TD_Monitor_Skill_Name_100008"] = 106,
    ["UI_TD_Monitor_Skill_Name_100009"] = 106,
    ["UI_TD_Monitor_Skill_Name_100011"] = 106,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100002"] = 106,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100004"] = 106,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100008"] = 106,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100009"] = 106,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100011"] = 106,
    ["UI_TD_Broadcast_Summon_Friend_Lv1"] = 106,
    ["UI_TD_Broadcast_Summon_Friend_Lv2"] = 106,
    ["UI_TD_Broadcast_Summon_Friend_Lv3"] = 106,
    ["UI_TD_Broadcast_Summon_Enemy_Lv1"] = 106,
    ["UI_TD_Broadcast_Summon_Enemy_Lv2"] = 106,
    ["UI_TD_Broadcast_Summon_Enemy_Lv3"] = 106,
    ["UI_TD_Broadcast_Friend_Unlock_Weapon"] = 106,
    ["UI_TYC_FPSSetting_Common_Title"] = 106,
    ["UI_TYC_FPSSetting_TYC_Title"] = 106,
    ["UI_TYC_FPSSetting_GyroscopeItem_Title"] = 106,
    ["UI_TYC_FPSSetting_PlayingItem_Title"] = 106,
    ["UI_TYC_FPSSetting_SniperItem_Title"] = 106,
    ["UI_TYC_FPSSetting_CommonItem_Tips"] = 106,
    ["UI_TD_Common_SuperTower_UpGrade"] = 106,
    ["UI_TDS_Broadcast_Player_LoseBlood_Monster"] = 106,
    ["UI_TDS_Broadcast_Player_LoseBlood_BloodNum"] = 106,
    ["UI_TDS_Broadcast_Player_Fail"] = 106,
    ["UI_TDS_Broadcast_Player_Quit"] = 106,
    ["UI_TDS_GetCoin"] = 106,
    ["UI_TD_Common_SuperTower_UpGrade_Text"] = 106,
    ["UI_TD_Common_SuperTower_HasEvolute"] = 106,
    ["UI_TD_Common_SuperTower_DamageUp"] = 106,
    ["UI_TDS_Level_Start_Prefix"] = 106,
    ["UI_TDS_Level_Start_Tips"] = 106,
    ["UI_TD_Dander_Monster_Appear"] = 106,
    ["UI_TD_Dander_Monster_Appear_Tips"] = 106,
    ["UI_TD_Dander_Monster_Call_Appear"] = 106,
    ["UI_TYC_Crystal_Store_Skin_Use_After_Buy"] = 106,
    ["UI_TYC_Crystal_Store_Skin_Use_After_Buy_Success"] = 106,
    ["UI_TYC_NewbieWall_NoAttack_Broadcast"] = 106,
    ["UI_TYC_NewbieWall_NoShoot_Broadcast"] = 106,
    ["UI_TYC_NewbieWall_Over_Broadcast"] = 106,
    ["UI_TDS_Dander_Monster_Appear_Tips"] = 106,
    ["UI_TYC_Wave_Fail_Tips"] = 106,
    ["UI_TYC_Wave_Fail_Content"] = 106,
    ["UI_TYC_Reincarnation_WaveProgress"] = 106,
    ["UI_TYC_Reincarnation_StillHaveWave"] = 106,
    ["UI_TYC_GameResult_Countdown_Restart"] = 106,
    ["UI_TYC_GameResult_Countdown_Continue"] = 106,
    ["UI_TYC_Wave_Fail_Boss_Content"] = 106,
    ["UI_TYC_BackToCommunityTip"] = 107,
    ["UI_TYC_BackToCommunityTip_Back"] = 107,
    ["UI_TYC_BackToCommunityTip_Restart"] = 107,
    ["UI_TYC_QuitTip_Restart"] = 107,
    ["UI_TYC_QuitTip_2"] = 107,
    ["UI_TYC_QuitTip_3"] = 107,
    ["UI_TYC_QuitTip_4"] = 107,
    ["UI_TYC_QuitTip_5"] = 107,
    ["UI_TYC_QuitTip_6"] = 107,
    ["UI_TYC_QuitTip_7"] = 107,
    ["UI_TYC_QuitTip_8"] = 107,
    ["UI_TYC_Ntf_Player_Fail_Reward_Content"] = 107,
    ["UI_TYC_PeakReincarnation"] = 107,
    ["UI_TYC_Monster_Boss"] = 107,
    ["UI_TD_Dander_Monster_Name"] = 107,
    ["UI_TYC_Wave_Idx_Tips"] = 107,
    ["UI_OMD_PlayerNum_One"] = 107,
    ["UI_OMD_PlayerNum_Two"] = 107,
    ["UI_OMD_PlayerNum_Four"] = 107,
    ["UI_OMD_Monster_Invade_Tips"] = 107,
    ["UI_OMD_Monster_Appear_Tips"] = 107,
    ["UI_OMD_Broadcast_Player_LoseBlood_Monster"] = 107,
    ["UI_OMD_Broadcast_Player_LoseBlood_BloodNum"] = 107,
    ["UI_OMD_GameResult_BtnBack_Lobby"] = 107,
    ["UI_OMD_GameResult_Btn_Restart"] = 107,
    ["UI_OMD_GameResult_Btn_NextLevel"] = 107,
    ["UI_OMD_GameResult_Text_LevelScore"] = 107,
    ["UI_OMD_GameResult_Text_CrownGrade"] = 107,
    ["UI_OMD_GameResult_Text_AdditionalCrown"] = 107,
    ["UI_OMD_GameResult_Text_CostTime"] = 107,
    ["UI_OMD_GameResult_Text_GateNum"] = 107,
    ["UI_OMD_GameResult_Text_TotalScore"] = 107,
    ["UI_OMD_GameResult_Text_TotalSkeletons"] = 107,
    ["UI_OMD_GameResult_Text_Wave"] = 107,
    ["UI_OMD_UI_OMD_LevelSelect_Unlock_Tip"] = 107,
    ["UI_OMD_GameResult_GameName"] = 107,
    ["UI_OMD_GameResult_Details"] = 107,
    ["UI_OMD_TrapHint_State_Build"] = 107,
    ["UI_OMD_TrapHint_State_Sell"] = 107,
    ["UI_OMD_TrapHint_Build_Disable"] = 107,
    ["UI_OMD_TrapHint_WarTrap_Build_Invalid"] = 107,
    ["UI_OMD_TrapHint_Build_Invalid"] = 107,
    ["UI_OMD_TrapHint_Build_Type1"] = 107,
    ["UI_OMD_TrapHint_Build_Type2"] = 107,
    ["UI_OMD_TrapHint_Build_Type4"] = 107,
    ["UI_OMD_TrapHint_Build_Type5"] = 107,
    ["UI_OMD_TrapHint_Build_Type6"] = 107,
    ["UI_OMD_TrapHint_Build_Type3"] = 107,
    ["UI_OMD_Member_Wait_Leader_Battle"] = 107,
    ["UI_OMD_Endless_Db_ConfirmTitle"] = 107,
    ["UI_OMD_Endless_Db_ConfirmContent"] = 107,
    ["UI_OMD_Dander_Monster_Appear"] = 107,
    ["UI_OMD_Dander_Monster_Appear_Tips"] = 107,
    ["UI_OMD_Player_Join_Message_Tips"] = 107,
    ["UI_OMD_Difficulty_Simple"] = 107,
    ["UI_OMD_Difficulty_Normal"] = 107,
    ["UI_OMD_Difficulty_Difficulty"] = 107,
    ["UI_OMD_Difficulty_Endless"] = 107,
    ["UI_OMD_QuitSaveConfirm"] = 107,
    ["UI_OMD_QuitSaveConfirm_Save"] = 107,
    ["UI_OMD_QuitSaveConfirm_NoSave"] = 107,
    ["UI_OMD_Give_Up_Db_Info"] = 107,
    ["UI_OMD_GameResult_Text_NewbieTitle"] = 107,
    ["UI_OMD_GameResult_Text_NewbieFail"] = 107,
    ["UI_OMD_GameResult_Text_End"] = 107,
    ["UI_OMD_GameResult_Text_MoreWave"] = 107,
    ["UI_OMD_GameResult_Text_LevelName"] = 107,
    ["UI_OMD_GameResult_Text_WaveVictory"] = 107,
    ["UI_OMD_GameResult_Text_WaveFailed"] = 107,
    ["UI_OMD_Player_Insufficient_player_Message_Tips"] = 107,
    ["UI_OMD_Player_Fill_Back_Again_Message_Tips"] = 107,
    ["UI_OMD_Player_Fill_Back_Start_Message_Tips"] = 107,
    ["UI_OMD_SelectWave_Tips"] = 107,
    ["UI_OMD_SelectWave_Item_Tips"] = 107,
    ["UI_OMD_SelectWave_Item_Tips_1"] = 107,
    ["UI_OMD_SelectWave_Item_Tips_2"] = 107,
    ["UI_OMD_SelectWave_GameWin_Tips_1"] = 107,
    ["UI_OMD_SelectWave_GameWin_Tips_2"] = 107,
    ["UI_OMD_TeamPreparation_Replay_Text"] = 107,
    ["UI_OMD_TeamPreparation_GoLobby_Text"] = 107,
    ["UI_OMD_EquipmentUpEntrance_Name"] = 107,
    ["UI_OMD_Tip_Insufficient_Crowns"] = 107,
    ["UI_OMD_Tip_Complete_Previous_Task"] = 107,
    ["UI_OMD_NotInRank"] = 107,
    ["UI_OMD_LockedRank"] = 107,
    ["UI_OMD_RankNoInfo"] = 107,
    ["UI_OMD_NewbieClickJianQiang"] = 107,
    ["UI_OMD_NewbieClickNu"] = 107,
    ["UI_OMD_NewbieClickObstacle"] = 107,
    ["UI_OMD_PlayerKillMonster"] = 107,
    ["UI_OMD_GetCoins"] = 107,
    ["UI_OMD_SellTrap"] = 107,
    ["UI_OMD_TeamPreparation_Prepared_Confirm_Text"] = 107,
    ["UI_OMD_TeamPreparation_Prepared_Prepare_Text"] = 107,
    ["UI_OMD_TeamPreparation_Select_Btn_Refuse_Text"] = 107,
    ["UI_OMD_TeamPreparation_Select_Btn_Confirm_Text"] = 107,
    ["UI_OMD_TeamPreparation_Select_Btn_Prepare_Text"] = 107,
    ["UI_OMD_TeamPreparation_Select_Btn_Ready_Text"] = 107,
    ["UI_OMD_No_Permission_Start_Challenge"] = 107,
    ["UI_OMD_Too_Long_Battle_Quit_Text"] = 107,
    ["UI_OMD_MinMap_LayerInfo"] = 108,
    ["UI_OMD_CurrentState_Can_Not_Change_Weapon"] = 108,
    ["UI_OMD_DeletePropsTip_YouHaveAlreadyPut"] = 108,
    ["UI_OMD_DeletePropsTip_Make"] = 108,
    ["UI_OMD_DeletePropsTip_AwayFromBag"] = 108,
    ["UI_OMD_DeletePropsTip_WillAutomaticallySellAllPlacedTraps"] = 108,
    ["UI_OMD_CancelPaidRevive_Text"] = 108,
    ["UI_OMD_RankBattleMode"] = 108,
    ["UI_OMD_RankChallengeMode"] = 108,
    ["UI_OMD_TotalRank"] = 108,
    ["UI_OMD_Not_Invite_On_FillBack_Text"] = 108,
    ["UI_OMD_UI_OMD_LevelSelect_CurLevel_Unlock_Tip"] = 108,
    ["UI_OMD_EndlessModeDescription"] = 108,
    ["UI_OMD_SellTrapDialogHint"] = 108,
    ["UI_OMD_SellTrapDialogConfirm"] = 108,
    ["UI_OMD_SellTrapDialogCancel"] = 108,
    ["UI_OMD_SellTrapDialogNameDefault"] = 108,
    ["UI_OMD_UI_OMD_LevelSelect_Unlock_Tip_Endless"] = 108,
    ["UI_OMD_UI_OMD_GameMode_Prepare"] = 108,
    ["UI_OMD_UI_OMD_GameMode_Start"] = 108,
    ["UI_OMD_BloorBar_Member_Wait_Battle"] = 108,
    ["UI_OMD_BloorBar_Wait_Other_Battle"] = 108,
    ["UI_OMD_HelpTip"] = 108,
    ["UI_OMD_HelpTip_Battle"] = 108,
    ["UI_OMD_HelpTip_Battle_Rule"] = 108,
    ["UI_OMD_HelpTip_Battle_Content"] = 108,
    ["UI_OMD_HelpTip_Endless"] = 108,
    ["UI_OMD_HelpTip_Endless_Rule"] = 108,
    ["UI_OMD_HelpTip_Endless_Content"] = 108,
    ["UI_OMD_StartBattle"] = 108,
    ["UI_OMD_GetCoinsByKill"] = 108,
    ["UI_OMD_GameResult_GetCrown"] = 108,
    ["UI_OMD_NewbieNotEnoughMoney"] = 108,
    ["UI_OMD_UI_Blood_Start_Battle"] = 108,
    ["UI_OMD_LevelSelect_Start_Battle"] = 108,
    ["UI_OMD_LevelSelect_Confirm"] = 108,
    ["UI_OMD_DailyMission_Not_Finish_Task_Tips"] = 108,
    ["UI_OMD_DailyMission_Finish_Task_Tips"] = 108,
    ["UI_OMD_Store_Confirm"] = 108,
    ["UI_OMD_Back_To_Hall"] = 108,
    ["UI_OMD_GameMode_Wait_Member_Prepare"] = 108,
    ["UI_OMD_DailyMission_Daily_Task_Complete"] = 108,
    ["UI_OMD_Skin_Name_Blunderbuss"] = 108,
    ["UI_OMD_Skin_Name_Crossbow"] = 108,
    ["UI_OMD_Skin_Name_Sword"] = 108,
    ["UI_OMD_Skin_Name_Magicbow"] = 108,
    ["UI_OMD_Skin_Name_Hammer"] = 108,
    ["UI_OMD_Skin_Name_Wand"] = 108,
    ["UI_OMD_Skin_Desc_Blunderbuss"] = 108,
    ["UI_OMD_Skin_Desc_Crossbow"] = 108,
    ["UI_OMD_Skin_Desc_Sword"] = 108,
    ["UI_OMD_Skin_Desc_Magicbow"] = 108,
    ["UI_OMD_Skin_Desc_Hammer"] = 108,
    ["UI_OMD_Skin_Desc_Wand"] = 108,
    ["UI_OMD_TrapSkin_Name_3001"] = 108,
    ["UI_OMD_TrapSkin_Name_3002"] = 108,
    ["UI_OMD_TrapSkin_Name_3003"] = 108,
    ["UI_OMD_TrapSkin_Name_3005"] = 108,
    ["UI_OMD_TrapSkin_Name_3006"] = 108,
    ["UI_OMD_TrapSkin_Name_3007"] = 108,
    ["UI_OMD_TrapSkin_Name_3008"] = 108,
    ["UI_OMD_TrapSkin_Name_3009"] = 108,
    ["UI_OMD_TrapSkin_Name_3010"] = 108,
    ["UI_OMD_TrapSkin_Name_3011"] = 108,
    ["UI_OMD_TrapSkin_Name_3012"] = 108,
    ["UI_OMD_TrapSkin_Name_3014"] = 108,
    ["UI_OMD_TrapSkin_Name_3015"] = 108,
    ["UI_OMD_TrapSkin_Name_3016"] = 108,
    ["UI_OMD_TrapSkin_Name_3017"] = 108,
    ["UI_OMD_TrapSkin_Name_3018"] = 108,
    ["UI_OMD_TrapSkin_Name_3019"] = 108,
    ["UI_OMD_TrapSkin_Name_3020"] = 108,
    ["UI_OMD_TrapSkin_Name_3021"] = 108,
    ["UI_OMD_TrapSkin_Name_3022"] = 108,
    ["UI_OMD_TrapSkin_Name_3023"] = 108,
    ["UI_OMD_TrapSkin_Name_3024"] = 108,
    ["UI_OMD_TrapSkin_Name_3025"] = 108,
    ["UI_OMD_TrapSkin_Name_3026"] = 108,
    ["UI_OMD_TrapSkin_Name_3027"] = 108,
    ["UI_OMD_TrapSkin_Name_3028"] = 108,
    ["UI_OMD_TrapSkin_Name_3029"] = 108,
    ["UI_OMD_TrapSkin_Name_3030"] = 108,
    ["UI_OMD_TrapSkin_Des_01"] = 108,
    ["UI_OMD_TrapSkin_Des_02"] = 108,
    ["UI_OMD_TrapSkin_Des_03"] = 108,
    ["UI_OMD_Wave_Select_Tip"] = 108,
    ["UI_OMD_AFK_WARNING"] = 108,
    ["UI_OMD_RedIsFull"] = 108,
    ["UI_OMD_BlueIsFull"] = 108,
    ["UI_OMD_UI_StartWave_In_80703_NewbieTip"] = 108,
    ["UI_OMD_AddHealth"] = 108,
    ["UI_OMD_AddMana"] = 108,
    ["UI_OMD_CLICK_QUITBUID_TIP"] = 108,
    ["UI_OMD_SELECT_LEVEL_ON_VOTING"] = 108,
    ["UI_OMD_No_Permission_Change_Level"] = 108,
    ["UI_OMD_Single_Db_Give_Up_Warning_Tips"] = 108,
    ["UI_OMD_CrownManagementTips"] = 108,
    ["UI_OMD_OrcTotal"] = 108,
    ["UI_OMD_OrcGeneral"] = 108,
    ["UI_OMD_OrcElite"] = 108,
    ["UI_OMD_OrcLord"] = 109,
    ["UI_OMD_Select_Level_Wait_Other_Loading"] = 109,
    ["UI_OMD_UI_OMD_Levels_Unlock_Tip"] = 109,
    ["UI_OMD_UI_OMD_Levels_Unlock_HardMode_One"] = 109,
    ["UI_OMD_UI_OMD_Levels_Unlock_HardMode_Two"] = 109,
    ["UI_OMD_ExchangeTargetInfo"] = 109,
    ["UI_OMD_ExchangeTrapSkinInfo_One"] = 109,
    ["UI_OMD_ExchangeTrapSkinInfo_Two"] = 109,
    ["UI_OMD_ExchangeNoStarCoin"] = 109,
    ["UI_OMD_VoteTime"] = 109,
    ["UI_OMD_VoteTimeTips"] = 109,
    ["UI_OMD_ConfirmContent4NewSeason"] = 109,
    ["UI_OMD_ConfirmContent4OldSeasonSingleDB"] = 109,
    ["UI_OMD_RoomHasPWDCannotRecruit"] = 109,
    ["UI_OMD_UGC_Confirm"] = 109,
    ["UI_OMD_UGC_WaitToConfirm"] = 109,
    ["UI_OMD_UGC_StartBattle"] = 109,
    ["UI_OMD_UGC_Select_Level_VersionError"] = 109,
    ["UI_OMD_UGC_LevelPlayerNumError"] = 109,
    ["UI_OMD_QuitTestUGCLevel"] = 109,
    ["UI_OMD_UGC_ReturnToCreate"] = 109,
    ["UI_OMD_UGC_Select_Level_InSearchPlayerStateError"] = 109,
    ["UI_OMD_UGC_Select_Level_InRecruitStateError"] = 109,
    ["UI_OMD_UGC_LevelTips"] = 109,
    ["UI_OMD_UGC_DropPoolEditorCountNum"] = 109,
    ["UI_OMD_UGC_DropPoolEditorCountNum_Perview"] = 109,
    ["UI_OMD_UGC_DropPoolEditorMaxNumTips"] = 109,
    ["UI_OMD_UGC_DropPoolEditorCreateBtn"] = 109,
    ["UI_OMD_UGC_DropPoolEditorDefaultName"] = 109,
    ["UI_OMD_UGC_DropPoolEditorDeleteConfirm"] = 109,
    ["UI_OMD_UGC_DropPoolEditorDeleteConfirm_WithRef"] = 109,
    ["UI_OMD_UGC_DropPoolEnumDefault"] = 109,
    ["UI_OMD_UGC_DropPoolEnumEmpty"] = 109,
    ["UI_OMD_UGC_DropPoolDropRateTips"] = 109,
    ["UI_OMD_UGC_EnterLevelFail"] = 109,
    ["UI_OMD_UGC_SelectLevelFail"] = 109,
    ["TravelDog_AfternoonMessage_1"] = 110,
    ["TravelDog_AfternoonMessage_2"] = 110,
    ["TravelDog_AfternoonMessage_3"] = 110,
    ["TravelDog_AfternoonMessage_4"] = 110,
    ["TravelDog_AfternoonMessage_5"] = 110,
    ["TravelDog_AfternoonMessage_6"] = 110,
    ["TravelDog_AfternoonMessage_7"] = 110,
    ["TravelDog_AfternoonMessage_8"] = 110,
    ["TravelDog_AfternoonMessage_9"] = 110,
    ["TravelDog_AfternoonMessage_10"] = 110,
    ["TravelDog_AfternoonMessage_11"] = 110,
    ["TravelDog_AfternoonMessage_12"] = 110,
    ["TravelDog_AfternoonMessage_13"] = 110,
    ["TravelDog_AfternoonMessage_14"] = 110,
    ["TravelDog_AfternoonMessage_15"] = 110,
    ["TravelDog_AfternoonMessage_16"] = 110,
    ["TravelDog_AfternoonMessage_17"] = 110,
    ["TravelDog_AfternoonMessage_18"] = 110,
    ["TravelDog_Sleep"] = 110,
    ["TravelDog_DateShow_Today"] = 110,
    ["TravelDog_DateShow"] = 110,
    ["TravelDog_DefaultWeather"] = 110,
    ["TravelDog_Timeout"] = 110,
    ["TravelDog_PreparationTips"] = 110,
    ["TravelDog_InFarmTip"] = 110,
    ["TravelDog_RoseMarket"] = 110,
    ["UGC_Programme_Element"] = 111,
    ["UGC_Programme_LogicElement"] = 111,
    ["UGC_Programme_Model"] = 111,
    ["UGC_Programme_SignalBox"] = 111,
    ["UGC_Programme_Vehicle"] = 111,
    ["UGC_Programme_Creature"] = 111,
    ["UGC_Programme_Complete"] = 111,
    ["UGC_Programme_Choose"] = 111,
    ["UGC_Programme_Button"] = 111,
    ["UGC_Programme_Text"] = 111,
    ["UGC_Programme_Picture"] = 111,
    ["UGC_Programme_Tips"] = 111,
    ["UGC_Programme_Tips_Error"] = 111,
    ["UGC_Programme_Element_Error"] = 111,
    ["UGC_Programme_LogicElement_Error"] = 111,
    ["UGC_Programme_Model_Error"] = 111,
    ["UGC_Programme_SignalBox_Error"] = 111,
    ["UGC_Programme_Vehicle_Error"] = 111,
    ["UGC_Programme_Creature_Error"] = 111,
    ["UGC_Programme_TextBoard_Error"] = 111,
    ["UGC_Programme_TextBoard"] = 111,
    ["UGC_Programme_ImageBoard"] = 111,
    ["UGC_Programme_C4Bomb"] = 111,
    ["UGC_Programme_PointGuide"] = 111,
    ["UGC_Programme_PointGuide_Error"] = 111,
    ["UGC_Programme_ProgressBoard"] = 111,
    ["UGC_MapCapacity_Save_IsExceedLimit"] = 111,
    ["UGC_MapCapacity_Save_IsAlmostLimit"] = 111,
    ["UGC_MapCapacity_Save_IsNearLimit"] = 111,
    ["UGC_Map_Capacity_Open_IsExceedLimit"] = 111,
    ["UGC_Map_Capacity_Open_IsAlmostLimit"] = 111,
    ["UGC_Map_Capacity_Publish_IsExceedLimit"] = 111,
    ["UGC_PACustomAttribute_NameRepeated"] = 111,
    ["UGC_MidJoin_CantInvite_SinglePlay"] = 111,
    ["UGC_MidJoin_CantInvite_SettingState"] = 111,
    ["UGC_MidJoin_CantInvite_MemberLimit"] = 111,
    ["UGC_MidJoin_CantInvite_GameType"] = 111,
    ["UGC_MidJoin_CantInvite_MultiTestRoom"] = 111,
    ["UGC_PatrolPath_OutofPointCount"] = 112,
    ["UGC_ActionEditor_DeleteConfirm"] = 112,
    ["UGC_ActionEditor_Deleted"] = 112,
    ["UGC_ActionEditor_DeleteUsedConfirm"] = 112,
    ["UGC_ActionEditor_StorageMax"] = 112,
    ["UGC_ActionEditor_VideoTransforming"] = 112,
    ["UGC_ActionEditor_VideoAnim"] = 112,
    ["UGC_NPCEditor_InteractionKey"] = 112,
    ["UGC_ActionEditor_VideoPageName"] = 112,
    ["UGC_ActionEditor_VideoCreatButton"] = 112,
    ["UGC_ActionEditor_VideoCreatTip1"] = 112,
    ["UGC_ActionEditor_VideoCreatTip2"] = 112,
    ["UGC_ActionEditor_VideoCreatTip3"] = 112,
    ["UGC_ActionEditor_VideoCreatTip4"] = 112,
    ["UGC_ActionEditor_VideoCreatTip5"] = 112,
    ["UGC_ActionEditor_QuitConfirm"] = 112,
    ["UGC_ActionEditor_SaveTimeoutText"] = 112,
    ["UGC_ActionEditor_DiyActionName"] = 112,
    ["UGC_ActionEditor_EditButtonTip"] = 112,
    ["UGC_ActionEditor_DelButtonTip"] = 112,
    ["UGC_ActionEditor_AddAnimFail"] = 112,
    ["UGC_ActionEditor_AddExpressionFail"] = 112,
    ["UGC_AIGCAnim_ServerBusy"] = 112,
    ["UGC_AIGCAnim_MediaFile_MaxSize_Tip"] = 112,
    ["UGC_AIGCAnim_MediaFile_MaxDuration_Tip"] = 112,
    ["UGC_AIGCAnim_GetUploadCosFailed_Tip"] = 112,
    ["UGC_AIGCAnim_NetworkIsBad_Tip"] = 112,
    ["UGC_AIGCAnim_UploadCosFailed_Tip"] = 112,
    ["UGC_AIGCAnim_ServerError_Tip"] = 112,
    ["UGC_AIGCAnim_ServerError_Busy_Tip"] = 112,
    ["UGC_AIGCAnim_ExamineFailed_Tip"] = 112,
    ["UGC_AIGCAnim_ExamineSpiteFailed_Tip"] = 112,
    ["UGC_AIGCAnim_ExamineSensitiveFailed_Tip"] = 112,
    ["UGC_AIGCAnim_GetJsonTimeout_Tip"] = 112,
    ["UGC_AIGCAnim_JsonReturnError_Tip"] = 112,
    ["UGC_AIGCAnim_DownloadPbinError_Tip"] = 112,
    ["UGC_AIGCAnim_MinWidth_Tip"] = 112,
    ["UGC_AIGCAnim_GenerateFailTip"] = 112,
    ["UGC_AIGCAnim_TooOften"] = 112,
    ["UGC_AIGCAnim_FileSaveFailed"] = 112,
    ["UGC_AIGCAnim_ExtensionNotSupport"] = 112,
    ["UGC_ActionEditor_TabOfficial"] = 112,
    ["UGC_ActionEditor_TabSplice"] = 112,
    ["UGC_ActionEditor_TabVideo"] = 112,
    ["UGC_AIGCAnim_SelectVideoTip"] = 112,
    ["UGC_Place_NPC_OutOfMaxNum"] = 112,
    ["UGC_Object_Cant_BindNPC"] = 112,
    ["UGC_ActionEditor_SaveNameRepeatTip"] = 112,
    ["UGC_ActionEditorExpreWithoutAction"] = 112,
    ["UGC_PatrolPath_OutofPathCount"] = 112,
    ["UGC_Camera_Shake"] = 112,
    ["UGC_Camera_ChangeView"] = 112,
    ["UGC_Camera_Block"] = 112,
    ["UGC_ActionEditor_SaveNameIsNull"] = 112,
    ["UGC_RPG_WarnningWordsTips"] = 112,
    ["UGC_Dialogue_Nothing"] = 112,
    ["UGC_Dialogue_GroupName"] = 112,
    ["UGC_Dialogue_CreateDialogue"] = 112,
    ["UGC_Dialogue_NoDialogue"] = 112,
    ["UGC_Dialogue_StepEdit"] = 112,
    ["UGC_Dialogue_OptionEdit"] = 112,
    ["UGC_Dialogue_ImageEdit"] = 112,
    ["UGC_Dialogue_MaxStepNum"] = 112,
    ["UGC_Dialogue_SaveSuccess"] = 112,
    ["UGC_Dialogue_MaxGroupNum"] = 112,
    ["UGC_Dialogue_CreateGroupSuccess"] = 112,
    ["UGC_Dialogue_Open"] = 112,
    ["UGC_Dialogue_Choose"] = 112,
    ["UGC_Dialogue_DeleteStepTips"] = 112,
    ["UGC_Dialogue_DeleteStepGroupTips"] = 112,
    ["UGC_Dialogue_DeleteOptionTips"] = 112,
    ["UGC_Dialogue_CreateOptionTips"] = 112,
    ["UGC_Dialogue_CreateGroupFirstTips"] = 112,
    ["UGC_Dialogue_DeleteGroupTips"] = 112,
    ["UGC_Dialogue_WithoutGroupTips"] = 112,
    ["UGC_Dialogue_AddNewGroup"] = 112,
    ["UGC_Dialogue_OptionWithoutBind"] = 112,
    ["UGC_Dialogue_OptionStop"] = 112,
    ["UGC_Dialogue_StepEmojiTitle"] = 112,
    ["UGC_Dialogue_StepBoxTitle"] = 112,
    ["UGC_Dialogue_AddOption"] = 112,
    ["UGC_Dialogue_StepSpeakerEmo"] = 112,
    ["UGC_Dialogue_StepSpeakerAnim"] = 112,
    ["UGC_Dialogue_StepDefault"] = 112,
    ["UGC_Object_Cant_BindMirrorActor"] = 112,
    ["Cant_ReplaceChar_CameraType_FP"] = 112,
    ["UGC_CustomAnimSubActor_Cannot_MultipleSelect"] = 112,
    ["UGC_AIGC_Voice_HasPeddingVoice"] = 112,
    ["MCG_ModeName"] = 113,
    ["MCG_ModeInfo"] = 113,
    ["MCG_ModeInfoUGC"] = 113,
    ["MCG_Mode_Background"] = 113,
    ["MCG_MapName_A"] = 113,
    ["MCG_MapInfo_A"] = 113,
    ["MCG_MapName_B"] = 113,
    ["MCG_MapInfo_B"] = 113,
    ["MCG_MapName_C"] = 113,
    ["MCG_MapInfo_C"] = 113,
    ["MCG_MapName_D"] = 113,
    ["MCG_MapInfo_D"] = 113,
    ["MCG_MapName_E"] = 113,
    ["MCG_MapInfo_E"] = 113,
    ["MCG_MapName_F"] = 113,
    ["MCG_MapInfo_F"] = 113,
    ["MCG_MapName_G"] = 113,
    ["MCG_MapInfo_G"] = 113,
    ["MCG_MapName_H"] = 113,
    ["MCG_MapInfo_H"] = 113,
    ["MCG_MapName_I"] = 113,
    ["MCG_MapInfo_I"] = 113,
    ["MCG_MapName_J"] = 113,
    ["MCG_MapInfo_J"] = 113,
    ["MCG_InGame_GuideNPC_Name"] = 113,
    ["MCG_InGame_Ghost_Guide"] = 113,
    ["MCG_InGame_Human_Guide"] = 113,
    ["MCG_InGame_Human"] = 113,
    ["MCG_InGame_Ghost"] = 113,
    ["MCG_InGame_GhostTips_Begin"] = 113,
    ["MCG_InGame_HumanTips_Begin"] = 113,
    ["MCG_InGame_GhostTips_Begin1"] = 113,
    ["MCG_InGame_GhostTips_Begin2"] = 113,
    ["MCG_InGame_HumanTips_Begin1"] = 113,
    ["MCG_InGame_HumanTips_Begin2"] = 113,
    ["MCG_InGame_HumanTips_Select"] = 113,
    ["MCG_InGame_GhostTips_Select"] = 113,
    ["MCG_InGame_GhostTips_SelectUI"] = 113,
    ["MCG_InGame_Second"] = 113,
    ["MCG_InGame_HumanSkill_location"] = 113,
    ["MCG_InGame_HumanSkill_Title"] = 113,
    ["MCG_InGame_HumanSkill_Title_Position"] = 113,
    ["MCG_InGame_HumanSkill_1A_Name"] = 113,
    ["MCG_InGame_HumanSkill_1A_Info"] = 113,
    ["MCG_InGame_HumanSkill_1A_Position"] = 113,
    ["MCG_InGame_HumanSkill_1B_Name"] = 113,
    ["MCG_InGame_HumanSkill_1B_Info"] = 113,
    ["MCG_InGame_HumanSkill_1C_Name"] = 113,
    ["MCG_InGame_HumanSkill_1C_Info"] = 113,
    ["MCG_InGame_HumanSkill_2A_Name"] = 113,
    ["MCG_InGame_HumanSkill_2A_Info"] = 113,
    ["MCG_InGame_HumanSkill_2A_Position"] = 113,
    ["MCG_InGame_HumanSkill_2B_Name"] = 113,
    ["MCG_InGame_HumanSkill_2B_Info"] = 113,
    ["MCG_InGame_HumanSkill_2C_Name"] = 113,
    ["MCG_InGame_HumanSkill_2C_Info"] = 113,
    ["MCG_InGame_HumanSkill_2D_Name"] = 113,
    ["MCG_InGame_HumanSkill_2D_Info"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast1"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast2"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast3"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast4"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast5"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast6"] = 113,
    ["MCG_InGame_HumanSkill_2D_Toast7"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastTang"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastAngel"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastNezha"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastDriven"] = 113,
    ["MCG_InGame_HumanSkill_2D_TeleportFailed"] = 113,
    ["MCG_InGame_HumanSkill_2D_TeleportRetry"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastForbidden"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastIsBusy"] = 113,
    ["MCG_InGame_HumanSkill_2D_ToastCD"] = 113,
    ["MCG_InGame_HumanSkill_2D_MY"] = 113,
    ["MCG_InGame_HumanSkill_2D_StarRemain"] = 113,
    ["MCG_InGame_Button_Teleport"] = 113,
    ["MCG_InGame_HumanSkill_2D_TeleportProgress"] = 113,
    ["MCG_InGame_HumanSkill_3A_Name"] = 113,
    ["MCG_InGame_HumanSkill_3A_Info"] = 113,
    ["MCG_InGame_HumanSkill_3A_Position"] = 113,
    ["MCG_InGame_HumanSkill_3B_Name"] = 113,
    ["MCG_InGame_HumanSkill_3B_Info"] = 113,
    ["MCG_InGame_HumanSkill_3C_Name"] = 113,
    ["MCG_InGame_HumanSkill_3C_Info"] = 113,
    ["MCG_InGame_HumanSkill_4A_Name"] = 113,
    ["MCG_InGame_HumanSkill_4A_Info"] = 113,
    ["MCG_InGame_HumanSkill_4A_Position"] = 113,
    ["MCG_InGame_HumanSkill_4B_Name"] = 113,
    ["MCG_InGame_HumanSkill_4B_Info"] = 113,
    ["MCG_InGame_HumanSkill_4C_Name"] = 113,
    ["MCG_InGame_HumanSkill_4C_Info"] = 113,
    ["MCG_InGame_HumanSkill_5A_Name"] = 113,
    ["MCG_InGame_HumanSkill_5A_Info"] = 113,
    ["MCG_InGame_HumanSkill_5A_Position"] = 113,
    ["MCG_InGame_HumanSkill_5B_Name"] = 113,
    ["MCG_InGame_HumanSkill_5B_Info"] = 113,
    ["MCG_InGame_HumanSkill_5C_Name"] = 113,
    ["MCG_InGame_HumanSkill_5C_Info"] = 113,
    ["MCG_InGame_HumanSkill_6A_Name"] = 113,
    ["MCG_InGame_HumanSkill_6A_Info"] = 114,
    ["MCG_InGame_HumanSkill_6A_Position"] = 114,
    ["MCG_InGame_HumanSkill_6B_Name"] = 114,
    ["MCG_InGame_HumanSkill_6B_Info"] = 114,
    ["MCG_InGame_HumanSkill_6C_Name"] = 114,
    ["MCG_InGame_HumanSkill_6C_Info"] = 114,
    ["MCG_InGame_HumanSkill_7A_Name"] = 114,
    ["MCG_InGame_HumanSkill_7A_Info"] = 114,
    ["MCG_InGame_HumanSkill_7A_Position"] = 114,
    ["MCG_InGame_HumanSkill_7B_Name"] = 114,
    ["MCG_InGame_HumanSkill_7B_Info"] = 114,
    ["MCG_InGame_HumanSkill_7C_Name"] = 114,
    ["MCG_InGame_HumanSkill_7C_Info"] = 114,
    ["MCG_InGame_HumanSkill_8A_Name"] = 114,
    ["MCG_InGame_HumanSkill_8A_Info"] = 114,
    ["MCG_InGame_HumanSkill_8A_StealthButton"] = 114,
    ["MCG_InGame_HumanSkill_8A_StealthCancel"] = 114,
    ["MCG_InGame_HumanSkill_8A_FillButton"] = 114,
    ["MCG_InGame_HumanSkill_8A_Position"] = 114,
    ["MCG_InGame_HumanSkill_8B_Name"] = 114,
    ["MCG_InGame_HumanSkill_8B_Info"] = 114,
    ["MCG_InGame_HumanSkill_8C_Name"] = 114,
    ["MCG_InGame_HumanSkill_8C_Info"] = 114,
    ["MCG_InGame_HumanSkill_9A_Name"] = 114,
    ["MCG_InGame_HumanSkill_9A_Info"] = 114,
    ["MCG_InGame_HumanSkill_9A_Position"] = 114,
    ["MCG_InGame_HumanSkill_9B_Name"] = 114,
    ["MCG_InGame_HumanSkill_9B_Info"] = 114,
    ["MCG_InGame_HumanSkill_9C_Name"] = 114,
    ["MCG_InGame_HumanSkill_9C_Info"] = 114,
    ["MCG_InGame_HumanSkill_10A_Name"] = 114,
    ["MCG_InGame_HumanSkill_10A_Info"] = 114,
    ["MCG_InGame_HumanSkill_10A_Position"] = 114,
    ["MCG_InGame_HumanSkill_10B_Name"] = 114,
    ["MCG_InGame_HumanSkill_10B_Info"] = 114,
    ["MCG_InGame_HumanSkill_10C_Name"] = 114,
    ["MCG_InGame_HumanSkill_10C_Info"] = 114,
    ["MCG_InGame_HumanSkill_11A_Name"] = 114,
    ["MCG_InGame_HumanSkill_11A_Info"] = 114,
    ["MCG_InGame_HumanSkill_11A_Position"] = 114,
    ["MCG_InGame_HumanSkill_11B_Name"] = 114,
    ["MCG_InGame_HumanSkill_11B_Info"] = 114,
    ["MCG_InGame_HumanSkill_11C_Name"] = 114,
    ["MCG_InGame_HumanSkill_11C_Info"] = 114,
    ["MCG_InGame_HumanSkill_12A_Name"] = 114,
    ["MCG_InGame_HumanSkill_12A_Info"] = 114,
    ["MCG_InGame_HumanSkill_12A_Position"] = 114,
    ["MCG_InGame_HumanSkill_12B_Name"] = 114,
    ["MCG_InGame_HumanSkill_12B_Info"] = 114,
    ["MCG_InGame_HumanSkill_12C_Name"] = 114,
    ["MCG_InGame_HumanSkill_12C_Info"] = 114,
    ["MCG_InGame_HumanSkill_13A_Name"] = 114,
    ["MCG_InGame_HumanSkill_13A_Info"] = 114,
    ["MCG_InGame_HumanSkill_13A_Position"] = 114,
    ["MCG_InGame_HumanSkill_13B_Name"] = 114,
    ["MCG_InGame_HumanSkill_13B_Info"] = 114,
    ["MCG_InGame_HumanSkill_13C_Name"] = 114,
    ["MCG_InGame_HumanSkill_13C_Info"] = 114,
    ["MCG_InGame_HumanSkill_14A_Name"] = 114,
    ["MCG_InGame_HumanSkill_14A_Info"] = 114,
    ["MCG_InGame_HumanSkill_14A_Position"] = 114,
    ["MCG_InGame_HumanSkill_14B_Name"] = 114,
    ["MCG_InGame_HumanSkill_14B_Info"] = 114,
    ["MCG_InGame_HumanSkill_14C_Name"] = 114,
    ["MCG_InGame_HumanSkill_14C_Info"] = 114,
    ["MCG_InGame_HumanSkill_15A_Name"] = 114,
    ["MCG_InGame_HumanSkill_15A_Info"] = 114,
    ["MCG_InGame_HumanSkill_15A_Position"] = 114,
    ["MCG_InGame_HumanSkill_15B_Name"] = 114,
    ["MCG_InGame_HumanSkill_15B_Info"] = 114,
    ["MCG_InGame_HumanSkill_15C_Name"] = 114,
    ["MCG_InGame_HumanSkill_15C_Info"] = 114,
    ["MCG_InGame_HumanSkill_16A_Name"] = 114,
    ["MCG_InGame_HumanSkill_16A_Info"] = 114,
    ["MCG_InGame_HumanSkill_16A_Position"] = 114,
    ["MCG_InGame_HumanSkill_16B_Name"] = 114,
    ["MCG_InGame_HumanSkill_16B_Info"] = 114,
    ["MCG_InGame_ShareDamage"] = 114,
    ["MCG_InGame_HumanSkill_16C_Name"] = 114,
    ["MCG_InGame_HumanSkill_16C_Info"] = 114,
    ["MCG_InGame_HumanSkill_17A_Name"] = 114,
    ["MCG_InGame_HumanSkill_17A_Info"] = 114,
    ["MCG_InGame_HumanSkill_17A_Position"] = 114,
    ["MCG_InGame_Button_GetOn"] = 114,
    ["MCG_InGame_Button_GetOff"] = 114,
    ["MCG_InGame_Button_Horn"] = 114,
    ["MCG_InGame_Button_GetOn_HurryUp"] = 114,
    ["MCG_InGame_CannotPlaceVehicleNotifyTip"] = 114,
    ["MCG_InGame_CannotPlaceGetOffNotifyTip"] = 114,
    ["MCG_InGame_HumanSkill_17B_Name"] = 114,
    ["MCG_InGame_HumanSkill_17B_Info"] = 114,
    ["MCG_InGame_HumanSkill_17C_Name"] = 114,
    ["MCG_InGame_HumanSkill_17C_Info"] = 114,
    ["MCG_InGame_HumanSkill_18A_Name"] = 114,
    ["MCG_InGame_HumanSkill_18A_Info"] = 114,
    ["MCG_InGame_HumanSkill_18A_Position"] = 114,
    ["MCG_InGame_HumanSkill_18B_Name"] = 114,
    ["MCG_InGame_HumanSkill_18B_Info"] = 114,
    ["MCG_InGame_HumanSkill_18C_Name"] = 114,
    ["MCG_InGame_HumanSkill_18C_Info"] = 114,
    ["MCG_InGame_HumanSkill_19A_Name"] = 115,
    ["MCG_InGame_HumanSkill_19A_Info"] = 115,
    ["MCG_InGame_HumanSkill_19A_Position"] = 115,
    ["MCG_InGame_HumanSkill_19B_Name"] = 115,
    ["MCG_InGame_HumanSkill_19B_Info"] = 115,
    ["MCG_InGame_HumanSkill_19C_Name"] = 115,
    ["MCG_InGame_HumanSkill_19C_Info"] = 115,
    ["MCG_InGame_HumanSkill_20A_Name"] = 115,
    ["MCG_InGame_HumanSkill_20A_Info"] = 115,
    ["MCG_InGame_HumanSkill_20A_Position"] = 115,
    ["MCG_InGame_HumanSkill_20B_Name"] = 115,
    ["MCG_InGame_HumanSkill_20B_Info"] = 115,
    ["MCG_InGame_HumanSkill_20C_Name"] = 115,
    ["MCG_InGame_HumanSkill_20C_Info"] = 115,
    ["MCG_InGame_HumanSkill_21A_Name"] = 115,
    ["MCG_InGame_HumanSkill_21A_Info"] = 115,
    ["MCG_InGame_HumanSkill_21A_Position"] = 115,
    ["MCG_InGame_HumanSkill_21A_Toast"] = 115,
    ["MCG_InGame_HumanSkill_21B_Name"] = 115,
    ["MCG_InGame_HumanSkill_21B_Info"] = 115,
    ["MCG_InGame_HumanSkill_21C_Name"] = 115,
    ["MCG_InGame_HumanSkill_21C_Info"] = 115,
    ["MCG_InGame_HumanSkill_22A_Name"] = 115,
    ["MCG_InGame_HumanSkill_22A_Info"] = 115,
    ["MCG_InGame_HumanSkill_22A_Position"] = 115,
    ["MCG_InGame_HumanSkill_22B_Name"] = 115,
    ["MCG_InGame_HumanSkill_22B_Info"] = 115,
    ["MCG_InGame_HumanSkill_22C_Name"] = 115,
    ["MCG_InGame_HumanSkill_22C_Info"] = 115,
    ["MCG_InGame_HumanSkill_23A_Name"] = 115,
    ["MCG_InGame_HumanSkill_23A_Info"] = 115,
    ["MCG_InGame_HumanSkill_23A_Position"] = 115,
    ["MCG_InGame_HumanSkill_23B_Name"] = 115,
    ["MCG_InGame_HumanSkill_23B_Info"] = 115,
    ["MCG_InGame_HumanSkill_23C_Name"] = 115,
    ["MCG_InGame_HumanSkill_23C_Info"] = 115,
    ["MCG_InGame_HumanSkill23_BTN_GetOn"] = 115,
    ["MCG_InGame_HumanSkill23_BTN_GetOff"] = 115,
    ["MCG_InGame_HumanSkill_24A_Name"] = 115,
    ["MCG_InGame_HumanSkill_24A_Info"] = 115,
    ["MCG_InGame_HumanSkill_24A_Position"] = 115,
    ["MCG_InGame_HumanSkill_24A_Toast"] = 115,
    ["MCG_InGame_HumanSkill_25A_Name"] = 115,
    ["MCG_InGame_HumanSkill_25A_Info"] = 115,
    ["MCG_InGame_HumanSkill_25A_Position"] = 115,
    ["MCG_InGame_HumanSkill_25B_Name"] = 115,
    ["MCG_InGame_HumanSkill_25B_Info"] = 115,
    ["MCG_InGame_HumanSkill_25C_Name"] = 115,
    ["MCG_InGame_HumanSkill_25C_Info"] = 115,
    ["MCG_InGame_HumanSkill_26A_Name"] = 115,
    ["MCG_InGame_HumanSkill_26A_Info"] = 115,
    ["MCG_InGame_HumanSkill_26A_Position"] = 115,
    ["MCG_InGame_HumanSkill_26B_Name"] = 115,
    ["MCG_InGame_HumanSkill_26B_Info"] = 115,
    ["MCG_InGame_HumanSkill_26C_Name"] = 115,
    ["MCG_InGame_HumanSkill_26C_Info"] = 115,
    ["MCG_InGame_HumanSkill_27A_Name"] = 115,
    ["MCG_InGame_HumanSkill_27A_Info"] = 115,
    ["MCG_InGame_HumanSkill_27A_NotifyTip"] = 115,
    ["MCG_InGame_HumanSkill_27A_NotifyTip2"] = 115,
    ["MCG_InGame_HumanSkill_27A_Position"] = 115,
    ["MCG_InGame_HumanSkill_27B_Name"] = 115,
    ["MCG_InGame_HumanSkill_27B_Info"] = 115,
    ["MCG_InGame_HumanSkill_27C_Name"] = 115,
    ["MCG_InGame_HumanSkill_27C_Info"] = 115,
    ["MCG_InGame_HumanSkill_28A_Name"] = 115,
    ["MCG_InGame_HumanSkill_28A_Info"] = 115,
    ["MCG_InGame_HumanSkill_28A_Position"] = 115,
    ["MCG_InGame_HumanSkill_28B_Name"] = 115,
    ["MCG_InGame_HumanSkill_28B_Info"] = 115,
    ["MCG_InGame_HumanSkill_28C_Name"] = 115,
    ["MCG_InGame_HumanSkill_28C_Info"] = 115,
    ["MCG_InGame_HumanSkill_29A_Name"] = 115,
    ["MCG_InGame_HumanSkill_29A_Info"] = 115,
    ["MCG_InGame_HumanSkill_29A_Position"] = 115,
    ["MCG_InGame_HumanSkill_29A_Toast"] = 115,
    ["MCG_InGame_HumanSkill_29B_Name"] = 115,
    ["MCG_InGame_HumanSkill_29B_Info"] = 115,
    ["MCG_InGame_HumanSkill_29C_Name"] = 115,
    ["MCG_InGame_HumanSkill_29C_Info"] = 115,
    ["MCG_InGame_HumanSkill_30A_Name"] = 115,
    ["MCG_InGame_HumanSkill_30A_Info"] = 115,
    ["MCG_InGame_HumanSkill_30A_Position"] = 115,
    ["MCG_InGame_HumanSkill_30B_Name"] = 115,
    ["MCG_InGame_HumanSkill_30B_Info"] = 115,
    ["MCG_InGame_HumanSkill_30C_Name"] = 115,
    ["MCG_InGame_HumanSkill_30C_Info"] = 115,
    ["MCG_InGame_HumanSkill_31A_Name"] = 115,
    ["MCG_InGame_HumanSkill_31A_Info"] = 115,
    ["MCG_InGame_HumanSkill_31A_Position"] = 115,
    ["MCG_InGame_HumanSkill_31A_Toast"] = 115,
    ["MCG_InGame_HumanSkill_31B_Name"] = 115,
    ["MCG_InGame_HumanSkill_31B_Info"] = 115,
    ["MCG_InGame_HumanSkill_31C_Name"] = 115,
    ["MCG_InGame_HumanSkill_31C_Info"] = 115,
    ["MCG_InGame_HumanSkill_32A_Name"] = 115,
    ["MCG_InGame_HumanSkill_32A_Info"] = 115,
    ["MCG_InGame_HumanSkill_32A_Position"] = 115,
    ["MCG_InGame_HumanSkill_32B_Name"] = 115,
    ["MCG_InGame_HumanSkill_32B_Info"] = 115,
    ["MCG_InGame_HumanSkill_32C_Name"] = 116,
    ["MCG_InGame_HumanSkill_32C_Info"] = 116,
    ["MCG_InGame_HumanSkill_33A_Name"] = 116,
    ["MCG_InGame_HumanSkill_33A_Position"] = 116,
    ["MCG_InGame_HumanSkill_33B_Name"] = 116,
    ["MCG_InGame_HumanSkill_33B_Info"] = 116,
    ["MCG_InGame_HumanSkill_33C_Name"] = 116,
    ["MCG_InGame_HumanSkill_33C_Info"] = 116,
    ["MCG_InGame_HumanSkill_33D_Name"] = 116,
    ["MCG_InGame_HumanSkill_33D_Info"] = 116,
    ["MCG_InGame_HumanSkill_Vip01A_Name"] = 116,
    ["MCG_InGame_HumanSkill_Vip01A_Info"] = 116,
    ["MCG_InGame_HumanSkill_Vip01A_Position"] = 116,
    ["MCG_InGame_HumanSkill_Vip01B_Name"] = 116,
    ["MCG_InGame_HumanSkill_Vip01B_Info"] = 116,
    ["MCG_InGame_HumanSkill_Vip01C_Name"] = 116,
    ["MCG_InGame_HumanSkill_Vip01C_Info"] = 116,
    ["MCG_InGame_GhostA_Name"] = 116,
    ["MCG_InGame_GhostA_Story"] = 116,
    ["MCG_InGame_GhostA_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostA_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostA_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostA_Skill_1_Trap"] = 116,
    ["MCG_InGame_TrapMaxNotifyTip"] = 116,
    ["MCG_InGame_GhostA_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostA_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostA_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostA_Skill_3_Name"] = 116,
    ["MCG_InGame_GhostA_Skill_3_Info"] = 116,
    ["MCG_InGame_GhostA_Skill_4_Name"] = 116,
    ["MCG_InGame_GhostA_Skill_4_Info"] = 116,
    ["MCG_InGame_GhostA_Skill_4_FullInfo"] = 116,
    ["MCG_InGame_Button_PullDown"] = 116,
    ["MCG_InGame_PullDown_Progress"] = 116,
    ["MCG_InGame_HumanState_Trapped"] = 116,
    ["MCG_InGame_StartTrappedNotifyTip"] = 116,
    ["MCG_InGame_EscapingTrap_Progress"] = 116,
    ["MCG_InGame_Trap_SaveProgress"] = 116,
    ["MCG_InGame_GhostB_Name"] = 116,
    ["MCG_InGame_GhostB_Story"] = 116,
    ["MCG_InGame_GhostB_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostB_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostB_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostB_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostB_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostB_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostB_Skill_3_Name"] = 116,
    ["MCG_InGame_GhostB_Skill_3_Info"] = 116,
    ["MCG_InGame_GhostC_Name"] = 116,
    ["MCG_InGame_GhostC_Info"] = 116,
    ["MCG_InGame_GhostC_Story"] = 116,
    ["MCG_InGame_GhostC_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostC_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostC_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostC_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostC_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostC_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostC_Skill_3_Name"] = 116,
    ["MCG_InGame_GhostC_Skill_3_Info"] = 116,
    ["MCG_InGame_GhostC_Skill_4_Name"] = 116,
    ["MCG_InGame_GhostC_Skill_4_Info"] = 116,
    ["MCG_InGame_GhostC_Skill_4_FullInfo"] = 116,
    ["MCG_InGame_GhostD_Name"] = 116,
    ["MCG_InGame_GhostD_ShortName"] = 116,
    ["MCG_InGame_GhostD_Story"] = 116,
    ["MCG_InGame_GhostD_Speak1"] = 116,
    ["MCG_InGame_GhostD_Speak2"] = 116,
    ["MCG_InGame_GhostD_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostD_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostD_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostD_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostD_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostD_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostD_Skill_3_Name"] = 116,
    ["MCG_InGame_GhostD_Skill_3_Info"] = 116,
    ["MCG_InGame_GhostD_Skill_3_FullInfo"] = 116,
    ["MCG_InGame_GhostE_Name"] = 116,
    ["MCG_InGame_GhostE_ShortName"] = 116,
    ["MCG_InGame_GhostE_Story"] = 116,
    ["MCG_InGame_GhostE_Speak1"] = 116,
    ["MCG_InGame_GhostE_Speak2"] = 116,
    ["MCG_InGame_GhostE_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostE_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostE_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostE_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostE_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostE_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostF_Name"] = 116,
    ["MCG_InGame_GhostF_ShortName"] = 116,
    ["MCG_InGame_GhostF_Story"] = 116,
    ["MCG_InGame_GhostF_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostF_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostF_Skill_1_FullInfo"] = 116,
    ["MCG_InGame_GhostF_Skill_2_Name"] = 116,
    ["MCG_InGame_GhostF_Skill_2_Info"] = 116,
    ["MCG_InGame_GhostF_Skill_2_FullInfo"] = 116,
    ["MCG_InGame_GhostG_Name"] = 116,
    ["MCG_InGame_GhostG_Story"] = 116,
    ["MCG_InGame_GhostG_Skill_1_Name"] = 116,
    ["MCG_InGame_GhostG_Skill_1_Info"] = 116,
    ["MCG_InGame_GhostG_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostG_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostG_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostG_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostH_Name"] = 117,
    ["MCG_InGame_GhostH_Story"] = 117,
    ["MCG_InGame_GhostH_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostH_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostH_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostH_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostH_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostH_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostH_Sad"] = 117,
    ["MCG_InGame_GhostH_Sad_Info"] = 117,
    ["MCG_InGame_GhostI_Name"] = 117,
    ["MCG_InGame_GhostI_ShortName"] = 117,
    ["MCG_InGame_GhostI_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostI_Skill_1_BtnStop"] = 117,
    ["MCG_InGame_GhostI_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostI_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostI_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostI_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostI_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostJ_Name"] = 117,
    ["MCG_InGame_GhostJ_ShortName"] = 117,
    ["MCG_InGame_GhostJ_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostJ_Skill_1_BtnFall"] = 117,
    ["MCG_InGame_GhostJ_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostJ_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostJ_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostJ_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostJ_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostK_Name"] = 117,
    ["MCG_InGame_GhostK_ShortName"] = 117,
    ["MCG_InGame_GhostK_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostK_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostK_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostK_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostK_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostK_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostL_Name"] = 117,
    ["MCG_InGame_GhostL_ShortName"] = 117,
    ["MCG_InGame_GhostL_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostL_Skill_1_BtnStop"] = 117,
    ["MCG_InGame_GhostL_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostL_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostL_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostL_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostL_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostM_Name"] = 117,
    ["MCG_InGame_GhostM_ShortName"] = 117,
    ["MCG_InGame_GhostM_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostM_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostM_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostM_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostM_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostM_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostM_Mind"] = 117,
    ["MCG_InGame_GhostM_Mind_Info"] = 117,
    ["MCG_InGame_GhostN_Name"] = 117,
    ["MCG_InGame_GhostN_ShortName"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostN_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Toast1"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Toast2"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Toast3"] = 117,
    ["MCG_InGame_GhostN_Skill_1_Progress"] = 117,
    ["MCG_InGame_GhostN_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostN_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostN_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostN_Skill_2_Button"] = 117,
    ["MCG_InGame_GhostO_Name"] = 117,
    ["MCG_InGame_GhostO_ShortName"] = 117,
    ["MCG_InGame_GhostO_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostO_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostO_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostO_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostO_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostO_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostP_Name"] = 117,
    ["MCG_InGame_GhostP_ShortName"] = 117,
    ["MCG_InGame_GhostP_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostP_Skill_1_BtnStop"] = 117,
    ["MCG_InGame_GhostP_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostP_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostP_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostP_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostP_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_GhostQ_Name"] = 117,
    ["MCG_InGame_GhostQ_ShortName"] = 117,
    ["MCG_InGame_GhostQ_Skill_1_Name"] = 117,
    ["MCG_InGame_GhostQ_Skill_1_Info"] = 117,
    ["MCG_InGame_GhostQ_Skill_1_FullInfo"] = 117,
    ["MCG_InGame_GhostQ_Skill_1_ToastName"] = 117,
    ["MCG_InGame_GhostQ_Skill_1_ToastText"] = 117,
    ["MCG_InGame_GhostQ_Skill_2_Name"] = 117,
    ["MCG_InGame_GhostQ_Skill_2_Info"] = 117,
    ["MCG_InGame_GhostQ_Skill_2_FullInfo"] = 117,
    ["MCG_InGame_Ghost_BigKing"] = 117,
    ["MCG_InGame_Ghost_BigKing_Skill"] = 118,
    ["MCG_InGame_Ghost_BigKing_Skill_Info"] = 118,
    ["MCG_InGame_Ghost_DoubleKings_Skill1"] = 118,
    ["MCG_InGame_Ghost_DoubleKings_Skill2"] = 118,
    ["MCG_InGame_HumanGoal_Title"] = 118,
    ["MCG_InGame_HumanGoal1_Text"] = 118,
    ["MCG_InGame_HumanGoal2_Text"] = 118,
    ["MCG_InGame_HumanGoal3_Text"] = 118,
    ["MCG_InGame_GhostGoal_Title"] = 118,
    ["MCG_InGame_GhostGoal_Text"] = 118,
    ["MCG_InGame_HumanState_Down"] = 118,
    ["MCG_InGame_HumanState_Caught"] = 118,
    ["MCG_InGame_HumanState_Bound"] = 118,
    ["MCG_InGame_HumanState_Eaten"] = 118,
    ["MCG_InGame_HumanState_Escape"] = 118,
    ["MCG_InGame_HelpHuman_Button"] = 118,
    ["MCG_InGame_HelpHuman_Progress"] = 118,
    ["MCG_InGame_HelpHuman_DownMessage"] = 118,
    ["MCG_InGame_HelpHuman_Message"] = 118,
    ["MCG_InGame_HelpHuman_DoneMessage"] = 118,
    ["MCG_InGame_Struggle_Message"] = 118,
    ["MCG_InGame_HumanMachine_Name"] = 118,
    ["MCG_InGame_HumanMachine_Button"] = 118,
    ["MCG_InGame_HumanMachine_Progress"] = 118,
    ["MCG_InGame_HumanMachine_Story1"] = 118,
    ["MCG_InGame_HumanMachine_Story2"] = 118,
    ["MCG_InGame_HumanMachine_Story3"] = 118,
    ["MCG_InGame_HumanMachine_Story4"] = 118,
    ["MCG_InGame_HumanMachine_Story5"] = 118,
    ["MCG_InGame_HumanMachine_Minigame"] = 118,
    ["MCG_InGame_HumanMachine_Prefect"] = 118,
    ["MCG_InGame_HumanMachine_Good"] = 118,
    ["MCG_InGame_HumanMachine_Miss"] = 118,
    ["MCG_InGame_HumanMachine_Done_Human"] = 118,
    ["MCG_InGame_HumanMachine_Done_Ghost"] = 118,
    ["MCG_InGame_HumanMachine_AllDone_Human"] = 118,
    ["MCG_InGame_HumanMachine_AllDone_Ghost"] = 118,
    ["MCG_InGame_HumanMachine_Toast_Nezha"] = 118,
    ["MCG_InGame_Door_Name"] = 118,
    ["MCG_InGame_Door_Button"] = 118,
    ["MCG_InGame_Door_Progress"] = 118,
    ["MCG_InGame_Door_Open_Human"] = 118,
    ["MCG_InGame_Door_Open_Ghost"] = 118,
    ["MCG_InGame_GhostAnger_Progress"] = 118,
    ["MCG_InGame_GhostAnger_State"] = 118,
    ["MCG_InGame_GhostAnger_Message"] = 118,
    ["MCG_InGame_GhostAnger_Message2"] = 118,
    ["MCG_InGame_GhostAnger_Rule"] = 118,
    ["MCG_InGame_GhostAnger_Title1"] = 118,
    ["MCG_InGame_GhostAnger_Info1"] = 118,
    ["MCG_InGame_GhostAnger_Title2"] = 118,
    ["MCG_InGame_GhostAnger_Info2"] = 118,
    ["MCG_InGame_GhostAnger_Title3"] = 118,
    ["MCG_InGame_GhostAnger_Info3"] = 118,
    ["MCG_InGame_GhostAnger_Title4"] = 118,
    ["MCG_InGame_GhostAnger_Info4"] = 118,
    ["MCG_InGame_GhostAnger_Title5"] = 118,
    ["MCG_InGame_GhostAnger_Info5"] = 118,
    ["MCG_InGame_GhostAnger_Title6"] = 118,
    ["MCG_InGame_GhostAnger_Info6"] = 118,
    ["MCG_InGame_Struggle_Progress"] = 118,
    ["MCG_InGame_ClimbWindow"] = 118,
    ["MCG_InGame_Surface_Button"] = 118,
    ["MCG_InGame_CatchButton_Ghost"] = 118,
    ["MCG_InGame_CatchButton_Human"] = 118,
    ["MCG_InGame_CatchButton_Ghost2"] = 118,
    ["MCG_InGame_GhostMachine_Name"] = 118,
    ["MCG_InGame_GhostMachine_Botton"] = 118,
    ["MCG_InGame_GhostMachine_DeathProgress"] = 118,
    ["MCG_InGame_GhostMachine_Message"] = 118,
    ["MCG_InGame_GhostMachine_SaveBotton"] = 118,
    ["MCG_InGame_GhostMachine_SaveProgress"] = 118,
    ["MCG_InGame_GhostMachine_SaveMessage"] = 118,
    ["MCG_InGame_GhostMachine_FreeMessage"] = 118,
    ["MCG_InGame_GhostMachine_SaveMessageB"] = 118,
    ["MCG_InGame_GhostMachine_FreeMessageB"] = 118,
    ["MCG_InGame_3KGhostSkill_Bottle_Message"] = 118,
    ["MCG_InGame_3KGhostSkill_Bottle_MessageG"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_Message"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_MessageG"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_AskHuman"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_AskGhost"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_Answer"] = 118,
    ["MCG_InGame_3KGhostSkill_Gourd_AnswerName"] = 118,
    ["MCG_InGame_4KGhostSkill_Gourd_AnswerNotice"] = 118,
    ["MCG_InGame_TrapBotton_Human"] = 118,
    ["MCG_InGame_TrapBotton_Ghost"] = 118,
    ["MCG_InGame_Win_Ghost"] = 118,
    ["MCG_InGame_Lose_Ghost"] = 118,
    ["MCG_InGame_Draw"] = 118,
    ["MCG_InGame_Win_Human"] = 118,
    ["MCG_InGame_Lose_Human"] = 118,
    ["MCG_Settlement_HumanWin"] = 118,
    ["MCG_Settlement_GhostWin"] = 118,
    ["MCG_Settlement_HumanState_Ingame"] = 118,
    ["MCG_Settlement_HumanState_Escape"] = 118,
    ["MCG_Settlement_HumanState_Lose"] = 118,
    ["MCG_Settlement_Human"] = 118,
    ["MCG_Settlement_Ghost"] = 118,
    ["MCG_Settlement_Score"] = 118,
    ["MCG_Settlement_Search"] = 119,
    ["MCG_Settlement_Save"] = 119,
    ["MCG_Settlement_Save2"] = 119,
    ["MCG_Settlement_Save3"] = 119,
    ["MCG_Settlement_Destroy"] = 119,
    ["MCG_Settlement_Kill"] = 119,
    ["MCG_Settlement_Hit"] = 119,
    ["MCG_Settlement_Pin"] = 119,
    ["MCG_Settlement_Eliminate"] = 119,
    ["MCG_ModeInstruction"] = 119,
    ["MCG_ModeInstruction_MainRule"] = 119,
    ["MCG_ModeInstruction_MainRule_Text"] = 119,
    ["MCG_ModeInstruction_MainRule_Text2"] = 119,
    ["MCG_ModeInstruction_Human"] = 119,
    ["MCG_ModeInstruction_HumanRule"] = 119,
    ["MCG_ModeInstruction_HumanSkill"] = 119,
    ["MCG_ModeInstruction_WinCondition"] = 119,
    ["MCG_ModeInstruction_WinCondition_HumanTitle"] = 119,
    ["MCG_ModeInstruction_WinCondition_GhostTitle"] = 119,
    ["MCG_ModeInstruction_WinCondition_Human"] = 119,
    ["MCG_ModeInstruction_WinCondition_Human2"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KHumanText"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KHumanText_Angel"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KHumanText_Nezha"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KGhostText"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KGhostText_Angel"] = 119,
    ["MCG_ModeInstruction_WinCondition_3KGhostText_Nezha"] = 119,
    ["MCG_ModeInstruction_HumanMachine"] = 119,
    ["MCG_ModeInstruction_HumanMachine_Text"] = 119,
    ["MCG_ModeInstruction_Door"] = 119,
    ["MCG_ModeInstruction_Door_Text"] = 119,
    ["MCG_ModeInstruction_3K_PSD"] = 119,
    ["MCG_ModeInstruction_3K_PSD_Text"] = 119,
    ["MCG_ModeInstruction_3K_FireWall"] = 119,
    ["MCG_ModeInstruction_3K_FireWall_Text"] = 119,
    ["MCG_ModeInstruction_3K_Tang"] = 119,
    ["MCG_ModeInstruction_3K_Tang_Text"] = 119,
    ["MCG_ModeInstruction_3K_Wukong"] = 119,
    ["MCG_ModeInstruction_3K_Wukong_Text"] = 119,
    ["MCG_ModeInstruction_3K_Angel"] = 119,
    ["MCG_ModeInstruction_3K_Angel_Text"] = 119,
    ["MCG_ModeInstruction_3K_Werewolf"] = 119,
    ["MCG_ModeInstruction_3K_Werewolf_Text"] = 119,
    ["MCG_ModeInstruction_3K_Bajie"] = 119,
    ["MCG_ModeInstruction_3K_Bajie_Text"] = 119,
    ["MCG_ModeInstruction_3K_Shaseng"] = 119,
    ["MCG_ModeInstruction_3K_Shaseng_Text"] = 119,
    ["MCG_ModeInstruction_3K_MagicWeapon"] = 119,
    ["MCG_ModeInstruction_3K_MagicWeapon_Text"] = 119,
    ["MCG_ModeInstruction_Skill1"] = 119,
    ["MCG_ModeInstruction_Skill1_Text"] = 119,
    ["MCG_ModeInstruction_Skill2"] = 119,
    ["MCG_ModeInstruction_Skill2_Text"] = 119,
    ["MCG_ModeInstruction_Skill3"] = 119,
    ["MCG_ModeInstruction_Skill3_Text"] = 119,
    ["MCG_ModeInstruction_Skill4"] = 119,
    ["MCG_ModeInstruction_Skill4_Text"] = 119,
    ["MCG_ModeInstruction_Skill5"] = 119,
    ["MCG_ModeInstruction_Skill5_Text"] = 119,
    ["MCG_ModeInstruction_Skill6"] = 119,
    ["MCG_ModeInstruction_Skill6_Text"] = 119,
    ["MCG_ModeInstruction_Skill7"] = 119,
    ["MCG_ModeInstruction_Skill7_Text"] = 119,
    ["MCG_ModeInstruction_Skill8"] = 119,
    ["MCG_ModeInstruction_Skill8_Text"] = 119,
    ["MCG_ModeInstruction_Skill9"] = 119,
    ["MCG_ModeInstruction_Skill9_Text"] = 119,
    ["MCG_ModeInstruction_Skill10"] = 119,
    ["MCG_ModeInstruction_Skill10_Text"] = 119,
    ["MCG_ModeInstruction_Skill11"] = 119,
    ["MCG_ModeInstruction_Skill11_Text"] = 119,
    ["MCG_ModeInstruction_3KSkillTang"] = 119,
    ["MCG_ModeInstruction_3KSkillTang_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill9"] = 119,
    ["MCG_ModeInstruction_3KSkill9_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill10"] = 119,
    ["MCG_ModeInstruction_3KSkill10_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill11"] = 119,
    ["MCG_ModeInstruction_3KSkill11_Text"] = 119,
    ["MCG_ModeInstruction_Skill12"] = 119,
    ["MCG_ModeInstruction_Skill12_Text"] = 119,
    ["MCG_ModeInstruction_Skill13"] = 119,
    ["MCG_ModeInstruction_Skill13_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill12"] = 119,
    ["MCG_ModeInstruction_3KSkill12_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill13"] = 119,
    ["MCG_ModeInstruction_3KSkill13_Text"] = 119,
    ["MCG_ModeInstruction_Skill14"] = 119,
    ["MCG_ModeInstruction_Skill14_Text"] = 119,
    ["MCG_ModeInstruction_Skill15"] = 119,
    ["MCG_ModeInstruction_Skill15_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill15"] = 119,
    ["MCG_ModeInstruction_3KSkill15_Text"] = 119,
    ["MCG_ModeInstruction_Skill16"] = 119,
    ["MCG_ModeInstruction_Skill16_Text"] = 119,
    ["MCG_ModeInstruction_3KSkillAngel"] = 119,
    ["MCG_ModeInstruction_3KSkillAngel_Text"] = 119,
    ["MCG_ModeInstruction_3KSkillWerewolf"] = 119,
    ["MCG_ModeInstruction_3KSkillWerewolf_Text"] = 119,
    ["MCG_ModeInstruction_3KSkill7"] = 119,
    ["MCG_ModeInstruction_3KSkill7_Text"] = 120,
    ["MCG_ModeInstruction_3KSkill10_Vampire"] = 120,
    ["MCG_ModeInstruction_3KSkill10_Text_Vampire"] = 120,
    ["MCG_ModeInstruction_Skill17"] = 120,
    ["MCG_ModeInstruction_Skill17_Text"] = 120,
    ["MCG_ModeInstruction_Skill18"] = 120,
    ["MCG_ModeInstruction_Skill18_Text"] = 120,
    ["MCG_ModeInstruction_3KSkill17"] = 120,
    ["MCG_ModeInstruction_3KSkill17_Text"] = 120,
    ["MCG_ModeInstruction_3KSkill18"] = 120,
    ["MCG_ModeInstruction_3KSkill18_Text"] = 120,
    ["MCG_ModeInstruction_3KSkill5"] = 120,
    ["MCG_ModeInstruction_3KSkill5_Text"] = 120,
    ["MCG_ModeInstruction_VIPSkill1"] = 120,
    ["MCG_ModeInstruction_VIPSkill1_Text"] = 120,
    ["MCG_ModeInstruction_Ghost"] = 120,
    ["MCG_ModeInstruction_GhostRule"] = 120,
    ["MCG_ModeInstruction_GhostSkill"] = 120,
    ["MCG_ModeInstruction_WinCondition_Ghost"] = 120,
    ["MCG_ModeInstruction_WinCondition_Ghost2"] = 120,
    ["MCG_ModeInstruction_FindHuman"] = 120,
    ["MCG_ModeInstruction_FindHuman_Text"] = 120,
    ["MCG_ModeInstruction_GhostMachine"] = 120,
    ["MCG_ModeInstruction_GhostMachine_Text"] = 120,
    ["MCG_ModeInstruction_Anger"] = 120,
    ["MCG_ModeInstruction_Anger_Text"] = 120,
    ["MCG_ModeInstruction_Ghost1"] = 120,
    ["MCG_ModeInstruction_Ghost1_Text"] = 120,
    ["MCG_ModeInstruction_Ghost2"] = 120,
    ["MCG_ModeInstruction_Ghost2_Text"] = 120,
    ["MCG_ModeInstruction_Ghost3_Text1"] = 120,
    ["MCG_ModeInstruction_Ghost3_Text2"] = 120,
    ["MCG_ModeInstruction_Ghost4_Text"] = 120,
    ["MCG_ModeInstruction_Ghost5_Text"] = 120,
    ["MCG_ModeInstruction_Ghost6_Text"] = 120,
    ["MCG_ModeInstruction_Ghost7_Text"] = 120,
    ["MCG_ModeInstruction_Ghost8_Text"] = 120,
    ["MCG_ModeInstruction_Ghost9_Text"] = 120,
    ["MCG_ModeInstruction_Ghost10_Text"] = 120,
    ["MCG_ModeInstruction_Ghost11_Text"] = 120,
    ["MCG_ModeInstruction_Ghost12_Text"] = 120,
    ["MCG_ModeInstruction_Ghost13_Text"] = 120,
    ["MCG_ModeInstruction_Ghost14_Text"] = 120,
    ["MCG_ModeInstruction_Ghost15_Text"] = 120,
    ["MCG_ModeInstruction_Ghost16_Text"] = 120,
    ["MCG_ModeInstruction_Ghost17_Text"] = 120,
    ["MCG_ModeInstruction_NoticeGhost"] = 120,
    ["MCG_ModeInstruction_NoticeGhost_Text"] = 120,
    ["MCG_ModeInstruction_Board"] = 120,
    ["MCG_ModeInstruction_Board_Text"] = 120,
    ["MCG_ModeInstruction_Save"] = 120,
    ["MCG_ModeInstruction_Save_Text"] = 120,
    ["MCG_ModeInstruction_Save2"] = 120,
    ["MCG_ModeInstruction_Save2_Text"] = 120,
    ["MCG_ModeInstruction_ScanHuman"] = 120,
    ["MCG_ModeInstruction_ScanHuman_Text"] = 120,
    ["MCG_InGame_Speech_Human"] = 120,
    ["MCG_InGame_Speech_Ghost"] = 120,
    ["MCG_InGame_Speech_Side"] = 120,
    ["MCG_InGame_Speech_NearBoss"] = 120,
    ["MCG_InGame_Speech_NeadHelp"] = 120,
    ["MCG_InGame_Speech_Saveme"] = 120,
    ["MCG_InGame_Speech_Gotohelp"] = 120,
    ["MCG_InGame_Speech_GotoSave"] = 120,
    ["MCG_InGame_Speech_HumanMachine"] = 120,
    ["MCG_InGame_Speech_Door"] = 120,
    ["MCG_InGame_Speech_Escape"] = 120,
    ["MCG_InGame_Speech_DonotSave"] = 120,
    ["MCG_InGame_Speech_FollowMe"] = 120,
    ["MCG_InGame_Speech_FacetoBoss"] = 120,
    ["MCG_InGame_Speech_ThankYou"] = 120,
    ["MCG_InGame_Speech_Ghost1"] = 120,
    ["MCG_InGame_Speech_Ghost2"] = 120,
    ["MCG_InGame_Speech_Ghost3"] = 120,
    ["MCG_InGame_Speech_Ghost4"] = 120,
    ["MCG_InGame_Speech_Ghost5"] = 120,
    ["MCG_InGame_Speech_Ghost6"] = 120,
    ["MCG_InGame_PropPonit"] = 120,
    ["MCG_InGame_StartGameCount"] = 120,
    ["MCG_InGame_OverallFixSpeedUp_Notice"] = 120,
    ["MCG_InGame_GhostEnhance_Notice"] = 120,
    ["MCG_InGame_Dying_DeathProgress"] = 120,
    ["MCG_InGame_cooperateFixSpeedUp_Progress"] = 120,
    ["MCG_InGame_OverallFixSpeedUp_Progress"] = 120,
    ["MCG_InGame_TangsengFixSpeedUp_Progress"] = 120,
    ["MCG_InGame_AngelFixSpeedUp_Progress"] = 120,
    ["StarBless_Active_No_In_Starchart_Tip"] = 120,
    ["StarBless_Active_Starchart_AlreadyUse_Tip"] = 120,
    ["Hamburger_Active_NoValidTarget_Tip"] = 120,
    ["Grenade_Active_NoValidTarget_Tip"] = 120,
    ["MCG_EndGame_Player"] = 120,
    ["MCG_Loading_Headphones"] = 120,
    ["MCG_InGame_SOS"] = 120,
    ["MCG_InGame_FTUE_HumanMachien"] = 120,
    ["MCG_InGame_FTUE_Minigame"] = 120,
    ["MCG_InGame_FTUE_GhostMachine"] = 120,
    ["MCG_InGame_FTUE_Save"] = 120,
    ["MCG_InGame_Chat_Tips"] = 120,
    ["MCG_InGame_Struggle_Button"] = 120,
    ["MCG_InGame_QuickChat_Button"] = 120,
    ["MCG_InGame_FinalFight_Human"] = 121,
    ["MCG_InGame_FinalFight_Ghost"] = 121,
    ["MCG_InGame_FinalFight_OpenDoor"] = 121,
    ["MCG_InGame_ChangeBoss_Button"] = 121,
    ["OnStarChartFixSuccessTip"] = 121,
    ["MCG_InGame_GhostBecomeBigKing"] = 121,
    ["MCG_InGame_Giveup"] = 121,
    ["MCG_InGame_Giveup_Yes"] = 121,
    ["MCG_InGame_Giveup_No"] = 121,
    ["MCG_InGame_Giveup_Yes2"] = 121,
    ["MCG_InGame_Giveup_No2"] = 121,
    ["MCG_InGame_Giveup_Cancel"] = 121,
    ["MCG_InGame_Giveup_Wait"] = 121,
    ["MCG_InGame_Giveup_Wait2"] = 121,
    ["MCG_InGame_Giveup_Commit"] = 121,
    ["MCG_InGame_Giveup_NeedConform1"] = 121,
    ["MCG_InGame_Giveup_NeedConform2"] = 121,
    ["MCG_InGame_Giveup_Fail"] = 121,
    ["MCG_InGame_Giveup_Human"] = 121,
    ["MCG_InGame_Giveup_Human_ForGhost"] = 121,
    ["MCG_InGame_Giveup_Ghost"] = 121,
    ["MCG_InGame_Giveup_Ghost_ForHuman"] = 121,
    ["MCG_InGame_Idle"] = 121,
    ["MCG_InGame_IdleAuto"] = 121,
    ["MCG_InGame_IdleCheck"] = 121,
    ["MCG_InGame_IdleSystemControl"] = 121,
    ["MCG_InGame_IdleIKnow"] = 121,
    ["MCG_InGame_IdleCancel"] = 121,
    ["MCG_InGame_IdlePlayer_NotifyTip"] = 121,
    ["MCG_BossDetectedStartNotifyTip"] = 121,
    ["MCG_BossAttack_Charge"] = 121,
    ["MCG_StarBless_Active_Detection_Tip"] = 121,
    ["MCG_StarBless_Active_NoEnergy_Tip"] = 121,
    ["MCG_ModeInstruction_BigLittleKing"] = 121,
    ["MCG_ModeInstruction_BigLittleKing_Info"] = 121,
    ["MCG_ModeInstruction_BigLittleKing_SkillTitle"] = 121,
    ["MCG_ModeInstruction_BigLittleKing_SkillInfo"] = 121,
    ["MCG_ModeInstruction_3Kings_SkillInfo"] = 121,
    ["MCG_ModeInstruction_ShutDownMachine"] = 121,
    ["MCG_ModeInstruction_ShutDownMachine_Info"] = 121,
    ["MCG_InGame_ShutDownMachine"] = 121,
    ["MCG_InGame_ShutDownMachine_Info"] = 121,
    ["MCG_InGame_ShutDownMachine_NotifyTip"] = 121,
    ["MCG_InGame_ShutDownMachine_Rate"] = 121,
    ["MCG_InGame_ShutDownMachine_Notice"] = 121,
    ["MCG_InGame_ShutDownMachine_Recovery_NotifyTip"] = 121,
    ["MCG_ModeInstruction_BigKing"] = 121,
    ["MCG_InGame_3Kings"] = 121,
    ["MCG_InGame_3Kings_Rule"] = 121,
    ["MCG_InGame_3Kings_Rule_Vampire"] = 121,
    ["MCG_InGame_3Kings_Rule_Nezha"] = 121,
    ["MCG_InGame_3KHumanMachine_Story1"] = 121,
    ["MCG_InGame_3KHumanMachine_Story2"] = 121,
    ["MCG_InGame_3KHumanMachine_Story3"] = 121,
    ["MCG_InGame_3KHumanMachine_Story1_Fire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story2_Fire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story3_Fire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story1_Vampire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story2_Vampire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story3_Vampire"] = 121,
    ["MCG_InGame_3KHumanMachine_Story1_Nezha"] = 121,
    ["MCG_InGame_3KHumanMachine_Story2_Nezha"] = 121,
    ["MCG_InGame_3KHumanMachine_Story3_Nezha"] = 121,
    ["MCG_InGame_Ghost_ChooseRoleAndSkill"] = 121,
    ["MCG_InGame_Ghost_ChooseRole"] = 121,
    ["MCG_InGame_Ghost_ChooseSkill"] = 121,
    ["MCG_InGame_NotRepeatable"] = 121,
    ["MCG_InGame_Chosen"] = 121,
    ["MCG_InGame_Forbidden"] = 121,
    ["MCG_InGame_Tang"] = 121,
    ["MCG_InGame_BecomeTang"] = 121,
    ["MCG_InGame_Tang_Buff"] = 121,
    ["MCG_InGame_Tang_Defeat"] = 121,
    ["MCG_InGame_Tang_Back"] = 121,
    ["MCG_InGame_Tang_Back02"] = 121,
    ["MCG_InGame_Tang_Pick"] = 121,
    ["MCG_InGame_Angel"] = 121,
    ["MCG_InGame_BecomeAngel"] = 121,
    ["MCG_InGame_Angel_Buff"] = 121,
    ["MCG_InGame_Angel_Defeat"] = 121,
    ["MCG_InGame_Angel_Back"] = 121,
    ["MCG_InGame_Angel_Pick"] = 121,
    ["MCG_InGame_Nezha"] = 121,
    ["MCG_InGame_BecomeNezha"] = 121,
    ["MCG_InGame_Nezha_Buff"] = 121,
    ["MCG_InGame_Nezha_Defeat"] = 121,
    ["MCG_InGame_Nezha_Back"] = 121,
    ["MCG_InGame_Nezha_Pick"] = 121,
    ["MCG_InGame_TakeOff"] = 121,
    ["MCG_InGame_Wukong"] = 121,
    ["MCG_InGame_BecomeWukong"] = 121,
    ["MCG_InGame_WuKong_Defeat"] = 121,
    ["MCG_InGame_Werewolf"] = 121,
    ["MCG_InGame_BecomeWerewolf"] = 121,
    ["MCG_InGame_Werewolf_Defeat"] = 121,
    ["MCG_InGame_MagicWeaponBox"] = 121,
    ["MCG_InGame_MagicWeaponBox_NotifyTip"] = 121,
    ["MCG_InGame_MagicWeaponBox_NotifyTip2"] = 121,
    ["MCG_InGame_MagicWeaponBox_Vampire"] = 121,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Vampire"] = 121,
    ["MCG_InGame_MagicWeaponBox_NotifyTip2_Vampire"] = 122,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha1"] = 122,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha2"] = 122,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha3"] = 122,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha1"] = 122,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha2"] = 122,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha3"] = 122,
    ["MCG_InGame_Open"] = 122,
    ["MCG_InGame_UnpackMagicWeaponBox"] = 122,
    ["MCG_InGame_CannotPickMagicWeapon"] = 122,
    ["MCG_InGame_CannotPickMagicWeapon_Tang"] = 122,
    ["MCG_InGameGotMagicWeapon_NotifyTip"] = 122,
    ["MCG_InGame_UnpackMagicWeaponBox_Vampire"] = 122,
    ["MCG_InGame_CannotPickMagicWeapon_Vampire"] = 122,
    ["MCG_InGame_CannotPickMagicWeapon_Angel"] = 122,
    ["MCG_InGame_CannotPickMagicWeapon_Nezha"] = 122,
    ["MCG_InGameGotMagicWeapon_Vampire_NotifyTip"] = 122,
    ["MCG_InGameGotMagicWeapon_NotifyTipShan"] = 122,
    ["MCG_InGame_3KDoor_Open_Human"] = 122,
    ["MCG_InGame_3KDoor_Open_Ghost"] = 122,
    ["MCG_InGame_3KDoor_Open_OnlyTang"] = 122,
    ["MCG_InGame_3KDoor_Open_Human_Vampire"] = 122,
    ["MCG_InGame_3KDoor_Open_Ghost_Vampire"] = 122,
    ["MCG_InGame_3KDoor_Open_OnlyAngel"] = 122,
    ["MCG_InGame_3KDoor_Open_Human_Nezha"] = 122,
    ["MCG_InGame_3KDoor_Open_Ghost_Nezha"] = 122,
    ["MCG_InGame_3KDoor_Open_OnlyNezha"] = 122,
    ["MCG_InGame_GhostDoor_Open1"] = 122,
    ["MCG_InGame_GhostDoor_Open2"] = 122,
    ["MCG_InGame_GhostDoor_Open3"] = 122,
    ["MCG_InGame_GhostDoor_Open1_Fire"] = 122,
    ["MCG_InGame_GhostDoor_Open2_Fire"] = 122,
    ["MCG_InGame_GhostDoor_Open3_Fire"] = 122,
    ["MCG_InGame_GhostDoor_Open1_Vampire"] = 122,
    ["MCG_InGame_GhostDoor_Open2_Vampire"] = 122,
    ["MCG_InGame_GhostDoor_Open3_Vampire"] = 122,
    ["MCG_InGame_GhostDoor_Tip1"] = 122,
    ["MCG_InGame_GhostDoor_Tip2"] = 122,
    ["MCG_InGame_GhostDoor_Tip_Nezha"] = 122,
    ["MCG_InGame_3KHumanGoal1"] = 122,
    ["MCG_InGame_3KHumanGoal2"] = 122,
    ["MCG_InGame_3KHumanGoal3"] = 122,
    ["MCG_InGame_3KHumanGoal4"] = 122,
    ["MCG_InGame_3KHumanGoal1_Fire"] = 122,
    ["MCG_InGame_3KHumanGoal2_Fire"] = 122,
    ["MCG_InGame_3KHumanGoal3_Fire"] = 122,
    ["MCG_InGame_3KHumanGoal4_Fire"] = 122,
    ["MCG_InGame_3KHumanGoal1_Vampire"] = 122,
    ["MCG_InGame_3KHumanGoal2_Vampire"] = 122,
    ["MCG_InGame_3KHumanGoal3_Vampire"] = 122,
    ["MCG_InGame_3KHumanGoal4_Vampire"] = 122,
    ["MCG_InGame_3KHumanGoal1_Nezha"] = 122,
    ["MCG_InGame_3KHumanGoal2_Nezha"] = 122,
    ["MCG_InGame_3KHumanGoal3_Nezha"] = 122,
    ["MCG_InGame_3KHumanGoal4_Nezha"] = 122,
    ["MCG_InGame_3KGhostGoal1"] = 122,
    ["MCG_InGame_3KGhostGoal2"] = 122,
    ["MCG_InGame_3KGhostGoal3"] = 122,
    ["MCG_InGame_3KGhostGoal4"] = 122,
    ["MCG_InGame_3KGhostGoal5"] = 122,
    ["MCG_InGame_3KGhostGoal1_Fire"] = 122,
    ["MCG_InGame_3KGhostGoal2_Fire"] = 122,
    ["MCG_InGame_3KGhostGoal3_Fire"] = 122,
    ["MCG_InGame_3KGhostGoal4_Fire"] = 122,
    ["MCG_InGame_3KGhostGoal5_Fire"] = 122,
    ["MCG_InGame_3KGhostGoal1_Vampire"] = 122,
    ["MCG_InGame_3KGhostGoal2_Vampire"] = 122,
    ["MCG_InGame_3KGhostGoal3_Vampire"] = 122,
    ["MCG_InGame_3KGhostGoal4_Vampire"] = 122,
    ["MCG_InGame_3KGhostGoal5_Vampire"] = 122,
    ["MCG_InGame_3KGhostGoal1_Nezha"] = 122,
    ["MCG_InGame_3KGhostGoal2_Nezha"] = 122,
    ["MCG_InGame_3KGhostGoal3_Nezha"] = 122,
    ["MCG_InGame_3KGhostGoal4_Nezha"] = 122,
    ["MCG_InGame_3KGhostGoal5_Nezha"] = 122,
    ["MCG_InGame_3KNezhaMiao_Toast"] = 122,
    ["MCG_InGame_CountDown"] = 122,
    ["MCG_InGame_Teleport_Tip"] = 122,
    ["MCG_InGame_SaveFromVase_Tip"] = 122,
    ["MCG_InGame_SaveFromGourd_Tip"] = 122,
    ["MCG_InGame_Drop"] = 122,
    ["MCG_InGame_DropConfirm"] = 122,
    ["MCG_InGame_DropConfirm_Vampire"] = 122,
    ["MCG_InGame_Cancel"] = 122,
    ["MCG_InGame_DropYes"] = 122,
    ["MCG_InGame_TangTip_PSD"] = 122,
    ["MCG_InGame_TangTip_ToCave"] = 122,
    ["MCG_InGame_TangTip_Escape"] = 122,
    ["MCG_InGame_TangTip_EscapeTang"] = 122,
    ["MCG_InGame_TangTip_CanPick"] = 122,
    ["MCG_InGame_AngelTip_PSD"] = 122,
    ["MCG_InGame_AngelTip_ToCave"] = 122,
    ["MCG_InGame_AngelTip_Escape"] = 122,
    ["MCG_InGame_AngelTip_EscapeAngel"] = 122,
    ["MCG_InGame_AngelTip_CanPick"] = 122,
    ["MCG_InGame_NezhaTip_PSD"] = 122,
    ["MCG_InGame_NezhaTip_ToCave"] = 122,
    ["MCG_InGame_NezhaTip_Escape"] = 122,
    ["MCG_InGame_NezhaTip_EscapeNezha"] = 122,
    ["MCG_InGame_NezhaTip_CanPick"] = 122,
    ["MCG_InGame_Ready_VIPNotice_Name"] = 123,
    ["MCG_InGame_Ready_VIPNotice_Skill"] = 123,
    ["MCG_InGame_Ready_VIPOnly"] = 123,
    ["MCG_InGame_Ready_VIPOnlyTips"] = 123,
    ["MCG_InGame_VIP01"] = 123,
    ["MCG_InGame_Ready_ChooseSkillPool"] = 123,
    ["MCG_InGame_Spring"] = 123,
    ["MCG_InGame_Spring_CanUse"] = 123,
    ["MCG_InGame_Spring_CanNotUse"] = 123,
    ["MCG_InGame_Spring_Used"] = 123,
    ["MCG_InGame_Spring_Need"] = 123,
    ["MCG_InGame_Spring_Using"] = 123,
    ["MCG_InGame_Spring_Button"] = 123,
    ["MCG_ModeInstruction_Spring"] = 123,
    ["MCG_ModeInstruction_Spring_Text"] = 123,
    ["MCG_InGame_Prop_Door"] = 123,
    ["MCG_InGame_Prop_HumanMachine"] = 123,
    ["MCG_InGame_Prop_BossMachine"] = 123,
    ["MCG_InGame_Prop_Roadblock"] = 123,
    ["MCG_InGame_Prop_Window"] = 123,
    ["MCG_InGame_Prop_Spring"] = 123,
    ["MCG_InGame_Prop_MagicWeaponBox"] = 123,
    ["MCG_InGame_Prop_MagicWeaponBox_Vampire"] = 123,
    ["MCG_ReportBug_FeedBackSuccess"] = 123,
    ["MCG_ReportBug_SelectFeedBackEvent"] = 123,
    ["MCG_ReportBug_EnterAtLeast20Words"] = 123,
    ["MCG_ReportBug_EnterUpTo200Words"] = 123,
    ["MCG_ReportBug_InputTips"] = 123,
    ["MCG_PropType_BossSkin"] = 123,
    ["MCG_PropType_PropSkin"] = 123,
    ["MCG_CommonSkill_NameText"] = 123,
    ["MCG_CommonSkill_HideText"] = 123,
    ["MCG_InLevel_Reputation_MyScore_LineOne"] = 123,
    ["MCG_InLevel_Reputation_MyScore_LineTwo"] = 123,
    ["MCG_InLevel_Reputation_OffenseRule_LineOne"] = 123,
    ["MCG_InLevel_Reputation_OffenseRule_LineTwo"] = 123,
    ["MCG_InLevel_Reputation_OffenseRule_LineThree"] = 123,
    ["MCG_InLevel_Reputation_OffenseRule_LineFour"] = 123,
    ["MCG_InLevel_Reputation_CreditRule_LineOne"] = 123,
    ["MCG_InLevel_Reputation_CreditRule_LineTwo"] = 123,
    ["MCG_InLevel_Reputation_CreditRule_LineThree"] = 123,
    ["MCG_InLevel_Main_NoCompleteWithReputation_Box"] = 123,
    ["MCG_InLevel_Surrender_RemainingTime"] = 123,
    ["MCG_PlayGuide_StartGame"] = 123,
    ["MCG_MEOWMEOW_START_TIPS"] = 123,
    ["MCG_PlayGuide_Freshman"] = 123,
    ["MCG_PlayGuide_Senior"] = 123,
    ["MCG_PlayGuide_Enter"] = 123,
    ["MCG_PlayGuide_Skip"] = 123,
    ["MCG_PlayGuide_EnterTips"] = 123,
    ["MCG_MatchCustom_Three"] = 123,
    ["MCG_MatchCustom_One"] = 123,
    ["MCG_MatchCustom_Star"] = 123,
    ["MCG_MatchCustom_Boss"] = 123,
    ["MCG_MEOWMEOW_DANCING_TIPS"] = 123,
    ["MCG_IdentityScore_ToBeSettled"] = 123,
    ["MCG_InLevel_Surrender_Again"] = 123,
    ["Chase_GameDetail_NotFindData"] = 123,
    ["MCG_InGame_Player_Sense_Scan"] = 123,
    ["Chase_TaskNotStartYet"] = 123,
    ["Chase_ActorBiography"] = 123,
    ["MCG_InGame_Total"] = 123,
    ["Chase_HeroRemain_Time"] = 123,
    ["Chase_HeroPower_Lock"] = 123,
    ["MCG_System_MallShowTab"] = 123,
    ["MCG_System_ChangeCycleTips"] = 123,
    ["MCG_InGame_HumanSkill_MagicCube_Name"] = 123,
    ["MCG_InGame_HumanSkill_MagicCube_Info"] = 123,
    ["MCG_InGame_HumanSkill_MagicCube_Position"] = 123,
    ["MCG_InGame_HumanSkill_36B_Name"] = 123,
    ["MCG_InGame_HumanSkill_36B_Info"] = 123,
    ["MCG_InGame_HumanSkill_36C_Name"] = 123,
    ["MCG_InGame_HumanSkill_36C_Info"] = 123,
    ["Chase_MasterPath_Title"] = 123,
    ["Chase_Lock_Proficiency_01"] = 123,
    ["Chase_Lock_Fightnum_01"] = 123,
    ["Chase_MasterPath_Question_Node_Content"] = 123,
    ["Chase_ProficiencyMain_Entrance_01"] = 123,
    ["Chase_ProficiencyMain_Library_01"] = 123,
    ["Chase_ProficiencyMain_Details_01"] = 123,
    ["Chase_ProficiencyMain_Details_02"] = 124,
    ["Chase_ProficiencyMain_Story_01"] = 123,
    ["Chase_ProficiencyMain_Proficiency_01"] = 123,
    ["Chase_ProficiencyMain_Fightnum_01"] = 123,
    ["Chase_Sort_Default"] = 123,
    ["Chase_Sort_Proficiency"] = 123,
    ["Chase_Sort_Score"] = 123,
    ["Chase_Sort_UseTime"] = 123,
    ["Chase_MasterPath_Timeitem_01"] = 123,
    ["MCG_InGame_HumanSkill_MagicCube_Detach"] = 123,
    ["MCG_InGame_HumanSkill_MagicCube_PutDowm"] = 123,
    ["Chase_ProficiencyMain_Story_02"] = 123,
    ["Chase_ProficiencyMain_Story_03"] = 123,
    ["MCG_InGame_GhostR_Name"] = 123,
    ["MCG_InGame_GhostR_ShortName"] = 123,
    ["MCG_InGame_GhostR_Skill_1_Name"] = 123,
    ["MCG_InGame_GhostR_Skill_1_Info"] = 123,
    ["MCG_InGame_GhostR_Skill_1_FullInfo"] = 123,
    ["MCG_InGame_GhostR_Skill_2_Name"] = 123,
    ["MCG_InGame_GhostR_Skill_2_Info"] = 123,
    ["MCG_InGame_GhostR_Skill_2_FullInfo"] = 123,
    ["MCG_ModeInstruction_Ghost18_Text"] = 124,
    ["Chase_BossPackageInvalid_ChooseFailedTip"] = 124,
    ["Chase_MoneyNotEnough_01"] = 124,
    ["Chase_Time_Proficiency_01"] = 124,
    ["MCG_InGame_StuckExit_Progress"] = 124,
    ["MCG_InGame_StuckExit_WaitTime"] = 124,
    ["Chase_ProficiencyMain_Story_04"] = 124,
    ["MCG_InGame_HumanSkill_Amulet_Name"] = 124,
    ["MCG_InGame_HumanSkill_Amulet_Info"] = 124,
    ["MCG_InGame_HumanSkill_Smoke_Name"] = 124,
    ["MCG_InGame_HumanSkill_Smoke_Info"] = 124,
    ["MCG_InGame_HumanSkill_Flashlight_Name"] = 124,
    ["MCG_InGame_HumanSkill_Flashlight_Info"] = 124,
    ["MCG_InGame_HumanSkill_Stealth_Name"] = 124,
    ["MCG_InGame_HumanSkill_Stealth_Info"] = 124,
    ["MCG_InGame_HumanSkill_Scan_Name"] = 124,
    ["MCG_InGame_HumanSkill_Scan_Info"] = 124,
    ["MCG_InGame_PlayerResponseUp_Skill_2_Name"] = 124,
    ["MCG_InGame_PlayerResponseUp_Skill_2_Info"] = 124,
    ["MCG_InGame_HideTrail_Skill_2_Name"] = 124,
    ["MCG_InGame_HideTrail_Skill_2_Info"] = 124,
    ["MCG_InGame_ExpandPerspective_Skill_2_Name"] = 124,
    ["MCG_InGame_ExpandPerspective_Skill_2_Info"] = 124,
    ["MCG_InGame_ExpandSearchArea_Skill_2_Name"] = 124,
    ["MCG_InGame_ExpandSearchArea_Skill_2_Info"] = 124,
    ["Chest_Exile_PlayerToAltar"] = 124,
    ["CHEST_ModeName"] = 125,
    ["CHEST_InLevel_Reputation_MyScore_LineOne"] = 125,
    ["CHEST_InLevel_Reputation_MyScore_LineTwo"] = 125,
    ["CHEST_InLevel_Reputation_OffenseRule_LineOne"] = 125,
    ["CHEST_InLevel_Reputation_OffenseRule_LineTwo"] = 125,
    ["CHEST_InLevel_Reputation_OffenseRule_LineThree"] = 125,
    ["CHEST_InLevel_Reputation_OffenseRule_LineFour"] = 125,
    ["CHEST_InLevel_Reputation_CreditRule_LineOne"] = 125,
    ["CHEST_InLevel_Reputation_CreditRule_LineTwo"] = 125,
    ["CHEST_InLevel_Reputation_CreditRule_LineThree"] = 125,
    ["Chest_MoneyNotEnough_01"] = 125,
    ["Chest_Lock_Proficiency_01"] = 125,
    ["Chest_Lock_Fightnum_01"] = 125,
    ["Chest_GameCharacterSelectView_Title"] = 125,
    ["Chest_FactionShortDesc_Boss"] = 125,
    ["Chest_FactionShortDesc_Star"] = 125,
    ["Chest_Collectibles_ItemLocked"] = 125,
    ["Chest_Match_RoleDataNotValid"] = 125,
    ["UI_ChestBag_ItemEquipSuc"] = 125,
    ["UI_ChestBag_ItemUnEquipSuc"] = 125,
    ["UI_ChestBag_ItemRepairSuc"] = 125,
    ["UI_ChestBag_ItemBuySuc"] = 125,
    ["UI_ChestBag_ItemSaleSuc"] = 125,
    ["UI_ChestBag_NoGridIdLeft"] = 125,
    ["UI_ChestBag_ItemCanNotEquip"] = 125,
    ["UI_ChestBag_Desc_Durability"] = 125,
    ["UI_ChestBag_Desc_Times"] = 125,
    ["UI_ChestBag_MallBuy_StartGame_CheckContent"] = 125,
    ["UI_ChestBag_CanNotBuyCommodity"] = 125,
    ["UI_ChestBag_CanNotSaleItem"] = 125,
    ["UI_ChestBag_CanNotUnEquipItem"] = 125,
    ["UI_ChestBag_CanNotRepairItem"] = 125,
    ["UI_ChestBag_CanNotEquipItem"] = 125,
    ["UI_ChestBag_BackTitle"] = 125,
    ["UI_ChestBag_Button_Detail_Content"] = 125,
    ["UI_ChestBag_Button_Resort_Content"] = 125,
    ["UI_ChestBag_Text_Capacity_Content"] = 125,
    ["UI_ChestBag_Text_PlayerMapCoinCost"] = 125,
    ["UI_ChestBag_Button_Equip_Content"] = 125,
    ["UI_ChestBag_Button_UnEquip_Content"] = 125,
    ["UI_ChestBag_Button_StartGame_Content"] = 125,
    ["UI_ChestBag_Button_Buy_Content"] = 125,
    ["UI_ChestBag_Button_Sale_Content"] = 125,
    ["UI_ChestBag_Text_CommodityCoinCost"] = 125,
    ["UI_ChestBag_Text_SelectedAll"] = 125,
    ["UI_ChestBag_Text_SalePrice"] = 125,
    ["UI_ChestBag_ClickItemToSaleBag"] = 125,
    ["UI_ChestBag_ClickItemToBuyBag"] = 125,
    ["UI_ChestBag_ClickItemToStorageBag"] = 125,
    ["UI_ChestBag_Button_Repair_Content"] = 125,
    ["UI_CheatBag_ItemDetailTitle"] = 125,
}
table_TextEntryData_IndexTable.Version = {
}
return table_TextEntryData_IndexTable

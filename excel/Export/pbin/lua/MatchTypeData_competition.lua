--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_competition.xlsx: 玩法

local data = {
[1000] = {
id = 1000,
modeID = 5,
desc = "锦标赛",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_04",
image = "CDN:T_ModelSelectLarge_Img_Type_01",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1000,
battlePlayerNum = 32,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
descShort = "淘汰赛",
battleRecordStyle = "UI_PlayerInfo_ModRecord_NomralBattleResult",
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
buttonDesc = "淘汰赛",
gameTypeId = 11800,
layoutID = 1,
isMainGame = 1,
isShowEmotionEntrance = 2,
gameModeType = 5,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 901,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true
},
[1001] = {
id = 1001,
modeID = 6,
desc = "锦标赛",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_04",
image = "CDN:T_ModelSelectLarge_Img_Type_01",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1000,
battlePlayerNum = 32,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "巅峰赛",
battleRecordStyle = "UI_PlayerInfo_ModRecord_NomralBattleResult",
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
buttonDesc = "巅峰赛",
gameTypeId = 11800,
layoutID = 1,
isMainGame = 1,
isShowEmotionEntrance = 2,
gameModeType = 5,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 901,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true
},
[1002] = {
id = 1002,
modeID = 9,
desc = "锦标赛",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_04",
image = "CDN:T_ModelSelectLarge_Img_Type_01",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1000,
battlePlayerNum = 32,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
descShort = "积分赛",
battleRecordStyle = "UI_PlayerInfo_ModRecord_NomralBattleResult",
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
buttonDesc = "积分赛",
gameTypeId = 11800,
layoutID = 1,
isMainGame = 1,
isShowEmotionEntrance = 2,
gameModeType = 10,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 901,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true
},
[1010] = {
id = 1010,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1010,
battlePlayerNum = 10,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "狼人之夜-10人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1011] = {
id = 1011,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1011,
battlePlayerNum = 11,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "狼人之夜-11人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1012] = {
id = 1012,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1012,
battlePlayerNum = 12,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "狼人之夜-12人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1013] = {
id = 1013,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1013,
battlePlayerNum = 8,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-8人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1014] = {
id = 1014,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1014,
battlePlayerNum = 10,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-10人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1015] = {
id = 1015,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1015,
battlePlayerNum = 12,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-12人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1016] = {
id = 1016,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1016,
battlePlayerNum = 15,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-15人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1017] = {
id = 1017,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1017,
battlePlayerNum = 10,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-10人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
},
[1018] = {
id = 1018,
modeID = 17,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 44,
value = 1
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "青铜解锁",
battleRecordCnt = 30,
matchRuleId = 1018,
battlePlayerNum = 12,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
descShort = "定制赛狼人-12人",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
buttonDesc = "谁是狼人",
accountUIName = "UI_Competition_InLevelFinalAccount",
gameTypeId = 20,
layoutID = 1,
isMainGame = 0,
isShowEmotionEntrance = 2,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 902,
bCloseMaxFPSOptimize = true,
jumpRandomLevelSequence = true,
canShowInEndLevelChangeMode = true
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
NeedShowProcessDisplay = false,
UseDefaultChampionDisplayScene = false,
useBasicMusicGroup = false,
bCloseMaxFPSOptimize = false,
bSkipFlyEnter = false,
jumpRandomLevelSequence = false,
canShowInEndLevelChangeMode = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/Chest/B_背包商城_Chest.xlsx: 背包商品

local data = {
[4300003] = {
mallId = 221,
commodityId = 4300003,
commodityName = "飞鞋",
coinType = 3558,
price = 5000,
beginTime = {
seconds = 1718294400
},
order = 300,
gender = 0,
itemIds = {
4890011
},
itemNums = {
1
}
},
[4300004] = {
mallId = 221,
commodityId = 4300004,
commodityName = "撬棍",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 400,
gender = 0,
itemIds = {
4890021
},
itemNums = {
1
}
},
[4300005] = {
mallId = 221,
commodityId = 4300005,
commodityName = "冰冻手雷",
coinType = 3558,
price = 5000,
beginTime = {
seconds = 1718294400
},
order = 500,
gender = 0,
itemIds = {
4890031
},
itemNums = {
1
}
},
[4300006] = {
mallId = 221,
commodityId = 4300006,
commodityName = "绷带",
coinType = 3558,
price = 5000,
beginTime = {
seconds = 1718294400
},
order = 600,
gender = 0,
itemIds = {
4890041
},
itemNums = {
1
}
},
[4300007] = {
mallId = 221,
commodityId = 4300007,
commodityName = "手电筒",
coinType = 3558,
price = 3000,
beginTime = {
seconds = 1718294400
},
order = 700,
gender = 0,
itemIds = {
4890051
},
itemNums = {
1
}
},
[4300008] = {
mallId = 221,
commodityId = 4300008,
commodityName = "圣水",
coinType = 3558,
price = 4000,
beginTime = {
seconds = 1718294400
},
order = 800,
gender = 0,
itemIds = {
4890061
},
itemNums = {
1
}
},
[4300009] = {
mallId = 221,
commodityId = 4300009,
commodityName = "白马摩托",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 900,
gender = 0,
itemIds = {
4890071
},
itemNums = {
1
}
},
[4300010] = {
mallId = 221,
commodityId = 4300010,
commodityName = "传送魔方",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1000,
gender = 0,
itemIds = {
4890081
},
itemNums = {
1
}
},
[4300011] = {
mallId = 221,
commodityId = 4300011,
commodityName = "替身药水",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1100,
gender = 0,
itemIds = {
4890091
},
itemNums = {
1
}
},
[4300012] = {
mallId = 221,
commodityId = 4300012,
commodityName = "冲锋鞋",
coinType = 3558,
price = 5000,
beginTime = {
seconds = 1718294400
},
order = 1200,
gender = 0,
itemIds = {
4891011
},
itemNums = {
1
}
},
[4300013] = {
mallId = 221,
commodityId = 4300013,
commodityName = "人参片",
coinType = 3558,
price = 5000,
beginTime = {
seconds = 1718294400
},
order = 1300,
gender = 0,
itemIds = {
4891021
},
itemNums = {
1
}
},
[4300014] = {
mallId = 221,
commodityId = 4300014,
commodityName = "传送器",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1400,
gender = 0,
itemIds = {
4891031
},
itemNums = {
1
}
},
[4300015] = {
mallId = 221,
commodityId = 4300015,
commodityName = "扫描仪",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1500,
gender = 0,
itemIds = {
4891041
},
itemNums = {
1
}
},
[4300016] = {
mallId = 221,
commodityId = 4300016,
commodityName = "假身",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1600,
gender = 0,
itemIds = {
4891051
},
itemNums = {
1
}
},
[4300017] = {
mallId = 221,
commodityId = 4300017,
commodityName = "假货！",
coinType = 3558,
price = 99999999,
beginTime = {
seconds = 1718294400
},
order = 1700,
gender = 0,
itemIds = {
4891061
},
itemNums = {
1
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
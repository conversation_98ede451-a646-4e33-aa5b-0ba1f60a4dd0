--com.tencent.wea.xlsRes.table_DegreeTypeData => excel/xls/P_排位段位&保分配置_nr3e_谁是狼人.xlsx: 段位信息

local data = {
[20000] = {
id = 20000,
degreeType = {
{
QID = 1,
desDegreeType = "青铜狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "青铜狼人V",
degreeIcon = "T_RankingReward_Icon_BronzeBG_5",
degreeStar = {
{
star = 0,
integralLeft = 0,
integralRight = 19,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 20,
integralRight = 39,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 40,
integralRight = 59,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 60,
integralRight = 79,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Bronze_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_BronzeBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "青铜狼人IV",
degreeIcon = "T_RankingReward_Icon_BronzeBG_4",
degreeStar = {
{
star = 0,
integralLeft = 60,
integralRight = 79,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 80,
integralRight = 99,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 100,
integralRight = 119,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 120,
integralRight = 139,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Bronze_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_BronzeBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "青铜狼人III",
degreeIcon = "T_RankingReward_Icon_BronzeBG_3",
degreeStar = {
{
star = 0,
integralLeft = 120,
integralRight = 139,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 140,
integralRight = 159,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 160,
integralRight = 179,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 180,
integralRight = 199,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Bronze_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_BronzeBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "青铜狼人II",
degreeIcon = "T_RankingReward_Icon_BronzeBG_2",
degreeStar = {
{
star = 0,
integralLeft = 180,
integralRight = 199,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 200,
integralRight = 219,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 220,
integralRight = 239,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 240,
integralRight = 259,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Bronze_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_BronzeBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "青铜狼人I",
degreeIcon = "T_RankingReward_Icon_BronzeBG_1",
degreeStar = {
{
star = 0,
integralLeft = 240,
integralRight = 259,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 260,
integralRight = 279,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 280,
integralRight = 299,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 300,
integralRight = 319,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Bronze_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_BronzeBG_sheishilangren"
}
}
},
{
QID = 2,
desDegreeType = "白银狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "白银狼人V",
degreeIcon = "T_RankingReward_Icon_SilverBG_5",
degreeStar = {
{
star = 0,
integralLeft = 300,
integralRight = 319,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 320,
integralRight = 339,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 340,
integralRight = 359,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 360,
integralRight = 379,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Silver_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_SilverBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "白银狼人IV",
degreeIcon = "T_RankingReward_Icon_SilverBG_4",
degreeStar = {
{
star = 0,
integralLeft = 360,
integralRight = 379,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 380,
integralRight = 399,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 400,
integralRight = 419,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 420,
integralRight = 439,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Silver_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_SilverBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "白银狼人III",
degreeIcon = "T_RankingReward_Icon_SilverBG_3",
degreeStar = {
{
star = 0,
integralLeft = 420,
integralRight = 439,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 440,
integralRight = 459,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 460,
integralRight = 479,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 480,
integralRight = 499,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Silver_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_SilverBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "白银狼人II",
degreeIcon = "T_RankingReward_Icon_SilverBG_2",
degreeStar = {
{
star = 0,
integralLeft = 480,
integralRight = 499,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 500,
integralRight = 519,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 520,
integralRight = 539,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 540,
integralRight = 559,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Silver_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_SilverBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "白银狼人I",
degreeIcon = "T_RankingReward_Icon_SilverBG_1",
degreeStar = {
{
star = 0,
integralLeft = 540,
integralRight = 559,
nextIntegral = 20,
starDesInfo = "0星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 560,
integralRight = 579,
nextIntegral = 20,
starDesInfo = "1星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 580,
integralRight = 599,
nextIntegral = 20,
starDesInfo = "2星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 600,
integralRight = 619,
nextIntegral = 20,
starDesInfo = "3星",
integralProtect = 1,
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Silver_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_SilverBG_sheishilangren"
}
}
},
{
QID = 3,
desDegreeType = "黄金狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "黄金狼人V",
degreeIcon = "T_RankingReward_Icon_GoldBG_5",
degreeStar = {
{
star = 0,
integralLeft = 600,
integralRight = 619,
nextIntegral = 20,
starDesInfo = "0星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 620,
integralRight = 639,
nextIntegral = 20,
starDesInfo = "1星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 640,
integralRight = 659,
nextIntegral = 20,
starDesInfo = "2星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 660,
integralRight = 679,
nextIntegral = 20,
starDesInfo = "3星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 680,
integralRight = 699,
nextIntegral = 20,
starDesInfo = "4星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Gold_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_GoldBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "黄金狼人IV",
degreeIcon = "T_RankingReward_Icon_GoldBG_4",
degreeStar = {
{
star = 0,
integralLeft = 680,
integralRight = 699,
nextIntegral = 20,
starDesInfo = "0星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 700,
integralRight = 719,
nextIntegral = 20,
starDesInfo = "1星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 720,
integralRight = 739,
nextIntegral = 20,
starDesInfo = "2星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 740,
integralRight = 759,
nextIntegral = 20,
starDesInfo = "3星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 760,
integralRight = 779,
nextIntegral = 20,
starDesInfo = "4星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Gold_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_GoldBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "黄金狼人III",
degreeIcon = "T_RankingReward_Icon_GoldBG_3",
degreeStar = {
{
star = 0,
integralLeft = 760,
integralRight = 779,
nextIntegral = 20,
starDesInfo = "0星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 780,
integralRight = 799,
nextIntegral = 20,
starDesInfo = "1星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 800,
integralRight = 819,
nextIntegral = 20,
starDesInfo = "2星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 820,
integralRight = 839,
nextIntegral = 20,
starDesInfo = "3星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 840,
integralRight = 859,
nextIntegral = 20,
starDesInfo = "4星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Gold_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_GoldBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "黄金狼人II",
degreeIcon = "T_RankingReward_Icon_GoldBG_2",
degreeStar = {
{
star = 0,
integralLeft = 840,
integralRight = 859,
nextIntegral = 20,
starDesInfo = "0星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 860,
integralRight = 879,
nextIntegral = 20,
starDesInfo = "1星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 880,
integralRight = 899,
nextIntegral = 20,
starDesInfo = "2星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 900,
integralRight = 919,
nextIntegral = 20,
starDesInfo = "3星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 920,
integralRight = 939,
nextIntegral = 20,
starDesInfo = "4星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Gold_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_GoldBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "黄金狼人I",
degreeIcon = "T_RankingReward_Icon_GoldBG_1",
degreeStar = {
{
star = 0,
integralLeft = 920,
integralRight = 939,
nextIntegral = 20,
starDesInfo = "0星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 940,
integralRight = 959,
nextIntegral = 20,
starDesInfo = "1星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 960,
integralRight = 979,
nextIntegral = 20,
starDesInfo = "2星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 980,
integralRight = 999,
nextIntegral = 20,
starDesInfo = "3星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1000,
integralRight = 1019,
nextIntegral = 20,
starDesInfo = "4星",
degreeProtect = 1,
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Gold_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_GoldBG_sheishilangren"
}
}
},
{
QID = 4,
desDegreeType = "铂金狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "铂金狼人V",
degreeIcon = "T_RankingReward_Icon_PlatinumBG_5",
degreeStar = {
{
star = 0,
integralLeft = 1000,
integralRight = 1019,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1020,
integralRight = 1039,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1040,
integralRight = 1059,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1060,
integralRight = 1079,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1080,
integralRight = 1099,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1100,
integralRight = 1119,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Platinum_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_PlatinumBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "铂金狼人IV",
degreeIcon = "T_RankingReward_Icon_PlatinumBG_4",
degreeStar = {
{
star = 0,
integralLeft = 1100,
integralRight = 1119,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1120,
integralRight = 1139,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1140,
integralRight = 1159,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1160,
integralRight = 1179,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1180,
integralRight = 1199,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1200,
integralRight = 1219,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Platinum_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_PlatinumBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "铂金狼人III",
degreeIcon = "T_RankingReward_Icon_PlatinumBG_3",
degreeStar = {
{
star = 0,
integralLeft = 1200,
integralRight = 1219,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1220,
integralRight = 1239,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1240,
integralRight = 1259,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1260,
integralRight = 1279,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1280,
integralRight = 1299,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1300,
integralRight = 1319,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Platinum_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_PlatinumBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "铂金狼人II",
degreeIcon = "T_RankingReward_Icon_PlatinumBG_2",
degreeStar = {
{
star = 0,
integralLeft = 1300,
integralRight = 1319,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1320,
integralRight = 1339,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1340,
integralRight = 1359,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1360,
integralRight = 1379,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1380,
integralRight = 1399,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1400,
integralRight = 1419,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Platinum_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_PlatinumBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "铂金狼人I",
degreeIcon = "T_RankingReward_Icon_PlatinumBG_1",
degreeStar = {
{
star = 0,
integralLeft = 1400,
integralRight = 1419,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1420,
integralRight = 1439,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1440,
integralRight = 1459,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1460,
integralRight = 1479,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1480,
integralRight = 1499,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1500,
integralRight = 1519,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Platinum_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_PlatinumBG_sheishilangren"
}
}
},
{
QID = 5,
desDegreeType = "钻石狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "钻石狼人V",
degreeIcon = "T_RankingReward_Icon_DiamondsBG_5",
degreeStar = {
{
star = 0,
integralLeft = 1500,
integralRight = 1519,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1520,
integralRight = 1539,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1540,
integralRight = 1559,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1560,
integralRight = 1579,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1580,
integralRight = 1599,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1600,
integralRight = 1619,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Diamonds_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_DiamondsBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "钻石狼人IV",
degreeIcon = "T_RankingReward_Icon_DiamondsBG_4",
degreeStar = {
{
star = 0,
integralLeft = 1600,
integralRight = 1619,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1620,
integralRight = 1639,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1640,
integralRight = 1659,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1660,
integralRight = 1679,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1680,
integralRight = 1699,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1700,
integralRight = 1719,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Diamonds_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_DiamondsBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "钻石狼人III",
degreeIcon = "T_RankingReward_Icon_DiamondsBG_3",
degreeStar = {
{
star = 0,
integralLeft = 1700,
integralRight = 1719,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1720,
integralRight = 1739,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1740,
integralRight = 1759,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1760,
integralRight = 1779,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1780,
integralRight = 1799,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1800,
integralRight = 1819,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Diamonds_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_DiamondsBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "钻石狼人II",
degreeIcon = "T_RankingReward_Icon_DiamondsBG_2",
degreeStar = {
{
star = 0,
integralLeft = 1800,
integralRight = 1819,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1820,
integralRight = 1839,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1840,
integralRight = 1859,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1860,
integralRight = 1879,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1880,
integralRight = 1899,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 1900,
integralRight = 1919,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Diamonds_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_DiamondsBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "钻石狼人I",
degreeIcon = "T_RankingReward_Icon_DiamondsBG_1",
degreeStar = {
{
star = 0,
integralLeft = 1900,
integralRight = 1919,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 1920,
integralRight = 1939,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 1940,
integralRight = 1959,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 1960,
integralRight = 1979,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 1980,
integralRight = 1999,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2000,
integralRight = 2019,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Diamonds_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_DiamondsBG_sheishilangren"
}
}
},
{
QID = 6,
desDegreeType = "星耀狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "星耀狼人V",
degreeIcon = "T_RankingReward_Icon_MasterBG_5",
degreeStar = {
{
star = 0,
integralLeft = 2000,
integralRight = 2019,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 2020,
integralRight = 2039,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 2040,
integralRight = 2059,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 2060,
integralRight = 2079,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 2080,
integralRight = 2099,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2100,
integralRight = 2119,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Master_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_MasterBG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "星耀狼人IV",
degreeIcon = "T_RankingReward_Icon_MasterBG_4",
degreeStar = {
{
star = 0,
integralLeft = 2100,
integralRight = 2119,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 2120,
integralRight = 2139,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 2140,
integralRight = 2159,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 2160,
integralRight = 2179,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 2180,
integralRight = 2199,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2200,
integralRight = 2219,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Master_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_MasterBG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "星耀狼人III",
degreeIcon = "T_RankingReward_Icon_MasterBG_3",
degreeStar = {
{
star = 0,
integralLeft = 2200,
integralRight = 2219,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 2220,
integralRight = 2239,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 2240,
integralRight = 2259,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 2260,
integralRight = 2279,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 2280,
integralRight = 2299,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2300,
integralRight = 2319,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Master_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_MasterBG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "星耀狼人II",
degreeIcon = "T_RankingReward_Icon_MasterBG_2",
degreeStar = {
{
star = 0,
integralLeft = 2300,
integralRight = 2319,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 2320,
integralRight = 2339,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 2340,
integralRight = 2359,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 2360,
integralRight = 2379,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 2380,
integralRight = 2399,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2400,
integralRight = 2419,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Master_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_MasterBG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "星耀狼人I",
degreeIcon = "T_RankingReward_Icon_MasterBG_1",
degreeStar = {
{
star = 0,
integralLeft = 2400,
integralRight = 2419,
nextIntegral = 20,
starDesInfo = "0星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 1,
integralLeft = 2420,
integralRight = 2439,
nextIntegral = 20,
starDesInfo = "1星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 2,
integralLeft = 2440,
integralRight = 2459,
nextIntegral = 20,
starDesInfo = "2星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 3,
integralLeft = 2460,
integralRight = 2479,
nextIntegral = 20,
starDesInfo = "3星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 4,
integralLeft = 2480,
integralRight = 2499,
nextIntegral = 20,
starDesInfo = "4星",
qualifyingWeight = 100,
additionalIntegral = 20
},
{
star = 5,
integralLeft = 2500,
integralRight = 2519,
nextIntegral = 20,
starDesInfo = "5星",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_Master_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_MasterBG_sheishilangren"
}
}
},
{
QID = 7,
desDegreeType = "最强狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "最强狼人V",
degreeIcon = "T_RankingReward_Icon_King1BG_5",
degreeStar = {
{
star = 0,
integralLeft = 2520,
integralRight = 2619,
nextIntegral = 100,
starDesInfo = "最强狼人V",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King1_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King1BG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "最强狼人IV",
degreeIcon = "T_RankingReward_Icon_King1BG_4",
degreeStar = {
{
star = 0,
integralLeft = 2620,
integralRight = 2719,
nextIntegral = 100,
starDesInfo = "最强狼人IV",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King1_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King1BG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "最强狼人III",
degreeIcon = "T_RankingReward_Icon_King1BG_3",
degreeStar = {
{
star = 0,
integralLeft = 2720,
integralRight = 2819,
nextIntegral = 100,
starDesInfo = "最强狼人III",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King1_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King1BG_sheishilangren"
},
{
degreeID = 4,
degreeDesInfo = "最强狼人II",
degreeIcon = "T_RankingReward_Icon_King1BG_2",
degreeStar = {
{
star = 0,
integralLeft = 2820,
integralRight = 2919,
nextIntegral = 100,
starDesInfo = "最强狼人II",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King1_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King1BG_sheishilangren"
},
{
degreeID = 5,
degreeDesInfo = "最强狼人I",
degreeIcon = "T_RankingReward_Icon_King1BG_1",
degreeStar = {
{
star = 0,
integralLeft = 2920,
integralRight = 3019,
nextIntegral = 100,
starDesInfo = "最强狼人I",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King1_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King1BG_sheishilangren"
}
}
},
{
QID = 8,
desDegreeType = "无双狼人",
degreeCfgList = {
{
degreeID = 1,
degreeDesInfo = "无双狼人",
degreeStar = {
{
star = 0,
integralLeft = 3020,
integralRight = 999999999,
nextIntegral = 30,
starDesInfo = "无双狼人",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King2_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King2BG_sheishilangren"
},
{
degreeID = 2,
degreeDesInfo = "荣耀狼人",
degreeStar = {
{
star = 0,
integralLeft = 3020,
integralRight = 999999999,
nextIntegral = 30,
starDesInfo = "荣耀狼人",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_King3_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_King3BG_sheishilangren"
},
{
degreeID = 3,
degreeDesInfo = "传奇狼人",
degreeStar = {
{
star = 0,
integralLeft = 3020,
integralRight = 999999999,
nextIntegral = 30,
starDesInfo = "传奇狼人",
qualifyingWeight = 100,
additionalIntegral = 20
}
},
degreeIconBP = "UI_RankingReward_Icon_TheStrongestKingBG_Head_sheishilangren",
degreeIconBG = "T_RankingReward_Icon_TheStrongestKingBG_sheishilangren"
}
},
isKingDegree = true
}
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
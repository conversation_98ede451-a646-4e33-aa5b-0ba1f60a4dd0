--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_策划专用.xlsx: 合家欢任务

local data = {
[10001] = {
id = 10001,
name = "1档",
desc = "1档",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 118,
value = 5
}
}
}
},
reward = {
itemIdList = {
830007
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 4,
taskGroupId = 15001
},
[10002] = {
id = 10002,
name = "2档",
desc = "2档",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 118,
value = 20
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
6
},
validPeriodList = {
0
}
},
jumpId = 4,
taskGroupId = 15001
},
[10003] = {
id = 10003,
name = "3档",
desc = "3档",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 118,
value = 40
}
}
}
},
reward = {
itemIdList = {
630034
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 4,
taskGroupId = 15001
},
[10004] = {
id = 10004,
name = "4档",
desc = "4档",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 118,
value = 70
}
}
}
},
reward = {
itemIdList = {
610024
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 4,
taskGroupId = 15001
},
[10005] = {
id = 10005,
name = "5档",
desc = "5档",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 118,
value = 100
}
}
}
},
reward = {
itemIdList = {
850142
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 4,
taskGroupId = 15001
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
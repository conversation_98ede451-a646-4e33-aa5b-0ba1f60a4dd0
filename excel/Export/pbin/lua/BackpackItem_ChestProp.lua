--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/Chest/D_道具表_CHEST.xlsx: CHEST道具表

local v0 = 100

local v1 = 50001

local v2 = {
NormalIcon = "CDN:T_Chase_Role_UmbraStar_10001"
}

local v3 = {
chestMeshPreviewScaleSize = 200,
chestMeshSquareHomeScaleSize = 200
}

local v4 = 300

local data = {
[4890001] = {
id = 4890001,
effect = true,
type = "ItemType_Chest_Prop_ExtendedBag",
name = "无用的背包",
desc = "无用的背包",
icon = "CDN:T_Chest_Item_icon_4890001",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Assist",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890002] = {
id = 4890002,
effect = true,
type = "ItemType_Chest_Prop_ExtendedBag",
name = "安全包",
desc = "安全包",
icon = "CDN:T_Chest_Item_icon_4890002",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Assist",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890011] = {
id = 4890011,
effect = true,
type = "ItemType_Chest_Prop",
name = "飞鞋",
desc = "使用后增加 60% 移动速度，加速期间持续消耗耐久度，再次点击可停用。",
icon = "CDN:T_Chest_Item_icon_4890011",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Survive",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890021] = {
id = 4890021,
effect = true,
type = "ItemType_Chest_Prop",
name = "撬棍",
desc = [[【点击技能键主动使用】
将撬棍扔出去，如果砸中暗星则会扣光所有耐久造成击晕，击晕时长与撬棍剩余耐久度正相关。
如果没有击中暗星，则会掉落在地上，可被再次拾取。

【装配在使用栏位内就会被动生效】
装配撬棍时，开箱 Sequence 需要更换位使用撬棍开箱，没有 QTE，开箱时间随撬棍等级。
每撬开一个箱子，都会损耗部分撬棍耐久，目前暂定耐久度够开 8~10 个箱子。]],
icon = "CDN:T_Chest_Item_icon_4890021",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Survive",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890031] = {
id = 4890031,
effect = true,
type = "ItemType_Chest_Prop",
name = "冰冻手雷",
desc = "使用后朝附近暗星投掷冰冻手雷，使暗星被冰冻一小段时间。",
icon = "CDN:T_Chest_Item_icon_4890031",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Survive",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890041] = {
id = 4890041,
effect = true,
type = "ItemType_Chest_Prop",
name = "绷带",
desc = [[使用后立刻恢复 50% 星宝基础血量。
使用时存在一定的动作硬直，大约 1.5 秒左右。（关于硬直有争议）
受使用次数限制，更高级的绷带使用次数更多。
如何治疗队友：直接套用“解救”按钮的启动逻辑，在靠近一个血量不满的星宝队友时，弹出一个额外的交互键“治疗队友”，Icon 与绷带道具的 Icon 一致，点击后即可使用 绷带对该队友进行治疗，和治疗自己一样，消耗绷带的使用次数，并且技能进入 CD，技能 CD 期间接近可治疗的队友时不会弹出额外的交互键。
- 跪倒和挂起的队友首先要被“解救”，因此不会和治疗队友的功能产生冲突。]],
icon = "CDN:T_Chest_Item_icon_4890041",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Survive",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890051] = {
id = 4890051,
effect = true,
type = "ItemType_Chest_Prop",
name = "手电筒",
desc = "为星宝提供一个额外的锥状视野范围，该视野仅自己可见（暗星和星宝均不可见）",
icon = "CDN:T_Chest_Item_icon_4890051",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Survive",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890061] = {
id = 4890061,
effect = true,
type = "ItemType_Chest_Prop",
name = "圣水",
desc = "原地生成一个防护罩，星宝可以自由穿过防护罩，暗星无法穿过，暗星的普攻以及伤害性技能可以对防护罩造成伤害，防护罩血量为0后会被打碎。",
icon = "CDN:T_Chest_Item_icon_4890061",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Assist",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890071] = {
id = 4890071,
effect = true,
type = "ItemType_Chest_Prop",
name = "白马摩托",
desc = "骑上摩托，切换为载具驾驶，背后可以载一名乘客，其他星宝可以通过交互上车。",
icon = "CDN:T_Chest_Item_icon_4890071",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Assist",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4890081] = {
id = 4890081,
effect = true,
type = "ItemType_Chest_Prop",
name = "传送魔方",
desc = [[投掷与命中：向指定方向投掷魔方，可命中星宝或者安心，命中后立刻爆炸；若未命中目标，到达最远运动距离时也爆炸
命中星宝效果：命中后，将新南国报传送到自己身上
爆炸范围效果：魔方爆炸后，对附近的暗星造成范围减速。

大王现役道具，完整移植。
由于作为通用道具（消耗品）合入版本，因此需要为其追加使用次数限制。]],
icon = "CDN:T_Chest_Item_icon_4890081",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Assist",
chestSideType = "CST_XINGBAO"
},
newQuality = 300
},
[4891011] = {
id = 4891011,
effect = true,
type = "ItemType_Chest_Prop",
name = "冲锋鞋",
desc = [[向前方迅速突进一段距离（14 米左右）
受 CD 和 耐久限制，每次使用都会扣除一定的耐久。
相比传送器的优势在于 CD 更短，且更耐用。]],
icon = "CDN:T_Chest_Item_icon_4891011",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4891021] = {
id = 4891021,
effect = true,
type = "ItemType_Chest_Prop",
name = "人参片",
desc = [[提神醒脑，立刻解除自身受到的控制效果。
可以考虑附加一小段时间内略微提升自身移速的性能。
受 CD 和使用次数制约。]],
icon = "CDN:icon_CHT_Prop_AX_renshenpian",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4891031] = {
id = 4891031,
effect = true,
type = "ItemType_Chest_Prop",
name = "传送器",
desc = [[使用后自动打开一张小地图，在小地图上选择传送坐标，并点击传送按钮，即可传送至该位置，传送器随即进入冷却。
受 CD 和使用次数限制。]],
icon = "CDN:T_Chest_Item_icon_4891031",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4891041] = {
id = 4891041,
effect = true,
type = "ItemType_Chest_Prop",
name = "扫描仪",
desc = [[显示一定区域内所有星宝的位置，共享给同局所有暗星。
扫描仪作用下的星宝如果被其他暗星抓住，这位暗星就需要给扫描仪的使用者上税。]],
icon = "CDN:icon_CHT_Prop_AX_toushiyi",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4891051] = {
id = 4891051,
effect = true,
type = "ItemType_Chest_Prop",
name = "假身",
desc = [[召唤一个和自己一样的假身，假身逼近星宝时给到星宝的提示信号与真身无异（脚步声、红色惊叹号等）。
假身会存在相当长的一段时间，并在存活期间四处闲逛，但他不会攻击、不会用技能、也不会主动追击星宝。
本质上，假身是利用与真身无异的提示机制，让一片区域变成虚假的高威胁区，从而压缩星宝的行动空间。
假身在外形上与真身基本无二，但在动作和行为模式上是存在破绽的，比如偶尔做些玩家做不出来的手舞足蹈、以及有 AI 控制的缺乏目的的移动行为等；
反之，当星宝知道场上存在暗星假身时，暗星也可以通过故意模仿 AI 的无目标移动来假装自己是假身，从而混肴视听、形成博弈。]],
icon = "CDN:T_Chest_Item_icon_4891051",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4891061] = {
id = 4891061,
effect = true,
type = "ItemType_Chest_Prop",
name = "假货！",
desc = [[放置一个假的大宝箱，一旦有星宝尝试开箱，他的位置就会立即暴露给所有暗星；
假宝箱在待机状态和一般宝箱基本无异，但周身会萦绕一些不易察觉的黑气或者氛围特效（传达一些不详感），有经验的星宝能够一眼辨别出这是宝箱怪。
假货的开箱流程和真宝箱是一样的（并且打开时也会消耗撬棍的耐久度），当星宝把箱子打开时，会有一双大手把星宝抓进箱子里，被抓的星宝会弹出挣脱键，需要通过高速的按键输入积累挣脱槽来尝试脱离。
- 当有星宝尝试开箱时，放置宝箱怪的暗星将收“叮……”一声的音效提示，并且该星宝以及宝箱怪将在所有暗星眼中被透视；
- 当有星宝被宝箱怪抓住时，会给放置宝箱怪的暗星推送“宝箱怪抓住了一名星宝……”的文字通告。
如果没能在指定时间内挣脱出来，星宝会以濒死状态被宝箱怪吐出来，等待被队友救起或者被大王捡走。
被吞进箱子的星宝会拼命喊救命，此时其他星宝可以来开箱把被吞的星宝释放出来，这个流程和从祭坛上解救队友是一样的。
假货是消耗品，在捂死星宝将他吐出之后、或者星宝从中挣脱、其他星宝完成解救，都视为一次吞噬的完成，宝箱怪也随即消失。（也就是说，一个宝箱怪最多将一个星宝置于濒死状态）。
暗星最多可以在场上布置 3 个假宝箱。]],
icon = "CDN:T_Chest_Item_icon_4891061",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = {
chestItemType = "CHEST_IT_Chase",
chestSideType = "CST_DARKSTAR"
},
newQuality = 300
},
[4892001] = {
id = 4892001,
effect = true,
name = "八宝琉璃瓶",
desc = "八宝琉璃瓶",
icon = "CDN:T_Chest_Item_icon_4892001",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 90
},
[4892002] = {
id = 4892002,
effect = true,
name = "宝莲灯",
desc = "宝莲灯",
icon = "CDN:T_Chest_Item_icon_4892002",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 90
},
[4892003] = {
id = 4892003,
effect = true,
name = "九州鼎",
desc = "九州鼎",
icon = "CDN:T_Chest_Item_icon_4892003",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892004] = {
id = 4892004,
effect = true,
name = "上古天书",
desc = "上古天书",
icon = "CDN:T_Chest_Item_icon_4892004",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892005] = {
id = 4892005,
effect = true,
name = "盘龙串",
desc = "盘龙串",
icon = "CDN:T_Chest_Item_icon_4892005",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892006] = {
id = 4892006,
effect = true,
name = "和氏璧",
desc = "和氏璧",
icon = "CDN:T_Chest_Item_icon_4892006",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892007] = {
id = 4892007,
effect = true,
name = "千里江山图",
desc = "千里江山图",
icon = "CDN:T_Chest_Item_icon_4892007",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892008] = {
id = 4892008,
effect = true,
name = "夜明珠",
desc = "夜明珠",
icon = "CDN:T_Chest_Item_icon_4892008",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892009] = {
id = 4892009,
effect = true,
name = "千年紫芝",
desc = "千年紫芝",
icon = "CDN:T_Chest_Item_icon_4892009",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892010] = {
id = 4892010,
effect = true,
name = "金翠如意",
desc = "金翠如意",
icon = "CDN:T_Chest_Item_icon_4892010",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 100
},
[4892011] = {
id = 4892011,
effect = true,
name = "玛瑙葫芦",
desc = "玛瑙葫芦",
icon = "CDN:T_Chest_Item_icon_4892011",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892012] = {
id = 4892012,
effect = true,
name = "玉枕",
desc = "玉枕",
icon = "CDN:T_Chest_Item_icon_4892012",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892013] = {
id = 4892013,
effect = true,
name = "白玉笛",
desc = "白玉笛",
icon = "CDN:T_Chest_Item_icon_4892013",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892014] = {
id = 4892014,
effect = true,
name = "长命锁",
desc = "长命锁",
icon = "CDN:T_Chest_Item_icon_4892014",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892015] = {
id = 4892015,
effect = true,
name = "宝石戒指",
desc = "宝石戒指",
icon = "CDN:T_Chest_Item_icon_4892015",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892016] = {
id = 4892016,
effect = true,
name = "金元宝",
desc = "金元宝",
icon = "CDN:T_Chest_Item_icon_4892016",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892017] = {
id = 4892017,
effect = true,
name = "香木琵琶",
desc = "香木琵琶",
icon = "CDN:T_Chest_Item_icon_4892017",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892018] = {
id = 4892018,
effect = true,
name = "青花瓷瓶",
desc = "青花瓷瓶",
icon = "CDN:T_Chest_Item_icon_4892018",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 200
},
[4892019] = {
id = 4892019,
effect = true,
name = "紫砂壶",
desc = "紫砂壶",
icon = "CDN:T_Chest_Item_icon_4892019",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892020] = {
id = 4892020,
effect = true,
name = "青瓷盖碗",
desc = "青瓷盖碗",
icon = "CDN:T_Chest_Item_icon_4892020",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892021] = {
id = 4892021,
effect = true,
name = "白石镇纸",
desc = "白石镇纸",
icon = "CDN:T_Chest_Item_icon_4892021",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892022] = {
id = 4892022,
effect = true,
name = "羽扇",
desc = "羽扇",
icon = "CDN:T_Chest_Item_icon_4892022",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892023] = {
id = 4892023,
effect = true,
name = "瓜",
desc = "瓜",
icon = "CDN:T_Chest_Item_icon_4892023",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892024] = {
id = 4892024,
effect = true,
name = "瓷碗",
desc = "瓷碗",
icon = "CDN:T_Chest_Item_icon_4892024",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892025] = {
id = 4892025,
effect = true,
name = "哈吉米",
desc = "哈吉米",
icon = "CDN:T_Chest_Item_icon_4892025",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 300
},
[4892026] = {
id = 4892026,
effect = true,
name = "拨浪鼓",
desc = "拨浪鼓",
icon = "CDN:T_Chest_Item_icon_4892026",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4892027] = {
id = 4892027,
effect = true,
name = "小黄鸭",
desc = "小黄鸭",
icon = "CDN:T_Chest_Item_icon_4892027",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4892028] = {
id = 4892028,
effect = true,
name = "草帽",
desc = "草帽",
icon = "CDN:T_Chest_Item_icon_4892028",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4892029] = {
id = 4892029,
effect = true,
name = "咸鱼",
desc = "咸鱼",
icon = "CDN:T_Chest_Item_icon_4892029",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4892030] = {
id = 4892030,
effect = true,
name = "空白签条",
desc = "空白签条",
icon = "CDN:T_Chest_Item_icon_4892030",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4892031] = {
id = 4892031,
effect = true,
name = "上周的古币",
desc = "上周的古币",
icon = "CDN:T_Chest_Item_icon_4892031",
scaleTimes = 100,
bHideInBag = true,
chunkGroupId = 50001,
chaseSkin = v2,
chestInfo = v3,
newQuality = 400
},
[4897001] = {
id = 4897001,
type = "ItemType_Chest_Story",
quality = 5,
name = "玲珑塔-小传1",
desc = "玲珑塔-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897001",
bHideInBag = true
},
[4897002] = {
id = 4897002,
type = "ItemType_Chest_Story",
quality = 5,
name = "八宝琉璃瓶-小传1",
desc = "八宝琉璃瓶-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897002",
bHideInBag = true
},
[4897003] = {
id = 4897003,
type = "ItemType_Chest_Story",
quality = 5,
name = "夜明珠-小传1",
desc = "夜明珠-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897003",
bHideInBag = true
},
[4897004] = {
id = 4897004,
type = "ItemType_Chest_Story",
quality = 5,
name = "七宝玉如意-小传1",
desc = "七宝玉如意-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897004",
bHideInBag = true
},
[4897005] = {
id = 4897005,
type = "ItemType_Chest_Story",
quality = 5,
name = "上古玉书-小传1",
desc = "上古玉书-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897005",
bHideInBag = true
},
[4897006] = {
id = 4897006,
type = "ItemType_Chest_Story",
quality = 5,
name = "禹王鼎-小传1",
desc = "禹王鼎-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897006",
bHideInBag = true
},
[4897007] = {
id = 4897007,
type = "ItemType_Chest_Story",
quality = 5,
name = "千年紫芝-小传1",
desc = "千年紫芝-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897007",
bHideInBag = true
},
[4897008] = {
id = 4897008,
type = "ItemType_Chest_Story",
quality = 5,
name = "盘龙珠-小传1",
desc = "盘龙珠-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897008",
bHideInBag = true
},
[4897009] = {
id = 4897009,
type = "ItemType_Chest_Story",
quality = 5,
name = "千里江山图-小传1",
desc = "千里江山图-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897009",
bHideInBag = true
},
[4897010] = {
id = 4897010,
type = "ItemType_Chest_Story",
quality = 5,
name = "和氏璧-小传1",
desc = "和氏璧-小传1的具体内容描述",
icon = "CDN:T_Chest_Item_icon_4897010",
bHideInBag = true
}
}

local mt = {
effect = false,
type = "ItemType_Chest_Treasure",
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data
com.tencent.wea.xlsRes.table_QuizConfig
excel/xls/H_活动答题.xlsx sheet:答题组配置
rows {
  id: 1
  questionList: 1
  questionList: 2
  questionList: 3
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 2
  questionList: 11
  questionList: 12
  questionList: 13
  questionList: 14
  questionList: 15
  questionList: 16
  canRepeatedAnswer: false
  isQuestionInOrder: true
}
rows {
  id: 3
  questionList: 17
  questionList: 18
  questionList: 19
  questionList: 20
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 4
  questionList: 21
  questionList: 22
  questionList: 23
  questionList: 24
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 5
  questionList: 25
  questionList: 26
  questionList: 27
  questionList: 28
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 6
  questionList: 29
  questionList: 30
  questionList: 31
  canRepeatedAnswer: true
  isQuestionInOrder: true
  beginTime {
    seconds: 1735660800
  }
  endTime {
    seconds: 1739289600
  }
}
rows {
  id: 7
  questionList: 32
  questionList: 33
  questionList: 34
  canRepeatedAnswer: true
  isQuestionInOrder: true
  beginTime {
    seconds: 1739289600
  }
  endTime {
    seconds: 1739721600
  }
}
rows {
  id: 8
  questionList: 35
  questionList: 36
  questionList: 37
  canRepeatedAnswer: true
  isQuestionInOrder: true
  beginTime {
    seconds: 1739721600
  }
  endTime {
    seconds: 1740326400
  }
}
rows {
  id: 9
  questionList: 38
  questionList: 39
  questionList: 40
  questionList: 41
  questionList: 42
  questionList: 43
  questionList: 44
  questionList: 45
  questionList: 46
  questionList: 47
  questionList: 48
  questionList: 49
  questionList: 50
  questionList: 51
  questionList: 52
  questionList: 53
  questionList: 54
  questionList: 55
  questionList: 56
  questionList: 57
  questionList: 58
  questionList: 59
  questionList: 60
  questionList: 61
  questionList: 62
  questionList: 63
  questionList: 64
  questionList: 65
  questionList: 66
  questionList: 67
  questionList: 68
  questionList: 69
  questionList: 70
  questionList: 71
  questionList: 72
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 10
  questionList: 73
  questionList: 74
  questionList: 75
  questionList: 76
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 11
  questionList: 77
  questionList: 78
  questionList: 79
  questionList: 80
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 12
  questionList: 81
  questionList: 82
  questionList: 83
  questionList: 84
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 13
  questionList: 85
  questionList: 86
  questionList: 87
  questionList: 88
  questionList: 130
  questionList: 131
  questionList: 132
  questionList: 133
  questionList: 93
  questionList: 134
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 14
  questionList: 95
  questionList: 96
  questionList: 97
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 15
  questionList: 98
  questionList: 99
  questionList: 100
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 16
  questionList: 101
  questionList: 102
  questionList: 103
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 17
  questionList: 104
  questionList: 105
  questionList: 106
  questionList: 107
  questionList: 108
  questionList: 109
  questionList: 110
  questionList: 111
  questionList: 112
  questionList: 113
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 18
  questionList: 114
  questionList: 115
  questionList: 116
  questionList: 117
  questionList: 118
  questionList: 119
  questionList: 120
  questionList: 121
  questionList: 122
  questionList: 123
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 19
  questionList: 124
  questionList: 125
  questionList: 126
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 20
  questionList: 127
  questionList: 128
  questionList: 129
  canRepeatedAnswer: true
  isQuestionInOrder: true
}
rows {
  id: 21
  questionList: 135
  questionList: 136
  questionList: 137
  canRepeatedAnswer: true
  isQuestionInOrder: true
}

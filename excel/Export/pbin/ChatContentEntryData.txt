com.tencent.wea.xlsRes.table_ChatContentEntryData
excel/xls/L_聊天文案信息表.xlsx sheet:文本映射表
rows {
  id: 1
  content: "元梦之星欢迎你"
}
rows {
  id: 2
  content: "元梦之星，我来啦！"
}
rows {
  id: 3
  content: "新年快乐！"
}
rows {
  id: 4
  content: "和美团圆！"
}
rows {
  id: 5
  content: "3月15日新赛季开启！"
}
rows {
  id: 6
  content: "新赛季啦"
}
rows {
  id: 7
  content: "小丸子要来啦"
}
rows {
  id: 8
  content: "5.11赛车小哈前来助阵"
}
rows {
  id: 9
  content: "6月7日12点畜牧开放！"
}
rows {
  id: 10
  content: "狼人新身份法医下周将登场"
}
rows {
  id: 11
  content: "狼人新身份通灵师5号降临"
}
rows {
  id: 12
  content: "狼人通灵师降临"
}
rows {
  id: 13
  content: "7月19日甜心乐园赛季开启！"
}
rows {
  id: 14
  content: "元梦星期五，快乐拦不住"
}
rows {
  id: 15
  content: "7月19日12点鱼塘开放！"
}
rows {
  id: 16
  content: "元梦星期五，相思终元满"
}
rows {
  id: 20
  content: "快来和我一起参与幸运好友，丰厚奖励等你来拿！"
}
rows {
  id: 21
  content: "任务进行中，快抓紧完成！"
}
rows {
  id: 22
  content: "任务已经完成了，快抓紧上线领奖！"
}
rows {
  id: 27
  content: "中秋佳节，月圆人团圆！"
}
rows {
  id: 28
  content: "月儿圆，合家幸福庆团圆！"
}
rows {
  id: 29
  content: "喜乐庆中秋，每天都是大丰收！"
}
rows {
  id: 30
  content: "祝星宝们年年月月事事顺心！"
}
rows {
  id: 31
  content: "21号收看冠军杯总决赛"
}
rows {
  id: 35
  content: "3月7日免费领桃源联动时装！"
}
rows {
  id: 36
  content: "[s53]相遇就是缘，加个好友吧。"
}
rows {
  id: 37
  content: "[s70]你先动手的，等着我的复仇吧!"
}
rows {
  id: 38
  content: "[s163]我是新手，求放过。"
}
rows {
  id: 39
  content: "[s131]欢迎下次再来哦~"
}
rows {
  id: 40
  content: "[s103]哟，你的昵称真有趣!"
}
rows {
  id: 41
  content: "[s134]怎样，我的城市很酷吧!"
}

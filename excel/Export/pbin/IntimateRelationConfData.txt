com.tencent.wea.xlsRes.table_IntimateRelationConfData
excel/xls/G_关系链.xlsx sheet:亲密关系配置
rows {
  id: 1
  relationType: IRT_Couple
  relationName: "情侣"
  relationFx: "FX_Love_Relation_001"
  relationPriority: 1
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_Lover3"
  bigIcon: "T_Friend_Img_Lover3"
  bigIcon: "T_Friend_Img_Lover3"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover2"
  bigIcon: "T_Friend_Img_Lover"
  normalIcon: "T_Friend_Img_Bg_Lover4"
  normalIcon: "T_Friend_Img_Bg_Lover4"
  normalIcon: "T_Friend_Img_Bg_Lover4"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover3"
  normalIcon: "T_Friend_Img_Bg_Lover2"
  simpleIcon: "T_Friend_Icon_Lover6"
  simpleIcon: "T_Friend_Icon_Lover6"
  simpleIcon: "T_Friend_Icon_Lover6"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover5"
  simpleIcon: "T_Friend_Icon_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  onlineRemindLevel: 1
  relationFx1: "FX_Love_Relation_001"
  relationFx2: "FX_Love_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Love_Relation_Basic_001"
}
rows {
  id: 2
  relationType: IRT_Other
  relationName: "兄弟"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_Brother3"
  bigIcon: "T_Friend_Img_Brother3"
  bigIcon: "T_Friend_Img_Brother3"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother2"
  bigIcon: "T_Friend_Img_Brother"
  normalIcon: "T_Friend_Icon_Bg_Brother4"
  normalIcon: "T_Friend_Icon_Bg_Brother4"
  normalIcon: "T_Friend_Icon_Bg_Brother4"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother3"
  normalIcon: "T_Friend_Icon_Bg_Brother2"
  simpleIcon: "T_Friend_Icon_Brother6"
  simpleIcon: "T_Friend_Icon_Brother6"
  simpleIcon: "T_Friend_Icon_Brother6"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother5"
  simpleIcon: "T_Friend_Icon_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  onlineRemindLevel: 1
  relationFx1: "FX_Brother_Relation_001"
  relationFx2: "FX_Brother_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Brother_Relation_Basic_001"
}
rows {
  id: 3
  relationType: IRT_Other
  relationName: "闺密"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_Bestie3"
  bigIcon: "T_Friend_Img_Bestie3"
  bigIcon: "T_Friend_Img_Bestie3"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie2"
  bigIcon: "T_Friend_Img_Bestie"
  normalIcon: "T_Friend_Img_Bg_Bestie4"
  normalIcon: "T_Friend_Img_Bg_Bestie4"
  normalIcon: "T_Friend_Img_Bg_Bestie4"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie3"
  normalIcon: "T_Friend_Img_Bg_Bestie2"
  simpleIcon: "T_Friend_Icon_Bestie6"
  simpleIcon: "T_Friend_Icon_Bestie6"
  simpleIcon: "T_Friend_Icon_Bestie6"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie5"
  simpleIcon: "T_Friend_Icon_Bestie"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  txtColor: "Intimate_Besite"
  onlineRemindLevel: 1
  relationFx1: "FX_Sister_Relation_001"
  relationFx2: "FX_Sister_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Sister_Relation_Basic_001"
}
rows {
  id: 4
  relationType: IRT_Other
  relationName: "密友"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_Ggy3"
  bigIcon: "T_Friend_Img_Ggy3"
  bigIcon: "T_Friend_Img_Ggy3"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy2"
  bigIcon: "T_Friend_Img_Ggy"
  normalIcon: "T_Friend_Img_Bg_Ggy4"
  normalIcon: "T_Friend_Img_Bg_Ggy4"
  normalIcon: "T_Friend_Img_Bg_Ggy4"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy3"
  normalIcon: "T_Friend_Img_Bg_Ggy2"
  simpleIcon: "T_Friend_Icon_Ggy6"
  simpleIcon: "T_Friend_Icon_Ggy6"
  simpleIcon: "T_Friend_Icon_Ggy6"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy5"
  simpleIcon: "T_Friend_Icon_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  txtColor: "Intimate_Ggy"
  onlineRemindLevel: 1
  relationFx1: "FX_Closefriend_Relation_001"
  relationFx2: "FX_Closefriend_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Closefriend_Relation_Basic_001"
}
rows {
  id: 5
  relationType: IRT_Other
  relationName: "姐弟"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_JieDi3"
  bigIcon: "T_Friend_Img_JieDi3"
  bigIcon: "T_Friend_Img_JieDi3"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi2"
  bigIcon: "T_Friend_Img_JieDi"
  normalIcon: "T_Friend_Img_Bg_JieDi4"
  normalIcon: "T_Friend_Img_Bg_JieDi4"
  normalIcon: "T_Friend_Img_Bg_JieDi4"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi3"
  normalIcon: "T_Friend_Img_Bg_JieDi2"
  simpleIcon: "T_Friend_Icon_JieDi6"
  simpleIcon: "T_Friend_Icon_JieDi6"
  simpleIcon: "T_Friend_Icon_JieDi6"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi5"
  simpleIcon: "T_Friend_Icon_JieDi"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  onlineRemindLevel: 1
  relationFx1: "FX_Jiedi_Relation_001"
  relationFx2: "FX_Jiedi_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Jiedi_Relation_Basic_001"
}
rows {
  id: 6
  relationType: IRT_Other
  relationName: "兄妹"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_XiongMei3"
  bigIcon: "T_Friend_Img_XiongMei3"
  bigIcon: "T_Friend_Img_XiongMei3"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei2"
  bigIcon: "T_Friend_Img_XiongMei"
  normalIcon: "T_Friend_Img_Bg_XiongMei4"
  normalIcon: "T_Friend_Img_Bg_XiongMei4"
  normalIcon: "T_Friend_Img_Bg_XiongMei4"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei3"
  normalIcon: "T_Friend_Img_Bg_XiongMei2"
  simpleIcon: "T_Friend_Icon_XiongMei6"
  simpleIcon: "T_Friend_Icon_XiongMei6"
  simpleIcon: "T_Friend_Icon_XiongMei6"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei5"
  simpleIcon: "T_Friend_Icon_XiongMei"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  txtColor: "Intimate_Lover"
  onlineRemindLevel: 1
  relationFx1: "FX_Xiongmei_Relation_001"
  relationFx2: "FX_Xiongmei_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Xiongmei_Relation_Basic_001"
}
rows {
  id: 7
  relationType: IRT_Other
  relationName: "家人"
  relationFx: "FX_Friend_Relation_001"
  relationPriority: 2
  fxEffectiveLevel: 8
  bigIcon: "T_Friend_Img_Family3"
  bigIcon: "T_Friend_Img_Family3"
  bigIcon: "T_Friend_Img_Family3"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family2"
  bigIcon: "T_Friend_Img_Family"
  normalIcon: "T_Friend_Img_Bg_Family4"
  normalIcon: "T_Friend_Img_Bg_Family4"
  normalIcon: "T_Friend_Img_Bg_Family4"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family3"
  normalIcon: "T_Friend_Img_Bg_Family2"
  simpleIcon: "T_Friend_Icon_Family6"
  simpleIcon: "T_Friend_Icon_Family6"
  simpleIcon: "T_Friend_Icon_Family6"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family5"
  simpleIcon: "T_Friend_Icon_Family"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  txtColor: "Intimate_Brother"
  onlineRemindLevel: 1
  relationFx1: "FX_Family_Relation_001"
  relationFx2: "FX_Family_Relation_002"
  relationFx2Height: "200"
  basicRelationFx: "FX_Family_Relation_Basic_001"
}

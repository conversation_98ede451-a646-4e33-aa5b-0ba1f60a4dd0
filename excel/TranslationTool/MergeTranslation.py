#!/usr/bin/python
# -*- coding: UTF-8 -*-

import os
import time
import openpyxl
import Consts


def main():
    print('Merge Translation Begin')

    isSuccess = True
    msg = ""

    if os.path.isfile(Consts.NEED_MERGE_TRANSLATION_FILE_NAME):
        need_merge_workbook = openpyxl.load_workbook(Consts.NEED_MERGE_TRANSLATION_FILE_NAME)
        need_merge_sheet = need_merge_workbook.get_sheet_by_name(need_merge_workbook.sheetnames[0])
        need_merge_lang_dict = {}
        for j in range(2, need_merge_sheet.max_column + 1):
            title = need_merge_sheet.cell(row=1, column=j).value
            if title in Consts.LANGUAGE_LIST:
                need_merge_lang_dict[j] = title
            if title in Consts.LANGUAGE_BACK_DICT:
                need_merge_lang_dict[j] = Consts.LANGUAGE_BACK_DICT[title]
        need_merge_content_dict = {}
        for i in range(2, need_merge_sheet.max_row + 1):
            key = need_merge_sheet.cell(row=i, column=1).value
            if key:
                need_merge_content_dict[key] = {"has_found": False, "has_merged": False}
                for j in range(2, need_merge_sheet.max_column + 1):
                    if j in need_merge_lang_dict:
                        value = need_merge_sheet.cell(row=i, column=j).value
                        if value:
                            need_merge_content_dict[key][need_merge_lang_dict[j]] = value

        workbook_list = []
        for translation_file_name in Consts.TRANSLATION_FILE_NAME_DICT.keys():
            workbook = openpyxl.load_workbook(translation_file_name, keep_vba=True)
            workbook_list.append(workbook)
            workbook.name = translation_file_name
            workbook.need_save = False
            for sheet in workbook:
                lang_dict = {}
                for j in range(2, sheet.max_column + 1):
                    lang_dict[j] = sheet.cell(row=3, column=j).value
                for i in range(4, sheet.max_row + 1):
                    key = sheet.cell(row=i, column=1).value
                    if key:
                        if key in need_merge_content_dict:
                            need_merge_content_dict[key]["has_found"] = True
                            for j in range(3, sheet.max_column + 1):
                                if j in lang_dict and lang_dict[j] in need_merge_content_dict[key]:
                                    sheet.cell(row=i, column=j).value = need_merge_content_dict[key][lang_dict[j]]
                                    sheet.cell(row=i, column=j+len(Consts.LANGUAGE_LIST)).value = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    need_merge_content_dict[key]["has_merged"] = True
                                    workbook.need_save = True

        for key, value in need_merge_content_dict.items():
            if not value["has_found"]:
                isSuccess = False
                msg = msg + "未找到的key: " + key + "\n"
            # elif not value["has_merged"]:
            #     msg = msg + u"无翻译的key: " + key + "\n"
    else:
        isSuccess = False
        msg = msg + "未找到文件: " + Consts.NEED_MERGE_TRANSLATION_FILE_NAME + "\n"

    if isSuccess:
        for workbook in workbook_list:
            if workbook.need_save:
                workbook.save(workbook.name)

    print('Merge Translation End')

    return isSuccess, msg


if __name__ == "__main__":
    main()

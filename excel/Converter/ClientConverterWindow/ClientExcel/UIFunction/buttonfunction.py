import ToolSettings
import multiprocessing
import os
import platform
import subprocess
import webbrowser
from excel.Converter.ClientConverterWindow.ClientExcel.UIFunction import checkboxfunction
from datetime import datetime
import json
import urllib.request
import urllib.error

from excel import conv2pb
from excel.ClientExcelConverter import DialogText
from excel.Converter.ClientConverterWindow.ClientExcel.ClientExcelController import ClientExcelController
from excel.Converter import ConverterUtility
import CheckRelatedTableUtil
from ExcelReaders import ExcelReaders
from excel.Converter import DeleteObsoletedPbin
import wx
import sys
import os.path
sys.path.append(os.path.join(os.getcwd(), "excel", "ClientExcelConverter"))

# 导入条件工具对话框
from excel.Converter.ClientConverterWindow.ClientExcel.UIFunction.dialog.condition import DialogCondition

CONFIG_FILE_PATH = 'ClientExcelConverter/ClientExcelConverter.json'
CommonRoot = os.path.abspath(os.path.join(os.getcwd(), ".."))  # Common根目录
OverseaFilename = os.path.join(CommonRoot, "excel", 'Oversea.txt')  # 海外版路径
dialogText = None
selectAll = False
dialogCondition = None

def GetFullPath(path):
    return './xls' + path

def OnConvert(client_excel_controller: ClientExcelController):  # 导表按钮触发,这里对所有表格都要遍历一遍看看是否选中。。。。但是这个感觉也改不了。。。。
    info = client_excel_controller.getConvertExcelList()
    if len(info) == 0:
        client_excel_controller.Error('请至少选中一个excel')
    else:
        DoConvert(client_excel_controller, info)


def OnDismantlingTable(client_excel_controller: ClientExcelController):
    excel_readers = client_excel_controller.GetExcelReaders()
    info = client_excel_controller.getConvertExcelList()
    if client_excel_controller.NeedConvertWhole():
        #feature_name = client_excel_controller.GetConfigJsonValueByName('feature')
        for feature_name, feature_info in info.items():
            excel_readers.AppendExeclToExcelInfoAsSameProtoMessageType(feature_info, feature_name)
        DoConvert(client_excel_controller, info)
    else:
        client_excel_controller.Error('请至少选中一个excel')


def OnOpenConverterFolder(client_excel_controller: ClientExcelController):  # 打开导表工具所在文件夹也就是\LetsGoDevelop\letsgo_common\excel
    tool_setting = ToolSettings.InitToolSettings()
    feature_names = client_excel_controller.GetConfigJsonValueByName('feature')
    for feature_name in feature_names:
        feature_setting = tool_setting.GetFeatureToolSetting(feature_name)
        xls_folder = getattr(feature_setting, 'EXCEL_XLS_DIR', None)
        if platform.system() == "Windows":
            os.system("explorer " + os.path.abspath(xls_folder))
        else:
            os.system("open " + os.path.abspath(xls_folder))

def OnRefresh(client_excel_controller: ClientExcelController):
    # client_excel_controller.ParseResConvertMap()
    client_excel_controller.InitTreeItems()
    client_excel_controller.ResetTreeItems()
    #client_excel_controller.SortTreeChildren()
    client_excel_controller.InitLog()

def OnText(client_excel_controller: ClientExcelController):
    global dialogText
    if not dialogText:
        dialogText = DialogText.DialogText(client_excel_controller.GetFrame(), client_excel_controller)

    dialogText.ShowModal()


def OnExcelProtocol(client_excel_controller: ClientExcelController):
    RunBat('../excel/client/client_generate_protocol.bat')


def OnCSProtocol(client_excel_controller: ClientExcelController):
    RunBat('../protos/client_proto/run4client.bat')


def OnReload(client_excel_controller: ClientExcelController):
    RunBat("../../private_svr/reload本地配置至私服.bat", "1")

def OnCfgProtocol(client_excel_controller: ClientExcelController):
    print("开始导表，无日志输出。可以打开LogNet.txt查看运行日志")
    RunBat('../clientTools/【协议导表PB】【新】- LetsGo 无Pause.bat')


def OnHelp(client_excel_controller: ClientExcelController):
    webbrowser.open("https://iwiki.woa.com/p/4013619938", new=2)


def OpenPbinOrderConfig(client_excel_controller: ClientExcelController):
    if platform.system() == "Windows":
        os.system(
            "explorer " + os.path.abspath(os.path.join(os.getcwd(), "RelatedTable.json")))
    else:
        os.system("open " + os.path.abspath(os.path.join(os.getcwd(), "RelatedTable.json")))


def OnSelectAll(client_excel_controller: ClientExcelController):
    global selectAll
    selectAll = not selectAll
    client_excel_controller.CheckTreeAllChildren(selectAll)


def NeedCommit(client_excel_controller: ClientExcelController, file_path):
    if client_excel_controller.HasGit():
        status = client_excel_controller.GetFileStatus(file_path)
        if status.startswith('M') or status.startswith('A'):
            return True
        else:
            return False
    else:
        return False


_ConvertTask = []
_CurrentCovertTask = None
class ConvertTaskRunner:
    def __init__(self, frame, info, client_excel_controller):
        self._need_accelerate = client_excel_controller.GetConfigJsonValueByName('convert_accelerate')
        self._disable_full_check = client_excel_controller.GetConfigJsonValueByName('disable_full_check')
        self._enable_obselete_pbin_check = client_excel_controller.GetConfigJsonValueByName('enable_obselete_pbin_check')
        self._has_git = client_excel_controller.HasGit()
        self._frame = frame
        self._timer = wx.Timer(self._frame)
        self._client_process = None
        self._client_process_error_code = 0
        self._server_process = None
        self._server_process_error_code = 0
        self._handling_info = None
        self._client_excel_controller = client_excel_controller
        self._loading_dialog = None
        self._process_manager = None
        self._client_process_log_list = None
        self._server_process_log_list = None
        self._info_list = []
        self._info = info


    def _CreateLoading(self):
        # 拉起的进度跳是模态对话框，背景主ui不可交互
        self._loading_dialog = self._client_excel_controller.CreateLoadingDialog()
        self._loading_dialog.Start()
        self._loading_dialog.UpdateProgress(5, "准备开始导表...")


    def _CreatePrecessQueue(self):
        self._process_manager = multiprocessing.Manager()
        self._client_process_log_list = self._process_manager.Queue()
        self._server_process_log_list = self._process_manager.Queue()

    def _CheckGitClicted(self, info):
        for info_key in info:
            for key in info_key:
                if self._client_excel_controller.IsConflicted(key, None):
                    self._client_excel_controller.ShowMessageBox(key + '存在git冲突，请先解决冲突', '')
                    return

    def _HandleInfoRelated(self, info):
        excel_reader = ExcelReaders.GetDefaultExcelReader()
        main_info = {}
        all_proto_message_types = {}
        main_excel_group = excel_reader.GetExcelGroupByExcelPath(ConverterUtility.MAIN_FEATURE_NAME, main_info)
        for feature_name, feature_info in info.items():
            if ConverterUtility.IsLetsGoFeature(feature_name):
                CheckRelatedTableUtil.MergeConvertInfos(main_info, feature_info)
                continue

            # 检查依赖
            util = CheckRelatedTableUtil.GetFeatureInstance(feature_name)
            excel_group = excel_reader.GetExcelGroupByExcelPath(feature_name, feature_info)
            feature_proto_message_types, main_feature_depends_info = util.AddRelatedTables(excel_group)
            all_proto_message_types.update(feature_proto_message_types)
            CheckRelatedTableUtil.MergeConvertInfos(main_info, main_feature_depends_info)

        # 子玩法导表，相关message type，需要检查主仓库对应的message type需不需要一起导
        for proto_message_type in all_proto_message_types.keys():
            excel_reader.AppendExcelToConvertGroupByProtoMessageType(main_excel_group, ConverterUtility.MAIN_FEATURE_NAME, proto_message_type)

        # 合并得到最终的主仓库导表info
        excel_reader.AppendExcelToExcelGroupByInfos(main_excel_group, ConverterUtility.MAIN_FEATURE_NAME, main_info)
        # 主仓库需要再检查一次依赖
        if len(main_excel_group.ExcelDescList) > 0:
            util = CheckRelatedTableUtil.GetFeatureInstance(ConverterUtility.MAIN_FEATURE_NAME)
            util.AddRelatedTables(main_excel_group)

        count_excel = 0
        main_info = excel_reader.ExcelGroupAsConvertInfo(main_excel_group)
        if len(main_info) > 0:
            self._info_list.append({"feature_name": "main", "info": main_info})
            count_excel += len(main_info)


        for feature_name, feature_info in info.items():
            if ConverterUtility.IsLetsGoFeature(feature_name):
                continue
            self._info_list.append({"feature_name": feature_name, "info": feature_info})
            count_excel += len(feature_info)

        if count_excel == 0:
            count_excel = 1
        speed = 40 / count_excel

        if speed > 1:
            speed = 1

        if speed < 0.3:
            speed = 0.3
        self._loading_dialog.SetAutoSpeed(speed)

    def Run(self):
        try:
            self._CreateLoading()
            self._CreatePrecessQueue()
            self._CheckGitClicted(self._info)
            self._HandleInfoRelated(self._info)

            self._frame.Bind(wx.EVT_TIMER, self._Tick, self._timer)
            self._timer.Start(50)
            self._loading_dialog.UpdateProgress(95, "正在努力导表中，请耐心等待...")
            self._TryConvertNextFeature()
        except Exception as e:
            self._client_excel_controller.Error(f"导表异常：{e}")
            self.Finally()

    def _RunConvertProcess(self, feature_name, info):
        self._handling_feature_name = feature_name
        game_name = "LetsGo"
        tool_setting = ToolSettings.InitToolSettings()
        feature_tool_setting = tool_setting.GetFeatureToolSetting(feature_name)
        feature_excel_dir = feature_tool_setting.EXCEL_DIR

        self._client_process = multiprocessing.Process(target=conv2pb.main, args=(
            feature_excel_dir, info, self._has_git, True, game_name, self._need_accelerate, False, self._disable_full_check, True,
            True, self._client_process_log_list, False, self._enable_obselete_pbin_check))
        self._client_process.start()
        self._client_process_error_code = None

        self._server_process = multiprocessing.Process(target=conv2pb.main, args=(
            feature_excel_dir, info, self._has_git, False, game_name, self._need_accelerate, False, self._disable_full_check, True,
            True, self._server_process_log_list, False, self._enable_obselete_pbin_check))
        self._server_process.start()
        self._server_process_error_code = None

    def _TryConvertNextFeature(self):
        if len(self._info_list) == 0:
            self.Finally()
            return False
        info = self._info_list.pop(0)
        self._handling_info = info
        self._RunConvertProcess(info["feature_name"], info["info"])

        return True

    def Finally(self):
        self._loading_dialog.UpdateProgress(100, "导表完成！！！")
        self._loading_dialog.Close()
        self._timer.Stop()
        if self._client_process and self._client_process.is_alive():
            self._client_process.terminate()

        if self._server_process and self._server_process.is_alive():
            self._server_process.terminate()
        global selectAll
        selectAll = False
        self._client_excel_controller.WuHu('===== 导表完毕 =====')

    def _HandleProcess(self, process, logs_queue):
        error_code = None
        while not logs_queue.empty():
            message = logs_queue.get()
            t_error_code, message = self._ParseMessage(message)
            if t_error_code is not None:
                error_code = t_error_code
            if error_code is not None and error_code < 0:
                self._client_excel_controller.Error(message)

            if t_error_code == 1:
                # 更新进度信息
                pass

        if not process.is_alive():
            if error_code is None:
                # 进程异常结束
                error_code = -999

        return error_code

    def _Tick(self, event):
        client_error_code = self._HandleProcess(self._client_process, self._client_process_log_list)

        server_error_code = self._HandleProcess(self._server_process, self._server_process_log_list)

        if client_error_code is not None:
            if self._client_process_error_code is None and client_error_code == -999:
                self._client_excel_controller.Error(f"{self._handling_info['feature_name']}: 客户端导表进程异常结束！！！")
            self._client_process_error_code = client_error_code

        if server_error_code is not None:
            if self._server_process_error_code is None and server_error_code == -999:
                self._client_excel_controller.Error(f"{self._handling_info['feature_name']}: 服务器导表进程异常结束！！！")
            self._server_process_error_code = server_error_code

        if self._client_process_error_code is not None and self._server_process_error_code is not None:
            # 均已完成
            self._client_excel_controller.UpdateConv2pb_set(self._handling_info.keys())
            self._TryConvertNextFeature()
        # 继续等待下一次检查

    def _ParseMessage(self, message):
        message_arr = message.split(":")
        error_code = None
        message_start_index = 0
        if len(message_arr) > 1 and message_arr[0] == "code":
            error_code = int(message_arr[1])
            message_start_index = 2

        n_message = ''.join(message_arr[message_start_index:])

        return error_code, n_message


# 这里是导表过程
def DoConvert(client_excel_controller: ClientExcelController, info):
    handler = ConvertTaskRunner(client_excel_controller.GetFrame(), info, client_excel_controller)
    handler.Run()

def ChangeFeatureMode(client_excel_controller: ClientExcelController, feature_name):
    cur_features = client_excel_controller.GetConfigJsonValueByName('feature')
    ans_feature = None
    for feature in cur_features:
        if feature_name == feature:
            ans_feature = feature
            break
    if ans_feature is not None:
        cur_features.remove(ans_feature)
        client_excel_controller.SetJsonValueByName(feature_name, False)
    else:
        cur_features.append(feature_name)
        client_excel_controller.SetJsonValueByName(feature_name, True)
    client_excel_controller.SetJsonValueByName('feature', cur_features)

    client_excel_controller.ResetTreeItems()

    global selectAll
    selectAll = False

    checkboxfunction.OnSaveSort(client_excel_controller)


def ParseCustomMap():
    custom_dict = []
    with open('config/res_convert_Custom.txt', 'r', encoding='utf-8') as f:
        for line in f.readlines():
            line = line.rstrip('\n')
            line = line.replace("excel/xls", "")
            custom_dict.append(line)
            print(line)
    return custom_dict


def RunBat(batFile, param=""):
    if platform.system() != "Windows":
        batFile = batFile.replace('.bat', '.sh')
    fullpath = os.path.abspath(batFile)
    d = os.path.dirname(fullpath)
    cmd = fullpath + " " + param
    print("RunBat: ", cmd)
    p = subprocess.Popen( ["cmd", "/k", cmd], cwd=d,creationflags=subprocess.CREATE_NEW_CONSOLE)
    stdout, stderr = p.communicate()
    rc = p.returncode
    if rc != 0:
        raise RuntimeError('RunBat Error, file: {} '.format(fullpath))
    print(stdout)

def OnOpenLog(client_excel_controller: ClientExcelController):
    commonRootPath = ConverterUtility.GetCommonRootPath()
    LogDir = os.path.join(commonRootPath, 'excel', 'ConverterLogs')
    if not os.path.exists(LogDir):
        os.makedirs(LogDir)
    if platform.system() == "Windows":
        os.system("explorer " + LogDir)
    else:
        os.system("open " + LogDir)

def OnCondition(client_excel_controller: ClientExcelController):
    """打开条件工具窗口"""
    global dialogCondition
    if not dialogCondition:
        dialogCondition = DialogCondition(client_excel_controller.GetFrame(), client_excel_controller)
    
    # 每次打开对话框时刷新条件列表
    wx.CallAfter(dialogCondition.LoadConditions, None)
    
    dialogCondition.ShowModal()

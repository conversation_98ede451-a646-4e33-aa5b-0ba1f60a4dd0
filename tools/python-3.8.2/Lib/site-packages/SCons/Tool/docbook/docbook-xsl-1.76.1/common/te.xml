<?xml version="1.0" encoding="utf-8"?>
<l:l10n xmlns:l="http://docbook.sourceforge.net/xmlns/l10n/1.0" language="te" english-language-name="Telugu">

<!-- * This file is generated automatically. -->
<!-- * To submit changes to this file upstream (to the DocBook Project) -->
<!-- * do not submit an edited version of this file. Instead, submit an -->
<!-- * edited version of the source file at the following location: -->
<!-- * -->
<!-- *  https://docbook.svn.sourceforge.net/svnroot/docbook/trunk/gentext/locale/te.xml -->
<!-- * -->
<!-- * E-mail the edited te.xml source file to: -->
<!-- * -->
<!-- *  <EMAIL> -->

<!-- ******************************************************************** -->

<!-- This file is part of the XSL DocBook Stylesheet distribution. -->
<!-- See ../README or http://docbook.sf.net/release/xsl/current/ for -->
<!-- copyright and other information. -->

<!-- ******************************************************************** -->
<!-- In these files, % with a letter is used for a placeholder: -->
<!--   %t is the current element's title -->
<!--   %s is the current element's subtitle (if applicable)-->
<!--   %n is the current element's number label-->
<!--   %p is the current element's page number (if applicable)-->
<!-- ******************************************************************** -->


<l:gentext key="Abstract" text="సంక్షిప్తము"/>
<l:gentext key="abstract" text="సంక్షిప్తము"/>
<l:gentext key="Acknowledgements" text="గుర్తింపులు"/>
<l:gentext key="acknowledgements" text="గుర్తింపులు"/>
<l:gentext key="Answer" text="సమాధానం:"/>
<l:gentext key="answer" text="సమాధానం:"/>
<l:gentext key="Appendix" text="అనుబంధం"/>
<l:gentext key="appendix" text="అనుబంధం"/>
<l:gentext key="Article" text="ప్రకరణము"/>
<l:gentext key="article" text="ప్రకరణము"/>
<l:gentext key="Author" text="గ్రంధకర్త"/>
<l:gentext key="Bibliography" text="గ్రంధ పట్టిక"/>
<l:gentext key="bibliography" text="గ్రంధ పట్టిక"/>
<l:gentext key="Book" text="పుస్తకము"/>
<l:gentext key="book" text="పుస్తకము"/>
<l:gentext key="CAUTION" text="ముందుజాగ్రత్త"/>
<l:gentext key="Caution" text="ముందుజాగ్రత్త"/>
<l:gentext key="caution" text="ముందుజాగ్రత్త"/>
<l:gentext key="Chapter" text="అధ్యాయము"/>
<l:gentext key="chapter" text="అధ్యాయము"/>
<l:gentext key="Colophon" text="చివరిమాట"/>
<l:gentext key="colophon" text="చివరిమాట"/>
<l:gentext key="Copyright" text="కాపీరైటు"/>
<l:gentext key="copyright" text="కాపీరైటు"/>
<l:gentext key="Dedication" text="అంకితం"/>
<l:gentext key="dedication" text="అంకితం"/>
<l:gentext key="Edition" text="సంచిక"/>
<l:gentext key="edition" text="సంచిక"/>
<l:gentext key="Editor" text="సంపాదకుడు"/>
<l:gentext key="Equation" text="సమీకరణము"/>
<l:gentext key="equation" text="సమీకరణము"/>
<l:gentext key="Example" text="ఉదాహరణ"/>
<l:gentext key="example" text="ఉదాహరణ"/>
<l:gentext key="Figure" text="మూర్తి"/>
<l:gentext key="figure" text="మూర్తి"/>
<l:gentext key="Glossary" text="పదకోశం"/>
<l:gentext key="glossary" text="పదకోశం"/>
<l:gentext key="GlossSee" text="చూడుము"/>
<l:gentext key="glosssee" text="చూడుము"/>
<l:gentext key="GlossSeeAlso" text="ఇదికూడా చూడుము"/>
<l:gentext key="glossseealso" text="ఇదికూడా చూడుము"/>
<l:gentext key="IMPORTANT" text="ముఖ్యమైన"/>
<l:gentext key="important" text="ముఖ్యమైన"/>
<l:gentext key="Important" text="ముఖ్యమైన"/>
<l:gentext key="Index" text="విషయసూచిక"/>
<l:gentext key="index" text="విషయసూచిక"/>
<l:gentext key="ISBN" text="ISBN"/>
<l:gentext key="isbn" text="ISBN"/>
<l:gentext key="LegalNotice" text="చట్టబద్ద నోటీసు"/>
<l:gentext key="legalnotice" text="చట్టబద్ద నోటీసు"/>
<l:gentext key="MsgAud" text="ప్రేక్షకులు"/>
<l:gentext key="msgaud" text="ప్రేక్షకులు"/>
<l:gentext key="MsgLevel" text="స్థాయి"/>
<l:gentext key="msglevel" text="స్థాయి"/>
<l:gentext key="MsgOrig" text="మూలము"/>
<l:gentext key="msgorig" text="మూలము"/>
<l:gentext key="NOTE" text="గమనిక"/>
<l:gentext key="Note" text="గమనిక"/>
<l:gentext key="note" text="గమనిక"/>
<l:gentext key="Part" text="భాగము"/>
<l:gentext key="part" text="భాగము"/>
<l:gentext key="Preface" text="ముందుమాట"/>
<l:gentext key="preface" text="ముందుమాట"/>
<l:gentext key="Procedure" text="పద్ధతి"/>
<l:gentext key="procedure" text="పద్ధతి"/>
<l:gentext key="ProductionSet" text="ఉత్పత్తి"/>
<l:gentext key="PubDate" text="ప్రచురణ తేది"/>
<l:gentext key="pubdate" text="ప్రచురణ తేది"/>
<l:gentext key="Published" text="ప్రచురితమైంది"/>
<l:gentext key="published" text="ప్రచురితమైంది"/>
<l:gentext key="Publisher" text="ప్రచురణకర్త"/>
<l:gentext key="Qandadiv" text="ప్రశ్నలు &amp; సమాధానములు"/>
<l:gentext key="qandadiv" text="ప్రశ్నలు &amp; సమాధానములు"/>
<l:gentext key="QandASet" text="తరచుగా అడుగు ప్రశ్నలు"/>
<l:gentext key="Question" text="ప్రశ్న:"/>
<l:gentext key="question" text="ప్రశ్న:"/>
<l:gentext key="RefEntry" text="పేజీ"/>
<l:gentext key="refentry" text="పేజీ"/>
<l:gentext key="Reference" text="సంభందిత"/>
<l:gentext key="reference" text="సంభందిత"/>
<l:gentext key="References" text="సంభందితములు"/>
<l:gentext key="RefName" text="నామము"/>
<l:gentext key="refname" text="నామము"/>
<l:gentext key="RefSection" text="విభాగము"/>
<l:gentext key="refsection" text="విభాగము"/>
<l:gentext key="RefSynopsisDiv" text="ముఖ్యవిషయసూచిక"/>
<l:gentext key="refsynopsisdiv" text="ముఖ్యవిషయసూచిక"/>
<l:gentext key="RevHistory" text="పునఃపరిశీలన చరిత్ర"/>
<l:gentext key="revhistory" text="పునఃపరిశీలన చరిత్ర"/>
<l:gentext key="revision" text="పునఃపరిశీలన"/>
<l:gentext key="Revision" text="పునఃపరిశీలన"/>
<l:gentext key="sect1" text="విభాగము"/>
<l:gentext key="sect2" text="విభాగము"/>
<l:gentext key="sect3" text="విభాగము"/>
<l:gentext key="sect4" text="విభాగము"/>
<l:gentext key="sect5" text="విభాగము"/>
<l:gentext key="section" text="విభాగము"/>
<l:gentext key="Section" text="విభాగము"/>
<l:gentext key="see" text="చూడుము"/>
<l:gentext key="See" text="చూడుము"/>
<l:gentext key="seealso" text="ఇదికూడా చూడుము"/>
<l:gentext key="Seealso" text="ఇదికూడా చూడుము"/>
<l:gentext key="SeeAlso" text="ఇదికూడా చూడుము"/>
<l:gentext key="set" text="సమితి"/>
<l:gentext key="Set" text="సమితి"/>
<l:gentext key="setindex" text="సమితి విషయసూచిక"/>
<l:gentext key="SetIndex" text="సమితి విషయసూచిక"/>
<l:gentext key="Sidebar" text="ప్రక్కపట్టీ"/>
<l:gentext key="sidebar" text="ప్రక్కపట్టీ"/>
<l:gentext key="step" text="అంచె"/>
<l:gentext key="Step" text="అంచె"/>
<l:gentext key="table" text="పట్టిక"/>
<l:gentext key="Table" text="పట్టిక"/>
<l:gentext key="task" text="కర్తవ్యము"/>
<l:gentext key="Task" text="కర్తవ్యము"/>
<l:gentext key="tip" text="చిట్కా"/>
<l:gentext key="TIP" text="చిట్కా"/>
<l:gentext key="Tip" text="చిట్కా"/>
<l:gentext key="Warning" text="హెచ్చరిక"/>
<l:gentext key="warning" text="హెచ్చరిక"/>
<l:gentext key="WARNING" text="హెచ్చరిక"/>
<l:gentext key="and" text="మరియు"/>
<l:gentext key="by" text="వీరిచేత"/>
<l:gentext key="Edited" text="సరికూర్చిన"/>
<l:gentext key="edited" text="సరికూర్చిన"/>
<l:gentext key="Editedby" text="వీరిచేత సరికూర్చబడింది"/>
<l:gentext key="editedby" text="వీరిచేత సరికూర్చబడింది"/>
<l:gentext key="in" text="నందు"/>
<l:gentext key="lastlistcomma" text=","/>
<l:gentext key="listcomma" text=","/>
<l:gentext key="notes" text="గమనికలు"/>
<l:gentext key="Notes" text="గమనికలు"/>
<l:gentext key="Pgs" text="పేజీలు"/>
<l:gentext key="pgs" text="పేజీలు"/>
<l:gentext key="Revisedby" text="వీరిచేత పునఃపరిశీలించబడింది: "/>
<l:gentext key="revisedby" text="వీరిచేత పునఃపరిశీలించబడింది: "/>
<l:gentext key="TableNotes" text="గమనికలు"/>
<l:gentext key="tablenotes" text="గమనికలు"/>
<l:gentext key="TableofContents" text="సారముల పట్టిక"/>
<l:gentext key="tableofcontents" text="సారముల పట్టిక"/>
<l:gentext key="unexpectedelementname" text="అనుకోని మూలకం నామము"/>
<l:gentext key="unsupported" text="మద్దతీయని"/>
<l:gentext key="xrefto" text="xref"/>
<l:gentext key="Authors" text="గ్రంధ కర్తలు"/>
<l:gentext key="copyeditor" text="కాపీ సంపాదకుడు"/>
<l:gentext key="graphicdesigner" text="గ్రాఫిక్ రూపకర్త"/>
<l:gentext key="productioneditor" text="ఉత్పత్తి సంపాదకుడు"/>
<l:gentext key="technicaleditor" text="సాంకేతిక సంపాదకుడు"/>
<l:gentext key="translator" text="అనువాదకుడు"/>
<l:gentext key="listofequations" text="సమీకరణముల జాబితా"/>
<l:gentext key="ListofEquations" text="సమీకరణముల జాబితా"/>
<l:gentext key="ListofExamples" text="ఉదాహరణముల జాబితా"/>
<l:gentext key="listofexamples" text="ఉదాహరణముల జాబితా"/>
<l:gentext key="ListofFigures" text="మూర్తుల జాబితా"/>
<l:gentext key="listoffigures" text="మూర్తుల జాబితా"/>
<l:gentext key="ListofProcedures" text="పద్ధతుల జాబితా"/>
<l:gentext key="listofprocedures" text="పద్ధతుల జాబితా"/>
<l:gentext key="listoftables" text="పట్టికల జాబితా"/>
<l:gentext key="ListofTables" text="పట్టికల జాబితా"/>
<l:gentext key="ListofUnknown" text="తెలియనివాటి జాబితా"/>
<l:gentext key="listofunknown" text="తెలియనివాటి జాబితా"/>
<l:gentext key="nav-home" text="నివాసము"/>
<l:gentext key="nav-next" text="తరువాతి"/>
<l:gentext key="nav-next-sibling" text="ముందుకు నడుపు"/>
<l:gentext key="nav-prev" text="ముందరి"/>
<l:gentext key="nav-prev-sibling" text="వెనుకకు నడుపు"/>
<l:gentext key="nav-up" text="పైనకు"/>
<l:gentext key="nav-toc" text="వివరముల పట్టిక"/>
<l:gentext key="Draft" text="ముసాయిదా"/>
<l:gentext key="above" text="పైన"/>
<l:gentext key="below" text="క్రింది"/>
<l:gentext key="sectioncalled" text="ఆ విభాగము పిలువబడుతుంది"/>
<l:gentext key="index symbols" text="చిహ్నములు"/>
<l:gentext key="writing-mode" text="lr-tb"/>
<l:gentext key="lowercase.alpha" text="abcdefghijklmnopqrstuvwxyz"/>
<l:gentext key="uppercase.alpha" text="ABCDEFGHIJKLMNOPQRSTUVWXYZ"/>
<l:gentext key="normalize.sort.input" text="AaÀàÁáÂâÃãÄäÅåĀāĂăĄąǍǎǞǟǠǡǺǻȀȁȂȃȦȧḀḁẚẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặBbƀƁɓƂƃḂḃḄḅḆḇCcÇçĆćĈĉĊċČčƇƈɕḈḉDdĎďĐđƊɗƋƌǅǲȡɖḊḋḌḍḎḏḐḑḒḓEeÈèÉéÊêËëĒēĔĕĖėĘęĚěȄȅȆȇȨȩḔḕḖḗḘḙḚḛḜḝẸẹẺẻẼẽẾếỀềỂểỄễỆệFfƑƒḞḟGgĜĝĞğĠġĢģƓɠǤǥǦǧǴǵḠḡHhĤĥĦħȞȟɦḢḣḤḥḦḧḨḩḪḫẖIiÌìÍíÎîÏïĨĩĪīĬĭĮįİƗɨǏǐȈȉȊȋḬḭḮḯỈỉỊịJjĴĵǰʝKkĶķƘƙǨǩḰḱḲḳḴḵLlĹĺĻļĽľĿŀŁłƚǈȴɫɬɭḶḷḸḹḺḻḼḽMmɱḾḿṀṁṂṃNnÑñŃńŅņŇňƝɲƞȠǋǸǹȵɳṄṅṆṇṈṉṊṋOoÒòÓóÔôÕõÖöØøŌōŎŏŐőƟƠơǑǒǪǫǬǭǾǿȌȍȎȏȪȫȬȭȮȯȰȱṌṍṎṏṐṑṒṓỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợPpƤƥṔṕṖṗQqʠRrŔŕŖŗŘřȐȑȒȓɼɽɾṘṙṚṛṜṝṞṟSsŚśŜŝŞşŠšȘșʂṠṡṢṣṤṥṦṧṨṩTtŢţŤťŦŧƫƬƭƮʈȚțȶṪṫṬṭṮṯṰṱẗUuÙùÚúÛûÜüŨũŪūŬŭŮůŰűŲųƯưǓǔǕǖǗǘǙǚǛǜȔȕȖȗṲṳṴṵṶṷṸṹṺṻỤụỦủỨứỪừỬửỮữỰựVvƲʋṼṽṾṿWwŴŵẀẁẂẃẄẅẆẇẈẉẘXxẊẋẌẍYyÝýÿŸŶŷƳƴȲȳẎẏẙỲỳỴỵỶỷỸỹZzŹźŻżŽžƵƶȤȥʐʑẐẑẒẓẔẕẕ" lang="en"/>
<l:gentext key="normalize.sort.output" text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABBBBBBBBBBBBBCCCCCCCCCCCCCCCCCDDDDDDDDDDDDDDDDDDDDDDDDEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEFFFFFFGGGGGGGGGGGGGGGGGGGGHHHHHHHHHHHHHHHHHHHHIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIJJJJJJKKKKKKKKKKKKKKLLLLLLLLLLLLLLLLLLLLLLLLLLMMMMMMMMMNNNNNNNNNNNNNNNNNNNNNNNNNNNOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOPPPPPPPPQQQRRRRRRRRRRRRRRRRRRRRRRRSSSSSSSSSSSSSSSSSSSSSSSTTTTTTTTTTTTTTTTTTTTTTTTTUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUVVVVVVVVWWWWWWWWWWWWWWWXXXXXXYYYYYYYYYYYYYYYYYYYYYYYZZZZZZZZZZZZZZZZZZZZZ" lang="en"/>
<l:dingbat key="startquote" text="“"/>
<l:dingbat key="endquote" text="”"/>
<l:dingbat key="nestedstartquote" text="‘"/>
<l:dingbat key="nestedendquote" text="’"/>
<l:dingbat key="singlestartquote" text="‘"/>
<l:dingbat key="singleendquote" text="’"/>
<l:dingbat key="bullet" text="•"/>
<l:gentext key="hyphenation-character" text="-"/>
<l:gentext key="hyphenation-push-character-count" text="2"/>
<l:gentext key="hyphenation-remain-character-count" text="2"/>
<l:context name="styles"><l:template name="person-name" text="first-last"/>
</l:context>
<l:context name="title"><l:template name="abstract" text="%t"/>
<l:template name="acknowledgements" text="%t"/>
<l:template name="answer" text="%t"/>
<l:template name="appendix" text="అనుబంధం %n. %t"/>
<l:template name="article" text="%t"/>
<l:template name="authorblurb" text="%t"/>
<l:template name="bibliodiv" text="%t"/>
<l:template name="biblioentry" text="%t"/>
<l:template name="bibliography" text="%t"/>
<l:template name="bibliolist" text="%t"/>
<l:template name="bibliomixed" text="%t"/>
<l:template name="bibliomset" text="%t"/>
<l:template name="biblioset" text="%t"/>
<l:template name="blockquote" text="%t"/>
<l:template name="book" text="%t"/>
<l:template name="calloutlist" text="%t"/>
<l:template name="caution" text="%t"/>
<l:template name="chapter" text="అధ్యాయము %n. %t"/>
<l:template name="colophon" text="%t"/>
<l:template name="dedication" text="%t"/>
<l:template name="equation" text="సమీకరణము %n. %t"/>
<l:template name="example" text="ఉదాహరణ %n. %t"/>
<l:template name="figure" text="మూర్తి %n. %t"/>
<l:template name="foil" text="%t"/>
<l:template name="foilgroup" text="%t"/>
<l:template name="formalpara" text="%t"/>
<l:template name="glossary" text="%t"/>
<l:template name="glossdiv" text="%t"/>
<l:template name="glosslist" text="%t"/>
<l:template name="glossentry" text="%t"/>
<l:template name="important" text="%t"/>
<l:template name="index" text="%t"/>
<l:template name="indexdiv" text="%t"/>
<l:template name="itemizedlist" text="%t"/>
<l:template name="legalnotice" text="%t"/>
<l:template name="listitem" text=""/>
<l:template name="lot" text="%t"/>
<l:template name="msg" text="%t"/>
<l:template name="msgexplan" text="%t"/>
<l:template name="msgmain" text="%t"/>
<l:template name="msgrel" text="%t"/>
<l:template name="msgset" text="%t"/>
<l:template name="msgsub" text="%t"/>
<l:template name="note" text="%t"/>
<l:template name="orderedlist" text="%t"/>
<l:template name="part" text="భాగము %n. %t"/>
<l:template name="partintro" text="%t"/>
<l:template name="preface" text="%t"/>
<l:template name="procedure" text="%t"/>
<l:template name="procedure.formal" text="పద్ధతి %n. %t"/>
<l:template name="productionset" text="%t"/>
<l:template name="productionset.formal" text="ఉత్పత్తి %n"/>
<l:template name="qandadiv" text="%t"/>
<l:template name="qandaentry" text="%t"/>
<l:template name="qandaset" text="%t"/>
<l:template name="question" text="%t"/>
<l:template name="refentry" text="%t"/>
<l:template name="reference" text="%t"/>
<l:template name="refsection" text="%t"/>
<l:template name="refsect1" text="%t"/>
<l:template name="refsect2" text="%t"/>
<l:template name="refsect3" text="%t"/>
<l:template name="refsynopsisdiv" text="%t"/>
<l:template name="refsynopsisdivinfo" text="%t"/>
<l:template name="segmentedlist" text="%t"/>
<l:template name="set" text="%t"/>
<l:template name="setindex" text="%t"/>
<l:template name="sidebar" text="%t"/>
<l:template name="step" text="%t"/>
<l:template name="table" text="పట్టిక %n. %t"/>
<l:template name="task" text="%t"/>
<l:template name="tasksummary" text="%t"/>
<l:template name="taskprerequisites" text="%t"/>
<l:template name="taskrelated" text="%t"/>
<l:template name="tip" text="%t"/>
<l:template name="toc" text="%t"/>
<l:template name="variablelist" text="%t"/>
<l:template name="varlistentry" text=""/>
<l:template name="warning" text="%t"/>
</l:context>
<l:context name="title-unnumbered"><l:template name="appendix" text="%t"/>
<l:template name="article/appendix" text="%t"/>
<l:template name="bridgehead" text="%t"/>
<l:template name="chapter" text="%t"/>
<l:template name="sect1" text="%t"/>
<l:template name="sect2" text="%t"/>
<l:template name="sect3" text="%t"/>
<l:template name="sect4" text="%t"/>
<l:template name="sect5" text="%t"/>
<l:template name="section" text="%t"/>
<l:template name="simplesect" text="%t"/>
<l:template name="part" text="%t"/>
</l:context>
<l:context name="title-numbered"><l:template name="appendix" text="అనుబంధం %n. %t"/>
<l:template name="article/appendix" text="%n. %t"/>
<l:template name="bridgehead" text="%n. %t"/>
<l:template name="chapter" text="అధ్యాయము %n. %t"/>
<l:template name="part" text="భాగము %n. %t"/>
<l:template name="sect1" text="%n. %t"/>
<l:template name="sect2" text="%n. %t"/>
<l:template name="sect3" text="%n. %t"/>
<l:template name="sect4" text="%n. %t"/>
<l:template name="sect5" text="%n. %t"/>
<l:template name="section" text="%n. %t"/>
<l:template name="simplesect" text="%t"/>
</l:context>
<l:context name="subtitle"><l:template name="appendix" text="%s"/>
<l:template name="acknowledgements" text="%s"/>
<l:template name="article" text="%s"/>
<l:template name="bibliodiv" text="%s"/>
<l:template name="biblioentry" text="%s"/>
<l:template name="bibliography" text="%s"/>
<l:template name="bibliomixed" text="%s"/>
<l:template name="bibliomset" text="%s"/>
<l:template name="biblioset" text="%s"/>
<l:template name="book" text="%s"/>
<l:template name="chapter" text="%s"/>
<l:template name="colophon" text="%s"/>
<l:template name="dedication" text="%s"/>
<l:template name="glossary" text="%s"/>
<l:template name="glossdiv" text="%s"/>
<l:template name="index" text="%s"/>
<l:template name="indexdiv" text="%s"/>
<l:template name="lot" text="%s"/>
<l:template name="part" text="%s"/>
<l:template name="partintro" text="%s"/>
<l:template name="preface" text="%s"/>
<l:template name="refentry" text="%s"/>
<l:template name="reference" text="%s"/>
<l:template name="refsection" text="%s"/>
<l:template name="refsect1" text="%s"/>
<l:template name="refsect2" text="%s"/>
<l:template name="refsect3" text="%s"/>
<l:template name="refsynopsisdiv" text="%s"/>
<l:template name="sect1" text="%s"/>
<l:template name="sect2" text="%s"/>
<l:template name="sect3" text="%s"/>
<l:template name="sect4" text="%s"/>
<l:template name="sect5" text="%s"/>
<l:template name="section" text="%s"/>
<l:template name="set" text="%s"/>
<l:template name="setindex" text="%s"/>
<l:template name="sidebar" text="%s"/>
<l:template name="simplesect" text="%s"/>
<l:template name="toc" text="%s"/>
</l:context>
<l:context name="xref"><l:template name="abstract" text="%t"/>
<l:template name="acknowledgements" text="%t"/>
<l:template name="answer" text="సమాధానం: %n"/>
<l:template name="appendix" text="%t"/>
<l:template name="article" text="%t"/>
<l:template name="authorblurb" text="%t"/>
<l:template name="bibliodiv" text="%t"/>
<l:template name="bibliography" text="%t"/>
<l:template name="bibliomset" text="%t"/>
<l:template name="biblioset" text="%t"/>
<l:template name="blockquote" text="%t"/>
<l:template name="book" text="%t"/>
<l:template name="calloutlist" text="%t"/>
<l:template name="caution" text="%t"/>
<l:template name="chapter" text="%t"/>
<l:template name="colophon" text="%t"/>
<l:template name="constraintdef" text="%t"/>
<l:template name="dedication" text="%t"/>
<l:template name="equation" text="%t"/>
<l:template name="example" text="%t"/>
<l:template name="figure" text="%t"/>
<l:template name="foil" text="%t"/>
<l:template name="foilgroup" text="%t"/>
<l:template name="formalpara" text="%t"/>
<l:template name="glossary" text="%t"/>
<l:template name="glossdiv" text="%t"/>
<l:template name="important" text="%t"/>
<l:template name="index" text="%t"/>
<l:template name="indexdiv" text="%t"/>
<l:template name="itemizedlist" text="%t"/>
<l:template name="legalnotice" text="%t"/>
<l:template name="listitem" text="%n"/>
<l:template name="lot" text="%t"/>
<l:template name="msg" text="%t"/>
<l:template name="msgexplan" text="%t"/>
<l:template name="msgmain" text="%t"/>
<l:template name="msgrel" text="%t"/>
<l:template name="msgset" text="%t"/>
<l:template name="msgsub" text="%t"/>
<l:template name="note" text="%t"/>
<l:template name="orderedlist" text="%t"/>
<l:template name="part" text="%t"/>
<l:template name="partintro" text="%t"/>
<l:template name="preface" text="%t"/>
<l:template name="procedure" text="%t"/>
<l:template name="productionset" text="%t"/>
<l:template name="qandadiv" text="%t"/>
<l:template name="qandaentry" text="ప్రశ్న: %n"/>
<l:template name="qandaset" text="%t"/>
<l:template name="question" text="ప్రశ్న: %n"/>
<l:template name="reference" text="%t"/>
<l:template name="refsynopsisdiv" text="%t"/>
<l:template name="segmentedlist" text="%t"/>
<l:template name="set" text="%t"/>
<l:template name="setindex" text="%t"/>
<l:template name="sidebar" text="%t"/>
<l:template name="table" text="%t"/>
<l:template name="task" text="%t"/>
<l:template name="tip" text="%t"/>
<l:template name="toc" text="%t"/>
<l:template name="variablelist" text="%t"/>
<l:template name="varlistentry" text="%n"/>
<l:template name="warning" text="%t"/>
<l:template name="olink.document.citation" text=" in %o"/>
<l:template name="olink.page.citation" text=" (page %p)"/>
<l:template name="page.citation" text=" [%p]"/>
<l:template name="page" text="(page %p)"/>
<l:template name="docname" text=" in %o"/>
<l:template name="docnamelong" text=" in the document titled %o"/>
<l:template name="pageabbrev" text="(p. %p)"/>
<l:template name="Page" text="Page %p"/>
<l:template name="bridgehead" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsection" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsect1" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsect2" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsect3" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect1" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect2" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect3" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect4" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect5" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="section" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="simplesect" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
</l:context>
<l:context name="xref-number"><l:template name="answer" text="సమాధానం: %n"/>
<l:template name="appendix" text="అనుబంధం %n"/>
<l:template name="bridgehead" text="విభాగము %n"/>
<l:template name="chapter" text="అధ్యాయము %n"/>
<l:template name="equation" text="సమీకరణము %n"/>
<l:template name="example" text="ఉదాహరణ %n"/>
<l:template name="figure" text="మూర్తి %n"/>
<l:template name="part" text="భాగము %n"/>
<l:template name="procedure" text="పద్ధతి %n"/>
<l:template name="productionset" text="ఉత్పత్తి %n"/>
<l:template name="qandadiv" text="ప్రశ్నలు &amp; సమాధానములు %n"/>
<l:template name="qandaentry" text="ప్రశ్న: %n"/>
<l:template name="question" text="ప్రశ్న: %n"/>
<l:template name="sect1" text="విభాగము %n"/>
<l:template name="sect2" text="విభాగము %n"/>
<l:template name="sect3" text="విభాగము %n"/>
<l:template name="sect4" text="విభాగము %n"/>
<l:template name="sect5" text="విభాగము %n"/>
<l:template name="section" text="విభాగము %n"/>
<l:template name="table" text="పట్టిక %n"/>
</l:context>
<l:context name="xref-number-and-title"><l:template name="appendix" text="అనుబంధం %n, %t"/>
<l:template name="bridgehead" text="విభాగము %n, “%t”"/>
<l:template name="chapter" text="అధ్యాయము %n, %t"/>
<l:template name="equation" text="సమీకరణము %n, “%t”"/>
<l:template name="example" text="ఉదాహరణ %n, “%t”"/>
<l:template name="figure" text="మూర్తి %n, “%t”"/>
<l:template name="part" text="భాగము %n, “%t”"/>
<l:template name="procedure" text="పద్ధతి %n, “%t”"/>
<l:template name="productionset" text="ఉత్పత్తి %n, “%t”"/>
<l:template name="qandadiv" text="ప్రశ్నలు &amp; సమాధానములు %n, “%t”"/>
<l:template name="refsect1" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsect2" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsect3" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="refsection" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="sect1" text="విభాగము %n, “%t”"/>
<l:template name="sect2" text="విభాగము %n, “%t”"/>
<l:template name="sect3" text="విభాగము %n, “%t”"/>
<l:template name="sect4" text="విభాగము %n, “%t”"/>
<l:template name="sect5" text="విభాగము %n, “%t”"/>
<l:template name="section" text="విభాగము %n, “%t”"/>
<l:template name="simplesect" text="ఆ విభాగము పిలువబడుతుంది “%t”"/>
<l:template name="table" text="పట్టిక %n, “%t”"/>
</l:context>
<l:context name="authorgroup"><l:template name="sep" text=", "/>
<l:template name="sep2" text=" మరియు "/>
<l:template name="seplast" text=", మరియు "/>
</l:context>
<l:context name="glossary"><l:template name="see" text="చూడుము %t."/>
<l:template name="seealso" text="ఇదికూడా చూడుము %t."/>
<l:template name="seealso-separator" text=", "/>
</l:context>
<l:context name="msgset"><l:template name="MsgAud" text="ప్రేక్షకులు: "/>
<l:template name="MsgLevel" text="స్థాయి: "/>
<l:template name="MsgOrig" text="మూలము: "/>
</l:context>
<l:context name="datetime"><l:template name="format" text="d/m/Y"/>
</l:context>
<l:context name="termdef"><l:template name="prefix" text="[Definition: "/>
<l:template name="suffix" text="]"/>
</l:context>
<l:context name="datetime-full"><l:template name="January" text="జనవరి"/>
<l:template name="February" text="ఫిబ్రవరి"/>
<l:template name="March" text="మార్చి"/>
<l:template name="April" text="ఏప్రిల్"/>
<l:template name="May" text="మే"/>
<l:template name="June" text="జూన్"/>
<l:template name="July" text="జులై"/>
<l:template name="August" text="ఆగస్టు"/>
<l:template name="September" text="సెప్టెంబర్"/>
<l:template name="October" text="అక్టోబర్"/>
<l:template name="November" text="నవంబర్"/>
<l:template name="December" text="డిసెంబర్"/>
<l:template name="Monday" text="సోమవారము"/>
<l:template name="Tuesday" text="మంగళవారము"/>
<l:template name="Wednesday" text="బుధవారము"/>
<l:template name="Thursday" text="గురువారము"/>
<l:template name="Friday" text="శుక్రవారము"/>
<l:template name="Saturday" text="శనివారము"/>
<l:template name="Sunday" text="ఆదివారము"/>
</l:context>
<l:context name="datetime-abbrev"><l:template name="Jan" text="జన"/>
<l:template name="Feb" text="ఫిబ్ర"/>
<l:template name="Mar" text="మార్చి"/>
<l:template name="Apr" text="ఏప్రి"/>
<l:template name="May" text="మే"/>
<l:template name="Jun" text="జూన్"/>
<l:template name="Jul" text="జులై"/>
<l:template name="Aug" text="ఆగ"/>
<l:template name="Sep" text="సెప్టెం"/>
<l:template name="Oct" text="అక్టో"/>
<l:template name="Nov" text="నవం"/>
<l:template name="Dec" text="డిసెం"/>
<l:template name="Mon" text="సోమ"/>
<l:template name="Tue" text="మంగళ"/>
<l:template name="Wed" text="బుధ"/>
<l:template name="Thu" text="గురు"/>
<l:template name="Fri" text="శుక్ర"/>
<l:template name="Sat" text="శని"/>
<l:template name="Sun" text="ఆది"/>
</l:context>
<l:context name="htmlhelp"><l:template name="langcode" text="0x044a Telugu"/>
</l:context>
<l:context name="index"><l:template name="term-separator" text=", "/>
<l:template name="number-separator" text=", "/>
<l:template name="range-separator" text="-"/>
</l:context>
<l:context name="iso690"><l:template name="lastfirst.sep" text=", "/>
<l:template name="alt.person.two.sep" text=" – "/>
<l:template name="alt.person.last.sep" text=" – "/>
<l:template name="alt.person.more.sep" text=" – "/>
<l:template name="primary.editor" text=" (ed.)"/>
<l:template name="primary.many" text=", et al."/>
<l:template name="primary.sep" text=". "/>
<l:template name="submaintitle.sep" text=": "/>
<l:template name="title.sep" text=". "/>
<l:template name="othertitle.sep" text=", "/>
<l:template name="medium1" text=" ["/>
<l:template name="medium2" text="]"/>
<l:template name="secondary.person.sep" text="; "/>
<l:template name="secondary.sep" text=". "/>
<l:template name="respons.sep" text=". "/>
<l:template name="edition.sep" text=". "/>
<l:template name="edition.serial.sep" text=", "/>
<l:template name="issuing.range" text="-"/>
<l:template name="issuing.div" text=", "/>
<l:template name="issuing.sep" text=". "/>
<l:template name="partnr.sep" text=". "/>
<l:template name="placepubl.sep" text=": "/>
<l:template name="publyear.sep" text=", "/>
<l:template name="pubinfo.sep" text=". "/>
<l:template name="spec.pubinfo.sep" text=", "/>
<l:template name="upd.sep" text=", "/>
<l:template name="datecit1" text=" [cited "/>
<l:template name="datecit2" text="]"/>
<l:template name="extent.sep" text=". "/>
<l:template name="locs.sep" text=", "/>
<l:template name="location.sep" text=". "/>
<l:template name="serie.sep" text=". "/>
<l:template name="notice.sep" text=". "/>
<l:template name="access" text="Available "/>
<l:template name="acctoo" text="Also available "/>
<l:template name="onwww" text="from World Wide Web"/>
<l:template name="oninet" text="from Internet"/>
<l:template name="access.end" text=": "/>
<l:template name="link1" text="&lt;"/>
<l:template name="link2" text="&gt;"/>
<l:template name="access.sep" text=". "/>
<l:template name="isbn" text="ISBN "/>
<l:template name="issn" text="ISSN "/>
<l:template name="stdnum.sep" text=". "/>
<l:template name="patcountry.sep" text=". "/>
<l:template name="pattype.sep" text=", "/>
<l:template name="patnum.sep" text=". "/>
<l:template name="patdate.sep" text=". "/>
</l:context><l:letters><l:l i="-1"/>
<l:l i="0">చిహ్నములు</l:l>
<l:l i="10">A</l:l>
<l:l i="10">a</l:l>
<l:l i="20">B</l:l>
<l:l i="20">b</l:l>
<l:l i="30">C</l:l>
<l:l i="30">c</l:l>
<l:l i="40">D</l:l>
<l:l i="40">d</l:l>
<l:l i="50">E</l:l>
<l:l i="50">e</l:l>
<l:l i="60">F</l:l>
<l:l i="60">f</l:l>
<l:l i="70">G</l:l>
<l:l i="70">g</l:l>
<l:l i="80">H</l:l>
<l:l i="80">h</l:l>
<l:l i="90">I</l:l>
<l:l i="90">i</l:l>
<l:l i="100">J</l:l>
<l:l i="100">j</l:l>
<l:l i="110">K</l:l>
<l:l i="110">k</l:l>
<l:l i="120">L</l:l>
<l:l i="120">l</l:l>
<l:l i="130">M</l:l>
<l:l i="130">m</l:l>
<l:l i="140">N</l:l>
<l:l i="140">n</l:l>
<l:l i="150">O</l:l>
<l:l i="150">o</l:l>
<l:l i="160">P</l:l>
<l:l i="160">p</l:l>
<l:l i="170">Q</l:l>
<l:l i="170">q</l:l>
<l:l i="180">R</l:l>
<l:l i="180">r</l:l>
<l:l i="190">S</l:l>
<l:l i="190">s</l:l>
<l:l i="200">T</l:l>
<l:l i="200">t</l:l>
<l:l i="210">U</l:l>
<l:l i="210">u</l:l>
<l:l i="220">V</l:l>
<l:l i="220">v</l:l>
<l:l i="230">W</l:l>
<l:l i="230">w</l:l>
<l:l i="240">X</l:l>
<l:l i="240">x</l:l>
<l:l i="250">Y</l:l>
<l:l i="250">y</l:l>
<l:l i="260">Z</l:l>
<l:l i="260">z</l:l>
</l:letters>
</l:l10n>

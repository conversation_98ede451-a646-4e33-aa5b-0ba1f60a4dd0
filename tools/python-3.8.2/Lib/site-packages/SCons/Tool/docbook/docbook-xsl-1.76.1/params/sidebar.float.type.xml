<refentry xmlns="http://docbook.org/ns/docbook"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          xmlns:xi="http://www.w3.org/2001/XInclude"
          xmlns:src="http://nwalsh.com/xmlns/litprog/fragment"
          xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
          version="5.0" xml:id="sidebar.float.type">
<refmeta>
<refentrytitle>sidebar.float.type</refentrytitle>
<refmiscinfo class="other" otherclass="datatype">list</refmiscinfo>
<refmiscinfo class="other" otherclass="value">none</refmiscinfo>
<refmiscinfo class="other" otherclass="value">before</refmiscinfo>
<refmiscinfo class="other" otherclass="value">left</refmiscinfo>
<refmiscinfo class="other" otherclass="value">start</refmiscinfo> 
<refmiscinfo class="other" otherclass="value">right</refmiscinfo>
<refmiscinfo class="other" otherclass="value">end</refmiscinfo>     
<refmiscinfo class="other" otherclass="value">inside</refmiscinfo>
<refmiscinfo class="other" otherclass="value">outside</refmiscinfo> 
</refmeta>
<refnamediv>
<refname>sidebar.float.type</refname>
<refpurpose>Select type of float for sidebar elements</refpurpose>
</refnamediv>

<refsynopsisdiv>
<src:fragment xml:id="sidebar.float.type.frag">
<xsl:param name="sidebar.float.type">none</xsl:param>
</src:fragment>
</refsynopsisdiv>

<refsection><info><title>Description</title></info>

<para>Selects the type of float for sidebar elements.
</para>
<itemizedlist>
<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>none</literal></quote>, then 
no float is used.
</para>
</listitem>
<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>before</literal></quote>, then 
the float appears at the top of the page.  On some processors,
that may be the next page rather than the current page.
</para>
</listitem>

<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>left</literal></quote>,
then a left side float is used.
</para>
</listitem>

<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>start</literal></quote>,
then when the text direction is left-to-right a left side float is used.
When the text direction is right-to-left, a right side float is used.
</para>
</listitem>

<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>right</literal></quote>,
then a right side float is used.
</para>
</listitem>

<listitem>
<para>If <parameter>sidebar.float.type</parameter> is
<quote><literal>end</literal></quote>,
then when the text direction is left-to-right a right side float is used.
When the text direction is right-to-left, a left side float is used.
</para>
</listitem>

<listitem>
<para>If your XSL-FO processor supports floats positioned on the
<quote><literal>inside</literal></quote> or
<quote><literal>outside</literal></quote>
of double-sided pages, then you have those two 
options for side floats as well.
</para>
</listitem>
</itemizedlist>

</refsection>
</refentry>

# 游戏宝箱玩法 - 对局结算模块技术设计文档

## 📋 **模块概述**

对局结算模块是宝箱玩法的核心模块，负责处理对局结束后的所有结算逻辑，包括玩家存活状态判断、奖励发放、道具过滤和价值计算等功能。

---

## 🏆 **1. chestBattleSettlement核心结算流程**

### **1.1 结算入口逻辑**

```java
public void chestBattleSettlement(LetsGoBattleDetailData battleDetailData, GlobalBattleResult globalBattleResult,
        CsLetsgo.LetsGoBattleSettlementNtf.Builder builder) {
    // 玩法类型检查
    if (MatchTypeRootData.getInstance().getGameRootTypeByMatchTypeId(globalBattleResult.getMatchType())
            != GameRootType.GRT_Chest) {
        return;
    }
    
    int chestActorType = globalBattleResult.getChaseActorType();
    ChestBattleDetailData chestBattleDetailData = battleDetailData.getChestBattleDetailData();
    
    // 获取对局信息
    ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo()
            .getChestBattleInfo(globalBattleResult.getBattleId());
    if (chestBattleInfo == null) {
        LOGGER.error("chestBattleSettlement battleInfo is null, uid:{} battleId:{}", 
                player.getUid(), globalBattleResult.getBattleId());
        return;
    }
    
    // 核心结算逻辑
    processPlayerSettlement(battleDetailData, globalBattleResult, chestBattleDetailData, 
            chestActorType, chestBattleInfo, builder);
}
```

### **1.2 结算流程概览**

```mermaid
flowchart TD
    A[结算开始] --> B{检查玩法类型}
    B -->|非宝箱玩法| C[跳过结算]
    B -->|宝箱玩法| D[获取对局信息]
    D --> E{对局信息存在?}
    E -->|否| F[记录错误并退出]
    E -->|是| G[判断玩家存活状态]
    G -->|被淘汰| H[清空备战道具]
    G -->|存活| I[处理奖励发放]
    H --> J[清理对局数据]
    I --> K[发放安全屋道具]
    K --> J
    J --> L[结算完成]
```

---

## ⚖️ **2. 玩家存活状态判断逻辑**

### **2.1 状态判断依据**

```java
private void processPlayerSettlement(LetsGoBattleDetailData battleDetailData, 
        GlobalBattleResult globalBattleResult, ChestBattleDetailData chestBattleDetailData, 
        int chestActorType, ChestBattleInfo chestBattleInfo, 
        CsLetsgo.LetsGoBattleSettlementNtf.Builder builder) {
    
    // 基于对局结果判断存活状态
    boolean isPlayerEliminated = (globalBattleResult.getBattleResult() == 
            BattleResultCode.BATTLE_RESULT_CODE_FAIL.getNumber());
    
    if (isPlayerEliminated) {
        // 被淘汰玩家处理
        handleEliminatedPlayer(battleDetailData.getBattleId(), chestActorType);
    } else {
        // 存活玩家处理
        handleSurvivedPlayer(battleDetailData, chestBattleDetailData, chestActorType, chestBattleInfo, builder);
    }
}
```

### **2.2 状态判断标准**

| 对局结果 | 状态判断 | 处理逻辑 |
|---------|---------|---------|
| BATTLE_RESULT_CODE_SUCCESS | 存活 | 发放奖励和携带道具 |
| BATTLE_RESULT_CODE_FAIL | 被淘汰 | 清空备战道具 |
| BATTLE_RESULT_CODE_DRAW | 平局 | 按存活处理 |

---

## 💀 **3. 被淘汰玩家处理**

### **3.1 备战道具清空机制**

```java
public void clearEquipBagItems(long battleId, int chestActorType) {
    if (battleId == 0) {
        return;
    }
    
    int beginGridId = 0;
    int endGridId = 0;
    if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
        beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
        endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
    } else {
        beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
        endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
    }
    
    // 清空指定范围的道具
    for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
        if (gridId2ItemUUID.containsKey(gridId)) {
            long itemUUID = gridId2ItemUUID.get(gridId);
            removeItem(itemUUID);
            gridId2ItemUUID.remove(gridId);
        }
    }
    
    LOGGER.info("clearEquipBagItems success, uid:{} battleId:{} chestActorType:{}", 
            player.getUid(), battleId, chestActorType);
}
```

### **3.2 清空策略说明**

- **范围精确**：只清空对应角色类型的备战道具
- **数据一致性**：同时清理内存和持久化数据
- **日志记录**：记录清空操作便于审计

---

## 🎁 **4. 存活玩家奖励发放**

### **4.1 奖励发放流程**

```java
private void handleSurvivedPlayer(LetsGoBattleDetailData battleDetailData, 
        ChestBattleDetailData chestBattleDetailData, int chestActorType, 
        ChestBattleInfo chestBattleInfo, CsLetsgo.LetsGoBattleSettlementNtf.Builder builder) {
    
    // 1. 更新道具价值和排行榜
    addCarryOutItemsValue(battleDetailData, chestActorType, chestBattleDetailData.getPersonalItemsValue());
    
    // 2. 发放各类奖励
    sendSettlementRewards(chestBattleDetailData);
    
    // 3. 发放安全屋道具
    sendSafeHouseItems(chestBattleInfo, builder);
}
```

### **4.2 奖励类型分类**

| 奖励类型 | 数据来源 | 处理逻辑 |
|---------|---------|---------|
| 携带道具 | equipBagItems | 过滤藏品类型后发放 |
| 基础奖励 | baseRewards | 直接发放 |
| 宝箱奖励 | darkStarRewardChestList | 按宝箱展开发放 |
| 随机奖励 | darkStarRandomRewardList | 直接发放 |
| 安全屋道具 | safeHouseItems | 从对局信息获取发放 |

---

## 🔍 **5. 藏品类型过滤机制**

### **5.1 过滤逻辑实现**

```java
private void sendSettlementRewards(ChestBattleDetailData chestBattleDetailData) {
    ChangedItems changedItems = new ChangedItems(ItemChangeReason.ICR_ChestSettlementRewards.getNumber(), "");
    
    // 过滤携带道具 - 仅发放藏品类型
    for (ItemInfo itemInfo : chestBattleDetailData.getEquipBagItemsList()) {
        if (isChestTreasureItem(itemInfo)) {
            changedItems.mergeItemInfo(itemInfo);
        }
    }
    
    // 处理其他奖励类型
    processOtherRewards(chestBattleDetailData, changedItems);
    
    // 批量发放
    NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(changedItems);
    if (!ret.getKey().isOk()) {
        LOGGER.error("sendSettlementRewards failed, uid:{} err:{}", player.getUid(), ret.getKey());
    }
}

private boolean isChestTreasureItem(ItemInfo itemInfo) {
    Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
    return itemConf != null && itemConf.getType() == ItemType.ItemType_Chest_Treasure;
}
```

### **5.2 过滤规则说明**

- **藏品类型**：只有`ItemType_Chest_Treasure`类型的道具才能被带出
- **配置驱动**：过滤规则基于道具配置表，便于调整
- **性能优化**：在发放前过滤，减少无效操作

---

## 💰 **6. 道具价值计算逻辑**

### **6.1 价值计算公式**

```java
private long calculateItemValue(ItemInfo itemInfo) {
    Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
    if (itemConf == null) {
        return 0L;
    }
    
    // 基础价值 * 数量 * 品质系数
    long baseValue = itemConf.getBaseValue();
    int quantity = itemInfo.getNumber();
    float qualityMultiplier = getQualityMultiplier(itemConf.getQuality());
    
    return Math.round(baseValue * quantity * qualityMultiplier);
}

private float getQualityMultiplier(int quality) {
    switch (quality) {
        case 1: return 1.0f;    // 普通
        case 2: return 1.5f;    // 稀有
        case 3: return 2.0f;    // 史诗
        case 4: return 3.0f;    // 传说
        default: return 1.0f;
    }
}
```

### **6.2 价值统计维度**

- **个人价值**：玩家个人携带道具的总价值
- **阵营价值**：整个阵营所有玩家的道具总价值
- **历史记录**：保存价值记录用于排行榜计算

---

## 📦 **7. 批量奖励发放策略**

### **7.1 批量处理优势**

```java
private void processAllRewards(ChestBattleDetailData chestBattleDetailData) {
    ChangedItems allRewards = new ChangedItems(ItemChangeReason.ICR_ChestSettlementRewards.getNumber(), "");
    
    // 合并所有类型的奖励
    mergeEquipBagItems(chestBattleDetailData.getEquipBagItemsList(), allRewards);
    mergeBaseRewards(chestBattleDetailData.getBaseRewardsList(), allRewards);
    mergeChestRewards(chestBattleDetailData.getDarkStarRewardChestListList(), allRewards);
    mergeRandomRewards(chestBattleDetailData.getDarkStarRandomRewardListList(), allRewards);
    
    // 一次性发放所有奖励
    NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(allRewards);
    
    if (ret.getKey().isOk()) {
        LOGGER.info("All rewards granted successfully, uid:{} totalItems:{}", 
                player.getUid(), allRewards.getItemInfos().size());
    } else {
        LOGGER.error("Failed to grant rewards, uid:{} error:{}", player.getUid(), ret.getKey());
    }
}
```

### **7.2 批量处理特点**

- **原子性**：所有奖励要么全部成功，要么全部失败
- **性能优化**：减少数据库事务次数
- **日志统一**：统一的变更记录便于追踪
- **错误处理**：统一的错误处理和回滚机制

---

## 🔧 **技术要点总结**

### **设计优势**
1. **状态驱动**：基于对局结果进行差异化处理
2. **数据安全**：被淘汰玩家的道具清空机制
3. **过滤精确**：只发放符合规则的道具
4. **批量优化**：减少数据库操作提升性能

### **关键配置**
- **BattleResultCode**：对局结果枚举
- **ItemType.ItemType_Chest_Treasure**：藏品类型定义
- **ItemChangeReason.ICR_ChestSettlementRewards**：结算奖励变更原因

### **性能考虑**
- **批量发放**：减少数据库事务
- **精确过滤**：避免无效道具处理
- **内存管理**：及时清理临时数据

### **监控指标**
- **结算成功率**：监控结算流程稳定性
- **奖励发放成功率**：监控奖励系统健康度
- **道具过滤比例**：监控游戏平衡性
- **价值计算准确性**：监控排行榜数据质量

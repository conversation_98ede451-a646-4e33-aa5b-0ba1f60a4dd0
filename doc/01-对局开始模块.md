# 游戏宝箱玩法 - 对局开始模块技术设计文档

## 📋 **模块概述**

对局开始模块负责处理玩家匹配成功后的初始化流程，包括事件监听、角色类型区分、入场券消耗、对局信息初始化和备战道具备份等核心功能。

---

## 🚀 **1. OnPlayerStartBattle事件处理机制**

### **1.1 事件监听器配置**

```java
private class OnPlayerStartBattle implements EventConsumer {
    @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerStartBattle)
    private NKErrorCode onEvent(PlayerStartBattleEvent event) throws NKRuntimeException {
        LOGGER.debug("onEvent PlayerStartBattleEvent, uid:{} battleId:{} matchType:{}",
                event.getPlayer().getUid(), event.getBattleInfo().getBattleid(), event.getGameMode());
        onMatchSuccess(event.getGameMode(), event.getBattleInfo().getBattleid());
        return NKErrorCode.OK;
    }

    @Override
    public boolean isDestroyed() {
        return false;
    }
}
```

### **1.2 技术实现细节**

- **事件路由**：使用`EventRouterType.ERT_ConsumerPlayerStartBattle`路由
- **异步处理**：事件处理不阻塞主流程
- **错误处理**：返回`NKErrorCode`进行统一错误管理
- **生命周期管理**：通过`isDestroyed()`方法管理监听器生命周期

### **1.3 事件注册机制**

```java
public ChestBattleMgr(Player player, ChestGameInfo chestAttrData) {
    this.player = player;
    this.chestAttrData = chestAttrData;
    // 注册事件监听器
    player.getEventSwitch().register(new OnPlayerStartBattle());
}
```

---

## 🎭 **2. 角色类型区分逻辑**

### **2.1 角色类型枚举定义**

```protobuf
// ChaseSideType枚举
enum ChaseSideType {
    CST_XINGBAO = 1;    // 星宝玩家
    CST_DARKSTAR = 2;   // 暗星玩家
}
```

### **2.2 角色类型判断逻辑**

```java
private void onMatchSuccess(int matchType, long battleId) {
    int chestActorType = chestAttrData.getCurrentRole().getActorType();
    
    // 仅暗星玩家需要消耗入场券
    if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
        costEnterGameCost(matchType);
    }
    
    // 初始化对局信息并备份道具
    ChestBattleInfo chestBattleInfo = new ChestBattleInfo()
            .setBattleId(battleId)
            .setBattleCreateTime(DateUtils.currentTimeSec());
    
    player.getChestItemManager().backupEquipBagItems(battleId, chestActorType, chestBattleInfo);
}
```

### **2.3 角色差异化处理**

| 角色类型 | 入场券消耗 | 备份道具范围 | 特殊逻辑 |
|---------|-----------|-------------|---------|
| 星宝玩家 | 无需消耗 | 星宝专用格子 | 收集道具为主 |
| 暗星玩家 | 需要消耗 | 暗星专用格子 | 追捕星宝为主 |

---

## 💰 **3. 入场券消耗机制**

### **3.1 配置结构定义**

```protobuf
// ChestMapSelectData配置
message ChestMapSelectData {
    optional int32 matchTypeId = 1;
    optional EntryCostInfo entryCostInfo = 2;
}

message EntryCostInfo {
    repeated ItemInfo bossActorTypeEntryCostItems = 1; // 暗星入场券消耗
}
```

### **3.2 入场券消耗实现**

```java
private void costEnterGameCost(int matchTypeId) {
    int chestActorType = chestAttrData.getCurrentRole().getActorType();
    if (chestActorType != ChaseSideType.CST_DARKSTAR.getNumber()) {
        return; // 仅暗星玩家消耗入场券
    }
    
    Optional<ChestMapSelectData> mapCfg = ChestMapSelectConfig.getInstance()
            .getByMatchTypeId(matchTypeId);
    if (mapCfg.isEmpty()) {
        LOGGER.error("uid {} costEnterGameCost matchTypeId:{} not found",
                player.getUid(), matchTypeId);
        return;
    }
    
    ChangedItems costInfo = new ChangedItems(ItemChangeReason.ICR_ChestEnterGameCost.getNumber(), "");
    costInfo.mergeItemInfoMulti(mapCfg.get().getEntryCostInfo().getBossActorTypeEntryCostItemsList(), 1);
    
    NKErrorCode ret = player.getBagManager().MinItems(costInfo, matchTypeId);
    if (!ret.isOk()) {
        LOGGER.error("uid {} costEnterGameCost matchTypeId:{} cost failed:{}",
                player.getUid(), matchTypeId, ret);
    }
}
```

### **3.3 错误处理策略**

- **配置缺失**：记录错误日志，允许玩家继续游戏
- **道具不足**：返回错误码，阻止玩家进入对局
- **系统异常**：记录详细日志，便于问题排查

---

## 🗂️ **4. ChestBattleInfo初始化流程**

### **4.1 数据结构定义**

```protobuf
// attr_ChestBattleInfo.proto
message proto_ChestBattleInfo {
    optional int64 battleId = 1;
    optional int64 battleCreateTime = 2;
    repeated proto_ChestItem safeHouseItems = 3;           // 安全屋道具
    repeated proto_ChestItem chestEquipItemsBackup = 4;    // 备战道具备份
}
```

### **4.2 初始化逻辑**

```java
// 初始化对局信息
ChestBattleInfo chestBattleInfo = new ChestBattleInfo()
        .setBattleId(battleId)
        .setBattleCreateTime(DateUtils.currentTimeSec());
player.getUserAttr().getChestGameInfo().putChestBattleInfo(battleId, chestBattleInfo);
```

### **4.3 数据管理策略**

- **对局ID绑定**：每个对局创建唯一的ChestBattleInfo实例
- **时间戳记录**：记录创建时间用于过期清理
- **内存管理**：存储在玩家属性中，支持快速访问
- **生命周期**：对局结束后自动清理

---

## 🎒 **5. 备战道具备份机制**

### **5.1 格子ID范围定义**

```java
private static final int XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN = 
        ChaseSideType.CST_XINGBAO.getNumber() * 10000;      // 星宝装备格子ID开始值
private static final int DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN = 
        ChaseSideType.CST_DARKSTAR.getNumber() * 10000;     // 暗星装备格子ID开始值
```

### **5.2 备份实现逻辑**

```java
public void backupEquipBagItems(long battleId, int chestActorType, ChestBattleInfo chestBattleInfo) {
    if (battleId == 0) {
        return;
    }
    
    int beginGridId = 0;
    int endGridId = 0;
    if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
        beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
        endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
    } else {
        beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
        endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
    }
    
    chestBattleInfo.clearChestEquipItemsBackup();
    for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
        if (!gridId2ItemUUID.containsKey(gridId)) {
            continue;
        }
        long itemUUID = gridId2ItemUUID.get(gridId);
        if (itemUUID == 0) {
            continue;
        }
        ChestItem item = retrieveItem(itemUUID);
        if (item != null) {
            chestBattleInfo.putChestEquipItemsBackup(item.getId(), item);
        }
    }
    
    LOGGER.info("backupEquipBagItems success, uid:{} battleId:{} chestActorType:{}",
            player.getUid(), battleId, chestActorType);
}
```

### **5.3 性能考虑点**

- **范围限制**：只备份指定格子范围的道具，避免全量扫描
- **内存优化**：使用引用而非深拷贝，减少内存占用
- **并发安全**：使用ConcurrentHashMap确保线程安全
- **异常处理**：备份失败不影响对局开始

---

## 🔧 **技术要点总结**

### **设计优势**
1. **事件驱动架构**：松耦合的事件处理机制
2. **角色差异化**：根据角色类型执行不同逻辑
3. **数据安全性**：完整的备份和恢复机制
4. **性能优化**：精确的范围控制和内存管理

### **关键配置**
- **ChestMapSelectConfig**：地图和入场券配置
- **ChestBagMiscConf**：背包容量配置
- **ChaseSideType**：角色类型枚举

### **监控指标**
- **事件处理耗时**：监控初始化流程性能
- **入场券消耗成功率**：监控支付系统稳定性
- **备份成功率**：监控数据安全性

### **错误处理**
- **配置缺失**：优雅降级，记录日志
- **道具不足**：阻止进入，提示玩家
- **系统异常**：详细日志，便于排查

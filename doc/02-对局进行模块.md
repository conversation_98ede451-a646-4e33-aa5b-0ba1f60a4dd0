# 游戏宝箱玩法 - 对局进行模块技术设计文档

## 📋 **模块概述**

对局进行模块负责处理游戏过程中玩家与安全屋的交互，包括道具保存、数据验证、即时发放和跨服务器通信等核心功能。

---

## 🏠 **1. 安全屋道具保存机制**

### **1.1 IRPC接口定义**

```protobuf
// ds_player.proto
message ChestPlayerSaveItemsRequest {
    optional int64 battleId = 1;
    repeated ItemInfo itemInfos = 2;
}

message ChestPlayerSaveItemsResponse {
    optional int32 result = 1;  // NKErrorCode
}
```

### **1.2 保存流程实现**

```java
public NKErrorCode irpcChestSaveItemsToSaveHouse(DsPlayer.ChestPlayerSaveItemsRequest req) {
    ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo().getChestBattleInfo(req.getBattleId());
    if (chestBattleInfo == null) {
        LOGGER.error("irpcChestSaveItemsToSaveHouse battleInfo is null, uid:{} battleId:{}",
                player.getUid(), req.getBattleId());
        return NKErrorCode.BattleNotExist;
    }
    
    ChangedItems safeHouseItems = new ChangedItems(ItemChangeReason.ICR_ChestSafeHouseRewards.getNumber(), "");
    
    for (ItemInfo itemInfo : req.getItemInfosList()) {
        // 数据验证流程
        if (!validateItemInfo(itemInfo, chestBattleInfo)) {
            continue;
        }
        
        // 保存到安全屋
        chestBattleInfo.putSafeHouseItems(itemInfo.getUuid(), 
            ChestItemManager.convItemInfoToChestItem(itemInfo));
        safeHouseItems.mergeItemInfo(itemInfo);
    }
    
    // 即时发放到背包
    NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(safeHouseItems);
    if (!ret.getKey().isOk()) {
        LOGGER.error("irpcChestSaveItemsToSaveHouse AddItems2 failed, uid:{} battleId:{} err:{} items:{}",
                player.getUid(), req.getBattleId(), ret.getKey(), safeHouseItems.toString());
        return ret.getKey();
    }
    
    return NKErrorCode.OK;
}
```

### **1.3 安全屋机制特点**

- **即时生效**：道具保存后立即发放到玩家背包
- **风险规避**：避免对局中途退出导致的道具丢失
- **策略选择**：玩家可选择保存珍贵道具或继续冒险

---

## ✅ **2. 数据验证流程**

### **2.1 三层验证机制**

```java
private boolean validateItemInfo(ItemInfo itemInfo, ChestBattleInfo chestBattleInfo) {
    // 第一层：UUID有效性验证
    if (itemInfo.getUuid() == 0) {
        LOGGER.debug("irpcChestSaveItemsToSaveHouse itemInfo.getUuid() == 0, uid:{} battleId:{} itemInfo:{}",
                player.getUid(), chestBattleInfo.getBattleId(), itemInfo);
        return false;
    }
    
    // 第二层：重复保存检查
    if (chestBattleInfo.getSafeHouseItems().containsKey(itemInfo.getUuid())) {
        LOGGER.debug("irpcChestSaveItemsToSaveHouse itemInfo already exist, uid:{} battleId:{} itemInfo:{}",
                player.getUid(), chestBattleInfo.getBattleId(), itemInfo);
        return false;
    }
    
    // 第三层：道具配置验证
    Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
    if (itemConf == null) {
        LOGGER.warn("item config not found, itemId:{}", itemInfo.getItemId());
        return false;
    }
    
    return true;
}
```

### **2.2 验证层级说明**

| 验证层级 | 验证内容 | 失败处理 | 日志级别 |
|---------|---------|---------|---------|
| UUID验证 | 检查UUID是否为0 | 跳过该道具 | DEBUG |
| 重复检查 | 检查是否已保存 | 跳过该道具 | DEBUG |
| 配置验证 | 检查道具配置存在性 | 跳过该道具 | WARN |

### **2.3 数据完整性保障**

```java
// 道具转换验证
ChestItem convertedItem = ChestItemManager.convItemInfoToChestItem(itemInfo);
if (convertedItem == null) {
    LOGGER.error("Failed to convert ItemInfo to ChestItem, itemInfo:{}", itemInfo);
    return false;
}

// 数量合理性检查
if (itemInfo.getNumber() <= 0 || itemInfo.getNumber() > MAX_ITEM_COUNT) {
    LOGGER.warn("Invalid item count, itemId:{} count:{}", itemInfo.getItemId(), itemInfo.getNumber());
    return false;
}
```

---

## 🎁 **3. 即时发放逻辑和错误处理**

### **3.1 即时发放策略**

```java
// 批量发放机制
ChangedItems safeHouseItems = new ChangedItems(ItemChangeReason.ICR_ChestSafeHouseRewards.getNumber(), "");
safeHouseItems.getTlogData().setChangeSubReason(chestBattleInfo.getBattleId());

// 合并所有有效道具
for (ItemInfo itemInfo : validItems) {
    safeHouseItems.mergeItemInfo(itemInfo);
}

// 一次性发放所有道具
NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(safeHouseItems);
```

### **3.2 错误处理策略**

```java
if (!ret.getKey().isOk()) {
    LOGGER.error("irpcChestSaveItemsToSaveHouse AddItems2 failed, uid:{} battleId:{} err:{} items:{}",
            player.getUid(), req.getBattleId(), ret.getKey(), safeHouseItems.toString());
    
    // 回滚已保存的道具
    for (ItemInfo itemInfo : req.getItemInfosList()) {
        if (chestBattleInfo.getSafeHouseItems().containsKey(itemInfo.getUuid())) {
            chestBattleInfo.removeSafeHouseItems(itemInfo.getUuid());
        }
    }
    
    return ret.getKey();
}
```

### **3.3 事务性保证**

- **原子操作**：保存和发放在同一事务中
- **回滚机制**：发放失败时回滚保存操作
- **状态一致性**：确保内存状态与数据库状态一致

---

## 🌐 **4. 跨服务器通信协议**

### **4.1 通信时序图**

```
DedicateServer -> GameSvr: irpcChestSaveItemsToSaveHouse(ItemInfo[])
GameSvr -> GameSvr: 验证道具数据
GameSvr -> GameSvr: 保存到安全屋
GameSvr -> GameSvr: 即时发放到背包
GameSvr -> Database: 更新玩家背包数据
GameSvr -> DedicateServer: 返回处理结果(NKErrorCode)
DedicateServer -> Client: 通知客户端结果
```

### **4.2 协议设计要点**

```java
// 请求参数验证
if (req.getBattleId() == 0) {
    LOGGER.error("Invalid battleId in request, uid:{}", player.getUid());
    return NKErrorCode.InvalidParam;
}

if (req.getItemInfosList().isEmpty()) {
    LOGGER.debug("Empty item list in request, uid:{} battleId:{}", 
            player.getUid(), req.getBattleId());
    return NKErrorCode.OK; // 空列表视为成功
}
```

### **4.3 性能优化**

- **批量处理**：一次请求处理多个道具
- **异步响应**：不阻塞游戏主循环
- **连接复用**：使用连接池减少网络开销

---

## 📊 **5. 监控和统计**

### **5.1 关键指标监控**

```java
// 性能指标
private static final Timer SAVE_ITEMS_TIMER = Metrics.timer("chest.save_items.duration");
private static final Counter SAVE_ITEMS_SUCCESS = Metrics.counter("chest.save_items.success");
private static final Counter SAVE_ITEMS_FAILURE = Metrics.counter("chest.save_items.failure");

public NKErrorCode irpcChestSaveItemsToSaveHouse(DsPlayer.ChestPlayerSaveItemsRequest req) {
    Timer.Sample sample = SAVE_ITEMS_TIMER.start();
    try {
        // 处理逻辑...
        SAVE_ITEMS_SUCCESS.increment();
        return NKErrorCode.OK;
    } catch (Exception e) {
        SAVE_ITEMS_FAILURE.increment();
        throw e;
    } finally {
        sample.stop();
    }
}
```

### **5.2 业务统计**

| 统计项 | 描述 | 用途 |
|-------|------|------|
| 保存成功率 | 成功保存的道具比例 | 系统稳定性监控 |
| 平均保存数量 | 每次保存的道具数量 | 玩家行为分析 |
| 保存频率 | 玩家保存道具的频率 | 游戏平衡性调整 |
| 道具类型分布 | 不同类型道具的保存比例 | 道具价值评估 |

---

## 🔧 **技术要点总结**

### **设计优势**
1. **即时反馈**：玩家操作后立即获得反馈
2. **数据安全**：多层验证确保数据有效性
3. **事务保证**：原子操作确保数据一致性
4. **性能优化**：批量处理减少网络开销

### **关键配置**
- **ItemChangeReason.ICR_ChestSafeHouseRewards**：安全屋奖励变更原因
- **MAX_ITEM_COUNT**：单个道具最大数量限制
- **IRPC超时配置**：跨服务调用超时时间

### **错误处理策略**
- **优雅降级**：部分失败不影响整体流程
- **详细日志**：便于问题排查和数据追踪
- **自动恢复**：支持重试和补偿机制

### **安全考虑**
- **数据校验**：防止非法数据注入
- **权限检查**：确保玩家只能操作自己的道具
- **频率限制**：防止恶意刷取接口

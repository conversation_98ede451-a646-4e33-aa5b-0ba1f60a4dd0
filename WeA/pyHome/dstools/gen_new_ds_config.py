#!/usr/bin/python
# -*- coding: UTF-8 -*-

import os,sys,getopt
import os.path
import sys
import time
from glob import glob
import codecs
import yaml

SCRIPT_PATH = os.path.abspath(os.path.split(os.path.realpath(__file__))[0])

sys.path.append(os.path.abspath(os.path.join(SCRIPT_PATH, "..", "..")))
from pyHome import WeAPath
sys.path.append(WeAPath.TOOL_PY_LIB_HOME)

from mako.template import Template

DS_CONFIG_INFO_PBIN_PATH = "./common/dscfg/DSAConfig.pbin"
DS_DEV_INFO_PBIN_PATH = "./common/dscfg/DSDevConfig.pbin"
DS_REGION_CONFIG_INFO_PBIN_PATH = "./common/dscfg/DSRegionConfig.pbin"
DS_PIPELINE_COMPAT_INFO_PATH = "./common/dscfg/pipeline_compat_group.txt"
DS_YAML_PATH = "./common/dscfg/ds.yaml"
NODEDS_PKG_ROOT_PATH = "../nodeds/"
DS_PKG_ROOT_PATH = "../ds/"
DS_FLEET_DEFAULT_WEIGHT = "100"

class AliasFleetInfo:
    def __init__(self, fleet_name, fleet_weight):
        self.fleet_name = fleet_name
        self.fleet_weight = fleet_weight

class AliasInfo:
    def __init__(self, name, fleetWeightArray):
        self.name = name
        self.fleet_weight_array = fleetWeightArray

class BuildInfo:
    def __init__(self, name, infixPath):
        self.name = name
        self.build_infix_path = infixPath

class FleetInfo:
    def __init__(self, name, customarg, mapName, buildName, dsType, dsBkId, startupMapName, featurePakVersion, enableDsSeed, isMultiThread, defaultEnableDsSeed):
        self.name = name
        self.customarg = customarg
        self.map_name = mapName
        self.build_name = buildName
        self.ds_type = dsType
        self.ds_bk_id = dsBkId
        self.startup_map_name = startupMapName
        self.feature_pak_version = featurePakVersion
        self.enable_ds_seed = enableDsSeed
        self.postforkthreading_param = ""
        if isMultiThread == 1:
            # 不管种子进程是否配置为单线程
            self.postforkthreading_param = "-PostForkThreading"
        elif isMultiThread == 2:
            self.postforkthreading_param = "-PostForkThreading -PostForkEngineThreading"
        else:
            if defaultEnableDsSeed == "false":
                # common参数没有配置为单线程
                self.postforkthreading_param = "-nothreading"

class DsCompatFleetInfo:
    def __init__(self, fleet_name, fleet_weight, fleet_ds_bk_id):
        self.fleet_name = fleet_name
        self.fleet_weight = fleet_weight
        self.fleet_ds_bk_id = fleet_ds_bk_id

class FeaturePkgDsInfo:
    def __init__(self, feature_id, feature_pkg_name, feature_pkg_version, ds_bk_id):
        # feature_id参见game-play.json
        self.feature_id = feature_id
        self.feature_pkg_name = feature_pkg_name
        self.feature_pkg_version = feature_pkg_version
        self.ds_bk_id = ds_bk_id

class DsCompatGroupInfo:
    def __init__(self, name, dsBkId, fleet_name, fleet_weight):
        self.name = name
        self.ds_bk_id = dsBkId
        self.compat_fleet_array = []
        compat_fleet_info = DsCompatFleetInfo(fleet_name, fleet_weight, dsBkId)
        self.compat_fleet_array.append(compat_fleet_info)
        self.feature_pkg_ds_array = []

    def addCompatFleet(self, fleet_name, fleet_weight, fleet_ds_bk_id):
        compat_fleet_info = DsCompatFleetInfo(fleet_name, fleet_weight, fleet_ds_bk_id)
        self.compat_fleet_array.append(compat_fleet_info)

    def addDsFeaturePkgFleet(self, feature_id, feature_pkg_name, feature_pkg_version, ds_bk_id):
        feature_pkg_ds_info = FeaturePkgDsInfo(feature_id, feature_pkg_name, feature_pkg_version, ds_bk_id)
        self.feature_pkg_ds_array.append(feature_pkg_ds_info)


class GenDsCompatGroupClass:
    def __init__(self, run_path):
        self.compat_group_array = []
        self.run_path = run_path

    def getDsCompatGroupB(self, compat_alias_name):
        for compat_group_obj in self.compat_group_array:
            if compat_group_obj.name == compat_alias_name:
                return compat_group_obj
        return None

    def getFeatureIdByFeatureVersion(self, feature_version):
        split_array = feature_version.strip().split('.')
        if len(split_array) > 0:
            return split_array[0]
        return None

    # 与G6DsManager.java中的实现要对应
    def  getFeatureCompatIdByFeatureVersion(self, feature_version):
        split_array = feature_version.strip().split('.')
        last_two_numbers = split_array[-2:]
        feature_compat_id = int(last_two_numbers[0]) * 10000 + int(last_two_numbers[1]) + 500
        return str(feature_compat_id)

    # pipeline_compat_group.txt格式:
    # {ds_base_compat_id},{namespace}_{ds_base_version},{ds_feature_pkg_version};{ds_feature_pkg_version}...,{compat_fleet_name},{compat_fleet_weight}
    # {ds底包兼容id},{命名空间}_{ds底包版本号},{ds副玩法包版本号};{ds副玩法包版本号}...,{灰度fleet名},{灰度fleet权重}
    # compatRCount 只读取倒数若干行
    def load_compat_data(self, namespace, comaptRCount, isDsc):
        compat_data_path = os.path.join(self.run_path, DS_PIPELINE_COMPAT_INFO_PATH)
        if os.path.exists(compat_data_path):
            with open(compat_data_path, 'r') as f:
                lines = f.readlines()
                last_lines = lines[-int(comaptRCount):]
                for line in last_lines:
                    print line
                    rows = line.strip().split(',')
                    if len(rows) != 2 and len(rows) != 3 and len(rows) != 5:
                        raise RuntimeError("pipeline_compat_group format error")
                    if len(rows) > 1:
                        print rows
                        print rows[0]
                        compat_alias_name = rows[0]
                        compat_fleet_name = rows[0]
                        compat_fleet_weight = DS_FLEET_DEFAULT_WEIGHT
                        feature_pak_version_list = []
                        if len(rows) > 2:
                            feature_pak_version_list = rows[2].strip().split(';')
                        if len(rows) > 4:
                            compat_fleet_name = rows[3]
                            compat_fleet_weight = rows[4]
                        pkg_tar_files = glob(os.path.join(NODEDS_PKG_ROOT_PATH + "{}/{}/ds".format(namespace, compat_fleet_name), '*.tar.gz'))
                        if not pkg_tar_files:
                            pkg_tar_files = glob(os.path.join(DS_PKG_ROOT_PATH + "{}/{}/ds".format(namespace, compat_fleet_name), '*.tar.gz'))
                        if isDsc or pkg_tar_files:
                            compat_group_info = self.getDsCompatGroupB(compat_alias_name)
                            if compat_group_info is None:
                                compat_group_info = DsCompatGroupInfo(compat_alias_name, rows[1], compat_fleet_name, compat_fleet_weight)
                                self.compat_group_array.append(compat_group_info)
                            else:
                                compat_group_info.addCompatFleet(compat_fleet_name, compat_fleet_weight, rows[1])
                            for feature_version in feature_pak_version_list:
                                feature_tar_files = glob(os.path.join(NODEDS_PKG_ROOT_PATH + "{}/{}/feature".format(namespace, compat_fleet_name), "*{}*.tar.gz".format(feature_version)))
                                if not feature_tar_files:
                                    feature_tar_files = glob(os.path.join(DS_PKG_ROOT_PATH + "{}/{}/feature".format(namespace, compat_fleet_name), "*{}*.tar.gz".format(feature_version)))
                                if isDsc or feature_tar_files:
                                    feature_id = self.getFeatureIdByFeatureVersion(feature_version)
                                    feature_pkg_name = self.getFeatureCompatIdByFeatureVersion(feature_version)
                                    compat_group_info.addDsFeaturePkgFleet(feature_id, feature_pkg_name, feature_version, rows[1])
                                else:
                                    print "not found {}/{} {} feature pkg".format(namespace, compat_fleet_name, feature_version)
                        else:
                            print "not found {}/{} ds pkg".format(namespace, compat_fleet_name)


class GenDsaConfigClass:
    def __init__(self, run_path):
        self.fleet_name_array = []
        self.alias_name_array = []
        self.alias_array = []
        self.build_name_array = []
        self.build_array = []
        self.fleet_array = []
        self.run_path = run_path

        # 多idc ds部署
        self.idc_all_region_id_suffix = []
        self.ds_yaml_config = None

    # exapmle: 1.0.28 => 1_0_28
    def format_version(self, version):
        ver_array = version.split(".")
        #if not ver_array or len(ver_array) < 3:
        #    print("invalid version format")
        #    sys.exit(2)
        return "_".join(ver_array)

    def load_ds_yaml(self):
        ds_yaml_path = os.path.join(self.run_path, DS_YAML_PATH)
        if os.path.exists(ds_yaml_path):
            f = codecs.open(ds_yaml_path, 'r', encoding='utf-8')
            cfg = f.read()
            f.close()
            res = yaml.load(cfg, Loader=yaml.FullLoader)
            return res
        return None

    def is_disable_ds_seed_in_yaml(self, dsaCfgId):
        if self.ds_yaml_config is None or self.ds_yaml_config['disable_ds_seed_list'] is None:
            return False
        if dsaCfgId in self.ds_yaml_config['disable_ds_seed_list']:
            return True
        return False

    def get_enable_ds_seed_by_dsa_cfg_id(self, defaultEnableDsSeed, dsaCfgId):
        if defaultEnableDsSeed == "true":
            if self.is_disable_ds_seed_in_yaml(dsaCfgId):
                return "false"
            else:
                return "true"
        return "false"

    def load_pbin_data(self, protoName, pbinFileName):
        fd = open(pbinFileName, 'rb')
        pbinProtoName = fd.readline().strip().decode('utf-8').split('.')[-1]

        # print(groupName + protoName)
        protoPy = __import__(protoName, fromlist=[protoName])

        #print dir(protoPy)

        pbinclass = getattr(protoPy, pbinProtoName)
        msg = pbinclass()
        msg.ParseFromString(fd.read())
        return msg

    def get_zone_id(self, dev_name):
        dev_ds_config = self.load_pbin_data("ResDsConfig_pb2", os.path.join(self.run_path, DS_DEV_INFO_PBIN_PATH))
        for pri in dev_ds_config.rows:
            if pri.dev_env == dev_name:
                return pri.id

        return 0

    def is_new_map(self, alias_name):
        return alias_name == "letsgo_startup" or alias_name == "lobby_startup"

    def append_fleet_info(self, version_fleet, fleets_value, map_value, build_value, dstype, ds_bk_id, startup_map_value, feature_pak_version, enable_ds_seed, isMultiThread, defaultEnableDsSeed):
        self.fleet_name_array.append(version_fleet)
        fleet_info = FleetInfo(version_fleet, fleets_value, map_value, build_value, dstype, ds_bk_id, startup_map_value, feature_pak_version, enable_ds_seed, isMultiThread, defaultEnableDsSeed)
        self.fleet_array.append(fleet_info)

    # build_ver: ds底包版本
    # dsa_ver: ds版本
    # ds_bk_id: ds构建id
    # build_infix_path: ds包中间路径
    # feature_id: 副玩法id(参见game-play.json)
    # dsa_config: dsa所有配置
    def gen_build(self, namespace, is_k8s, build_ver, dsa_ver, ds_min_new_compat_id, ds_bk_id, build_infix_path, map_id, feature_id, dsa_config, feature_pak_version, defaultEnableDsSeed):
        print "gen_build"
        print "buildVersin: ", build_ver, " dsVersion: ", dsa_ver, " minNewCompatId: ", ds_min_new_compat_id, " featurePakVersion: ", feature_pak_version
        build_value = self.format_version(build_ver) + "_build"
        if build_ver == "main" or build_ver == "" or not is_k8s:
            build_value = "default" + "_build"

        if  build_value not in self.build_name_array:
            self.build_name_array.append(build_value)
            build_info = BuildInfo(build_value, build_infix_path)
            self.build_array.append(build_info)
        else:
            print "build is exist"

        for row in dsa_config.rows:
            feature_id_filter = int(feature_id) == 0 or (int(feature_id) in row.separateAliasFeatureIds)
            if not feature_id_filter:
                continue
            map_id_filter = len(map_id) == 0 or (row.id in map_id)
            if not map_id_filter:
                continue

            map_value = row.mapName
            startup_map_value = row.startupMapName
            alias_value = row.alias
            isMultiThread = 1
            ## no attr means (-nothreading -postforkthreading)
            if hasattr(row, 'isMultiThread'):
                isMultiThread = row.isMultiThread
            fleets_value =  "fleet_" + row.alias
            if int(feature_id) != 0:
                fleets_value = "fleet_" + row.alias + "_" + feature_id
            is_new = False

            if dsa_ver == "main" or dsa_ver == "":
                if fleets_value not in self.fleet_name_array:
                    self.fleet_name_array.append(fleets_value)
                    fleet_info = FleetInfo(fleets_value, fleets_value, map_value, build_value, row.dstype, ds_bk_id, startup_map_value, feature_pak_version, self.get_enable_ds_seed_by_dsa_cfg_id(defaultEnableDsSeed, row.id), isMultiThread, defaultEnableDsSeed)
                    self.fleet_array.append(fleet_info)
            else:
                # version
                version_fleet = fleets_value + "_" + self.format_version(dsa_ver)
                if int(dsa_ver) >= int(ds_min_new_compat_id):
                    is_new = True
                if version_fleet not in self.fleet_name_array:
                    print version_fleet
                    ## old ver full map       new ver and new map
                    if not is_new or self.is_new_map(alias_value):
                        self.append_fleet_info(version_fleet, fleets_value, map_value, build_value, row.dstype, ds_bk_id, startup_map_value, feature_pak_version, self.get_enable_ds_seed_by_dsa_cfg_id(defaultEnableDsSeed, row.id), isMultiThread, defaultEnableDsSeed)
                    else:
                        print "is_new=", is_new, " map=", map_value, " version=", self.format_version(dsa_ver)

    def append_alias_info(self, pipeline_alias_value, fleets_value, compat_info):
        pipeline_fleets = []
        for compat_fleet_info in compat_info.compat_fleet_array:
            pipeline_fleet = fleets_value + "_" + self.format_version(compat_fleet_info.fleet_name)
            pipeline_fleets.append(AliasFleetInfo(pipeline_fleet, compat_fleet_info.fleet_weight))
        self.alias_name_array.append(pipeline_alias_value)
        pipeline_alias_info = AliasInfo(pipeline_alias_value, pipeline_fleets)
        self.alias_array.append(pipeline_alias_info)

    def append_feature_alias_info(self, feature_pipeline_alias_value, feature_fleets_value, feature_pkg_ds_info):
        feature_pipeline_fleets = []
        feature_pipeline_fleet = feature_fleets_value + "_" + self.format_version(feature_pkg_ds_info.feature_pkg_name)
        feature_pipeline_fleets.append(AliasFleetInfo(feature_pipeline_fleet, DS_FLEET_DEFAULT_WEIGHT))
        self.alias_name_array.append(feature_pipeline_alias_value)
        feature_pipeline_alias_info = AliasInfo(feature_pipeline_alias_value, feature_pipeline_fleets)
        self.alias_array.append(feature_pipeline_alias_info)

    def getMapIdListByRegionName(self, dsa_config, dsa_region_config, world_id, region_name):
        map_id_array = []
        region_map_array = []
        region_mix_type = 0
        for row in dsa_region_config.rows:
            if int(row.serverId) != int(world_id):
                continue
            region_map_array.extend(row.dsaCfgIdList)
            if row.regionName == region_name:
                ## use config
                map_id_array = row.dsaCfgIdList
                ## no attr means independent region
                if hasattr(row, 'regionMixType'):
                    region_mix_type = row.regionMixType
        ##print map_id_array
        ##print region_map_array
        if len(map_id_array) > 0 and region_mix_type == 0:
            ## independent region
            return map_id_array
        for row in dsa_config.rows:
            if row.id not in region_map_array:
                map_id_array.append(row.id)
        return map_id_array

    def getDefaultAllMapIdList(self, dsa_config):
        map_id_array = []
        for row in dsa_config.rows:
            map_id_array.append(row.id)
        return map_id_array

    #world_id=7,region_name_suffix="shanghai"
    def load(self, namespace, need_private, is_k8s, dsa_ver, ds_min_new_compat_id, map_id, ds_bk_id, compat_group_array, **extra_kwargs):
        world_id = extra_kwargs.get('world_id', None)
        region_name_suffix = extra_kwargs.get('region_name_suffix', None)
        defaultEnableDsSeed = extra_kwargs.get('defaultEnableDsSeed', "false")
        dsa_config = self.load_pbin_data("ResDsConfig_pb2", os.path.join(self.run_path, DS_CONFIG_INFO_PBIN_PATH))
        dev_ds_config = self.load_pbin_data("ResDsConfig_pb2", os.path.join(self.run_path, DS_DEV_INFO_PBIN_PATH))
        dsa_region_config_path = os.path.join(self.run_path, DS_REGION_CONFIG_INFO_PBIN_PATH)
        if world_id is not None and region_name_suffix is not None and os.path.exists(dsa_region_config_path):
            dsa_region_config = self.load_pbin_data("ResDsConfig_pb2", dsa_region_config_path)
            region_name = str(world_id) + "-" + region_name_suffix
            map_id_array = self.getMapIdListByRegionName(dsa_config, dsa_region_config, world_id, region_name)
        else:
            map_id_array = self.getDefaultAllMapIdList(dsa_config)
        self.ds_yaml_config = self.load_ds_yaml()

        ##print dsa_config
        ##print dev_ds_config
        ##print dsa_region_config
        ##print namespace
        ##build && fleet
        ##print region_name
        if dsa_ver == "main" or dsa_ver == "":
            self.gen_build(namespace, is_k8s, "main", "main", ds_min_new_compat_id, ds_bk_id, "", map_id_array, 0, dsa_config, "", defaultEnableDsSeed)
            version_array = []
            for row in dsa_config.rows:
                for version in row.version :
                    if  version not in version_array:
                        version_array.append(version)
            print version_array
            ##from config
            for version in version_array:
                if (dsa_ver == version or not is_k8s):
                    print "config ds"
                    self.gen_build(namespace, is_k8s, version, version, ds_min_new_compat_id, ds_bk_id, "", map_id_array, 0, dsa_config, "", defaultEnableDsSeed)
            ##from pipeline
            for compat_info in compat_group_array:
                print "pipeline ds"
                ## 底包ds
                for compat_fleet_info in compat_info.compat_fleet_array:
                    build_infix_path = "/ds/" + namespace + "/" + self.format_version(compat_fleet_info.fleet_name)
                    self.gen_build(namespace, is_k8s, compat_fleet_info.fleet_name, compat_fleet_info.fleet_name, ds_min_new_compat_id, compat_fleet_info.fleet_ds_bk_id, build_infix_path, map_id_array, 0, dsa_config, "", defaultEnableDsSeed)
                ## 独立ds feature pkg
                print "pipeline feature pkg ds"
                for feature_ds_info in compat_info.feature_pkg_ds_array:
                    ## 还是用底包作为启动路径
                    build_infix_path = "/ds/" + namespace + "/" + self.format_version(compat_info.name)
                    self.gen_build(namespace, is_k8s, compat_info.name, feature_ds_info.feature_pkg_name, ds_min_new_compat_id, feature_ds_info.ds_bk_id, build_infix_path, map_id_array, feature_ds_info.feature_id, dsa_config, feature_ds_info.feature_pkg_version, defaultEnableDsSeed)
        if need_private:
            ##from dev_ds
            for pri in dev_ds_config.rows:
                if dsa_ver == pri.dev_env and is_k8s:
                    print "pri ds"
                    self.gen_build(namespace, is_k8s, pri.dev_env, pri.dev_env, ds_min_new_compat_id, ds_bk_id, "", map_id_array, 0, dsa_config, "", defaultEnableDsSeed)

        ##alias
        for row in dsa_config.rows:
            map_id_filter = len(map_id_array) == 0 or (row.id in map_id_array)
            if not map_id_filter:
                continue
            alias_value = row.alias
            fleets_value =  "fleet_" + row.alias
            ##main alias
            alias_info = AliasInfo(alias_value, [AliasFleetInfo(fleets_value, DS_FLEET_DEFAULT_WEIGHT)])
            self.alias_array.append(alias_info)
            ##config alias
            for version in row.version :
                version_alias_value = alias_value + "_" + self.format_version(version)
                version_fleet = fleets_value + "_" + self.format_version(version)
                version_fleets = [AliasFleetInfo(version_fleet, DS_FLEET_DEFAULT_WEIGHT)]
                self.alias_name_array.append(version_alias_value)
                version_alias_info = AliasInfo(version_alias_value, version_fleets)
                self.alias_array.append(version_alias_info)

            is_new_map = self.is_new_map(row.alias)
            ## pipeline alias
            for compat_info in compat_group_array:
                is_new = False
                if int(compat_info.name) >= int(ds_min_new_compat_id):
                    is_new = True
                # print "pipeline alias name: ", compat_info.name, " isNew: ", is_new
                ## old ver full map       new ver and new map
                if not is_new or is_new_map:
                    pipeline_alias_value = alias_value + "_" + self.format_version(compat_info.name)
                    if  pipeline_alias_value not in self.alias_name_array:
                        self.append_alias_info(pipeline_alias_value, fleets_value, compat_info)
                    ## feature alias
                    for feature_ds_info in compat_info.feature_pkg_ds_array:
                        if int(feature_ds_info.feature_id) in row.separateAliasFeatureIds:
                            feature_pipeline_alias_value = alias_value + "_" + feature_ds_info.feature_id + "_" + self.format_version(feature_ds_info.feature_pkg_name)
                            feature_fleets_value =  "fleet_" + row.alias + "_" + feature_ds_info.feature_id
                            self.append_feature_alias_info(feature_pipeline_alias_value, feature_fleets_value, feature_ds_info)
                else:
                    print "is_new=", is_new, " map=", row.mapName, " newMinCompatId=", ds_min_new_compat_id

            ##pri alias
            if is_k8s and need_private:
                for pri in dev_ds_config.rows:
                    pri_fleet =  fleets_value + "_" + pri.dev_env
                    pri_alias_value = alias_value + "_" + pri.dev_env
                    self.alias_name_array.append(pri_alias_value)
                    pri_alias_info = AliasInfo(pri_alias_value, [AliasFleetInfo(pri_fleet, DS_FLEET_DEFAULT_WEIGHT)])
                    self.alias_array.append(pri_alias_info)

        self.build_array.sort()


def main():
    gen_class = GenDsaConfigClass()
    gen_class.load(True, False, "")

    print gen_class.fleet_name_array
    print gen_class.alias_array
    print gen_class.build_name_array
    print gen_class.build_array
    print gen_class.fleet_array

if __name__ == '__main__':
    main()
package org.openjdk.jol.timiext.filter.node;

import java.lang.reflect.Field;

public class EnumNodeFilter implements JolNodeFilter {

    private static EnumNodeFilter instance = new EnumNodeFilter();

    public static EnumNodeFilter getDefault() {
        return instance;
    }

    @Override
    public boolean filter(Object obj, Object parent, Field field) {
        Class curClazz = obj.getClass();
        if (obj.getClass().isEnum()) {
            return true;
        }
        return false;
    }
}

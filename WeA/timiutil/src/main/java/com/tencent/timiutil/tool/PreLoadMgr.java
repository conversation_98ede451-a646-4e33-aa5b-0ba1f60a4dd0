package com.tencent.timiutil.tool;


import io.github.classgraph.ClassGraph;
import io.github.classgraph.ClassInfoList;
import io.github.classgraph.ScanResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.stream.Collectors;

public class PreLoadMgr {
    private static final Logger LOGGER = LogManager.getLogger(PreLoadMgr.class);

    public static PreLoadMgr getInstance() {
        return PreLoadMgr.LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final PreLoadMgr INSTANCE = new PreLoadMgr();
    }

    public void preLoadPkg(String url, boolean verbose) {
        long start = System.currentTimeMillis();
        try (ScanResult result = new ClassGraph()
                                    .verbose(verbose)
                                    .enableClassInfo()
                                    .acceptPackages(url)
                                    .scan()) {
            ClassInfoList classInfos = result.getAllClasses();
            List<Class<?>> classList = classInfos.loadClasses();
            for (Class clazz : classList) {
                LOGGER.debug("preload class {}", clazz.getName());
            }
            
            LOGGER.info("preload {} classes in {} cost {}"
                , classList.size(), url, System.currentTimeMillis() - start);
        } catch (Exception e) {
            LOGGER.error("preload package:{} exception"
                , url, e);
            List<String> classpathEntries = new ClassGraph()
                    .getClasspathFiles()
                    .stream()
                    .map(file -> file.getPath())
                    .collect(Collectors.toList());
            LOGGER.error("preload package:{} cp:{}", url, classpathEntries);
            throw new RuntimeException(e);
        }
    }
}

package queryCmd.Command;

import static com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants.TCAPLUS_CMD_UPDATE_REQ;

import Metadata.Field;
import Metadata.Table;
import SQLprocess.InputQuery;
import Util.Connection;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import tableTransfrom.fieldToRecord;

public class Update {

    private static Logger logger = LogManager.getLogger("Update.class");

    public static void update(Table tb, long seq, InputQuery inputQuery) {
        Request req = Connection.getRequest();
        req.setTableName(tb.getName());
        req.setCmd(TCAPLUS_CMD_UPDATE_REQ);
        Record record = req.addRecord();
        for (Field entry : tb.getFields()) {
            if (entry.isKey() && entry.getValueString() != null) {
                if (entry.getBlobColumeSplit() != null) {
                    fieldToRecord.setSplitRecordBlob(record, entry);
                } else {
                    fieldToRecord.SetRecordKey(record, entry);
                }
            } else if (!entry.isKey() && entry.getValueString() != null) {
                if (entry.getBlobColumeSplit() != null) {
                    fieldToRecord.setSplitRecordBlob(record, entry);
                } else {
                    fieldToRecord.SetRecordValue(record, entry);
                }
            }
        }

        if (seq == -1) {
            Response response = Connection.getClient().poll(req);
            if (response.getResult() != 0) {
                logger.error("Update Error-->{}", response.getResult());
                System.err.println("ERROR [Cmd (UPDATE)]:" + response.getErrDetail());
            } else {
                if (inputQuery != null) {
                    System.out.println("SUCCESS [Cmd (UPDATE)]");
                }
            }
        } else {
            Connection.getClient().post(req, response -> {
                int index = (int) seq % inputQuery.getSize();
                String asynRes = "";
                if (response.getResult() != 0) {
                    logger.error("Update Error-->{}", response.getResult());
                    asynRes += String
                            .format("ERROR[Seq(%d) Cmd(UPDATE)]: Please check your input,maybe the key is not "
                                            + "existed%n",
                                    seq);
                } else {
                    asynRes += String.format("SUCCESS[Seq(%d) Cmd(UPDATE)]%n", seq);
                }
                inputQuery.getBuffer()[index] = asynRes;
            });
        }
    }
}

import Main.clientMain;
import SQLprocess.InputQuery;
import Util.Connection;
import java.util.Scanner;
import org.junit.After;
import org.junit.Before;


public class TestMain {

    /**
     * Method: inputOneQuery(String query)
     */
    public static void main(String[] args) throws Exception {
        clientMain.init();
        Scanner sc = new Scanner(System.in);
        InputQuery inputQuery = new InputQuery();

        while (true) {
            System.out.print("[TcaplusClient]#");
            String line = sc.nextLine();
            if (line.equalsIgnoreCase("quit")) {
                break;
            }
            try {
                inputQuery.inputOneQuery(line, -1, null);
            } catch (NullPointerException e) {
                System.err.println(e.getMessage());
            } catch (IllegalArgumentException e) {
                System.err.println(e.getMessage());
            } catch (IllegalStateException e) {
                System.err.println(e.getMessage());
            }
        }

        Connection.close();

    }

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }
}
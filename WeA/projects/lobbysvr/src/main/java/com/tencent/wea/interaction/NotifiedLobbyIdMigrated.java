package com.tencent.wea.interaction;

import com.tencent.wea.lobbyservice.lobbydata.LobbyInfo;
import com.tencent.wea.lobbyservice.lobbydata.LobbyMgr;

public class NotifiedLobbyIdMigrated {
    public static void handle(long lobbyId) {
        String lobbyGroupId = LobbyMgr.getLobbyGroupId(lobbyId);
        if (lobbyGroupId == null) {
            return;
        }
        LobbyMgr lobbyMgr = LobbyMgr.getInstance(lobbyGroupId);
        LobbyInfo lobby = lobbyMgr.getLobbyInfo(lobbyId);
        if (lobby != null) {
            lobby.onLobbyIdMigrated();
        }
    }
}

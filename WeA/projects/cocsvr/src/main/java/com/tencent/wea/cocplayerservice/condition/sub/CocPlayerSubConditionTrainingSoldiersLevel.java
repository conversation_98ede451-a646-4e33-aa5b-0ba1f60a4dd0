package com.tencent.wea.cocplayerservice.condition.sub;

import com.tencent.eventcenter.BaseEvent;
import com.tencent.wea.cocplayerservice.condition.BaseCocPlayerSubCondition;
import com.tencent.wea.cocplayerservice.event.CocPlayerTrainingSoldierEvent;
import com.tencent.wea.xlsRes.keywords.SubConditionType;

import java.util.List;

public class CocPlayerSubConditionTrainingSoldiersLevel extends BaseCocPlayerSubCondition {
    @Override
    public int getType() {
        return SubConditionType.SCT_CocTrainingSoldiersLevel_VALUE;
    }

    @Override
    public boolean isOk(List<Long> valueList, BaseEvent event) {
        if (event instanceof CocPlayerTrainingSoldierEvent) {
            CocPlayerTrainingSoldierEvent trainingSoldierEvent = (CocPlayerTrainingSoldierEvent) event;
            if (!valueList.isEmpty() && valueList.get(0) == 0L) {
                return true;
            }
            if (valueList.contains((long) trainingSoldierEvent.getLevel()) && trainingSoldierEvent.getAmount() > 0) {
                return true;
            }
        }
        return false;
    }
}

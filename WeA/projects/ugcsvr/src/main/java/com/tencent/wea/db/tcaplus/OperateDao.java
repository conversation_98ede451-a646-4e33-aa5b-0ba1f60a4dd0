package com.tencent.wea.db.tcaplus;

import com.tencent.cache.Cache;
import com.tencent.cache.CacheUtil;
import com.tencent.coRedis.CoRedisCmd;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.BreakException;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusManager.TcaplusRecordData;
import com.tencent.tcaplus.TcaplusManager.TcaplusRecordGroup;
import com.tencent.tcaplus.TcaplusManager.TcaplusRsp;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.wea.data.MapData;
import com.tencent.wea.data.OpActionData.UgcOpActionData;
import com.tencent.wea.data.UgcPlayerData;
import com.tencent.wea.handle.ConfigHandle;
import com.tencent.wea.manager.SubscriptionMgr;
import com.tencent.wea.module.operate.UgcOpStatusEnum;
import com.tencent.wea.module.sortmgr.CollectMapSortMgr;
import com.tencent.wea.module.sortmgr.PlayedMapSortMgr;
import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcMapLabelScoreInfo;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcCollect;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcFans;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcGiveLike;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcSub;
import com.tencent.wea.xlsRes.keywords.UGCMapType;
import com.tencent.wea.xlsRes.keywords.UgcMapConfEnum;
import io.lettuce.core.KeyValue;
import io.lettuce.core.ScoredValue;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeoutException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class OperateDao {

    private static final Logger LOGGER = LogManager.getLogger(OperateDao.class);

    private static final int PLAY_MAP_CAP = 500;

    public static OperateDao getInstance() {
        return InstanceHolder.INSTANCE;
    }

    public PlayerUgcGiveLike getGiveLikeData(long creatorId, long ugcId) {
        TcaplusDb.PlayerUgcGiveLike.Builder builder = TcaplusDb.PlayerUgcGiveLike.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setUgcId(ugcId);
        TcaplusRsp rsp = TcaplusUtil.newGetReq(builder).send();
        if (rsp.getResult().noError()) {
            return (PlayerUgcGiveLike) rsp.firstRecordData().msg;
        } else {
            if (!rsp.getResult().recordNotExist()) {
                LOGGER.error("OperateDbMgr giveLike err,result:{} ugcId {}", rsp.getResult(), ugcId);
            }
        }
        return null;
    }

    public boolean deleteGiveLikeStatus(long creatorId, long ugcId) {
        PlayerUgcGiveLike.Builder newBuilder = PlayerUgcGiveLike.newBuilder();
        newBuilder.setCreatorId(creatorId);
        newBuilder.setUgcId(ugcId);
        TcaplusRsp rsp = TcaplusUtil.newDeleteReq(newBuilder).send();
        if (rsp.getResult().noError()) {
            return true;
        } else {
            LOGGER.error("OperateDbMgr deleteGiveLikeStatus err,result:{} ugcId {}", rsp.getResult(), ugcId);
        }
        return false;
    }

    public boolean insertGiveLikeData(UgcOpActionData data) {
        PlayerUgcGiveLike.Builder giveLike = PlayerUgcGiveLike.newBuilder();
        giveLike.setCreatorId(data.creatorId);
        giveLike.setUgcId(data.ugcId);
        giveLike.setStatus(UgcOpStatusEnum.Operate.getStatus());
        giveLike.setCreateTime(Framework.currentTimeMillis());
        giveLike.setMapType(data.mapType.getNumber());
        giveLike.setUgcResType(data.ugcResParam.getResType().getNumber());

        TcaplusRsp rsp = TcaplusUtil.newInsertReq(giveLike).send();
        if (rsp.getResult().noError()) {
            return true;
        } else {
            LOGGER.error("OperateDbMgr insertGiveLikeData err,result:{} ugcId {}", rsp.getResult(), data.ugcId);
        }
        return false;
    }


    public PlayerUgcCollect getCollectData(long creatorId, long ugcId) {
        TcaplusDb.PlayerUgcCollect.Builder builder = TcaplusDb.PlayerUgcCollect.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setUgcId(ugcId);
        TcaplusRsp rsp = TcaplusUtil.newGetReq(builder).send();
        if (rsp.getResult().noError()) {
            return (PlayerUgcCollect) rsp.firstRecordData().msg;
        } else {
            if (!rsp.getResult().recordNotExist()) {
                LOGGER.error("OperateDbMgr getCollectData err,result:{} ugcId {}", rsp.getResult(), ugcId);
            }
        }
        return null;
    }

    public boolean insertCollectData(UgcOpActionData data) {
        PlayerUgcCollect.Builder collect = PlayerUgcCollect.newBuilder();
        collect.setCreatorId(data.creatorId);
        collect.setUgcId(data.ugcId);
        collect.setStatus(UgcOpStatusEnum.Operate.getStatus());
        collect.setCreateTime(data.opTime);
        collect.setMapType(data.mapType.getNumber());
        collect.setUgcResType(data.ugcResParam.getResType().getNumber());

        TcaplusRsp rsp = TcaplusUtil.newInsertReq(collect).send();
        if (rsp.getResult().noError()) {
            return true;
        } else {
            LOGGER.error("OperateDbMgr insertCollectData err,result:{} ugcId {}", rsp.getResult(), data.ugcId);
        }
        return false;
    }

    public Map<Long, PlayerUgcCollect> getAllCollectList(long creatorId, Set<Long> list) {
        TcaplusManager.TcaplusReq batchGetReq = null;
        Map<Long, PlayerUgcCollect> retMap = new HashMap<>();
        HashSet<Long> collectList = new HashSet<>(list);
        for (Long ugcId : collectList) {
            TcaplusDb.PlayerUgcCollect.Builder collect = TcaplusDb.PlayerUgcCollect.newBuilder();
            collect.setCreatorId(creatorId);
            collect.setUgcId(ugcId);

            if (batchGetReq == null) {
                batchGetReq = TcaplusUtil.newBatchGetReq(collect);
            } else {
                batchGetReq.addRecord(collect);
            }

            if (batchGetReq != null && batchGetReq.getRecordCount() >= 1024) {
                getBatchCollect(retMap, batchGetReq);
                batchGetReq = null;
            }
        }
        getBatchCollect(retMap, batchGetReq);
        return retMap;
    }

    private void getBatchCollect(Map<Long, PlayerUgcCollect> list, TcaplusManager.TcaplusReq batchGetReq) {
        if (batchGetReq == null) {
            return;
        }
        TcaplusRsp batchGetRsp = TcaplusManager.getInstance().tcaplusSend(batchGetReq);
        if (!batchGetRsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getAllPublish batch get list fail code {}",
                    batchGetRsp.getResult().getValue());
        }

        for (TcaplusRecordGroup rspData : batchGetRsp.getRspDataList()) {
            for (TcaplusRecordData rData : rspData.getRecordList()) {
                TcaplusDb.PlayerUgcCollect data = (TcaplusDb.PlayerUgcCollect) rData.msg;
                list.put(data.getUgcId(), data);
            }
        }
    }

    public void addOMDPlayedMap(long creatorId, long ugcId, int mapType, int pointsNum) {
        LOGGER.debug("addOMDPlayedMap mapId({}), userId({})", ugcId, creatorId);
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId, mapType, pointsNum);
        try {
            long score = Framework.currentTimeMillis();
            coRedisCmd.zadd(redisKey, score, String.valueOf(ugcId));
            // 长度判断
            Long zcard = coRedisCmd.zcard(redisKey);
            int cap = ConfigHandle.getConfigEnum(UgcMapConfEnum.UGC_PLAY_MAP_SHOW_LIST_COUNT_LIMIT, PLAY_MAP_CAP);
            if (zcard != null && zcard > cap) {
                coRedisCmd.zremrangebyrank(redisKey, 0, zcard - cap - 1);
            }
        } catch (Exception e) {
            LOGGER.error("addOMDPlayedMap err:{} creatorId:{}, ugcId:{}", e.getMessage(), creatorId, ugcId);
        }
    }

    public void addPlayedMap(long creatorId, long ugcId, UgcPlayerData playerData, MapData map, boolean isPass) {
        LOGGER.debug("AddPlayedMap mapId({}), userId({})", ugcId, creatorId);
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId);
        try {
            long score = Framework.currentTimeMillis();
            coRedisCmd.zadd(redisKey, score, String.valueOf(ugcId));
            // 长度判断
            Long zcard = coRedisCmd.zcard(redisKey);
            int cap = ConfigHandle.getConfigEnum(UgcMapConfEnum.UGC_PLAY_MAP_SHOW_LIST_COUNT_LIMIT, PLAY_MAP_CAP);
            if (zcard != null && zcard > cap) {
                coRedisCmd.zremrangebyrank(redisKey, 0, zcard - cap - 1);
            }
            if (playerData == null || playerData.getCollect(UgcInstanceType.CommonInstance_VALUE).getInfoMap()
                    .containsKey(ugcId)) {
                CollectMapSortMgr.getInstance().updateRedisMapPlayTime(creatorId, ugcId, score);
            }
            PlayedMapSortMgr.getInstance().addMapToRedis(creatorId, map, isPass);
        } catch (Exception e) {
            LOGGER.error("AddPlayedMap err:{} creatorId:{}, ugcId:{}", e.getMessage(), creatorId, ugcId);
        }
    }

    public List<Long> getPlayedMapIdList(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId);
        try {
            List<String> res = coRedisCmd.zrevrange(redisKey, offset, offset + num - 1);
            List<Long> mapIdList = new ArrayList<>(res.size());
            res.forEach(mapId -> {
                mapIdList.add(Long.valueOf(mapId));
            });
            return mapIdList;
        } catch (Exception e) {
            LOGGER.error("getPlayedMapIdList err:{} creatorId:{}, offset:{}", e.getMessage(), creatorId, offset);
        }
        return new ArrayList<>();
    }

    public List<Long> getOMDPlayedMapIdList(long creatorId, int offset, int num, int pointsNum) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId, UGCMapType.OMDGame_VALUE, pointsNum);
        try {
            List<String> res = coRedisCmd.zrevrange(redisKey, offset, offset + num - 1);
            List<Long> mapIdList = new ArrayList<>(res.size());
            res.forEach(mapId -> {
                mapIdList.add(Long.valueOf(mapId));
            });
            return mapIdList;
        } catch (Exception e) {
            LOGGER.error("getOMDPlayedMapIdList err:{} creatorId:{}, offset:{}", e.getMessage(), creatorId, offset);
        }
        return new ArrayList<>();
    }

    public List<ScoredValue<String>> getPlayedMapIdListWithScore(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId);
        try {
            List<ScoredValue<String>> res = coRedisCmd.zrevrangeWithScores(redisKey, offset, offset + num - 1);
            return res;
        } catch (Exception e) {
            LOGGER.error("getPlayedMapIdList err:{} creatorId:{}, offset:{}", e.getMessage(), creatorId, offset);
        }
        return new ArrayList<>();
    }

    public long getPlayedMapTime(long creatorId, long ugcId) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcPlayMapRecord.getKey(creatorId);
        try {
            Double res = coRedisCmd.zscore(redisKey, String.valueOf(ugcId));
            if (res != null) {
                return res.longValue();
            }
        } catch (Exception e) {
            LOGGER.error("getPlayedMapTime err:{} creatorId:{}, ugcId:{}", e.getMessage(), creatorId, ugcId);
        }
        return 0;
    }


    public List<Long> getPublishMapIdList(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapPublishTime.getKey(creatorId);
        try {
            List<String> res = coRedisCmd.zrevrange(redisKey, offset, offset + num - 1);
            List<Long> mapIdList = new ArrayList<>(res.size());
            res.forEach(mapId -> {
                mapIdList.add(Long.valueOf(mapId));
            });
            return mapIdList;
        } catch (Exception e) {
            LOGGER.error("getPublishMapIdList creatorId:{}, offset:{}, e",
                    creatorId, offset, e);
        }
        return new ArrayList<>();
    }

    // 获取已发布的组合地图的id
    public List<Long> getPublishGroupMapIdList(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcGroupMapPublishTime.getKey(creatorId);
        try {
            List<String> res = coRedisCmd.zrevrange(redisKey, offset, offset + num - 1);
            List<Long> mapIdList = new ArrayList<>(res.size());
            res.forEach(mapId -> {
                mapIdList.add(Long.valueOf(mapId));
            });
            return mapIdList;
        } catch (Exception e) {
            LOGGER.error("getPublishGroupMapIdList creatorId:{}, offset:{}, e",
                    creatorId, offset, e);
        }
        return new ArrayList<>();
    }

    public List<KeyValue<String, String>> getPlayerLastPublishTime(Set<Long> creatorIds) {
        if (creatorIds == null || creatorIds.isEmpty()) {
            return new ArrayList<>();
        }
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        List<String> keys = new ArrayList<>(creatorIds.size());
        creatorIds.forEach(creatorId -> {
            String redisKey = CacheUtil.UgcLastPublishTime.getKey(creatorId);
            keys.add(redisKey);
        });

        try {
            return coRedisCmd.mget(keys.toArray(String[]::new));
        } catch (Exception e) {
            LOGGER.error("getPlayerLastPublishTime err:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public void updatePlayerLastPublishTime(long creatorId) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcLastPublishTime.getKey(creatorId);
        try {
            coRedisCmd.set(redisKey, String.valueOf(Framework.currentTimeMillis()));
        } catch (Exception e) {
            LOGGER.error("updatePlayerLastPublishTime err:{}", e.getMessage());
        }
    }

    public void updatePlayerLastPublishTime(long creatorId, long createTime) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcLastPublishTime.getKey(creatorId);
        try {
            coRedisCmd.set(redisKey, String.valueOf(createTime));
        } catch (Exception e) {
            LOGGER.error("updatePlayerLastPublishTime err:{}", e.getMessage());
        }
    }

    public void updatePlayerLastPublishTimeFromPublishList(long creatorId) {
        long createTime = 0;
        List<ScoredValue<String>> scoredValueList = getPublishMapIdListWithPublishTime(creatorId, 0, 1);
        if (!scoredValueList.isEmpty()) {
            createTime = (long) scoredValueList.get(0).getScore();
        }
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcLastPublishTime.getKey(creatorId);
        try {
            coRedisCmd.set(redisKey, String.valueOf(createTime));
        } catch (Exception e) {
            LOGGER.error("updatePlayerLastPublishTimeFromPublishList err:{}", e.getMessage());
        }
    }

    public void batchAddSubPlayerMapsByPublishTime(long creatorId, List<ScoredValue<String>> insertList) {
        if (insertList.size() <= 0) {
            return;
        }
        String key = CacheUtil.UgcSubPlayerMaps.getKey(creatorId);

        CoRedisCmd<String, String> redisCmd = Cache.getCoRedisCmdForString();
        try {
            ScoredValue<String>[] array = new ScoredValue[insertList.size()];
            redisCmd.zaddAll(key, insertList.toArray(array));
            redisCmd.expire(key, SubscriptionMgr.SUB_MAPS_EXPIRE_SECONDS);
        } catch (InterruptedException | TimeoutException | BreakException e) {
            LOGGER.error("batchAddSubPlayerMapsByPublishTime fail e:{}", e.getMessage());
        }
    }

    public void batchDelSubPlayerMapsByPublishTime(long creatorId, List<ScoredValue<String>> delList) {
        if (delList.size() <= 0) {
            return;
        }
        String key = CacheUtil.UgcSubPlayerMaps.getKey(creatorId);
        CoRedisCmd<String, String> redisCmd = Cache.getCoRedisCmdForString();
        try {
            String[] members = delList.stream().map(ScoredValue::getValue).toArray(String[]::new);
            redisCmd.zrem(key, members);
        } catch (InterruptedException | TimeoutException | BreakException e) {
            LOGGER.error("batchDelSubPlayerMapsByPublishTime fail e:{}", e.getMessage());
        }
    }

    public void batchDelSubPlayerMapsByPublishTime(long creatorId, Set<Long> delList) {
        if (delList.size() <= 0) {
            return;
        }
        String key = CacheUtil.UgcSubPlayerMaps.getKey(creatorId);
        CoRedisCmd<String, String> redisCmd = Cache.getCoRedisCmdForString();
        try {
            String[] members = delList.stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            redisCmd.zrem(key, members);
        } catch (InterruptedException | TimeoutException | BreakException e) {
            LOGGER.error("batchDelSubPlayerMapsByPublishTime fail e:{}", e.getMessage());
        }
    }

    public long getSubMapIdListSize(long creatorId) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcSubPlayerMaps.getKey(creatorId);
        try {
            return coRedisCmd.zcard(redisKey);
        } catch (Exception e) {
            LOGGER.error("getSubMapIdListSize err:{} creatorId:{}", e.getMessage(), creatorId);
        }
        return 0;
    }

    public List<ScoredValue<String>> getSubMapIdListWithPublishTime(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcSubPlayerMaps.getKey(creatorId);
        try {
            List<ScoredValue<String>> res = coRedisCmd.zrevrangeWithScores(redisKey, offset, offset + num - 1);
            coRedisCmd.expire(redisKey, SubscriptionMgr.SUB_MAPS_EXPIRE_SECONDS);
            return res;
        } catch (Exception e) {
            LOGGER.error("getSubMapIdListWithPublishTime err:{} creatorId:{}, offset:{}", e.getMessage(), creatorId,
                    offset);
        }
        return new ArrayList<>();
    }

    public List<ScoredValue<String>> getPublishMapIdListByScoreWithPublishTime(long creatorId, long lower, long upper,
            long count) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapPublishTime.getKey(creatorId);
        try {
            List<ScoredValue<String>> res = coRedisCmd.zrevrangeByscoreWithScores(redisKey, lower, upper, 0,
                    count);
            return res;
        } catch (Exception e) {
            LOGGER.error("getPublishMapIdListByScoreWithPublishTime err:{} creatorId:{}, lower:{}, upper{},count:{}",
                    e.getMessage(), creatorId, lower, upper, count);
        }
        return new ArrayList<>();
    }


    public List<ScoredValue<String>> getPublishMapIdListWithPublishTime(long creatorId, int offset, int num) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapPublishTime.getKey(creatorId);
        try {
            List<ScoredValue<String>> res = coRedisCmd.zrevrangeWithScores(redisKey, offset, offset + num - 1);
            return res;
        } catch (Exception e) {
            LOGGER.error("getPublishMapIdList err:{} creatorId:{}, offset:{}", e.getMessage(), creatorId, offset);
        }
        return new ArrayList<>();
    }

    public boolean isCreatorPublishMap(long creatorId, long ugcId) {
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapPublishTime.getKey(creatorId);
        try {
            if (Objects.nonNull(coRedisCmd.zscore(redisKey, String.valueOf(ugcId)))) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("zscoreFail redisKey:{} . ", redisKey, e);
        }
        return false;
    }

    public boolean deleteCollectStatus(long creatorId, long ugcId) {
        PlayerUgcCollect.Builder newBuilder = PlayerUgcCollect.newBuilder();
        newBuilder.setCreatorId(creatorId);
        newBuilder.setUgcId(ugcId);
        TcaplusRsp rsp = TcaplusUtil.newDeleteReq(newBuilder).send();
        if (rsp.getResult().noError()) {
            return true;
        } else {
            LOGGER.error("OperateDbMgr deleteCollectStatus err,result:{} ugcId {}", rsp.getResult(), ugcId);
        }
        return false;
    }

    public PlayerUgcSub getUgcSub(long creatorId, long subCreatorId) {
        if (subCreatorId == creatorId) {
            return null;
        }

        PlayerUgcSub.Builder builder = PlayerUgcSub.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setSubId(subCreatorId);
        TcaplusRsp rsp = TcaplusUtil.newGetReq(builder).send();
        if (rsp.getResult().noError()) {
            return (PlayerUgcSub) rsp.firstRecordData().msg;
        } else {
            if (!rsp.getResult().recordNotExist()) {
                LOGGER.error("OperateDbMgr getUgcSub err,result:{} creatorId:{},subCreatorId:{}",
                        rsp.getResult().getValue(), creatorId, subCreatorId);
            }
        }
        return null;
    }


    public HashSet<Long> batchGetUgcSubs(long creatorId, Set<Long> subCreatorIds) {
        TcaplusManager.TcaplusReq batchGetReq = null;
        HashSet<Long> setSubIds = new HashSet<>();
        for (long subCreatorId : subCreatorIds) {
            PlayerUgcSub.Builder builder = PlayerUgcSub.newBuilder();
            builder.setCreatorId(creatorId);
            builder.setSubId(subCreatorId);
            if (batchGetReq == null) {
                batchGetReq = TcaplusUtil.newBatchGetReq(builder);
            } else {
                batchGetReq.addRecord(builder);
            }
            if (batchGetReq.getRecordCount() >= 1000) {
                HashSet<Long> set = batchTcaplusSend(batchGetReq);
                batchGetReq = null;
                if(!set.isEmpty()){
                    setSubIds.addAll(set);
                }
            }
        }

        if (batchGetReq != null) {
            HashSet<Long> set = batchTcaplusSend(batchGetReq);
            if (!set.isEmpty()) {
                setSubIds.addAll(set);
            }
        }
        return setSubIds;
    }

    private HashSet<Long> batchTcaplusSend(TcaplusManager.TcaplusReq batchGetReq) {
        HashSet<Long> subIds = new HashSet<>();
        TcaplusRsp batchGetRsp = TcaplusManager.getInstance().tcaplusSend(batchGetReq);
        if (!batchGetRsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("BaseTable batchTcaplusSend fail {}", batchGetRsp.getResult());
            return subIds;
        }
        for (TcaplusManager.TcaplusRecordGroup rspData : batchGetRsp.getRspDataList()) {
            for (TcaplusManager.TcaplusRecordData<?> rData : rspData.getRecordList()) {
                PlayerUgcSub msg = (PlayerUgcSub)rData.msg;
                subIds.add(msg.getSubId());
            }
        }
        return subIds;
    }

    public boolean insertUgcSub(long creatorId, long subUserId) {
        PlayerUgcSub.Builder builder = PlayerUgcSub.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setSubId(subUserId);
        builder.setIsTop(0);
        builder.setTopTime(-1);
        builder.setCreateTime(Framework.currentTimeMillis());
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newInsertReq(builder).send();
        if (rsp.isOKIgnoreRecordNotExist()) {
            return true;
        } else {
            LOGGER.error("OperateDbMgr insertUgcSbu err,result:{} creatorId:{},subUserId:{}", rsp.getResult(),
                    creatorId,
                    subUserId);
        }
        return false;
    }

    public boolean insertFansDb(long fansId, long subUid) {
        PlayerUgcFans.Builder builder = PlayerUgcFans.newBuilder();
        builder.setCreatorId(subUid);
        builder.setFanId(fansId);
        builder.setCreateTime(Framework.currentTimeMillis());
        TcaplusManager.TcaplusRsp result = TcaplusUtil.newInsertReq(builder).send();
        if (result.isOKIgnoreRecordNotExist()) {
            return true;
        } else {
            LOGGER.error("insertFansDb err,result:{} fansId:{},subUid:{}", result.getResult(), fansId, subUid);
        }
        return false;
    }

    public boolean deleteFans(long creatorId, long fansId) {
        PlayerUgcFans.Builder builder = PlayerUgcFans.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setFanId(fansId);
        builder.setCreateTime(Framework.currentTimeMillis());
        TcaplusManager.TcaplusRsp result = TcaplusUtil.newDeleteReq(builder).send();
        if (!result.isOKIgnoreRecordNotExist()) {
            LOGGER.error("deleteFans err,result:{} creatorId:{},fansId:{}", result.getResult(), creatorId, fansId);
            return false;
        }
        return true;
    }

    public boolean deleteSub(long creatorId, long subId) {
        PlayerUgcSub.Builder builder = PlayerUgcSub.newBuilder();
        builder.setCreatorId(creatorId);
        builder.setSubId(subId);
        TcaplusManager.TcaplusRsp result = TcaplusUtil.newDeleteReq(builder).send();
        if (!result.isOKIgnoreRecordNotExist()) {
            LOGGER.error("deleteSub err,result:{} creatorId:{},subId:{}", result.getResult(), creatorId, subId);
            return false;
        }
        return true;
    }

    public List<PlayerUgcGiveLike> getAllGiveLikeList(long creatorId) {

        List<PlayerUgcGiveLike> likeList = new ArrayList<>();

        TcaplusDb.PlayerUgcGiveLike.Builder dbReq = TcaplusDb.PlayerUgcGiveLike.newBuilder();
        dbReq.setCreatorId(creatorId);
        TcaplusRsp rsp = TcaplusUtil.newGetByPartKeyReq(dbReq).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("getAllGiveLikeList db error={}", rsp.getResult());
            return likeList;
        }

        for (TcaplusManager.TcaplusRecordGroup rspData : rsp.getRspDataList()) {
            for (TcaplusManager.TcaplusRecordData<?> rData : rspData.getRecordList()) {
                PlayerUgcGiveLike giveLikeDbData = (PlayerUgcGiveLike) rData.msg;
                LOGGER.debug("get givelike data, ugcid:{}", giveLikeDbData.getUgcId());
                likeList.add(giveLikeDbData);
            }
        }
        return likeList;
    }

    public List<UgcMapLabelScoreInfo> updateRedisMapLabelScore(long ugcId, List<UgcMapLabelScoreInfo> addInfos,
            int addScoreCount) {
        LOGGER.debug("updateRedisMapLabelScore mapId({})", ugcId);
        List<UgcMapLabelScoreInfo> newInfos = new ArrayList<>();
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapLabelScore.getKey(ugcId);
        try {
            for (UgcMapLabelScoreInfo info : addInfos) {
                UgcMapLabelScoreInfo.Builder newInfo = info.toBuilder();
                String field = String.valueOf(info.getLabel());
                // 增加分数
                long newScore = coRedisCmd.hincrby(redisKey, "SCORE_" + field, info.getScore());
                // 增加人数
                long newCount = coRedisCmd.hincrby(redisKey, "COUNT_" + field, addScoreCount);
                newInfo.setScore(newScore).setCount(newCount);
                newInfos.add(newInfo.build());
            }
        } catch (Exception e) {
            LOGGER.error("updateRedisMapLabelScore err:{} ugcId:{}", e.getMessage(), ugcId);
            NKErrorCode.UgcMapLabelScoreUpdateFail.throwError("updateRedisMapLabelScore err:{} ugcId:{}",
                    e.getMessage(), ugcId);
        }

        return newInfos;
    }

    // 只改人数,只能gm指令用
    public List<UgcMapLabelScoreInfo> updateRedisMapLabelScorePlayerCount(long ugcId,
            List<UgcMapLabelScoreInfo> addInfos) {
        LOGGER.debug("updateRedisMapLabelScorePlayerCount mapId({})", ugcId);
        List<UgcMapLabelScoreInfo> newInfos = new ArrayList<>();
        CoRedisCmd<String, String> coRedisCmd = Cache.getCoRedisCmdForString();
        String redisKey = CacheUtil.UgcMapLabelScore.getKey(ugcId);
        try {
            for (UgcMapLabelScoreInfo info : addInfos) {
                UgcMapLabelScoreInfo.Builder newInfo = info.toBuilder();
                String field = String.valueOf(info.getLabel());
                // 查询分数
                String score = coRedisCmd.hget(redisKey, "SCORE_" + field);
                // 修改人数
                coRedisCmd.hset(redisKey, "COUNT_" + field, String.valueOf(info.getCount()));
                newInfo.setScore(score == null ? 0 : Long.parseLong(score)).setCount(info.getCount());
                newInfos.add(newInfo.build());
            }
        } catch (Exception e) {
            LOGGER.error("updateRedisMapLabelScorePlayerCount err:{} ugcId:{}", e.getMessage(), ugcId);
            NKErrorCode.UgcMapLabelScoreUpdateFail.throwError("updateRedisMapLabelScorePlayerCount err:{} ugcId:{}",
                    e.getMessage(), ugcId);
        }

        return newInfos;
    }

    private static class InstanceHolder {

        private static final OperateDao INSTANCE = new OperateDao();
    }
}

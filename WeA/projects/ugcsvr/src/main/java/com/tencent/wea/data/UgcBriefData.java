package com.tencent.wea.data;

import com.tencent.hotresourceloader.hotresclass.HotResWhiteListByUgcIdCfg;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.UGCEditorMapTemplate;
import com.tencent.ugc.BaseDBData;
import com.tencent.ugc.CommonUtil;
import com.tencent.wea.data.MapData.CreateData;
import com.tencent.wea.data.MapData.SaveData;
import com.tencent.wea.data.UgcBriefData.Key;
import com.tencent.wea.hotRes.HotResWhiteListType;
import com.tencent.wea.manager.UgcManager;
import com.tencent.wea.manager.UgcMapEvaluationManager.UgcMapEvaluationReportInfo;
import com.tencent.wea.manager.UgcPlayerMgr;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDbWrapper.UgcBrief;
import com.tencent.wea.xlsRes.ResUGCEditor.Item_UGCEditorMapTemplate;
import com.tencent.wea.xlsRes.keywords.UgcAchievementStatusType;
import com.tencent.wea.xlsRes.keywords.UgcMapEvaluationStatusType;
import jodd.util.StringUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import java.util.*;

import static com.tencent.wea.protocol.common.UgcInstanceType.forNumber;

public class UgcBriefData extends BaseDBData<Key> {

    private static final Logger LOGGER = LogManager.getLogger(UgcBriefData.class);

    public UgcBriefData() {
        dbData = new UgcBrief();
    }

    @Override
    public Key getKey() {
        Key key = new Key();
        key.creatorId = getRecord().getCreatorId();
        key.ugcId = getRecord().getUgcId();
        return key;
    }

    public UgcBrief getRecord() {
        return (UgcBrief) super.getRecord();
    }

    public TcaplusDb.UgcBrief.Builder getDBData() {
        return getRecord().getBuilder();
    }

    public long getCreatorId() {
        return this.getRecord().getCreatorId();
    }

    public void setCreatorId(long creatorId) {
        this.getRecord().setCreatorId(creatorId);
    }

    public long getUgcId() {
        return this.getRecord().getUgcId();
    }

    public void setUgcId(long ugcId) {
        this.getRecord().setUgcId(ugcId);
    }

    public String getName() {
        return this.getRecord().getName();
    }

    public void setName(String name) {
        this.getRecord().setName(name);
    }

    public int getTemplateId() {
        return this.getRecord().getTemplateId();
    }

    public void setTemplateId(int templateId) {
        this.getRecord().setTemplateId(templateId);
    }

    public int getCurLayerId() {
        return this.getRecord().getCurLayerId();
    }

    public void setCurLayerId(int layerId) {
        this.getRecord().setCurLayerId(layerId);
    }

    public int getSaveType() {
        return this.getRecord().getSaveType();
    }

    public void setSaveType(int saveType) {
        this.getRecord().setSaveType(saveType);
    }

    public int getSaveCount() {
        return this.getRecord().getSaveCount();
    }

    public void setSaveCount(int saveCount) {
        this.getRecord().setSaveCount(saveCount);
    }

    public long getCreateTime() {
        return this.getRecord().getCreateTime();
    }

    public void setCreateTime(long createTime) {
        this.getRecord().setCreateTime(createTime);
    }

    public long getExpireTime() {
        return this.getRecord().getExpireTime();
    }

    public void setExpireTime(long expireTime) {
        this.getRecord().setExpireTime(expireTime);
    }

    public UgcMdList getMdList() {
        return this.getRecord().getMdList();
    }

    public void setMdList(UgcMdList mdList) {
        this.getRecord().clearMdList();
        this.getRecord().getMdListBuilder().mergeFrom(mdList);
    }

    public void setAchievementMap(UgcAchievementMap achievementMap) {
        // 更新数据
        this.getRecord().clearUgcAchievement();
        this.getRecord().getUgcAchievementBuilder().mergeFrom(achievementMap);
        // 更新版本号
        this.getRecord().setAchievementVerId(this.getAchievementVerId() + 1);
    }

    public void setExtraConfigIndexMap(UgcMapExtraConfigIndexMap extraConfigIndexMap) {
        // 更新数据
        this.getRecord().clearExtraConfigIndexMap();
        this.getRecord().getExtraConfigIndexMapBuilder().mergeFrom(extraConfigIndexMap);
        // 更新版本号
        this.getRecord().setExtraConfigIndexVerId(this.getExtraConfigIndexVerId() + 1);
    }

    public void setMapLoading(MapLoadingInfo mapLoadingInfo) {
        this.getRecord().clearMapLoading();
        this.getRecord().getMapLoadingBuilder().mergeFrom(mapLoadingInfo);
    }

    public void setLobbyCover(MapLobbyCoverInfo lobbyCoverInfo) {
        this.getRecord().clearLobbyCover();
        this.getRecord().getLobbyCoverBuilder().mergeFrom(lobbyCoverInfo);
    }

    public long getOldUgcId() {
        return this.getRecord().getOldUgcId();
    }

    public void setOldUgcId(long oldUgcId) {
        this.getRecord().setOldUgcId(oldUgcId);
    }

    public String getDesc() {
        return this.getRecord().getDesc();
    }

    public void setDesc(String desc) {
        this.getRecord().setDesc(desc);
    }

    public long getPublishTime() {
        return this.getRecord().getPublishTime();
    }

    public void setPublishTime(long publishTime) {
        this.getRecord().setPublishTime(publishTime);
    }

    public String getTags() {
        return this.getRecord().getTags();
    }

    public void setTags(String tags) {
        this.getRecord().setTags(tags);
    }

    public int getReportStatus() {
        return this.getRecord().getReportStatus();
    }

    public void setReportStatus(int reportStatus) {
        this.getRecord().setReportStatus(reportStatus);
    }

    public long getRejectTime() {
        return this.getRecord().getRejectTime();
    }

    public void setRejectTime(long rejectTime) {
        this.getRecord().setRejectTime(rejectTime);
    }

    public int getEditorSec() {
        return this.getRecord().getEditorSec();
    }

    public void setEditorSec(int editorSec) {
        this.getRecord().setEditorSec(editorSec);
    }

    public int getDisableMultiTest() { 
        return this.getRecord().getDisableMultiTest(); 
    }

    public void setDisableMultiTest(int disableMultiTest) { 
      this.getRecord().setDisableMultiTest(disableMultiTest); 
    }


    public int getMapType() {
        return this.getRecord().getMapType();
    }

    public void setMapType(int mapType) {
        this.getRecord().setMapType(mapType);
    }

    public int getIsCollect() {
        return this.getRecord().getIsCollect();
    }

    public void setIsCollect(int isCollect) {
        this.getRecord().setIsCollect(isCollect);
    }

    public long getCollectTime() {
        return this.getRecord().getCollectTime();
    }

    public void setCollectTime(long collectTime) {
        this.getRecord().setCollectTime(collectTime);
    }

    public void setEvaluationStatus(UgcMapEvaluationStatusType evaluationStatus) {
        this.getRecord().setEvaluationStatus(evaluationStatus.getNumber());
    }

    public void setEvaluationStatusReport(UgcMapEvaluationStatusType evaluationStatus) {
        this.getRecord().setEvaluationStatusReport(evaluationStatus.getNumber());
    }

    public UgcExtraInfo getExtraInfo() {
        return this.getRecord().getExtraInfo();
    }

    public void setExtraInfo(UgcExtraInfo extraInfo) {
        this.getRecord().clearExtraInfo();
        this.getRecord().getExtraInfoBuilder().mergeFrom(extraInfo);
    }

    public String getBucket() {
        return this.getRecord().getBucket();
    }

    public void setBucket(String bucket) {
        this.getRecord().setBucket(bucket);
    }

    public UgcGroupIdList getUgcGroupIdList() {
        return this.getRecord().getUgcGroupIdList();
    }

    public void setUgcGroupIdList(UgcGroupIdList ugcGroupIdList) {
        this.getRecord().clearUgcGroupIdList();
        this.getRecord().getUgcGroupIdListBuilder().mergeFrom(ugcGroupIdList);
    }

    public List<Long> getAllResIdList() {
        return this.getRecord().getResIdList().getArrayLongList();
    }

    public void addAllResIdList(ArrayLong resIdList) {
        this.getRecord().clearResIdList();
        this.getRecord().getResIdListBuilder().mergeFrom(resIdList);
    }

    public List<Long> getBanInfoList() {
        return this.getRecord().getBanResIdList().getArrayLongList();
    }


    public String getUgcVersion() {
        return this.getRecord().getUgcVersion();
    }

    public boolean getIsAllowMidJoin() {
        return this.getRecord().getPublishInfo().getIsAllowMidJoin();
    }

    public void setIsAllowMidJoin(boolean isAllowMidJoin) {
        this.getRecord().getPublishInfoBuilder().setIsAllowMidJoin(isAllowMidJoin);
    }

    public void setUgcVersion(String ugcVersion) {
        this.getRecord().setUgcVersion(ugcVersion);
    }

    public String getClientVersion() {
        return this.getRecord().getClientVersion();
    }

    public void setClientVersion(String clientVersion) {
        this.getRecord().setClientVersion(clientVersion);
    }

    public int getModelType() {
        return this.getRecord().getModelType();
    }

    public void setModelType(int modelType) {
        this.getRecord().setModelType(modelType);
    }

    public UgcMapMetaSaveInfo.Builder getSaveInfo() {
        return this.getRecord().getSaveInfoBuilder();
    }

    public void setSaveInfo(UgcMapMetaSaveInfo saveInfo) {
        this.getRecord().clearSaveInfo();
        this.getRecord().getSaveInfoBuilder().mergeFrom(saveInfo);
    }

    public UgcCosInfo.Builder getCover() {
        return this.getRecord().getCoverBuilder();
    }

    public void setCover(UgcCosInfo cosInfo) {
        this.getRecord().clearCover();
        this.getRecord().getCoverBuilder().mergeFrom(cosInfo);
    }

    public MapCoverVideoInfoSaved.Builder getCoverVideoBuilder() {
        return this.getRecord().getVideoInfoBuilder();
    }

    public void clearCoverVideoMgr() {
        getCoverVideoBuilder().clearCoverVideoMgr();
    }

    public void setCoverVideoMgr(MapCoverVideoInfo coverVideoInfo) {
        getCoverVideoBuilder().setCoverVideoMgr(coverVideoInfo);
    }

    public UgcMapMetaList getMetaList() {
        return this.getRecord().getMetaList();
    }

    public void setMetaList(UgcMapMetaList metaList) {
        this.getRecord().clearMetaList();
        this.getRecord().getMetaListBuilder().mergeFrom(metaList);
    }

    public String getDifficulty() {
        return this.getRecord().getDifficulty();
    }

    public void setDifficulty(String difficulty) {
        this.getRecord().setDifficulty(difficulty);
    }

    public UgcCommonMapInfo getCommMap() {
        return this.getRecord().getCommMap();
    }

    public void setCommMap(UgcCommonMapInfo commMap) {
        this.getRecord().getCommMapBuilder().mergeFrom(commMap);
    }

    public UgcLayerList getLayers() {
        return this.getRecord().getLayers();
    }

    public void setLayers(UgcLayerList layers) {
        this.getRecord().getLayersBuilder().mergeFrom(layers);
    }

    public UgcLayerList.Builder getLayerBuilder() {
        return this.getRecord().getLayersBuilder();
    }

    public long getUpdateTime() {
        return this.getRecord().getUpdateTime();
    }

    public void setUpdateTime(long updateTime) {
        this.getRecord().setUpdateTime(updateTime);
    }

    public long getSecFlags() {
        return this.getRecord().getSecFlags();
    }

    public void setSecFlags(long secFlags) {
        this.getRecord().setSecFlags(secFlags);
    }

    public void setCoCreateData(long creatorId, long ugcId) {
        UgcBrief data = this.getRecord();
        data.setCreatorId(creatorId);
        data.setUgcId(ugcId);
        data.setMapType(UgcInstanceType.CoCreateInstance_VALUE);
    }

    public int getResCategory() { return this.getRecord().getResCategory(); }

    public void setResCategory(int category) { this.getRecord().setResCategory(category); }

    public int getResSubCategory() { return this.getRecord().getResSubCategory(); }

    public void setResSubCategory(int subCategory) { this.getRecord().setResSubCategory(subCategory); }

    public String getResLabels() { return this.getRecord().getResLabels(); }

    public void setResLabels(String labels) { this.getRecord().setResLabels(labels); }

    public int getPublishGoodsStatus() { return this.getRecord().getPublishGoodsStatus(); }

    public void setPublishGoodsStatus(int status) { this.getRecord().setPublishGoodsStatus(status); }

    public void setHasPublishGoodsRecord(int status) { this.getRecord().setHasPublishGoodsRecord(status); }
    
    public int getBuyGoodsStatus() { return this.getRecord().getBuyGoodsStatus(); }

    public void setBuyGoodsStatus(int status) { this.getRecord().setBuyGoodsStatus(status); }

    public boolean getIsOpenSave() { return this.getRecord().getPublishInfo().getIsOpenSave(); }

    public void setIsOpenSave(boolean isOpen) { this.getRecord().getPublishInfoBuilder().setIsOpenSave(isOpen); }

    public int getIsResPubCosPath() { return this.getRecord().getIsResPubCosPath(); }

    public void setIsResPubCosPath(int isResPubCosPath) { this.getRecord().setIsResPubCosPath(isResPubCosPath); }

    public int getExtraConfigIndexVerId() {
        return this.getRecord().getExtraConfigIndexVerId();
    }

    public UgcMapExtraConfigIndexMap getExtraConfigIndexMap() {
        return this.getRecord().getExtraConfigIndexMap();
    }

    public int getAchievementVerId() {
        return this.getRecord().getAchievementVerId();
    }

    public UgcAchievementMap getAchievementMap() {
        return this.getRecord().getUgcAchievement();
    }

    public boolean getIsLuaCoding() {
        return this.getRecord().getPublishInfo().getIsLuaCoding();
    }

    public void setIsLuaCoding(boolean isLua) {
        this.getRecord().getPublishInfoBuilder().setIsLuaCoding(isLua);
    }

    public MapLoadingInfo getMapLoadingInfo() {
        return this.getRecord().getMapLoading();
    }

    public MapLobbyCoverInfo getMapLobbyCoverInfo() {
        return this.getRecord().getLobbyCover();
    }

    public Set<Integer> getAchievementIdPublish() {
        Set<Integer> result = new HashSet<>();
        for (Map.Entry<Integer, UgcAchievementInfo> entry : getAchievementMap().getAchCfgMapMap().entrySet()) {
            UgcAchievementInfo info = entry.getValue();
            if (info.getType() == UgcAchievementStatusType.UAST_Publish_VALUE) {
                result.add(entry.getKey());
            }
        }
        return result;
    }

    public void setUgcData(UgcData data) {
        this.setUgcId(data.getUgcId());
        this.setName(data.getName());
        this.setTemplateId(data.getTemplateId());
        this.setSaveType(data.getSaveType());
        this.setSaveCount(data.getSaveCount());
        this.setCreateTime(data.getCreateTime());
        this.setExpireTime(data.getExpireTime());
        this.setMdList(data.getMdList());
        this.setOldUgcId(data.getOldUgcId());
        this.setDesc(data.getDesc());
        this.setTags(data.getTags());
        this.setReportStatus(data.getReportStatus());
        this.setEditorSec(data.getEditorSec());
        this.setDisableMultiTest(data.getDisableMultiTest());
        this.setMapType(data.getMapType());
        this.setIsCollect(data.getIsCollect());
        this.setCollectTime(data.getCollectTime());
        this.setExtraInfo(data.getExtraInfo());
        this.setBucket(data.getBucket());
        this.setUgcGroupIdList(data.getUgcGroupIdList());
        this.setUgcVersion(data.getUgcVersion());
        this.setClientVersion(data.getClientVersion());
        this.setSaveInfo(data.getSaveInfo().build());
        this.setMetaList(data.getMetaList());
        this.setDifficulty(data.getDifficulty());
        this.setCommMap(data.getCommMap());
        this.setLayers(data.getLayers());
        this.setUpdateTime(data.getUpdateTime());
    }


    public UgcCoCreateData generateData(UgcEditorList.Builder editorList) {
        UgcCoCreateData data = new UgcCoCreateData();
        UgcBrief brief = this.getRecord();
        data.setUgcId(brief.getUgcId());
        data.setName(brief.getName());
        data.setTemplateId(brief.getTemplateId());
        data.setSaveType(brief.getSaveType());
        data.setSaveCount(brief.getSaveCount());
        data.setCreateTime(brief.getCreateTime());
        data.setExpireTime(brief.getExpireTime());
        data.setMdList(brief.getMdList());
        data.setOldUgcId(brief.getOldUgcId());
        data.setDesc(brief.getDesc());
        data.setTags(brief.getTags());
        data.setReportStatus(brief.getReportStatus());
        data.setEditorSec(brief.getEditorSec());
        data.setDisableMultiTest(brief.getDisableMultiTest());
        data.setMapType(brief.getMapType());
        data.setIsCollect(brief.getIsCollect());
        data.setCollectTime(brief.getCollectTime());
        data.setExtraInfo(brief.getExtraInfo());
        data.setBucket(brief.getBucket());
        data.setUgcGroupIdList(brief.getUgcGroupIdList());
        data.setUgcVersion(brief.getUgcVersion());
        data.setClientVersion(brief.getClientVersion());
        data.setSaveInfo(brief.getSaveInfo());
        data.setMetaList(brief.getMetaList());
        data.setDifficulty(brief.getDifficulty());
        data.setCommMap(brief.getCommMap());
        data.setLayers(brief.getLayers());
        data.setUpdateTime(brief.getUpdateTime());
        data.setMember(editorList);
        data.setCover(brief.getCover());
        data.setAchievementMap(brief.getUgcAchievement());
        data.setExtraConfigIndexMap(brief.getExtraConfigIndexMap());
        data.setIsLuaCoding(brief.getPublishInfo().getIsLuaCoding());
        data.setSource(brief.getPublishInfo().getSource());
        data.setMapLoading(brief.getMapLoading());
        data.setLobbyCover(brief.getLobbyCover());
        return data;
    }

    public void createInfoData(CreateData createData, UgcMdList pubList) {
        UgcBrief data = this.getRecord();
        data.setCreatorId(createData.creatorId);
        data.setUgcId(createData.ugcId);
        data.setName(createData.name);
        data.setDesc(createData.desc);
        data.setTemplateId(createData.templateId);
        data.setMapType(createData.instanceType.getNumber());
        data.setIsDelete(0);
        data.setSaveType(createData.saveType);
        data.setTags(createData.tags);
        data.setCreateTime(createData.createTime);
        data.setOldUgcId(createData.oldUgcId);
        data.setBucket(createData.bucket);
        data.setUgcVersion(createData.ugcVersion);
        data.setClientVersion(createData.clientVersion);
        data.setDifficulty(String.valueOf(createData.difficulty));
        data.getCommMapBuilder().setMapKey(createData.mapKey);
        data.getUgcGroupIdListBuilder().mergeFrom(createData.groupIds);
        data.setUpdateTime(createData.updateTime);
        data.setUgcResType(createData.ugcResparam.getResType().getNumber());
        data.setResCategory(createData.ugcResparam.getCategory());
        data.setResSubCategory(createData.ugcResparam.getSubCategory());
        data.setResLabels(StringUtil.join(createData.ugcResparam.getLabelsList(), ","));
        data.setPublishTime(createData.createTime);
        data.getPublishInfoBuilder().setIsOpenSave(createData.isOpenSave);
        data.getPublishInfoBuilder().setIsAllowMidJoin(createData.isAllowMidJoin);
        data.setIsResPubCosPath(createData.ugcResparam.getIsResPubCosPath()? 1:0);
        data.setModelType(createData.setData.getModelType().getNumber());
        data.getPublishInfoBuilder().setIsAiGen(createData.isAiGen);
        data.setCurLayerId(1);
        data.getPublishInfoBuilder().setIsLuaCoding(createData.isLuaCoding);
        data.getPublishInfoBuilder().setTraceStr(createData.traceStr);
        data.getPublishInfoBuilder().setSource(createData.instanceSource);
        data.setIsAdvert(createData.isAdvertMap?1:0);
        if (!createData.coverInfos.isEmpty()) {
            data.getCoverBuilder().addAllInfo(createData.coverInfos);
        }
        if (createData.editorEsc > 0) {
            data.setEditorSec(createData.editorEsc);
        }
        if (!createData.resIdList.isEmpty()) {
            data.getResIdListBuilder().addAllArrayLong(createData.resIdList);
        }
        if (createData.camps != null && !createData.camps.isEmpty()) {
            data.getPublishInfoBuilder().addAllCamps(createData.camps);
        }
        if (createData.blueTopics != null && !createData.blueTopics.isEmpty()) {
            data.getPublishInfoBuilder().addAllBlueTopics(createData.blueTopics);
        }
        if (createData.goldTopics != null && !createData.goldTopics.isEmpty()) {
            data.getPublishInfoBuilder().addAllGoldTopics(createData.goldTopics);
        }
        if (createData.extraInfo != null) {
            data.getExtraInfoBuilder().mergeFrom(createData.extraInfo);
        }
        if (pubList != null) {
            data.getMdListBuilder().addAllInfo(pubList.getInfoList());
        }
        for (UgcLayerInfo info : createData.layerInfos) {
            data.getLayersBuilder().putLayer(info.getLayerId(), info);
        }
        UgcEditorList.Builder editors = UgcEditorList.newBuilder();
        for (UgcEditorInfo info : createData.editorInfoList) {
            editors.addCreator(info);
        }
        data.getEditorsBuilder().mergeFrom(editors.build());
        // 组合还是维持和线上一样的类型
        if (createData.instanceType == UgcInstanceType.ResInstance &&
                createData.ugcResparam.getResType() == UgcResType.EURT_Group) {
            data.setMapType(UgcInstanceType.GroupInstance.getNumber());
        }
        data.getUgcAchievementBuilder().mergeFrom(createData.achievementMap);
        data.getExtraConfigIndexMapBuilder().mergeFrom(createData.extraConfigIndexMap);
        if (!CommonUtil.checkMapCanNotInitiateEvaluation(createData.templateId)) {
            data.setEvaluationStatus(UgcMapEvaluationStatusType.UMRST_SelfTest_VALUE);
            data.setEvaluationStatusReport(UgcMapEvaluationStatusType.UMRST_SelfTest_VALUE);
        }
        data.getMapLoadingBuilder().mergeFrom(createData.mapLoadingInfo);
        data.getLobbyCoverBuilder().mergeFrom(createData.lobbyCoverInfo);
    }

    public UgcMapEvaluationReportInfo convertEvaluationReportInfo() {
        UgcBrief data = this.getRecord();
        if (data.getMdList().getInfoList().isEmpty()
                && (Framework.currentTimeMillis() - data.getCreateTime()) > CommonUtil.ongDaySec) {
            return null;
        }

        return new UgcMapEvaluationReportInfo(data.getUgcId(), data.getCreatorId(), data.getTemplateId(),
                data.getName(), data.getUpdateTime(), data.getEvaluationStatus(), data.getEvaluationStatusReport());
    }

    public UgcItem.Builder convertUgcItem() {
        UgcBrief data = this.getRecord();
        if (data.getMdList().getInfoList().isEmpty()
                && (Framework.currentTimeMillis() - data.getCreateTime()) > CommonUtil.ongDaySec) {
            return null;
        }

        UgcItem.Builder item = UgcItem.newBuilder();
        item.setUgcId(data.getUgcId());
        item.setName(data.getName());
        item.setCreateTime(data.getCreateTime());
        item.setSaveType(data.getSaveType());
        item.setExpireTime(data.getExpireTime());
        item.setTemplateId(data.getTemplateId());
        item.setDesc(data.getDesc());
        item.setBucket(CommonUtil.getClientBucket(data.getBucket()));
        item.setRegion(CommonUtil.getRegion(data.getBucket()));
        item.setRejectTime(data.getRejectTime());
        item.setReportStatus(SafeStatus.forNumber(data.getReportStatus()));
        item.setOldUgcId(data.getOldUgcId());
        item.setIsCollect(data.getIsCollect() == 1);
        item.setCollectTime(data.getCollectTime());
        item.setTags(data.getTags());
        item.addAllMetaInfo(data.getMdList().getInfoList());
        item.setUgcVersion(data.getUgcVersion());
        item.setClientVersion(data.getClientVersion());
        item.setExtraInfo(data.getExtraInfo());
        item.setEditorSec(data.getEditorSec());
        item.setDisableMultiTest(data.getDisableMultiTest());
        item.addMetaMap(data.getSaveInfo().getHandSave());  //手动
        item.addMetaMap(data.getSaveInfo().getAutoSave());  //自动
        item.addAllMetaMap(data.getMetaList().getMetaList()); //更新
        item.setGroupIds(data.getUgcGroupIdList());
        item.setUpdateTime(data.getUpdateTime());
        item.setFullBucket(data.getBucket());
        item.setUgcResType(UgcResType.forNumber(data.getUgcResType()));
        item.setResCategory(data.getResCategory());
        item.setResSubCategory(data.getResSubCategory());
        item.setPublishTime(data.getPublishTime());
        item.setIsOpenSave(data.getPublishInfo().getIsOpenSave());
        item.setIsResPubCosPath(data.getIsResPubCosPath() == 1);
        item.addAllCovers(data.getCover().getInfoList());
        item.setIsAiGen(data.getPublishInfo().getIsAiGen());
        item.setHasPublishGoodsRecord(data.getHasPublishGoodsRecord() == 1);
        item.addAllAchievementIndexList(CommonUtil.convertUgcAchievementMap(data.getUgcAchievement()));
        item.addAllExtraConfigIndexList(CommonUtil.convertExtraConfigIndexMap(data.getExtraConfigIndexMap()));
        item.setIsLuaCoding(data.getPublishInfo().getIsLuaCoding());
        item.setSource(data.getPublishInfo().getSource());
        item.setDifficulty(data.getDifficulty());
        item.setMapLoading(data.getMapLoading());
        item.setLobbyCover(data.getLobbyCover());
        UgcMapAdvertStatus ugcMapAdvertStatus = UgcMapAdvertStatus.forNumber(data.getAdvertStatus());
        if (ugcMapAdvertStatus != null) {
            item.setAdvertStatus(ugcMapAdvertStatus);
        }
        if (item.getHasPublishGoodsRecord()) {
            MapData map = UgcManager.getInstance().getMap(data.getUgcId());
            if (map != null) {
                item.setIsApplyTakeOff(map.getIsApplyTakeOff() == 1);
            }
        }
        item.setCurLayerId(data.getCurLayerId());
        for (int topicId : data.getPublishInfo().getGoldTopicsList()) {
            item.addTopics(UgcMapTopic.newBuilder()
                    .setId(topicId)
                    .setType(1));
        }
        for (String topicName : data.getPublishInfo().getBlueTopicsList()) {
            item.addTopics(UgcMapTopic.newBuilder()
                    .setName(topicName)
                    .setType(0));
        }

        UgcInstanceType ugcInstanceType = forNumber(data.getMapType());
        if (ugcInstanceType != null) {
            item.setInstanceType(ugcInstanceType);
        }
        UgcMapPublishGoodsStatus ugcMapPublishGoodsStatus = UgcMapPublishGoodsStatus.forNumber(data.getPublishGoodsStatus());
        if (ugcMapPublishGoodsStatus != null) {
            item.setPublishGoodsStatus(ugcMapPublishGoodsStatus);
        }
        UgcMapBuyGoodsStatus ugcMapBuyGoodsStatus = UgcMapBuyGoodsStatus.forNumber(data.getBuyGoodsStatus());
        if (ugcMapBuyGoodsStatus != null) {
            item.setBuyGoodsStatus(ugcMapBuyGoodsStatus);
        }

        Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(data.getTemplateId());
        if (template != null) {
            item.setType(template.getType().getNumber());
        }

        item.addAllLayerList(data.getLayers().getLayerMap().values());
        if (!data.getEditors().getCreatorList().isEmpty()) {
            List<EditorItemInfo> editorInfoList = UgcPlayerMgr.getInstance()
                    .getEditorInfoList(data.getUgcId(), data.getEditors(), data.getLayers());
            item.addAllCreators(editorInfoList);
        }

        item.setSecFlags(data.getSecFlags());
        item.setLockState(data.getMgrInfo().getLockState());
        // 兼容M7线上组合数据 标签第一个变为大类型
        if (ugcInstanceType == UgcInstanceType.GroupInstance) {
            if (!data.getTags().isEmpty() && data.getResCategory() == 0) {
                String[] splitTags = data.getTags().split(",");
                if (splitTags.length > 0) {
                    item.setResCategory(Integer.parseInt(splitTags[0]));
                }
            }
        }

        if (!data.getResLabels().isEmpty()) {
            String[] labels = data.getResLabels().split(",");
            for (String label : labels) {
                try {
                    item.addResLabels(Integer.parseInt(label));
                } catch (NumberFormatException e) {
                    LOGGER.error("UgcBriefData err label, creatorId:{}, ugcId:{}, resType:{}, label:{}",
                            data.getCreatorId(), data.getUgcId(), data.getUgcResType(), data.getResLabels());
                }
            }
        }

        boolean isWhite = HotResWhiteListByUgcIdCfg.getInstance().checkIsInWhiteListOrTimeRange(data.getUgcId(), HotResWhiteListType.RWLT_UgcEditMap_VALUE);
        boolean isLockWhite = HotResWhiteListByUgcIdCfg.getInstance().checkIsInWhiteListOrTimeRange(data.getUgcId(), HotResWhiteListType.RWLT_UgcEditLockMap_VALUE);
        LOGGER.debug("UgcMapBriefWhite ugcId:{} isWhite:{}, isLockWhite:{}",data.getUgcId(),isWhite,isLockWhite);
        if (data.getMgrInfo().getLockState() != 0) {
            item.setIsWhiteMap(isLockWhite);
        }else{
            item.setIsWhiteMap(isWhite);
        }
        item.setEvaluationStatus(data.getEvaluationStatus());
        return item;
    }

    public void setData(long creatorId, UgcBaseBrief brief) {
        this.getRecord().setCreatorId(creatorId);
        this.getRecord().setUgcId(brief.getUgcId());
        this.getRecord().setName(brief.getName());
        this.getRecord().setTemplateId(brief.getTemplateId());
        this.getRecord().setSaveType(brief.getSaveType());
        this.getRecord().setSaveCount(brief.getSaveCount());
        this.getRecord().setCreateTime(brief.getCreateTime());
        this.getRecord().setExpireTime(brief.getExpireTime());
        this.getRecord().getMdListBuilder().mergeFrom(brief.getMdList());
        this.getRecord().setOldUgcId(brief.getOldUgcId());
        this.getRecord().setDesc(brief.getDesc());
        this.getRecord().setTags(brief.getTags());
        this.getRecord().setReportStatus(brief.getReportStatus());
        this.getRecord().setRejectTime(brief.getRejectTime());
        this.getRecord().setEditorSec(brief.getEditorSec());
        this.getRecord().setDisableMultiTest(brief.getDisableMultiTest());
        this.getRecord().setMapType(brief.getMapType());
        this.getRecord().setIsCollect(brief.getIsCollect());
        this.getRecord().setCollectTime(brief.getCollectTime());
        this.getRecord().getExtraInfoBuilder().mergeFrom(brief.getExtraInfo());
        this.getRecord().setBucket(brief.getBucket());
        this.getRecord().getUgcGroupIdListBuilder().mergeFrom(brief.getUgcGroupIdList());
        this.getRecord().setUgcVersion(brief.getUgcVersion());
        this.getRecord().setClientVersion(brief.getClientVersion());
        this.getRecord().getSaveInfoBuilder().mergeFrom(brief.getSaveInfo());
        this.getRecord().getMetaListBuilder().mergeFrom(brief.getMetaList());
        this.getRecord().setDifficulty(brief.getDifficulty());
        this.getRecord().getCommMapBuilder().mergeFrom(brief.getCommMap());
        this.getRecord().getLayersBuilder().mergeFrom(brief.getLayers());
        this.getRecord().getEditorsBuilder().mergeFrom(brief.getEditors());
        this.getRecord().setUpdateTime(brief.getUpdateTime());
    }

    public UgcBaseList.Builder getBaseBriefList(UgcInstanceType type) {
        return getBaseList(type);
    }

    private UgcBaseList.Builder getBaseList(UgcInstanceType type) {
        UgcBaseList.Builder baseList = null;
        switch (type.getNumber()) {
            case UgcInstanceType.CommonInstance_VALUE:
                baseList = getRecord().getMapListBuilder();
                break;
            case UgcInstanceType.GroupInstance_VALUE:
                baseList = getRecord().getGroupListBuilder();
                break;
            case UgcInstanceType.HomeInstance_VALUE:
                getRecord().clearHomeList();
                baseList = getRecord().getHomeListBuilder();
                break;
            default:
                LOGGER.error("ugcBaseBrief type not exist :{}", type.getNumber());
        }
        return baseList;
    }

    public TcaplusDb.UgcPublish.Builder convertPublish() {
        TcaplusDb.UgcPublish.Builder publish = TcaplusDb.UgcPublish.newBuilder();
        publish.setUgcId(this.getRecord().getUgcId());
        publish.setCreatorId(this.getRecord().getCreatorId());
        publish.setName(this.getRecord().getName());
        publish.setDesc(this.getRecord().getDesc());
        publish.setIsDelete(0);
        publish.setCreateTime(Framework.currentTimeMillis());
        publish.setMdList(this.getRecord().getMdList());
        publish.setBucket(this.getRecord().getBucket());
        publish.setOldUgcId(this.getRecord().getOldUgcId());
        publish.setTemplateId(this.getRecord().getTemplateId());
        publish.setTags(this.getRecord().getTags());
        publish.setUgcInstanceType(this.getRecord().getMapType());
        publish.setUgcGroupIdList(this.getRecord().getUgcGroupIdList());
        publish.setEditorSec(this.getRecord().getEditorSec());
        publish.setExtraInfo(this.getRecord().getExtraInfo());
        publish.setUgcVersion(this.getRecord().getUgcVersion());
        publish.setClientVersion(this.getRecord().getClientVersion());
        publish.setMetaList(this.getRecord().getMetaList());
        publish.setDifficulty(this.getRecord().getDifficulty());
        publish.setCommMap(this.getRecord().getCommMap());
        publish.setMgrInfo(this.getRecord().getMgrInfo());
        publish.setDescInfo(this.getRecord().getPublishInfo());
        publish.setLayers(this.getRecord().getLayers());
        publish.setEditors(this.getRecord().getEditors());
        publish.setUpdateTime(Framework.currentTimeMillis());
        publish.setUgcResType(this.getRecord().getUgcResType());
        publish.setResCategory(this.getRecord().getResCategory());
        publish.setResSubCategory(this.getRecord().getResSubCategory());
        publish.setResLabels(this.getRecord().getResLabels());
        publish.setPublishTime(Framework.currentTimeMillis());
        publish.setIsResPubCosPath(this.getRecord().getIsResPubCosPath());
        return publish;
    }




    public static class Key {

        public long creatorId = 0L;

        public long ugcId = 0L;

        public Key() {
        }

        public Key(long creatorId, long ugcId) {
            this.creatorId = creatorId;
            this.ugcId = ugcId;
        }

        @Override
        public int hashCode() {
            return Long.hashCode(this.creatorId);
        }

        @Override
        public boolean equals(Object object) {
            if (object instanceof Key) {
                UgcBriefData.Key other = (UgcBriefData.Key) object;
                return ugcId == other.ugcId && creatorId == other.creatorId;
            }
            return false;
        }

        public String toString() {
            return creatorId + "_" + ugcId;
        }
    }
}

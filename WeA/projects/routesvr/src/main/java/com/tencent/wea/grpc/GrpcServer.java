package com.tencent.wea.grpc;

import static com.tencent.wea.protocol.SsHead.RpcRelayMode.RRM_KeyHash_VALUE;
import static com.tencent.wea.protocol.SsHead.RpcRelayMode.RRM_MetaData_VALUE;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NonBlockingExecutor;
import com.tencent.region.RegionManager;
import com.tencent.rpc.RpcResponse;
import com.tencent.rpc.RpcRoutingUtil;
import com.tencent.rpc.RpcUtil;
import com.tencent.tbuspp.TbusppInstance;
import com.tencent.tbuspp.TbusppManager;
import com.tencent.tbuspp.TbusppMsgType;
import com.tencent.tbuspp.TbusppUtil;
import com.tencent.timiCoroutine.LocalService.DoNotDirectCall;
import com.tencent.timiutil.coroutine.CoroutineAsync;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.util.BlueGreenDeploymentUtil;
import com.tencent.wea.data.ConstData;
import com.tencent.wea.data.RpcMessage;
import com.tencent.wea.framework.RouteSvrEngine;
import com.tencent.wea.grpc.GrUgcsvr.UgcReply;
import com.tencent.wea.grpc.GrUgcsvr.UgcRequest;
import com.tencent.wea.protocol.SsHead;
import com.tencent.wea.protocol.SsHead.RpcRelayData;
import com.tencent.wea.protocol.SsHead.RpcResponseFlag;
import com.tencent.wea.protocol.SsHead.RpcRouting;
import io.grpc.Grpc;
import io.grpc.InsecureServerCredentials;
import io.grpc.Server;
import io.grpc.stub.StreamObserver;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import java.io.IOException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2023/06/18
 */
public class GrpcServer {

    public static final int timeOut = 30;
    private static final Logger LOGGER = LogManager.getLogger(GrpcServer.class);
    private static NonBlockingExecutor executor = new NonBlockingExecutor("grpc-client-async");
    private ThreadPoolExecutor thread;
    private Server server;

    private static void onFailed(RpcMessage message) {
        message.getHeader().setErrorCode(NKErrorCode.UgcGrpcClientError.getValue());

        RpcResponse response = new RpcResponse(SsHead.RpcHeader.newBuilder(message.getHeader().build()));
        response.setFlag(RpcResponseFlag.RRF_GRPC_REDIRECT_FAILED);

        ByteBuf buffer = response.serializeToByteBuf();
        RpcMessage msg = new RpcMessage(message.getServer(), buffer);
        RouteSvrEngine.getSpecInstance().getRelayService().offer(msg);

//        SsHead.RpcRouting routing = response.getHeader().getRouting();

//        UgcRequest.Builder request = UgcRequest.newBuilder();
//        request.setBuffer(ByteString.copyFrom(buffer.array()));
//        request.setSeqId(response.getHeader().getSeqId());
//        request.setAsyncId(response.getHeader().getAsyncId());
//        request.setMsgType(TbusppMsgType.WEA_RPC.getMsgType());
//        request.setSrcServiceName(routing.getSrcServiceName());
//        request.setSrcInstance(routing.getSrcInstance());
//        request.setDstServiceName(routing.getDstServiceName());
//        request.setLength(buffer.array().length);
//        request.setDstInstance(routing.getDstInstance());

//        int destId = TbusppUtil.getWorldId(routing.getSrcServiceName());
//        GrpcManager.getInstance().Dispatch(destId, request, null);
    }

    private static void sendData(UgcRequest request, RpcMessage rpc) {
        Monitor.getInstance().add.succ(MonitorId.attr_ugc_grpc_request_receive, 1);
        SsHead.RpcHeader.Builder header = rpc.getHeader();

        RpcRouting route = header.getRouting();
        RpcRelayData rpcRelayData = route.getRelayData();

        if (rpcRelayData.getRelayMode() == RRM_KeyHash_VALUE) {
            long hashKey = RpcRoutingUtil.getRoutingKey(header.getRouting());
            TbusppManager.getInstance().sendData(request.getDstServiceName(), request.getDstInstance(),
                    rpc.getBuffer(),
                    request.getSeqId(), request.getAsyncId(), String.valueOf(hashKey),
                    request.getMsgType());
        } else if (rpcRelayData.getRelayMode() == RRM_MetaData_VALUE) {
            long hashKey = RpcRoutingUtil.getRoutingKey(header.getRouting());
            String dstService = RpcRoutingUtil.getProxyServiceName();
            TbusppManager.getInstance().sendData(dstService, null, rpc.getBuffer(), header.getSeqId(),
                    header.getAsyncId(), String.valueOf(hashKey), TbusppMsgType.WEA_RPC.getMsgType());
        } else {
            TbusppManager.getInstance()
                    .sendData(route.getDstServiceName(), "", rpc.getBuffer(), header.getSeqId(),
                            header.getAsyncId(), "", TbusppMsgType.WEA_RPC.getMsgType()); //轮询路由
        }
    }

    public int init() {
        int port = PropertyFileReader.getIntItem("grpc_server_port", 9000);

        if (ServerEngine.getInstance().isDev()) {
            port += Framework.getInstance().getInstanceId();
            LOGGER.info("grpc_server_port {}", port);
        }

        thread = new ThreadPoolExecutor(
                1,
                1,
                0, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("grpc-ugcsvr-thread-%d").build());

        int finalPort = port;
        thread.execute(() -> {
            try {

                int keepAliveTime = PropertyFileReader.getIntItem("grpc_keep_alive_time", 20);
                int keepAliveTimeout = PropertyFileReader.getIntItem("grpc_keep_alive_timeout", 2);

                server = Grpc.newServerBuilderForPort(finalPort, InsecureServerCredentials.create())
                        .keepAliveTime(keepAliveTime, TimeUnit.SECONDS)
                        .keepAliveTimeout(keepAliveTimeout, TimeUnit.SECONDS)
                        .maxInboundMessageSize(32 * 1024 * 1024)
                        .addService(new UgcServiceImpl())
                        .build()
                        .start();
                LOGGER.info("GrpcServer init success port {}", finalPort);
            } catch (IOException e) {
                LOGGER.error("GrpcServer IOException ", e);
            }

            try {
                server.awaitTermination();
            } catch (InterruptedException e) {
                LOGGER.error("GrpcServer InterruptedException ", e);
            }
        });

        return 0;
    }

    public void stop() {
        try {
            if (!server.shutdown().awaitTermination(timeOut, TimeUnit.SECONDS)) {
                LOGGER.error("GrpcServer stop error");
            } else {
                LOGGER.info("GrpcServer stop success");
            }
        } catch (Exception e) {
            LOGGER.error("GrpcServer stop error {}", e.getMessage());
        }
    }

    public static class GrpcMessage {

        public UgcRequest request;
        public ByteBuf byteBuf;
        public StreamObserver<UgcReply> responseObserver;
        public long recvTime = 0;
        public long asyncId = 0;
    }

    public static class RpcFiberAsync extends CoroutineAsync<RpcMessage, Exception> {

        public RpcFiberAsync(GrpcMessage message) {
            super(null);
            RpcFiberAsync async = this;
            long asyncId = getAsyncId();
            setRunnable(() -> {
                executor.execute(() -> {
                    try {

                        String remote = message.request.getSrcServiceName() + "." + message.request.getSrcInstance();

                        ByteBuf byteBuf = Unpooled.wrappedBuffer(message.request.getBuffer().toByteArray());
                        int src = TbusppUtil.join(TbusppUtil.splitServiceName(remote));
                        RpcMessage rpc = new RpcMessage(src, byteBuf, message.request.getLength());
                        rpc.parseHeader();
                        rpc.setRegionAsyncId(asyncId);

                        TbusppManager.getInstance().sendDataToDestInstance(message.request.getDstServiceName(),
                                message.request.getDstInstance(), rpc.getBuffer(),
                                message.request.getSeqId(),
                                asyncId, message.request.getMsgType());
                    } catch (Exception e) {
                        LOGGER.error("RpcFiberAsync failed", e);
                        async.asyncFailed(e);
                    }
                });
            });

            message.asyncId = getAsyncId();
        }

        public void onResponse(RpcMessage response) {
            completed(response);
        }
    }

    static class UgcServiceImpl extends UgcServiceGrpc.UgcServiceImplBase {

        @Override
        public StreamObserver<GrUgcsvr.UgcRequest> requestStreamMsg(StreamObserver<UgcReply> responseObserver) {
            return new StreamObserver<UgcRequest>() {

                @Override
                public void onNext(UgcRequest request) {
                    onMessage(request);
                }

                @Override
                public void onError(Throwable t) {
                    LOGGER.error("region requestStreamMsg Exception ", t);
                }

                @Override
                public void onCompleted() {
                    UgcReply.Builder builder = UgcReply.newBuilder();
                    builder.setDstInstance(Framework.getInstance().getWorldId() + "-" + Framework.getInstance()
                            .getInstanceId());
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                }
            };
        }

        @Override
        public void requestMsg(UgcRequest request, StreamObserver<UgcReply> responseObserver) {
            try {
                onMessage(request);
                responseObserver.onNext(UgcReply.newBuilder().build());
                responseObserver.onCompleted();
            } catch (Exception e) {
                LOGGER.error("region requestMsg Exception ", e);
            }
        }

        private void onMessage(UgcRequest request) {

            try {
                String remote = request.getSrcServiceName() + "." + request.getSrcInstance();

                ByteBuf byteBuf = Unpooled.wrappedBuffer(request.getBuffer().toByteArray());
                int src = TbusppUtil.join(TbusppUtil.splitServiceName(remote));
                RpcMessage rpc = new RpcMessage(src, byteBuf, request.getLength());
                rpc.parseHeader();

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("region {} srcServiceName {} asyncId {} destServiceName {}",
                            rpc.isRequestNew() ? "request" : "response", request.getSrcServiceName(),
                            request.getAsyncId(),
                            request.getDstServiceName());
                }

                boolean isReq = true;
                if (ConstData.REQ_TYPE == 1) {
                    isReq = rpc.isRequest();
                } else {
                    isReq = rpc.isRequestNew();
                    if (rpc.getHeader().getReqType() == 0) {
                        LOGGER.error(
                                "GrpcServer reqType error isRequest={} srcServiceName {} asyncId {} destServiceName {}",
                                rpc.getHeader().getReqType(), request.getSrcServiceName(), request.getAsyncId(),
                                request.getDstServiceName());
                    }
                }

                if (isReq) {

                    if (ConstData.CheckOnlineInstanceFlag == 1) {
                        // check available instance
                        int rcpRoutingDstTypeInt = rpc.getHeader().getRouting().getDstTypeInt();
                        if (0 == rcpRoutingDstTypeInt) {
                            // 兼容性
                            rcpRoutingDstTypeInt = rpc.getHeader().getRouting().getDstType().getNumber();
                        }
                        boolean onlineInstanceByServerType = TbusppInstance.isOnlineInstanceByServerType(
                                rcpRoutingDstTypeInt);
                        if (!onlineInstanceByServerType) {
                            LOGGER.error(
                                    "GrpcServer no available instance.srcServiceName {} asyncId {} destServiceName {}",
                                    request.getSrcServiceName(), request.getAsyncId(),
                                    request.getDstServiceName());
                            // onfail通知
                            onFailed(rpc);
                            return;
                        }
                    }
                    // 如果当前处于灰度状态中，使用协程执行
                    int worldId = Framework.getInstance().getWorldId();
                    //LOGGER.debug("test bluGreenFlag:{}, open:{} worldId:{} isRegion:{}", ConstData.BlueGreenFlag, BlueGreenDeploymentUtil.getInstance()
                    //.isBlueGreenDeploymentOpen(), worldId, RegionManager.getInstance().isRegion(worldId));
                    if (ConstData.BlueGreenFlag == 1 && BlueGreenDeploymentUtil.getInstance()
                            .isBlueGreenDeploymentOpen()
                            && !RegionManager.getInstance()
                            .isRegion(worldId)) {
                        DoNotDirectCall.Grpc.runJob(RouteSvrEngine.getSpecInstance().getGrpcService(),
                                0, () -> {
                                    //LOGGER.debug("test2 bluGreenFlag:{}, open:{} worldId:{} isRegion:{}", ConstData.BlueGreenFlag, BlueGreenDeploymentUtil.getInstance()
                                    //.isBlueGreenDeploymentOpen(), worldId, !RegionManager.getInstance().isRegion(worldId));
                                    SsHead.RpcHeader.Builder header = rpc.getHeader();
                                    BlueGreenDeploymentUtil.getInstance().cacheBlueGreenMarkFromRedis(header);
                                    sendData(request, rpc);
                                    return null;
                                }, "grpcserver", true);

                    } else {
                        sendData(request, rpc);
                    }
                } else {
                    Monitor.getInstance().add.succ(MonitorId.attr_ugc_grpc_request_response, 1);
                    TbusppManager.getInstance()
                            .sendDataToDestInstance(request.getSrcServiceName(), request.getSrcInstance(),
                                    rpc.getBuffer(),
                                    request.getSeqId(), request.getAsyncId(), request.getMsgType());
                }

            } catch (Exception e) {
                LOGGER.error("region requestMsg Exception ", e);
            }
        }
    }


}

//generated by tool

package com.tencent.wea.rpc.idiphandler;

import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.resourceloader.resclass.ActivityFunParaConfig;
import com.tencent.wea.attr.WishActivityData;
import com.tencent.wea.protocol.idip.*;
import com.tencent.wea.service.implement.WishActivity;
import com.tencent.wea.service.logic.generalmsg.ActivityWishGeneralMsgImpl;
import com.tencent.wea.service.manager.PlayerActivityManager;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.wea.xlsRes.ResActivityFunPara.ActivityFunParaType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class QueryWishActivityHelpInfoReqHandler implements IdipMsgHandler {

    public static final Logger LOGGER = LogManager.getLogger(QueryWishActivityHelpInfoReqHandler.class);

    @Override
    public IdipResponse.Builder handle(IdipRequest.Builder request) {
        QueryWishActivityHelpInfoReq.Builder req = request.getQueryWishActivityHelpInfoReqBuilder();
        QueryWishActivityHelpInfoRsp.Builder res = QueryWishActivityHelpInfoRsp.newBuilder();

        PlayerActivity player = PlayerActivityManager.getInstance().findPlayerActivity(req.getUid());
        if (player == null) {
            // 离线情况要判断ActivityInteractionTable表数据，因此回到idipsvr复用idip上的方法
            throw new NKRuntimeException(NKErrorCode.UserIsOffline);
        }

        NKErrorCode retCode;
        try {
            retCode = onlineHandle(player, req, res);
            if (retCode.hasError()) {
                LOGGER.error("onlineHandle error, openid:{}, uid:{}, e:{}", req.getOpenId(), req.getUid(), retCode);
            }
        } catch (NKRuntimeException ex) {
            LOGGER.error("runtime error, openid:{}, uid:{}, e:{}", req.getOpenId(), req.getUid(), ex);
            retCode = ex.getEnumErrCode();
        } catch (Exception ex) {
            LOGGER.error("unexpected error, openid:{}, uid:{}, e:{}", req.getOpenId(), req.getUid(), ex);
            retCode = NKErrorCode.UnknownError;
        }

        if (retCode.isOk()) {
            res.setResult(0);
            res.setRetMsg("success");
        } else {
            res.setResult(retCode.getValue());
            res.setRetMsg(retCode.name());
        }

        return IdipResponse.newBuilder().setQueryWishActivityHelpInfoRsp(res);
    }

    private static NKErrorCode onlineHandle(PlayerActivity player, QueryWishActivityHelpInfoReq.Builder req,
            QueryWishActivityHelpInfoRsp.Builder res) {
        long uid = req.getUid();
        long inviterUid = req.getInviterUid();
        int activityId = req.getActivityId();
        if (uid <= 0 || activityId <= 0 || inviterUid <= 0) {
            return NKErrorCode.InvalidParams;
        }

        WishActivity activity = ActivityWishGeneralMsgImpl.getWishActivity(player, activityId);
        if (null == activity) {
            return NKErrorCode.PlayerActivityDataNotFound;
        }

        WishActivityData data = activity.getWishData();
        int curCnt = data.getTodayHelpCnt();
        int maxCnt = ActivityFunParaConfig.getInstance().getIntValue(ActivityFunParaType.AFPT_MAX_HELP_CNT);
        boolean hasHelpTargetUid = activity.isHaveHelpUid(inviterUid);

        res.setMaxDailyHelpCnt(maxCnt);
        res.setTodayLeftHelpCnt(curCnt < maxCnt ? maxCnt - curCnt : 0);
        res.setHasHelpTarget(hasHelpTargetUid ? 1 : 0);

        return NKErrorCode.OK;
    }
}
package com.tencent.wea.service.manager;

import com.google.common.collect.Lists;
import com.tencent.condition.event.player.common.TaskRewardCompensateEvent;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.RewardCompensateConfData;
import com.tencent.resourceloader.resclass.TaskGroupData;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.*;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.outputcontrol.OutputModuleCtx;

import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.service.object.PlayerActivity;
import com.tencent.wea.service.outputcontrol.ActsvrOutputMgr;
import com.tencent.wea.service.task.ActivityTask;
import com.tencent.wea.xlsRes.ResReward;
import com.tencent.wea.xlsRes.ResTask;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.RewardCompensateHandleType;
import com.tencent.wea.xlsRes.keywords.RewardCompensateType;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;


/**
 * <AUTHOR>
 * @date 2024/07/08
 * @desc task奖励补偿管理器
 */

public class TaskRewardCompensateMgr {

    private static final Logger LOGGER = LogManager.getLogger(TaskRewardCompensateMgr.class);

    /**
     * 活动玩家
     */
    private final PlayerActivity player;

    public TaskRewardCompensateMgr(PlayerActivity player) {
        this.player = player;
    }

/**************************************-----主流程START-----************************************/
    public void init() {
        MapAttrObj<RewardCompensateType, RewardCompensateTaskStatus> rewardCompensateAttr = player.getAttr().getRewardCompensate().getTypeToTasks();

        // 将包含任务的领奖类型中的任务注册到任务监控列表
        for (ResReward.RewardCompensateConf conf : RewardCompensateConfData.getInstance().getArrayList()) {
            // 如果已经记录 不做任务的初始化
            if (rewardCompensateAttr.containsKey(conf.getType())) {
                //RewardCompensateTaskStatus status = rewardCompensateAttr.get(conf.getType());
                continue;
            }
            initByType(conf.getType());
        }
    }

    public void onLogin() {
        init();
    }

    public void onLoad() {
        init();
    }

    /**
     *  触发时机：任务完成后重置和删除前调用
     *  检查设置任务奖励补领信息
     * @param taskId
     */
    public void checkAndSetTaskReward(int taskId) {
        // 是否属于待补领模块的任务
        if (!checkTaskInMonitor(taskId)) {
            return;
        }
        LOGGER.debug("player:{} task:{} in monitor", player.getUid(), taskId);

        RewardCompensateTaskStatus status = getTaskStatus(taskId);
        if (status == null) {
            LOGGER.error("player:{} taskId:{} not found RewardCompensateTaskStatus", player.getUid(), taskId);
            return;
        }

        //记录 任务 可领取奖励
        recordTaskRewardNotGet(taskId, status);

        // 检测 当前RewardCompensateTaskStatus 是否完成
        if (status.getTasks().isEmpty()) {
            onCompleteRewardTaskStatus(status);
        }

    }


    /**************************************-----主流程END----************************************/



    private void initByType(RewardCompensateType type) {
        ResReward.RewardCompensateConf conf = RewardCompensateConfData.getInstance().get(type);
        if (conf == null) {
            return;
        }

        if (!conf.getEnable()) {
            return;
        }

        // 任务组类型处理
        if (conf.getHandleType() == RewardCompensateHandleType.RCHT_TASKGROUP) {
            long beginMs = conf.getTaskGroupInfo().getBeginTimeSec().getSeconds() * 1000;
            long endMs = conf.getTaskGroupInfo().getEndTimeSec().getSeconds() * 1000;
            // 过期
            if (endMs < DateUtils.currentTimeMillis()) {
                return;
            }
            RewardCompensateTaskStatus status = new RewardCompensateTaskStatus();
            for (int groupId : conf.getTaskGroupInfo().getGroupIdListList()) {
                ResTask.TaskGroup taskGroup = TaskGroupData.getInstance().getTaskGroup(groupId);
                if (taskGroup == null) {
                    LOGGER.debug("taskGroup:{} not found", groupId);
                    continue;
                }
                for (int taskId : taskGroup.getTaskIdListList()) {
                    status.addTasks(taskId);
                    player.getAttr().getRewardCompensate().getTaskToType().put(
                            taskId,
                            new RewardCompensateTask()
                                    .setId(taskId)
                                    .setType(conf.getType())
                    );
                }
            }
            status.setType(conf.getType());
            status.setDeleteMs(endMs);
            player.getAttr().getRewardCompensate().putTypeToTasks(conf.getType(), status);
            LOGGER.info("player:{} type:{} set tasks succ", player.getUid(), conf.getType());
            return;
        }

        LOGGER.debug("player:{} type:{} is not related with task", player.getUid(), conf.getType());
    }

    /**
     * 记录 任务id 未领取奖励
     * @param taskId
     */
    private void recordTaskRewardNotGet(int taskId, RewardCompensateTaskStatus status) {
        // task check
        ActivityTask task = player.getTaskManager().getTask(taskId);
        if (task == null) {
            return ;
        }

        // 删除为空的任务
        for (int curId : status.getTasks().getValues()) {
            if (player.getTaskManager().getTask(curId) == null) {
                status.getTasks().remove(curId);
                status.getHtasks().add(curId);
                LOGGER.debug("player:{} null task:{} move into htasks", player.getUid(), curId);
            }
        }


        // attr-RewardCompensate check
        if (!status.getTasks().contains(taskId)) {
            return;
        }

        // 移动
        if (status.getTasks().contains(taskId)) {
            status.getTasks().remove(taskId);
            status.getHtasks().add(taskId);
            LOGGER.debug("player:{} move task:{} into htasks", player.getUid(), taskId);
        }


        if (task.getStatus() != TaskStatus.TS_Completed ) {
            return;
        }

        // task 奖励领取 (ps: 这个并没有真正领取, receiveReward内部会更新认为状态为已领取，返回奖励itemlist)
        List<ItemInfo> rewardList = task.receiveReward();

        // 通用产出系统检查
//        OutputModuleCtx outputCtx = null;
//        if (PropertyFileReader.getRealTimeBooleanItem("open_reward_compensate_output_control", true)) {
//            outputCtx = ActsvrOutputMgr.makePlayerTaskCtx(player, taskId, 1, task.getLastCompleteTime() / 1000);
//            if (!outputCtx.canOutput()) {
//                LOGGER.error("player:{} task:{} receiveReward error: output control check fail", player.getUid(), taskId);
//                return;
//            }
//        }
        if (rewardList != null && !rewardList.isEmpty()) {
            status.getRewardTasks().add(taskId);
        }

        for (ItemInfo item : rewardList) {
            int itemId = item.getItemId();
            long num = item.getItemNum();
            RewardItemInfo itemInfo = status.getRewards().get((long) itemId);
            if (itemInfo == null) {
                itemInfo = new RewardItemInfo();
                itemInfo.setId(itemId);
            }
            itemInfo.addNum(num);
            itemInfo.setExpireTimeMs(item.getExpireTimeMs());
            itemInfo.setExpireType(item.getExpireType());
            status.getRewards().put((long) itemId, itemInfo);
        }

//        if (outputCtx != null) {
//            outputCtx.recordOutput();   // 产出系统计数
//        }
        LOGGER.debug("player:{} type:{} task:{} rewardItems:{} record to rewardCompensateInfo succ", player.getUid(), status.getType(), taskId, rewardList);
    }

    /**
     * 任务是否在监控列表中
     * @return
     */
    private boolean checkTaskInMonitor(int taskId) {
        return player.getAttr().getRewardCompensate().getTaskToType().containsKey(taskId);
    }



    /**
     *  获取任务对应的 RewardCompensateTaskStatus
     * @return RewardCompensateTaskStatus
     */
    private RewardCompensateTaskStatus getTaskStatus(int taskId) {
        RewardCompensateInfo rewardCompensateAttr = player.getAttr().getRewardCompensate();
        RewardCompensateType rewardCompensateType = rewardCompensateAttr.getTaskToType().get(taskId).getType();
        if (rewardCompensateType == null) {
            LOGGER.error("player:{} task:{} not found type in task2type", player.getUid(), taskId);
            return null;
        }
        RewardCompensateTaskStatus status = rewardCompensateAttr.getTypeToTasks(rewardCompensateType);
        if (status == null) {
            LOGGER.error("player:{} taskId:{} type:{} not found status in typeToTasks", player.getUid(), taskId, rewardCompensateType);
            return null;
        }
        return status;
    }


    /**
     * 某个奖励类型RewardTaskStatus完成 （多个组内task全部完成）
     */
    private void onCompleteRewardTaskStatus(RewardCompensateTaskStatus status) {
        RewardCompensateType rewardCompensateType = status.getType();
        // 邮件补发(ps: 这里会有个问题, 如果发邮件失败，此次的补发实际就没有发，依然进入下个周期，目前暂时只打了错误日志，后续如果有强需求再处理)
        sendMail(rewardCompensateType);

        //  监控列表 移除 已完成status中所有任务
        for (int rmid : status.getHtasks().getValues()) {
            player.getAttr().getRewardCompensate().getTaskToType().remove(rmid);
        }

        //  删除 RewardCompensateTaskStatus
        player.getAttr().getRewardCompensate().removeTypeToTasks(rewardCompensateType);
        LOGGER.info("delete player:{} type:{} in RewardCompensateAttr", player.getUid(), rewardCompensateType);

        //发送任务奖励补偿事件
        List<Integer> compensateTasks = Lists.newArrayList(status.getRewardTasks().getValues());
        if (!compensateTasks.isEmpty()) {
            new TaskRewardCompensateEvent(player.getConditionManager())
                    .setTaskIds(compensateTasks)
                    .dispatch(player);
            status.getRewardTasks().clear();
        }

        // 重置任务
        if (status.getDeleteMs() > DateUtils.currentTimeMillis()) {
            LOGGER.info("reload player:{} type:{} RewardCompensateAttr", player.getUid(), rewardCompensateType);
            initByType(rewardCompensateType);
        }
    }


    /**
     * 补发奖励信息
     */
    private void sendMail(RewardCompensateType type) {
        RewardCompensateTaskStatus status = player.getAttr().getRewardCompensate().getTypeToTasks(type);
        if (status == null) {
            return;
        }

        if (status.getRewards().size() == 0) {
            return;
        }


        MailAttachmentList.Builder attachment = MailAttachmentList.newBuilder();
        for (RewardItemInfo reward : status.getRewards().values()) {
            attachment.addList(
                    MailAttachment.newBuilder().setItemIfo(
                            ItemInfo.newBuilder()
                                    .setItemId((int) reward.getId())
                                    .setItemNum(reward.getNum())
                                    .setExpireTimeMs(reward.getExpireTimeMs())
                                    .setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE)
                                    .build()
                    )
            );
        }

        // 清空reward
        LOGGER.debug("player:{} RewardCompensateType:{} rewardlist:{}, will start sending compensation mail", player.getUid(), status.getRewards().values(), type);
        status.getRewards().clear();

        try {
            CurrentExecutorUtil.runJob(() -> {
                int templateId = 3;
                ResReward.RewardCompensateConf conf = RewardCompensateConfData.getInstance().get(type);
                if (conf != null) {
                    templateId = conf.getMailTemplateId();
                }

                long mailId = MailInteraction.sendTemplateMail(
                        player.getUid(), templateId, attachment, MailInteraction.TlogSendReason.rewardCompensate
                );
                if (mailId < 0) {
                    LOGGER.error("player:{} RewardCompensateType:{} rewardlist:{} SendMail Failed", player.getUid(), type, status.getRewards());
                    return null;
                }
                LOGGER.debug("player:{} RewardCompensateType:{} rewardlist:{} send mail success, mailId:{}", player.getUid(), status.getRewards().values(), type, mailId);
                return null;

            }, "rewardCompensateSendMail", false);
        } catch (Exception e) {
            LOGGER.error("SendMail Job Error", e);
        }
    }





}

package com.tencent.wea.service.object;

import com.google.protobuf.Message.Builder;
import com.tencent.wea.protocol.common.EventBatchSendData;
import java.util.function.Function;


/**
 * <AUTHOR>
 * @date 2024/05/24
 * @desc 活动rcp消息队列任务对象
 */

public class ActivityRpcMsgQueueTaskObject<T extends com.google.protobuf.GeneratedMessageV3> {
    public long uid; // uid
    public T reqBulider;
    public CallbackFunctionByRpc<T> callbakFunByRpc;// Rpc回调函数

    @FunctionalInterface
    public interface CallbackFunctionByRpc<T> {
        void accept(T t);
    }

    public void initRpcMsg(long uid, T reqBulider, CallbackFunctionByRpc<T> callbakFun){
        this.uid = uid;
        this.callbakFunByRpc = callbakFun;
        this.reqBulider = reqBulider;
    }
}
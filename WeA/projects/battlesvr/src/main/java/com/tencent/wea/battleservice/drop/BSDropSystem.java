package com.tencent.wea.battleservice.drop;

import com.tencent.drop.BaseDropSystem;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.g6.irpc.proto.ds_player.DsPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.common.BatchDropInfo;
import com.tencent.wea.protocol.common.DropCalcInfo;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.DropItem;

import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.BatchDropItemRequest;
import com.tencent.wea.g6.irpc.proto.ds_roomsvr.DsRoomsvr.BatchDropItemReply;


import java.util.List;


/**
 * <AUTHOR>
 *
 * BattleServer上的掉落系统
 */
public class BSDropSystem extends BaseDropSystem {
    private static final Logger LOGGER = LogManager.getLogger(BSDropSystem.class);

    private BSDropSystem() {
        super();
    }

    private static class InstanceHolder {
        private static final BSDropSystem instance = new BSDropSystem();
    }

    public static BSDropSystem getInstance() {
        return InstanceHolder.instance;
    }

    @Override
    protected void init() {
        super.init();
        LOGGER.info("GSDropSystem initialized");
    }

    public BatchDropItemReply.Builder batchDropItems(BatchDropItemRequest req) {
        BatchDropItemReply.Builder replyBuilder = BatchDropItemReply.newBuilder()
                .setResult(0);

        try {
            for (BatchDropInfo dropInfoReq : req.getDropInfosList()) {
                BatchDropInfo.Builder dropInfoResp = BatchDropInfo.newBuilder()
                        .setType(dropInfoReq.getType()).setEntityId(dropInfoReq.getEntityId());

                for (DropCalcInfo dropCalcReq : dropInfoReq.getDropIdsList()) {
                    DropCalcInfo.Builder dropCalcResp = DropCalcInfo.newBuilder()
                            .setDropId(dropCalcReq.getDropId());

                    List<ItemInfo> itemInfos = this.batchDrop(dropCalcReq.getDropId(), 1);

                    for (ItemInfo itemInfo : itemInfos) {
                        DropItem.Builder dropItem = DropItem.newBuilder()
                                .setItemId(itemInfo.getItemId())
                                .setCount(itemInfo.getItemNum());
                        dropCalcResp.addItems(dropItem);
                    }
                    dropInfoResp.addDropIds(dropCalcResp);
                }

                replyBuilder.addDropInfos(dropInfoResp);
            }

            replyBuilder.setResult(NKErrorCode.OK.getValue());
        } catch (Exception e) {
            LOGGER.error("BSDropSystem.batchDropItems error", e);
            replyBuilder.setResult(NKErrorCode.UnknownError.getValue());
        }

        return replyBuilder;
    }

}
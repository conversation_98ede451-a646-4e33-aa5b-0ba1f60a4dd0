package com.tencent.wea.battleservice.battledata.settlement.mmrscore;

import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.MatchMmrChestDarkStarSettlementData;
import com.tencent.resourceloader.resclass.MatchMmrChestXingbaoSettlementData;
import com.tencent.wea.battleservice.battledata.settlement.mmrscore.dataprovider.BattleResultDataProvider;
import com.tencent.wea.battleservice.battledata.settlement.mmrscore.dataprovider.LGBattleResultDataProvider;
import com.tencent.wea.xlsRes.ResMatchMMR;
import com.tencent.wea.xlsRes.keywords.BattleEventType;
import com.tencent.wea.xlsRes.keywords.ChestActorIdType;
import com.tencent.wea.xlsRes.keywords.CommonCompareType;
import com.tencent.wea.xlsRes.keywords.MMRScoreSettlementType;
import com.tencent.wea.xlsRes.keywords.MMRScoreType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * chest玩法的Mmr积分计算器
 */
@MMRCalculatorType(type = MMRScoreSettlementType.MSST_ChestGame, dataProviderCls = LGBattleResultDataProvider.class)
public class MMRScoreChestCalculator extends MMRScoreCalculator {

    private static final Logger LOGGER = LogManager.getLogger(MMRScoreChestCalculator.class);

    public MMRScoreChestCalculator(BattleResultDataProvider inDataProvider) {
        super(inDataProvider);
    }

    @Override
    public @Nullable NKPair<MMRScoreType, Map<Long, Integer>> calculateMMRScoreChanges() {
        if (!canCalculateMmrChanges()) {
            return null;
        }

        MMRScoreType scoreType = dataProvider.getBattleSettlementMMRScoreType();
        NKPair<MMRScoreType, Map<Long, Integer>> ret = new NKPair<>(scoreType, new HashMap<>());
        Map<Long, Integer> retMap = ret.getValue();

        for (long uid : dataProvider.getAllUids()) {
            // 机器人不需要结算
            if (dataProvider.isRobot(uid)) {
                continue;
            }

            // 获取当前结算用的列表
            List<ResMatchMMR.MatchMmrChestSettlementScope> settlementDetailCfgList = getPlayerSettlementDetailCfgList(uid);
            if (settlementDetailCfgList.isEmpty()) {
                LOGGER.error("battleId {} uid {} settlementDetailCfgList is empty",
                        dataProvider.getDataIdForLog(), uid);

                continue;
            }

            int uidMmrScoreChanged = calculatePlayerMmrScore(uid, settlementDetailCfgList);
            if (uidMmrScoreChanged != 0) {
                retMap.put(uid, uidMmrScoreChanged);
            }
        }

        return ret;
    }

    /**
     * 计算玩家Mmr积分变化
     * @param uid 玩家uid
     * @param settlementDetailCfgList 玩家对应mmr分数结算配置
     * @return 玩家Mmr积分变化值
     */
    private int calculatePlayerMmrScore(long uid, List<ResMatchMMR.MatchMmrChestSettlementScope> settlementDetailCfgList) {
        int scoreChangedRet = 0;
        // 默认关卡序号
        final int DEFAULT_LEVEL_IDX = 0;
        // 记录已经计算过的事件
        Set<Integer> calculatedEventTypes = new HashSet<>();

        for (ResMatchMMR.MatchMmrChestSettlementScope settlementDetailCfg : settlementDetailCfgList) {
            // 一种事件只会结算一次
            if (calculatedEventTypes.contains(settlementDetailCfg.getBattleEventType())) {
                continue;
            }

            BattleEventType battleEventType = BattleEventType.forNumber(settlementDetailCfg.getBattleEventType());
            if (null == battleEventType || battleEventType == BattleEventType.BET_Unknown) {
                LOGGER.error("battleId {} uid {} unknown battleEventType",
                        dataProvider.getDataIdForLog(), settlementDetailCfg.getBattleEventType());
                continue;
            }

            // 获取玩家在事件上的数值
            Optional<Integer> uidBattleEventVal = dataProvider.getUidLevelBattleEvent(uid, DEFAULT_LEVEL_IDX, battleEventType);
            if (uidBattleEventVal.isEmpty()) {
                LOGGER.error("battleId {} uid {} battleEventType {} uidBattleEventVal is empty",
                        dataProvider.getDataIdForLog(), uid, battleEventType);
                continue;
            }

            if (!isEventComparisonPass(uidBattleEventVal.get(), settlementDetailCfg.getEventCompareType(),
                    settlementDetailCfg.getEventCompareArgsList())) {
                continue;
            }

            int scoreChanged = settlementDetailCfg.getMmrScoreChange();
            scoreChangedRet += scoreChanged;
            // 记录已经计算过的事件
            calculatedEventTypes.add(settlementDetailCfg.getBattleEventType());

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("battleId {} uid {} battleEventType {} playerVal {} scoreChanged {}",
                        dataProvider.getDataIdForLog(), uid, battleEventType, uidBattleEventVal.get(), scoreChanged);
            }
        }

        return scoreChangedRet;
    }

    /**
     * 检查结算事件是否满足条目
     * @param uidBattleEventVal 玩家在事件上的数值
     * @param eventCompareType 事件比较类型
     * @param compareArgsList 比较参数列表
     * @return true 表示通过检查
     */
    private boolean isEventComparisonPass(int uidBattleEventVal, int eventCompareType, List<Integer> compareArgsList) {
        switch (eventCompareType) {
            // 等于
            case CommonCompareType.CMT_EQ_VALUE: {
                if (compareArgsList.isEmpty()) {
                    LOGGER.error("battle {} eventCompareType {} compareArgsList is empty",
                            dataProvider.getDataIdForLog(), eventCompareType);

                    return false;
                }

                return uidBattleEventVal == compareArgsList.get(0);
            }

            // 左闭右开区间范围内
            case CommonCompareType.CMT_BETWEEN_LEFT_INCLUSIVE_VALUE: {
                if (compareArgsList.size() < 2) {
                    LOGGER.error("battle {} eventCompareType {} compareArgsList size {} < 2",
                            dataProvider.getDataIdForLog(), eventCompareType, compareArgsList.size());
                    return false;
                }

                int inclusiveLeft = compareArgsList.get(0);
                int excludeRight = compareArgsList.get(1);
                return uidBattleEventVal >= inclusiveLeft && uidBattleEventVal < excludeRight;
            }

            // 没处理的
            default: {
                LOGGER.error("battle {} unknown eventCompareType {}",
                        dataProvider.getDataIdForLog(), eventCompareType);
                return false;
            }
        }
    }

    /**
     * 获取玩家对应结算信息列表
     * @param uid 玩家uid
     * @return 玩家结算信息列表
     */
    private List<ResMatchMMR.MatchMmrChestSettlementScope> getPlayerSettlementDetailCfgList(long uid) {
        // 获取当前Mmr
        Optional<Integer> curMmrScore = dataProvider.getUidMMRScore(uid);
        if (curMmrScore.isEmpty()) {
            LOGGER.error("battle {} uid {} curMmrScore is empty",
                    dataProvider.getDataIdForLog(), uid);
            return Collections.emptyList();
        }

        // 获取当前阵营id
        Optional<Integer> sideId = dataProvider.getUidSide(uid);
        if (sideId.isEmpty()) {
            LOGGER.error("battleId {} uid {} sideId is empty",
                    dataProvider.getDataIdForLog(), uid);
            return Collections.emptyList();
        }

        switch (sideId.get()) {
            case ChestActorIdType.CAIT_Xingbao_VALUE: {
                return MatchMmrChestXingbaoSettlementData.getInstance().getChestXingbaoSettlementListByMmr(curMmrScore.get());
            }

            case ChestActorIdType.CAIT_Boss_VALUE: {
                return MatchMmrChestDarkStarSettlementData.getInstance().getChestDarkStarSettlementListByMmr(curMmrScore.get());
            }

            default: {
                LOGGER.error("battleId {} unknown sideId {}",
                        dataProvider.getDataIdForLog(), sideId.get());
                return Collections.emptyList();
            }
        }
    }
}

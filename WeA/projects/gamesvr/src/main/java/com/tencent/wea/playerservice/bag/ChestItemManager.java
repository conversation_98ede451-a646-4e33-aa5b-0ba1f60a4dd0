package com.tencent.wea.playerservice.bag;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.nk.util.guid.ItemIdGenerator;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ChestItemAttrData;
import com.tencent.resourceloader.resclass.ChestItemBaseData;
import com.tencent.resourceloader.resclass.ChestBagMiscConf;
import com.tencent.resourceloader.resclass.MallCommodityConf;
import com.tencent.wea.attr.ChestBattleInfo;
import com.tencent.wea.attr.ChestItem;
import com.tencent.wea.attr.ChestItemDb;
import com.tencent.wea.attr.KvIL;
import com.tencent.wea.g6.irpc.proto.sd_battle.SdBattleOuterClass;
import com.tencent.wea.g6.irpc.service.SdBattleService;
import com.tencent.wea.item.AbstractItemManager;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsChestBag;
import com.tencent.wea.protocol.CsChestBag.ChestItemChangeReason;
import com.tencent.wea.protocol.CsChestBag.ChestItemPosition;
import com.tencent.wea.protocol.CsChestBag.ChestMallBuyMsg_C2S_Msg;
import com.tencent.wea.protocol.CsMall;
import com.tencent.wea.protocol.common.ChestItemInfo;
import com.tencent.wea.protocol.common.ItemArray;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.KeyValueInt64;
import com.tencent.wea.xlsRes.ResBackpackItem;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResChestItem;
import com.tencent.wea.xlsRes.ResChestItem.ChestItemBaseConfig;
import com.tencent.wea.xlsRes.ResMall;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.keywords.ChaseSideType;
import com.tencent.wea.xlsRes.keywords.ChestItemAttrType;
import com.tencent.wea.xlsRes.keywords.CoinType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * chestItem管理器
 * 继承自AbstractItemManager，实现宝箱特定的道具管理逻辑
 *
 * <AUTHOR>
 */
public class ChestItemManager extends AbstractItemManager<ChestItem, ChestItemDb> {

    private static final Logger LOGGER = LogManager.getLogger(ChestItemManager.class);
    private static final int INVALID_GRID_ID = 0;                                  // 无效格子ID（格子ID从1开始，<=0都无效）
    private static final int STORAGE_GRID_ID_BEGIN = 1;                            // 格子ID起始值
    private static final int STORAGE_MAX_GRID_COUNT = ChestBagMiscConf.getInstance()
            .getChestBagMiscConfig().getMaxStorageCapacity();                         // 背包最大格子数
    private static final int XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN =
            ChaseSideType.CST_XINGBAO.getNumber() * 10000;          // 星宝装备格子ID开始值
    private static final int XINGBAO_EQUIP_STORAGE_GRID_ID_END =
            XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN + ChestBagMiscConf.getInstance()
                    .getChestBagMiscConfig().getPlayerAvailableBagCount();          // 星宝装备格子ID开始值

    private static final int DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN =
            ChaseSideType.CST_DARKSTAR.getNumber() * 10000;         // 暗星装备格子ID开始值
    private static final int DARKSTAR_EQUIP_STORAGE_GRID_ID_END =
            DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN + ChestBagMiscConf.getInstance()
                    .getChestBagMiscConfig().getBossAvailableBagCount();         // 暗星装备格子ID开始值
    private final Player player;
    // 位置管理相关字段
    private final Map<Integer, Long> gridId2ItemUUID = new ConcurrentHashMap<>();  // 格子ID到道具UUID的映射
    private final Set<Integer> lockedGrids = new HashSet<>();                      // 锁定的格子ID集合
    private final ChestItemPositionNotifier positionNotifier;                      // 位置变更通知器

    // 动态空闲格子管理相关字段
    private final PriorityBlockingQueue<Integer> availableGridQueue;                // 空闲格子优先队列（自动排序，线程安全）
    private final AtomicInteger availableGridCount;                                 // 剩余可用格子数（原子操作）
    private final Object gridManagementLock = new Object();                        // 格子管理锁

    public ChestItemManager(Player player) {
        super(player.getUserAttr().getChestGameInfo().getChestItemDb());
        this.player = player;
        this.positionNotifier = new ChestItemPositionNotifier(player);
        this.availableGridQueue = new PriorityBlockingQueue<>();
        this.availableGridCount = new AtomicInteger(0);
        initializePositionMappings();
        initializeDynamicGridManagement();
    }

    /**
     * 检查格子ID是否有效
     * 格子ID有效范围：1 到 MAX_GRID_COUNT
     * XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN 到 XINGBAO_EQUIP_STORAGE_GRID_ID_END
     * DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN 到 DARKSTAR_EQUIP_STORAGE_GRID_ID_END
     * 小于等于0的格子ID都被视为无效
     */
    private static boolean isValidGridId(int gridId) {
        return isStorage(gridId) || isXingBaoEquipStorage(gridId) || isDarkStarEquipStorage(gridId);
    }

    private static boolean isStorageGridLocked(int gridId) {
        return gridId > STORAGE_MAX_GRID_COUNT;
    }

    /**
     * 检查格子ID是否为仓库
     */
    private static boolean isStorage(int gridId) {
        return gridId >= STORAGE_GRID_ID_BEGIN && gridId <= STORAGE_MAX_GRID_COUNT;
    }

    /**
     * 检查格子ID是否为星宝装备仓库
     *
     * @param gridId
     *
     * @return
     */
    private static boolean isXingBaoEquipStorage(int gridId) {
        return gridId > XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN
                && gridId <= XINGBAO_EQUIP_STORAGE_GRID_ID_END;
    }

    /**
     * 检查格子ID是否为暗星装备仓库
     *
     * @param gridId
     *
     * @return
     */
    private static boolean isDarkStarEquipStorage(int gridId) {
        return gridId > DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN
                && gridId <= DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
    }


    /**
     * 检查chestItem是否过期
     *
     * @param item chestItem
     *
     * @return 是否过期
     */
    private static boolean isItemExpired(ChestItem item) {
        return false;
    }

    /**
     * 为chestItem设置属性
     *
     * @param item  chestItem
     * @param key   属性键
     * @param value 属性值
     */
    public static void setItemAttribute(ChestItem item, int key, long value) {
        if (item.getAttr(key) != null) {
            item.getAttr(key).setValue(value);
            return;
        }
        KvIL attr = new KvIL();
        attr.setK(key);
        attr.setValue(value);
        item.putAttr(key, attr);
    }

    /**
     * 为chestItem添加属性
     *
     * @param item  chestItem
     * @param key   属性键
     * @param value 属性值
     */
    private static void addItemAttribute(ChestItem item, int key, long value) {
        if (item.getAttr(key) != null) {
            item.getAttr(key).addValue(value);
            return;
        }
        KvIL attr = new KvIL();
        attr.setK(key);
        attr.setValue(value);
        item.putAttr(key, attr);
    }

    /**
     * 获取chestItem属性值（公开方法，供 BagManager 使用）
     *
     * @param item chestItem
     * @param key  属性键
     *
     * @return 属性值，不存在返回0
     */
    public static long getItemAttribute(ChestItem item, int key) {
        KvIL attr = item.getAttr(key);
        return attr != null ? attr.getValue() : 0;
    }

    public static long getItemAttribute(ItemInfo item, int key) {
        for (KeyValueInt64 attrInfo : item.getChestItemInfo().getChestItemAttrList()) {
            if (attrInfo.getKey() == key) {
                return attrInfo.getValue();
            }
        }
        return 0;
    }

    // 更新chestItem的价值
    public static void updateItemValue(ChestItem item, ResChestItem.ChestItemBaseConfig chestItemBaseConfig) {
        if (chestItemBaseConfig == null) {
            chestItemBaseConfig = ChestItemBaseData.getInstance().get(item.getItemId());
        }
        if (chestItemBaseConfig == null) {
            LOGGER.error("chestItemBaseConfig is null, itemId:{}", item.getItemId());
            return;
        }
        if (chestItemBaseConfig.getMaxDurability() <= 0) {
            return;
        }
        long fullBaseValue = chestItemBaseConfig.getBaseValue();
        long actValue = (fullBaseValue - chestItemBaseConfig.getEmptyDurabilityValue()) * getItemAttribute(item,
                ChestItemAttrType.CIAT_Durability.getNumber()) + chestItemBaseConfig.getEmptyDurabilityValue();
        actValue = Math.min(actValue, fullBaseValue);
        long sellAmount = actValue * chestItemBaseConfig.getSalePriceDecay() / 10000;
        long repairAmount = (fullBaseValue - actValue) * chestItemBaseConfig.getRepairCoefficient() / 10000;
        setItemAttribute(item, ChestItemAttrType.CIAT_BaseValue.getNumber(), actValue);
        setItemAttribute(item, ChestItemAttrType.CIAT_SellAmount.getNumber(), sellAmount);
        setItemAttribute(item, ChestItemAttrType.CIAT_RepairAmount.getNumber(), repairAmount);
    }

    @Override
    protected ChestItem createItemInstance() {
        return new ChestItem();
    }

    @Override
    protected void storeItem(long itemUUID, ChestItem item) {
        dataStorage.putChestItems(itemUUID, item);
    }

    @Override
    public ChestItem retrieveItem(long itemUUID) {
        return dataStorage.getChestItems(itemUUID);
    }

    @Override
    protected void removeItemFromStorage(long itemUUID) {
        // 获取道具信息以清理位置映射和回收格子
        ChestItem item = dataStorage.getChestItems(itemUUID);
        if (item != null && item.getGridId() > 0) {
            gridId2ItemUUID.remove(item.getGridId());
            // 回收格子到空闲队列
            recycleGridToAvailableQueue(item.getGridId());
        }

        dataStorage.removeChestItems(itemUUID);
        LOGGER.debug("Removed chest item from storage: player={}, itemUUID={}, gridId={}",
                player.getUid(), itemUUID, item != null ? item.getGridId() : "unknown");
    }

    @Override
    protected void initializeItem(ChestItem item, ItemInfo itemInfo) {

        try {
            var chestBaseConf = ChestItemBaseData.getInstance().get(item.getItemId());
            // chestItem特定的初始化逻辑
            if (itemInfo.hasChestItemInfo()) {
                for (KeyValueInt64 attr : itemInfo.getChestItemInfo().getChestItemAttrList()) {
                    int key = (int) attr.getKey();
                    item.putAttr(key, new KvIL().setK(key).setValue(attr.getValue()));
                }
            } else {
                // 默认配置属性
                var chestAttrConf = ChestItemAttrData.getInstance().get(item.getItemId());
                if (chestAttrConf != null) {
                    for (ResChestItem.ChestItemAttr attr : chestAttrConf.getAttrList()) {
                        addItemAttribute(item, attr.getKey().getNumber(), attr.getValue());
                    }
                }
                if (chestBaseConf != null && chestBaseConf.getMaxDurability() > 0) {
                    setItemAttribute(item, ChestItemAttrType.CIAT_Durability.getNumber(),
                            chestBaseConf.getMaxDurability());
                }
            }
            updateItemValue(item, chestBaseConf);
            LOGGER.debug("Initialized chest item: player={}, itemId={}, itemUUID={}, attributes={}",
                    player.getUid(), item.getItemId(), item.getId(), item.getAttr().size());
        } catch (Exception e) {
            LOGGER.error("Failed to retrieve chest item from database: player={}, itemId={}",
                    player.getUid(), item.getItemId(), e);
        }

    }

    @Override
    public ChestItem createItem(ResBackpackItem.Item_BackpackItem itemConf, long itemNum, ItemInfo itemInfo) {
        if (itemNum <= 0) {
            LOGGER.warn("Invalid chest item number: {}", itemNum);
            return null;
        }

        long itemUUID = ItemIdGenerator.getInstance().allocGuid();
        ChestItem item = createItemInstance();
        item.setId(itemUUID);
        item.setItemId(itemInfo.getItemId());
        item.setNumber(itemNum);

        // 调用子类特定的初始化逻辑
        initializeItem(item, itemInfo);
        storeItem(itemUUID, item);
        addItemMapping(itemInfo.getItemId(), itemUUID);
        allocateGridId(item);
        LOGGER.debug("Created chest item: player={}, itemId={}, itemUUID={}, gridId:{} number={}",
                player.getUid(), itemInfo.getItemId(), itemUUID, item.getGridId(), itemNum);
        return item;
    }

    public ChestItem createItemWithGridId(ResBackpackItem.Item_BackpackItem itemConf, long itemNum, ItemInfo itemInfo,
            int gridId) {
        if (itemNum <= 0) {
            LOGGER.warn("Invalid chest item number: {}", itemNum);
            return null;
        }

        if (!isValidGridId(gridId)) {
            LOGGER.error("Invalid grid id: {}", gridId);
            return null;
        }

        if (gridId2ItemUUID.containsKey(gridId)) {
            LOGGER.error("Grid id already occupied: {}", gridId);
            return null;
        }

        long itemUUID = ItemIdGenerator.getInstance().allocGuid();
        ChestItem item = createItemInstance();
        item.setId(itemUUID);
        item.setItemId(itemInfo.getItemId());
        item.setNumber(itemNum);
        item.setGridId(gridId);

        // 调用子类特定的初始化逻辑
        initializeItem(item, itemInfo);
        storeItem(itemUUID, item);
        addItemMapping(itemInfo.getItemId(), itemUUID);
        allocateGridId(item);
        LOGGER.debug("Created chest item: player={}, itemId={}, itemUUID={}, gridId:{}, number={}",
                player.getUid(), itemInfo.getItemId(), itemUUID, gridId, itemNum);
        return item;
    }

    // 分配仓库格子
    private void allocateGridId(ChestItem item) {
        // 自动分配背包格子ID（使用动态分配策略）
        if (item.getGridId() <= 0) {
            int assignedGridId = allocateSmallestAvailableGridId();
            item.setGridId(assignedGridId);
            if (assignedGridId > 0) {
                gridId2ItemUUID.put(assignedGridId, item.getId());
            }
        } else {
            // 如果已指定格子ID，需要从空闲队列中移除该格子
            synchronized (gridManagementLock) {
                if (availableGridQueue.remove(item.getGridId())) {
                    availableGridCount.decrementAndGet();
                }
            }
            gridId2ItemUUID.put(item.getGridId(), item.getId());
        }
    }

    @Override
    public long getItemCount(int itemId) {
        long totalCount = 0;
        Set<Long> uuids = itemId2UUIDs.get(itemId);
        if (uuids != null) {
            for (Long uuid : uuids) {
                ChestItem item = retrieveItem(uuid);
                if (item != null && isItemValid(uuid)) {
                    totalCount += item.getNumber();
                }
            }
        }
        return totalCount;
    }

    public static ItemInfo.Builder convChestItemToItemInfo(ChestItem item) {
        ItemInfo.Builder itemInfo = ItemInfo.newBuilder().setUuid(item.getId())
                .setItemId(item.getItemId())
                .setItemNum(item.getNumber())
                .setChestItemInfo(chestItemToChestItemInfo(item));
        return itemInfo;
    }

    public static ChestItem convItemInfoToChestItem(ItemInfo itemInfo) {
        ChestItem chestItem = new ChestItem().setId(itemInfo.getUuid())
                .setItemId(itemInfo.getItemId())
                .setNumber(itemInfo.getItemNum())
                .setGridId(itemInfo.getChestItemInfo().getGridId());
        if (itemInfo.getChestItemInfo().getChestItemAttrCount() > 0) {
            for (KeyValueInt64 attrInfo : itemInfo.getChestItemInfo().getChestItemAttrList()) {
                int attrKey = (int) attrInfo.getKey();
                if (ChestItemAttrType.forNumber(attrKey) == null) {
                    continue;
                }
                chestItem.putAttr(attrKey, new KvIL().setK(attrKey).setValue(attrInfo.getValue()));
            }
        }
        updateItemValue(chestItem, null);
        return chestItem;
    }


    /**
     * 尝试与现有道具堆叠
     *
     * @param itemInfo        道具信息
     * @param numberToAdd     要添加的数量
     * @param maxStackSize    最大堆叠数量
     * @param addResults      添加结果列表
     * @param changeResults   变更结果列表
     *
     * @return 剩余未添加的数量
     */
    private long tryStackWithExistingItems(ItemInfo itemInfo, long numberToAdd, int maxStackSize,
            ItemArray.Builder addResults, List<ChestItemChangeResult> changeResults) {

        // 获取现有的同类型道具
        List<ChestItem> existingItems = getItemsByItemId(itemInfo.getItemId());

        long remainingToAdd = numberToAdd;

        for (ChestItem existingItem : existingItems) {
            if (remainingToAdd <= 0) {
                break;
            }

            // 检查道具是否有效且未满
            if (!isItemValid(existingItem.getId()) || existingItem.getNumber() >= maxStackSize) {
                continue;
            }

            // 检查属性是否匹配（只有属性完全一致的道具才能堆叠）
            if (!canStackWithItem(existingItem, itemInfo)) {
                continue;
            }

            // 计算可以添加到此道具的数量
            long availableSpace = maxStackSize - existingItem.getNumber();
            long toAddToThisItem = Math.min(remainingToAdd, availableSpace);

            if (toAddToThisItem > 0) {
                // 更新道具数量
                long oldNumber = existingItem.getNumber();
                existingItem.setNumber(oldNumber + toAddToThisItem);

                // 记录添加结果
                addResults.addItems(
                        ItemInfo.newBuilder().setUuid(existingItem.getId()).setItemId(existingItem.getItemId())
                                .setItemNum(toAddToThisItem).build());
                changeResults.add(new ChestItemChangeResult().setItemUUID(existingItem.getId())
                        .setItemId(existingItem.getItemId())
                        .setGridId(existingItem.getGridId())
                        .setChangeType(ChestItemChangeReason.CICR_ItemUpdate)
                        .setChangeBefore(oldNumber)
                        .setChangeNum(toAddToThisItem)
                        .setChangeAfter(existingItem.getNumber())
                );

                remainingToAdd -= toAddToThisItem;

                LOGGER.debug(
                        "Stacked with existing chest item: player={}, itemId={}, itemUUID={}, oldNumber={}, addedNumber={}, newNumber={}",
                        player.getUid(), itemInfo.getItemId(), existingItem.getId(), oldNumber, toAddToThisItem,
                        existingItem.getNumber());
            }
        }

        return remainingToAdd;
    }

    /**
     * 检查是否可以与指定道具堆叠
     * 只有当道具ID相同且所有属性完全一致时才允许堆叠
     *
     * @param existingItem 现有道具
     * @param itemInfo     道具信息
     *
     * @return 是否可以堆叠
     */
    private boolean canStackWithItem(ChestItem existingItem, ItemInfo itemInfo) {
        // todo 检查属性数量是否匹配

        // 对于其他参数类型，暂时认为不可堆叠
        return false;
    }

    /**
     * 扣道具接口
     *
     * @param itemUUID 道具UUID
     * @param number 移除数量
     * @return 是否成功移除
     */
    @Override
    public boolean removeItem(long itemUUID, long number) {
        if (number <= 0) {
            LOGGER.warn("Invalid remove number: player={}, itemUUID={}, number={}",
                    player.getUid(), itemUUID, number);
            return false;
        }

        try {
            ChestItem item = retrieveItem(itemUUID);
            if (item == null) {
                LOGGER.warn("Item not found for removal: player={}, itemUUID={}",
                        player.getUid(), itemUUID);
                return false;
            }

            if (!isItemValid(itemUUID)) {
                LOGGER.warn("Invalid item for removal: player={}, itemUUID={}, itemId={}",
                        player.getUid(), itemUUID, item.getItemId());
                return false;
            }

            long currentNumber = item.getNumber();
            if (number > currentNumber) {
                LOGGER.warn("Insufficient item quantity: player={}, itemUUID={}, requested={}, available={}",
                        player.getUid(), itemUUID, number, currentNumber);
                return false;
            }

            // 执行扣除操作
            var chestItemChangeResult = performSingleItemRemoval(item, number);
            if (chestItemChangeResult != null) {
                // 发送扣除通知
                positionNotifier.notifySingleItemPositionChange(
                        chestItemChangeResult.getItemUUID(),
                        chestItemChangeResult.getGridId(),
                        chestItemChangeResult.getItemId(),
                        chestItemChangeResult.getChangeType()
                );

                LOGGER.debug("Successfully removed item: player={}, result={}",
                        player.getUid(), chestItemChangeResult.toString());
                return true;
            }

            return false;

        } catch (Exception e) {
            LOGGER.error("Error removing item: player={}, itemUUID={}, number={}, error={}",
                    player.getUid(), itemUUID, number, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 按道具ID批量扣除道具
     *
     * @param itemId 道具ID
     * @param number 要扣除的数量
     *
     * @return 批量扣除结果
     */
    @Override
    public boolean removeItemByItemId(int itemId, long number) {
        try {
            LOGGER.debug("Starting batch removal: player={}, itemId={}, number={}",
                    player.getUid(), itemId, number);

            // 获取所有符合条件的道具，按FIFO顺序排序
            List<ChestItem> candidateItems = getItemsByItemIdSortedByCreationTime(itemId);

            if (candidateItems.isEmpty()) {
                LOGGER.warn("No items found for removal: player={}, itemId={}",
                        player.getUid(), itemId);
                return false;
            }

            // 执行批量扣除
            List<ChestItemChangeResult> removeResults = new ArrayList<>();
            long remainingToRemove = number;

            for (ChestItem item : candidateItems) {
                if (remainingToRemove <= 0) {
                    break;
                }

                if (!isItemValid(item.getId())) {
                    continue;
                }

                long currentItemNumber = item.getNumber();
                long toRemoveFromThisItem = Math.min(remainingToRemove, currentItemNumber);

                ChestItemChangeResult result = performSingleItemRemoval(item, toRemoveFromThisItem);
                if (result != null) {
                    removeResults.add(result);
                    remainingToRemove -= result.getChangeNum();
                    LOGGER.debug("Removed from item: player={}, itemUUID={}, removed={}, remaining={}",
                            player.getUid(), item.getId(), result.getChangeNum(), remainingToRemove);
                }
            }

            // 发送批量扣除通知
            if (!removeResults.isEmpty()) {
                notifyItemChange(removeResults);
            }

            LOGGER.info("Batch removal completed: player={}, itemId={}, remainingToRemove={}",
                    player.getUid(), itemId, remainingToRemove);

            return remainingToRemove == 0;

        } catch (Exception e) {
            LOGGER.error("Error removing items by ID: player={}, itemId={}, number={}, error={}",
                    player.getUid(), itemId, number, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行单个道具的扣除操作
     *
     * @param item           要扣除的道具
     * @param numberToRemove 要扣除的数量
     *
     * @return 实际移除数量
     */
    private ChestItemChangeResult performSingleItemRemoval(ChestItem item, long numberToRemove) {
        var result = new ChestItemChangeResult().setItemUUID(item.getId())
                .setItemId(item.getItemId())
                .setGridId(item.getGridId())
                .setChangeBefore(item.getNumber());
        try {
            long currentNumber = item.getNumber();
            int gridId = item.getGridId();

            if (numberToRemove >= currentNumber) {
                // 完全移除道具
                long removedNumber = currentNumber;

                // 从映射中移除
                removeItemMapping(item.getItemId(), item.getId());

                // 从存储中移除
                removeItemFromStorage(item.getId());

                LOGGER.debug("Completely removed item: player={}, itemUUID={}, itemId={}, removedNumber={}, gridId={}",
                        player.getUid(), item.getId(), item.getItemId(), removedNumber, gridId);

                return result.setChangeType(ChestItemChangeReason.CICR_ItemRemove)
                        .setChangeNum(removedNumber)
                        .setChangeAfter(0);

            } else {
                // 部分移除，减少数量
                long newNumber = currentNumber - numberToRemove;
                item.setNumber(newNumber);

                LOGGER.debug(
                        "Partially removed item: player={}, itemUUID={}, itemId={}, removedNumber={}, remainingNumber={}",
                        player.getUid(), item.getId(), item.getItemId(), numberToRemove, newNumber);

                return result.setChangeType(ChestItemChangeReason.CICR_ItemUpdate)
                        .setChangeNum(numberToRemove)
                        .setChangeAfter(newNumber);
            }

        } catch (Exception e) {
            LOGGER.error("Error performing single item removal: player={}, itemUUID={}, numberToRemove={}, error={}",
                    player.getUid(), item.getId(), numberToRemove, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取指定道具ID的所有道具，按创建时间排序（FIFO）
     *
     * @param itemId 道具ID
     *
     * @return 排序后的道具列表
     */
    private List<ChestItem> getItemsByItemIdSortedByCreationTime(int itemId) {
        List<ChestItem> items = getItemsByItemId(itemId);

        // 按道具UUID排序（UUID包含时间信息，可以实现FIFO）
        items.sort(Comparator.comparingLong(ChestItem::getId));

        return items;
    }

    /**
     * 发送道具移除通知
     *
     * @param removeResults 移除结果列表
     */
    private void notifyItemChange(List<ChestItemChangeResult> removeResults) {

    }

    @Override
    public boolean isItemValid(long itemUUID) {
        ChestItem item = retrieveItem(itemUUID);
        if (item == null || item.getNumber() <= 0) {
            return false;
        }

        // 检查是否过期
        if (isItemExpired(item)) {
            return false;
        }

        return true;
    }

    @Override
    public void cleanExpiredItems() {
        List<Long> expiredUUIDs = new ArrayList<>();

        // 查找过期道具
        for (Set<Long> uuids : itemId2UUIDs.values()) {
            for (Long uuid : uuids) {
                ChestItem item = retrieveItem(uuid);
                if (item != null && isItemExpired(item)) {
                    expiredUUIDs.add(uuid);
                }
            }
        }

        // 处理过期道具
        for (Long uuid : expiredUUIDs) {
            handleExpiredItem(uuid);
        }

        if (!expiredUUIDs.isEmpty()) {
            LOGGER.info("Cleaned {} expired chest items for player {}",
                    expiredUUIDs.size(), player.getUid());
        }
    }

    /**
     * 处理过期chestItem
     *
     * @param itemUUID 过期道具UUID
     */
    private void handleExpiredItem(long itemUUID) {

    }

    @Override
    protected void initItemMappings() {
        // 从现有数据重建映射关系
        for (ChestItem item : dataStorage.getChestItems().values()) {
            if (item != null) {
                addItemMapping(item.getItemId(), item.getId());
            }
        }
    }

    /**
     * 初始化位置映射
     */
    private void initializePositionMappings() {
        gridId2ItemUUID.clear();
        // 从现有道具重建位置映射
        for (ChestItem item : dataStorage.getChestItems().values()) {
            if (item != null && item.getGridId() > 0) {
                gridId2ItemUUID.put(item.getGridId(), item.getId());
            } else {
                LOGGER.warn("Invalid chest item found during position mapping initialization: player={}, item={}",
                        player.getUid(), item != null ? item.getId() : "null");
            }
        }
        LOGGER.debug("Initialized position mappings for player {}, mappings: {}",
                player.getUid(), gridId2ItemUUID.size());
    }

    /**
     * 初始化动态空闲格子管理系统
     * 构建空闲格子队列并统计可用格子数量
     * 增加道具格子校验、冲突检测和自动重新分配机制
     */
    private void initializeDynamicGridManagement() {
        synchronized (gridManagementLock) {
            // 清空队列
            availableGridQueue.clear();

            // 1. 校验现有道具的格子分配并处理冲突
            Map<Integer, Long> validOccupiedGrids = validateAndResolveGridConflicts();

            // 2. 构建空闲格子队列（按格子ID升序排列）
            for (int gridId = STORAGE_GRID_ID_BEGIN; gridId <= STORAGE_MAX_GRID_COUNT; gridId++) {
                if (!validOccupiedGrids.containsKey(gridId) && !lockedGrids.contains(gridId)) {
                    availableGridQueue.offer(gridId); // 可用格子ID入队
                }
            }

            // 3. 更新可用格子计数
            availableGridCount.set(availableGridQueue.size());

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(
                        "Initialized dynamic grid management for player {}: validOccupiedGrids={}, availableGrids={}, maxGrids={}, queueSize={}",
                        player.getUid(), validOccupiedGrids.size(), availableGridCount.get(), STORAGE_MAX_GRID_COUNT,
                        availableGridQueue.size());
            }
        }
    }

    /**
     * 校验现有道具的格子分配并解决冲突
     *
     * @return 有效的已占用格子映射（格子ID -> 道具UUID）
     */
    private Map<Integer, Long> validateAndResolveGridConflicts() {
        Map<Integer, Long> validOccupiedGrids = new HashMap<>();
        List<ChestItem> conflictItems = new ArrayList<>();
        int totalConflicts = 0;
        int resolvedConflicts = 0;

        LOGGER.debug("Starting grid conflict validation for player {}", player.getUid());

        // 1. 遍历所有道具，检查格子分配的合法性
        for (ChestItem item : dataStorage.getChestItems().values()) {
            if (item == null || item.getNumber() <= 0) {
                LOGGER.warn("Invalid item found during grid validation: player={}, item={}",
                        player.getUid(), item != null ? item.getId() : "null");
                continue;
            }

            int currentGridId = item.getGridId();

            // 检查格子ID是否有效
            if (!isValidGridId(currentGridId)) {
                LOGGER.warn("Item has invalid grid ID: player={}, itemUUID={}, itemId={}, gridId={}",
                        player.getUid(), item.getId(), item.getItemId(), currentGridId);
                conflictItems.add(item);
                totalConflicts++;
                continue;
            }

            // 检查格子是否在有效范围内（仅处理仓库格子）
            if (!isStorage(currentGridId)) {
                // 非仓库格子（如装备格子）直接跳过，不参与空闲格子管理
                continue;
            }

            // 检查格子是否已被其他道具占用
            if (validOccupiedGrids.containsKey(currentGridId)) {
                long existingItemUUID = validOccupiedGrids.get(currentGridId);
                LOGGER.warn("Grid conflict detected: player={}, gridId={}, existingItemUUID={}, conflictItemUUID={}",
                        player.getUid(), currentGridId, existingItemUUID, item.getId());
                conflictItems.add(item);
                totalConflicts++;
                continue;
            }

            // 检查道具与格子映射是否一致
            Long mappedItemUUID = gridId2ItemUUID.get(currentGridId);
            if (mappedItemUUID != null && !mappedItemUUID.equals(item.getId())) {
                LOGGER.warn("Grid mapping inconsistency: player={}, gridId={}, mappedUUID={}, actualUUID={}",
                        player.getUid(), currentGridId, mappedItemUUID, item.getId());
                conflictItems.add(item);
                totalConflicts++;
                continue;
            }

            // 格子分配有效，记录占用关系
            validOccupiedGrids.put(currentGridId, item.getId());
        }

        // 2. 处理冲突道具，自动重新分配格子
        if (!conflictItems.isEmpty()) {
            LOGGER.warn("Found {} conflict items for player {}, attempting auto-resolution",
                    conflictItems.size(), player.getUid());

            for (ChestItem conflictItem : conflictItems) {
                int newGridId = findBestAvailableGridForReallocation(conflictItem.getGridId(), validOccupiedGrids);

                if (newGridId != INVALID_GRID_ID) {
                    // 记录重新分配前的状态
                    int oldGridId = conflictItem.getGridId();

                    // 执行重新分配
                    conflictItem.setGridId(newGridId);
                    validOccupiedGrids.put(newGridId, conflictItem.getId());
                    resolvedConflicts++;

                    LOGGER.info(
                            "Auto-resolved grid conflict: player={}, itemUUID={}, itemId={}, oldGrid={}, newGrid={}",
                            player.getUid(), conflictItem.getId(), conflictItem.getItemId(), oldGridId, newGridId);
                } else {
                    LOGGER.error(
                            "Failed to resolve grid conflict - no available grid: player={}, itemUUID={}, itemId={}",
                            player.getUid(), conflictItem.getId(), conflictItem.getItemId());
                }
            }
        }

        // 3. 重建格子映射关系
        gridId2ItemUUID.clear();
        gridId2ItemUUID.putAll(validOccupiedGrids);

        // 4. 记录初始化结果
        if (totalConflicts > 0) {
            LOGGER.warn(
                    "Grid conflict resolution completed: player={}, totalConflicts={}, resolvedConflicts={}, unresolvedConflicts={}",
                    player.getUid(), totalConflicts, resolvedConflicts, totalConflicts - resolvedConflicts);
        } else {
            LOGGER.debug("No grid conflicts found during initialization: player={}", player.getUid());
        }

        return validOccupiedGrids;
    }

    /**
     * 为重新分配寻找可用格子
     *
     * @param originalGridId 原始格子ID
     * @param occupiedGrids  已占用的格子映射
     *
     * @return 最佳可用格子ID，如果没有可用格子返回INVALID_GRID_ID
     */
    private int findBestAvailableGridForReallocation(int originalGridId, Map<Integer, Long> occupiedGrids) {
        //如果原格子ID有效且未被占用，优先使用原格子
        if (isStorage(originalGridId) && !occupiedGrids.containsKey(originalGridId) && !lockedGrids.contains(
                originalGridId)) {
            return originalGridId;
        }
        //寻找最小可用格子ID
        for (int gridId = STORAGE_GRID_ID_BEGIN; gridId <= STORAGE_MAX_GRID_COUNT; gridId++) {
            if (!occupiedGrids.containsKey(gridId) && !lockedGrids.contains(gridId)) {
                LOGGER.debug("Found smallest available grid for reallocation: player={}, originalGrid={}, newGrid={}",
                        player.getUid(), originalGridId, gridId);
                return gridId;
            }
        }

        // 4. 没有可用格子
        LOGGER.error(
                "No available grid found for reallocation: player={}, originalGrid={}, occupiedCount={}, maxGrids={}",
                player.getUid(), originalGridId, occupiedGrids.size(), STORAGE_MAX_GRID_COUNT);
        return INVALID_GRID_ID;
    }

    // ==================== 位置管理相关方法 ====================

    /**
     * 交换两个道具的位置
     *
     * @param sourceItemUuid 源道具UUID
     * @param sourceGridId   源位置格子ID
     * @param targetItemUuid 目标道具UUID（可为0表示移动到空格子）
     * @param targetGridId   目标位置格子ID
     *
     * @return 错误码
     */
    public NKErrorCode swapItemPosition(long sourceItemUuid, int sourceGridId,
            long targetItemUuid, int targetGridId) {
        try {
            // 参数校验
            NKErrorCode validationResult = validateSwapParameters(sourceItemUuid, sourceGridId,
                    targetItemUuid, targetGridId);
            if (validationResult != NKErrorCode.OK) {
                return validationResult;
            }

            // 获取源道具
            ChestItem sourceItem = retrieveItem(sourceItemUuid);
            if (sourceItem == null) {
                LOGGER.warn("Source item not found: player={}, itemUuid={}", player.getUid(), sourceItemUuid);
                return NKErrorCode.ItemNotExist;
            }

            ChestItem targetItem = null;
            if (targetItemUuid != 0) {
                targetItem = retrieveItem(targetItemUuid);
                if (targetItem == null) {
                    LOGGER.warn("Target item not found: player={}, itemUuid={}", player.getUid(), targetItemUuid);
                    return NKErrorCode.ItemNotExist;
                }
            }

            // 执行位置交换
            return performPositionSwap(sourceItem, sourceGridId, targetItem, targetGridId);

        } catch (Exception e) {
            LOGGER.error("Error swapping item positions: player={}, sourceUuid={}, targetUuid={}, error={}",
                    player.getUid(), sourceItemUuid, targetItemUuid, e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 校验交换参数
     */
    private NKErrorCode validateSwapParameters(long sourceItemUuid, int sourceGridId,
            Long targetItemUuid, int targetGridId) {
        // 检查格子ID有效性
        if (!isValidGridId(sourceGridId) || !isValidGridId(targetGridId)) {
            return NKErrorCode.ChestBagErrInvalidGridId;
        }

        // 检查格子是否被锁定
        if (lockedGrids.contains(sourceGridId) || lockedGrids.contains(targetGridId)) {
            return NKErrorCode.ChestBagErrGridLocked;
        }

        // 检查是否是相同位置
        if (sourceGridId == targetGridId) {
            return NKErrorCode.ChestBagErrSamePosition;
        }

        return NKErrorCode.OK;
    }

    /**
     * 执行位置交换
     */
    private NKErrorCode performPositionSwap(ChestItem sourceItem, int sourceGridId,
            ChestItem targetItem, int targetGridId) {
        // 更新源道具位置
        sourceItem.setGridId(targetGridId);

        if (targetItem != null) {
            // 如果有目标道具，交换位置
            targetItem.setGridId(sourceGridId);
            gridId2ItemUUID.put(sourceGridId, targetItem.getId());
        } else {
            // 如果目标位置为空，清除源位置映射
            gridId2ItemUUID.remove(sourceGridId);
            // 将源位置回收到空闲队列
            recycleGridToAvailableQueue(sourceGridId);
        }

        // 更新位置映射
        gridId2ItemUUID.put(targetGridId, sourceItem.getId());

        // 如果目标位置原本是空闲的，需要从空闲队列中移除
        if (targetItem == null) {
            synchronized (gridManagementLock) {
                if (availableGridQueue.remove(targetGridId)) {
                    availableGridCount.decrementAndGet();
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug(
                                "Removed target grid from available queue during swap: player={}, gridId={}, remainingGrids={}",
                                player.getUid(), targetGridId, availableGridCount.get());
                    }
                }
            }
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Swapped item positions: player={}, sourceItem={} ({} -> {}), targetItem={} ({} -> {})",
                    player.getUid(), sourceItem.getId(), sourceGridId, targetGridId,
                    targetItem != null ? targetItem.getId() : "null", targetGridId, sourceGridId);
        }

        // 发送位置变更通知
        List<CsChestBag.ChestItemPosition> changedPositions = new ArrayList<>();
        changedPositions.add(CsChestBag.ChestItemPosition.newBuilder()
                .setItemUuid(sourceItem.getId())
                .setGridId(targetGridId)
                .setItemId(sourceItem.getItemId())
                .build());

        if (targetItem != null) {
            changedPositions.add(CsChestBag.ChestItemPosition.newBuilder()
                    .setItemUuid(targetItem.getId())
                    .setGridId(sourceGridId)
                    .setItemId(targetItem.getItemId())
                    .build());
        }

        positionNotifier.notifyItemsSwapped(changedPositions);

        return NKErrorCode.OK;
    }

    /**
     * 背包整理（客户端排序版本）
     * 客户端负责排序，服务器负责校验和保存
     *
     * @param newPositions 客户端整理后的位置信息
     *
     * @return 错误码
     */
    public NKErrorCode organizeBag(List<CsChestBag.ChestItemPosition> newPositions) {
        try {
            LOGGER.info("Processing bag organize request: player={}, positions={}",
                    player.getUid(), newPositions.size());

            // 1. 校验客户端提供的位置信息
            NKErrorCode validationResult = validateOrganizePositions(newPositions);
            if (!validationResult.isOk()) {
                LOGGER.warn("Bag organize validation failed: player={}, validationResult={}",
                        player.getUid(), validationResult);
                return validationResult;
            }

            // 2. 应用新的位置布局
            NKErrorCode applyResult = applyNewPositions(newPositions);
            if (applyResult != NKErrorCode.OK) {
                LOGGER.error("Failed to apply new positions: player={}, error={}",
                        player.getUid(), applyResult);
                return applyResult;
            }

            // 在Ntf之前同步属性变化
            player.getUserAttrMgr().collectAndSyncDirtyToClient();

            // 3. 发送背包整理完成通知
            positionNotifier.notifyBagOrganized(newPositions);

            LOGGER.info("Bag organize completed successfully: player={}, positions={}",
                    player.getUid(), newPositions.size());

            return applyResult;

        } catch (Exception e) {
            LOGGER.error("Error organizing bag: player={}, error={}",
                    player.getUid(), e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }


    /**
     * 获取所有道具位置信息
     */
    public List<CsChestBag.ChestItemPosition> getAllItemPositions() {
        List<CsChestBag.ChestItemPosition> positions = new ArrayList<>();

        for (int gridId = STORAGE_GRID_ID_BEGIN; gridId <= STORAGE_MAX_GRID_COUNT; gridId++) {
            if (!gridId2ItemUUID.containsKey(gridId)) {
                continue;
            }
            long itemUUID = gridId2ItemUUID.get(gridId);
            if (itemUUID == 0) {
                continue;
            }
            ChestItem item = retrieveItem(itemUUID);
            if (item != null && isItemValid(item.getId())) {
                CsChestBag.ChestItemPosition.Builder posBuilder = CsChestBag.ChestItemPosition.newBuilder()
                        .setItemUuid(item.getId())
                        .setItemId(item.getItemId())
                        .setGridId(item.getGridId());
                positions.add(posBuilder.build());
            }
        }

        return positions;
    }

    private static ChestItemInfo chestItemToChestItemInfo(ChestItem item) {
        ChestItemInfo.Builder chestItemInfo = ChestItemInfo.newBuilder()
                .setGridId(item.getGridId());
        for (KvIL attrInfo : item.getAttr().values()) {
            chestItemInfo.addChestItemAttr(
                    KeyValueInt64.newBuilder().setKey(attrInfo.getK()).setValue(attrInfo.getValue()).build());

        }
        return chestItemInfo.build();
    }

    /**
     * 获取背包最大格子数
     */
    public int getMaxGridCount() {
        return STORAGE_MAX_GRID_COUNT;
    }

    /**
     * 获取锁定的格子列表
     */
    public List<Integer> getLockedGrids() {
        return new ArrayList<>(lockedGrids);
    }

    /**
     * 锁定格子（动态版本）
     * 锁定格子时需要从空闲队列中移除
     */
    public void lockGrid(int gridId) {
        if (isValidGridId(gridId)) {
            synchronized (gridManagementLock) {
                lockedGrids.add(gridId);
                // 从空闲队列中移除被锁定的格子
                if (availableGridQueue.remove(gridId)) {
                    availableGridCount.decrementAndGet();
                    LOGGER.debug(
                            "Locked grid and removed from available queue: player={}, gridId={}, remainingGrids={}",
                            player.getUid(), gridId, availableGridCount.get());
                } else {
                    LOGGER.debug("Locked grid (was not in available queue): player={}, gridId={}",
                            player.getUid(), gridId);
                }
            }
        }
    }

    /**
     * 解锁格子（动态版本）
     * 解锁格子时需要重新加入空闲队列（如果该格子确实空闲）
     */
    public void unlockGrid(int gridId) {
        if (lockedGrids.remove(gridId)) {
            synchronized (gridManagementLock) {
                // 检查格子是否真的空闲，如果是则加入空闲队列
                if (!gridId2ItemUUID.containsKey(gridId)) {
                    if (!availableGridQueue.contains(gridId)) {
                        availableGridQueue.offer(gridId);
                        availableGridCount.incrementAndGet();
                        LOGGER.debug(
                                "Unlocked grid and added to available queue: player={}, gridId={}, totalAvailableGrids={}",
                                player.getUid(), gridId, availableGridCount.get());
                    }
                } else {
                    LOGGER.debug("Unlocked grid (occupied, not added to queue): player={}, gridId={}",
                            player.getUid(), gridId);
                }
            }
        }
    }

    /**
     * 分配最小可用格子ID（动态版本）
     * 优先分配编号最小的空闲格子，确保道具存放的有序性
     *
     * @return 分配的格子ID，如果没有可用格子返回INVALID_GRID_ID
     */
    private int allocateSmallestAvailableGridId() {
        synchronized (gridManagementLock) {
            Integer gridId = availableGridQueue.poll();
            if (gridId != null) {
                // 更新可用格子计数
                availableGridCount.decrementAndGet();

                LOGGER.debug("Allocated smallest available grid: player={}, gridId={}, remainingGrids={}",
                        player.getUid(), gridId, availableGridCount.get());

                return gridId;
            } else {
                LOGGER.warn("No available grid found: player={}, maxGrids={}, queueSize={}",
                        player.getUid(), STORAGE_MAX_GRID_COUNT, availableGridQueue.size());
                return INVALID_GRID_ID;
            }
        }
    }

    /**
     * 回收格子到空闲队列
     * 当道具被移除时，将格子重新加入空闲队列
     *
     * @param gridId 要回收的格子ID
     */
    private void recycleGridToAvailableQueue(int gridId) {
        if (!isStorage(gridId)) {
            return;
        }

        synchronized (gridManagementLock) {
            // 检查格子是否真的空闲
            if (!gridId2ItemUUID.containsKey(gridId) && !lockedGrids.contains(gridId)) {
                // 避免重复添加
                if (!availableGridQueue.contains(gridId)) {
                    availableGridQueue.offer(gridId);
                    availableGridCount.incrementAndGet();

                    LOGGER.debug("Recycled grid to available queue: player={}, gridId={}, totalAvailableGrids={}",
                            player.getUid(), gridId, availableGridCount.get());
                }
            }
        }
    }

    /**
     * 校验道具位置
     */
    public NKErrorCode validateItemPositions(List<CsChestBag.ChestItemPosition> positions) {
        Set<Integer> usedGrids = new HashSet<>();

        for (CsChestBag.ChestItemPosition position : positions) {
            // 检查格子ID有效性
            if (!isValidGridId(position.getGridId())) {
                return NKErrorCode.ChestBagErrInvalidGridId;
            }

            // 检查格子是否重复使用
            if (usedGrids.contains(position.getGridId())) {
                return NKErrorCode.ChestBagErrGridOccupied;
            }
            usedGrids.add(position.getGridId());

            // 检查道具是否存在
            ChestItem item = retrieveItem(position.getItemUuid());
            if (item == null) {
                return NKErrorCode.ChestBagErrItemNotFound;
            }

            // 检查道具ID是否匹配
            if (item.getItemId() != position.getItemId()) {
                return NKErrorCode.ChestBagErrInvalidItemType;
            }
        }

        return NKErrorCode.OK;
    }

    /**
     * 获取指定格子的道具UUID
     */
    public Long getItemUuidByGridId(int gridId) {
        return gridId2ItemUUID.get(gridId);
    }

    /**
     * 检查格子是否被占用
     */
    public boolean isGridOccupied(int gridId) {
        return gridId2ItemUUID.containsKey(gridId);
    }

    /**
     * 获取道具的格子ID
     */
    public int getItemGridId(long itemUuid) {
        ChestItem item = retrieveItem(itemUuid);
        return item != null ? item.getGridId() : INVALID_GRID_ID;
    }

    /**
     * 检查背包格子是否超出限制（动态版本）
     * 使用原子操作的剩余格子数进行快速判断
     * 由于宝箱道具不堆叠，每个道具都占据一个独立格子
     *
     * @param chestBagNeedGrids 需要的格子数量
     *
     * @return true表示超出限制，false表示未超出
     */
    public boolean isBagGirdsExceedLimit(int chestBagNeedGrids) {
        if (chestBagNeedGrids <= 0) {
            return false;
        }

        // 使用原子操作获取当前可用格子数
        int currentAvailableGrids = availableGridCount.get();
        boolean exceedLimit = chestBagNeedGrids > currentAvailableGrids;

        LOGGER.debug(
                "Checking bag grids limit (dynamic): player={}, needGrids={}, availableGrids={}, maxGrids={}, queueSize={}, exceedLimit={}",
                player.getUid(), chestBagNeedGrids, currentAvailableGrids, STORAGE_MAX_GRID_COUNT,
                availableGridQueue.size(),
                exceedLimit);

        return exceedLimit;
    }

    /**
     * 获取当前剩余可用格子数量（动态版本）
     *
     * @return 剩余可用格子数量
     */
    public int getAvailableGridCount() {
        return availableGridCount.get();
    }

    /**
     * 获取当前已使用的格子数量（动态版本）
     *
     * @return 已使用的格子数量
     */
    public int getCurrentUsedGridCount() {
        return STORAGE_MAX_GRID_COUNT - availableGridCount.get();
    }

    /**
     * 获取空闲格子队列的详细信息（调试用）
     *
     * @return 空闲格子列表（按升序排列）
     */
    public List<Integer> getAvailableGridsList() {
        synchronized (gridManagementLock) {
            return new ArrayList<>(availableGridQueue);
        }
    }

    /**
     * 验证背包整理位置信息
     * 检查客户端提供的位置信息是否合法
     *
     * @param newPositions 客户端整理后的位置信息
     *
     * @return 验证结果
     */
    private NKErrorCode validateOrganizePositions(List<CsChestBag.ChestItemPosition> newPositions) {
        try {
            // 1. 基础参数检查
            if (newPositions == null || newPositions.isEmpty()) {
                return NKErrorCode.ChestBagErrInvalidParam;
            }

            // 2. 收集当前所有有效道具-仅限仓库
            Map<Long, ChestItem> currentItems = new HashMap<>();
            for (int gridId = STORAGE_GRID_ID_BEGIN; gridId <= STORAGE_MAX_GRID_COUNT; gridId++) {
                if (!gridId2ItemUUID.containsKey(gridId)) {
                    continue;
                }
                long itemUUID = gridId2ItemUUID.get(gridId);
                ChestItem item = retrieveItem(itemUUID);
                if (item != null && isItemValid(item.getId())) {
                    currentItems.put(item.getId(), item);
                }
            }

            // 3. 检查道具数量是否匹配
            if (newPositions.size() != currentItems.size()) {
                return NKErrorCode.ChestBagErrItemCountMismatch;
            }

            // 4. 验证每个位置信息
            Set<Integer> usedGrids = new HashSet<>();
            Set<Long> processedItems = new HashSet<>();

            for (CsChestBag.ChestItemPosition position : newPositions) {
                // 4.1 检查格子ID有效性
                if (!isStorage(position.getGridId())) {
                    return NKErrorCode.ChestBagErrInvalidGridId;
                }

                // 4.2 检查格子是否被锁定
                if (isStorageGridLocked(position.getGridId())) {
                    return NKErrorCode.ChestBagErrGridLocked;
                }

                // 4.3 检查格子是否重复使用
                if (usedGrids.contains(position.getGridId())) {
                    return NKErrorCode.ChestBagErrGridOccupied;
                }
                usedGrids.add(position.getGridId());

                // 4.4 检查道具是否存在
                ChestItem item = currentItems.get(position.getItemUuid());
                if (item == null) {
                    return NKErrorCode.ChestBagErrItemNotFound;
                }

                // 4.5 检查道具ID是否匹配
                if (item.getItemId() != position.getItemId()) {
                    return NKErrorCode.ChestBagErrInvalidItemType;
                }

                // 4.6 检查道具是否重复处理
                if (processedItems.contains(position.getItemUuid())) {
                    return NKErrorCode.ChestBagErrDuplicateItem;
                }
                processedItems.add(position.getItemUuid());
            }

            // 5. 检查是否所有道具都被包含
            if (processedItems.size() != currentItems.size()) {
                return NKErrorCode.ChestBagErrItemCountMismatch;
            }

            LOGGER.debug("Organize positions validation passed: player={}, positions={}, usedGrids={}",
                    player.getUid(), newPositions.size(), usedGrids.size());

            return NKErrorCode.OK; // 验证通过

        } catch (Exception e) {
            LOGGER.error("Error validating organize positions: player={}, error={}",
                    player.getUid(), e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 应用新的位置配置
     * 实际更新道具位置，确保数据一致性
     *
     * @param newPositions 新的位置信息
     *
     * @return 操作结果
     */
    private NKErrorCode applyNewPositions(List<CsChestBag.ChestItemPosition> newPositions) {
        // 备份当前状态用于回滚
        Map<Long, Integer> originalPositions = new HashMap<>();
        Map<Integer, Long> originalGridMappings = new HashMap<>(gridId2ItemUUID);

        try {
            LOGGER.debug("Starting to apply new positions: player={}, positions={}",
                    player.getUid(), newPositions.size());

            // 1. 备份原始位置信息
            for (CsChestBag.ChestItemPosition position : newPositions) {
                ChestItem item = retrieveItem(position.getItemUuid());
                if (item != null) {
                    originalPositions.put(position.getItemUuid(), item.getGridId());
                }
            }

            // 2. 清空当前位置映射（准备重建）
            gridId2ItemUUID.clear();

            // 3. 应用新位置配置
            List<CsChestBag.ChestItemPosition> appliedPositions = new ArrayList<>();

            for (CsChestBag.ChestItemPosition position : newPositions) {
                ChestItem item = retrieveItem(position.getItemUuid());
                if (item == null) {
                    // 如果道具不存在，回滚并返回错误
                    rollbackPositions(originalPositions, originalGridMappings);
                    return NKErrorCode.ChestBagErrItemNotFound;
                }

                // 更新道具位置
                int oldGridId = item.getGridId();
                item.setGridId(position.getGridId());

                // 更新位置映射
                gridId2ItemUUID.put(position.getGridId(), position.getItemUuid());

                appliedPositions.add(position);

                LOGGER.debug("Applied position change: player={}, itemUuid={}, itemId={}, oldGrid={}, newGrid={}",
                        player.getUid(), position.getItemUuid(), position.getItemId(), oldGridId, position.getGridId());
            }

            // 4. 重新构建空闲格子队列
            rebuildAvailableGridQueue();

            LOGGER.info("Successfully applied new positions: player={}, totalPositions={}, availableGrids={}",
                    player.getUid(), appliedPositions.size(), availableGridCount.get());

            return NKErrorCode.OK;

        } catch (Exception e) {
            LOGGER.error("Error applying new positions: player={}, error={}",
                    player.getUid(), e.getMessage(), e);

            // 发生异常时回滚
            rollbackPositions(originalPositions, originalGridMappings);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 回滚位置变更
     * 在应用新位置失败时恢复原始状态
     *
     * @param originalPositions    原始道具位置
     * @param originalGridMappings 原始格子映射
     */
    private void rollbackPositions(Map<Long, Integer> originalPositions, Map<Integer, Long> originalGridMappings) {
        try {
            LOGGER.warn("Rolling back position changes: player={}", player.getUid());

            // 1. 恢复道具位置
            for (Map.Entry<Long, Integer> entry : originalPositions.entrySet()) {
                ChestItem item = retrieveItem(entry.getKey());
                if (item != null) {
                    item.setGridId(entry.getValue());
                }
            }

            // 2. 恢复格子映射
            gridId2ItemUUID.clear();
            gridId2ItemUUID.putAll(originalGridMappings);

            // 3. 重新构建空闲格子队列
            rebuildAvailableGridQueue();

            LOGGER.warn("Position rollback completed: player={}", player.getUid());

        } catch (Exception e) {
            LOGGER.error("Error during position rollback: player={}, error={}",
                    player.getUid(), e.getMessage(), e);

            // 如果回滚也失败，强制重新初始化
            initializePositionMappings();
            initializeDynamicGridManagement();
        }
    }

    /**
     * 重新构建空闲格子队列
     * 在位置变更后重新计算可用格子
     */
    private void rebuildAvailableGridQueue() {
        synchronized (gridManagementLock) {
            // 清空队列
            availableGridQueue.clear();

            // 收集已占用的格子ID
            Set<Integer> occupiedGrids = new HashSet<>(gridId2ItemUUID.keySet());

            // 构建空闲格子队列
            for (int gridId = STORAGE_GRID_ID_BEGIN; gridId <= STORAGE_MAX_GRID_COUNT; gridId++) {
                if (!occupiedGrids.contains(gridId) && !lockedGrids.contains(gridId)) {
                    availableGridQueue.offer(gridId);
                }
            }

            // 更新可用格子计数
            availableGridCount.set(availableGridQueue.size());

            LOGGER.debug("Rebuilt available grid queue: player={}, occupiedGrids={}, availableGrids={}, queueSize={}",
                    player.getUid(), occupiedGrids.size(), availableGridCount.get(), availableGridQueue.size());
        }
    }

    public void chestItemRepair(List<Long> itemUUIDList) {
        // pre check
        for (long itemUUID : itemUUIDList) {
            ChestItem item = retrieveItem(itemUUID);
            if (item == null) {
                NKErrorCode.ItemNotExist.throwError("chest item not exist, uid:{} itemUUID:{}",
                        player.getUid(), itemUUID);
                return;
            }
            var itemConf = ChestItemBaseData.getInstance().get(item.getItemId());
            if (itemConf == null) {
                NKErrorCode.ResKeyNotFound.throwError("chest item conf not exist, uid:{} itemUUID:{}",
                        player.getUid(), itemUUID);
            }
        }
        // calc cost
        long totalCost = 0;
        for (long itemUUID : itemUUIDList) {
            ChestItem item = retrieveItem(itemUUID);
            totalCost += getItemAttribute(item, ChestItemAttrType.CIAT_RepairAmount.getNumber()) * item.getNumber();
        }

        // cost
        var minItems = new ChangedItems(ItemChangeReason.ICR_ChestItemRepair.getNumber(), "");
        minItems.mergeItemInfo(CoinType.CT_ChestCoin.getNumber(), totalCost);
        player.getBagManager().MinItems(minItems).throwErrorIfNotOk("chest item repair minItems failed");

        // repair
        for (long itemUUID : itemUUIDList) {
            ChestItem item = retrieveItem(itemUUID);
            var itemConf = ChestItemBaseData.getInstance().get(item.getItemId());
            long maxDurability = itemConf.getMaxDurability();
            setItemAttribute(item, ChestItemAttrType.CIAT_Durability.getNumber(), maxDurability);
        }
    }

    public void chestItemSell(List<Long> itemUUIDList, List<Long> numberList) {
        // pre check
        if (itemUUIDList.size() != numberList.size()) {
            NKErrorCode.InvalidParams.throwError("chest item sell params error, uid:{} itemUUIDList:{} numberList:{}",
                    player.getUid(), itemUUIDList, numberList);
            return;
        }
        long totalCost = 0;
        HashMap<Long, Long> sellItemsMap = new HashMap<>();
        for (int index = 0; index < itemUUIDList.size(); index++) {
            long itemUUID = itemUUIDList.get(index);
            long number = numberList.get(index);
            ChestItem item = retrieveItem(itemUUID);
            if (item == null) {
                NKErrorCode.ItemNotExist.throwError("chest item not exist, uid:{} itemUUID:{}",
                        player.getUid(), itemUUID);
                return;
            }
            if (number > item.getNumber()) {
                NKErrorCode.InvalidParams.throwError("chest item sell number error, uid:{} itemUUID:{} number:{}",
                        player.getUid(), itemUUID, number);
                return;
            }
            if (!sellItemsMap.containsKey(itemUUID)) {
                sellItemsMap.put(itemUUID, number);
                totalCost += number * getItemAttribute(item, ChestItemAttrType.CIAT_SellAmount.getNumber());
            } else {
                NKErrorCode.InvalidParams.throwError("chest item sell duplicate itemUUID, uid:{} itemUUID:{}",
                        player.getUid(), itemUUID);
                return;
            }
        }
        // calc cost

        // cost
        var addItems = new ChangedItems(ItemChangeReason.ICR_ChestItemSell.getNumber(), "");
        addItems.mergeItemInfo(CoinType.CT_ChestCoin.getNumber(), totalCost);

        // repair
        for (Map.Entry<Long, Long> sellInfo : sellItemsMap.entrySet()) {
            ChestItem item = retrieveItem(sellInfo.getKey());
            if (item.getNumber() <= sellInfo.getValue()) {
                removeItem(item.getId(), item.getNumber());
            }
        }

        var ret = player.getBagManager().AddItems2(addItems);
        if (ret.getKey() != NKErrorCode.OK) {
            LOGGER.error("chest item sell add item error, uid:{} ret:{}", player.getUid(), ret.getKey());
            ret.getKey().throwErrorIfNotOk("chest item sell add item error, uid:{} ret:{}",
                    player.getUid(), ret.getKey());
        }
    }

    private static boolean isChestGameType(int matchType) {
        return true;
    }

    public List<CsChestBag.ChestBagInfo> getChestBagInfo() {
        List<CsChestBag.ChestBagInfo> chestBagInfos = new ArrayList<>();
        chestBagInfos.add(CsChestBag.ChestBagInfo.newBuilder()
                .setBagType(0)
                .setOptionalGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getMaxStorageCapacity())
                .setMaxGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getMaxStorageCapacity())
                .build());
        chestBagInfos.add(CsChestBag.ChestBagInfo.newBuilder()
                .setBagType(ChaseSideType.CST_XINGBAO.getNumber())
                .setOptionalGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getPlayerAvailableBagCount())
                .setMaxGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getPlayerMaxBagCount())
                .build());
        chestBagInfos.add(CsChestBag.ChestBagInfo.newBuilder()
                .setBagType(ChaseSideType.CST_DARKSTAR.getNumber())
                .setOptionalGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getBossAvailableBagCount())
                .setMaxGridCount(ChestBagMiscConf.getInstance()
                        .getChestBagMiscConfig().getBossMaxBagCount())
                .build());
        return chestBagInfos;
    }

    private List<Integer> getEquipStorageEmptyGrids(int chestActorType) {
        if (chestActorType == 0) {
            return getAvailableGridsList();
        }
        int beginGridId = 0;
        int endGridId = 0;
        if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
            beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
        } else if (chestActorType == ChaseSideType.CST_XINGBAO.getNumber()) {
            beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
        }
        List<Integer> emptyGrids = new ArrayList<>();
        for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
            if (!gridId2ItemUUID.containsKey(gridId)) {
                emptyGrids.add(gridId);
            }
        }
        return emptyGrids;
    }

    /**
     * 判断是否为宝箱道具类型
     *
     * @param itemType 道具类型
     *
     * @return 是否为宝箱道具
     */
    public static boolean isChestItemType(ItemType itemType) {
        return ChestBagMiscConf.getInstance()
                .getChestBagMiscConfig().getChestItemBagItemTypesList().contains(itemType);
    }

    @Override
    public ItemArray.Builder addItem(ItemInfo itemInfo) {
        ItemArray.Builder result = ItemArray.newBuilder();
        List<ChestItemChangeResult> changeResults = new ArrayList<>();

        if (itemInfo.getItemNum() <= 0) {
            LOGGER.warn("Invalid item number: player={}, itemId={}, number={}",
                    player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum());
            return result;
        }

        try {
            // 获取道具配置
            var itemConfig = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConfig == null) {
                LOGGER.error("Item config not found: player={}, itemId={}",
                        player.getUid(), itemInfo.getItemId());
                return result;
            }

            // 检查背包容量限制
            long remainingToAdd = itemInfo.getItemNum();

            // 获取最大堆叠数量
            int maxStackSize = itemConfig.getStackedNum();
            if (maxStackSize <= 0) {
                maxStackSize = 1; // 默认不堆叠
            }

            LOGGER.debug("Adding chest item: player={}, itemId={}, number={}, maxStackSize={}",
                    player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), maxStackSize);

            // 如果道具支持堆叠，尝试与现有道具合并
            if (maxStackSize > 1) {
                remainingToAdd = tryStackWithExistingItems(itemInfo, remainingToAdd, maxStackSize, result,
                        changeResults);
            }

            // 创建新的道具实例来容纳剩余数量
            while (remainingToAdd > 0) {
                // 检查是否还有可用格子
                if (isBagGirdsExceedLimit(1)) {
                    LOGGER.warn("Bag capacity exceeded: player={}, itemId={}, remainingToAdd={}",
                            player.getUid(), itemInfo.getItemId(), remainingToAdd);
                    break;
                }

                // 计算本次创建的道具数量
                long currentItemNumber = Math.min(remainingToAdd, maxStackSize);

                // 创建新道具实例
                ChestItem newItem = createItem(itemConfig, currentItemNumber, itemInfo);
                if (newItem == null) {
                    LOGGER.error("Failed to create chest item: player={}, itemId={}, number={}",
                            player.getUid(), itemInfo.getItemId(), currentItemNumber);
                    break;
                }

                ItemInfo.Builder newItemInfo = convChestItemToItemInfo(newItem);
                // 记录添加结果
                result.addItems(newItemInfo.setIsNew(true).build());
                changeResults.add(new ChestItemChangeResult()
                        .setItemUUID(newItem.getId())
                        .setItemId(newItem.getItemId())
                        .setGridId(newItem.getGridId())
                        .setChangeType(ChestItemChangeReason.CICR_ItemAdd)
                        .setChangeBefore(0)
                        .setChangeNum(currentItemNumber)
                        .setChangeAfter(currentItemNumber)
                );

                remainingToAdd -= currentItemNumber;

                LOGGER.debug("Created new chest item: player={}, itemId={}, itemUUID={}, number={}, gridId={}",
                        player.getUid(), itemInfo.getItemId(), newItem.getId(), currentItemNumber, newItem.getGridId());
            }

            // 发送添加通知
            if (!changeResults.isEmpty()) {
                notifyItemChange(changeResults);
            }

            // 记录成功添加的数量
            long totalAdded = itemInfo.getItemNum() - remainingToAdd;
            if (totalAdded > 0) {
                LOGGER.info("Successfully added chest items: player={}, itemId={}, requested={}, added={}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), totalAdded);
            }

            if (remainingToAdd > 0) {
                LOGGER.warn(
                        "Partially added chest items due to capacity limit: player={}, itemId={}, requested={}, added={}, remaining={}",
                        player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), totalAdded, remainingToAdd);
            }

            return result;

        } catch (Exception e) {
            LOGGER.error("Error adding chest item: player={}, itemId={}, number={}, error={}",
                    player.getUid(), itemInfo.getItemId(), itemInfo.getItemNum(), e.getMessage(), e);
            return result;
        }
    }

    private void syncEquipChestItemToDs() {
        if (!isChestGameType(player.getCurrentMatchType())) {
            return;
        }
        var battleId = player.getUserAttr().getBattleInfo().getBattleid();
        var dsaInstanceId = player.getUserAttr().getBattleInfo().getDsaInstanceID();
        var sceneId = player.getUserAttr().getBattleInfo().getSceneId();
        var dsSessionId = player.getUserAttr().getBattleInfo().getDsSessionId();
        if (battleId == 0 || dsaInstanceId == 0) {
            LOGGER.debug("player {} openid {} battleId:{} dsaInstanceId:{} syncEquipChestItemToDs skip",
                    player.getUid(), player.getOpenId(), battleId, dsaInstanceId);
            return;
        }
        if (dsSessionId == 0L || sceneId == 0L || dsSessionId == battleId) {
            dsSessionId = battleId;
        }
        long finalDsSessionId = dsSessionId;
        var reqBuilder = SdBattleOuterClass.ChestBuyEquipItemsSyncRequest.newBuilder()
                .setDsSessionId(finalDsSessionId)
                .setDsaInstId(dsaInstanceId)
                .setUid(player.getUid());
        for (ChaseSideType chestActorType : ChaseSideType.values()) {
            if (chestActorType == ChaseSideType.CST_UNKNOWN) {
                continue;
            }
            reqBuilder.addChestSideEquipInfos(SdBattleOuterClass.ChestSideEquipItemsInfo.newBuilder()
                    .setCamp(chestActorType.getNumber())
                    .addAllItemInfos(getEquipItems(chestActorType.getNumber()))
            );
        }

        // 备份备战背包道具
        ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo().getChestBattleInfo(battleId);
        if (chestBattleInfo != null) {
            int chestActorType = player.getUserAttr().getChestGameInfo().getCurrentRole().getActorType();
            player.getChestItemManager().backupEquipBagItems(battleId, chestActorType, chestBattleInfo);
        } else {
            LOGGER.warn("chestBattleInfo is null, uid:{} battleId:{}", player.getUid(), battleId);
        }

        try {
            SdBattleService.get().irpcChestBuyEquipItemsSync(reqBuilder.build());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("irpcChestBuyEquipItemsSync success, uid:{} battleId:{} req:{}",
                        player.getUid(), battleId, reqBuilder.toString());
            }
        } catch (Exception ex) {
            LOGGER.error("irpcChestBuyEquipItemsSync fail", ex);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcChestBuyEquipItemsSync req:{}", reqBuilder.toString());
        }
    }

    public List<ItemInfo> getEquipItems(int chestActorType) {
        ArrayList<ItemInfo> itemInfoList = new ArrayList<>();
        int beginGridId = 0;
        int endGridId = 0;
        if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
            beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
        } else if (chestActorType == ChaseSideType.CST_XINGBAO.getNumber()) {
            beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
        }
        for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
            if (!gridId2ItemUUID.containsKey(gridId)) {
                continue;
            }
            long itemUUID = gridId2ItemUUID.get(gridId);
            if (itemUUID == 0) {
                continue;
            }
            ChestItem item = retrieveItem(itemUUID);
            if (item != null && isItemValid(item.getId())) {
                ItemInfo.Builder itemInfo = convChestItemToItemInfo(item);
                itemInfoList.add(itemInfo.build());
            }
        }
        return itemInfoList;
    }

    /**
     * 获取带入装备的总价值
     *
     * @param actorType
     *
     * @return
     */
    public long getEquipItemsValue(int actorType) {
        long totalValue = 0;
        for (ItemInfo itemInfo : getEquipItems(actorType)) {
            totalValue +=
                    itemInfo.getItemNum() * getItemAttribute(itemInfo, ChestItemAttrType.CIAT_BaseValue.getNumber());
        }
        return totalValue;
    }

    /**
     * 使用道具
     *
     * @param itemUUID  道具UUID
     * @param num       使用数量
     * @param params    使用参数
     * @param reason    使用原因
     * @param subReason 子原因
     *
     * @return 错误码
     */
    public NKErrorCode useItem(long itemUUID, int num, List<Long> params, int reason, long subReason) {
        try {
            LOGGER.debug("player({} {} {}) useItem itemUUID({}) num({}) params({}) reason({}) subReason({})",
                    player.getUid(), player.getName(), player.getOpenId(), itemUUID, num, params, reason, subReason);

            ChestItem item = retrieveItem(itemUUID);
            if (item == null) {
                return NKErrorCode.ItemNotExist;
            }

            NKErrorCode ret = checkUseItem(itemUUID, num, params);
            if (!ret.isOk()) {
                return ret;
            }

            ResBackpackItem.Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                return NKErrorCode.ResNotFound;
            }

            // 首先尝试获取ChestItem专用处理器
            ChestItemUseHandler chestHandler = ChestItemUseRouter.getChestItemHandler(itemConf);
            if (chestHandler == null) {
                return NKErrorCode.ItemCanNotUse;
            }

            // 使用检测
            NKErrorCode checkRet = chestHandler.check(itemConf, player, item, num, params);
            if (!checkRet.isOk()) {
                return checkRet;
            }

            String billNo = BillNoIdGenerator.getBusinessBillNo("useChestItem");

            boolean removeResult = removeItem(itemUUID, num);
            if (!removeResult) {
                LOGGER.error("Failed to remove item: player={}, itemUUID={}, num={}",
                        player.getUid(), itemUUID, num);
                return NKErrorCode.UpdateItemNumByUUIDError;
            }

            chestHandler = ChestItemUseRouter.getChestItemHandler(itemConf);
            return chestHandler.use(itemConf, player, item, num, params, billNo, reason, subReason);
        } catch (Exception e) {
            LOGGER.error("Error using chest item: player={}, itemUUID={}, num={}, error={}",
                    player.getUid(), itemUUID, num, e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 道具有效性验证
     *
     * @param item     道具实例
     * @param itemConf 道具配置
     * @param num      使用数量
     * @param params   使用参数
     *
     * @return 验证结果
     */
    private NKErrorCode validateItemForUse(ChestItem item, ResBackpackItem.Item_BackpackItem itemConf,
            int num, List<Long> params) {
        // 检查道具数量是否足够
        if (item.getNumber() < num) {
            LOGGER.warn("Insufficient item quantity: player={}, itemUUID={}, available={}, required={}",
                    player.getUid(), item.getId(), item.getNumber(), num);
            return NKErrorCode.ItemNumNotEnough;
        }

        // 检查道具是否过期
        if (isItemExpired(item)) {
            LOGGER.warn("Item is expired: player={}, itemUUID={}, itemId={}",
                    player.getUid(), item.getId(), item.getItemId());
            return NKErrorCode.ItemIsExpire;
        }

        // 检查道具版本兼容性
        if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
            LOGGER.warn("Item version incompatible: player={}, itemId={}, clientVersion={}",
                    player.getUid(), item.getItemId(), player.getClientVersion64());
            return NKErrorCode.ItemVersionNotMatch;
        }

        return NKErrorCode.OK;
    }

    /**
     * 检查道具使用条件（不扣除道具）
     *
     * @param itemUUID 道具UUID
     * @param num      使用数量
     * @param params   使用参数
     *
     * @return 检查结果
     */
    public NKErrorCode checkUseItem(long itemUUID, int num, List<Long> params) {
        try {
            LOGGER.debug("Checking item use conditions: player={}, itemUUID={}, num={}, params={}",
                    player.getUid(), itemUUID, num, params);

            // 1. 基础参数校验
            if (num <= 0) {
                return NKErrorCode.ItemUseNumMustBePositive;
            }

            // 2. 获取道具实例
            ChestItem item = retrieveItem(itemUUID);
            if (item == null) {
                return NKErrorCode.ItemNotExist;
            }

            // 3. 获取道具配置
            ResBackpackItem.Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                return NKErrorCode.ResNotFound;
            }

            // 4. 道具有效性验证
            NKErrorCode validationResult = validateItemForUse(item, itemConf, num, params);
            if (validationResult != NKErrorCode.OK) {
                return validationResult;
            }

            return NKErrorCode.OK;

        } catch (Exception e) {
            LOGGER.error("Error checking item use conditions: player={}, itemUUID={}, error={}",
                    player.getUid(), itemUUID, e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 批量使用道具（按道具ID）
     *
     * @param itemId    道具ID
     * @param num       使用数量
     * @param params    使用参数
     * @param reason    使用原因
     * @param subReason 子原因
     *
     * @return 使用结果
     */
    public NKErrorCode useItemByItemId(int itemId, int num, List<Long> params, int reason, long subReason) {
        try {
            LOGGER.info("Using items by itemId: player={}, itemId={}, num={}, params={}, reason={}, subReason={}",
                    player.getUid(), itemId, num, params, reason, subReason);

            // 1. 获取所有符合条件的道具
            List<ChestItem> candidateItems = getItemsByItemIdSortedByCreationTime(itemId);
            if (candidateItems.isEmpty()) {
                LOGGER.warn("No items found for use: player={}, itemId={}", player.getUid(), itemId);
                return NKErrorCode.ItemNotExist;
            }

            // 2. 检查总数量是否足够
            long totalAvailable = candidateItems.stream().mapToLong(ChestItem::getNumber).sum();
            if (totalAvailable < num) {
                LOGGER.warn("Insufficient total item quantity: player={}, itemId={}, available={}, required={}",
                        player.getUid(), itemId, totalAvailable, num);
                return NKErrorCode.ItemNumNotEnough;
            }

            // 3. 按FIFO顺序使用道具
            int remainingToUse = num;
            for (ChestItem item : candidateItems) {
                if (remainingToUse <= 0) {
                    break;
                }

                if (!isItemValid(item.getId())) {
                    continue;
                }

                int useFromThisItem = (int) Math.min(remainingToUse, item.getNumber());
                NKErrorCode useResult = useItem(item.getId(), useFromThisItem, params, reason, subReason);

                if (useResult != NKErrorCode.OK) {
                    LOGGER.error("Failed to use item in batch: player={}, itemUUID={}, error={}",
                            player.getUid(), item.getId(), useResult);
                    return useResult;
                }

                remainingToUse -= useFromThisItem;
                LOGGER.debug("Used item in batch: player={}, itemUUID={}, used={}, remaining={}",
                        player.getUid(), item.getId(), useFromThisItem, remainingToUse);
            }

            if (remainingToUse > 0) {
                LOGGER.error("Failed to use all required items: player={}, itemId={}, remaining={}",
                        player.getUid(), itemId, remainingToUse);
                return NKErrorCode.ItemNumNotEnough;
            }

            LOGGER.info("Successfully used items by itemId: player={}, itemId={}, num={}",
                    player.getUid(), itemId, num);

            return NKErrorCode.OK;

        } catch (Exception e) {
            LOGGER.error("Error using items by itemId: player={}, itemId={}, num={}, error={}",
                    player.getUid(), itemId, num, e.getMessage(), e);
            return NKErrorCode.UnknownError;
        }
    }

    /**
     * 判断是否为宝箱道具类型
     *
     * @param itemType 道具类型
     *
     * @return 是否为宝箱道具
     */
    public static boolean canBringInChestGame(ItemType itemType) {
        return ChestBagMiscConf.getInstance()
                .getChestBagMiscConfig().getChestBringInItemTypesList().contains(itemType);
    }

    public CsChestBag.ChestMallBuyMsg_S2C_Msg.Builder chestMallBuyMsg(ChestMallBuyMsg_C2S_Msg reqMsg) {
        CsChestBag.ChestMallBuyMsg_S2C_Msg.Builder rspMsg = CsChestBag.ChestMallBuyMsg_S2C_Msg.newBuilder();
        // pre check
        HashMap<Integer, ItemInfo.Builder> itemBuyMap = new HashMap<>();
        HashMap<Integer, Integer> commodityBuyMap = new HashMap<>();

        String businessBillNo = BillNoIdGenerator.getBusinessBillNo("mall");

        ChangedItems costItems = new ChangedItems(ItemChangeReason.ICR_ChestItemBuyAndEquip.getNumber(), "");
        costItems.setBusBillNo(businessBillNo);

        int chestActorType = 0;
        for (CsChestBag.ChestEquipBuyInfo buyMsgInfo : reqMsg.getBugMsgList()) {
            CsMall.MallBuyMsg_C2S_Msg buyMsg = buyMsgInfo.getBugMsg();
            if (buyMsg.getBuyNum() <= 0) {
                NKErrorCode.InvalidParams.throwError("chest mall buy num error, uid:{} buyNum:{}",
                        player.getUid(), buyMsg.getBuyNum());
                return rspMsg;
            }
            ResMall.MallCommodity commodityConf = MallCommodityConf.getInstance().get(buyMsg.getCommodityId());
            if (commodityConf == null) {
                NKErrorCode.ResKeyNotFound.throwError("chest mall buy commodity not exist, uid:{} commodityId:{}",
                        player.getUid(), buyMsg.getCommodityId());
                return rspMsg;
            }
            if (commodityConf.getItemIdsCount() > 1) { // 只能配置一个道具
                NKErrorCode.InvalidParams.throwError("chest mall buy commodity item count error, uid:{} commodityId:{}",
                        player.getUid(), buyMsg.getCommodityId());
                return rspMsg;
            }
            NKErrorCode errorCode = player.getMallManager().checkCommodityBuy(commodityConf, buyMsg.getBuyNum());
            if (errorCode != NKErrorCode.OK) {
                errorCode.throwError("checkCommodityBuy error, commodityId:{} buyNum:{}",
                        commodityConf.getCommodityId(), buyMsg.getBuyNum());
                return rspMsg;
            }
            long finalUnitPrice = commodityConf.getDiscountPrice() > 0 ?
                    commodityConf.getDiscountPrice() : commodityConf.getPrice();
            costItems.mergeItemInfo(commodityConf.getCoinType(), finalUnitPrice * buyMsg.getBuyNum());

            // 判断是否是chest道具
            int itemId = commodityConf.getItemIds(0);
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (!ItemManagerFactory.isChestItemType(itemConf.getType())) {
                NKErrorCode.ChestItemTypeErr.throwError(
                        "chest mall buy item type error, uid:{} commodityId:{} itemId:{} type:{}",
                        player.getUid(), commodityConf.getCommodityId(), itemId, itemConf.getType());
                return rspMsg;
            }
            // chestItem chestActorType 校验
            ChestItemBaseConfig chestItemBaseConfig = ChestItemBaseData.getInstance().get(itemId);
            if (chestItemBaseConfig == null) {
                NKErrorCode.ResKeyNotFound.throwError("chest mall buy chest item not exist, uid:{} itemId:{}",
                        player.getUid(), itemId);
                return rspMsg;
            }
            // 校验是否是空格子
            if (gridId2ItemUUID.containsKey(buyMsgInfo.getTargetGridId())) {
                NKErrorCode.InvalidParams.throwError("chest mall buy chest item grid error, uid:{} itemId:{} gridId:{}",
                        player.getUid(), itemId, buyMsgInfo.getTargetGridId());
                return rspMsg;
            }

            if (chestItemBaseConfig.getCamp().getNumber() > 0) {
                if (isXingBaoEquipStorage(buyMsgInfo.getTargetGridId())
                        && chestItemBaseConfig.getCamp() == ChaseSideType.CST_XINGBAO
                        || isDarkStarEquipStorage(buyMsgInfo.getTargetGridId())
                        && chestItemBaseConfig.getCamp() == ChaseSideType.CST_DARKSTAR) {
                    // nothing to do
                } else {
                    NKErrorCode.ChestMallBuyItemGridErr.throwError(
                            "chest mall buy chest item chestActorType error, uid:{} itemId:{} chestActorType:{}",
                            player.getUid(), itemId, chestItemBaseConfig.getCamp().getNumber());
                    return rspMsg;
                }

                if (chestActorType == 0) {
                    chestActorType = chestItemBaseConfig.getCamp().getNumber();
                } else if (chestActorType != chestItemBaseConfig.getCamp().getNumber()) {
                    NKErrorCode.ChestMallBuyItemCampErr.throwError(
                            "chest mall buy chest item chestActorType error, uid:{} itemId:{} chestActorType:{}",
                            player.getUid(), itemId, chestItemBaseConfig.getCamp().getNumber());
                    return rspMsg;
                }
            }

            if (itemBuyMap.containsKey(buyMsgInfo.getTargetGridId())) {
                NKErrorCode.InvalidParams.throwError("chest mall buy duplicate grid id, uid:{} gridId:{}",
                        player.getUid(), buyMsgInfo.getTargetGridId());
                return rspMsg;
            }
            itemBuyMap.put(buyMsgInfo.getTargetGridId(),
                    ItemInfo.newBuilder().setItemId(commodityConf.getItemIds(0))
                            .setItemNum((long) commodityConf.getItemNums(0) * buyMsg.getBuyNum()));
            // 添加购买道具
            commodityBuyMap.compute(buyMsg.getCommodityId(),
                    (k, v) -> v == null ? buyMsg.getBuyNum() : v + buyMsg.getBuyNum());
        }

        if (!player.getMallManager().checkCanBatchBuy(commodityBuyMap.keySet())) {
            NKErrorCode.MallCommodityCanNotBatchBuy.throwError("checkCanBatchBuy error, commodity ids:{}",
                    commodityBuyMap.keySet());
            return rspMsg;
        }

        // 扣钱
        player.getBagManager().MinItems(costItems)
                .throwErrorIfNotOk("chest mall buy minItems error, uid:{} costItems:{}",
                        player.getUid(), costItems);

        // 添加购买记录
        for (Map.Entry<Integer, Integer> entry : commodityBuyMap.entrySet()) {
            int commodityId = entry.getKey();
            int buyNum = entry.getValue();
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
            player.getMallManager().addCommodityBuyRecord(commodityConf, buyNum);
        }

        // 发货
        for (Map.Entry<Integer, ItemInfo.Builder> entry : itemBuyMap.entrySet()) {
            int gridId = entry.getKey();
            ItemInfo itemInfo = entry.getValue().build();
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            ChestItem item = createItemWithGridId(itemConf, itemInfo.getItemNum(), itemInfo, gridId);
            if (item == null) {
                LOGGER.error("chest mall buy createItem error, uid:{} itemInfo:{}",
                        player.getUid(), itemInfo);
                continue;
            }
            rspMsg.addItemPositions(ChestItemPosition.newBuilder().setItemUuid(item.getId())
                    .setGridId(item.getGridId())
                    .setItemId(item.getItemId()).build());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("chest mall buy createItem success, uid:{} gridId:{} item:{}",
                        player.getUid(), gridId, item);
            }
        }
        // sync to Ds
        syncEquipChestItemToDs();
        return rspMsg;
    }

    // 对局开始备份备战背包道具
    public void backupEquipBagItems(long battleId, int chestActorType, ChestBattleInfo chestBattleInfo) {
        if (battleId == 0) {
            return;
        }
        int beginGridId = 0;
        int endGridId = 0;
        if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
            beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
        } else {
            beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
        }
        chestBattleInfo.clearChestEquipItemsBackup();
        for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
            if (!gridId2ItemUUID.containsKey(gridId)) {
                continue;
            }
            long itemUUID = gridId2ItemUUID.get(gridId);
            if (itemUUID == 0) {
                continue;
            }
            ChestItem item = retrieveItem(itemUUID);
            if (item != null) {
                chestBattleInfo.putChestEquipItemsBackup(item.getId(), item);
//                removeItem(item.getId(), item.getNumber());
            }
        }
        LOGGER.info("backupEquipBagItems success, uid:{} battleId:{} chestActorType:{}",
                player.getUid(), battleId, chestActorType);
    }

    // 清空备战背包道具 玩家阵亡和中途退出
    public void clearEquipBagItems(long battleId, int chestActorType) {
        if (battleId == 0) {
            return;
        }
        LOGGER.info("clearEquipBagItems success, uid:{} battleId:{} chestActorType:{}",
                player.getUid(), battleId, chestActorType);

        int beginGridId = 0;
        int endGridId = 0;
        if (chestActorType == ChaseSideType.CST_DARKSTAR.getNumber()) {
            beginGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = DARKSTAR_EQUIP_STORAGE_GRID_ID_END;
        } else {
            beginGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_BEGIN;
            endGridId = XINGBAO_EQUIP_STORAGE_GRID_ID_END;
        }
        for (int gridId = beginGridId + 1; gridId <= endGridId; gridId++) {
            if (!gridId2ItemUUID.containsKey(gridId)) {
                continue;
            }
            long itemUUID = gridId2ItemUUID.get(gridId);
            if (itemUUID == 0) {
                continue;
            }
            ChestItem item = retrieveItem(itemUUID);
            if (item != null) {
                removeItem(item.getId(), item.getNumber());
            }
        }
    }

    public static class ChestItemChangeResult {

        private long itemUUID;
        private int itemId;
        private int gridId;
        private CsChestBag.ChestItemChangeReason changeType;
        private long changeBefore;
        private long changeNum;
        private long changeAfter;

        public ChestItemChangeResult() {
        }

        public long getItemUUID() {
            return itemUUID;
        }

        public ChestItemChangeResult setItemUUID(long itemUUID) {
            this.itemUUID = itemUUID;
            return this;
        }

        public int getItemId() {
            return itemId;
        }

        public ChestItemChangeResult setItemId(int itemId) {
            this.itemId = itemId;
            return this;
        }

        public int getGridId() {
            return gridId;
        }

        public ChestItemChangeResult setGridId(int gridId) {
            this.gridId = gridId;
            return this;
        }

        public CsChestBag.ChestItemChangeReason getChangeType() {
            return changeType;
        }

        public ChestItemChangeResult setChangeType(
                CsChestBag.ChestItemChangeReason changeType) {
            this.changeType = changeType;
            return this;
        }

        public long getChangeBefore() {
            return changeBefore;
        }

        public ChestItemChangeResult setChangeBefore(long changeBefore) {
            this.changeBefore = changeBefore;
            return this;
        }

        public long getChangeNum() {
            return changeNum;
        }

        public ChestItemChangeResult setChangeNum(long changeNum) {
            this.changeNum = changeNum;
            return this;
        }

        public long getChangeAfter() {
            return changeAfter;
        }

        public ChestItemChangeResult setChangeAfter(long changeAfter) {
            this.changeAfter = changeAfter;
            return this;
        }

        public String toString() {
            return "ChestItemChangeResult{" +
                    "itemUUID=" + itemUUID +
                    ", itemId=" + itemId +
                    ", gridId=" + gridId +
                    ", changeType=" + changeType +
                    ", changeBefore=" + changeBefore +
                    ", changeNum=" + changeNum +
                    ", changeAfter=" + changeAfter +
                    '}';
        }

        public enum ChestItemChangeType {
            CompletelyRemoved,
            PartiallyRemoved,
            NotChanged
        }
    }
}

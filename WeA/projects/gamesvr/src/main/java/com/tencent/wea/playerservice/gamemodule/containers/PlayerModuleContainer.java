package com.tencent.wea.playerservice.gamemodule.containers;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.gamemodule.GameModule;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 模块容器
 *
 * <AUTHOR>
 * @date 2019/5/28
 */
public class PlayerModuleContainer extends GameModule.ModuleContainer<PlayerModule> {

    private static final Logger LOGGER = LogManager.getLogger(PlayerModuleContainer.class);

    private boolean loadedWithLoginInfo = false;

    public int modulesPrepareRegister() {
        return forEachModule(m -> {
            try {
                m.prepareRegister();
                return true;
            } catch (Exception e) {
                LOGGER.error("failed to prepareRegister, moduleId: {}, Exception: ", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_prepare_register_exception, 1);
                return false;
            }
        }, "PlayerModuleContainerPrepareRegister");
    }

    public int modulesOnRegister() {
        return forEachModule(m -> {
            try {
                m.onRegister();
                return true;
            } catch (NKCheckedException e) {
                LOGGER.error("failed to onRegister, moduleId: {}, Exception: ", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_register_exception, 1);
                return false;
            }
        }, "PlayerModuleContainerOnRegister");
    }

    public void modulesAfterRegister() {
        forEachModule(m -> {
            try {
                m.afterRegister();
            } catch (Exception e) {
                LOGGER.error("failed to afterRegister, moduleId: {}, Exception: ", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_register_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterRegister");
    }

    public int modulesPrepareLoad() {
        return forEachModule(m -> {
            try {
                m.prepareLoad();
            } catch (NKCheckedException e) {
                Monitor.getInstance().add.total(MonitorId.attr_modules_prepare_load_exception, 1);
                e.getEnumErrCode().throwErrorIfNotOk(e.getMessage());
            }
            return true;
        }, "PlayerModuleContainerPrepareLoad");
    }

    public int modulesOnLoad() {
        return forEachModule(m -> {
            try {
                m.onLoad();
            } catch (NKCheckedException e) {
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_load_exception, 1);
                e.getEnumErrCode().throwErrorIfNotOk(e.getMessage());
            }
            return true;
        }, "PlayerModuleContainerOnLoad");
    }

    public void modulesLoadedWithLoginInfo() {
        if (loadedWithLoginInfo) {
            return;
        }
        forEachModule(m -> {
            try {
                m.loadedWithLoginInfo();
            } catch (Exception e) {
                LOGGER.error("after load module {} failed. e:", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_load_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterLoad");
        loadedWithLoginInfo = true;
    }

    public void modulesAfterLoad() {
        forEachModule(m -> {
            try {
                m.afterLoad();
            } catch (Exception e) {
                LOGGER.error("after load module {} failed. e:", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_load_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterLoad");
    }

    public void modulesPrepareLogin() {
        forEachModule(m -> {
            try {
                //if(ConfigUtils.isModuleSwitchOn(m.getModuleId().getNumber())) {
                if (m.isModuleEnable()) {
                    m.prepareLogin();
                } else {
                    LOGGER.debug("modulesPrepareLogin module not open, module id: {}", m.getModuleId());
                }
                return true;
            } catch (Exception e) {
                LOGGER.error("failed to prepareLogin, module id: {}", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_prepare_login_exception, 1);
                throw new NKRuntimeException(NKErrorCode.ModuleInitFail, "", e);
            }
        }, "PlayerModuleContainerPrepareLogin");
    }

    public void modulesOnLogin() {
        forEachModule(m -> {
            try {
                //if(ConfigUtils.isModuleSwitchOn(m.getModuleId().getNumber())) {
                if (m.isModuleEnable()) {
                    m.onLogin();
                } else {
                    LOGGER.debug("modulesOnLogin module not open, module id: {}", m.getModuleId());
                }
                return true;
            } catch (NKCheckedException e) {
                LOGGER.error("failed to onLogin, module id: {}", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_login_exception, 1);
                throw new NKRuntimeException(NKErrorCode.ModuleInitFail, "", e);
            }
        }, "PlayerModuleContainerOnLogin");
    }

    public void modulesAfterLoginConcurrently(boolean todayFirstLogin) {
        int concurrentCount = PropertyFileReader.getRealTimeIntItem("module_concurrent_limit", 50);
        if (concurrentCount <= 0) {
            concurrentCount = 10;
        }
        LOGGER.debug("modulesAfterLoginConcurrently {}", concurrentCount);
        Function<PlayerModule, Boolean> func = (PlayerModule m) -> {
            try {
                if (m.isModuleEnable()) {
                    m.afterLogin(todayFirstLogin);
                } else {
                    LOGGER.debug("modulesAfterLoginConcurrently module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("failed to modulesAfterLoginConcurrently, module id: {}", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_login_exception, 1);
                return false;
            }
            return true;
        };
        List<PlayerModule> mList = new ArrayList<PlayerModule>();
        forEachModule(m -> {
            mList.add(m);
            return true;
        }, "modulesAfterLoginConcurrently");
        CurrentExecutorUtil.partBatchSubmitJob(mList, func, Math.max(1, mList.size()/concurrentCount), 5000);
    }

    public void modulesAfterLogin(boolean todayFirstLogin) {
        forEachModule(m -> {
            try {
                //if(ConfigUtils.isModuleSwitchOn(m.getModuleId().getNumber())) {
                if (m.isModuleEnable()) {
                    m.afterLogin(todayFirstLogin);
                } else {
                    LOGGER.debug("modulesAfterLogin module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("failed to afterLogin, module id: {}", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_login_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterLogin");
    }

    public void modulesAfterLoginFinish(boolean todayFirstLogin) {
        forEachModule(m -> {
            try {
                if (m.isModuleEnable()) {
                    m.afterLoginFinish(todayFirstLogin);
                } else {
                    LOGGER.debug("modulesAfterLoginFinish module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("failed to afterLoginFinish, module id: {}", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_login_finish_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterLoginFinish");
    }

    public void modulesOnMidnight() {
        forEachModule(m -> {
            try {
                m.onMidNight();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_midnight_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnMidnight");
    }

    public void modulesOnMidnightBefore() {
        forEachModule(m -> {
            try {
                m.onMidNightBefore();
            } catch (Exception e) {
                LOGGER.error("", e);
            }
            return true;
        }, "PlayerModuleContainerOnMidnightBefore");
    }

    public void modulesOnWeekStart() {
        forEachModule(m -> {
            try {
                m.onWeekStart();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_week_start_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnWeekStart");
    }

    public void modulesOnDisconnect() {
        forEachModule(m -> {
            try {
                if (m.isModuleEnable()) {
                    m.onDisconnect();
                } else {
                    LOGGER.debug("modulesOnLogout module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("", e);
            }
            return true;
        }, "PlayerModuleContainerOnLeave");
    }

    public void modulesOnLeave() {
        forEachModule(m -> {
            try {
                if (m.isModuleEnable()) {
                    m.onLeave();
                } else {
                    LOGGER.debug("modulesOnLogout module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("", e);
            }
            return true;
        }, "PlayerModuleContainerOnLeave");
    }

    public void modulesOnLogout() {
        forEachModule(m -> {
            try {
                //if(ConfigUtils.isModuleSwitchOn(m.getModuleId().getNumber())) {
                if (m.isModuleEnable()) {
                    m.onLogout();
                } else {
                    LOGGER.debug("modulesOnLogout module not open, module id: {}", m.getModuleId());
                }
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_logout_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnLogout");
    }

    public void modulesOnDayThreeStart() {
        forEachModule(m -> {
            try {
                m.onDayThreeStart();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_week_start_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnDayThreeStart");
    }


    public void modulesOnDayFiveStart() {
        forEachModule(m -> {
            try {
                m.onDayFiveStart();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_logout_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnDayFiveStart");
    }


    public void modulesOnDaySixStart() {
        forEachModule(m -> {
            try {
                m.onDaySixStart();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_logout_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnDaySixStartt");
    }

    public void modulesOnEveryHourStart() {
        forEachModule(m -> {
            try {
                m.onEveryHourStart();
            } catch (Exception e) {
                LOGGER.error("", e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_on_everyhour_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerOnEveryHourStart");
    }

    public void modulesAfterRefreshABTest() {
        forEachModule(m -> {
            try {
                m.afterRefreshABTest();
            } catch (Exception e) {
                LOGGER.error("after refresh abtest module {} failed. e:", m.getModuleId(), e);
                Monitor.getInstance().add.total(MonitorId.attr_modules_after_refresh_abtest_exception, 1);
            }
            return true;
        }, "PlayerModuleContainerAfterRefreshABTest");
    }

}
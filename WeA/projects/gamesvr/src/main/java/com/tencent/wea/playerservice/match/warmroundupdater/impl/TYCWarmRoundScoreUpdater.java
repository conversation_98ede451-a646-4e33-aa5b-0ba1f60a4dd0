package com.tencent.wea.playerservice.match.warmroundupdater.impl;

import com.tencent.nk.commonframework.Framework;
import com.tencent.wea.battleresult.GlobalBattleResult;
import com.tencent.wea.playerservice.match.PlayerMatchWarmRoundManager;
import com.tencent.wea.playerservice.match.warmroundupdater.WarmRoundScoreEndBattleUpdater;
import com.tencent.wea.playerservice.match.warmroundupdater.WarmRoundScoreTlogParam;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tlog.flow.TlogMacros;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.keywords.MatchWarmRoundScoreCase;
import com.tencent.wea.xlsRes.keywords.WarmRoundScoreType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 塔防一系列温暖局更新器的base cls
 * 特点是
 */
public abstract class TYCWarmRoundScoreUpdater extends WarmRoundScoreEndBattleUpdater {
    protected static final Logger LOGGER = LogManager.getLogger(TYCWarmRoundScoreUpdater.class);


    protected static final int TYC_BATTLE_RESULT_VICTORY = 0;
    protected static final int TYC_BATTLE_RESULT_FAIL = 1;

    public TYCWarmRoundScoreUpdater(Player player, PlayerMatchWarmRoundManager warmRoundManager) {
        super(player, warmRoundManager);
    }

    @Override
    public void updateWarmRoundScore(ResMatch.MatchType matchTypeConf,
                                     GlobalBattleResult battleResult,
                                     PlayerInteraction.PiiDsSettlementParams settlementInfo) {
        try {
            WarmRoundScoreType warmRoundScoreType = matchTypeConf.getWarmRoundType();
            if (WarmRoundScoreType.WRST_Invalid == warmRoundScoreType) {
                LOGGER.error("player {} battle {} warmRoundScoreType is invalid",
                        player.getUid(), battleResult.getBattleId());
                return;
            }

            MatchWarmRoundScoreCase scoreCase = getMatchedWarmRoundScoreCase(matchTypeConf,
                    battleResult,
                    settlementInfo);

            LOGGER.info("TYCWarmRoundScoreUpdater log, uid: {} scoreCase: {}", player.getUid(), scoreCase);
            if (null == scoreCase) {
                LOGGER.warn("player {} battle {} getFpsMatchedWarmRoundScoreCase ret null",
                        player.getUid(), battleResult.getBattleId());
                return;
            }

            WarmRoundScoreTlogParam tlogParam = new WarmRoundScoreTlogParam.Builder(warmRoundScoreType,
                    TlogMacros.WARM_ROUND_SCORE_CHANGE_REASON.WRSCR_AfterBattle).build();
            long battleEndTime = Framework.currentTimeMillis();
            warmRoundManager.addWarmRoundScoreChange(warmRoundScoreType, scoreCase,
                    battleEndTime, false, tlogParam);
        } catch (Exception e) {
            LOGGER.error("player {} battle {} TYCWarmRoundScoreUpdater error",
                    player.getUid(), battleResult.getBattleId(), e);
        }

    }


    public abstract MatchWarmRoundScoreCase getMatchedWarmRoundScoreCase(ResMatch.MatchType matchTypeConf,
                                                                         GlobalBattleResult battleResult,
                                                                         PlayerInteraction.PiiDsSettlementParams settlementInfo);


}

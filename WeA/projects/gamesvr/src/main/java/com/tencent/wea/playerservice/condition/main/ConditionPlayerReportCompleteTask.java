package com.tencent.wea.playerservice.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.PlayerReportCompleteTaskEvent;
import com.tencent.wea.playerservice.task.RunTask;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.TaskStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ConditionPlayerReportCompleteTask extends BasePlayerCondition {

    private static final Logger LOGGER = LogManager.getLogger(ConditionPlayerReportCompleteTask.class);

    /**
     * 构造函数
     */
    public ConditionPlayerReportCompleteTask() {
        super();
    }

    /**
     * 用来唯一表示一个条件类所属的条件类型，用来反射注册到条件类管理容器
     *
     * @return 条件类型
     */
    @Override
    public int getType() { return ConditionType.ConditionType_PlayerReportCompleteTask_VALUE; }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerReportCompleteTask)
    private void onEvent(PlayerReportCompleteTaskEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    public boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        PlayerReportCompleteTaskEvent reportCompleteTaskEvent = (PlayerReportCompleteTaskEvent) event;
        RunTask taskInfo = event.getPlayer().getTaskManager().getTask(reportCompleteTaskEvent.getTaskId());
        if (taskInfo == null){
            return false;
        }
        LOGGER.debug("ConditionPlayerReportCompleteTask handleProgress, taskId:{} status:{}",
                reportCompleteTaskEvent.getTaskId(), taskInfo.getStatus().getNumber());
        if (checkSubCondition(event, progress.getSubConditionList())){
            LOGGER.debug("ConditionPlayerReportCompleteTask check pass, taskId:{}",
                    reportCompleteTaskEvent.getTaskId());
            if (taskInfo.getStatus().getNumber() <= TaskStatus.TS_Triggered_VALUE) {
                return progress.increaseValue();
            }
        }
        return false;
    }
}

package com.tencent.wea.playerservice.condition.sub;

import com.tencent.wea.playerservice.condition.BasePlayerSubCondition;
import com.tencent.wea.playerservice.event.common.PlayerPlayUgcMapEvent;
import com.tencent.wea.playerservice.event.common.PlayerPublishUgcTopicEvent;
import com.tencent.wea.playerservice.event.common.PlayerThirdPartyTaskEvt;
import com.tencent.wea.xlsRes.keywords.SubConditionType;

import java.util.Collection;
import java.util.List;

public class SubConditionFinishDreamClass extends BasePlayerSubCondition {

    public SubConditionFinishDreamClass() {

    }

    @Override
    public int getType() {
        return SubConditionType.SCT_FinishDreamClass_VALUE;
    }

    @Override
    public boolean isOk(List<Long> valueList, Object object) {
        if (object instanceof PlayerThirdPartyTaskEvt) {
            PlayerThirdPartyTaskEvt event = (PlayerThirdPartyTaskEvt) object;
            return this.getType() == event.getSubconditionType();
        }
        return false;
    }
}

package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.rpc.service.ActivityService;
import java.util.List;

/**
 * 钓鱼名人堂GM, 转发到 ActivitySvr
 */
public class GmFishingHallOfFameActivity implements GmHandler {

    @Override
    public int handle(Player player, List<String> param) throws NKTimeoutException, RpcException {
        if (param.isEmpty()) {
            return NKErrorCode.InvalidParams.getValue();
        }

        SsCommon.TransmitGMCommandWithTargetUIDReq.Builder req = SsCommon.TransmitGMCommandWithTargetUIDReq.newBuilder()
                .setCmdName(this.getClass().getSimpleName())
                .setUid(player.getUid())
                .addAllParam(param);
        return ActivityService.get().transmitGMCommandWithTargetUID(req).getRet();
    }
}

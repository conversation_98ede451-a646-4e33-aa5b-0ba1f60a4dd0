package com.tencent.wea.playerservice.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.condition.BasePlayerSubCondition;
import com.tencent.wea.playerservice.condition.PlayerConditionRegistry;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.PlayMapCompleteEvent;
import com.tencent.wea.playerservice.event.common.PlayerPlayMapCountEvent;
import com.tencent.wea.xlsRes.ResCondition;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ConditionPlayMapComplete extends BasePlayerCondition {
    private static final Logger LOGGER = LogManager.getLogger(ConditionPlayMapComplete.class);

    /**
     * 构造函数
     */
    public ConditionPlayMapComplete() {

    }

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayMapComplete_VALUE;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayMapComplete)
    private void onEvent(PlayMapCompleteEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    public boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        long ugcId = ((PlayMapCompleteEvent) event).getUgcId();
        if (checkSubCondition(ugcId, progress)) {
            return progress.addValue(1);
        }
        return false;
    }

    protected boolean checkSubCondition(Object obj, ConditionOperation conditionProgress) {
        for (ResCondition.ResSubConditionInfo resSubCondition : conditionProgress.getSubConditionList()) {
            BasePlayerSubCondition subCondition = PlayerConditionRegistry.getInstance()
                    .getSubCondition(resSubCondition.getType());
            if (!subCondition.isOk(resSubCondition.getValueList(), obj)) {
                return false;
            }
        }
        return true;
    }
}

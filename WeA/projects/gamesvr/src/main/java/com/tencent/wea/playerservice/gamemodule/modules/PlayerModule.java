package com.tencent.wea.playerservice.gamemodule.modules;

import com.google.protobuf.Message;
import com.google.protobuf.Message.Builder;
import com.tencent.timiutil.gamemodule.GameModule;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.keywords.GameModuleId;

import java.util.HashMap;
import java.util.function.Function;

/**
 * 模块
 *
 * <AUTHOR>
 * @date 2019/5/28
 */
public abstract class PlayerModule extends GameModule.Module {

    protected final Player player;
    private final GameModuleId module;
    protected HashMap<Integer, Function<Message, Builder>> allCmd;

    public PlayerModule(GameModuleId module, Player player) {
        this.module = module;
        this.player = player;
        this.player.addModule(this);
    }

    /**
     * 增加模块内部执行函数
     *
     * @param req
     * @throws NKCheckedException
     */
    public Builder executeCmd(int id, Message req) {
        if (allCmd == null) {
            allCmd = new HashMap<>();
        }

        if (isModuleEnable()) {
            Function<Message, Builder> fuc = allCmd.get(id);
            if (fuc == null) {
                NKErrorCode.ModuleCmdError.throwError("PlayerModule executeCmd fuc == null,id:{} playerid:{}", id,
                        player.getUid());
                return null;
            }
            return fuc.apply(req);
        } else {
            NKErrorCode.ModuleOpenError.throwError("PlayerModule executeCmd module not open,id:{} playerid:{}", id,
                    player.getUid());
        }
        return null;
    }

    @Override
    public final GameModuleId getModuleId() {
        return module;
    }

    public final Player getPlayer() {
        return player;
    }

    /*
      注册时的调用顺序:
      prepareRegister->onRegister->*afterRegister->prepareLoad->onLoad->*afterLoad->prepareLogin->onLogin->*afterLogin->afterLoginFinish

       加载时的调用顺序:
      prepareLoad->onLoad->*afterLoad

      登陆时的调用顺序:
      prepareLogin->onLogin->*afterLogin->afterLoginFinish

      ‘*’标记的流程将在另启协程异步执行
     */

    /**
     * 玩家注册时, 本模块内部的注册相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void prepareRegister() throws NKCheckedException;

    /**
     * 玩家注册时, 本模块跨模块的注册相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void onRegister() throws NKCheckedException;

    /**
     * 玩家注册后, 本模块后续的注册相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    public abstract void afterRegister() throws NKCheckedException;

    /**
     * 玩家加载时, 本模块内部的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void prepareLoad() throws NKCheckedException;

    /**
     * 玩家加载时, 本模块跨模块的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void onLoad() throws NKCheckedException;

    /**
     * 玩家加载后, 本模块后续的加载相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    public abstract void afterLoad();

    /**
     * 玩家登录时, 本模块内部的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void prepareLogin() throws NKCheckedException;

    /**
     * 玩家登录时, 本模块跨模块的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    public abstract void onLogin() throws NKCheckedException;

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     * 当前登录逻辑还强依赖afterlogin的推送，所以afterlogin已经变成登录时的调用了，登录后的调用用 afterLoginFinish 接口
     *
     * @param todayFirstLogin
     */
    public abstract void afterLogin(boolean todayFirstLogin);

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    public abstract void afterLoginFinish(boolean todayFirstLogin);

    /**
     * 玩家登出时, 本模块的登出相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     */
    public abstract void onLogout();

    /**
     * 凌晨刷新逻辑
     * 只要上次刷新时间小于今天开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    public abstract void onMidNight();

    /**
     * 凌晨前刷新刷新逻辑（暂定0点前5分钟内，超过不执行）
     * 只要上次刷新时间小于今天开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    public void onMidNightBefore() {

    }

    /**
     * 每周刷新逻辑
     * 只要上次刷新时间本周开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    public abstract void onWeekStart();

//    /**
//     * 周刷新逻辑
//     * 只要上次刷新时间小于本周开始时间，产生一次刷新
//     * 抛出异常不会影响调用其他模块的该接口
//     */
//    public abstract void onWeeklyStart();

    /**
     * 玩家断开连接，本模块的相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     */
    public void onDisconnect() {

    }

    /**
     * 玩家暂时离开，本模块的相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     */
    public void onLeave() {

    }

    //凌晨3点刷新
    public void onDayThreeStart() {

    }

    //凌晨5点刷新
    public void onDayFiveStart() {

    }

    //凌晨6点刷新
    public void onDaySixStart() {

    }

    public void onReload() {

    }

    /**
     * 加载完成并设置了登录信息
     */
    public void loadedWithLoginInfo() {

    }

    public void onEveryHourStart() {

    }

    public void afterRefreshABTest() {

    }

    public boolean isModuleEnable() {
        return true;
    }
}

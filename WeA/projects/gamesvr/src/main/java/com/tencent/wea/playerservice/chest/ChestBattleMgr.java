package com.tencent.wea.playerservice.chest;

import com.github.javaparser.printer.lexicalpreservation.changes.Change;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ChestMapSelectConfig;
import com.tencent.resourceloader.resclass.ChestMiscConf;
import com.tencent.resourceloader.resclass.ChestSettleBaseRewardData;
import com.tencent.resourceloader.resclass.ChestSettleExtraDrawData;
import com.tencent.resourceloader.resclass.MatchTypeRootData;
import com.tencent.resourceloader.resclass.QDTSeasonCfgData;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ChestActorRankValue;
import com.tencent.wea.attr.ChestBattleCarryOutInfo;
import com.tencent.wea.attr.ChestBattleInfo;
import com.tencent.wea.attr.ChestGameInfo;
import com.tencent.wea.attr.ChestItem;
import com.tencent.wea.battleresult.GlobalBattleResult;
import com.tencent.wea.g6.irpc.proto.ds_player.DsPlayer;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ChestItemManager;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.drop.GSDropSystem;
import com.tencent.wea.playerservice.event.common.battle.PlayerStartBattleEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsLetsgo;
import com.tencent.wea.protocol.common.G6Common.BattleResultCode;
import com.tencent.wea.protocol.common.G6Common.ChestBattleDetailData;
import com.tencent.wea.protocol.common.G6Common.LetsGoBattleDetailData;
import com.tencent.wea.protocol.common.ItemArray;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResChestMap.ChestMapSelectData;
import com.tencent.wea.xlsRes.ResChestSettlement.ChestBattleEventCondition;
import com.tencent.wea.xlsRes.ResChestSettlement.ChestBattlePoolConfig;
import com.tencent.wea.xlsRes.ResChestSettlement.ChestSettleBaseRewardConfig;
import com.tencent.wea.xlsRes.ResChestSettlement.ChestSettleExtraDrawConfig;
import com.tencent.wea.xlsRes.ResQualifying.SeasonCfg;
import com.tencent.wea.xlsRes.keywords.ChaseSideType;
import com.tencent.wea.xlsRes.keywords.ChestItemAttrType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.GameRootType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.RankRule;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ChestBattleMgr {

    private static final Logger LOGGER = LogManager.getLogger(ChestBattleMgr.class);
    private final ChestGameInfo chestAttrData;
    private final Player player;

    public ChestBattleMgr(Player player, ChestGameInfo chestAttrData) {
        this.player = player;
        this.chestAttrData = chestAttrData;
        player.getEventSwitch().register(new OnPlayerStartBattle());
    }

    // chest对局结算逻辑
    public void chestBattleSettlement(LetsGoBattleDetailData battleDetailData, GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder builder) {
        if (MatchTypeRootData.getInstance().getGameRootTypeByMatchTypeId(globalBattleResult.getMatchType())
                != GameRootType.GRT_Chest) {
            return;
        }

        int chestActorType = globalBattleResult.getChaseActorType();
        // 玩家被淘汰,清空备战背包
        if (BattleResultCode.BATTLE_RESULT_CODE_FAIL.equals(globalBattleResult.getBattleResult())) {
            player.getChestItemManager().clearEquipBagItems(globalBattleResult.getBattleId(), chestActorType);
        }
        ChestBattleDetailData chestBattleDetailData = battleDetailData.getChestBattleDetailData();

        ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo()
                .getChestBattleInfo(globalBattleResult.getBattleId());
        if (chestBattleInfo == null) {
            LOGGER.error("chestBattleSettlement battleInfo is null, uid:{} battleId:{}",
                    player.getUid(), globalBattleResult.getBattleId());
            return;
        }

//        sendSafeHouseItems(chestBattleInfo, builder);
//        calcBaseReward(player.getUid(), globalBattleResult, builder);
//        calcExtraReward(player.getUid(), globalBattleResult, builder);
//        long totalValue = calcBringOutItemsValue(chestBattleDetailData);

        addCarryOutItemsValue(battleDetailData, chestActorType, chestBattleDetailData.getPersonalItemsValue());
        sendSettlementRewards(chestBattleDetailData);
    }

    private void sendSettlementRewards(ChestBattleDetailData chestBattleDetailData) {
        ChangedItems changedItems = new ChangedItems(ItemChangeReason.ICR_ChestSettlementRewards.getNumber(), "");
        // 对局结束带出的道具
        for (ItemInfo itemInfo : chestBattleDetailData.getEquipBagItemsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConf == null || itemConf.getType() != ItemType.ItemType_Chest_Treasure) {
                continue;
            }
            changedItems.mergeItemInfo(itemInfo);
        }
        for (ItemInfo itemInfo : chestBattleDetailData.getBaseRewardsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConf == null || itemConf.getType() != ItemType.ItemType_Chest_Treasure) {
                continue;
            }
            changedItems.mergeItemInfo(itemInfo);
        }
        for (ItemArray itemArray : chestBattleDetailData.getDarkStarRewardChestListList()) {
            for (ItemInfo itemInfo : itemArray.getItemsList()) {
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
                if (itemConf == null || itemConf.getType() != ItemType.ItemType_Chest_Treasure) {
                    continue;
                }
                changedItems.mergeItemInfo(itemInfo);
            }
        }
        for (ItemInfo itemInfo : chestBattleDetailData.getDarkStarRandomRewardListList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemInfo.getItemId());
            if (itemConf == null || itemConf.getType() != ItemType.ItemType_Chest_Treasure) {
                continue;
            }
            changedItems.mergeItemInfo(itemInfo);
        }
        player.getBagManager().AddItems2(changedItems);
    }

    private void addCarryOutItemsValue(LetsGoBattleDetailData battleDetailData, int actorType, long totalValue) {
        long now = DateUtils.currentTimeSec();
        long lastUpdateTime = player.getUserAttr().getChestGameInfo().getChestRankInfo().getLastUpdateTime();

        ChestActorRankValue actorRankInfo = player.getUserAttr().getChestGameInfo().getChestRankInfo()
                .getChestRankValues(actorType);
        if (actorRankInfo == null) {
            actorRankInfo = new ChestActorRankValue().setActorType(actorType);
            player.getUserAttr().getChestGameInfo().getChestRankInfo().putChestRankValues(actorType, actorRankInfo);
        }
        if (!DateUtils.isSameWeek(now, lastUpdateTime)) {
            actorRankInfo.clearTopNValueList();
        }

        ChestBattleCarryOutInfo carryOutInfo = new ChestBattleCarryOutInfo().setBattleId(battleDetailData.getBattleId())
                .setBattleTime(battleDetailData.getBattleStartTime())
                .setItemValues(totalValue);
        actorRankInfo.putTopNValueList(carryOutInfo.getBattleId(), carryOutInfo);
        // 只保留分数最高的N个成绩
        Collection<ChestBattleCarryOutInfo> topNList = actorRankInfo.getTopNValueList().values();
        final int KEEP_TOP_N = ChestMiscConf.getInstance().getChestMiscConfig()
                .getCheskWeeklyRankTopNTotalValue(); // 保留前N个最高成绩
        long oldWeeklyTotalValue = actorRankInfo.getWeeklyTotalValue();
        long weeklyTotalValue = 0L;
        if (topNList.size() > KEEP_TOP_N) {
            List<ChestBattleCarryOutInfo> sortedList = new ArrayList<>(topNList);
            sortedList.sort((a, b) -> Long.compare(b.getItemValues(), a.getItemValues())); // 降序
            actorRankInfo.clearTopNValueList();
            for (int i = 0; i < KEEP_TOP_N; i++) {
                ChestBattleCarryOutInfo info = sortedList.get(i);
                actorRankInfo.putTopNValueList(info.getBattleId(), info);
                weeklyTotalValue += info.getItemValues();
            }
            if (LOGGER.isDebugEnabled()) {
                for (int i = KEEP_TOP_N; i < sortedList.size(); i++) {
                    ChestBattleCarryOutInfo info = sortedList.get(i);
                    LOGGER.debug("addCarryOutItemsValue remove chestCarryOutInfo, uid:{} battleId:{} itemValues:{}",
                            player.getUid(), info.getBattleId(), info.getItemValues());
                }
            }
        }
        actorRankInfo.setWeeklyTotalValue(weeklyTotalValue);

        player.getUserAttr().getChestGameInfo().getChestRankInfo().setLastUpdateTime(now);
        LOGGER.debug("addCarryOutItemsValue, uid:{} totalValue:{} oldWeeklyTotalValue:{} lastUpdateTime:{}",
                player.getUid(), totalValue, oldWeeklyTotalValue, lastUpdateTime);

        try {
            var rankingConf = RankingConfData.getInstance().getByDescId(RankRule.RR_PlayMode_Chest, actorType);
            if (rankingConf == null) {
                LOGGER.error("rankingConf not found, heroId:{}", actorType);
                return;
            }
            NKErrorCode errorCode = player.getRankManager()
                    .refresh(rankingConf.getRankId(), Math.toIntExact(weeklyTotalValue), 0);
            if (!errorCode.isOk()) {
                LOGGER.error("refresh rank failed, uid:{} rankId:{} err:{}",
                        player.getUid(), rankingConf.getRankId(), errorCode);
            }
        } catch (Exception e) {
            LOGGER.error("addCarryOutItemsValue, uid:{} totalValue:{} lastUpdateTime:{} e:",
                    player.getUid(), totalValue, lastUpdateTime, e);
        }
    }

    // 进地图消耗入场券
    private void costEnterGameCost(int matchTypeId) {
        int chestActorType = chestAttrData.getCurrentRole().getActorType();
        if (chestActorType != ChaseSideType.CST_DARKSTAR.getNumber()) {
            return;
        }
        Optional<ChestMapSelectData> mapCfg = ChestMapSelectConfig.getInstance()
                .getByMatchTypeId(matchTypeId);
        if (mapCfg.isEmpty()) {
            LOGGER.error("uid {} costEnterGameCost matchTypeId:{} not found",
                    player.getUid(), matchTypeId);
            return;
        }
        NKErrorCode result = NKErrorCode.UnknownError;
        ChangedItems costInfo = new ChangedItems(ItemChangeReason.ICR_ChestEnterGameCost.getNumber(), "");
        costInfo.mergeItemInfoMulti(mapCfg.get().getEntryCostInfo().getBossActorTypeEntryCostItemsList(), 1);
        NKErrorCode ret = player.getBagManager().MinItems(costInfo, matchTypeId);
        if (!ret.isOk()) {
            LOGGER.error("uid {} costEnterGameCost matchTypeId:{} cost failed:{}",
                    player.getUid(), matchTypeId, result);
        }
    }

    private class OnPlayerStartBattle implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerStartBattle)
        private NKErrorCode onEvent(PlayerStartBattleEvent event) throws NKRuntimeException {
            LOGGER.debug("onEvent PlayerStartBattleEvent, uid:{} battleId:{} matchType:{}",
                    event.getPlayer().getUid(), event.getBattleInfo().getBattleid(), event.getGameMode());
            onMatchSuccess(event.getGameMode(), event.getBattleInfo().getBattleid());
            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    // 删除过期的对局信息
    private void clearExpiredChestBattleInfo() {
        long now = DateUtils.currentTimeSec();
        for (ChestBattleInfo chestBattleInfo : player.getUserAttr().getChestGameInfo().getChestBattleInfo().values()) {
            if (now - chestBattleInfo.getBattleCreateTime() > DateUtils.ONE_HOUR_SECONDS) {
                sendSafeHouseItems(chestBattleInfo, CsLetsgo.LetsGoBattleSettlementNtf.newBuilder());
                player.getUserAttr().getChestGameInfo().removeChestBattleInfo(chestBattleInfo.getBattleId());
                LOGGER.info("clearExpiredChestBattleInfo, uid:{} battleId:{} battleCreateTime:{}",
                        player.getUid(), chestBattleInfo.getBattleId(), chestBattleInfo.getBattleCreateTime());
            }
        }
    }

    // 安全屋奖励结算
    private void sendSafeHouseItems(ChestBattleInfo chestBattleInfo,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder builder) {
        if (chestBattleInfo == null) {
            return;
        }
        if (chestBattleInfo.getSafeHouseItems().isEmpty()) {
            return;
        }
        LOGGER.info("sendSafeHouseItems, uid:{} battleId:{} safeHouseItems:{}",
                player.getUid(), chestBattleInfo.getBattleId(), chestBattleInfo.getSafeHouseItems());
        ChangedItems safeHouseItems = new ChangedItems(ItemChangeReason.ICR_ChestSafeHouseRewards.getNumber(), "");
        safeHouseItems.getTlogData().setChangeSubReason(chestBattleInfo.getBattleId());
        for (ChestItem chestItem : chestBattleInfo.getSafeHouseItems().values()) {
            ItemInfo.Builder itemInfo = ChestItemManager.convChestItemToItemInfo(chestItem);
            safeHouseItems.mergeItemInfo(itemInfo.build());
            builder.getDetailDataBuilder().getChestBattleDetailDataBuilder().addSafeHouseItems(itemInfo);
        }
        NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(safeHouseItems);
        if (!ret.getKey().isOk()) {
            LOGGER.error("sendSafeHouseItems failed, uid:{} battleId:{} err:{} items:{}",
                    player.getUid(), chestBattleInfo.getBattleId(), ret.getKey(), safeHouseItems.toString());
        }
    }

    //匹配成功
    private void onMatchSuccess(int matchType, long battleId) {
        costEnterGameCost(matchType);
        // 初始化对局信息
        ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo().getChestBattleInfo(battleId);
        if (chestBattleInfo == null) {
            chestBattleInfo = new ChestBattleInfo()
                    .setBattleId(battleId)
                    .setBattleCreateTime(DateUtils.currentTimeSec());
            player.getUserAttr().getChestGameInfo().putChestBattleInfo(battleId, chestBattleInfo);
        }

        int chestActorType = chestAttrData.getCurrentRole().getActorType();
        player.getChestItemManager().backupEquipBagItems(battleId, chestActorType, chestBattleInfo);
    }

    private static boolean checkBattleConditionsPass(GlobalBattleResult globalBattleResult,
            List<ChestBattleEventCondition> conditionsList) {
        for (ChestBattleEventCondition condition : conditionsList) {
            if (globalBattleResult.getChaseEventValue(condition.getEventType()) < condition.getEventValue()) {
                return false;
            }
        }
        return true;
    }

    private static void calcBaseReward(long uid, GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        List<ChestSettleBaseRewardConfig> dataList = ChestSettleBaseRewardData.getInstance().getArrayList();
        ArrayList<Integer> poolIdList = new ArrayList<>();
        for (ChestSettleBaseRewardConfig config : dataList) {
            int hitPoolId = 0;
            for (ChestBattlePoolConfig poolConfig : config.getRewardPoolsList()) {
                if (checkBattleConditionsPass(globalBattleResult, poolConfig.getConditionsList())) {
                    hitPoolId = poolConfig.getPoolId();
                    LOGGER.debug("checkBattleConditionsPass, uid:{} battleId:{} hitPoolId: {}",
                            uid, globalBattleResult.getBattleId(), hitPoolId);
                }
            }
            if (hitPoolId > 0) {
                poolIdList.add(hitPoolId);
            }
        }
        if (poolIdList.isEmpty()) {
            LOGGER.error("calcBaseReward poolIdList is empty, uid:{} battleId:{} poolIdList:{}",
                    uid, globalBattleResult.getBattleId(), poolIdList);
            return;
        }
        List<ItemInfo> itemList = new ArrayList<>();
        for (int poolId : poolIdList) {
            List<ItemInfo> dropRewards = GSDropSystem.getInstance().batchDrop(poolId, 1);
            itemList.addAll(dropRewards);
        }
        ntfBuilder.getDetailDataBuilder().getChestBattleDetailDataBuilder().addAllBaseRewards(itemList);
    }

    private static void calcExtraReward(long uid, GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        ChestSettleExtraDrawConfig extraRewardConf = ChestSettleExtraDrawData.getInstance()
                .get(globalBattleResult.getMatchType());
        if (extraRewardConf == null) {
            return;
        }
        HashMap<Integer, Integer> poolIdToDrawTimes = new HashMap<>();
        for (var drawConf : extraRewardConf.getDrawConfList()) {
            if (checkBattleConditionsPass(globalBattleResult, drawConf.getPoolConditionsList())) {
                poolIdToDrawTimes.put(drawConf.getPoolId(), 0);
                for (var drawTimesConf : drawConf.getDrawTimesConditionsList()) {
                    if (checkBattleConditionsPass(globalBattleResult, drawTimesConf.getConditionsList())) {
                        poolIdToDrawTimes.merge(drawConf.getPoolId(), drawTimesConf.getAddDrawTimes(), Integer::sum);
                        LOGGER.debug("checkBattleConditionsPass, uid:{} battleId:{} poolId:{} addDrawTimes:{}",
                                uid, globalBattleResult.getBattleId(), drawConf.getPoolId(),
                                drawTimesConf.getAddDrawTimes());
                    }
                }
            }
        }
        if (poolIdToDrawTimes.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, Integer> entry : poolIdToDrawTimes.entrySet()) {
            List<ItemInfo> dropRewards = GSDropSystem.getInstance().batchDrop(entry.getKey(), entry.getValue());
            ntfBuilder.getDetailDataBuilder().getChestBattleDetailDataBuilder()
                    .addDarkStarRewardChestList(ItemArray.newBuilder().addAllItems(dropRewards).build());
        }
    }

    public NKErrorCode irpcChestSaveItemsToSaveHouse(DsPlayer.ChestPlayerSaveItemsRequest req) {
        ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo().getChestBattleInfo(req.getBattleId());
        if (chestBattleInfo == null) {
            LOGGER.error("irpcChestSaveItemsToSaveHouse battleInfo is null, uid:{} battleId:{}",
                    player.getUid(), req.getBattleId());
            return NKErrorCode.BattleNotExist;
        }
        ChangedItems safeHouseItems = new ChangedItems(ItemChangeReason.ICR_ChestSafeHouseRewards.getNumber(), "");
        for (ItemInfo itemInfo : req.getItemInfosList()) {
            if (itemInfo.getUuid() == 0) {
                LOGGER.debug("irpcChestSaveItemsToSaveHouse itemInfo.getUuid() == 0, uid:{} battleId:{} itemInfo:{}",
                        player.getUid(), req.getBattleId(), itemInfo);
                continue;
            }
            if (chestBattleInfo.getSafeHouseItems().containsKey(itemInfo.getUuid())) {
                LOGGER.debug("irpcChestSaveItemsToSaveHouse itemInfo already exist, uid:{} battleId:{} itemInfo:{}",
                        player.getUid(), req.getBattleId(), itemInfo);
                continue;
            }
            chestBattleInfo.putSafeHouseItems(itemInfo.getUuid(), ChestItemManager.convItemInfoToChestItem(itemInfo));
            safeHouseItems.mergeItemInfo(itemInfo);
        }
        NKPair<NKErrorCode, ItemChangeDetails> ret = player.getBagManager().AddItems2(safeHouseItems);
        if (!ret.getKey().isOk()) {
            LOGGER.error("irpcChestSaveItemsToSaveHouse AddItems2 failed, uid:{} battleId:{} err:{} items:{}",
                    player.getUid(), req.getBattleId(), ret.getKey(), safeHouseItems.toString());
            return ret.getKey();
        }
        return NKErrorCode.OK;
    }

    private List<ItemInfo> getBattleSafeHouseItems(long battleId) {
        ChestBattleInfo chestBattleInfo = player.getUserAttr().getChestGameInfo().getChestBattleInfo(battleId);
        if (chestBattleInfo == null) {
            return null;
        }
        List<ItemInfo> itemInfoList = new ArrayList<>();
        for (ChestItem item : chestBattleInfo.getSafeHouseItems().values()) {
            itemInfoList.add(ChestItemManager.convChestItemToItemInfo(item).build());
        }
        return itemInfoList;
    }
}

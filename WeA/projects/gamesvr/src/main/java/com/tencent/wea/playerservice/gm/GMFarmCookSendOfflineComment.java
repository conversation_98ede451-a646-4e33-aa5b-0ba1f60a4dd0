package com.tencent.wea.playerservice.gm;

import static com.tencent.wea.playerservice.cshandler.handler.farm.FarmCSHandler.callRPC;
import static com.tencent.wea.playerservice.cshandler.handler.farm.FarmCSHandler.getService;

import com.tencent.wea.FarmGMCmdID;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsFarmsvr;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class GMFarmCookSendOfflineComment implements GmHandler {
    private static final Logger LOGGER = LogManager.getLogger(GMFarmCookSendOfflineComment.class);

    @Override
    public int handle(Player player, List<String> param) {
        var rpcReq = SsFarmsvr.RpcFarmGMCmdReq.newBuilder();
        rpcReq.setFarmID(player.getPlayerCookMgr().getCurrentCookId());
        rpcReq.setOperatorID(player.getUid());
        rpcReq.setCmdID(FarmGMCmdID.FarmCookSendOfflineComment);
        for (var p : param) {
            if (p.isEmpty()) {
                break;
            }
            rpcReq.addParams(SsFarmsvr.FarmGMParam.newBuilder().setI64(Long.parseLong(p)));
        }

        callRPC(() -> getService().rpcFarmGMCmd(rpcReq));
        return 0;
    }
}

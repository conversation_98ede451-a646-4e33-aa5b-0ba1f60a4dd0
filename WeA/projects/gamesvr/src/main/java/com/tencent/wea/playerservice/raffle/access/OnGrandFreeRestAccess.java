package com.tencent.wea.playerservice.raffle.access;

import com.tencent.wea.xlsRes.ResRaffle.RaffleAccessCfg;
import com.tencent.wea.xlsRes.ResRaffle.RaffleCfg;
import java.util.Optional;

public class OnGrandFreeRestAccess extends DirectAccess {

    public OnGrandFreeRestAccess(RaffleAccessCfg cfg, RaffleCfg poolCfg, Reader reader) {
        super(cfg, poolCfg, reader);
    }

    @Override
    protected Optional<Integer> findAccessibleMultiDrawPool(long currentMs) {
        return Optional.empty();
    }
}

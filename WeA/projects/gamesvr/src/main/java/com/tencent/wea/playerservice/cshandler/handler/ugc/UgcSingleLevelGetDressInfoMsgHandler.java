package com.tencent.wea.playerservice.cshandler.handler.ugc;

import com.google.protobuf.Message;
import com.tencent.resourceloader.resclass.UgcTemplateConflictOutlookData;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsUgc;
import java.util.Collection;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class UgcSingleLevelGetDressInfoMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(UgcSingleLevelGetDressInfoMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsUgc.UgcSingleLevelGetDressInfo_C2S_Msg reqMsg = (CsUgc.UgcSingleLevelGetDressInfo_C2S_Msg)request;
        CsUgc.UgcSingleLevelGetDressInfo_S2C_Msg.Builder rspMsg =  CsUgc.UgcSingleLevelGetDressInfo_S2C_Msg.newBuilder();
        rspMsg.setTemplateId(reqMsg.getTemplateId());
        Collection<Integer> before = player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList();
        if (UgcTemplateConflictOutlookData.getInstance().checkPlayerOutlookConflict(reqMsg.getTemplateId(), before)) {
            if (!player.getUserAttr().getPlayerPublicEquipments().getBackupDressUpInfosList().isEmpty()) {
                rspMsg.addAllDressUpItems(player.getUserAttr().getPlayerPublicEquipments().getBackupDressUpInfosList());
            }
        } else {
            rspMsg.addAllDressUpItems(player.getUserAttr().getPlayerPublicEquipments().getDressUpInfosList());
        }
        return rspMsg;
    }
}
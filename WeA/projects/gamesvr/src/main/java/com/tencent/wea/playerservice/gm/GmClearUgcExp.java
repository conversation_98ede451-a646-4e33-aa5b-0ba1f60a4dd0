package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.rpc.RpcResult;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.SsUgcsvr.RpcGMClearPlayerUgcExpRes;
import com.tencent.wea.rpc.service.UgcService;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class GmClearUgcExp implements GmHandler {

    private static final Logger LOGGER = LogManager.getLogger(GmPlayerModifyAttr.class);

    @Override
    public int handle(Player player, List<String> param) {
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("get publish list not found ugc service is running");
            return 1;
        }
        SsUgcsvr.RpcGMClearPlayerUgcExpReq.Builder req = SsUgcsvr.RpcGMClearPlayerUgcExpReq.newBuilder();
        req.setCreatorId(player.getCreatorId());
        try {
            RpcResult<RpcGMClearPlayerUgcExpRes.Builder> result = service.rpcGMClearPlayerUgcExp(req);
            int res = result.getData().getResult();
            if (res != 0) {
                LOGGER.error("failed");
                return res;
            }
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("rpcGMUpdatePlayerUgcExp err,{}", player.getUid());
        }

        return 0;
    }
}

package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.guid.BaseGenerator;
import com.tencent.nk.util.guid.GuidType;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityHistoryData;
import com.tencent.wea.attr.SquadActivityHistoryData;
import com.tencent.wea.playerservice.player.Player;
import java.util.ArrayList;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: WeA
 * @description: 添加多人成团历史队员uid
 * @author: nichtsun
 * @create: 2024-05-17
 **/

public class GMAddSquadActivityHistoryPartner implements GmHandler {
    private static final Logger LOGGER = LogManager.getLogger(GMAddSquadActivityHistoryPartner.class);
    @Override
    public int handle(Player player, List<String> param) {
        String uidList = param.get(0);
        String[] uidSplit = uidList.split(";");
        List<Long> uidListToAdd = new ArrayList<>();
        for (String uidStr : uidSplit) {
            try {
                long uid = Long.parseLong(uidStr);
                GuidType uidType = BaseGenerator.getUidType(uid);
                if (uidType != GuidType.GUID_TYPE_USER_ID) {
                    LOGGER.debug("using wrong uid, uid:{}", uid);
                    continue;
                }
                uidListToAdd.add(uid);
            } catch (NumberFormatException e) {
                LOGGER.debug("using wrong uid, uid:{}", uidStr);
                continue;
            }
        }
        if (uidListToAdd.size() <= 0) {
            return 0;
        }
        ActivityHistoryData activityHistoryData = player.getUserAttr().getModNote()
                .getActivityHistoryData();
        int curMaxGmActivity = 0;
        for (Integer activityId : activityHistoryData.getSquadData().keySet()) {
            if (activityId >= 5000) {
                continue;
            }
            curMaxGmActivity = Math.max(curMaxGmActivity, activityId);
        }
        if (curMaxGmActivity >= 5000) {
            return 0;
        }
        curMaxGmActivity++; // 自增作为新的活动id
        SquadActivityHistoryData squadData = new SquadActivityHistoryData();
        squadData.setActivityId(curMaxGmActivity);
        for (Long uid : uidListToAdd) {
            squadData.addMemberUidList(uid);
        }
        squadData.setLastTimestamp(DateUtils.currentTimeMillis());
        activityHistoryData.putSquadData(curMaxGmActivity, squadData);
        return 0;
    }
}

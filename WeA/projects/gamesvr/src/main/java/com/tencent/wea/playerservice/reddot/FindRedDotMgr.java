package com.tencent.wea.playerservice.reddot;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.ActivityUnit;
import com.tencent.wea.attr.MapAttrObj;
import com.tencent.wea.attr.PixuiRedDotInfo;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityManager;
import com.tencent.wea.playerservice.activity.lifecycle.ActivityMsgHelper;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsActivitysvr;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


/**
 * <AUTHOR>
 * @date 2024/6/4
 */
public class FindRedDotMgr extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(FindRedDotMgr.class);

    private final Player player;

    public FindRedDotMgr(Player player) {
        super(GameModuleId.GMI_PixuiRedDot, player);
        this.player = player;
    }

    public void clearDot() {
        // 清除所有活动红点
        for (BaseActivity activity : player.getActivityManager().getRunningActivity().values()) {
            ActivityUnit unit = activity.getActivityUnit();
            if (!activity.disableFindRedDotClear() && unit != null) {
                activity.removeNewRedDotId();
                activity.clearRedDot();
                activity.clearAllClickClearUpRedDotInfoExceptNewRedDot();
                activity.ntfRedDotInfo("FindRedDotMgr_clearDot");
                LOGGER.debug("activity-{} clear reddot", unit.getId());
            }
        }
        // pixui
        clearPixuiRedDot();

        // 去ActivitySvr
        SsActivitysvr.RpcActivityClearRedDotReq.Builder reqBuilder = SsActivitysvr.RpcActivityClearRedDotReq.newBuilder();
        reqBuilder.setClearType(ActivityManager.ActivityRedDotClearType.CLEAR_ALL.getValue());

        SsActivitysvr.RpcActivityClearRedDotRes.Builder rspBulider = ActivityMsgHelper.sendActivityClearRedDotMsg(player, reqBuilder);
        if(NKErrorCode.OK.getValue() != rspBulider.getResult()){
            LOGGER.error("sendActivityClearRedDotMsg err! player[{}] res[{}]",
                    player.getUid(), rspBulider.getResult());
        }
    }

    /**
     * 清除所有引导红点
     */
    public void clearGuideDot() {
        for (BaseActivity activity : player.getActivityManager().getRunningActivity().values()) {
            ActivityUnit unit = activity.getActivityUnit();
            if (!activity.disableFindRedDotClear() && unit != null) {
                activity.removeNewRedDotId();
                activity.clearAllClickClearUpRedDotInfoExceptNewRedDot();
                activity.ntfRedDotInfo("FindRedDotMgr_clearGuideDot");
                LOGGER.debug("activity-{} clear guide reddot", unit);
            }
        }
//        clearPixuiRedDot();
        // 去ActivitySvr
        SsActivitysvr.RpcActivityClearRedDotReq.Builder reqBuilder = SsActivitysvr.RpcActivityClearRedDotReq.newBuilder();
        reqBuilder.setClearType(ActivityManager.ActivityRedDotClearType.CLEAR_GUIDE.getValue());

        SsActivitysvr.RpcActivityClearRedDotRes.Builder rspBulider = ActivityMsgHelper.sendActivityClearRedDotMsg(player, reqBuilder);
        if(NKErrorCode.OK.getValue() != rspBulider.getResult()){
            LOGGER.error("sendActivityClearRedDotMsg err! player[{}] res[{}]",
                    player.getUid(), rspBulider.getResult());
        }
    }

    /**
     * 清除pixui红点
     */
    public void clearPixuiRedDot() {
        long curMs = DateUtils.currentTimeMillis();
        long todayMs = DateUtils.getDayBeginTimeMs(curMs);
        MapAttrObj<Integer, PixuiRedDotInfo> pixuiRedDot = player.getUserAttr().getPixuiRedDot();
        CsActivity.PixuiRedDotNtf.Builder builder = CsActivity.PixuiRedDotNtf.newBuilder();

        for (PixuiRedDotInfo info : pixuiRedDot.values()) {
            info.setClearMs(curMs);
            CsActivity.PixuiRedDotInfo.Builder csRedDotInfo =
                    CsActivity.PixuiRedDotInfo.newBuilder();
            csRedDotInfo.setId(info.getId());
            csRedDotInfo.setIsShow(false);
            builder.addInfo(csRedDotInfo);
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_PIXUIREDDOTNTF, builder);
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {
        long curMs = DateUtils.currentTimeMillis();
        long dayMs = DateUtils.getDayBeginTimeMs(curMs);
        MapAttrObj<Integer, PixuiRedDotInfo> pixuiRedDot = player.getUserAttr().getPixuiRedDot();
        CsActivity.PixuiRedDotNtf.Builder builder = CsActivity.PixuiRedDotNtf.newBuilder();
        for (PixuiRedDotInfo info : pixuiRedDot.values()) {
            CsActivity.PixuiRedDotInfo.Builder csRedDotInfo =
                    CsActivity.PixuiRedDotInfo.newBuilder();
            csRedDotInfo.setId(info.getId());
            long dayClearMs = DateUtils.getDayBeginTimeMs(info.getClearMs());
            if (info.getDeleteMs() == 0 && dayClearMs != dayMs) {
                csRedDotInfo.setIsShow(true);
            } else {
                csRedDotInfo.setIsShow(false);
            }
            builder.addInfo(csRedDotInfo);
        }
        if (builder.getInfoCount() > 0) {
            player.sendNtfMsg(MsgTypes.MSG_TYPE_PIXUIREDDOTNTF, builder);
        }

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {

    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void onMidNight() {
        // pixui每日红点更新
        long curMs = DateUtils.currentTimeMillis();
        long dayMs = DateUtils.getDayBeginTimeMs(curMs);
        MapAttrObj<Integer, PixuiRedDotInfo> pixuiRedDot = player.getUserAttr().getPixuiRedDot();
        CsActivity.PixuiRedDotNtf.Builder builder = CsActivity.PixuiRedDotNtf.newBuilder();
        for (PixuiRedDotInfo info : pixuiRedDot.values()) {
            long dayClearMs = DateUtils.getDayBeginTimeMs(info.getClearMs());

            CsActivity.PixuiRedDotInfo.Builder csRedDotInfo =
                    CsActivity.PixuiRedDotInfo.newBuilder();
            csRedDotInfo.setId(info.getId());

            if (info.getDeleteMs() == 0 && dayMs != dayClearMs) {
                csRedDotInfo.setIsShow(true);
            }else {
                csRedDotInfo.setIsShow(false);
            }
            builder.addInfo(csRedDotInfo);
        }
        if (builder.getInfoCount() > 0) {
            player.sendNtfMsg(MsgTypes.MSG_TYPE_PIXUIREDDOTNTF, builder);
        }
    }

    @Override
    public void onWeekStart() {

    }
}

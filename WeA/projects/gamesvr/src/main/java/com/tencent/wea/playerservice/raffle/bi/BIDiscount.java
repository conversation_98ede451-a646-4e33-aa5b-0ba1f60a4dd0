package com.tencent.wea.playerservice.raffle.bi;

import com.google.common.collect.Maps;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.RaffleBIData;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.raffle.utils.BIUtils;
import com.tencent.wea.protocol.common.RaffleBIQueryItem;
import com.tencent.wea.protocol.common.RaffleBIQueryParam;
import com.tencent.wea.protocol.common.RaffleBIQueryParam.Builder;
import com.tencent.wea.protocol.common.RaffleBIQueryParamData;
import com.tencent.wea.protocol.common.RaffleBIQueryResult;
import com.tencent.wea.xlsRes.ResRaffle.RaffleBICfg;
import com.tencent.wea.xlsRes.ResRaffle.RaffleBIEnvCfg;
import com.tencent.wea.xlsRes.keywords.RaffleBIType;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class BIDiscount implements BIOp<RaffleBIQueryParam.Builder, RaffleBIQueryResult.Builder, Boolean> {

    private static final Logger LOGGER = LogManager.getLogger(BIDiscount.class);

    private final long uid;
    private final String openId;
    private final String flowId;
    private final int discountCount;
    private final int restCoinNum;
    private final Map<Integer, Integer> items;
    private final int raffleId;
    private final int cfgId;

    private BIDiscount(long uid, String openId, String flowId, int discountCount, int restCoinNum,
            Map<Integer, Integer> items, int raffleId, long currentMs) {

        this.uid = uid;
        this.openId = openId;
        this.flowId = flowId;
        this.discountCount = discountCount;
        this.restCoinNum = restCoinNum;
        this.items = items;
        this.raffleId = raffleId;

        var conf = getCurrentBIConf(raffleId, currentMs);
        this.cfgId = conf == null ? 0 : conf.getId();
    }

    public static NKPair<Boolean, NKErrorCode> check(long uid, String openId, String flowId, int discountCount,
            int restCoinNum, Map<Integer, Integer> items, int raffleId, long currentMs) {

        var op = new BIDiscount(uid, openId, flowId, discountCount, restCoinNum, items, raffleId, currentMs);
        return RaffleBIDispatcher.dispatch(op);
    }

    public static NKPair<Boolean, NKErrorCode> check(Player player, String flowId, int discountCount, int restCoinNum,
            Map<Integer, Integer> items, int raffleId, long currentMs) {

        var op = new BIDiscount(player.getUid(), player.getOpenId(), flowId, discountCount, restCoinNum, items,
                raffleId, currentMs);
        return RaffleBIDispatcher.dispatch(op);
    }

    private static RaffleBICfg getCurrentBIConf(int raffleId, long currentMs) {
        var list = RaffleBIData.getInstance().getByType(RaffleBIType.RBT_Discount_VALUE);
        for (var conf : list) {
            if (conf.getRaffleId() != raffleId) {
                continue;
            }

            if (currentMs < conf.getStartTime().getSeconds() * 1000
                    || conf.getEndTime().getSeconds() * 1000 < currentMs) {
                continue;
            }

            return conf;
        }

        return null;
    }

    public static boolean isOpen(int raffleId, long currentMs) {
        return getCurrentBIConf(raffleId, currentMs) != null;
    }


    @Override
    public Map<String, String> extraHeader(RaffleBIEnvCfg cfg) {
        return Maps.newHashMap();
    }

    @Override
    public Builder buildReqBody(RaffleBIEnvCfg conf) {
        String sceneId = BIUtils.findParam(conf, "sceneid", "");
        String credId = BIUtils.findParam(conf, "credid", "");
        String drawType = BIUtils.findParam(conf, "drawtype", "");

        RaffleBIQueryParamData.Builder data = RaffleBIQueryParamData.newBuilder()
                .setRoleid(Long.toString(uid)).setDrawround(Integer.toString(0)).setDrawtype(drawType)
                .setExt1(Integer.toString(restCoinNum)).setExt2(Integer.toString(discountCount));

        items.forEach((id, num) -> data.addHaveitems(
                RaffleBIQueryItem.newBuilder().setId(String.valueOf(id)).setItemnum(String.valueOf(num))));

        return RaffleBIQueryParam.newBuilder().setFlowid(flowId)
                .setReqTime(Long.toString(DateUtils.currentTimeSec())).setSceneid(sceneId).setUserid(openId)
                .setCredid(credId).setData(data);
    }

    @Override
    public RaffleBIQueryResult.Builder buildRsp() {
        return RaffleBIQueryResult.newBuilder();
    }

    @Override
    public NKPair<Boolean, NKErrorCode> parse(RaffleBIQueryResult.Builder rsp) {
        if (!"0".equals(rsp.getErrcode())) {
            LOGGER.error("receive errcode from bi backend, uid:{} flow:{} err:{} msg:{}", uid, flowId,
                    rsp.getErrcode(), rsp.getErrdesc());
            String[] args = new String[]{Integer.toString(raffleId)};
            Monitor.getInstance().add.fail(MonitorId.attr_raffle_bi_discount_error, 1, args);
            return new NKPair<>(false, NKErrorCode.RaffleBIReturnErrorCode);
        }

        List<RaffleBIQueryItem> resultList = rsp.getData().getItemsList();
        if (resultList.isEmpty()) {
            LOGGER.error("unknown result from bi backend, uid:{} flow:{}", uid, flowId);
            String[] args = new String[]{Integer.toString(raffleId)};
            Monitor.getInstance().add.fail(MonitorId.attr_raffle_bi_discount_error, 1, args);
            return new NKPair<>(false, NKErrorCode.RaffleBIResultInvalid);
        }

        return new NKPair<>(!"0".equals(resultList.get(0).getId()), NKErrorCode.OK);
    }

    @Override
    public NKPair<Boolean, NKErrorCode> error(NKErrorCode errorCode) {
        return new NKPair<>(false, errorCode);
    }

    @Override
    public int getType() {
        return RaffleBIType.RBT_Discount_VALUE;
    }

    @Override
    public int getCfgId() {
        return cfgId;
    }
}

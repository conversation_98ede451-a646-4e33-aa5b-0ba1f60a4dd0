package com.tencent.wea.playerservice.cshandler.handler.platcommon;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlatCommonMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(PlatCommonMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        //CsPlatCommon.PlatCommon_C2S_Msg reqMsg = (CsPlatCommon.PlatCommon_C2S_Msg)request;
        NKErrorCode.UnknownError.throwError("PlatCommonMsgHandler not implemented");
        return null;
    }
}
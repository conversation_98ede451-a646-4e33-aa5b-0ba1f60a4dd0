package com.tencent.wea.playerservice.condition.sub;

import com.tencent.wea.playerservice.condition.BasePlayerSubCondition;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.PlayerLoginEvent;
import com.tencent.wea.playerservice.event.common.PlayerUpdateStartPrivilegesEvent;
import com.tencent.wea.xlsRes.keywords.SubConditionType;
import com.tencent.wea.xlsRes.keywords.EventDataType;
import java.util.List;


public class SubConditionStartPrivileges extends BasePlayerSubCondition {
    public SubConditionStartPrivileges() {

    }

    @Override
    public int getType() {
        return SubConditionType.SCT_StartPrivileges_VALUE;
    }

    @Override
    public boolean isOk(List<Long> valueList,
                        Object event) {
        if (event instanceof PlayerLoginEvent || event instanceof PlayerUpdateStartPrivilegesEvent) {
            BasePlayerEvent playerEvent = (BasePlayerEvent) event;
            Object data = playerEvent.getEventData(EventDataType.EDT_StartPrivileges);
            if (data instanceof Integer) {
                Integer privileges = (Integer)data;
                LOGGER.debug("Start privileges {}", privileges);
                return checkValueEqual(valueList, privileges);
            }
        } 
        return false;
    }
}

package com.tencent.wea.playerservice.cshandler.handler.relation;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.task.manager.LuckyFriendTaskManager;
import com.tencent.wea.protocol.CsRelation;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.xlsRes.keywords.TaskType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class LuckyFriendApplyFriendMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(LuckyFriendApplyFriendMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsRelation.LuckyFriendApplyFriend_C2S_Msg reqMsg = (CsRelation.LuckyFriendApplyFriend_C2S_Msg)request;

        LuckyFriendTaskManager taskManager = player.getTaskManager().getTaskManagerByType(TaskType.TaskType_LuckyFriend_VALUE,
                LuckyFriendTaskManager.class);
        if (null == taskManager) {
            NKErrorCode.UnknownError.throwError("player {} get lucky friend task manager null", player.getUid());
        }

        taskManager.playerOperateMatchFriend(reqMsg.getFriendUid(), reqMsg.getApply(), reqMsg.getTaskId());

        return CsRelation.LuckyFriendApplyFriend_S2C_Msg.newBuilder();
    }
}

package com.tencent.wea.playerservice.gm;

import com.tencent.util.CollectionUtil;
import com.tencent.wea.FarmGMCmdID;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsFarmsvr;

import java.util.List;

import static com.tencent.wea.playerservice.cshandler.handler.farm.FarmCSHandler.callRPC;
import static com.tencent.wea.playerservice.cshandler.handler.farm.FarmCSHandler.getService;

public class GmFarmSelectFish implements GmHandler {

    /**
     * gm处理逻辑接口
     *
     * @param player 玩家
     * @param param  gm参数列表
     * @return int
     */

    @Override
    public int handle(Player player, List<String> param) {
        var rpcReq = SsFarmsvr.RpcFarmGMCmdReq.newBuilder();
        rpcReq.setFarmID(player.getPlayerFarmMgr().getCurrentFarmId());
        rpcReq.setOperatorID(player.getUid());

        int fish = 0;
        if (!CollectionUtil.isNullOrEmpty(param)) {
            fish = Integer.parseInt(param.get(0));
        }

        rpcReq.setCmdID(FarmGMCmdID.SelectFish);
        rpcReq.addParams(SsFarmsvr.FarmGMParam.newBuilder().setI64(fish));
        callRPC(() -> getService().rpcFarmGMCmd(rpcReq));
        return 0;
    }
}

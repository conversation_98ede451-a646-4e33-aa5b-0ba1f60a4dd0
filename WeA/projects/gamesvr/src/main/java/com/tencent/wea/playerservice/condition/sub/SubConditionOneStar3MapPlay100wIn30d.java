package com.tencent.wea.playerservice.condition.sub;

import com.tencent.wea.playerservice.condition.BasePlayerSubCondition;
import com.tencent.wea.playerservice.event.common.PlayerThirdPartyTaskEvt;
import com.tencent.wea.xlsRes.keywords.SubConditionType;

import java.util.List;

public class SubConditionOneStar3MapPlay100wIn30d extends BasePlayerSubCondition {

    public SubConditionOneStar3MapPlay100wIn30d() {

    }

    @Override
    public int getType() {
        return SubConditionType.SCT_OneStar3MapPlay100wIn30d_VALUE;
    }

    @Override
    public boolean isOk(List<Long> valueList, Object object) {
        if (object instanceof PlayerThirdPartyTaskEvt) {
            PlayerThirdPartyTaskEvt event = (PlayerThirdPartyTaskEvt) object;
            return this.getType() == event.getSubconditionType();
        }
        return false;
    }
}

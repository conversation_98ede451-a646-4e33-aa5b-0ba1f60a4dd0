package com.tencent.wea.playerservice.cshandler.handler.ainpc;

import com.google.protobuf.Message;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.cshandler.GamesvrPbMsgHandlerFactory;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsAinpc;
import com.tencent.wea.protocol.CsAinpc.AiNpcProfileType;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class AiNpcCheckTextMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(AiNpcCheckTextMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsAinpc.AiNpcCheckText_C2S_Msg reqMsg = (CsAinpc.AiNpcCheckText_C2S_Msg)request;
        CsAinpc.AiNpcCheckText_S2C_Msg.Builder resMsg = CsAinpc.AiNpcCheckText_S2C_Msg.newBuilder();
        resMsg.setText(reqMsg.getText()).setType(reqMsg.getType());
        boolean ret = player.getPlayerAigcNpcMgr().cacheTempPalText(AiNpcProfileType.forNumber(reqMsg.getType()), reqMsg.getText());
        resMsg.setRet(ret);
        return resMsg;
    }
}

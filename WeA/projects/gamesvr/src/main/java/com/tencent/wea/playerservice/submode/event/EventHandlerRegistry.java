package com.tencent.wea.playerservice.submode.event;


import com.tencent.wea.playerservice.playerref.PlayerRefMgr;
import com.tencent.wea.xlsRes.keywords.EventType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/17
 * @desc 子玩法事件注册器
 */

public class EventHandlerRegistry {
    private static final Logger LOGGER = LogManager.getLogger(EventHandlerRegistry.class);
    private static final Map<Integer, EventHandler> handlers = new HashMap<>();

    public static void registerHandler(int eventType, EventHandler handler) {
        handlers.put(eventType, handler);
        LOGGER.info("eventType[{}] register ok!", eventType);
    }

    public static EventHandler getHandler(int eventType) {
        return handlers.get(eventType);
    }

    static {
        // 注册事件类型及对应处理
        EventHandlerRegistry.registerHandler(EventType.ET_TestSubModeEvent.getNumber(), new TestSubModeEventHandler());
    }
}

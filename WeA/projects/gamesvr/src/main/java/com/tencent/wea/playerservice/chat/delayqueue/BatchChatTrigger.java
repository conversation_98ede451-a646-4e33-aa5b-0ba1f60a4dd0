package com.tencent.wea.playerservice.chat.delayqueue;

import com.tencent.delayqueue.TaskHolder;
import com.tencent.delayqueue.TaskTrigger;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.common.SingleMsgSendTask;
import java.util.ArrayList;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: WeA
 * @description: 批量消息触发器
 * @author: nichtsun
 * @create: 2024-12-09
 **/

public class BatchChatTrigger extends TaskTrigger {

    private static final Logger LOGGER = LogManager.getLogger(BatchChatTrigger.class);

    private MsgDelayTaskHolder taskHolder;

    public BatchChatTrigger(Player player) {
        this.taskHolder = new MsgDelayTaskHolder(player);
    }

    public List<Long> batchSendPrivateMsg(Player player, List<SingleMsgSendTask> tasks, long sendTimeMs) {
        List<Long> failList = new ArrayList<>();
        int batchMsgSendIntervalMs = PropertyFileReader.getRealTimeIntItem("batch_msg_send_interval_ms", 500);
        long expectedExecuteTimeMs = sendTimeMs;
        for (SingleMsgSendTask task : tasks) {
            if (task.getReceiverUid() <= 0) {
                LOGGER.error("receive private msg task with no receiver, task:{}",
                        NKStringFormater.formatPb(task, 1000));
                continue;
            }
            expectedExecuteTimeMs += batchMsgSendIntervalMs;
            PrivateMsgDelayTask delayTask = new PrivateMsgDelayTask(player, task, expectedExecuteTimeMs);
            int ret = getHolder().registerTask(delayTask, expectedExecuteTimeMs);
            if (ret != 0) {
                failList.add(task.getReceiverUid());
            }
        }
        return failList;
    }

    @Override
    protected TaskHolder getHolder() {
        return taskHolder;
    }

    @Override
    protected void reportMonitor(int executedTaskCnt) {

    }
}

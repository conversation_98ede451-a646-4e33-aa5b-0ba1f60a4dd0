package com.tencent.wea.playerservice.recharge;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.ReturningUserConfData;
import com.tencent.wea.attr.ReturnActivity;
import com.tencent.wea.playerservice.money.DeliverGoodsHandler;
import com.tencent.wea.midas.MidasProductUtil.MidasProductParam;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.common.DeliverGoodsMetaData;
import com.tencent.wea.xlsRes.ResReturningUser.ReturningUserBait;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ReturnActivityChargeSignInTicketMidasHandler implements DeliverGoodsHandler {

    private static final Logger LOGGER = LogManager.getLogger(ReturnActivityChargeSignInTicketMidasHandler.class);

    public static ReturnActivityChargeSignInTicketMidasHandler getInstance() { return InstanceHolder.INSTANCE; }

    @Override
    public void deliverGoods(Player player, List<MidasProductParam> productParamList, DeliverGoodsMetaData metaData) {
        ReturnActivity returnActivity = player.getUserAttr().getReturningInfo().getReturnActivity();

        ReturningUserBait returningUserBait = ReturningUserConfData.getInstance()
                .getReturningUserBaitByIndex(returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
        if (null == returningUserBait) {
            LOGGER.error("getReturningUserBaitByIndex failed  player:{} UserConfId:{} BaitIndex:{}",
                    player.getUid(), returnActivity.getUserConfId(), returnActivity.getUserBaitIndex());
            return;
        }

        productParamList.forEach(productParam -> {
            if (returningUserBait.getChargeSignInTicket().equals(productParam.getProductId())) {
                NKErrorCode result = player.getReturnActivityManager()
                        .confirmBuyChargeSignInActivityTicket(productParam, metaData);
                if (!result.isOk()) {
                    LOGGER.error("confirmBuyChargeSignInActivityTicket failed, player:{} midas:{}", player.getUid(),
                            productParam.getProductId());
                }
            } else if (returningUserBait.getChargeGiftTicket().equals(productParam.getProductId())) {
                NKErrorCode result = player.getReturnActivityManager()
                        .confirmBuyChargeGiftTicket(productParam, metaData);
                if (!result.isOk()) {
                    LOGGER.error("confirmBuyChargeGiftTicket failed, player:{} midas:{}", player.getUid(),
                            productParam.getProductId());
                }
            }
        });
    }

    private static class InstanceHolder {
        private static final ReturnActivityChargeSignInTicketMidasHandler INSTANCE = new ReturnActivityChargeSignInTicketMidasHandler();
    }

}

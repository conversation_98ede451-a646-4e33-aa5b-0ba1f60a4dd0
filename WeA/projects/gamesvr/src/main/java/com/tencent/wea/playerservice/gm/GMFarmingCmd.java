package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.FarmingConfig;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsXiaowosvr;
import com.tencent.wea.XiaowoGMCmdID;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

import static com.tencent.wea.playerservice.cshandler.handler.xiaowo.XiaowoCSHandler.callRPC;
import static com.tencent.wea.playerservice.cshandler.handler.xiaowo.XiaowoCSHandler.getService;

public class GMFarmingCmd implements GmHandler {
    private static final Logger LOGGER = LogManager.getLogger(GMFarmingCmd.class);
    /**
     * gm处理逻辑接口
     *
     * @param player 玩家
     * @param param  gm参数列表
     * @return int
     */
    @Override
    public int handle(Player player, List<String> param) {
        switch ((int) Long.parseLong(param.get(0))) {
            case 1:
            case 2:
            case 3:
            case 4:
                var rpcReq = SsXiaowosvr.RpcXiaowoGMCmdReq.newBuilder();
                rpcReq.setXiaowoID(player.getPlayerXiaoWoMgr().getCurrentXiaoWoId());
                rpcReq.setOperatorID(player.getUid());
                rpcReq.setCmdID(XiaowoGMCmdID.FarmingCmd);
                rpcReq.addParams(SsXiaowosvr.XiaowoGMParam.newBuilder().setI64(Long.parseLong(param.get(0))));
                callRPC(() -> getService().rpcXiaowoGMCmd(rpcReq));
                break;
            case 5:
                var cropItemIDs = FarmingConfig.getAllCropItemIDs();
                var changedItems = new ChangedItems(ItemChangeReason.ICR_GmModify.getNumber(), "");
                for (var itemID : cropItemIDs) {
                    changedItems.mergeItemInfo(itemID, 10);
                }
                player.getBagManager().AddItems2(changedItems, changedItems.getChangeReason());
                break;
        }
        return 0;
    }
}

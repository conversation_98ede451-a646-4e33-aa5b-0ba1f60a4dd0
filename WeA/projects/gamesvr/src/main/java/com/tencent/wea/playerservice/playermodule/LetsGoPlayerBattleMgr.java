package com.tencent.wea.playerservice.playermodule;

import com.google.common.collect.Lists;
import com.tencent.arena.ArenaCommonDefine;
import com.tencent.condition.event.player.battle.FinishBattleEvent;
import com.tencent.condition.event.player.battle.FinishLevelEvent;
import com.tencent.condition.event.player.common.PlayerSkillByModelEvent;
import com.tencent.condition.event.player.common.PlayerSkillEvent;
import com.tencent.condition.event.player.common.UgcSettleJoinCntEvent;
import com.tencent.condition.event.player.common.UgcSettleScoreEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.rainbow.config.RealtimeConfig;
import com.tencent.rank.utils.RankIdLevelIdMapper;
import com.tencent.rank.utils.RankIdSeasonIdMapper;
import com.tencent.resourceloader.resclass.ArenaMatchGroupData;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ClientKVConf;
import com.tencent.resourceloader.resclass.DailyVictoryChestConf;
import com.tencent.resourceloader.resclass.HOKRiftPowerConf;
import com.tencent.resourceloader.resclass.LevelDropArenaData;
import com.tencent.resourceloader.resclass.LevelDropArenaSpecialData;
import com.tencent.resourceloader.resclass.LevelDropConfData;
import com.tencent.resourceloader.resclass.LevelDropLimitConfData;
import com.tencent.resourceloader.resclass.LevelInfoData;
import com.tencent.resourceloader.resclass.MatchDegreeTypeGroupData;
import com.tencent.resourceloader.resclass.MatchLevelRandEventRecordRuleData;
import com.tencent.resourceloader.resclass.MatchLevelRecordRuleData;
import com.tencent.resourceloader.resclass.MatchModeTypeData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.MatchTypeRootData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.MiscConfArena;
import com.tencent.resourceloader.resclass.PlayerLevelConfData;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.RankingRuleConfData;
import com.tencent.resourceloader.resclass.RelationMiscConfData;
import com.tencent.resourceloader.resclass.ReturningUserConstsData;
import com.tencent.resourceloader.resclass.SeasonConfData;
import com.tencent.resourceloader.resclass.SummerFlashPhotoConf;
import com.tencent.resourceloader.resclass.UGCEditorMapTemplate;
import com.tencent.resourceloader.resclass.UGCMapCommonConf;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplusattr.CommonAttr;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.ugc.CommonUtil;
import com.tencent.ugc.UgcFuncUtil;
import com.tencent.util.CollectionUtil;
import com.tencent.util.ReputationScoreUtil;
import com.tencent.wea.attr.BattleDateData;
import com.tencent.wea.attr.BattleLevelRoundData;
import com.tencent.wea.attr.BattleModeData;
import com.tencent.wea.attr.BattleResultData;
import com.tencent.wea.attr.BattleSettlementMVPInfo;
import com.tencent.wea.attr.LevelAchievementInfo;
import com.tencent.wea.attr.LevelRecordByPlay;
import com.tencent.wea.attr.LimitInfoStruct;
import com.tencent.wea.battlehistory.BattleHistoryUtil;
import com.tencent.wea.battleresult.GlobalBattleResult;
import com.tencent.wea.battleresult.LevelBattleResult;
import com.tencent.wea.battleresult.UgcBattleResult;
import com.tencent.wea.interaction.player.PlayerInteractionInvoker;
import com.tencent.wea.midjoin.HokConfs;
import com.tencent.wea.midjoin.TycConfs;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ChangedItems.ChangeItem;
import com.tencent.wea.playerservice.battle.drop.LevelDropGenerator;
import com.tencent.wea.playerservice.cups.source.CupsBattle;
import com.tencent.wea.playerservice.event.common.PlayerMapByRoomEvent;
import com.tencent.wea.playerservice.event.common.PlayerUgcMapBestCountEvent;
import com.tencent.wea.playerservice.event.common.PlayerUgcMatchPlayModeSelectEvent;
import com.tencent.wea.playerservice.event.common.PlayerUgcMatchPlayModeSelectEventEnableQuit;
import com.tencent.wea.playerservice.event.common.activity.AddScoreActivityTaskScoreEvent;
import com.tencent.wea.playerservice.event.common.battle.PlayerChaseCustomBattleDetailDataEvent;
import com.tencent.wea.playerservice.event.common.battle.PlayerFinishBattleEvent;
import com.tencent.wea.playerservice.event.common.battle.PlayerFinishLevelEvent;
import com.tencent.wea.playerservice.fps.FpsUtil;
import com.tencent.wea.playerservice.mayday.MayDayUtil;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.player.PlayerBattleMgr;
import com.tencent.wea.playerservice.rank.RankManager.RefreshDescriptor;
import com.tencent.wea.playerservice.report.BattleReportUtil;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr.UgcMapEnjoyResultData;
import com.tencent.wea.playerservice.ugc.data.MapData;
import com.tencent.wea.playerservice.ugc.data.MapData.BestRecordData;
import com.tencent.wea.playerservice.ugc.manager.SsRpcUgcManager;
import com.tencent.wea.playerservice.ugc.manager.UgcDataFlowManager;
import com.tencent.wea.protocol.CsLetsgo;
import com.tencent.wea.protocol.CsLetsgo.HeatSettlement;
import com.tencent.wea.protocol.CsLetsgo.LetsGoBattleSettlementNtf;
import com.tencent.wea.protocol.CsLetsgo.QualifyingSettlement;
import com.tencent.wea.protocol.CsPlayer.ABTestType;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.SsUgcsvr.RpcGetUgcPublishMapRes.Builder;
import com.tencent.wea.protocol.SsUgcsvr.UgcBaseInfo;
import com.tencent.wea.protocol.SsUgcsvr.UgcPlayType;
import com.tencent.wea.protocol.common.AddCupsDetailInfo;
import com.tencent.wea.protocol.common.AlgoInfo;
import com.tencent.wea.protocol.common.BattleEventData;
import com.tencent.wea.protocol.common.BattleLevelEventData;
import com.tencent.wea.protocol.common.BattleLevelEventDataForArena;
import com.tencent.wea.protocol.common.BattlePlayerCoMatchInfo;
import com.tencent.wea.protocol.common.BattleRewardAdditionType;
import com.tencent.wea.protocol.common.BattleSettlementData;
import com.tencent.wea.protocol.common.BattleSettlementExtraInfo;
import com.tencent.wea.protocol.common.CompetitionCommon.CompetitionGameType;
import com.tencent.wea.protocol.common.CupsAdditionInfo;
import com.tencent.wea.protocol.common.DsStatComparisonOp;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.protocol.common.G6Common.BattleEventContext;
import com.tencent.wea.protocol.common.G6Common.BattleResultCode;
import com.tencent.wea.protocol.common.G6Common.ChampionScoreGradeInfo;
import com.tencent.wea.protocol.common.G6Common.FpsReturnItemInfo;
import com.tencent.wea.protocol.common.G6Common.LetsGoBattleDetailData;
import com.tencent.wea.protocol.common.G6Common.LetsGoLevelBattleEvent;
import com.tencent.wea.protocol.common.G6Common.LevelDropItemInfo;
import com.tencent.wea.protocol.common.GameModeType;
import com.tencent.wea.protocol.common.KeyValueInt32;
import com.tencent.wea.protocol.common.KeyValueInt64;
import com.tencent.wea.protocol.common.MatchRuleClientInfo;
import com.tencent.wea.protocol.common.MatchRuleInfo;
import com.tencent.wea.protocol.common.MatchTypeABTestInfo;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.MemberPlayTime;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.PublishItem;
import com.tencent.wea.protocol.common.RankSeasonInfo;
import com.tencent.wea.protocol.common.RoomBattleSettlementInfo;
import com.tencent.wea.protocol.common.RoomType;
import com.tencent.wea.protocol.common.UgcLevelSettlementInfo;
import com.tencent.wea.protocol.common.UgcMapMetaInfo;
import com.tencent.wea.protocol.common.UgcMapModelType;
import com.tencent.wea.room.RoomUtil.UpdateMemberBaseInfoSource;
import com.tencent.wea.rpc.service.UgcService;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.starp.tlog.StarPTlogFlowMgr;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PiiFpsReturnItemParams;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.xlsRes.ResActivity;
import com.tencent.wea.xlsRes.ResArenaDailyVictoryChest.ResDailyVictoryChestData;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResCommon.Item;
import com.tencent.wea.xlsRes.ResCups.AdditionType;
import com.tencent.wea.xlsRes.ResHOKRiftPower;
import com.tencent.wea.xlsRes.ResLevelDrop;
import com.tencent.wea.xlsRes.ResLevelDrop.LevelDropLimitConfig;
import com.tencent.wea.xlsRes.ResLevelDropArena;
import com.tencent.wea.xlsRes.ResLevelInfo.T_LevelInfoData;
import com.tencent.wea.xlsRes.ResMatch;
import com.tencent.wea.xlsRes.ResMatch.MatchType;
import com.tencent.wea.xlsRes.ResMatchLevelRecord.MatchLevelRandEventRecordRule;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.ResMisc.FpsDropLimit;
import com.tencent.wea.xlsRes.ResRanking.RankingConf;
import com.tencent.wea.xlsRes.ResReturningUser;
import com.tencent.wea.xlsRes.ResSeason.SeasonConf;
import com.tencent.wea.xlsRes.ResUGCEditor;
import com.tencent.wea.xlsRes.keywords.BattleEventType;
import com.tencent.wea.xlsRes.keywords.CoinType;
import com.tencent.wea.xlsRes.keywords.CommonLimitType;
import com.tencent.wea.xlsRes.keywords.IAAType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.LevelType;
import com.tencent.wea.xlsRes.keywords.LimitType;
import com.tencent.wea.xlsRes.keywords.PlayerGameDataStatDuration;
import com.tencent.wea.xlsRes.keywords.PlayerGameDataStatType;
import com.tencent.wea.xlsRes.keywords.PlayerGameTimeType;
import com.tencent.wea.xlsRes.keywords.Programmer;
import com.tencent.wea.xlsRes.keywords.QualifyType;
import com.tencent.wea.xlsRes.keywords.ReturningUserConstsEnum;
import com.tencent.wea.xlsRes.keywords.UGCMapType;
import com.tencent.wea.xlsRes.keywords.UgcMapConfEnum;
import com.tencent.wechatrobot.WechatLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.tencent.wea.protocol.common.MatchTypeEMoeGameType.MTEMGT_ArenaGame_VALUE;
import static com.tencent.wea.protocol.common.MatchTypeEMoeGameType.MTEMGT_RogueLikeGame;
import static com.tencent.wea.protocol.common.MatchTypeEMoeGameType.MTEMGT_WerewolfGame_VALUE;
import static com.tencent.wea.xlsRes.keywords.BattleEventType.BET_AT_LEVEL;
import static com.tencent.wea.xlsRes.keywords.BattleEventType.BET_LEVEL_DIFFICULTY_INDEX;
import static com.tencent.wea.xlsRes.keywords.BattleEventType.BET_LEVEL_END_REASON;
import static com.tencent.wea.xlsRes.keywords.BattleEventType.BET_LEVEL_START_TIMR;
import static com.tencent.wea.xlsRes.keywords.BattleEventType.BET_PASS_LEVEL_COUNT;

public class LetsGoPlayerBattleMgr extends PlayerBattleMgr {

    public LetsGoPlayerBattleMgr(Player player) {
        super(player);
        registerDailyDropLimit();
        registerFpsDropLimit();
    }

    @Override
    public void onReload() {
        registerDailyDropLimit();
        registerFpsDropLimit();
    }

    public void updateGameStartCount(){
        updateNextDayCount();
        // 统计对战开启局数+1
        player.getUserAttr().getModNote().setTodayGameStartCount(player.getUserAttr().getModNote().getTodayGameStartCount() +1);
        // 更新数据更新时间
        player.getUserAttr().getModNote().setUpdateTime(DateUtils.currentTimeMillis());
        LOGGER.debug("TodayGameStartCount:{}, RecentGameStartCount:{}, UpdateTime:{}",
                player.getUserAttr().getModNote().getTodayGameStartCount(),
                player.getUserAttr().getModNote().getRecentGameStartCount(),
                player.getUserAttr().getModNote().getUpdateTime());
    }

    public void battleAddPakPlayTimes(PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {
        long battleStartTime = dsSettlementParams.getDetailData().getBattleStartTime();
        long battleEndTime = dsSettlementParams.getDetailData().getBattleEndTime();
        long battleTotalTime = battleEndTime - battleStartTime;
        int matchType = dsSettlementParams.getDetailData().getMatchType();
        if (battleTotalTime <= 0) {
            LOGGER.warn("battleAddPakPlayTimes battleId:{} battleStartTime:{} battleEndTime:{} battleTotalTime:{} is valid",
                    dsSettlementParams.getDetailData().getBattleId(),battleStartTime,battleEndTime,battleTotalTime);
            return;
        }
        if (battleTotalTime < DateUtils.ONE_SECOND_MILLIS * ClientKVConf.getInstance().getPakPlayTime()) {
            LOGGER.debug("battleAddPakPlayTimes battleId:{} battleStartTime:{} battleEndTime:{} battleTotalTime:{} less than {}",
                    dsSettlementParams.getDetailData().getBattleId(),battleStartTime,battleEndTime,battleTotalTime,DateUtils.ONE_SECOND_MILLIS * ClientKVConf.getInstance().getPakPlayTime());
            return;
        }
        player.getPlayerPakPlayMgr().addPakPlayTimes(matchType);
    }

    @Override
    public void onMidNight() {
        updateNextDayCount();
    }

    private void registerDailyDropLimit() {
        ResMisc.MiscBattleConf battleConf = MiscConf.getInstance().getMiscConf().getBattleConf();
        for (Item item : battleConf.getBattleDropDailyLimitList()) {
            LimitInfoStruct limitStruct = player.getLimitManager()
                    .registerLimitInfo(CommonLimitType.CLT_BattleDailyLimit, item.getItemId(), item.getItemNum());
            LOGGER.debug("battleSettlement registerDailyDropLimit success, uid:{}, itemId:{} currentVal:{}",
                    player.getUid(), item.getItemId(), limitStruct.getValue());
        }
    }

    private void registerFpsDropLimit() {
        ResMisc.MiscBattleConf battleConf = MiscConf.getInstance().getMiscConf().getBattleConf();
        for (FpsDropLimit item : battleConf.getFpsDropLimitList()) {
            long limitId = FpsUtil.getFpsDropLimitId(item.getGameTypeId(), item.getItemId());
            LimitInfoStruct limitStruct = player.getLimitManager()
                    .registerLimitInfo(CommonLimitType.CLT_ActivityCoinWeeklyDropLimit, limitId,
                            item.getWeeklyDropLimit());
            LimitInfoStruct limitStruct2 = player.getLimitManager()
                    .registerLimitInfo(CommonLimitType.CLT_ActivityCoinSeasonalCostLimit, limitId,
                            item.getSeasonalCostLimit());
            LimitInfoStruct limitStruct3 = player.getLimitManager()
                    .registerLimitInfo(CommonLimitType.CLT_ActivityCoinSeasonalDropLimit, limitId,
                            0);
            LOGGER.debug(
                    "battleSettlement registerFpsDropLimit success, uid:{}, gameTypeId:{}, itemId:{}, limitId:{}, weeklyDrop:{}, seasonalCost:{}, seasonalDrop:{}",
                    player.getUid(), item.getGameTypeId(), item.getItemId(), limitId, limitStruct.getValue(),
                    limitStruct2.getValue(), limitStruct3.getValue());
        }
    }

    public void addBattleResultData(long battleId, int matchType, GlobalBattleResult result,
            long battleEndTime) {
        BattleModeData modeData = player.getUserAttr().getPlayerPublicSummaryInfo().getBattleModeData(matchType);
        ResMatch.MatchType gameCfg = MatchTypeData.getInstance().get(matchType);
        if (modeData != null && modeData.getRecentBattleResultDataSize() >= gameCfg.getBattleRecordCnt()) {
            clearOldestBattleResultData(modeData);
        }
        BattleResultData battleResult = new BattleResultData();
        battleResult.setId(battleId);
        battleResult.setResult(result.getBattleResult().getNumber());
        battleResult.setEndTime(battleEndTime);
        battleResult.setBattleRole(result.getCampRole());
        MatchTypeABTestInfo oneRoundABTestInfo = result.getMatchTypeABTestInfoMap()
                .get(ABTestType.ABTT_PROMOTION_COMPETITION_ONE_ROUND_VALUE);
        if (oneRoundABTestInfo != null && oneRoundABTestInfo.getLevelRoundCount() != 0) {
            battleResult.getSpecialBattleData().setIsOneRound(true);
        }
        for (int i = 0; i < result.getLevelBattleResults().size(); i++) {
            int round = i + 1;
            BattleLevelRoundData levelRoundData = new BattleLevelRoundData();
            levelRoundData.setRound(round).setLevelId(result.getLevelBattleResults().get(i).getLevel());
            battleResult.putLevelRounds(round, levelRoundData);
        }
        if (modeData == null) {
            modeData = new BattleModeData();
            player.getUserAttr().getPlayerPublicSummaryInfo().putBattleModeData(matchType, modeData);
        }
        modeData.putRecentBattleResultData(battleId, battleResult);
        LOGGER.debug("uid:{} battle:{} modeData length, matchType:{} size:{}",
                player.getUid(), battleId, matchType, modeData.getRecentBattleResultDataSize());
    }

    /**
     * 统计玩法根类型游玩的日期
     */
    public void addBattleDateData(int matchType, long battleEndTime) {
        Integer gameTypeId = MatchTypeRootData.getInstance().getMatchTypeGameTypeId(matchType);
        if (gameTypeId == null) {
            LOGGER.debug("matchType can not find gameTypeId, uid:{}, matchType:{}", player.getUid(), matchType);
            return;
        }

        int battleStartDate = Integer.parseInt(DateUtils.fromTimeToDayStr(battleEndTime));
        BattleDateData battleDateData = player.getUserAttr().getBattleDateData(gameTypeId);
        if (battleDateData == null) {
            battleDateData = new BattleDateData();
            battleDateData.setGameTypeId(gameTypeId);
            player.getUserAttr().getBattleDateData().put(gameTypeId, battleDateData);
        }

        if (battleDateData.getDateList().size() >= MiscConf.getInstance().getMiscConf().getBattleDateDataMaxSize()) {
            int oldestDate = 0;
            for (Integer dateNum : battleDateData.getDateListList()) {
                if (oldestDate == 0 || dateNum < oldestDate) {
                    oldestDate = dateNum;
                }
            }
            battleDateData.removeDateList(oldestDate);
        }

        if (!battleDateData.getDateList().contains(battleStartDate)) {
            battleDateData.getDateList().add(battleStartDate);
            LOGGER.debug("LetsGoPlayerBattleMgr addBattleDateData success, uid:{} matchType:{} gameType:{} battleEndTime:{}, size:{}",
                    player.getUid(), matchType, gameTypeId, battleEndTime, battleDateData.getDateList().size());
            player.getPlayerGameModeReturnManager().sendLastRootTypeBattleTimeNtf(gameTypeId,false);
        }
    }

    private void clearOldestBattleResultData(BattleModeData modeData) {
        long oldestBattleId = 0;
        long oldestEndTime = 0;
        for (long oldBattleId : modeData.getRecentBattleResultData().keySet()) {
            BattleResultData oldBattleResultData = modeData.getRecentBattleResultData(oldBattleId);
            if (oldestEndTime == 0 || oldestEndTime > oldBattleResultData.getEndTime()) {
                oldestEndTime = oldBattleResultData.getEndTime();
                oldestBattleId = oldBattleResultData.getId();
            }
        }
        modeData.removeRecentBattleResultData(oldestBattleId);
    }

    private void clearBattleResultData(BattleModeData modeData, int targetNum) {
        if (modeData != null && modeData.getRecentBattleResultDataSize() > targetNum) {
            ArrayList<BattleResultData> sortedBattleResultData = new ArrayList<BattleResultData>(
                    modeData.getRecentBattleResultData().values());
            sortedBattleResultData.sort(new Comparator<BattleResultData>() {
                @Override
                public int compare(BattleResultData o1, BattleResultData o2) {
                    return Long.compare(o2.getEndTime(), o1.getEndTime());
                }
            });

            sortedBattleResultData.forEach((battleResultData) -> {
                if (modeData.getRecentBattleResultDataSize() > targetNum) {
                    modeData.getRecentBattleResultData().remove(battleResultData.getId());
                }
            });
        }
    }

    private void clearBattleDateData() {
        int maxSize = MiscConf.getInstance().getMiscConf().getBattleDateDataMaxSize();
        for (BattleDateData battleDateData : player.getUserAttr().getBattleDateData().values()) {
            if (battleDateData != null && battleDateData.getDateList().size() > maxSize) {
                List<Integer> sortedBattleDateData = new ArrayList<>(battleDateData.getDateListList());
                sortedBattleDateData.sort(Integer::compareTo);
                for (Integer dateNum : sortedBattleDateData) {
                    if (battleDateData.getDateList().size() > maxSize) {
                        battleDateData.removeDateList(dateNum);
                    }
                }
            }
        }
    }

    @Override
    public void onLogin() throws NKCheckedException {
        super.onLogin();
        for (ResMatch.MatchType gameCfg : MatchTypeData.getInstance().dataArray) {
            BattleModeData modeData = player.getUserAttr().getPlayerPublicSummaryInfo()
                    .getBattleModeData(gameCfg.getId());
            if (modeData == null) {
                continue;
            }
            clearBattleResultData(modeData, gameCfg.getBattleRecordCnt());
        }

        clearBattleDateData();
    }


    private NKPair<Integer, HashMap<Integer, Integer>> getBattleDropByConfig(GlobalBattleResult battleResult) {
        HashMap<Integer, Integer> battleDrop = new HashMap<>();
        // TODO 掉落配置逻辑
        ResMatch.MatchType gameCfg = MatchTypeData.getInstance().get(battleResult.getMatchType());
        // 普通温暖局不掉落
        int dropId = battleResult.isGuideWarmRound() && gameCfg.getWarmDropId() > 0
                ? gameCfg.getWarmDropId() : Math.max(gameCfg.getDropId(), 0);
        ResLevelDrop.LevelDropConf levelDropConf = LevelDropConfData.getInstance().get(dropId);
        if (null == levelDropConf) {
            LOGGER.error("GenerateLevelDrop invalid dropId: {}", dropId);
            return new NKPair<>(0, battleDrop);
        }
        if (levelDropConf.getGuaranteedRewardCount() > 0) {
            levelDropConf.getGuaranteedRewardList().forEach((item) -> {
                battleDrop.merge(item.getItemId(), item.getItemNum(), (oldValue, newValue) -> oldValue + newValue);
                LOGGER.debug("getBattleDropByConfig addGuaranteedReward dropId:{}, rewardId:{} rewardNum:{}",
                        levelDropConf.getId(), item.getItemId(), item.getItemNum());
            });
        }
        if (levelDropConf.getDropConditionCount() > 0) {
            for (LevelBattleResult levelBattleResult : battleResult.getLevelBattleResults()) {
                if (dropId > 0) {
                    levelBattleResult = addDropLevelEvent(battleResult.getMatchType(), levelBattleResult);
                    HashMap<Integer, Integer> levelDrop = LevelDropGenerator.getInstance().generateLevelDrop(
                            levelDropConf, levelBattleResult);
                    if (levelDrop != null) {
                        levelDrop.forEach((k, v) -> {
                            battleDrop.merge(k, v, (oldValue, newValue) -> oldValue + newValue);
                        });
                    }
                }
            }
        }
        return new NKPair<>(dropId, battleDrop);
    }

    // 添加用于结算的关卡事件
    private LevelBattleResult addDropLevelEvent(int matchType, LevelBattleResult levelBattleResult) {
        levelBattleResult.addLevelBattleEvent(BattleEventType.BET_WEEK, DateUtils.getDayOfWeek(DateUtils.currentTimeMillis()) - 1);
        levelBattleResult.addLevelBattleEvent(BattleEventType.BET_COMPETITION_CNT_DAY, player.getArenaMgr().getGameTimesOfLevelDrop(matchType, PlayerGameDataStatType.PGDST_GameTimesDaily));
        levelBattleResult.addLevelBattleEvent(BattleEventType.BET_COMPETITION_CNT_WEEK, (int)(player.getPlayerGameTimesStat(matchType, PlayerGameDataStatType.PGDST_GameTimesWeekly, false)));
        levelBattleResult.addLevelBattleEvent(BattleEventType.BET_COMPETITION_WIN_CNT_DAY, player.getArenaMgr().getGameTimesOfLevelDrop(matchType, PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2Daily));
        levelBattleResult.addLevelBattleEvent(BattleEventType.BET_COMPETITION_WIN_CNT_WEEK, (int)(player.getPlayerGameTimesStat(matchType, PlayerGameDataStatType.PGDST_WinTimesWeekly, false)));
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("player {} {}, LevelBattleResult:{}", player.getUid(), player.getName(), levelBattleResult);
        }
        return levelBattleResult;
    }

    private NKPair<Integer, HashMap<Integer, Integer>> ArenaBattleDrop(long battleId,
            GlobalBattleResult globalBattleResult,
            NKPair<Integer, HashMap<Integer, Integer>> battleDropInfo) {

        // 先把旧数据清除
        player.clearArenaHeroCoinDropInfo();

        int matchType = globalBattleResult.getMatchType();
        ResLevelDropArena.LevelDropArenaConfig ldaConfig = LevelDropArenaData.getInstance().get(matchType);
        if (ldaConfig == null) {
            return battleDropInfo;
        }
        if (globalBattleResult.isGiveUp()) {
            LOGGER.info("ArenaBattleDrop, player {} {} {} giveUp",
                    player.getUid(), player.getOpenId(), player.getName());
            return battleDropInfo;
        }


        int totalKill = globalBattleResult.moBa.getCommon().getArenaTotalKill();
        int totalAssist = globalBattleResult.moBa.getCommon().getArenaTotalAssist();
        int totalDead = globalBattleResult.moBa.getCommon().getArenaTotalDead();
        int rank = globalBattleResult.getFinalTeamRank();
        int goals = globalBattleResult.moBa.getFootball().getFootballGoals();

        float scoreValueB = 0.0f;
        if(6300 == matchType){ // 玩法id 暂时魔数判断后续引用地方增多增加conf模块
            scoreValueB = (float)(totalKill + totalAssist) / (2 * (totalDead == 0 ? 1 : totalDead )) + goals;
        }else{
            scoreValueB = totalKill + totalAssist - totalDead;
        }

        int score = (int) (ldaConfig.getScoreBase()
                + (ldaConfig.getScoreParamC() - rank) * ldaConfig.getScoreParamA()
                + ldaConfig.getScoreParamBFloat() * scoreValueB);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("matchType({}), score<{}> = INT[base<{}> + (paramC<{}> - rank<{}>) * paramA<{}> + paramB<{}> * scoreValueB<{}>" +
                            " (kill<{}>;assist<{}>;dead<{}>;goals<{}>)]", matchType, score, ldaConfig.getScoreBase(), ldaConfig.getScoreParamC(),
                    rank, ldaConfig.getScoreParamA(), ldaConfig.getScoreParamBFloat(), scoreValueB, totalKill, totalAssist, totalDead, goals);
        }
        if (score <= 0) {
            LOGGER.error("ArenaBattleDrop error, player:{} {}, gametype:{}, totalKill:{}, totalAssist:{}, totalDead:{}, rank:{}, conf:{}, score:{}",
                player.getUid(), player.getName(), matchType, totalKill, totalAssist, totalDead, rank, ldaConfig, score);
            return battleDropInfo;
        }

        LevelDropItemInfo.Builder dropBuilder = LevelDropItemInfo.newBuilder().setItemId(ldaConfig.getItemId());
        dropBuilder.setBaseCount(score);

        // week加成
        int week = DateUtils.getDayOfWeek(DateUtils.currentTimeMillis()) - 1;
        int weekAddtion = 0;
        if (ldaConfig.getWeekAddtionsList().size() >= week && ldaConfig.getWeekAddtionsList().get(week) > 0) {
            weekAddtion = ldaConfig.getWeekAddtionsList().get(week);
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(BattleRewardAdditionType.BRAT_ARENA_WEEK_ADDTION_VALUE);
            additionInfo.setValue(weekAddtion);
            dropBuilder.addAddition(additionInfo);
        }

        // 组队加成
        boolean isInTeam = globalBattleResult.isTeamMatch();
        int teamAddtion = 0;
        if (isInTeam && ldaConfig.getTeamAddtion() > 0 && ldaConfig.getTeamAddtion() > 0) {
            teamAddtion = ldaConfig.getTeamAddtion();
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(BattleRewardAdditionType.BRAT_ARENA_TEAM_ADDTION_VALUE);
            additionInfo.setValue(ldaConfig.getTeamAddtion());
            dropBuilder.addAddition(additionInfo);
        }

        // 首胜加成
        int firstWinAddtion = 0;
        int arenaWinTimesDaily = player.getArenaMgr().getGameTimesOfLevelDrop(matchType, PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2Daily);
        if (arenaWinTimesDaily == 1 && ldaConfig.getFirstWinAddtion() > 0) {
            firstWinAddtion = ldaConfig.getFirstWinAddtion();
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(BattleRewardAdditionType.BRAT_ARENA_FIRST_WIN_ADDTION_VALUE);
            additionInfo.setValue(firstWinAddtion);
            dropBuilder.addAddition(additionInfo);
        }

        score = score * (100 + weekAddtion + teamAddtion + firstWinAddtion) / 100;

        int key = battleDropInfo.getKey();
        HashMap<Integer, Integer> battleDrop = battleDropInfo.getValue();
        battleDrop.merge(ldaConfig.getItemId(), score, (oldValue, newValue) -> oldValue + newValue);
        player.setArenaHeroCoinDropInfo(dropBuilder);
        LOGGER.info("ArenaBattleDrop,player:{} {} {}, gametype:{}" +
                ", totalKill:{}, totalAssist:{}, totalDead:{}, rank:{} goals:{}" +
                        ", week:{}, weekAddtion:{}, isTeam:{}, teamAddtion:{}, firstWinAddtion:{}" +
                        ", basescore:{}, addtionscore:{}, conf:{}",
                player.getOpenId(), player.getUid(), player.getName(), matchType,
                totalKill, totalAssist, totalDead, rank, goals,
                week, weekAddtion, isInTeam, teamAddtion, firstWinAddtion,
                dropBuilder.getBaseCount(), score, ldaConfig);

        return new NKPair<>(key, battleDrop);
    }

    private NKPair<Integer, HashMap<Integer, Integer>> arenaBattleDropSpecial(long battleId, GlobalBattleResult globalBattleResult,
                                                                              NKPair<Integer, HashMap<Integer, Integer>> battleDropInfo) {
        LevelDropArenaSpecialData ldasdInst = LevelDropArenaSpecialData.getInstance();
        if (null == ldasdInst) {
            LOGGER.error("player({} {}) invalid LevelDropArenaSpecialData inst", player.getUid(), player.getName());
            return battleDropInfo;
        }
        Map<Integer, ResLevelDropArena.LevelDropArenaSpecialConfig> ldasAllConfig = LevelDropArenaSpecialData.getInstance().getAll();
        if (ldasAllConfig == null) {
            LOGGER.error("player({} {})LevelDropArenaSpecialData getAll error.", player.getUid(), player.getName());
            return battleDropInfo;
        }
        HashMap<Integer, Integer> battleDrop = battleDropInfo.getValue();
        // 逐条检查，满足条件即发放奖励
        for (Map.Entry<Integer, ResLevelDropArena.LevelDropArenaSpecialConfig> entry : ldasAllConfig.entrySet()) {
            ResLevelDropArena.LevelDropArenaSpecialConfig config = entry.getValue();
            boolean isAllConditionMatch = (config.getConditionsCount() > 0);
            for (ResLevelDropArena.LevelDropArenaSpecialCondition condition : config.getConditionsList()) {
                if (!checkArenaBattleDropSpecialCondition(battleId, globalBattleResult, condition)){
                    isAllConditionMatch = false;
                    break;
                }
            }
            if (isAllConditionMatch) {
                for (int i = 0; i < config.getRewardIdCount() && i < config.getRewardNumCount(); ++i) {
                    int rewardId = config.getRewardId(i);
                    int rewardNum = config.getRewardNum(i);
                    battleDrop.merge(rewardId, rewardNum, Integer::sum);
                }
            }
            LOGGER.info("player({} {}) config({}:{}) isAllConditionMatch({}) rewardIds({}) rewardNum({})",
                    player.getUid(), player.getName(), config.getId(), config.getDesc(), isAllConditionMatch,
                    config.getRewardIdList(), config.getRewardNumList());
        }
        return new NKPair<>(battleDropInfo.getKey(), battleDrop);
    }

    private boolean checkArenaBattleDropSpecialCondition(long battleId, GlobalBattleResult globalBattleResult,
                                                         ResLevelDropArena.LevelDropArenaSpecialCondition condition) {
        int value = 0;
        int gameType = globalBattleResult.getMatchType();
        switch (condition.getType()) {
            case ResLevelDropArena.LevelDropArenaSpecialConditionType.LDASCT_GameType_VALUE:
                value = gameType;
                break;
            case ResLevelDropArena.LevelDropArenaSpecialConditionType.LDASCT_GameTimesWithTeamRankTop2Daily_VALUE:
                value = (int) player.getPlayerGameTimesStat(gameType, PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2Daily, false);
                break;
            default:
                return false;
        }
        boolean result = checkArenaBattleDropSpecialConditionParams(condition.getOpt(), value, condition.getParamsList());
        LOGGER.info("player({} {}) conditionType({}) result({}) value({}) opt({}) params:{}",
                player.getUid(), player.getName(), condition.getType(), result, value, condition.getOpt(), condition.getParamsList());
        return result;
    }

    private boolean checkArenaBattleDropSpecialConditionParams(int opt, int value, List<Integer> params) {
        int target = Math.toIntExact(params.get(0));
        switch (opt) {
            case DsStatComparisonOp.DSCO_Equal_VALUE:
                return value == target;
            case DsStatComparisonOp.DSCO_MoreOrEqual_VALUE:
                return value >= target;
            case DsStatComparisonOp.DSCO_More_VALUE:
                return value > target;
            case DsStatComparisonOp.DSCO_LessOrEqual_VALUE:
                return value <= target;
            case DsStatComparisonOp.DSCO_Less_VALUE:
                return value < target;
            case DsStatComparisonOp.DSCO_AnyOf_VALUE: {
                for (int i = 0; i < params.size(); ++i) {
                    if (Math.toIntExact(params.get(i)) == value) {
                        return true;
                    }
                }
                return false;
            }
            case DsStatComparisonOp.DSCO_WithIn_VALUE: {
                int upper = target;
                if (params.size() > 1) {
                    upper = Math.toIntExact(params.get(1));
                }
                return target <= value && value <= upper;
            }
            default:
                return false;
        }
    }

    private void appendArenaAddtionList(LevelDropItemInfo.Builder dropBuilder, int itemId, int addValue) {
        // 峡谷币加成
        if (player.getArenaHeroCoinDropInfo() != null && player.getArenaHeroCoinDropInfo().getItemId() == itemId) {
            dropBuilder.setBaseCount(player.getArenaHeroCoinDropInfo().getBaseCount());
            int totalAddtion = 0;
            for (var addtion : player.getArenaHeroCoinDropInfo().getAdditionList()) {
                dropBuilder.addAddition(addtion);
                totalAddtion = totalAddtion + addtion.getValue();
            }
            // 减去其他加成项，剩余就是首局的加成
            int baseScoreAfterAddtion = dropBuilder.getBaseCount() * (100 + totalAddtion) / 100;
            if (addValue > baseScoreAfterAddtion) {
                KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                additionInfo.setKey(BattleRewardAdditionType.BRAT_ARENA_FIRST_COMPETION_ADDTION_DAILY_VALUE);
                additionInfo.setValue(addValue - baseScoreAfterAddtion);
                dropBuilder.addAddition(additionInfo);
            }
        }
    }

    // 修正掉落奖励限制数量
    private Integer fixDropItemNumLimited(int itemId, int itemNum, LevelDropItemInfo.Builder dropBuilder) {
        LevelDropLimitConfig dropLimitConfig = LevelDropLimitConfData.getInstance().get(itemId);
        if (dropLimitConfig != null) {
            if (!LevelDropLimitConfData.getInstance().checkItemDropInTimeRange(dropLimitConfig)) {
                return 0;
            }
            int limitNum = getDropItemLimitNum(dropLimitConfig);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("drop limit:item:{}, num:{}/{}, week:{}, conf:{}",
                        itemId, limitNum, itemNum, DateUtils.getDayOfWeek(DateUtils.currentTimeMillis())-1, dropLimitConfig);
            }
            dropBuilder.setLimitCount(limitNum);
            if (dropLimitConfig.getLimitType() == LimitType.LT_DailyLimit) {
                dropBuilder.setDayLimitCount(limitNum);
            }
            else if (dropLimitConfig.getLimitType() == LimitType.LT_WeeklyLimit) {
                dropBuilder.setWeekLimitCount(limitNum);
            }
            if (player.getLimitManager().getLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit) == null ||
                    !player.getLimitManager().getLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit).getLimitInfo()
                            .containsKey((long) itemId)) {
                player.getLimitManager().registerLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit, itemId,
                        limitNum);
            }
            long currentNum = player.getLimitManager().getValue(CommonLimitType.CLT_BattleLevelDropLimit, itemId);
            dropBuilder.setCurrentCount((int) currentNum);
            if (currentNum + itemNum >= limitNum) {
                int realAddItemNum = Math.toIntExact(limitNum - currentNum <= 0 ? 0 : limitNum - currentNum);
                if (realAddItemNum == 0) {
                    if (dropLimitConfig.getLimitType() == LimitType.LT_DailyLimit) {
                        dropBuilder.setHasReachedDayLimit(true);
                    }
                    else if (dropLimitConfig.getLimitType() == LimitType.LT_WeeklyLimit) {
                        dropBuilder.setHasReachedWeekLimit(true);
                    }
                }
                return realAddItemNum;
            }
        }
        return itemNum;
    }

    private void addDropItemNumLimited(int itemId, long itemNum) {
        LevelDropLimitConfig dropLimitConfig = LevelDropLimitConfData.getInstance().get(itemId);
        if (dropLimitConfig == null) {
            return;
        }
        if (!LevelDropLimitConfData.getInstance().checkItemDropInTimeRange(dropLimitConfig)) {
            return;
        }
        if (player.getLimitManager().getLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit) == null ||
                !player.getLimitManager().getLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit).getLimitInfo()
                        .containsKey((long) itemId)) {
            player.getLimitManager()
                    .registerLimitInfo(CommonLimitType.CLT_BattleLevelDropLimit, itemId, getDropItemLimitNum(dropLimitConfig));
        }
        player.getLimitManager().addValue(CommonLimitType.CLT_BattleLevelDropLimit, itemId, itemNum,
                getExpireTimeMsByLimitType(dropLimitConfig.getLimitType()));
    }

    private int getDropItemLimitNum(LevelDropLimitConfig dropLimitConfig) {
        int limitNum = dropLimitConfig.getLimitNum();
        if (dropLimitConfig.getWeekLimitNumList().isEmpty()
                || dropLimitConfig.getLimitType() == LimitType.LT_WeeklyLimit) {
            return limitNum;
        }
        int curWeek = DateUtils.getDayOfWeek(DateUtils.currentTimeMillis()) - 1;
        if (dropLimitConfig.getWeekLimitNumList().size() <= curWeek) {
            return limitNum;
        }
        int weekLimitNum = dropLimitConfig.getWeekLimitNum(curWeek);
        if (limitNum == 0) {
            return weekLimitNum;
        } else if (weekLimitNum == 0){
            return limitNum;
        } else {
            return Math.min(limitNum, dropLimitConfig.getWeekLimitNum(curWeek));
        }
    }

    private long getExpireTimeMsByLimitType(LimitType limitType) {
        long currentTimeMs = Framework.currentTimeMillis();
        switch (limitType) {
            case LT_DailyLimit:
                return DateUtils.getDayEndTimeMs(currentTimeMs);
            case LT_WeeklyLimit:
                return DateUtils.getNextWeekNumTime(currentTimeMs, 1);
            case LT_MonthlyLimit:
                return DateUtils.getFirstDayTimeOfNextMonthTime(currentTimeMs);
            case LT_YearlyLimit:
                return DateUtils.getFirstDayTimeOfNextYearTime(currentTimeMs);
            case LT_LifeLongLimit:
                break;
            case LT_SeasonLimit:
                SeasonConf seasonConf = SeasonConfData.getInstance().getCurrOrLatestSeason();
                if (seasonConf == null) {
                    return -1;
                }
                return seasonConf.getEndTime().getSeconds();
            default:
        }
        return -1;
    }


    @Override
    public void ugcBattleSettlement(PlayerInteraction.PlayerInteractionData interactData, boolean multiplayer,
            int passTime) {
        // 统计对局开启次数+1
        updateGameStartCount();
        battleAddPakPlayTimes(interactData.getDsSettlementParams());
        // 战斗结算
        long battleId = interactData.getDsSettlementParams().getDetailData().getBattleId();
        int result = interactData.getDsSettlementParams().getBattleResult().getResult();
        int matchType = interactData.getDsSettlementParams().getDetailData().getMatchType();
        long ugcId = interactData.getDsSettlementParams().getUgcId();
        long roomId = interactData.getDsSettlementParams().getRoomId();
        LOGGER.info(
                "UgcSettlement player-{}, battleId = {} modeId = {}, ugcId = {}, result = {}",
                player.getUid(), battleId, matchType, ugcId, result);

        CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder = CsLetsgo.LetsGoBattleSettlementNtf.newBuilder();
        ntfBuilder.setBattleId(battleId).setResult(result).setGameId(0).setModeId(matchType);

        MatchType matchTypeConf = MatchTypeData.getInstance().get(matchType);

        GlobalBattleResult.Builder globalBattleResultBuilder = new GlobalBattleResult.Builder(
                interactData.getDsSettlementParams().getBattleResult(),
                interactData.getDsSettlementParams().getDetailData());
        globalBattleResultBuilder.setWarmRoundSettlementInfo(
                        interactData.getDsSettlementParams().getWarmRoundSettlement())
                .setRoomMemberList(interactData.getDsSettlementParams().getRoomMemberList())
                .setSideMemberList(interactData.getDsSettlementParams().getSideMemberList())
                .setChampionTeamInfo(interactData.getDsSettlementParams().getChampionTeamInfo())
                .setBattleMemberList(interactData.getDsSettlementParams().getBattleMemberList())
                .setDetailed(player.getPlayerBattleMgr().getBattleDetailedScore())
                .setTegGameId(interactData.getDsSettlementParams().getTegGameId());
        final GlobalBattleResult globalBattleResult = globalBattleResultBuilder.build();

        ntfBuilder.setDetailData(interactData.getDsSettlementParams().getDetailData());
        ntfBuilder.addAllRoomMemberUids(interactData.getDsSettlementParams().getRoomMemberList());
        ntfBuilder.addAllSideMemberUids(interactData.getDsSettlementParams().getSideMemberList());
        ntfBuilder.setSelfScore(globalBattleResult.getBattleScore());
        ntfBuilder.setSelfGrade(globalBattleResult.getMatchGrade());
        ntfBuilder.setIsGiveUp(globalBattleResult.isGiveUp() ? 1 : 0);
        // 填充玩家信息
        for (MemberBaseInfo battleMember : interactData.getDsSettlementParams().getBattleMemberList()) {
            MemberBaseInfo.Builder filterInfo = battleMember.toBuilder().clearDressUpItems();
            for (int itemId : battleMember.getDressUpItemsList()) {
                if (BackpackItem.getInstance().checkItemVer(itemId,player.getClientVersion64())) {
                    filterInfo.addDressUpItems(itemId);
                }
            }
            ntfBuilder.addBattleMember(filterInfo);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("globalBattleResult:{}", globalBattleResult.toString());
        }
        // 战斗掉落
        battleDropReward(battleId, globalBattleResult, ntfBuilder, ItemChangeReason.ICR_UgcBattleDrop);
//        if (multiplayer) {  多人的不走这里了
//            ugcSpecialSettlement(ugcId, roomId, globalBattleResult, ntfBuilder);
//            MemberBaseInfo.Builder battlePlayerMemberInfo = MemberBaseInfo.newBuilder();
//            StringBuilder memberRelationStrBuilder = new StringBuilder();
//            for (MemberBaseInfo memberInfo : interactData.getDsSettlementParams().getBattleMemberList()) {
//                if (memberInfo.getIsRobot()) {
//                    continue;
//                }
//                int relationType = player.getFriendManager().getRelationTypeVal(memberInfo.getUid());
//                if (relationType > 0) {
//                    memberRelationStrBuilder.append(memberInfo.getUid()).append(",").append(relationType).append(";");
//                }
//                if (memberInfo.getUid() == player.getUid()) {
//                    battlePlayerMemberInfo.mergeFrom(memberInfo);
//                }
//            }
//            ntfBuilder.setRuleInfo(battlePlayerMemberInfo.getRuleInfo());
//            //event dispatch
//            dispatchBattleEvent(globalBattleResult, battlePlayerMemberInfo,
//                    interactData.getDsSettlementParams().getFashionRank(), memberRelationStrBuilder.toString(),
//                    interactData.getDsSettlementParams().getRoomMemberList(), ntfBuilder);
//        }
        BattleEventData.Builder data = BattleEventData.newBuilder().setMatchType(matchType).setIsSinglePlay(true)
                .setBattleCostTime(passTime);
        if (!player.getUserAttr().getUgcMapSetInfo().getSingleStageInfo().getHasStarted()) {
            player.getUserAttr().getUgcMapSetInfo().getSingleStageInfo().setHasStarted(true);
            player.getPlayerUgcManager()
                    .triggerPlayedMapEvt(ugcId, interactData.getDsSettlementParams().getMapSource(), false, matchType,
                            data, interactData.getDsSettlementParams().getLogicMapSource(),
                            interactData.getDsSettlementParams().getFromCollectionId());
        }

        if (result == 0) {
            player.getPlayerUgcManager()
                    .triggerPlayedMapEvt(ugcId, interactData.getDsSettlementParams().getMapSource(), true, matchType,
                            data, interactData.getDsSettlementParams().getLogicMapSource(),
                            interactData.getDsSettlementParams().getFromCollectionId());
        }

        player.getPlayerUgcManager().triggerUgcSettlementEvt(ugcId, passTime);
        // ugc跑图集星活动(新版闯关挑战)
        player.getUgcMapSimpleActivityMgr().getPlayMapCollectStarActivity()
                .addStarBattleSettlement(ugcId, globalBattleResult);

        player.sendNtfMsg(MsgTypes.MSG_TYPE_LETSGOBATTLESETTLEMENTNTF, ntfBuilder);
    }

    private int getBattleUgcEnjoyTime(int matchType, LevelBattleResult levelBattleResult) {
        if (TycConfs.isOMDGame(matchType) || TycConfs.isOMDGameMulTest(matchType)) {
            return levelBattleResult.getBattleSurvivalTime() * 1000;
        }
        return levelBattleResult.getUgcInLevelTime();
    }

    private String genFriendRelationStr(Player player, Set<Long> playerUidSet) {
        if (playerUidSet == null) {
            return "";
        }
        StringBuilder memberRelationStrBuilder = new StringBuilder();
        for (long memberUid : playerUidSet) {
            if (memberUid == player.getUid()) {
                continue;
            }
            boolean gameFriend = player.getFriendManager().isGameFriend(memberUid);
            boolean platFriend = player.getFriendManager().isPlatFriend(memberUid);
            int relationType = gameFriend ? 2 : 0;
            relationType += platFriend ? 1 : 0;
            memberRelationStrBuilder.append(memberUid).append(",").append(relationType).append(";");
        }

        return memberRelationStrBuilder.toString();
    }

    private void ugcSpecialSettlement(UgcBattleResult ugcBattleResult) {

        long battleId = ugcBattleResult.getBattleId();
        long ugcId = ugcBattleResult.getUgcId();
        int logicGameSource = ugcBattleResult.getLogicGameSource();
        long mapPoolId = ugcBattleResult.getMapPoolId();
        boolean singleRoom = ugcBattleResult.isSingleRoom();
        int mapSource = ugcBattleResult.getMapSource();
        AlgoInfo recommendMapAlgoInfo = ugcBattleResult.getRecommendMapAlgoInfo();
        String fromCollectionId = ugcBattleResult.getFromCollectionId();
        int matchType = ugcBattleResult.getMatchType();
        BattleResultCode battleResult = ugcBattleResult.getBattleResult();
        String sceneStr = ugcBattleResult.getScenesStr();
        int lastSceneId = ugcBattleResult.getLastSceneId();
        var multiRoundScoreInfo = ugcBattleResult.getUgcMultiRoundScoreSettlementInfo();
        int multiRoundScore = 0;
        if (multiRoundScoreInfo.getMemberScoreInfosCount() != 0 && multiRoundScoreInfo.getIsEnd()) {
            for (var oneScoreInfo : multiRoundScoreInfo.getMemberScoreInfosList()) {
                if (oneScoreInfo.getUid() == player.getUid()) {
                    multiRoundScore = oneScoreInfo.getCurScoreNum() + oneScoreInfo.getScoreIncrement();
                    break;
                }
            }
        }

        fillUgcInfo(ugcId, ugcBattleResult);
        int finalMultiRoundScore = multiRoundScore;
        ugcBattleResult.getLevelBattleResults().forEach(levelBattleResult -> {
            try {
                BestRecordData recordData = new BestRecordData();
                recordData.isPass = ugcBattleResult.getBattleResult().getNumber() == 0;
                recordData.passSec = levelBattleResult.getCostTime();
                recordData.ugcId = ugcId;
                recordData.creatorId = player.getCreatorId();
                recordData.gameSource = 0;
                recordData.logicMapSource = logicGameSource;
                recordData.fromCollectionId = fromCollectionId;
                recordData.nickName = player.getName();
                recordData.avatar = player.getProfile();
                recordData.isMidJoin = levelBattleResult.isMidJoin();
                recordData.lastEndBattleTime = ugcBattleResult.getBattleEndTime();
                recordData.passScore = levelBattleResult.getPlayScore();
                recordData.multiRoundScore = finalMultiRoundScore;
                SsUgcsvr.RpcMapUgcBestRecordRes rpcRecordRes = SsRpcUgcManager.getInstance()
                        .ugcBestRecord(player, recordData);


                ResActivity.SummerFlashPhotoConf summerFlashPhotoConf= SummerFlashPhotoConf.getInstance().get(1);
                if(recordData.passScore>0 && summerFlashPhotoConf!=null &&ugcId==SummerFlashPhotoConf.getInstance().get(1).getMapId()){
                    diapatchScoreEvent(levelBattleResult.getPlayScore());
                }


                //在gamesvr的个人信息设置玩过的地图数量
                player.setPlayerGameTime(PlayerGameTimeType.PGTT_PlayedMaps, rpcRecordRes.getPlayedMapCount());
                if (rpcRecordRes.getIsBestRecord()) {
                    ugcBattleResult.setBestRecord(true);
                    // 生存类和冲锋团队竞技类地图最佳记录为false
                    ResUGCEditor.Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance()
                            .get(rpcRecordRes.getTemplateId());
                    if (template != null) {
                        if (template.getType() != UGCMapType.Topspeed
                                && template.getType() != UGCMapType.UGCRoundGame) {
                            ugcBattleResult.setBestRecord(false);
                        } else if (template.getType() == UGCMapType.UGCRoundGame
                                && rpcRecordRes.getModelType() != UgcMapModelType.UgcMap_Speed_VALUE) {
                            ugcBattleResult.setBestRecord(false);
                        }
                    } else {
                        LOGGER.error("get Item_UGCEditorMapTemplate fail:{}, {}", ugcId, rpcRecordRes.getTemplateId());
                    }
                    long incr = CommonAttr.incr(player.getUid(), CommonAttr.CommonAttrType.UGC_PASS_LEVEL_RECORD,
                            ugcId);
                    if (incr == 1) {
                        new PlayerUgcMapBestCountEvent(player).dispatch();
                    }
                }
            } catch (Exception e) {
                LOGGER.error("ugcBestRecord fail battleId:{} ugcId{} e=", ugcBattleResult.getBattleId(), ugcId, e);
            }

            PublishItem mapInfo = ugcBattleResult.getUgcMapInfo();
            if (mapInfo != null && mapInfo.getCreatorId() != player.getCreatorId()) {
                player.getUgcPlayerGrowUpMgr()
                        .getWeeklyActivityDegreeItemFromBattle(ugcBattleResult.getBattleId(), mapInfo.getUgcId(),
                                levelBattleResult.getBattleUseTime(), mapSource);
            }

            String version = "";
            if (mapInfo != null) {
                UgcMapMetaInfo metaInfo = CommonUtil.getPublishMetaInfo(mapInfo.getMetaInfoList());
                version = metaInfo.getVersion();
            }
            int canvasId = ugcBattleResult.getCanvasId();
            Map<String, String> sourceInfo = ugcBattleResult.getSourceInfo();
            boolean isMidJoin = levelBattleResult.isMidJoin();
            int killNum = levelBattleResult.getKillTimes();
            int isGameWatch = levelBattleResult.getUGCIsGameWatch();
            int midJoinType = CommonUtil.convertJoinReasonToMidJoinSourceType(ugcBattleResult.getMatchId(), isMidJoin,
                    ugcBattleResult.getJoinReason());
            int enjoyTime = getBattleUgcEnjoyTime(matchType, levelBattleResult);
            String friendRelationStr = genFriendRelationStr(player, ugcBattleResult.getPlayerUidSet());
            UgcMapEnjoyResultData.Builder resultDataBuilder = UgcMapEnjoyResultData.newBuilder().setMapNum(0).setHP(0).setEnjoyTime(enjoyTime).setMapID(String.valueOf(ugcId))
                    .setMapType(mapInfo.getType()).setMapTagName(mapInfo.getTags()).setCreatorRoleID(String.valueOf(mapInfo.getUid())).setCreatorOpenID(mapInfo.getOpenId())
                    .setBattleID(String.valueOf(battleId)).setResult(battleResult.getNumber())
                    .setFailReason(battleResult.getNumber() == 1 ? (levelBattleResult.isGiveUp() ? 2 : 1) : 0).setRoomID(String.valueOf(ugcBattleResult.getRoomId())).setRoomNum(0)
                    .setRoomAudienceNum(0).setGameSource(mapSource).setUsersNum(ugcBattleResult.getRoomMemberCnt()).setRcmdInfo(recommendMapAlgoInfo.getRecId())
                    .setABTestInfo(recommendMapAlgoInfo.getExpTag()).setCanvasID(canvasId).setTabDesc(ugcBattleResult.getTabDesc()).setExposePos(0).setPoints(0)
                    .setRecord(levelBattleResult.getCostTime()).setRecordType(1).setRank(levelBattleResult.getRank()).setDeathNum(levelBattleResult.getDeadTimes()).setDifficulty(0)
                    .setModule("").setActivityId("").setThemeName("").setSearchId("").setMatchId(ugcBattleResult.getMatchId()).setBattleType(matchType)
                    .setCamp(levelBattleResult.getBattleEventValue(BattleEventType.BET_LEVEL_CAMP_TYPE)).setUgcVersion(version)
                    .setMapCollectId(ugcBattleResult.getFromCollectionId()).setSubTabName("").setIsMidJoin(isMidJoin).setIsPseudoGame(singleRoom ? 1 : 0).setMapPoolId(mapPoolId)
                    .setKillNum(killNum).setIsGameWatch(isGameWatch).setScenesStr(sceneStr).setScenesId(lastSceneId).setLobbyType(ugcBattleResult.getLobbyType())
                    .setLobbyMapId(ugcBattleResult.getLobbyMapId()).setMidJoinType(midJoinType).setSourceInfo(sourceInfo).setFriendRelation(friendRelationStr)
                    .setIsFastMulti(ugcBattleResult.isQuickStart() ? 1 : 0);
            UgcDataFlowManager.getInstance().sendUGCMapEnjoyResultFlow(player, resultDataBuilder.build());
        });

        /*roomStartBattleNotify中已经赋值过了,这里不在赋值了
        if (ntfBuilder.hasUgcMapInfo()) {
            player.getPlayerUgcManager().completeUgcMapTopics(ntfBuilder.getUgcMapInfoBuilder().getTopicsBuilderList());
            for (var topic : ntfBuilder.getUgcMapInfo().getTopicsList()) {
                if (topic.getType() == 0) {
                    player.getUserAttr().getUgcMapSetInfo().getSingleStageInfo().addBlueTopicId(topic.getId());
                    continue;
                }
                if (topic.getType() == 1) {
                    player.getUserAttr().getUgcMapSetInfo().getSingleStageInfo().addGoldTopicId(topic.getId());
                    continue;
                }
            }
        }*/
    }



    private void diapatchScoreEvent(int score){
        UgcSettleScoreEvent ugcSettleScoreEvent = new UgcSettleScoreEvent(player.getConditionMgr()).setScore(score);
        player.getPlayerEventManager().dispatch(ugcSettleScoreEvent);


        UgcSettleJoinCntEvent ugcSettleJoinCntEvent = new UgcSettleJoinCntEvent(player.getConditionMgr());
        player.getPlayerEventManager().dispatch(ugcSettleJoinCntEvent);

        LOGGER.debug("diapatchScoreEvent score:{} uid:{}", score, player.getUid());
    }

    public void sendOMDMultiTestLog(UgcBattleResult ugcBattleResult) {

        long battleId = ugcBattleResult.getBattleId();
        long ugcId = ugcBattleResult.getUgcId();
        int logicGameSource = ugcBattleResult.getLogicGameSource();
        long mapPoolId = ugcBattleResult.getMapPoolId();
        boolean singleRoom = ugcBattleResult.isSingleRoom();
        int mapSource = ugcBattleResult.getMapSource();
        AlgoInfo recommendMapAlgoInfo = ugcBattleResult.getRecommendMapAlgoInfo();
        String fromCollectionId = ugcBattleResult.getFromCollectionId();
        int matchType = ugcBattleResult.getMatchType();
        BattleResultCode battleResult = ugcBattleResult.getBattleResult();
        String sceneStr = ugcBattleResult.getScenesStr();
        int lastSceneId = ugcBattleResult.getLastSceneId();

        try {
            UgcBaseInfo ugcBaseInfo = SsRpcUgcManager.getInstance()
                    .ugcGetBaseInfo(player, ugcId, UgcPlayType.UgcMulPlay);
            if (ugcBaseInfo == null) {
                LOGGER.error("mapInfo is not find mapId:{} uid:{}", ugcId, player.getUid());
                return;
            }

            UgcMapMetaInfo metaInfo = CommonUtil.getPublishMetaInfo(ugcBaseInfo.getMdList().getInfoList());
            String version = metaInfo == null ? "" : metaInfo.getVersion();

            ugcBattleResult.getLevelBattleResults().forEach(levelBattleResult -> {
                try {
                    BestRecordData recordData = new BestRecordData();
                    recordData.isPass = ugcBattleResult.getBattleResult().getNumber() == 0;
                    recordData.passSec = levelBattleResult.getCostTime();
                    recordData.ugcId = ugcId;
                    recordData.creatorId = player.getCreatorId();
                    recordData.gameSource = 0;
                    recordData.logicMapSource = logicGameSource;
                    recordData.fromCollectionId = fromCollectionId;
                    recordData.nickName = player.getName();
                    recordData.avatar = player.getProfile();
                    recordData.isMidJoin = levelBattleResult.isMidJoin();
                    recordData.lastEndBattleTime = ugcBattleResult.getBattleEndTime();
                    recordData.passScore = levelBattleResult.getPlayScore();
                    SsUgcsvr.RpcMapUgcBestRecordRes rpcRecordRes = SsRpcUgcManager.getInstance()
                            .ugcBestRecord(player, recordData);
                    //在gamesvr的个人信息设置玩过的地图数量
                } catch (Exception e) {
                    LOGGER.error("ugcBestRecord fail battleId:{} ugcId{} e=", ugcBattleResult.getBattleId(), ugcId, e);
                }

                int canvasId = ugcBattleResult.getCanvasId();
                Map<String, String> sourceInfo = ugcBattleResult.getSourceInfo();
                boolean isMidJoin = levelBattleResult.isMidJoin();
                int killNum = levelBattleResult.getKillTimes();
                int isGameWatch = levelBattleResult.getUGCIsGameWatch();
                int enjoyTime = getBattleUgcEnjoyTime(matchType, levelBattleResult);
                UgcMapEnjoyResultData.Builder resultDataBuilder = UgcMapEnjoyResultData.newBuilder().setMapNum(0).setHP(0).setEnjoyTime(enjoyTime).setMapID(String.valueOf(ugcId))
                        .setMapType(ugcBaseInfo.getUgcType()).setMapTagName(ugcBaseInfo.getTags()).setCreatorRoleID("0").setCreatorOpenID("0").setBattleID(String.valueOf(battleId))
                        .setResult(battleResult.getNumber()).setFailReason(battleResult.getNumber() == 1 ? (levelBattleResult.isGiveUp() ? 2 : 1) : 0)
                        .setRoomID(String.valueOf(ugcBattleResult.getRoomId())).setRoomNum(0).setRoomAudienceNum(0).setGameSource(mapSource)
                        .setUsersNum(ugcBattleResult.getRoomMemberCnt()).setRcmdInfo(recommendMapAlgoInfo.getRecId()).setABTestInfo(recommendMapAlgoInfo.getExpTag())
                        .setCanvasID(canvasId).setTabDesc(ugcBattleResult.getTabDesc()).setExposePos(0).setPoints(0).setRecord(levelBattleResult.getCostTime()).setRecordType(1)
                        .setRank(levelBattleResult.getRank()).setDeathNum(levelBattleResult.getDeadTimes()).setDifficulty(0).setModule("").setActivityId("").setThemeName("")
                        .setSearchId("").setMatchId(ugcBattleResult.getMatchId()).setBattleType(matchType)
                        .setCamp(levelBattleResult.getBattleEventValue(BattleEventType.BET_LEVEL_CAMP_TYPE)).setUgcVersion(version).setMapCollectId("").setSubTabName("")
                        .setIsMidJoin(isMidJoin).setIsPseudoGame(singleRoom ? 1 : 0).setMapPoolId(mapPoolId).setKillNum(killNum).setIsGameWatch(isGameWatch).setScenesStr(sceneStr)
                        .setScenesId(lastSceneId).setLobbyType(ugcBattleResult.getLobbyType()).setLobbyMapId(ugcBattleResult.getLobbyMapId()).setMidJoinType(0)
                        .setSourceInfo(sourceInfo).setIsFastMulti(ugcBattleResult.isQuickStart() ? 1 : 0);
                UgcDataFlowManager.getInstance().sendUGCMapEnjoyResultFlow(player, resultDataBuilder.build());
            });
        } catch (Exception e) {
            LOGGER.error("sendOMDMultiTestLog error :", e);
        }

    }

    public void sendUgcTestSpecialSettlementTlog(GlobalBattleResult globalBattleResult, Set<Long> playerUidSet,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, MemberBaseInfo.Builder memberBaseInfo,
            AlgoInfo recommendMapAlgoInfo, BattleSettlementExtraInfo settlementExtraInfo) { //ugc测试，只打tlog日志，不能有实际任何收益

        boolean omdGameMulTest = TycConfs.isOMDGameMulTest(globalBattleResult.getMatchType());
        if (omdGameMulTest) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("sendUgcTestSpecialSettlementTlog end battle not to tlog ugcId={} matchTypeId={}",
                        globalBattleResult.getUgcId(), globalBattleResult.getMatchType());
            }
            return;
        }

        UgcBaseInfo ugcBaseInfo = SsRpcUgcManager.getInstance()
                .ugcGetBaseInfo(player, globalBattleResult.getUgcId(), UgcPlayType.UgcMulPlay);
        if (ugcBaseInfo == null) {
            LOGGER.error("mapInfo is not find mapId:{} uid:{}", globalBattleResult.getUgcId(), player.getUid());
            return;
        }
        globalBattleResult.getLevelBattleResults().forEach(levelBattleResult -> {
            MatchRuleInfo matchRuleInfo = memberBaseInfo.getRuleInfo();
            int gameSource = TlogFlowMgr.getUgcMapBattleGameSource(18, ntfBuilder.getModeId(), matchRuleInfo);
            // 处理下新的上报方式
            MatchRuleClientInfo clientInfo = matchRuleInfo.getClientInfo();
            int canvasId = clientInfo.getCanvasId();
            UgcMapMetaInfo metaInfo = CommonUtil.getLayerDataInfo(ugcBaseInfo.getMdList().getInfoList());
            String version = metaInfo == null ? "" : metaInfo.getVersion();
            boolean isMidJoin = levelBattleResult.isMidJoin();
            int killNum = levelBattleResult.getKillTimes();
            int isGameWatch = levelBattleResult.getUGCIsGameWatch();
            String scenesStr = UgcFuncUtil.genScenesStr(ntfBuilder.getDetailData().getUgcBattleMultiLevelData());
            int lastSceneId = ntfBuilder.getDetailData().getUgcBattleMultiLevelData().getLastSceneId();
            if (memberBaseInfo.getBattleSceneFlow().getLastSceneId() != 0) {
                scenesStr = UgcFuncUtil.genScenesStr(memberBaseInfo.getBattleSceneFlow());
                lastSceneId = (int)memberBaseInfo.getBattleSceneFlow().getLastSceneId();
            }
            int midJoinType = CommonUtil.convertJoinReasonToMidJoinSourceType(memberBaseInfo.getMatchID(), isMidJoin, memberBaseInfo.getJoinReason());
            int enjoyTime = getBattleUgcEnjoyTime(globalBattleResult.getMatchType(), levelBattleResult);
            String friendRelationStr = genFriendRelationStr(player, playerUidSet);
            UgcMapEnjoyResultData.Builder resultDataBuilder = UgcMapEnjoyResultData.newBuilder()
                    .setMapNum(0).setHP(0).setEnjoyTime(enjoyTime).setMapID(String.valueOf(globalBattleResult.getUgcId())).setMapType(ugcBaseInfo.getUgcType())
                    .setMapTagName(ugcBaseInfo.getTags()).setCreatorRoleID("0").setCreatorOpenID("").setBattleID(String.valueOf(globalBattleResult.getBattleId()))
                    .setResult(globalBattleResult.getBattleResult().getNumber())
                    .setFailReason(globalBattleResult.getBattleResult().getNumber() == 1 ? (levelBattleResult.isGiveUp() ? 2 : 1) : 0)
                    .setRoomID(String.valueOf(globalBattleResult.getRoomId())).setRoomNum(0).setRoomAudienceNum(0).setGameSource(gameSource)
                    .setUsersNum(globalBattleResult.getRoomMemberCnt()).setRcmdInfo(recommendMapAlgoInfo.getRecId()).setABTestInfo(recommendMapAlgoInfo.getExpTag())
                    .setCanvasID(canvasId).setTabDesc("").setExposePos(0).setPoints(0).setRecord(levelBattleResult.getCostTime()).setRecordType(1)
                    .setRank(levelBattleResult.getRank()).setDeathNum(levelBattleResult.getDeadTimes()).setDifficulty(0).setModule("").setActivityId("").setThemeName("")
                    .setSearchId("").setMatchId(memberBaseInfo.getMatchID()).setBattleType(ntfBuilder.getModeId())
                    .setCamp(levelBattleResult.getBattleEventValue(BattleEventType.BET_LEVEL_CAMP_TYPE)).setUgcVersion(version).setMapCollectId("").setSubTabName("")
                    .setIsMidJoin(isMidJoin).setIsPseudoGame(settlementExtraInfo.getSingleRoom() ? 1 : 0).setMapPoolId(0).setKillNum(killNum).setIsGameWatch(isGameWatch)
                    .setScenesStr(scenesStr).setScenesId(lastSceneId).setLobbyType(memberBaseInfo.getRuleInfo().getClientInfo().getLobbyType())
                    .setLobbyMapId(memberBaseInfo.getRuleInfo().getClientInfo().getLobbyMapId()).setMidJoinType(midJoinType).setSourceInfo(clientInfo.getSourceInfoMap())
                    .setFriendRelation(friendRelationStr).setIsFastMulti(0).setIsFastMulti(settlementExtraInfo.getQuickStart() ? 1 : 0);
            UgcDataFlowManager.getInstance().sendUGCMapEnjoyResultFlow(player, resultDataBuilder.build());
        });
    }

    private void fillUgcInfo(long ugcId, UgcBattleResult ugcBattleResult) {
        if (ugcId <= 0) {
            LOGGER.error("ugcId error:{}", ugcId);
        }
        UgcService service = UgcService.get();
        if (service == null) {
            LOGGER.error("not found ugc service,please check ugc service");
            return;
        }
        SsUgcsvr.RpcGetUgcPublishMapReq.Builder ssReq = SsUgcsvr.RpcGetUgcPublishMapReq.newBuilder();
        ssReq.setCreatorId(player.getCreatorId());
        ssReq.setNeedUgcKey(true);
        ssReq.addMapIdList(ugcId);
        try {
            RpcResult<Builder> rpcResult = service.rpcGetUgcPublishMap(ssReq);
            if (rpcResult.getData() == null || rpcResult.getData().getResult() != 0 || rpcResult.getData().getMapInfosCount() == 0) {
                LOGGER.error("rpcGetUgcPublishMap ret:{}", rpcResult.getData());
                return;
            }
            ugcBattleResult.setUgcMapInfo(rpcResult.getData().getMapInfos(0));
            ugcBattleResult.setKeyInfo(rpcResult.getData().getKeyInfo());
        } catch (Exception e) {
            LOGGER.error("rpcGetUgcPublishMap err, req:{}, e:{}", ssReq.toString(), e.getMessage());
        }
    }

    private void saveMVPInfo(int gameTypeID, int matchTypeID, long battleEndTime) {
        if (!MiscConf.getInstance().getMiscConf().getChatConf().
                getCommunityChannelMVPGameTypeIDList().contains(gameTypeID)) {
            return;
        }
        BattleSettlementMVPInfo mvpInfo = null;
        switch (gameTypeID) {
            case MTEMGT_WerewolfGame_VALUE: {
                mvpInfo = player.getUserAttr().getPlayerPublicGameData().getCommunityChannelIconInfo().getWolfKillMVPInfo();
            } break;
            case MTEMGT_ArenaGame_VALUE:{
                mvpInfo = player.getUserAttr().getPlayerPublicGameData().getCommunityChannelIconInfo().getArenaMVPInfo();
            } break;
        }
        if (null == mvpInfo) {
            return;
        }
        mvpInfo.setLastMVPTS(battleEndTime);
        mvpInfo.setMatchTypeID(matchTypeID);
        LOGGER.info("saveMVPInfo {} {} {} {}", player.getUid(), gameTypeID, matchTypeID, mvpInfo.getLastMVPTS());
    }

    public UgcBattleResult buildUgcBattleResult(PlayerInteraction.PiiDsSettlementParams dsSettlementParams,
            MemberBaseInfo.Builder battlePlayerMemberInfo) {
        int mapSource = dsSettlementParams.getMapSource();
        mapSource = TlogFlowMgr.getUgcMapBattleGameSource(mapSource, dsSettlementParams.getMatchTypeId(),
                battlePlayerMemberInfo.getRuleInfo());
        int logicMapSource = dsSettlementParams.getLogicMapSource();
        if (logicMapSource == 0 || logicMapSource == 999) {
            logicMapSource = battlePlayerMemberInfo.getRuleInfo().getClientInfo().getLogicMapSource();
        }
        int lobbyType = battlePlayerMemberInfo.getRuleInfo().getClientInfo().getLobbyType();
        long lobbyMapId = battlePlayerMemberInfo.getRuleInfo().getClientInfo().getLobbyMapId();

        LetsGoBattleDetailData detailData = dsSettlementParams.getDetailData();
        List<LetsGoLevelBattleEvent> letsgoEventsList = detailData.getLetsgoEventsList();
        String scenesStr = UgcFuncUtil.genScenesStr(dsSettlementParams.getDetailData().getUgcBattleMultiLevelData());
        int lastSceneId = dsSettlementParams.getDetailData().getUgcBattleMultiLevelData().getLastSceneId();
        if (battlePlayerMemberInfo.getBattleSceneFlow().getLastSceneId() != 0L) {
            scenesStr = UgcFuncUtil.genScenesStr(battlePlayerMemberInfo.getBattleSceneFlow());
            lastSceneId = (int)battlePlayerMemberInfo.getBattleSceneFlow().getLastSceneId();
        }
        boolean singleRoom = dsSettlementParams.getSettlementExtraInfo().getSingleRoom();
        UgcBattleResult ugcBattleResult = new UgcBattleResult(dsSettlementParams.getBattleId(), player.getUid(),
                dsSettlementParams.getMatchTypeId(), dsSettlementParams.getBattleResult().getResult(),
                letsgoEventsList);
        ugcBattleResult.setUgcId(dsSettlementParams.getUgcId());
        ugcBattleResult.setRecommendMapAlgoInfo(dsSettlementParams.getRecommendMapAlgoInfo());
        ugcBattleResult.setLogicGameSource(logicMapSource);
        ugcBattleResult.setMapSource(mapSource);
        ugcBattleResult.setMapPoolId(dsSettlementParams.getMapPoolId());
        ugcBattleResult.setSingleRoom(singleRoom);
        ugcBattleResult.setFromCollectionId(dsSettlementParams.getFromCollectionId());
        ugcBattleResult.setRoomMemberList(dsSettlementParams.getRoomMemberList());
        ugcBattleResult.setRoomId(dsSettlementParams.getRoomId());
        ugcBattleResult.setMatchId(battlePlayerMemberInfo.getMatchID());
        ugcBattleResult.setCanvasId(battlePlayerMemberInfo.getRuleInfo().getClientInfo().getCanvasId());
        ugcBattleResult.setBattleEndTime(dsSettlementParams.getBattleEndTime());
        ugcBattleResult.setLastSceneId(lastSceneId);
        ugcBattleResult.setScenesStr(scenesStr);
        ugcBattleResult.setLobbyType(lobbyType);
        ugcBattleResult.setLobbyMapId(lobbyMapId);
        ugcBattleResult.setTabDesc(battlePlayerMemberInfo.getRuleInfo().getClientInfo().getTabDesc());
        ugcBattleResult.setSearchID(battlePlayerMemberInfo.getRuleInfo().getClientInfo().getSearchID());
        ugcBattleResult.setSubTabName(battlePlayerMemberInfo.getRuleInfo().getClientInfo().getSubTabName());
        ugcBattleResult.setUgcMultiRoundScoreSettlementInfo(
                dsSettlementParams.getUgcMultiRoundScoreSettlementInfo());
        ugcBattleResult.setJoinReason(battlePlayerMemberInfo.getJoinReason());
        ugcBattleResult.setSourceInfo(battlePlayerMemberInfo.getRuleInfo().getClientInfo().getSourceInfoMap());
        ugcBattleResult.setPlayerUidSet(getBattlePlayerUidSet(dsSettlementParams));
        ugcBattleResult.setQuickStart(dsSettlementParams.getSettlementExtraInfo().getQuickStart());
        return ugcBattleResult;
    }

    public void observeSettlement(long battleId, int result, int matchType,
            PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {
        // 导播没有观测对象时，需要单独给他空的ntf，以便他可以退出游戏
        LOGGER.debug("battle end observer uid:{} battleId:{} ", player.getUid(), battleId);
        CsLetsgo.LetsGoBattleSettlementNtf.Builder observeSettlementNtf = CsLetsgo.LetsGoBattleSettlementNtf.newBuilder();
        observeSettlementNtf.setObservedPlayerUid(-1).setModeId(matchType)
                .addAllBattleMember(dsSettlementParams.getBattleMemberList())
                .setDetailData(dsSettlementParams.getDetailData());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_LETSGOBATTLESETTLEMENTNTF, observeSettlementNtf);
    }

    public void processBattleSettlementPre() {
        // 统计对局开启次数+1
        updateGameStartCount();
    }

    public Set<Long> getBattlePlayerUidSet(PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {
        Set<Long> playerUidSet = new HashSet<>();
        for (MemberPlayTime memberPlayTime : dsSettlementParams.getMemberInBattleExtraList().getMemberLeaveListList()) {
            playerUidSet.add(memberPlayTime.getUid());
        }
        for (MemberPlayTime memberPlayTime : dsSettlementParams.getMemberInBattleExtraList().getMemberInBattleListList()) {
            playerUidSet.add(memberPlayTime.getUid());
        }
        for (var member : dsSettlementParams.getBattleMemberList()) {
            playerUidSet.add(member.getUid());
        }
        return playerUidSet;
    }

    @Override
    public void battleSettlement(PlayerInteraction.PlayerInteractionData interactData, boolean fromDb) {
        // 结算预处理逻辑
        processBattleSettlementPre();

        if (!interactData.hasDsSettlementParams()) {
            LOGGER.warn("battleSettlement called but params invalid, uid:{}, data:{} fromDb:{}",
                    player.getUid(), interactData, fromDb);
            return;
        }
        PlayerInteraction.PiiDsSettlementParams dsSettlementParams = interactData.getDsSettlementParams();
        //包体ID玩法统计
        battleAddPakPlayTimes(interactData.getDsSettlementParams());
        //UGC测试房间只推送结算数据给客户端，不改玩家数据
//        // 战斗结算
        final GlobalBattleResult globalBattleResult = buildGlobalBattleResult(dsSettlementParams);
        LOGGER.info("player:{} battleId:{} matchType:{} roomInfoId:{} interactId:{} fromDb:{} ",
                player.getUid(), globalBattleResult.getBattleId(), globalBattleResult.getMatchType(),
                globalBattleResult.getRoomId(), interactData.getId(), fromDb);

        long battleId = dsSettlementParams.getDetailData().getBattleId();
        int result = dsSettlementParams.getBattleResult().getResult();
        if (LOGGER.isDebugEnabled()) {
            int matchType = dsSettlementParams.getDetailData().getMatchType();
            long battleEndTime = dsSettlementParams.getDetailData().getBattleEndTime();
            long roomId = dsSettlementParams.getRoomId();
            LOGGER.debug(
                    "DsSettlement uid:{}, battleId:{} matchType:{} result:{} roomId:{} battleEndTime:{} attrSeason:{} battleMemberSize:{} globalBattleResult:{}",
                    player.getUid(), battleId, matchType, result, roomId, battleEndTime,
                    player.getUserAttr().getPlayerPublicGameData().getQualifyingInfo().getSeason(),
                    dsSettlementParams.getBattleMemberList().size(), globalBattleResult.toString());
        }

        // 导播没有观测对象时，需要单独给他空的ntf，以便他可以退出游戏
        if (battleId == 0 && result == -1) {
            observeSettlement(globalBattleResult.getBattleId(), result,
                    globalBattleResult.getMatchType(), dsSettlementParams);
            return;
        }

        CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder = CsLetsgo.LetsGoBattleSettlementNtf.newBuilder();

        MemberBaseInfo.Builder battlePlayerMemberInfo = globalBattleResult.getBattleMemberBaseInfo(player.getUid())
                .toBuilder();
        // 结算协议数据填充
        fillSettlementNtf(globalBattleResult, ntfBuilder, battlePlayerMemberInfo, dsSettlementParams);

        // ugc数据填充
        int mapSource = dsSettlementParams.getMapSource();
        mapSource = TlogFlowMgr.getUgcMapBattleGameSource(mapSource, globalBattleResult.getMatchType(),
                battlePlayerMemberInfo.getRuleInfo());
        int logicMapSource = dsSettlementParams.getLogicMapSource();
        if (logicMapSource == 0 || logicMapSource == 999) {
            logicMapSource = battlePlayerMemberInfo.getRuleInfo().getClientInfo().getLogicMapSource();
        }
        // 兽人全部结算改为endLevel执行，防止double触发
        boolean omdGame = TycConfs.isOMDGame(globalBattleResult.getMatchType());
        if (globalBattleResult.getUgcId() > 0 && !omdGame) {
            if (battlePlayerMemberInfo.getRuleInfo().getGameModeType()
                    == GameModeType.GMT_UgcTest) { //UGC多人测试，非发布地图，不能有收益，这里只发送tlog
                Set<Long> playerUidSet = getBattlePlayerUidSet(dsSettlementParams);
                sendUgcTestSpecialSettlementTlog(globalBattleResult, playerUidSet,
                        ntfBuilder, battlePlayerMemberInfo,
                        dsSettlementParams.getRecommendMapAlgoInfo(),
                        dsSettlementParams.getSettlementExtraInfo());
            } else {
                UgcBattleResult ugcBattleResult = buildUgcBattleResult(dsSettlementParams,
                        battlePlayerMemberInfo);
                ugcSpecialSettlement(ugcBattleResult);
                // 填充ugc信息
                ntfBuilder.setUgcMapInfo(ugcBattleResult.getUgcMapInfo());
                ntfBuilder.setKeyInfo(ugcBattleResult.getKeyInfo());
                ntfBuilder.setIsBestRecord(ugcBattleResult.isBestRecord());
            }
        }

        // 进行对局计数
        // 中途退出不计数
        // 自定义房间不计数
        if (battlePlayerMemberInfo.getRuleInfo().getRoomType() != RoomType.CustomRoom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest
                && !globalBattleResult.isGiveUp()) {
            addPlayerGameTimes(globalBattleResult);
        }

        //解锁关卡图鉴
        levelIllustratedUnlocked(battlePlayerMemberInfo, globalBattleResult);
        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 刷新排行榜
            processBattleRankRefresh(globalBattleResult);
            // 结算奖励计算发放
            battleDropReward(globalBattleResult.getBattleId(), globalBattleResult, ntfBuilder,
                    ItemChangeReason.ICR_BattleDrop);
            // fps各玩法道具掉落规则
            fpsBattleSettlement(battlePlayerMemberInfo, globalBattleResult, ntfBuilder);
        }

        if (globalBattleResult.isMVP()) {
            saveMVPInfo(globalBattleResult.getMatchTypeConf().getGameTypeId(), globalBattleResult.getMatchType(),
                    globalBattleResult.getBattleEndTime());
        }

        // 其他模块对局结算
        processBattleSettlementModule(globalBattleResult, dsSettlementParams, ntfBuilder, battlePlayerMemberInfo);

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 更新最近的战斗结果数据
            // addBattleResultData会调用updateRoomMemberBaseInfo() 因此涉及到组队信息变更的, 要在此之前
            addBattleResultData(globalBattleResult.getBattleId(), globalBattleResult.getMatchType(), globalBattleResult,
                    globalBattleResult.getBattleEndTime());
            //更新最近的战斗日期数据
            addBattleDateData(globalBattleResult.getMatchType(), globalBattleResult.getBattleEndTime());
        }
        // 对局日志数据上报处理
        processBattleLogAndReport(globalBattleResult, ntfBuilder, dsSettlementParams);

        // battle settlement ntf
        onBattleSettlement(globalBattleResult.getBattleId(), ntfBuilder,
                dsSettlementParams.getObservePlayersList(), fromDb);

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            //event dispatch
            dispatchBattleEvent(globalBattleResult, battlePlayerMemberInfo, dsSettlementParams, ntfBuilder, mapSource,
                    logicMapSource);
        }

        // moba55战斗结束处理
        player.getHokMgr().battleSettlement(globalBattleResult.getMatchType(),  globalBattleResult, interactData, battlePlayerMemberInfo);

        // 一定要在最后
        clearBattleInfo(globalBattleResult.getBattleId(), "battleSettlement");
        if (!needSkipSettlementState(dsSettlementParams.getDetailData().getMatchType())) {
            player.getPlayerStateMgr().enterSettlementScene();
        }
        Monitor.getInstance().avg.total(MonitorId.attr_battle_settlement_cost_time_ms,
                DateUtils.currentTimeMillis() - globalBattleResult.getBattleEndTime());
    }

    public void fillSettlementNtf(GlobalBattleResult globalBattleResult, LetsGoBattleSettlementNtf.Builder ntfBuilder,
            MemberBaseInfo.Builder battlePlayerMemberInfo, PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {
        ntfBuilder.setBattleId(globalBattleResult.getBattleId());
        ntfBuilder.setResult(globalBattleResult.getBattleResult().getNumber());
        ntfBuilder.setGameId(0);
        ntfBuilder.setModeId(globalBattleResult.getMatchType());
        ntfBuilder.setIsGiveUp(globalBattleResult.isGiveUp() ? 1 : 0);
        ntfBuilder.setMatchDynamicConfigData(dsSettlementParams.getMatchDynamicConfigData());
        ntfBuilder.setSelfScore(globalBattleResult.getBattleScore());
        ntfBuilder.setSelfGrade(globalBattleResult.getMatchGrade());
        ntfBuilder.setDetailData(dsSettlementParams.getDetailData());
        ntfBuilder.addAllRoomMemberUids(dsSettlementParams.getRoomMemberList());
        ntfBuilder.addAllRoomMembers(dsSettlementParams.getRoomMemberInfosList());
        ntfBuilder.addAllSideMemberUids(dsSettlementParams.getSideMemberList());
        ntfBuilder.setRuleInfo(battlePlayerMemberInfo.getRuleInfo());
        ntfBuilder.addAllCustomResult(dsSettlementParams.getCustomResultsList());
        for (BattlePlayerCoMatchInfo battlePlayerCoMatchInfo : dsSettlementParams
                .getCoMatchInfoListList()) {
            ntfBuilder.addCoMatchInfoList(battlePlayerCoMatchInfo);
        }
        ntfBuilder.setUgcMultiRoundScoreSettlementInfo(dsSettlementParams
                .getUgcMultiRoundScoreSettlementInfo());

        // 填充玩家信息
        ntfBuilder.addAllBattleMember(dsSettlementParams.getBattleMemberList());
        // 冠军队伍数据填充
        ntfBuilder.setChampionTeamInfo(dsSettlementParams.getChampionTeamInfo());
        for (ChampionScoreGradeInfo championScoreGradeInfo : dsSettlementParams.getChampionTeamInfo()
                .getChampionTeamPlayerList()) {
            ntfBuilder.addChampionMemberUids(championScoreGradeInfo.getUid());
        }
        ntfBuilder.setWarmType(globalBattleResult.isWarmRound() ? 1 : globalBattleResult.isGuideWarmRound() ? 2 : 0);
        ntfBuilder.setIAAConfId(
                player.getIaaManager().checkBattleSettlementIAAShow(IAAType.IAAT_PlayMode_VALUE, globalBattleResult));

        //海选赛没到最终关 不显示赛事结算
        if (dsSettlementParams.getCompetitionSettlement().getCompetitionData().getGameType()
                == CompetitionGameType.COMP_GAMETYPE_PRELIM_VALUE
                && !globalBattleResult.isFinalGame()) {
            // nothing to do
        } else {
            // 赛事结算数据填充
            ntfBuilder.setCompetitionSettlement(dsSettlementParams.getCompetitionSettlement());
        }
    }

    // 其他模块对局结算处理
    public void processBattleSettlementModule(GlobalBattleResult globalBattleResult,
            PlayerInteraction.PiiDsSettlementParams dsSettlementParams,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, MemberBaseInfo.Builder battlePlayerMemberInfo) {
        player.getUserAttr().getPlayerPublicGameData().addSeasonPlayedMatchTypeIds(globalBattleResult.getMatchType());
        player.getFriendManager().addChangeField(PlayerPublicInfoField.SEASON_PLAYED_MATCH_TYPE_IDS);
        player.getFriendRecommendManager()
                .setRecentBattleCache(globalBattleResult.getBattleId(), globalBattleResult.getMatchType(),
                        globalBattleResult.getLevelBattleResults().size(),
                        globalBattleResult.getBattleResult().getNumber(),
                        dsSettlementParams.getBattleMemberList(),
                        globalBattleResult.getRoomMemberList());

        // 对局结算的时候清理掉player身上的对局聊天上报信息
        player.setBattleChatReportInfo(null);
        if (player.getUserAttr().getBattleInfo().getBattleid() == globalBattleResult.getBattleId()) {
            player.getPlayerChatManager()
                    .removeChatGroupKey(player.getUserAttr().getBattleInfo().getGlobalChatGroupKey());
            player.getPlayerChatManager()
                    .removeChatGroupKey(player.getUserAttr().getBattleInfo().getSideChatGroupKey());
        } else {
            LOGGER.warn("battleId not match, current battleId:{}", player.getUserAttr().getBattleInfo().getBattleid());
        }

        // 排位结算事件
        if (globalBattleResult.getQualifyType() != QualifyType.QT_None_VALUE) {
            battleSettlementEvent(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
        }

        // 触发房间的结算逻辑
        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Custom
                || battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Ugc
                || battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_UgcPartyGame) { //UgcTest不需要要
            player.getPlayerRoomMgr().roomSettlement(globalBattleResult.isGiveUp(), dsSettlementParams.getRoomId(),
                    dsSettlementParams.getBattleMemberList());
        }

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 奖杯增加
            try {
                cupSettlement(globalBattleResult, ntfBuilder);
            } catch (Exception e) {
                LOGGER.error("player {} battle {} settlement cups error, e:", player.getUid(),
                        globalBattleResult.getBattleId(), e);
            }
            // 结算MMR等信息
            updatePlayerMatchStatics(globalBattleResult, dsSettlementParams);
            // 基于对局结果进行福袋逻辑处理
            playerBlessBagLogicProcess(globalBattleResult, globalBattleResult.getMatchTypeConf());

            // 主动放弃记录临时历史记录
            if (globalBattleResult.isGiveUp() && globalBattleResult.getMatchTypeConf().getIsShowBattleRecord()) {
                addGiveUpBattleRecords(globalBattleResult);
            }
            // HOK game计算热力值
            try {
                //检查是否为新手局
                if (dsSettlementParams.getRoomInfoID() != 66001) {
                    heatSettlement(globalBattleResult, ntfBuilder);
                } else {
                    LOGGER.debug("interactData.getDsSettlementParams().getRoomInfoID() = {}",
                            dsSettlementParams.getRoomInfoID());
                }
            } catch (Exception e) {
                LOGGER.error("player {} battle {} settlement heat error, e:", player.getUid(),
                        globalBattleResult.getBattleId(), e);
            }
//        player.getClubMgr().handleFinishBattle(matchType);
            player.getRecentActivityMgr()
                    .handleFinishBattle(globalBattleResult.getMatchType(), globalBattleResult.getBattleStartTime());
            // 活动结算
            player.getActivityManager()
                    .battleSettlement(globalBattleResult.getBattleId(), globalBattleResult, ntfBuilder);
            // 峡谷结算
            processArenaSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);

            //设置5V5夸夸参数(高光时刻)
            String arenaHighlightJson = dsSettlementParams.getDetailData().getArenaHighlightJson();
            LOGGER.debug("arenaHighlightJson:{},matchType:{}", arenaHighlightJson, globalBattleResult.getMatchType());
            ntfBuilder.getArenaSettlementInfoBuilder().setArenaHighlightJson(arenaHighlightJson);
            // ugc跑图集星活动(新版闯关挑战)
            player.getUgcMapSimpleActivityMgr().getPlayMapCollectStarActivity()
                    .addStarBattleSettlement(globalBattleResult.getUgcId(), globalBattleResult);
        }

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 过滤掉ugc测试模式后, 基于对局结果进行玩家信誉分处理
            playerReputationScoreProcess(globalBattleResult, globalBattleResult.getMatchTypeConf(),
                    battlePlayerMemberInfo.getRuleInfo().getGameModeType(),
                    globalBattleResult.getRoomMemberList());
        }

        long battleStartTime = globalBattleResult.getBattleStartTime();
        if (CollectionUtil.nonEmpty(globalBattleResult.getRoomMemberList())
                && !isTycBattleMultiPlayer(globalBattleResult.getMatchType())) {
            // 只有开房间模式需要30s条件 (开房间模式: GMT_Custom/GMT_Ugc)
            if (GameModeType.GMT_Custom == battlePlayerMemberInfo.getRuleInfo().getGameModeType()
                    || GameModeType.GMT_Ugc == battlePlayerMemberInfo.getRuleInfo().getGameModeType()
                    || GameModeType.GMT_UgcPartyGame == battlePlayerMemberInfo.getRuleInfo().getGameModeType()) {
                // 战斗持续时长
                long battleCostSecs = TimeUnit.MILLISECONDS.toSeconds(
                        globalBattleResult.getBattleEndTime() - battleStartTime);
                int needTimeSecs = RelationMiscConfData.getInstance().getMiscConf()
                        .getAddIntimacyNeedPlayTogetherTimeSecs();
                // if (CollectionUtil.nonEmpty(globalBattleResult.getRoomMemberList())
                if (battleCostSecs >= needTimeSecs
                        // 自己不是中途加入战斗
                        && globalBattleResult.getLevelBattleResults().stream()
                        .noneMatch(LevelBattleResult::isMidJoin)) {
                    // 不是中途加入战斗的队友们
                    @SuppressWarnings("ConstantConditions")
                    List<Long> nonMidJoinMemberUids = globalBattleResult.getRoomMemberList().stream()
                            .filter(uid -> uid != player.getUid())
                            .map(e -> globalBattleResult.getBattleMemberBaseInfoMap().get(e))
                            .filter(e -> Objects.nonNull(e) && !e.getIsRobot() && !e.getIsMidJoin() && (
                                    0 == e.getBattleEndTime()
                                            || TimeUnit.MILLISECONDS.toSeconds(e.getBattleEndTime() - battleStartTime)
                                            >= needTimeSecs))
                            .map(MemberBaseInfo::getUid)
                            .collect(Collectors.toList());
                    for (MemberPlayTime memberPlayTime : dsSettlementParams.getMemberInBattleExtraList().getMemberLeaveListList()) {
                        if (TimeUnit.MILLISECONDS.toSeconds(memberPlayTime.getLeaveTime() - battleStartTime) >= needTimeSecs) {
                            nonMidJoinMemberUids.add(memberPlayTime.getUid());
                        }
                    }
                    for (MemberPlayTime memberPlayTime : dsSettlementParams.getMemberInBattleExtraList().getMemberInBattleListList()) {
                        nonMidJoinMemberUids.add(memberPlayTime.getUid());
                    }
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("doHandleBattleIntimacy. PlayerInteractionData:{} globalBattleResult:{}",
                                dsSettlementParams, globalBattleResult);
                    }
                    // 更新亲密度
                    player.getFriendManager().handleBattleIntimacy(nonMidJoinMemberUids, battleStartTime,
                            globalBattleResult.getMatchType(), ntfBuilder, true);
                }
            } else {
                // 更新亲密度
                player.getFriendManager().handleBattleIntimacy(globalBattleResult.getRoomMemberList(), battleStartTime,
                        globalBattleResult.getMatchType(), ntfBuilder);
            }
        }

        // 狼人结算（包含匹配+自定义房间）
        player.getWolfKillMgr().battleSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
        // 大王结算
        processChaseSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
        // Chest结算
        processChestSettlement(dsSettlementParams, globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
    }

    public void processChestSettlement(PlayerInteraction.PiiDsSettlementParams dsSettlementParams,
            GlobalBattleResult globalBattleResult, CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder,
            MemberBaseInfo.Builder battlePlayerMemberInfo) {
        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            try {
                player.getChestBattleMgr()
                        .chestBattleSettlement(dsSettlementParams.getDetailData(), globalBattleResult, ntfBuilder);
            } catch (Exception e) {
                LOGGER.error("chestBattleSettlement error, uid:{} battleId:{}",
                        player.getUid(), globalBattleResult.getBattleId(), e);
            }
            // Chest专精
            player.getChestMgr().chestBattleSettlement(false, globalBattleResult);
        }
    }

    public void processChaseSettlement(GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, MemberBaseInfo.Builder battlePlayerMemberInfo) {
        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom
                && battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 大王专精
            player.getChaseMgr().battleSettlement(false, globalBattleResult, ntfBuilder);
        }

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() != GameModeType.GMT_UgcTest) {
            // 更新游玩次数
            player.getChaseMgr().updateChaseMemberBaseInfoDataOnBattleEnd(globalBattleResult,
                    ntfBuilder.getRuleInfo().getGameModeType());
        }
        // 更新综合积分记录
        player.getChaseMgr().updateComprehensiveScoreOnBattleSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo.getRuleInfo().getGameModeType());

    }

    public void processArenaSettlement(GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, MemberBaseInfo.Builder battlePlayerMemberInfo) {
        // 峡谷战力值结算
        player.getArenaMgr().battleSettlement(globalBattleResult, ntfBuilder, battlePlayerMemberInfo);
        // 峡谷战绩统计
        player.getArenaMgr().statsProc(globalBattleResult.getMatchType(), globalBattleResult, battlePlayerMemberInfo);
        // 更新arena最新对局信息
        player.getArenaMgr().updateArenaLastBattleData(globalBattleResult.getBattleStartTime(), globalBattleResult);
        // 智能npc结算推送
        player.getArenaMgr().aiNpcChatPushSettlement(globalBattleResult.getMatchType(), globalBattleResult, ntfBuilder);
        //3v3新手局需要判断是否为高手来处理温暖局
        if(ArenaCommonDefine.getInstance().is3V3(globalBattleResult.getMatchType())){
            player.getArenaMgr().update3V3GuideWarmRound(globalBattleResult.isGuideWarmRound(), globalBattleResult.getPlayScore());
        }
    }

    private void processBattleLogAndReport(GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder,
            PlayerInteraction.PiiDsSettlementParams dsSettlementParams) {

        // 更新TlogSecRoundEnd需要的数据
        player.handleTlogBattleSettlement(globalBattleResult.getBattleId(), ntfBuilder);

        if (StarPConfs.isStarPGame(globalBattleResult.getMatchType())) {
            // starp tlog
            StarPTlogFlowMgr.sendStarPBattleSettlementFlow(2, 1, dsSettlementParams);
        }
    }

    public void processBattleRankRefresh(GlobalBattleResult globalBattleResult) {
        boolean rankNeedFresh =
                globalBattleResult.getQualifyType() != QualifyType.QT_Lightning_VALUE || MiscConf.getInstance()
                        .getMiscConf().getRankConf()
                        .getLevelLightningScore();
        if (rankNeedFresh) {
            //判断随机事件实验是否跳过关卡图鉴、排行榜(先判断玩法模式对结算的影响 在判断随机事件对结算的影响)
            boolean skipRank = false;
            MatchLevelRandEventRecordRule matchLevelRandEventRecordRule = MatchLevelRandEventRecordRuleData.getInstance()
                    .get(globalBattleResult.getMatchType());
            if (matchLevelRandEventRecordRule != null) {
                skipRank = matchLevelRandEventRecordRule.getSkipRank();
            }

            for (int round = 1; round <= globalBattleResult.getLevelBattleResults().size(); round++) {
                LevelBattleResult levelEvent = globalBattleResult.getLevelBattleResults().get(round - 1);
                if (skipRank) {
                    //触发随机事件跳过排行榜
                    if (levelEvent.isRandEvent()) {
                        LOGGER.debug("battleSettlement randEvent skip rank, uid:{} level:{} match:{} round:{}",
                                player.getUid(), levelEvent.getLevel(),
                                globalBattleResult.getMatchType(), levelEvent.getLevelIndex());
                    } else {
                        refreshRank(globalBattleResult.getBattleId(), globalBattleResult.getMatchType(), levelEvent);
                    }
                } else {
                    // 更新玩家属性系统中的关卡图鉴数据，忽略闪电赛成绩（除非显式开启）
                    refreshRank(globalBattleResult.getBattleId(), globalBattleResult.getMatchType(), levelEvent);
                }
            }
        }

        // 兽人玩法刷新排行榜
        refreshBattlePlayerRank(globalBattleResult.getBattleId(), globalBattleResult);
    }

    public void addPlayerGameTimes(GlobalBattleResult globalBattleResult) {
        player.addPlayerGameTime(PlayerGameTimeType.PGTT_GameTimes, 1);
        player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_GameTimes,
                PlayerGameDataStatDuration.PGDSD_Forever, 1);
        player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_GameTimesV2,
                PlayerGameDataStatDuration.PGDSD_Forever, 1);
        player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_GameTimesDaily,
                PlayerGameDataStatDuration.PGDSD_Daily, 1);
        player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_GameTimesWeekly,
                PlayerGameDataStatDuration.PGDSD_Weekly, 1);
        if (globalBattleResult.getMatchTypeConf().getModeID() == 1 && globalBattleResult.isFinalGame()) { // 经典玩法终局
            player.addPlayerGameTime(PlayerGameTimeType.PGTT_FinalTimes, 1);
        }
        if (globalBattleResult.getUgcId() == 0
                && BattleResultCode.BATTLE_RESULT_CODE_WIN.equals(globalBattleResult.getBattleResult())) {
            player.addPlayerGameTime(PlayerGameTimeType.PGTT_ChampionTimes, 1);
            // 每日胜局数统计
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_WinTimes,
                    PlayerGameDataStatDuration.PGDSD_Daily, 1);
        }

        if (globalBattleResult.getUgcId() == 0
                && (globalBattleResult.moBa.getArena().isArenaTeamRankTop2() || globalBattleResult.moBa.getHok().isHokWin())) { // Hok没有排名，只有输赢
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(),
                    PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2,
                    PlayerGameDataStatDuration.PGDSD_Forever, 1);
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(),
                    PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2Daily,
                    PlayerGameDataStatDuration.PGDSD_Daily, 1);
            if (1 == player.getPlayerGameTimesStat(globalBattleResult.getMatchType(),
                    PlayerGameDataStatType.PGDST_GameTimesWithTeamRankTop2Daily, true)) { // 每日首次前两名
                player.addPlayerGameTimesStat(globalBattleResult.getMatchType(),
                        PlayerGameDataStatType.PGDST_GameDaysWithTeamRankTop2,
                        PlayerGameDataStatDuration.PGDSD_Forever, 1);
            }
        }
        if (globalBattleResult.moBa.getCommon().isWin()) {
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_WinTimes,
                    PlayerGameDataStatDuration.PGDSD_Forever, 1);
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(), PlayerGameDataStatType.PGDST_WinTimesDaily,
                    PlayerGameDataStatDuration.PGDSD_Daily, 1);
            player.addPlayerGameTimesStat(globalBattleResult.getMatchType(),
                    PlayerGameDataStatType.PGDST_WinTimesWeekly,
                    PlayerGameDataStatDuration.PGDSD_Weekly, 1);
        }
    }

    /**
     * 核算热力值
     */
    private void heatSettlement(GlobalBattleResult globalBattleResult, CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder){
        boolean isHokGame = HokConfs.isInHokType(globalBattleResult.getMatchType());
        if(!isHokGame){
            return;
        }

        if (MiscConfArena.getInstance() == null) {
            LOGGER.debug("MiscConfArena.getInstance() is null");
            return;
        }
        ResMisc.MiscConfArena miscConfArena = MiscConfArena.getInstance().getMiscConfArena();
        if(miscConfArena == null){
            LOGGER.debug("miscConfArena is null");
            return;
        }
        //判断活动是否开启
        BaseActivity runningActivity = player.getActivityManager().getRunningActivity(miscConfArena.getCanyonCombat());
        if(runningActivity == null){
            LOGGER.debug("RunningActivity is null , activityId = {},",miscConfArena.getCanyonCombat());
            return;
        }
        //行为ID != 0  违规
        LOGGER.debug("CheckBehaviorIdsList = {}", () -> miscConfArena.getCheckBehaviorIdsList());
        int behaviorId = globalBattleResult.getBehaviorId(miscConfArena.getCheckBehaviorIdsList());

        HeatSettlement.Builder heatSettlement = HeatSettlement.newBuilder();
        heatSettlement.setBehaviorId(behaviorId);

        //当前的热力值
//        long coinNum = player.getBagManager().getMoneyNum(CoinType.CT_Heat.getNumber());
        long coinNum = player.getActivityManager().getActivityScore(miscConfArena.getCanyonCombat());
        heatSettlement.setHeatValue(coinNum);

        ResHOKRiftPower.HOKRiftPowerConf data = HOKRiftPowerConf.getInstance().getData(coinNum);
        if(data == null){
            LOGGER.debug("HOKRiftPowerConf is null , coinNum= {}" , coinNum);
            return;
        }

        boolean isWin = globalBattleResult.moBa.getCommon().isWin();
        boolean isMvp = globalBattleResult.moBa.getHok().isHokMvp();//是否为MVP
        int playScore = globalBattleResult.getPlayScore();//个人表现分
        LOGGER.debug("isWin = {} , isMvp = {} , playScore = {} , behaviorId = {}", isWin, isMvp, playScore, behaviorId);
        //计算得分
        if(behaviorId == 0){
            int basePoint = data.getBasePoint();
            if(isWin){
                heatSettlement.setWinScore(data.getResultPointWin());
                basePoint += data.getResultPointWin();
                if(isMvp){
                    heatSettlement.setMvpScore(data.getMvpPointWin());
                    basePoint += data.getMvpPointWin();
                }else{
                    heatSettlement.setMvpScore(0);
                }
            }else{
                heatSettlement.setWinScore(data.getResultPointLose());
                basePoint += data.getResultPointLose();
                if (isMvp) {
                    heatSettlement.setMvpScore(data.getMvpPointLose());
                    basePoint += data.getMvpPointLose();
                } else {
                    heatSettlement.setMvpScore(0);
                }
            }
            //评分补偿阈值
            int personScore = (int) Math.max(0,
                    ((float) playScore / 100 - data.getMvpThreshold()) * data.getMvpRatio());
            heatSettlement.setPersonScore(data.getBasePoint() + personScore);
            basePoint += personScore;
//            player.getBagManager().setMoneyNum(CoinType.CT_Heat.getNumber(),coinNum + basePoint , ItemChangeReason.ICR_ArenaDefault.getNumber(),"");
            //更新排行榜
            player.getActivityManager().reportActivityScore(miscConfArena.getCanyonCombat(), basePoint);
        }else{
            heatSettlement.setWinScore(0);
            heatSettlement.setPersonScore(0);
            heatSettlement.setMvpScore(0);
        }
        LOGGER.debug("heatSettlement = {}", () -> heatSettlement.toString());
        ntfBuilder.setHeatSettlement(heatSettlement);
    }

    public void cupSettlement(GlobalBattleResult battleResult, CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        // 结算奖杯
        CupsBattle cupsBattle = new CupsBattle(player, battleResult);
        if (cupsBattle.getModeCupsStageConfig() == null) {
            LOGGER.debug("player {} battle {} modeId {} getModeCupsStageConfig not exist",
                    player.getUid(), cupsBattle.getBattleId(), cupsBattle.getModeId());
            return;
        }
        AddCupsDetailInfo addCupsDetailInfo = player.getCupsManager().addCups(cupsBattle);
        if (addCupsDetailInfo != null) {
            player.getCupsManager().addModeCups(cupsBattle);
            ntfBuilder.setAddCupsDetailInfo(addCupsDetailInfo);
        }
        List<Integer> ruleIds = Optional.ofNullable(cupsBattle.getCupsAddInfo())
                .map(e -> e.getAdditionInfoList().stream()
                        .filter(s -> AdditionType.AT_PlayMode == s.getAdditionType())
                        .map(CupsAdditionInfo::getRuleId)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        // 保存数据库
        BattleSettlementData data = BattleSettlementData.newBuilder()
                .setBattleId(battleResult.getBattleId())
                .setCupsNum(cupsBattle.getTotalAddNumDb() / 100)
                .addAllRuleId(ruleIds)
                .build();
        MatchType matchType = MatchTypeData.getInstance().get(battleResult.getMatchType());
        if (matchType != null) {
            BattleHistoryUtil.addBattleSettlementData(matchType.getRecordType(), player.getUid(), data);
        }
        BattleHistoryUtil.addBattleSettlementData(0, player.getUid(), data);
    }

    @Override
    public void levelFinish(PlayerInteraction.PlayerInteractionData interactData, boolean fromDb) {
        if (!interactData.hasDsLevelFinishParams()) {
            LOGGER.warn("FallGuys levelFinish called but params invalid, uid:{}, data:{} fromDb:{}",
                    player.getUid(), interactData, fromDb);
            return;
        }

        long battleId = interactData.getDsLevelFinishParams().getBattleId();
        int levelId = interactData.getDsLevelFinishParams().getLevelId();
        int matchType = interactData.getDsLevelFinishParams().getMatchType();
        int levelIndex = interactData.getDsLevelFinishParams().getLevelIndex();
        long ugcId = interactData.getDsLevelFinishParams().getUgcId();
        int competitionSeason = interactData.getDsLevelFinishParams().getCompetitionSeason();
        int competitionGameType = interactData.getDsLevelFinishParams().getCompetitionGameType();
        var battlePlayers = interactData.getDsLevelFinishParams().getBattlePlayersList();
        var competitionBattleData = interactData.getDsLevelFinishParams().getCompetitionBattleData();
        int warmType = interactData.getDsLevelFinishParams().getWarmRoundType();
        int gameModeType = interactData.getDsLevelFinishParams().getGameModeType();
        List<Long> roomMemberList = interactData.getDsLevelFinishParams().getRoomMemberList();
        var levelRoundABTestTag = interactData.getDsLevelFinishParams().getLevelRoundABTestTag();
        int aiDifficultyTestType = interactData.getDsLevelFinishParams().getAiDifficultyTestType();
        boolean randEvent = interactData.getDsLevelFinishParams().getRandEvent();

        long omdUgcId = 0;
        if (interactData.getDsLevelFinishParams().hasUgcSettlementInfo()) {
            UgcLevelSettlementInfo ugcSettlementInfo = interactData.getDsLevelFinishParams().getUgcSettlementInfo();
            if (ugcSettlementInfo.getUgcId() > 0) {
                omdUgcId = ugcSettlementInfo.getUgcId();
            }
        }

        LOGGER.info(
                "receive level finish, player:{} battleId:{} levelIndex:{} levelId:{} match:{} playerCount:{} ugcId={} omdUgcId={}",
                player.getUid(), battleId, levelIndex, levelId, matchType, battlePlayers, ugcId, omdUgcId);

        LevelBattleResult levelBattleResult = new LevelBattleResult(player.getUid(), levelIndex,
                LetsGoLevelBattleEvent.newBuilder().setLevel(levelId).setGameMode(matchType).setRandEvent(randEvent)
                        .addAllLevelEvents(interactData.getDsLevelFinishParams().getContextList()).build());

        // 处理ugc相关逻辑
        if (interactData.getDsLevelFinishParams().hasUgcSettlementInfo()) {
            boolean omdGame = TycConfs.isOMDGame(matchType);
            if (omdUgcId > 0 && omdGame) {
                Monitor.getInstance().add.succ(MonitorId.attr_ugc_omd_result_statistics, 1);
                UgcBattleResult ugcBattleResult = getUgcBattleResult(interactData, omdUgcId, levelBattleResult);
                this.ugcSpecialSettlement(ugcBattleResult);
            } else {
                // tlog
                boolean isMulTestGame = TycConfs.isOMDGameMulTest(matchType);
                if (omdUgcId > 0 && isMulTestGame) {
                    UgcBattleResult ugcBattleResult = getUgcBattleResult(interactData, omdUgcId, levelBattleResult);
                    this.sendOMDMultiTestLog(ugcBattleResult);
                }
            }
        }

        boolean isSpecialLevel=false;
        T_LevelInfoData levelInfoData = LevelInfoData.getInstance().get(levelId);

        // 无尽模式特殊处理
        if (levelInfoData!=null&&levelInfoData.getLevelType() == LevelType.LT_MAYDAY_INF){
            int IsUpdate = levelBattleResult.getBattleEventValue(BattleEventType.BET_MAYDAY_INF_ROOM_INFO_UPDATE);
            LOGGER.debug("levelFinish  levelType Inf uid: {} battleid: {} IsUpdate: {}", player.getUid(), battleId, IsUpdate);
            if(IsUpdate > 0)
            {
                MayDayUtil.NotifyInfRoomInfoChange(player);
            }
        }

        // 自定义房间特殊关卡判定，特殊关卡会上报数据。目前是竞速类型的62（飞车模式）需要上报
        if (levelInfoData!=null&&levelInfoData.getLevelType()== LevelType.LT_JS&&levelInfoData.getLevelModuleId()==62){
            isSpecialLevel=true;
        }

        // 自定义房间特殊关卡判定，MAYDAY
        if (levelInfoData!=null&&(levelInfoData.getLevelType() == LevelType.LT_MAYDAY ||
            levelInfoData.getLevelType() == LevelType.LT_MAYDAY_SPY ||
            levelInfoData.getLevelType() == LevelType.LT_MAYDAY_INF)){
            isSpecialLevel=true;
        }
        if (HokConfs.isInHokType(matchType)){
            player.getHokMgr().updateUserType(levelBattleResult.moBa.getHok().getHOKFirstRoundUserType(), levelBattleResult.getPlayScore());
        }

        // 不是特殊关卡，并且是自定义房间的对局，跳过关卡数据上报的环节
        if (!isSpecialLevel
                && ((gameModeType == GameModeType.GMT_Custom_VALUE && !TycConfs.isOMDGame(matchType))
                || gameModeType == GameModeType.GMT_UgcTest_VALUE)) {
            LOGGER.debug("skip level end report for custom game, uid:{} battleId:{} gameModeType:{}", player.getUid(), battleId,
                    gameModeType);
        } else {
            MapData.UgcEventData eventData = new MapData.UgcEventData();
            eventData.mapSource = interactData.getDsSettlementParams().getMapSource();
            eventData.ugcId = omdUgcId;
            eventData.matchType = matchType;
            eventData.omdGame = TycConfs.isOMDGame(matchType);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("levelFinish  uid: {} battleid: {} matchType: {}", player.getUid(), battleId, eventData.matchType);
            }
            if (eventData.omdGame && eventData.ugcId > 0) {
                PlayerMapByRoomEvent roomEvent = new PlayerMapByRoomEvent(player);
                roomEvent.setParam(eventData);
                roomEvent.dispatch();
            }

            dispatchLevelEvent(battleId, levelBattleResult, battlePlayers, competitionSeason, competitionGameType,
                    roomMemberList);
            player.getRankManager().freshPlayerRankInfo(interactData.getDsLevelFinishParams().getRanksInfo());
            processTyCRelationIntimacy(interactData);

            player.getHokMgr().updateUserScore(levelBattleResult.getPlayScore(), matchType);
        }

        StringBuilder roomMemberRelationStrBuilder = new StringBuilder();
        MemberBaseInfo battlePlayerMemberInfo = interactData.getDsLevelFinishParams().getBattlePlayer();
        roomMemberList.forEach(uid -> {
            int relationType = player.getFriendManager().getRelationTypeVal(uid);
            if (relationType > 0) {
                roomMemberRelationStrBuilder.append(uid).append(",").append(relationType).append(";");
            }
        });

        int techniqueNum = 0;
        int techniqueScore = interactData.getDsLevelFinishParams().getSkillGrade();
        List<G6Common.BattleSkillContext> eventsList = interactData.getDsLevelFinishParams().getSkillDataList();
        for (G6Common.BattleSkillContext event : eventsList) {
                techniqueNum += event.getSkillCount();
        }
        if(LOGGER.isDebugEnabled()){
            LOGGER.debug("getDsSettlementParams sendPlayerMapResultFlow techniqueNum:{},techniqueScore:{}",techniqueNum,techniqueScore);
        }
        TlogFlowMgr.sendPlayerMapResultFlow(player, battleId, matchType, ugcId, warmType, levelBattleResult,
                battlePlayerMemberInfo, levelIndex + 1,
                roomMemberList.size(), roomMemberRelationStrBuilder.toString(), competitionBattleData,
                levelRoundABTestTag, aiDifficultyTestType,techniqueNum,techniqueScore);
    }

    private UgcBattleResult getUgcBattleResult(PlayerInteractionData interactData, long omdUgcId,
            LevelBattleResult levelBattleResult) {

        long battleId = interactData.getDsLevelFinishParams().getBattleId();
        int levelId = interactData.getDsLevelFinishParams().getLevelId();
        int matchType = interactData.getDsLevelFinishParams().getMatchType();
        int levelIndex = interactData.getDsLevelFinishParams().getLevelIndex();
        UgcLevelSettlementInfo ugcSettlementInfo = interactData.getDsLevelFinishParams().getUgcSettlementInfo();

        List<BattleEventContext> contextList = interactData.getDsLevelFinishParams().getContextList();
        int battleResult = levelBattleResult.getBattleEventValue(BattleEventType.BET_LEVEL_END_REASON);
        UgcBattleResult ugcBattleResult = new UgcBattleResult(battleId, player.getUid(), levelId, levelIndex,
                matchType,
                battleResult,
                contextList);

        //ugcSettlementInfo 为啥不直接复用这个结构是因为有可能有自定义结构，那么这个就会不断的耦合这个数据结构
        ugcBattleResult.setUgcId(omdUgcId);
        ugcBattleResult.setRecommendMapAlgoInfo(ugcSettlementInfo.getRecommendMapAlgoInfo());
        ugcBattleResult.setLogicGameSource(ugcSettlementInfo.getLogicMapSource());
        ugcBattleResult.setMapSource(ugcSettlementInfo.getMapSource());
        ugcBattleResult.setMapPoolId(ugcSettlementInfo.getMapPoolId());
        ugcBattleResult.setSingleRoom(ugcSettlementInfo.getSettlementExtraInfo().getSingleRoom());
        ugcBattleResult.setQuickStart(ugcSettlementInfo.getSettlementExtraInfo().getQuickStart());
        ugcBattleResult.setFromCollectionId(ugcSettlementInfo.getFromCollectionId());
        return ugcBattleResult;
    }

    public boolean isTycBattleMultiPlayer(int marchType) {
        return marchType == 809 || marchType == 810;
    }

    private boolean isTycEndlessMode(PlayerInteraction.PlayerInteractionData interactData) {
        return interactData.getDsLevelFinishParams().getContextList().stream()
                .anyMatch(context -> context.getSubEventType() == BET_LEVEL_DIFFICULTY_INDEX
                        && context.getEventValue() == 3);
    }

    private boolean isTycEndlessLevelValid(PlayerInteraction.PlayerInteractionData interactData) {
        return interactData.getDsLevelFinishParams().getContextList().stream()
                .anyMatch(context -> context.getSubEventType() == BET_PASS_LEVEL_COUNT && context.getEventValue() >= 3);
    }

    private boolean isIntimacyBattleValid(PlayerInteraction.PlayerInteractionData interactData){
        return interactData.getDsLevelFinishParams().getContextList().stream()
                .anyMatch(context -> context.getSubEventType()==BET_LEVEL_END_REASON && context.getEventValue()!=2);
    }

    private boolean isIntimacyBattleTypeValid(PlayerInteraction.PlayerInteractionData interactData){
        return interactData.getDsLevelFinishParams().getContextList().stream()
                .anyMatch(context -> context.getSubEventType()==BET_AT_LEVEL && context.getEventValue()!=0);
    }

    private boolean needAddTycIntimacy(PlayerInteraction.PlayerInteractionData interactData){
        if (getLevelStartTime(interactData)==0){
            LOGGER.error("get level start time is 0 {}", interactData.getDsLevelFinishParams().getBattleId());
            return false;
        }
        if (!isIntimacyBattleValid(interactData)){
            LOGGER.error("user quit battle {}", interactData.getDsLevelFinishParams().getBattleId());
            return false;
        }
        if (!isIntimacyBattleTypeValid(interactData)){
            LOGGER.error("user is in lobby {}", interactData.getDsLevelFinishParams().getBattleId());
            return false;
        }
        if(!isTycBattleMultiPlayer(interactData.getDsLevelFinishParams().getMatchType())){
            return false;
        }
        if (isTycEndlessMode(interactData)){
            if(isTycEndlessLevelValid(interactData)){
                LOGGER.error("isTycEndlessLevelValid is not up to 3 {}",
                        interactData.getDsLevelFinishParams().getBattleId());
                return true;
            }
            return false;
        }
        return true;
    }

    /**
     * 返回战斗开始时间 单位秒
     * @param interactData
     * @return
     */
    private long getLevelStartTime(PlayerInteraction.PlayerInteractionData interactData){
        return interactData.getDsLevelFinishParams().getContextList().stream()
                .filter(context->context.getSubEventType()==BET_LEVEL_START_TIMR)
                .findAny()
                .map(BattleEventContext::getEventValue)
                .orElse(0L);
    }

    private void processTyCRelationIntimacy(PlayerInteraction.PlayerInteractionData interactData){
        if(!needAddTycIntimacy(interactData)){
            return;
        }
        
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("begin handle battleIntimacy {}, friendList {}", player.getUid(),
                    interactData.getDsLevelFinishParams().getTeamPlayersList());
        }

        player.getFriendManager().handleBattleIntimacy(interactData.getDsLevelFinishParams().getTeamPlayersList(),
                TimeUnit.SECONDS.toMillis(getLevelStartTime(interactData)), interactData.getDsLevelFinishParams().getMatchType(), null);
    }

    /*根据规则限制 图鉴解锁范围
    * 1247
     */
    public void levelIllustratedUnlocked(MemberBaseInfo.Builder battlePlayerMemberInfo,
            GlobalBattleResult globalBattleResult) {
        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Normal
                || battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Rank
                || battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Custom
                || battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Special){
            // 判断是否需要刷新关卡图鉴
            MatchLevelRecordRuleData recordRuleData = MatchLevelRecordRuleData.getInstance();
            boolean illustrationNeedRefresh =
                    recordRuleData == null || !recordRuleData.isPlayLevelRecordBlocked(
                            globalBattleResult.getMatchType());
            //判断随机事件是否跳过关卡图鉴(先判断玩法模式对结算的影响 在判断随机事件对结算的影响)
            boolean skipIllustration = false;
            MatchLevelRandEventRecordRule matchLevelRandEventRecordRule = MatchLevelRandEventRecordRuleData.getInstance()
                    .get(globalBattleResult.getMatchType());
            if (matchLevelRandEventRecordRule != null) {
                skipIllustration = matchLevelRandEventRecordRule.getSkipIllustration();
            }

            for (int round = 1; round <= globalBattleResult.getLevelBattleResults().size(); round++) {
                LevelBattleResult levelEvent = globalBattleResult.getLevelBattleResults().get(round - 1);

                if (illustrationNeedRefresh && hasIllustrationConfig(levelEvent.getLevel())) {
                    // 更新玩家的关卡图鉴       触发随机事件跳过关卡图鉴
                    if (skipIllustration && levelEvent.isRandEvent()) {
                        LOGGER.debug("battleSettlement randEvent skip illustration, uid:{} level:{} match:{} round:{}", player.getUid(), levelEvent.getLevel(),
                                globalBattleResult.getMatchType(), levelEvent.getLevelIndex());
                    } else {
                        refreshIllustration(levelEvent, globalBattleResult.getMatchType());
                    }
                }
            }
        } else {
            LOGGER.info("battleSettlement skip debug, uid:{} battleId:{} gameModeType:{}",
                    player.getUid(), globalBattleResult.getBattleId(),
                    battlePlayerMemberInfo.getRuleInfo().getGameModeType());
        }
    }

    /*
    * 是否有(是否进关卡图鉴) 配置
     */
    private boolean hasIllustrationConfig(int level){
        T_LevelInfoData cfg = LevelInfoData.getInstance().get(level);
        if (cfg == null) {
            return false;
        }
        return cfg.getIllustrationUnlock() == 1;
    }


    /**
     * 根据关卡上报数据更新关卡图鉴
     *
     * @param event 事件数据
     * @param matchType 玩法id
     */
    private void refreshIllustration(LevelBattleResult event, int matchType) {
        T_LevelInfoData cfg = LevelInfoData.getInstance().get(event.getLevel());
        if (cfg == null) {
            LOGGER.error("cannot find config for level : {}", event.getLevel());
            return;
        }

        if (isLevelTypeRacing(cfg.getLevelType())) {
            LevelAchievementInfo info = getLevelAchievementInfo(cfg);
            getElapsedTime(event).ifPresent(elapsed -> {
                if (elapsed == 0) {
                    LOGGER.warn("elapsed time is 0 so skip refresh, player:{} level:{}", player.getUid(),
                            event.getLevel());
                    return;
                }
                // 全局成绩更新
                int opType = 1;
                if (info.getBestScore() == -1 || Math.toIntExact(elapsed) < info.getBestScore()) {
                    if (info.getBestScore() != -1) {
                        opType = 2;
                    }
                    info.setBestScore(Math.toIntExact(elapsed));
                    TlogFlowMgr.sendMapCollectionFlow(player, opType, event.getLevel(), cfg.getLevelType().getNumber(),
                            player.getUserAttr().getLevelIllustration().getLevelAchievementInfoSize(), 0);
                    LOGGER.debug("player {} update its score for {}", player.getUid(), info.getId());
                }
                LevelRecordByPlay dataByPlay = info.getDataByPlay(matchType);
                if (dataByPlay != null && dataByPlay.getData().getTimeCost() <= Math.toIntExact(elapsed)) {
                    // 有记录且未破记录了
                    return;
                }
                opType = 2;
                if (dataByPlay == null) {
                    opType = 1;
                    dataByPlay = new LevelRecordByPlay();
                    dataByPlay.setPlayId(matchType);
                    info.putDataByPlay(matchType, dataByPlay);
                }
                dataByPlay.getData().setTimeCost(Math.toIntExact(elapsed));
                TlogFlowMgr.sendMapCollectionFlow(player, opType, event.getLevel(), cfg.getLevelType().getNumber(),
                        player.getUserAttr().getLevelIllustration().getLevelAchievementInfoSize(), matchType);
                LOGGER.debug("player {} update its score for {} at match type {}", player.getUid(), info.getId(),
                        matchType);
            });
        } else {
            LevelAchievementInfo info = getLevelAchievementInfo(cfg);
            info.setBestScore(0); // 置为过关状态，具体数据由后续逻辑更新
            // 非竞速关卡 仅开图鉴
            TlogFlowMgr.sendMapCollectionFlow(player, 1, event.getLevel(), cfg.getLevelType().getNumber(),
                    player.getUserAttr().getLevelIllustration().getLevelAchievementInfoSize(), 0);
        }
    }

    /**
     * 关卡类型为竞速
     *
     * @param type 关卡类型
     * @return
     */
    private boolean isLevelTypeRacing(LevelType type) {
        return type == LevelType.LT_Racing || type == LevelType.LT_TeamChampionRacing
                || type == LevelType.LT_SurvivalRacing || type == LevelType.LT_SinglePoint /*加上个人积分赛*/;
    }

    private void dispatchLevelEvent(long battleId, LevelBattleResult result, List<Long> battlePlayers,
            int competitionSeason,
            int competitionGameType, List<Long> roomMemberList) {
        BattleLevelEventData.Builder data = BattleLevelEventData.newBuilder()
                .setPass(result.getLevelResult() == BattleResultCode.BATTLE_RESULT_CODE_WIN)
                .setAtRound(result.getLevelIndex() + 1).setLevelType(result.getLevelType().getNumber())
                .setLevelId(result.getLevel()).setMatchType(result.getGameModel())
                .setIsSinglePlay(battlePlayers.size() <= 1).setCompetitionSeason(competitionSeason)
                .setCompetitionGameType(competitionGameType)
                .setRandEvent(result.isRandEvent())
                .setIsGiveUp(result.getBattleEventValue(BattleEventType.BET_IS_GIVEUP) == 1)
                .setJsSingleCircleTime(result.getBattleEventValue(BattleEventType.BET_JS_CAR_SINGLE_CIRCLE_USE_TIME))
                .addAllRoomMember(roomMemberList);

        BattleLevelEventDataForArena.Builder arenaDataBuilder = BattleLevelEventDataForArena.newBuilder();
        {
            arenaDataBuilder.setCardQualityChroma(result.moBa.getArena().getArenaCardQualityChroma())
                    .setTotalDamage(result.moBa.getArena().getArenaTotalDamage())
                    .setTotalAssist(result.moBa.getArena().getArenaTotalAssist())
                    .setTotalKill(result.moBa.getArena().getArenaTotalKill())
                    .setUseItemTimes(result.moBa.getArena().getArenaUseItemTimes())
                    .setDestroyObjectTimes(result.moBa.getArena().getArenaDestroyObjectTimes())
                    .setPentaKillTimes(result.moBa.getArena().getArenaPentaKillTimes())
                    .setDoubleKillInOneRoundTimes(result.moBa.getArena().getArenaDoubleKillInOneRoundTimes())
                    .setSunceCarCarryOtherPlayersTimes(result.moBa.getArena().getArenaSunceCarCarryOtherPlayersTimes())
                    .setYaoAddShieldTimes(result.moBa.getArena().getArenaYaoAddShieldTimes())
                    .setWin1V2AfterAllyDeadTimes(result.moBa.getArena().getArena1V2WinAfterAllyDeadTimes())
                    .setTripleKillTimes(result.moBa.getArena().getTripleKillTimes())
                    .setPickHealthKitTimes(result.moBa.getArena().getPickHealthKitTimes())
                    .setTotalRoundNumber(result.moBa.getArena().getTotalRoundNumber())
                    .setSelfEliminatedTimes(result.moBa.getArena().getSelfEliminatedTimes())
                    .setWin1V3AfterAllyDeadTimes(result.moBa.getArena().getWin1V3AfterAllyDeadTimes())
                    .setRemainScoreOfNo1Team(result.moBa.getArena().getRemainScoreOfNo1Team())
                    .setLowPercentKillOtherPlayersTimes(result.moBa.getHok().getLowPercentKillOtherPlayersTimes())
                    .setRescueTeammateTimes(result.moBa.getArena().getRescueTeammateTimes())
                    .setIsLoseMvp(result.getBattleEventValue(BattleEventType.BET_ARENA_IS_LOSE_MVP) != 0 ? 1 : 0)
                    .setHeroId(result.getBattleEventValue(BattleEventType.BET_ARENA_ROLE))
                    .setIsTeamMatch(result.getBattleEventValue(BattleEventType.BET_IS_TEAM_MATCH)==1)
                    .setFootballGoals(result.moBa.getFootball().getFootballGoals());

            for (int i = BattleEventType.BET_ARENA_CARD_1_VALUE; i < BattleEventType.BET_ARENA_CARD_15_VALUE; i++) {
                int cardId = result.getBattleEventValue(BattleEventType.forNumber(i));
                if (cardId != 0){
                    arenaDataBuilder.addUsedCards(cardId);
                }
            }
            for (int i = BattleEventType.BET_ARENA_ROUND_RESULT_1_VALUE;
                 i < BattleEventType.BET_ARENA_ROUND_RESULT_1_VALUE + result.moBa.getArena().getTotalRoundNumber(); i++) {
                arenaDataBuilder.addRoundResults(result.getBattleEventValue(BattleEventType.forNumber(i)));
            }
            for (int i = BattleEventType.BET_ARENA_ROUND_1_KILL_NUMBER_VALUE;
                 i < BattleEventType.BET_ARENA_ROUND_1_KILL_NUMBER_VALUE + result.moBa.getArena().getTotalRoundNumber(); i++) {
                arenaDataBuilder.addRoundKillNumbers(result.getBattleEventValue(BattleEventType.forNumber(i)));
            }
            // 峡谷所有玩法的mvp和win
            if (ArenaMatchGroupData.getInstance().is5v5Match(result.getGameModel())) {
                arenaDataBuilder.setIsMvp(result.getBattleEventValue(BattleEventType.BET_WOLF_ISMVP) != 0 ? 1 : 0)
                        .setIsWin(result.getBattleEventValue(BattleEventType.BET_LEVEL_RESULT) == 0)
                        .setIsLoseMvp(result.getBattleEventValue(BattleEventType.BET_ARENA_IS_LOSE_MVP) != 0 ? 1 : 0);
            } else if (ArenaMatchGroupData.getInstance().is3v3Match(result.getGameModel())){
                // 3v3前两名都取胜
                final int teamRank = result.getBattleEventValue(BattleEventType.BET_TEAM_RANK);
                arenaDataBuilder.setIsMvp(result.getBattleEventValue(BattleEventType.BET_ARENA_IS_MVP) != 0 ? 1 : 0)
                        .setIsWin(teamRank == 1 || teamRank == 2);
            } else if (ArenaMatchGroupData.getInstance().isArenaMatch(result.getGameModel())){
                arenaDataBuilder.setIsMvp(result.getBattleEventValue(BattleEventType.BET_ARENA_IS_MVP) != 0 ? 1 : 0)
                        .setIsWin(result.getBattleEventValue(BattleEventType.BET_TEAM_RANK) == 1);
            }
            // 峡谷挂机
            if (result.getBattleEventValue(BattleEventType.BET_ARENA_REPUTATION_REPORT) == 1 ||
                    result.getBattleEventValue(BattleEventType.BET_HOK_RIFT_AFK_PERCENT) >= 40) {
                arenaDataBuilder.setIsAfk(true);
            }
        }
        data.setArenaData(arenaDataBuilder.build());

        StringBuilder builder = new StringBuilder();
        builder.append("pass:").append(data.getPass()).append(",at:").append(data.getAtRound()).append(",level_type:")
                .append(data.getLevelType()).append(",level_id:").append(data.getLevelId()).append(",match:")
                .append(data.getMatchType()).append(",competitionSeason:").append(data.getCompetitionSeason())
                .append(",competitionGameType:").append(data.getCompetitionGameType())
                .append(",rank:").append(data.getRank())
                .append(",randEvent:").append(data.getRandEvent())
                .append(",arenaCardQualityChroma:").append(data.getArenaData().getCardQualityChroma())
                .append(",arenaTotalDamage:").append(data.getArenaData().getTotalDamage())
                .append(",arenaTotalAssist:").append(data.getArenaData().getTotalAssist())
                .append(",arenaTotalKill:").append(data.getArenaData().getTotalKill())
                .append(",arenaUseItemTimes:").append(data.getArenaData().getUseItemTimes())
                .append(",arenaIsMvp:").append(data.getArenaData().getIsMvp())
                .append(",arenaDestroyObjectTimes:").append(data.getArenaData().getDestroyObjectTimes())
                .append(",arenaPentaKillTimes").append(data.getArenaData().getPentaKillTimes())
                .append(",arenaPentaDoubleKillInOneRoundTimes").append(data.getArenaData().getDoubleKillInOneRoundTimes())
                .append(",arenaSurviveInPoisonMaxSeconds").append(data.getArenaData().getSurviveInPoisonMaxSeconds())
                .append(",arenaSunceCarCarryOtherPlayersTimes").append(data.getArenaData().getSunceCarCarryOtherPlayersTimes())
                .append(",arenaYaoAddShieldTimes").append(data.getArenaData().getYaoAddShieldTimes())
                .append(",arena1V2WinAfterAllyDeadTimes").append(data.getArenaData().getWin1V2AfterAllyDeadTimes())
                .append(",arenaRescueTeammateTimes").append(data.getArenaData().getRescueTeammateTimes());

        if (result.hasBattleEventValue(BattleEventType.BET_SELF_OUT_IN_TEAM)) {
            data.setOutInTeam(result.getSelfOutInTeam());
            builder.append(",out_in_team:").append(data.getOutInTeam());
        }

        if (result.getCostTime() > 0) {
            data.setElapsedTime(result.getCostTime());
            builder.append(",elapsed:").append(data.getElapsedTime());
        }

        if (result.getSurvivalTime() > 0) {
            data.setSurvivalTime(result.getSurvivalTime());
            builder.append(",survival:").append(data.getSurvivalTime());
        }

        result.getBattleEventValueList().forEach(kv -> {
            data.addStat(KeyValueInt64.newBuilder().setKey(kv.key.getNumber()).setValue(kv.value).build());
            builder.append(",stat:[").append(kv.key.getNumber()).append("=").append(kv.value).append("]");
        });

        LOGGER.info("dispatch level finish, battle:{} player:{} data:[{}]", battleId, player.getUid(), builder);
        new PlayerFinishLevelEvent(player).setLevelData(data).setBattlePlayers(battlePlayers).dispatch();

        player.getPlayerEventManager().dispatch(new FinishLevelEvent(player.getConditionMgr()).setLevelEventData(data.build()));

    }

    public void dispatchBattleEvent(GlobalBattleResult result, MemberBaseInfo.Builder battlePlayerMemberInfo,
            PlayerInteraction.PiiDsSettlementParams dsSettlementParams,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, int mapSource, int logicMapSource) {

        StringBuilder memberRelationStrBuilder = new StringBuilder();
        for (MemberBaseInfo memberInfo : dsSettlementParams.getBattleMemberList()) {
            if (memberInfo.getIsRobot()) {
                continue;
            }
            int relationType = player.getFriendManager().getRelationTypeVal(memberInfo.getUid());
            if (relationType > 0) {
                memberRelationStrBuilder.append(memberInfo.getUid()).append(",").append(relationType).append(";");
            }
        }
        String memberRelationStr = memberRelationStrBuilder.toString();

        BattleEventData.Builder data = BattleEventData.newBuilder().setMatchType(result.getMatchType())
                .setIsWin(result.getBattleResult() == BattleResultCode.BATTLE_RESULT_CODE_WIN).setIsMVP(result.isMVP())
                .addAllLevelIds(result.getLevelBattleResults().stream().map(LevelBattleResult::getLevel).collect(
                        Collectors.toList())).addAllRoomMember(dsSettlementParams.getRoomMemberList())
                .setBattleCreateTime(result.getBattleStartTime()).setCompetitionSeason(result.getCompetitionSeason())
                .setCompetitionGameType(result.getCompetitionGameType()).setIsGiveUp(result.isGiveUp())
                .setCompetitionScore(result.getCompetitionLevelScore())
                .setCompetitionType(result.getCompetitionCompType())
                .setIsSinglePlay(ntfBuilder.getBattleMemberCount() <= 1)
                .setBattleCostTime(result.getBattleEndTime() - result.getBattleStartTime())
                .setIgnoreFinishBattleEventTask(result.ignoreFinishBattleEventTask())
                .addAllReputationBehaviorTypeIds(result.getReputationBehaviorTypeIds())
                .setUgcId(result.getUgcId());
        StringBuilder roomMemberRelationStrBuilder = new StringBuilder();
        dsSettlementParams.getRoomMemberList().forEach(uid -> {
            if (player.getFriendManager().isFriend(uid)) {
                data.addRoomFriend(uid);
            }
            int relationType = player.getFriendManager().getRelationTypeVal(uid);
            if (relationType > 0) {
                roomMemberRelationStrBuilder.append(uid).append(",").append(relationType).append(";");
            }
        });
        StringBuilder builder = new StringBuilder();
        builder.append("win:").append(data.getIsWin()).append(",mvp:").append(data.getIsMVP()).append(",match:")
                .append(data.getMatchType()).append(",competitionSeason:").append(data.getCompetitionSeason())
                .append(",competitionGameType:").append(data.getCompetitionGameType()).append(",competitionScore:")
                .append(data.getCompetitionScore()).append(",competitionType:").append(data.getCompetitionType())
                .append(",ignoreFinishBattleEventTask:").append(data.getIgnoreFinishBattleEventTask())
                .append(",ugcId:").append(data.getUgcId());

        result.getBattleEventValueList().forEach(kv -> {
            data.addStat(KeyValueInt64.newBuilder().setKey(kv.key.getNumber()).setValue(kv.value).build());
            builder.append(",stat:[").append(kv.key.getNumber()).append("=").append(kv.value).append("]");
        });

        //对局中使用的技巧
        for (int skillId : result.getSkillSet()) {
            PlayerSkillEvent skillEvent = new PlayerSkillEvent(player.getConditionMgr());
            skillEvent.setSkillId(skillId);
            player.getPlayerEventManager().dispatch(skillEvent);
        }
        //X模式下技巧分达到X
        for (LevelBattleResult battleResult : result.getLevelBattleResults()) {
            LevelType levelType = battleResult.getLevelType();
            PlayerSkillByModelEvent modelEvent = new PlayerSkillByModelEvent(player.getConditionMgr());
            modelEvent.setLevelType(levelType.getNumber());
            modelEvent.setSkillGrade(result.getSkillGrade());
            player.getPlayerEventManager().dispatch(modelEvent);
        }
        // tlog 流水
        int mapNum = 0;
        int campSide = 0;
//        for (LevelBattleResult levelBattleResult : result.getLevelBattleResults()) {
//            if (levelBattleResult.getLevel() == 0) { //跳关
//                continue;
//            }
//            TlogFlowMgr.sendPlayerMapResultFlow(player, result, levelBattleResult, battlePlayerMemberInfo, ++mapNum,
//                    roomMemberList.size(), roomMemberRelationStrBuilder.toString());
//            campSide = levelBattleResult.getLevelCampSide();
//        }
        TlogFlowMgr.sendPlayerBattleResultFlow(player, result, battlePlayerMemberInfo,
                dsSettlementParams.getFashionRank(), memberRelationStr,
                campSide, ntfBuilder, dsSettlementParams.getRoomMemberCount(),
                roomMemberRelationStrBuilder.toString(),
                dsSettlementParams.getLevelRoundABTestTag());
        //上报营地
        BattleReportUtil.reportBattleEnd(player, result);

        //抛出大王数据补充事件
        boolean hasChaseDetailData = dsSettlementParams.getDetailData().hasChaseDetailData();
        if (hasChaseDetailData){
            G6Common.ChaseCustomBattleDetailData chaseDetailData = dsSettlementParams.getDetailData().getChaseDetailData();
            new PlayerChaseCustomBattleDetailDataEvent(player).setChaseCustomBattleDetailData(chaseDetailData.toBuilder()).dispatch();
        }

        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(UpdateMemberBaseInfoSource.Settlement);
        player.getRecommendMgr().onFinishBattle(result.getMatchType());

        boolean isSpecialLevel = false;
        if(result.getMatchType() == LevelType.LT_MAYDAY_INF.getNumber()) //MAYDAY无尽模式特殊处理
        {
            isSpecialLevel = true;
        }

        if (!isSpecialLevel && battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_Custom) {
            LOGGER.debug("skip battle finish battle event cause custom game, playerUid:{}",
                    battlePlayerMemberInfo.getUid());
            return;
        }
        PlayerFinishBattleEvent playerFinishBattleEvent = new PlayerFinishBattleEvent(player)
                .setModeType(result.getMatchType())
                .setBattleData(data)
                .setMemberInfoData(battlePlayerMemberInfo.build())
                .setMatchTypeConf(result.getMatchTypeConf());

        if (battlePlayerMemberInfo.getRuleInfo().getGameModeType() == GameModeType.GMT_UgcMultiRoundScore) {
            if (!ntfBuilder.getUgcMultiRoundScoreSettlementInfo().getIsEnd()) {
                LOGGER.debug("skip battle finish event cause ugc multi round not end, playerUid:{}",
                        battlePlayerMemberInfo.getUid());
                return;
            }
            // 处理ugc多轮对局结算数据事件
            if (battlePlayerMemberInfo.getUgcUserData().getBattleSettlementInfosCount() > 0) {
                // 设置总体的名次
                RoomBattleSettlementInfo battleSettlementInfos = battlePlayerMemberInfo.getUgcUserData()
                        .getBattleSettlementInfos(0);
                playerFinishBattleEvent.setRank(battleSettlementInfos.getRankIndex()); // 完成对局事件加上对应的名次数据
                playerFinishBattleEvent.setGameModeType(GameModeType.GMT_UgcMultiRoundScore_VALUE);
                // 上报对应活动的积分变更事件
                int relatedActivityIdConf = UGCMapCommonConf.getInstance()
                        .getIntConf(UgcMapConfEnum.UGC_MUlTI_ROUND_MATCH_RELATED_ACTIVITY_ID, 0);
                if (relatedActivityIdConf > 0) {
                    AddScoreActivityTaskScoreEvent addScoreActivityScoreEvent = new AddScoreActivityTaskScoreEvent(player);
                    addScoreActivityScoreEvent.setActivityId(relatedActivityIdConf)
                            .setAddScore(battleSettlementInfos.getMemberMultiRoundScoreSettlementInfo().getCurScoreNum()
                                    + battleSettlementInfos.getMemberMultiRoundScoreSettlementInfo()
                                    .getScoreIncrement());
                    player.getPlayerEventManager().dispatch(addScoreActivityScoreEvent);
                }
            }

        }
        playerFinishBattleEvent.dispatch();
        player.getPlayerEventManager().dispatch(new FinishBattleEvent(player.getConditionMgr())
                .setBattleData(data.build())
                .setGameModeType(result.getMatchTypeConf().getGameModeType())
                .setMemberInfoData(battlePlayerMemberInfo.build())
                .setMatchType(result.getMatchType()));
        player.getPlayerEventManager().reportGameplayEventData(result.getFeatureId(), result.getGameplayEventDataList());
        LOGGER.debug("dispatch battle gameplay event, battle:{} player:{} featureId:{}, data:[{}]",
                result.getBattleId(), player.getUid(), result.getFeatureId(), result.getGameplayEventDataList());

        if (result.getUgcId() > 0) {
            MapData.UgcEventData eventData = new MapData.UgcEventData();
            eventData.gameModeType = battlePlayerMemberInfo.getRuleInfo().getGameModeType();
            eventData.mapSource = mapSource;
            eventData.ugcId = result.getUgcId();
            eventData.matchType = result.getMatchType();
            eventData.data = data;
            eventData.logicMapSource = logicMapSource;
            eventData.fromCollectionId = dsSettlementParams.getFromCollectionId();
            eventData.mapPoolId = dsSettlementParams.getMapPoolId();
            eventData.matchTypeConf = result.getMatchTypeConf();
            eventData.someWhereIn = battlePlayerMemberInfo.getRuleInfo().getSomeWhereIn();
            eventData.battleStartTime = result.getBattleStartTime();
            eventData.battleEndTime = result.getBattleEndTime();
            eventData.battleId = result.getBattleId();
            eventData.omdGame = TycConfs.isOMDGame(result.getMatchType());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player:{} UgcEventData matchType:{}  omdGame:{}, ugcId:{}, mapPoolId:{}", player.getUid(),
                    result.getMatchType(), eventData.omdGame, eventData.ugcId, eventData.mapPoolId);
            }
            player.getPlayerUgcManager().handlerUgcMapEvent(eventData);
            if (result.getBattleResult().equals(BattleResultCode.BATTLE_RESULT_CODE_WIN)) {
                player.getPlayerUgcManager()
                        .triggerPlayedMapEvt(result.getUgcId(), mapSource, true, result.getMatchType(), data,
                                logicMapSource, dsSettlementParams.getFromCollectionId());
            }
        }

        if(result.getUgcId() > 0 || result.getMatchTypeConf().getModeID() == 7){
            // 星世界匹配入口
            new PlayerUgcMatchPlayModeSelectEvent(player).setParam(data.getIsGiveUp(),data.getIsWin(),battlePlayerMemberInfo.getRuleInfo().getSomeWhereIn(),result.getMatchTypeConf().getGameModeType()).dispatch();
            new PlayerUgcMatchPlayModeSelectEventEnableQuit(player).setParam(battlePlayerMemberInfo.getRuleInfo().getSomeWhereIn()).dispatch();
        }
    }

    private Optional<Long> getElapsedTime(LevelBattleResult event) {
        return event.getEventMap().get(BattleEventType.BET_REACH_END_TIME).stream().findFirst();
    }

    private LevelAchievementInfo getLevelAchievementInfo(T_LevelInfoData level) {
        LevelAchievementInfo info = player.getUserAttr().getLevelIllustration().getLevelAchievementInfo(level.getId());
        if (info == null) {
            info = new LevelAchievementInfo();
            info.setId(level.getId());
            info.setType(level.getLevelType().getNumber());
            info.setBestScore(-1);
            player.getUserAttr().getLevelIllustration().putLevelAchievementInfo(level.getId(), info);
        }

        return info;
    }

    private void refreshRank(long battleId, int playMode, LevelBattleResult event) {
        long currentSec = DateUtils.currentTimeSec();

        var rankingConfList = RankIdLevelIdMapper.forWrite(event.getLevel(), playMode, currentSec);
        T_LevelInfoData levelInfo = LevelInfoData.getInstance().get(event.getLevel());
        if (levelInfo == null || rankingConfList.isEmpty()) {
            LOGGER.debug("cannot find config for level of this mode, uid:{} battleId:{} level:{} mode:{}",
                    player.getUid(), battleId, event.getLevel(), playMode);
            return;
        }

        List<Integer> rankIds = Lists.newArrayList();
        rankingConfList.forEach(c -> rankIds.add(c.getRankId()));

        var ruleConf = RankingRuleConfData.getInstance().get(rankingConfList.get(0).getRule());
        if (ruleConf == null) {
            LOGGER.error("rank rule conf not found, rule:{}", rankingConfList.get(0).getRule());
            return;
        }

        BattleEventType ctxType = ruleConf.getLevelScoreType();
        if (ctxType == BattleEventType.BET_Unknown) {
            LOGGER.debug("cannot find config for level : {}", event.getLevel());
            return;
        }

        RankSeasonInfo seasonInfo = RankIdSeasonIdMapper.ofTs(rankingConfList.get(0).getRankId(), currentSec);
        if (seasonInfo == null) {
            LOGGER.debug("cannot find season for level:{}", event.getLevel());
            return;
        }

        // 是否过关
        if (event.getLevelResult() == BattleResultCode.BATTLE_RESULT_CODE_WIN) {
            long eventValue = event.getBattleEventValue(ctxType);
            // 如果没有上报耗时，则不会更新数据，同时过滤掉无效值
            if (eventValue > 0) {
                boolean acceptable = true;

                // check pass time lower bound
                if (ctxType == BattleEventType.BET_REACH_END_TIME) {
                    acceptable = levelInfo.getLevelPassTimeSecLowerBound() * 1000 <= eventValue;
                }

                if (acceptable) {
                    List<RefreshDescriptor> scores = Lists.newArrayList();
                    for (var conf : rankingConfList) {
                        scores.add(RefreshDescriptor.of(conf, seasonInfo, (int) eventValue));
                    }

                    player.getRankManager().batchRefresh(scores);
                    LOGGER.debug("report level rank score, uid:{} rankId:{} level:{} score:{}", player.getUid(),
                            rankIds, event.getLevel(), eventValue);
                } else {
                    LOGGER.error(
                            "refuse to report new score due to bound, uid:{} rankId:{} level:{} lower_bound:{} score:{}",
                            player.getUid(), rankIds, event.getLevel(),
                            levelInfo.getLevelPassTimeSecLowerBound() * 1000, eventValue);
                }

            }
        }
    }

    private void refreshBattlePlayerRank(long battleId, GlobalBattleResult globalBattleResult) {
        List<G6Common.BattlePlayerRankInfo> battlePlayerRankInfos = globalBattleResult.getBattlePlayerRankInfos();
        if (battlePlayerRankInfos == null || battlePlayerRankInfos.isEmpty()) {
            LOGGER.info("no battlePlayerRankInfos, matchType:{}", globalBattleResult.getMatchType());
            return;
        }

        if (!TycConfs.isOpenOMDFreshRank()) {
            return;
        }

        // 获取当前玩法类型ID
        int matchType = globalBattleResult.getMatchType();
        ResMatch.MatchType gameCfg = MatchTypeData.getInstance().get(matchType);
        if (gameCfg == null) {
            LOGGER.error("MatchType conf not found, uid:{}, matchType:{}", player.getUid(), matchType);
            return;
        }

        try {
            long currentSec = DateUtils.currentTimeSec();
            CurrentExecutorUtil.runJob(() -> {
                for (G6Common.BattlePlayerRankInfo freshRankInfo : battlePlayerRankInfos) {
                    RankingConf rankingConf = RankingConfData.getInstance().get(freshRankInfo.getRankId());
                    if (rankingConf == null) {
                        LOGGER.error("rank conf not found, battleId:{} rankId:{}", battleId, freshRankInfo.getRankId());
                        continue;
                    }

                    RankSeasonInfo info = RankIdSeasonIdMapper.ofTs(rankingConf.getRankId(), currentSec);
                    if (info == null) {
                        LOGGER.error("rank season not found, battleId:{} rankId:{}", battleId, rankingConf.getRankId());
                        continue;
                    }

                    player.getRankManager().refresh(freshRankInfo.getRankId(), freshRankInfo.getScore(), info.getId());
                    LOGGER.info("refreshBattlePlayerRank log, uid: {} battle: {} rankId: {}, score: {}",
                            player.getUid(), battleId, freshRankInfo.getRankId(), freshRankInfo.getScore());
                }
                return null;
            }, "refreshBattlePlayerRank", false);
        } catch (NKCheckedException e) {
            LOGGER.error("call job to refreshBattlePlayerRank execute failed : {}", e.getEnumErrCode());
        }
    }

    private void levelDropReward(long battleId, GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, ItemChangeReason itemChangeReason) {
        ChangedItems items = new ChangedItems(itemChangeReason.getNumber(), String.valueOf(battleId));
        for (LevelBattleResult levelBattleResult : globalBattleResult.getLevelBattleResults()) {
            levelBattleResult.getDropItemMap().forEach(items::mergeItemInfo);
        }
        items.addChangeReservedParams(globalBattleResult.getMatchType());
        player.getBagManager().AddItems2(items, false, battleId).getKey()
                .throwErrorIfNotOk("levelDropReward error items:{}", items.toString());
    }

    private void fpsBattleSettlement(MemberBaseInfo.Builder battlePlayerMemberInfo,
            GlobalBattleResult globalBattleResult,
            LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        long battleId = globalBattleResult.getBattleId();

        List<LevelDropItemInfo> dropItemInfo = globalBattleResult.getFpsDropItemInfo();
        int matchType = globalBattleResult.getMatchType();

        // 获取当前玩法类型ID
        ResMatch.MatchType gameCfg = MatchTypeData.getInstance().get(matchType);
        if (gameCfg == null) {
            LOGGER.error("MatchType conf not found, uid:{}, matchType:{}", player.getUid(), matchType);
            return;
        }
        int gameTypeId = gameCfg.getGameTypeId();

        // 玩家自己的奖励
        FpsUtil.dropReward(player, battleId, matchType, dropItemInfo, ntfBuilder, false);

        // 返还给其他玩家的奖励
        List<FpsReturnItemInfo> returnItemInfoList = globalBattleResult.getFpsReturnItemInfo();
        if (returnItemInfoList != null) {
            for (FpsReturnItemInfo returnItemInfo : returnItemInfoList) {
                PiiFpsReturnItemParams.Builder params = PiiFpsReturnItemParams.newBuilder();
                params.setBattleId(battleId);
                params.setMatchType(matchType);
                params.addAllItemInfo(returnItemInfo.getItemInfoList());
                params.setTeammatePlayerUid(player.getUid());
                params.setTeammatePlayerName(player.getName());
                params.setEvacuateTimeMs(DateUtils.currentTimeSec() * 1000);
                params.addAllEquipItemId(returnItemInfo.getEquipItemIdList());

                PlayerInteractionData.Builder data = PlayerInteractionData.newBuilder();
                data.setInstruction(PlayerInteractionInstruction.PII_FPS_RETURN_ITEM);
                data.setFpsReturnItemParams(params);
                PlayerInteractionInvoker.interact(returnItemInfo.getUid(), data);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("return item to player:{}, battleId:{}, matchType:{}, item:{}", returnItemInfo.getUid(),
                        battleId, matchType, returnItemInfo.getItemInfoList());
                }
            }
        }

        // 肉鸽模式结算
        if (gameTypeId == MTEMGT_RogueLikeGame.getNumber()) {
            FpsUtil.roguelikeSettlement(player, battlePlayerMemberInfo, globalBattleResult, ntfBuilder);
        }
    }

    private NKPair<Integer, HashMap<Integer, Integer>> activityBattleDrop(GlobalBattleResult battleResult,
                                                                          NKPair<Integer, HashMap<Integer, Integer>> battleDropInfo) {
        int key = battleDropInfo.getKey();
        HashMap<Integer, Integer> battleDrop = battleDropInfo.getValue();
        var activityDrops = player.getActivityManager().getActivityDrops(battleResult);
        if (!activityDrops.isEmpty()) {
            for (var entry : activityDrops.entrySet()) {
                battleDrop.merge(entry.getKey(), entry.getValue(), Integer::sum);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("add activityDrops, rewardId:{} rewardNum:{}", entry.getKey(), entry.getValue());
                }
            }
        }
        return new NKPair<>(key, battleDrop);
    }

    public void battleDropReward(long battleId, GlobalBattleResult globalBattleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, ItemChangeReason itemChangeReason) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("battleDropReward debug, battleId:{} globalBattleResult:{}", battleId, globalBattleResult);
        }
        NKPair<Integer, HashMap<Integer, Integer>> battleDropInfo = getBattleDropByConfig(globalBattleResult);

        // 活动配置掉落
        battleDropInfo = activityBattleDrop(globalBattleResult, battleDropInfo);

        // Arena 玩法特殊掉落规则
        battleDropInfo = ArenaBattleDrop(battleId, globalBattleResult, battleDropInfo);
        battleDropInfo = arenaBattleDropSpecial(battleId, globalBattleResult, battleDropInfo);
        // 每日胜利宝箱掉落
        battleDropInfo = arenaDailyBattleDrop(globalBattleResult, battleDropInfo,ntfBuilder);

        LOGGER.debug(
                "battleDropReward debug, playerUid:{} battleId:{} dropId:{} dropReward:{}",
                player.getUid(), globalBattleResult.getBattleId(), battleDropInfo.getKey(), battleDropInfo.getValue());
        if (ntfBuilder.getRuleInfo().getRoomType() == RoomType.CustomRoom) {
            LOGGER.debug("custom room skip drop procedure, battleId:{}", battleId);
            return;
        }
        calcBattleRewardAdditionAndLimit(battleId, itemChangeReason, battleDropInfo.getValue(), ntfBuilder,
                globalBattleResult);
    }

    /**
     * 检查是否有每日moba胜利宝箱
     */
    private NKPair<Integer, HashMap<Integer, Integer>> arenaDailyBattleDrop(GlobalBattleResult globalBattleResult, NKPair<Integer, HashMap<Integer,
            Integer>> battleDropInfo,CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder) {
        //添加开关
        String arenaDailyBattleDropSwitch = RealtimeConfig.getInstance().getRealtimeConfigInRainbow()
                .getOrDefault("arenaDailyBattleDropSwitch", "false").toString();
        LOGGER.debug("arenaDailyBattleDropSwitch = {}",arenaDailyBattleDropSwitch);
        if(Boolean.getBoolean(arenaDailyBattleDropSwitch)){
            return battleDropInfo;
        }

        int dailyVictoryNum = player.getArenaMgr().getDailyVictoryNum();

        //中途退出不处理
        if(globalBattleResult.isGiveUp()){
            if(dailyVictoryNum == DailyVictoryChestConf.getInstance().getMaxStorage()){
                ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(-1);//标记已完成
            }else{
                ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(dailyVictoryNum);
            }
            return battleDropInfo;
        }
        //失败不处理, 前2名才算胜利
        if(!globalBattleResult.moBa.getCommon().isWin() && !globalBattleResult.moBa.getArena().isArenaTeamRankTop2()){
            LOGGER.debug("globalBattleResult.isWin() is false, skip drop daily victory chest, battleId:{}",
                    globalBattleResult.getBattleId());
            if(dailyVictoryNum == DailyVictoryChestConf.getInstance().getMaxStorage()){
                ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(-1);//标记已完成
            }else{
                ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(dailyVictoryNum);
            }
            return battleDropInfo;
        }

        //次数已经是最大次数了，还需要传给前端，用来做特效处理
        if(dailyVictoryNum == DailyVictoryChestConf.getInstance().getMaxStorage()){
            ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(-1);//标记已完成
        }

        ResDailyVictoryChestData resDailyVictoryChestData = DailyVictoryChestConf.getInstance()
                .get(dailyVictoryNum + 1);
        //检查是否配置了每日胜利宝箱
        if(resDailyVictoryChestData == null){
            LOGGER.debug("resDailyVictoryChestData is null, skip drop daily victory chest, dailyVictoryNum:{}",
                    dailyVictoryNum + 1);
            return battleDropInfo;
        }

        //检查关卡
        int matchType = globalBattleResult.getMatchType();
        boolean contains = resDailyVictoryChestData.getMatchIdList().contains(matchType);
        if(!contains){
            LOGGER.debug("matchType:{} not in resDailyVictoryChestData", matchType);
            return battleDropInfo;
        }

        //获取存储的奖励倍数
        int dailyVictoryStorage = player.getArenaMgr().getDailyVictoryStorage(dailyVictoryNum + 1);
        LOGGER.debug("arenaDailyBattleDrop dailyVictoryStorage:{} , itemId :{} , itemNum:{}", dailyVictoryStorage,resDailyVictoryChestData.getItemId(),resDailyVictoryChestData.getItemNum());
        //添加物品
        int key = battleDropInfo.getKey();
        HashMap<Integer, Integer> battleDrop = battleDropInfo.getValue();
        battleDrop.merge(resDailyVictoryChestData.getItemId(), resDailyVictoryChestData.getItemNum() * dailyVictoryStorage, (oldValue, newValue) -> oldValue + newValue);

        //记录发宝箱次数
        player.getArenaMgr().addDailyVictoryNum(DailyVictoryChestConf.getInstance().getMaxStorage());

        //结算信息里，新增宝箱进度
        ntfBuilder.getArenaSettlementInfoBuilder().setArenaDailyVictoryNum(player.getArenaMgr().getDailyVictoryNum());

        return new NKPair<>(key, battleDrop);
    }

    // 计算结算奖励的加成和掉落限制
    private void calcBattleRewardAdditionAndLimit(long battleId, ItemChangeReason itemChangeReason,
            HashMap<Integer, Integer> battleDrop,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder,
            GlobalBattleResult globalBattleResult) {
        // 自己或队友有回归特权
        boolean isSelfOrTeammateInReturnPrivilege = player.getReturnActivityManager().isPrivilegeValid();
        // 判断好友是否有回归特权
        boolean friendReturnPrivilege = false;
        // 判断 GameModeType 是否满足条件
        boolean gameModeTypeValid = true;
        for (long uid : globalBattleResult.getRoomMemberList()) {
            MemberBaseInfo info = globalBattleResult.getBattleMemberBaseInfo(uid);
            if (info != null) {
                if (uid == player.getUid()) {
                    gameModeTypeValid = info.getRuleInfo().getGameModeType() != GameModeType.GMT_Custom;
                } else {
                    if (info.getReturningPrivilege()) {
                        isSelfOrTeammateInReturnPrivilege = true;
                        if (player.getFriendManager().isFriend(uid)) {
                            friendReturnPrivilege = true;
                        }
                    }
                }
            }
        }
        boolean finalIsSelfOrTeammateInReturnPrivilege = isSelfOrTeammateInReturnPrivilege;
        boolean finalGameModeTypeValid = gameModeTypeValid;
        // 判断是否经典和娱乐
        boolean privilegeMode = false;
        MatchType matchType = MatchTypeData.getInstance().get(globalBattleResult.getMatchType());
        if (matchType != null) {
            ResMatch.MatchModeType matchModeType = MatchModeTypeData.getInstance().get(matchType.getModeID());
            if (matchModeType != null &&
                    (matchModeType.getModeID() == 1 || matchModeType.getModeID() == 3)) {
                privilegeMode = true;
            }
        }

        ResMisc.MiscBattleConf battleConf = MiscConf.getInstance().getMiscConf().getBattleConf();
        long expireTimeMs = DateUtils.getFirstDayOfWeek(DateUtils.currentTimeMillis()).getTime()
                + DateUtils.timeToSeconds(battleConf.getBattleDropDailyRefreshTime()) * DateUtils.ONE_SECOND_MILLIS
                + DateUtils.ONE_WEEK_MILLIS; // 每周限制总掉落

        HashMap<Integer, Integer> dropDailyLimit = new HashMap<>();
        for (Item item : battleConf.getBattleDropDailyLimitList()) {
            dropDailyLimit.put(item.getItemId(), item.getItemNum());
        }

        NKPair<BattleRewardAdditionType, HashMap<Integer, Integer>> platPrivilegesAdditionInfo =
                MiscConf.getInstance().getPlatPrivileges(player.getPlayerPlatPrivilegesType());
        ChangedItems items = new ChangedItems(itemChangeReason.getNumber(), String.valueOf(battleId));
        ChangedItems costItems = new ChangedItems(itemChangeReason.getNumber(), String.valueOf(battleId));
        boolean finalFriendReturnPrivilege = friendReturnPrivilege;
        boolean finalPrivilegeMode = privilegeMode;
        AtomicBoolean usedReturningPrivilege = new AtomicBoolean(false);
        battleDrop.forEach((itemId, itemNum) -> {
            LevelDropItemInfo.Builder dropBuilder = LevelDropItemInfo.newBuilder().setItemId(itemId);
            int addValue = itemNum;
            int totalLimitAddValue = 0; // 当前最大可获得
            if (dropDailyLimit.containsKey(itemId)) {
                int currentValue = (int) player.getLimitManager()
                        .getValue(CommonLimitType.CLT_BattleDailyLimit, itemId);
                int weeklyLimitAddValue = dropDailyLimit.get(itemId);
                dropBuilder.setCurrentCount(currentValue);
                dropBuilder.setLimitCount(weeklyLimitAddValue);
                dropBuilder.setWeekLimitCount(weeklyLimitAddValue);
                if (currentValue >= weeklyLimitAddValue) {
                    dropBuilder.setItemCount(0);
                    dropBuilder.setHasReachedWeekLimit(true);
                    ntfBuilder.addItemList(dropBuilder.build());
                    return;
                }
                totalLimitAddValue = Math.max(totalLimitAddValue, weeklyLimitAddValue - currentValue);
            }
            if (itemId == CoinType.CT_Exp_VALUE) {
                long currExp = player.getPlayerMoneyMgr().getCoinNum(CoinType.CT_Exp.getNumber());
                long maxExp = PlayerLevelConfData.getInstance().getExpUpperLimit();
                if (currExp >= maxExp) {
                    dropBuilder.setItemCount(0);
                    ntfBuilder.addItemList(dropBuilder.build());
                    return;
                } else {
                    totalLimitAddValue = Math.min(totalLimitAddValue, (int) (maxExp - currExp));
                }
            }
            if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                addValue = totalLimitAddValue;
            } else
                // 特权加成
                if (platPrivilegesAdditionInfo.getValue().containsKey(itemId)) {
                    int beforeNum = addValue;
                    addValue += (int) Math.ceil(
                            (double) (itemNum * platPrivilegesAdditionInfo.getValue().get(itemId)) / 100);
                    KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                    additionInfo.setKey(platPrivilegesAdditionInfo.getKey().getNumber());
                    if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                        addValue = totalLimitAddValue;
                        additionInfo.setValue(addValue - beforeNum);
                        dropBuilder.addAddition(additionInfo);
                    } else {
                        additionInfo.setValue(addValue - beforeNum);
                        dropBuilder.addAddition(additionInfo);
                    }
                }

            // 经验加成卡
            if (itemId == CoinType.CT_Exp_VALUE &&
                    player.getItemManager().GetItemNumByItemId(
                            MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId()) > 0) {
                Item_BackpackItem itemConf = BackpackItem.getInstance()
                        .get(MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId());
                int beforeNum = addValue;
                addValue += (int) Math.ceil((double) (itemNum * itemConf.getUseParam(0)) / 100);
                KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                additionInfo.setKey(BattleRewardAdditionType.BRAT_EXP_ADDITION_ITEM_VALUE);
                if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                    addValue = totalLimitAddValue;
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                } else {
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                }
                costItems.mergeItemInfo(MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId(),
                        1);
            } else
                // 印章加成卡
                if (itemId == CoinType.CT_ActivityCoin_VALUE &&
                        player.getItemManager().GetItemNumByItemId(
                                MiscConf.getInstance().getMiscConf().getBattleDropRewardActivityCoinAdditionalItemId())
                                > 0) {
                    Item_BackpackItem itemConf = BackpackItem.getInstance()
                            .get(MiscConf.getInstance().getMiscConf()
                                    .getBattleDropRewardActivityCoinAdditionalItemId());
                    int beforeNum = addValue;
                    addValue += (int) Math.ceil((double) (itemNum * itemConf.getUseParam(0)) / 100);
                    KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                    additionInfo.setKey(BattleRewardAdditionType.BRAT_ACTIVITYCOIN_ADDITION_ITEM_VALUE);
                    if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                        addValue = totalLimitAddValue;
                        additionInfo.setValue(addValue - beforeNum);
                        dropBuilder.addAddition(additionInfo);
                    } else {
                        additionInfo.setValue(addValue - beforeNum);
                        dropBuilder.addAddition(additionInfo);
                    }
                    costItems.mergeItemInfo(
                            MiscConf.getInstance().getMiscConf().getBattleDropRewardActivityCoinAdditionalItemId(), 1);
                }

            // 回归特权结算奖励翻倍
            if (finalPrivilegeMode &&
                    (itemId == CoinType.CT_Exp_VALUE || itemId == CoinType.CT_ActivityCoin_VALUE) &&
                    (player.getReturnActivityManager().isPrivilegeValid() || finalFriendReturnPrivilege) &&
                    player.getReturnActivityManager().isDailyDoubleRewardValid()) {
                int percent = 100;
                ResReturningUser.ReturningUserConstsInfo cfgVal = ReturningUserConstsData.getInstance()
                        .get(ReturningUserConstsEnum.RUCE_PrivilegeDailyAddonPercent);
                if (cfgVal != null) {
                    percent = cfgVal.getValue();
                }
                int beforeNum = addValue;
                addValue += (int) Math.ceil(itemNum * percent / 100);
                KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                if (player.getReturnActivityManager().isPrivilegeValid()) {
                    additionInfo.setKey(BattleRewardAdditionType.BRAT_RETURNING_PRIVILEGES_VALUE);
                } else {
                    additionInfo.setKey(BattleRewardAdditionType.BRAT_FRIEND_RETURNING_PRIVILEGES_VALUE);
                }
                if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                    addValue = totalLimitAddValue;
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                } else {
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                }
                usedReturningPrivilege.set(true);
            }
            int settlementBuffAddNum =  player.getPlayerBattleSettlementItemBuffModel().calculateBattleSettlementBuffItemNum(globalBattleResult,itemId,itemNum);
            if (settlementBuffAddNum > 0){
                int beforeNum = addValue;
                addValue += settlementBuffAddNum;
                KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
                additionInfo.setKey(BattleRewardAdditionType.BRAT_SETTLEMENT_ITEM_BUFF_VALUE);
                if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                    addValue = totalLimitAddValue;
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                } else {
                    additionInfo.setValue(addValue - beforeNum);
                    dropBuilder.addAddition(additionInfo);
                }
            }
            appendArenaAddtionList(dropBuilder, itemId, addValue);

            addValue = fixDropItemNumLimited(itemId, addValue, dropBuilder);
            if (addValue <= 0) {
                LOGGER.debug("battleDropReward addItems up to limit, uid:{} battleId:{} itemId:{}", player.getUid(),
                        battleId, itemId);
                return;
            }
            dropBuilder.setItemCount(addValue);
            items.mergeItemInfo(itemId, addValue);
            ntfBuilder.addItemList(dropBuilder.build());
        });

        if (usedReturningPrivilege.get()) {
            player.getReturnActivityManager().addDailyDoubleReward(1);
        }

        if (finalGameModeTypeValid && !globalBattleResult.isGiveUp() && finalPrivilegeMode
                && finalIsSelfOrTeammateInReturnPrivilege) {
            player.getReturnActivityManager().addFriendshipFireAfterRound(items, ntfBuilder);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("battleDropReward addItems, battleId:{} items:{} costItems:{} dropDailyLimit:{} itemList:{}",
                    battleId, items, costItems, dropDailyLimit, ntfBuilder.getItemListList());
        }
        try {
            var ret = player.getBagManager().MinItems(costItems, battleId);
            if (ret.hasError()) {
                ret.throwError("minItems error, costItems:{}", costItems.toString());
            }
            for (ChangeItem changeItem : items.getChangeItems()) {
                // 每周限制掉落
                if (dropDailyLimit.containsKey(changeItem.getSrcItemId())) {
                    player.getLimitManager().addValue(CommonLimitType.CLT_BattleDailyLimit, changeItem.getSrcItemId(),
                            changeItem.getSrcItemNum(), expireTimeMs);
                }
                addDropItemNumLimited(changeItem.getSrcItemId(), changeItem.getSrcItemNum());
            }
            items.addChangeReservedParams(globalBattleResult.getMatchType());
            player.getBagManager().AddItems2(items, false, battleId).getKey()
                    .throwErrorIfNotOk("battleDropReward error items:{}", items.toString());
        } catch (Exception ex) {
            LOGGER.error("battleDropReward addItems fail:{}", ex.getMessage());
        }
    }


    // 特权加成
    private int getPrivilegeAddition(int itemId, int itemNum, int addValue, HashMap<Integer, Integer> dropDailyLimit,
            int totalLimitAddValue, LevelDropItemInfo.Builder dropBuilder) {
        NKPair<BattleRewardAdditionType, HashMap<Integer, Integer>> platPrivilegesAdditionInfo =
                MiscConf.getInstance().getPlatPrivileges(player.getPlayerPlatPrivilegesType());
        if (platPrivilegesAdditionInfo.getValue().containsKey(itemId)) {
            int beforeNum = addValue;
            addValue += (int) Math.ceil(
                    (double) (itemNum * platPrivilegesAdditionInfo.getValue().get(itemId)) / 100);
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(platPrivilegesAdditionInfo.getKey().getNumber());
            if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                addValue = totalLimitAddValue;
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            } else {
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            }
        }
        return addValue;
    }

    // 道具加成
    private int getItemAddition(int itemId, int itemNum, int addValue, HashMap<Integer, Integer> dropDailyLimit,
            int totalLimitAddValue, LevelDropItemInfo.Builder dropBuilder, ChangedItems costItems) {
        // 经验加成卡
        if (itemId == CoinType.CT_ActivityCoin_VALUE &&
                player.getItemManager().GetItemNumByItemId(
                        MiscConf.getInstance().getMiscConf().getBattleDropRewardActivityCoinAdditionalItemId())
                        > 0) {
            Item_BackpackItem itemConf = BackpackItem.getInstance()
                    .get(MiscConf.getInstance().getMiscConf()
                            .getBattleDropRewardActivityCoinAdditionalItemId());
            int beforeNum = addValue;
            addValue += (int) Math.ceil((double) (itemNum * itemConf.getUseParam(0)) / 100);
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(BattleRewardAdditionType.BRAT_ACTIVITYCOIN_ADDITION_ITEM_VALUE);
            if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                addValue = totalLimitAddValue;
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            } else {
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            }
            costItems.mergeItemInfo(
                    MiscConf.getInstance().getMiscConf().getBattleDropRewardActivityCoinAdditionalItemId(), 1);
        }
        // 印章加成卡
        if (itemId == CoinType.CT_Exp_VALUE &&
                player.getItemManager().GetItemNumByItemId(
                        MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId()) > 0) {
            Item_BackpackItem itemConf = BackpackItem.getInstance()
                    .get(MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId());
            int beforeNum = addValue;
            addValue += (int) Math.ceil((double) (itemNum * itemConf.getUseParam(0)) / 100);
            KeyValueInt32.Builder additionInfo = KeyValueInt32.newBuilder();
            additionInfo.setKey(BattleRewardAdditionType.BRAT_EXP_ADDITION_ITEM_VALUE);
            if (dropDailyLimit.containsKey(itemId) && addValue >= totalLimitAddValue) {
                addValue = totalLimitAddValue;
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            } else {
                additionInfo.setValue(addValue - beforeNum);
                dropBuilder.addAddition(additionInfo);
            }
            costItems.mergeItemInfo(MiscConf.getInstance().getMiscConf().getBattleDropRewardExpAdditionalItemId(), 1);
        }
        return addValue;
    }

    // 排位结算事件
    public void battleSettlementEvent(GlobalBattleResult battleResult,
            CsLetsgo.LetsGoBattleSettlementNtf.Builder ntfBuilder, MemberBaseInfo.Builder battlePlayerMemberInfo) {
        if (battleResult == null) {
            return;
        }
        QualifyingSettlement.Builder qualifyingSettlement = QualifyingSettlement.newBuilder();
        if (MatchDegreeTypeGroupData.getInstance().qualifySettlementAtFinal(battleResult.getMatchType())
                && !battleResult.isBattleFinished()) {
            qualifyingSettlement.setErrorCode(NKErrorCode.BattleQualifySettlementLater.getValue());
            // 中途退出 暂时不进行段位分计算
            LOGGER.info("matchType qualifySettlementAtFinal, uid:{} battleId:{}", player.getUid(),
                    battleResult.getBattleId());
            return;
        }
        try {
            player.getQualifyingManager()
                    .updateQualifyingScore(battleResult, qualifyingSettlement, battlePlayerMemberInfo);
        } catch (Exception e) {
            LOGGER.error("updateQualifyingScore throw exception :uid:{} battleId:{} {} {}", player.getUid(),
                    battleResult.getBattleId(), e.getMessage(), e.getStackTrace());
            WechatLog.debugPanicWithNoticer(Programmer.blitzzhang,"updateQualifyingScore uid:{} battleId:{} Exception: e",
                        player.getUid(), battleResult.getBattleId(), e);
        }
        player.flushPlayerPublic();
        qualifyingSettlement.setIAAConfId(player.getIaaManager().checkBattleSettlementIAAShow(IAAType.IAAT_Qualify_VALUE,battleResult));
        ntfBuilder.setQualifyingSettlement(qualifyingSettlement);
        LOGGER.info("uid:{} battleId:{} qualifyingSettlement:{}", player.getUid(), battleResult.getBattleId(),
                qualifyingSettlement);
    }

    @Override
    protected void onBattleAfterLoginReconnect() {
        player.getPlayerChatManager().addChatGroupKey(player.getUserAttr().getBattleInfo().getGlobalChatGroupKey());
        player.getPlayerChatManager().addChatGroupKey(player.getUserAttr().getBattleInfo().getSideChatGroupKey());
    }

    private void updatePlayerMatchStatics(GlobalBattleResult battleResult,
            PlayerInteraction.PiiDsSettlementParams settlementParams) {
        try {
            player.getWarmRoundManager().updateWarmScoreOnBattleEnd(battleResult, settlementParams);
        } catch (Exception e) {
            // 不要影响结算
            LOGGER.error("player {} battle {} updatePlayerMatchStatics catch",
                    player.getUid(), battleResult.getBattleId(), e);
        }
    }

    /**
     * 用户福袋逻辑处理
     *
     * @param battleResult
     * @param matchTypeConf
     */
    private void playerBlessBagLogicProcess(GlobalBattleResult battleResult, MatchType matchTypeConf) {

        if (battleResult == null || matchTypeConf == null) {
            return;
        }

        // 判断用户是否是MVP, 且对局模式是否是经典模式, 且是否是新手局, 如果不满足条件, 直接返回
        if (!battleResult.isMVP() || matchTypeConf.getModeID() != 1 || battleResult.isGuideWarmRound()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("not meet bless bag generate condition, openId:{}, uid:{}, battle mvp:{}, modeId:{}, " +
                                "guideWarmRound:{}",
                        player.getOpenId(), player.getUid(), battleResult.isMVP(), matchTypeConf.getModeID(),
                        battleResult.isGuideWarmRound());
            }
            return;
        }

        // 申请玩家福袋
        player.applyPlayerBlessBag();
    }

    /**
     * 玩家对局结算信誉分处理
     *
     * @param battleResult
     * @param matchTypeConf
     * @param gameModeType
     * @param roomMembers
     */
    public void playerReputationScoreProcess(GlobalBattleResult battleResult, MatchType matchTypeConf,
                                              GameModeType gameModeType, List<Long> roomMembers) {

        if (battleResult == null || matchTypeConf == null || gameModeType == null || roomMembers == null) {
            return;
        }

        if (!ReputationScoreUtil.playerReputationScoreSwitch(player.getClientVersion64())) {
            return;
        }

        try {
            // 对局结算信誉分逻辑处理
            player.getReputationScoreMgr().battleSettlementReputationScoreProcess(battleResult, matchTypeConf,
                    gameModeType, roomMembers);
        } catch (Exception ex) {
            LOGGER.error("battleSettlementReputationScoreProcess catch exception, uid:{}, battleId:{}, ",
                    player.getUid(), battleResult.getBattleId(), ex);
        }
    }

    // 某些玩法下，如果玩家存在违规行为，需要施加惩罚，例如不给排位分
    static public boolean shouldApplyPunishmentForBadBehavior(int gameType, List<Integer> reputationBehaviorIdsList) {
        if (MiscConfArena.getInstance().getNonSettlementGameTypesOfIllegalBehavior().contains(gameType)) {
            for (int behaviorId : reputationBehaviorIdsList) {
                if (MiscConfArena.getInstance().getNonSettlementIllegalBehaviorIds().contains(behaviorId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 通过杂项配置检查当前玩法是否要跳过结算中状态
     *
     * @return
     */
    public static boolean needSkipSettlementState(int matchType) {
        MiscConf miscConf = MiscConf.getInstance();
        if (miscConf == null || miscConf.getMiscConf() == null) {
            LOGGER.error("there is no misc conf");
            return false;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("check skip settlement state, curMatchType:{} conf:{}",
                    matchType,
                    miscConf.getMiscConf().getTeamInfoShowMisConf().getSkipSettlementStateMatchTypesList());
        }
        return miscConf.getMiscConf().getTeamInfoShowMisConf().getSkipSettlementStateMatchTypesList()
                .contains(matchType);
    }
}

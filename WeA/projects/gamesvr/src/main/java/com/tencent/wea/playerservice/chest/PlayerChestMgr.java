package com.tencent.wea.playerservice.chest;

import com.tencent.condition.event.player.common.PlayerReadChestIdentityBiographyEvent;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.rank.utils.RankIdSeasonIdMapper;
import com.tencent.resourceloader.resclass.*;
import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.attr.*;
import com.tencent.wea.battleresult.GlobalBattleResult;
import com.tencent.wea.g6.irpc.proto.ds_player.DsPlayer;
import com.tencent.wea.g6.irpc.proto.ds_player.DsPlayer.ChestPlayerSaveItemsRequest;
import com.tencent.wea.g6.irpc.proto.sd_player.SdPlayerOuterClass;
import com.tencent.wea.g6.irpc.service.SdPlayerService;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.chase.ChaseIdentityPointUtil;
import com.tencent.wea.playerservice.chest.ChestIdentityBattlePerformanceDao;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.AttrChestIdentityBattlePerformanceDatas;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsChest;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.CsLetsgo;
import com.tencent.wea.protocol.common.ChaseChestMemberBaseInfoData;
import com.tencent.wea.protocol.common.ChestRoleData;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.TopRankJointInfo;
import com.tencent.wea.room.RoomUtil;
import com.tencent.wea.xlsRes.ResChestIDMastery;
import com.tencent.wea.xlsRes.ResChestMap;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResCommon.KeyValueInt32;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.ResRanking.RankingConf;
import com.tencent.wea.xlsRes.keywords.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * @program: project
 * @description: 大王Chest
 * @author: digoldzhang
 * @create: 2025-01-23 15:16
 **/

public class PlayerChestMgr extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(PlayerChestMgr.class);
    ChestGameInfo chestAttrData;
    private ChestIdentityBattlePerformanceDatas chestBattlePerformanceDatas;
    private ChestBattleMgr chestBattleMgr;

    public PlayerChestMgr(Player player) {
        super(GameModuleId.GMI_ChestMgr, player);
        chestAttrData = player.getUserAttr().getChestGameInfo();
        chestBattleMgr = new ChestBattleMgr(player, chestAttrData);
    }

    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    @Override
    public void onLogin() throws NKCheckedException {

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        onFirstCheckOpenIdentityFightPower();
        refreshChestCurrentRole(false);
    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void onMidNight() {

    }

    @Override
    public void onWeekStart() {

    }

    @Override
    public void onEveryHourStart() {
        weeklySettlementRank();
    }

    public ChestBattleMgr getChestBattleMgr() {
        return chestBattleMgr;
    }

    private void onFirstCheckOpenIdentityFightPower() {
        for (ResChestIDMastery.ChestIDMasteryUnlock chestIDMasteryUnlock : ChestIDMasteryUnlock.getInstance().dataArray) {
            int actorId = chestIDMasteryUnlock.getActorId();
            getOrCreateChestIdentityProficiencyData(actorId);
        }
        /*
        ChestIdentityProficiencyInfo attr = getAttrChestIdentityProficiencyInfo();
        boolean completed = attr.getCompleteFirstCheckOpenFightPower() == 1;
        if (completed) {
            return;
        }
        if (ChestIdentityProficiencyMiscConfigData.getInstance() == null){
            return;
        }
        ResChestIDMastery.ChestIdentityProficiencyMiscConfig config = ChestIdentityProficiencyMiscConfigData.getInstance().get(ChaseIdentityProficiencyConfigType.CIPCT_FightPowerMoreThanUnlockFightPower);
        if (config == null){
            LOGGER.error("unlock fight power err config is null");
            return;
        }
        int needFightPower = config.getValue();
        attr.setCompleteFirstCheckOpenFightPower(1);
        for (ResChestIDMastery.ChestIDMasteryUnlock chestIDMasteryUnlock : ChestIDMasteryUnlock.getInstance().dataArray) {
            int actorId = chestIDMasteryUnlock.getActorId();
            int fightPower = calculateIdentityFightPower(actorId);
            if (fightPower > needFightPower){
                ChestIdentityProficiencyData data = getOrCreateChestIdentityProficiencyData(actorId);
                data.setUnlockSpecialization(1);
            }
        }
        */
        LOGGER.debug("unlock fight power success");
    }

    /*
    private int calculateIdentityFightPower(int actorId){
        ChestIdentitySpecializationData specializationData = chestAttrData.getIdentitySpecialization()
                .getIdentityData(actorId);
        if (specializationData == null) {
            return 0;
        }
        int oldScore = (specializationData.getBattleScore() + specializationData.getPerfScore())
                * specializationData.getActivePoint() / 100;
        return oldScore;
    }
    */

    public void changChestRoleInfo(int actorId, int actorType) {
        if (actorType == ChestActorIdType.CAIT_Xingbao_VALUE) {
            // 星宝
            ResChestIDMastery.ChestIDMasteryPlayer playerConfig = ChestIDMasteryPlayerData.getInstance().get(actorId);
            if (playerConfig == null) {
                NKErrorCode.ChestInvalidActorId.throwErrorIfNotOk("invalid player actorId:{}, actorType:{}", actorId, actorType);
            }
        } else if (actorType == ChestActorIdType.CAIT_Boss_VALUE) {
            // 暗星
            ResChestIDMastery.ChestIDMasteryBoss bossConfig = ChestIDMasteryBossData.getInstance().get(actorId);
            if (bossConfig == null) {
                NKErrorCode.ChestInvalidActorId.throwErrorIfNotOk("invalid boss actorId:{}, actorType:{}", actorId, actorType);
            }
        } else {
            NKErrorCode.ChestInvalidActorId.throwErrorIfNotOk("invalid params actorId:{}, actorType:{}", actorId, actorType);
        }
        NKPair<Integer, NKPair<Long, Long>> unlockInfo = getUnLockInfo(actorId);
        if (null == unlockInfo) {
            NKErrorCode.ChestLockedActorId.throwErrorIfNotOk("locked actorId:{}, actorType:{}", actorId, actorType);
        } else {
            if (unlockInfo.getValue() != null) {
                if (DateUtils.currentTimeMillis() < unlockInfo.getValue().getKey() || DateUtils.currentTimeMillis() > unlockInfo.getValue().getValue()) {
                    NKErrorCode.ChestLockedActorId.throwErrorIfNotOk("locked actorId:{}, actorType:{}", actorId, actorType);
                }
            }
        }
        // 局內切换
        if (player.getPlayerBattleMgr().isInBattle()) {
            if (actorType != chestAttrData.getCurrentRole().getActorType()) {
                NKErrorCode.ChestInvalidActorTypeInBattle.throwErrorIfNotOk("invalid actor type actorId:{}, actorType:{}", actorId, actorType);
            }
        }
        updateChestRoleInfo(actorId, actorType);
        player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.ChestUserDataChange);
        updateDsPlayerChestInfo();
    }

    private void updateChestRoleInfo(int actorId, int actorType) {
        chestAttrData.getCurrentRole().setActorId(actorId).setActorType(actorType);
    }

    private void refreshChestCurrentRole(boolean ntf) {
        int currentActorId = chestAttrData.getCurrentRole().getActorId();
        if (currentActorId == 0) {
            return;
        }
        NKPair<Integer, NKPair<Long, Long>> unlockInfo = getUnLockInfo(currentActorId);
        boolean isValidActorId = true;
        if (null == unlockInfo) {
            isValidActorId = false;
        } else {
            if (unlockInfo.getValue() != null) {
                if (DateUtils.currentTimeMillis() < unlockInfo.getValue().getKey() || DateUtils.currentTimeMillis() > unlockInfo.getValue().getValue()) {
                    isValidActorId = false;
                }
            }
        }
        if (!isValidActorId) {
            ResChestIDMastery.ChestIdentityProficiencyMiscConfig config = ChestIdentityProficiencyMiscConfigData.getInstance().get(ChaseIdentityProficiencyConfigType.CIPCT_ChestDefaultActorID);
            int defaultActorId = 0;
            if (chestAttrData.getCurrentRole().getActorType() == ChestActorIdType.CAIT_Xingbao_VALUE) {
                // 星宝
                defaultActorId = config.getValueList(0);
            } else if (chestAttrData.getCurrentRole().getActorType() == ChestActorIdType.CAIT_Boss_VALUE){
                // 暗星
                defaultActorId = config.getValueList(1);
            }
            if (defaultActorId != 0) {
                LOGGER.debug("refreshChestCurrentRole defaultActorId:{}", defaultActorId);
                updateChestRoleInfo(defaultActorId, chestAttrData.getCurrentRole().getActorType());
                if (ntf) {
                    player.getPlayerRoomMgr().updateRoomMemberBaseInfo(RoomUtil.UpdateMemberBaseInfoSource.ChestUserDataChange);
                    updateDsPlayerChestInfo();
                }
            }
        }
    }

    public void updateDsPlayerChestInfo() {
        if (!ServerEngine.getInstance().isBusiness() && player.getOpenId().startsWith("ROBOT_PRESSTEST_")) {
            // simulator robot cannot connect ds, skip it
            return;
        }

        long battleId = player.getUserAttr().getBattleInfo().getBattleid();
        long dsaInstanceId = player.getUserAttr().getBattleInfo().getDsaInstanceID();
        if (battleId == 0 || dsaInstanceId == 0) {
            LOGGER.debug("player {} openid {} invalid battleId {} or instance {}",
                    player.getUid(), player.getOpenId(), battleId, dsaInstanceId);
            return;
        }
        try {
            CurrentExecutorUtil.runJob(() -> {
                SdPlayerOuterClass.SyncPlayerChestInfoRequest.Builder reqBuilder =
                        SdPlayerOuterClass.SyncPlayerChestInfoRequest.newBuilder()
                                .setUuid(player.getUid())
                                .setCurrentRoleInfo(chestAttrData.getCurrentRole().getCopyCsBuilder())
                                .setDsSessionId(battleId)
                                .setDsaInstId(dsaInstanceId);

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("player {} openid {} SyncPlayerChestInfo:{}", player.getUid(), player.getOpenId(), reqBuilder);
                }
                try {
                    SdPlayerService.get().irpcSyncPlayerChestInfo(reqBuilder.build());
                } catch (Exception ex) {
                    LOGGER.error("updateDsPlayerChestInfo fail", ex);
                }
                return null;
            }, "updateDsPlayerChestInfo", false);
        } catch (Exception e) {
            LOGGER.error("updateDsPlayerChestInfo exception:\n", e);
        }
    }

    /**
     * 获取MemberBaseInfo中 大王Chest玩法数据pb
     *
     * @return 大王Chest玩法数据pb
     */
    public ChaseChestMemberBaseInfoData getChestMemberBaseInfoData() {
        ChaseChestMemberBaseInfoData.Builder builder = ChaseChestMemberBaseInfoData.newBuilder();
        builder.setCurrentRoleInfo(ChestRoleData.newBuilder()
                .setActorId(chestAttrData.getCurrentRole().getActorId())
                .setActorType(chestAttrData.getCurrentRole().getActorType())
                .setEquipItemsValue(
                        player.getChestItemManager().getEquipItemsValue(chestAttrData.getCurrentRole().getActorType()))
                .setChestCoinNum(player.getBagManager().getMoneyNum(CoinType.CT_ChestCoin.getNumber()))
                .build());
        return builder.build();
    }

    public DsPlayer.GetPlayerChestDataReply.Builder irpcGetPlayerChestData(int actorType) {
        DsPlayer.GetPlayerChestDataReply.Builder reply = DsPlayer.GetPlayerChestDataReply.newBuilder();
        reply.setCurrentRoleInfo(chestAttrData.getCurrentRole().getCopySsBuilder());
        HashSet<ItemType> itemTypes = new HashSet<>();
        int chestActorType = chestAttrData.getCurrentRole().getActorType();
        if (ChestActorIdType.CAIT_Xingbao_VALUE == chestActorType) {
            itemTypes.add(ItemType.ItemType_Chase_Prop_Unlock);
        } else if (ChestActorIdType.CAIT_Boss_VALUE == chestActorType) {
            itemTypes.add(ItemType.ItemType_Chase_Boss_Unlock);
        } else {
            LOGGER.error("irpcGetPlayerChestData actorType error {} {}", getPlayer().getUid(), chestActorType);
            NKErrorCode.ChestInvalidActorTypeInBattle.throwError("invalid actorType:{}", chestActorType);
            return reply;
        }
        reply.addAllItemInfos(player.getChestItemManager().getEquipItems(chestActorType));
        ArrayList<Integer> unlockItemIDList = player.getItemManager().getItemsByItemTypeNoExpire(itemTypes);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcGetPlayerChestData unlockItemIDList {} {}", getPlayer().getUid(), unlockItemIDList);
        }
        ArrayList<Integer> unlockActorList = new ArrayList<>();
        for (var itemID : unlockItemIDList) {
            var unlockData = ChestIDMasteryUnlock.getInstance().getUnlockDataByLockItemID(itemID);
            if (null == unlockData) {
                LOGGER.error("irpcGetPlayerChestData getUnlockDataByLockItemID error {} {}", getPlayer().getUid(),
                        itemID);
                continue;
            }
            unlockActorList.add(unlockData.getActorId());
        }
        // 加上免费的
        unlockActorList.addAll(ChestIDMasteryUnlock.getInstance().getAllFreeActorId());

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcGetPlayerChestData unlockActorList {} {}", getPlayer().getUid(), unlockActorList);
        }

        List<Integer> limitFreeActorList = new ArrayList<>();
        var limitFreeActor = ChestIDMasteryLimitFree.getInstance().getCurrCfg();
        if (null != limitFreeActor) {
            limitFreeActorList = new ArrayList(limitFreeActor.getActorIdsList());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcGetPlayerChestData limitFreeActorList {} {}", getPlayer().getUid(), limitFreeActorList);
        }
        limitFreeActorList.removeAll(unlockActorList);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcGetPlayerChestData after limitFreeActorList {} {}", getPlayer().getUid(),
                    limitFreeActorList);
        }
        for (var actorID : unlockActorList) {
            DsPlayer.ChaseActorBattleData.Builder data = getChestActorBattleData(actorID, chestActorType, 1);
            if (data == null) {
                continue;
            }
            reply.addActorData(data);
        }
        for (var actorID : limitFreeActorList) {
            DsPlayer.ChaseActorBattleData.Builder data = getChestActorBattleData(actorID, chestActorType, 2);
            if (data == null) {
                continue;
            }
            reply.addActorData(data);
        }
        reply.addAllChaseInteractions(player.getChaseMgr().getAllChaseInteractions());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("irpcGetPlayerChaseData reply {} {}", getPlayer().getUid(), reply);
        }
        return reply;
    }

    private DsPlayer.ChaseActorBattleData.Builder getChestActorBattleData(int chestActorID, int chestActorType, int unlock) {
        var mappingConfig = ChaseActorCustomMappingConfigData.getInstance().get(ChaseShareFlagType.CSFT_Chest, chestActorID, chestActorType);
        if (null == mappingConfig) {
            LOGGER.error("getChestActorBattleData mappingConfig is null {},{}", chestActorID, chestActorType);
            return null;
        }
        var customConfig = ChaseCustomConfig.getInstance().get(mappingConfig.getChaseCustomID());
        if (null == customConfig) {
            LOGGER.error("getChestActorBattleData customConfig is null {},{},{}", chestActorID, chestActorType, mappingConfig.getChaseCustomID());
            return null;
        }
        DsPlayer.ChaseActorBattleData.Builder builder = DsPlayer.ChaseActorBattleData.newBuilder();
        builder.setActorId(chestActorID);
        builder.setUnlock(unlock);
        boolean result = player.getChaseMgr().getChaseActorDressItemInfo(builder, customConfig.getActorId(), customConfig.getActorType(), ChaseShareFlagType.CSFT_Chest);
        if (!result) {
            LOGGER.info("getChaseActorDressItemInfo failed :{},{}", customConfig.getActorId(), customConfig.getActorType());
        }
//        var identityData = chestAttrData.getIdentitySpecialization().getIdentityData().get(actorID);
//        if (null != identityData) {
//            builder.setIdentityBadgeId(identityData.getBadgeId());
//        }
        return builder;
    }

    /**
     * 获取角色小传配置
     *
     * @param actorId
     * @param biographyId
     * @return
     */
    private ResChestIDMastery.ChestIdentityBiographyConfig getChestIdentityBiographyConfig(int actorId,
                                                                                           int biographyId) {
        if (ChestIdentityBiographyConfigData.getInstance() == null) {
            LOGGER.error("ChestIdentityBiographyConfigData is null");
            return null;
        }
        return ChestIdentityBiographyConfigData.getInstance().get(actorId, biographyId);
    }

    /**
     * 阅读角色小传
     *
     * @param actorId
     * @param biographyId
     */
    public CsChest.ChestIdentityReadBiography_S2C_Msg.Builder readIdentityBiography(int actorId, int biographyId) {
        ResChestIDMastery.ChestIdentityBiographyConfig config = getChestIdentityBiographyConfig(actorId, biographyId);
        if (config == null) {
            LOGGER.error("readIdentityBiography config is null , actorId:{} , BiographyId: {}", actorId, biographyId);
            NKErrorCode.InvalidParams.throwError("config is null");
        }
        ChestIdentityProficiencyData data = getAttrChestIdentityProficiencyInfo().getChestIdentityProficiencyDatas().get(actorId);
        if (data == null){
            CsChest.ChestIdentityReadBiography_S2C_Msg.Builder builder = CsChest.ChestIdentityReadBiography_S2C_Msg.newBuilder();
            builder.setActorId(actorId);
            return builder;
        }
        data.getUnReadBiography().remove(biographyId);
        if (data.getUnlockBiography().contains(biographyId)){
            PlayerReadChestIdentityBiographyEvent event = new PlayerReadChestIdentityBiographyEvent(player.getConditionMgr());
            event.setActorId(actorId);
            event.setBiographyId(config.getId());
            player.getPlayerEventManager().dispatch(event);
        }
        CsChest.ChestIdentityReadBiography_S2C_Msg.Builder builder = CsChest.ChestIdentityReadBiography_S2C_Msg.newBuilder();
        builder.setActorId(actorId);
        builder.addAllUnReadBiography(data.getUnReadBiographyList());
        return builder;
    }

    public List<CsChest.ChestIdentityProficiencyInfo.Builder> getIdentityProficiency(List<Integer> actorIds) {
        if (!player.getPlayerBattleMgr().isInBattle()) {
            refreshChestCurrentRole(true);
        }
        List<CsChest.ChestIdentityProficiencyInfo.Builder> list = new ArrayList<>();
        ChestIdentityProficiencyInfo attr = getAttrChestIdentityProficiencyInfo();
        for (Integer actorId : actorIds) {
            MapAttrObj<Integer, ChestIdentityProficiencyData> datas = attr.getChestIdentityProficiencyDatas();
            ChestIdentityProficiencyData data = datas.get(actorId);
            CsChest.ChestIdentityProficiencyInfo.Builder builder = buildIdentityProficiencyInfo(actorId, data);
            list.add(builder);
        }
        return list;
    }

    private CsChest.ChestIdentityProficiencyInfo.Builder buildIdentityProficiencyInfo(int actorId,
                                                                                      ChestIdentityProficiencyData data) {
        CsChest.ChestIdentityProficiencyInfo.Builder builder = CsChest.ChestIdentityProficiencyInfo.newBuilder();
        if (data == null) {
            builder.setActorId(actorId);
            return builder;
        }
        builder.setActorId(data.getIdentityId());
        builder.setProficiency(data.getProficiency());
        builder.addAllUnlockBiography(data.getUnlockBiographyList());
        builder.addAllClimeProficiencyRewards(data.getClaimedProgressRewardList());
        builder.setLastPlayTime(data.getLastPlayTime());
        builder.setUnlockSpecialization(data.getUnlockSpecialization());
        builder.addAllUnReadBiography(data.getUnReadBiographyList());
        NKPair<Integer, NKPair<Long, Long>> unlockInfo = getUnLockInfo(actorId);
        if (unlockInfo != null) {
            builder.setUnlockActor(unlockInfo.getKey());
            if (unlockInfo.getValue() != null){
                builder.setTemporaryUnlockEndTimeMs(unlockInfo.getValue().getValue());
            }
        }
        return builder;
    }

    /**
     * 领取熟练度奖励
     */
    public void drawIdentityProficiencyReward(int actorId, Set<Integer> professionIds) {
        ChestIdentityProficiencyData data = getOrCreateChestIdentityProficiencyData(actorId);
        HashMap<Integer, Integer> rewards = new HashMap<>();
        boolean unlockFightPower = false;
        int subReason = 0;
        NKPair<Integer, NKPair<Long, Long>> unlockInfo = getUnLockInfo(actorId);
        if (unlockInfo == null){
            NKErrorCode.InvalidParams.throwError("not unlock :{}", actorId);
        }
        //只有拥有永久角色才能领取奖励
        if (unlockInfo.getKey() != 1){
            NKErrorCode.InvalidParams.throwError("not unlock :{}", actorId);
        }
        for (Integer professionId : professionIds) {
            ResChestIDMastery.ChestIdentityProficiencyConfig config = getChestIdentityProficiencyConfig(actorId,
                    professionId);
            if (config == null) {
                LOGGER.error("drawIdentityProficiencyReward config is null");
                NKErrorCode.InvalidParams.throwError("config is null :{}", professionId);
            }
            if (data.getClaimedProgressReward().contains(professionId)) {
                LOGGER.error("drawIdentityProficiencyReward has been claimed {}", professionId);
                NKErrorCode.InvalidParams.throwError("has been claimed :{}", professionId);
            }
            if (config.getNeedUnlockBiography() != 0){
                boolean unlock =  data.getUnlockBiography().contains(config.getNeedUnlockBiography());
                if (!unlock){
                    LOGGER.error("drawIdentityProficiencyReward unlock  Proficiency {} {} ", professionId,config.getNeedUnlockBiography());
                    NKErrorCode.InvalidParams.throwError("has been claimed :{}", professionId);
                }
            }
            for (ResCommon.Item item : config.getRewardItemList()) {
                rewards.compute(item.getItemId(), (k, v) -> v == null ? item.getItemNum() : v + item.getItemNum());
            }
            if (config.getUnLockFightPower() == 1) {
                unlockFightPower = true;
            }
            subReason = config.getId();
        }
        data.getClaimedProgressReward().addAll(professionIds);
        ChangedItems changedItems = new ChangedItems(rewards,
                ItemChangeReason.ICR_DrawChestIdentityReward.getNumber(), "");
        NKPair<NKErrorCode, ItemChangeDetails> addItemRtn = player.getBagManager().AddItems2(changedItems,subReason);
        if (addItemRtn != null) {
            if (addItemRtn.getKey() != NKErrorCode.OK) {
                LOGGER.error("drawIdentityProficiencyReward AddItems2 failed :{}", addItemRtn.getKey().getValue());
            }
        }
        if (unlockFightPower) {
            data.setUnlockSpecialization(1);
            //战斗力解锁后需要刷新一下排行榜
            //refreshIdentityMasteryRank(actorId);
        }
    }

    /**
     * 获取单个角色的熟练度attr
     *
     * @param actorId
     * @return
     */
    private ChestIdentityProficiencyData getOrCreateChestIdentityProficiencyData(int actorId) {
        ChestIdentityProficiencyInfo attr = getAttrChestIdentityProficiencyInfo();
        MapAttrObj<Integer, ChestIdentityProficiencyData> datas = attr.getChestIdentityProficiencyDatas();
        ChestIdentityProficiencyData data = datas.get(actorId);
        if (data == null) {
            data = new ChestIdentityProficiencyData();
            datas.put(actorId, data);
        }
        return data;
    }

    /**
     * 获取角色熟练度节点配置
     *
     * @param actorId
     * @param professionId
     * @return
     */
    private ResChestIDMastery.ChestIdentityProficiencyConfig getChestIdentityProficiencyConfig(int actorId,
                                                                                               int professionId) {
        if (ChestIdentityProficiencyConfigData.getInstance() == null) {
            LOGGER.error("ChestIdentityProficiencyConfigData is null");
            return null;
        }
        return ChestIdentityProficiencyConfigData.getInstance().get(actorId, professionId);
    }

    private NKPair<Integer,NKPair<Long,Long>> getUnLockInfo(int actorId){
        Set<Integer> freeIds = ChestIDMasteryUnlock.getInstance().getAllFreeActorId();
        if (freeIds.contains(actorId)) {
            //免费的
            return new NKPair<>(1, null);
        }
        ResChestIDMastery.ChestIDMasteryUnlock config = ChestIDMasteryUnlock.getInstance().get(actorId);
        if (config == null) {
            LOGGER.warn("ChestIDMasteryUnlock not has actorId:{}", actorId);
            return null;
        }
        int unlockItemId = config.getUnlockItemId();
        HashMap<Long, Item> itemMap = player.getBagManager().getItemManager().getItemsByItemId(unlockItemId);
        long itemExpiresMs = 0L;
        for (Item value : itemMap.values()) {
            //拥有永久身份
            if (value.getExpireMs() == 0){
                return new NKPair<>(1, null);
            }
            if (value.getExpireMs() > 0 ){
                itemExpiresMs = Math.max(itemExpiresMs,value.getExpireMs());
            }
        }
        if (itemExpiresMs > 0){
            return new NKPair<>(2, new NKPair<>(DateUtils.currentTimeMillis(), itemExpiresMs));
        }

        var limitFreeActor = ChestIDMasteryLimitFree.getInstance().getCurrCfg();
        if (null != limitFreeActor) {
            if (limitFreeActor.getActorIdsList().contains(actorId)) {
                return new NKPair<>(2, new NKPair<>(limitFreeActor.getBeginTime().getSeconds()*1000, limitFreeActor.getEndTime().getSeconds()*1000));
            }
        }
        return null;
    }

    /**
     * 获取熟练度属性对象
     *
     * @return
     */
    private ChestIdentityProficiencyInfo getAttrChestIdentityProficiencyInfo() {
        return player.getUserAttr().getChestGameInfo().getChestIdentityProficiencyInfo();
    }

    private Set<Integer> getNeedAddProficiencyMatchType() {
        Set<Integer> needMatchType = new HashSet<>();
        ChestIdentityProficiencyMiscConfigData miscConf = ChestIdentityProficiencyMiscConfigData.getInstance();
        if (miscConf == null) {
            LOGGER.error("ChestIdentityProficiencyMiscConfigData is null");
            return needMatchType;
        }
        ResChestIDMastery.ChestIdentityProficiencyMiscConfig config = miscConf.get(
                ChaseIdentityProficiencyConfigType.CIPCT_NeedAddProficiencyMatchType);
        if (config == null) {
            LOGGER.error("ChaseIdentityProficiencyMiscConfig is null");
            return needMatchType;
        }
        needMatchType.addAll(config.getValueListList());
        return needMatchType;
    }

    /**
     * 从战局信息更新熟练度数据
     *
     * @param isFromChaseSettlementInteraction
     * @param battleResult
     */
    public void updateChestIdentityProficiency(boolean isFromChaseSettlementInteraction, GlobalBattleResult battleResult) {
        int actorId = ChaseIdentityPointUtil.translateBattleActorToIdentityId(battleResult.getChaseActorId(),
                battleResult.getChaseActorType());
        if (actorId <= 0) {
            LOGGER.error("actorId is invalid, uid:{}, battleId:{}, matchType:{}", player.getUid(),
                    battleResult.getBattleId(), battleResult.getMatchType());
            return;
        }
        LOGGER.debug("chest battle settlement, uid:{}, battleId:{}, matchType:{}", player.getUid(),
                battleResult.getBattleId(), battleResult.getMatchType());
        ResChestIDMastery.ChestIDMasteryUnlock actorUnlockConfig = ChestIDMasteryUnlock.getInstance().get(actorId);
        if (actorUnlockConfig == null){
            LOGGER.error("chest battle settlement actorId :{} actoryType:{} cast : {}",battleResult.getChaseActorId(),battleResult.getChaseActorType(),actorId);
            return;
        }
        if (!battleResult.isBattleFinished()) {
            LOGGER.info("skip identity settlement because battle not finished, uid:{}, battleId:{}",
                    player.getUid(), battleResult.getBattleId());
            return;
        }
        //refreshDailyAddProficiencyMax();

        int matchType = battleResult.getMatchType();
        Set<Integer> needCheckMatchType = getNeedAddProficiencyMatchType();
        if (!needCheckMatchType.contains(matchType)) {
            return;
        }
        if (!PropertyFileReader.getRealTimeBooleanItem("chest_identity_proficiency_switch", true)) {
            return;
        }
        if (!battleResult.isBattleFinished()) {
            LOGGER.info("skip identity settlement because battle not finished, uid:{}, battleId:{}",
                    player.getUid(), battleResult.getBattleId());
            return;
        }
        /*
        int oldProficiency = getActorProficiency(actorId);
        int addProficiency = calculateAddIdentityProficiency(battleResult);
        ChestIdentityProficiencyInfo data = getAttrChestIdentityProficiencyInfo();
        int current =  data.getDailyAddProficiency();
        int dailyAddMax = getDailyMaxAddProficiency();
        if (dailyAddMax == 0){
            LOGGER.error("player add proficiency dailyMaxAdd is null config");
        }else {
            if (current + addProficiency > dailyAddMax) {
                LOGGER.error("player add proficiency more than max :{}", current);
                return;
            }
            data.addDailyAddProficiency(addProficiency);
        }

        addIdentityProficiency(actorId,addProficiency);
        int newProficiency = getActorProficiency(actorId);
        updateLastPlayTime(actorId,DateUtils.currentTimeMillis());
        LOGGER.debug("player :{}  addProficiency:{}", player.getUid(), addProficiency);
        LOGGER.info("uid:{}, battleId:{}, matchType:{}, oldProficiency:{}, newProficiency:{}, addProficiency:{}",
                player.getUid(), battleResult.getBattleId(), battleResult.getMatchType(), oldProficiency,
                newProficiency,
                addProficiency);
        */
        if (isFromChaseSettlementInteraction) {
            LOGGER.info("uid:{}, battleId:{}, matchType:{}",
                    player.getUid(), battleResult.getBattleId(), battleResult.getMatchType());
        }
        handlerBattleResultToBattlePerformance(battleResult,actorId);
    }

    private void refreshDailyAddProficiencyMax(){
        ChestIdentityProficiencyInfo data = getAttrChestIdentityProficiencyInfo();
        long lastRefreshTime = data.getRefreshTimeMs();
        long now = DateUtils.currentTimeMillis();
        if (DateUtils.isSameDay(lastRefreshTime,now)){
            return;
        }
        data.setRefreshTimeMs(now);
        data.setDailyAddProficiency(0);
    }

    /**
     * 更新熟练度
     *
     * @param actorId
     * @param addProficiency
     */
    public void addIdentityProficiency(int actorId, int addProficiency) {
        ChestIdentityProficiencyData data = getOrCreateChestIdentityProficiencyData(actorId);
        data.addProficiency(addProficiency);
    }

    /**
     * 获取单个角色的熟练度
     *
     * @param actorId
     * @return
     */
    public int getActorProficiency(int actorId) {
        ChestIdentityProficiencyInfo attr = getAttrChestIdentityProficiencyInfo();
        MapAttrObj<Integer, ChestIdentityProficiencyData> datas = attr.getChestIdentityProficiencyDatas();
        ChestIdentityProficiencyData data = datas.get(actorId);
        if (data == null) {
            return 0;
        }
        return data.getProficiency();
    }

    public void updateLastPlayTime(int actorId, long lastPlayTime) {
        ChestIdentityProficiencyData data = getOrCreateChestIdentityProficiencyData(actorId);
        data.setLastPlayTime(lastPlayTime);
    }

    /**
     *
     * @param globalBattleResult
     * @return
     */
    private int calculateAddIdentityProficiency(GlobalBattleResult globalBattleResult) {
        return 1;
    }

    /**
     * 单局固定增加的熟练度
     *
     * @return
     */
    private int getDailyMaxAddProficiency() {
        ChestIdentityProficiencyMiscConfigData miscConf = ChestIdentityProficiencyMiscConfigData.getInstance();
        if (miscConf == null) {
            LOGGER.error("ChestIdentityProficiencyMiscConfigData is null");
            return 0;
        }
        ResChestIDMastery.ChestIdentityProficiencyMiscConfig config = miscConf.get(
                ChaseIdentityProficiencyConfigType.CIPCT_DailyAddProficiencyMax);
        if (config == null) {
            LOGGER.error("ChestIdentityProficiencyMiscConfig is null");
            return 0;
        }
        return config.getValue();
    }

    public void handlerBattleResultToBattlePerformance(GlobalBattleResult globalBattleResult,int actorId) {
        NKPair<Map<Integer, Long>, Map<Integer, Long>> rtn = collectionBattleResultToBattlePerformance(globalBattleResult,actorId);
        Map<Integer,Long> sumMap = rtn.getKey();
        Map<Integer,Long> replaceMaxMap = rtn.getValue();
        LOGGER.debug("addBattleResultToBattlePerformance actorId:{} sumMap:{} maxMap:{}" , actorId,sumMap,replaceMaxMap);
        //异步更新  这里会读写数据库
        try {
            CurrentExecutorUtil.runJob(() -> {
                try {
                    addBattlePerformance(actorId,sumMap,replaceMaxMap);
                } catch (Exception e) {
                    LOGGER.error("handlerBattleResultToBattlePerformance error", e);
                }
                return NKErrorCode.OK;
            }, "refresh_racing_cost_time", true);
        } catch (NKCheckedException e) {
            LOGGER.error("handlerBattleResultToBattlePerformance error", e);
        }
    }

    /**
     * 转换 battle -> 自身数据的记录
     * @param globalBattleResult
     * @param actorId
     */
    public NKPair<Map<Integer,Long>,Map<Integer,Long>> collectionBattleResultToBattlePerformance(GlobalBattleResult globalBattleResult,int actorId){
        int matchType =  globalBattleResult.getMatchType();
        int teamRank = globalBattleResult.getChaseCampResult() + 1; // 1胜2负3平
        int battleScore =  globalBattleResult.getChaseEventValue( BattleEventType.BET_LEVEL_CHASE_COMPREHENSIVE_SCORE);
        Map<Integer,Long> sumMap = new HashMap<>();
        Map<Integer,Long> replaceMaxMap = new HashMap<>();
        ChestIdentityBattlePerformanceConfigData data = ChestIdentityBattlePerformanceConfigData.getInstance();
        if (data == null){
            LOGGER.error("collectionBattleResultToBattlePerformance data is null");
            return new NKPair<>(sumMap,replaceMaxMap);
        }
        for (ResChestIDMastery.ChestIdentityBattlePerformanceConfig config : data.dataArray) {
            if (!config.getMatchTypeIdsList().contains(matchType)) {
                continue;
            }
            if (config.getPerformanceType() == ChaseIdentityBattlePerformanceType.CIBPT_WIN_COUNT){
                if (teamRank == 1){
                    sumMap.put(config.getId(),1L);
                }
                continue;
            }
            if (config.getPerformanceType() == ChaseIdentityBattlePerformanceType.BIBPT_Total_Battle_Count){
                sumMap.put(config.getId(),1L);
                continue;
            }
            //金牌统计
            if (config.getPerformanceType() == ChaseIdentityBattlePerformanceType.BIBPT_Gold){
                if (battleScore >= getMinimumScoreForGoldMedalSettlement()){
                    sumMap.put(config.getId(),1L);
                }
            }
            BattleEventType battleEventType = config.getBattleEventType();
            if (battleEventType == BattleEventType.BET_Unknown){
                continue;
            }
            long value = globalBattleResult.getChaseEventValue(battleEventType);
            if (value == 0){
                if (globalBattleResult.haveBattleEvent(battleEventType)) {
                    value = globalBattleResult.getBattleEventValue(battleEventType);
                }
            }
            if (config.getCollectionType() == ChaseIdentityBattlePerformanceCollectionType.CIBPCT_Cumulative){
                if (config.getParam()> 0){
                    if (value > config.getParam()){
                        sumMap.put(config.getId(),1L);
                    }
                }else{
                    sumMap.put(config.getId(),value);
                }
            }
            if (config.getCollectionType() == ChaseIdentityBattlePerformanceCollectionType.CIBPCT_Max){
                replaceMaxMap.put(config.getId(),value);
            }
        }
        return new NKPair<>(sumMap, replaceMaxMap);
    }

    /**
     * 添加对局表现数据
     *
     * @param actorId
     * @param sumMap
     */
    public void addBattlePerformance(int actorId, Map<Integer, Long> sumMap,Map<Integer,Long> replaceMaxMap) {
        loadBattlePerformance();
        if (chestBattlePerformanceDatas == null) {
            LOGGER.error("addBattlePerformance battlePerformanceDatas is null");
            return;
        }
        ChestIdentityBattlePerformanceData oneIdentityAttr = chestBattlePerformanceDatas.getChestIdentityBattlePerformanceData()
                .get(actorId);
        if (oneIdentityAttr == null) {
            oneIdentityAttr = new ChestIdentityBattlePerformanceData();
            chestBattlePerformanceDatas.getChestIdentityBattlePerformanceData().put(actorId, oneIdentityAttr);
        }
        for (Map.Entry<Integer, Long> entry : sumMap.entrySet()) {
            int type = entry.getKey();
            long addValue = entry.getValue();
            ChestIdentityBattlePerformance onePerformanceAttr = oneIdentityAttr.getChestIdentityBattlePerformance().get(type);
            if (onePerformanceAttr == null) {
                onePerformanceAttr = new ChestIdentityBattlePerformance();
                oneIdentityAttr.getChestIdentityBattlePerformance().put(type, onePerformanceAttr);
            }
            onePerformanceAttr.setValue(onePerformanceAttr.getValue() + addValue);
        }
        for (Map.Entry<Integer, Long> entry : replaceMaxMap.entrySet()) {
            int type = entry.getKey();
            long addValue = entry.getValue();
            ChestIdentityBattlePerformance onePerformanceAttr = oneIdentityAttr.getChestIdentityBattlePerformance().get(type);
            if (onePerformanceAttr == null) {
                onePerformanceAttr = new ChestIdentityBattlePerformance();
                oneIdentityAttr.getChestIdentityBattlePerformance().put(type, onePerformanceAttr);
            }

            onePerformanceAttr.setValue(Math.max(onePerformanceAttr.getValue(),addValue));
        }
        AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas.Builder updateRecord = chestBattlePerformanceDatas.getCopyDbBuilder();
        ChestIdentityBattlePerformanceDao.updateChestIdentityBattlePerformance(player.getUid(), updateRecord.build());
    }

    /**
     * 大王结算金牌最低分
     *
     * @return
     */
    public long getMinimumScoreForGoldMedalSettlement() {
        ResMisc.MiscConf relationConf = MiscConf.getInstance().getMiscConf();
        return relationConf.getChaseExtConf().getChaseMinimumScoreForGoldMedalSettlement();
    }

    public List<CsChest.ChestIdentityBattlePerformanceInfo.Builder> getIdentityBattlePerformance(
            List<Integer> actorIds) {
        loadBattlePerformance();
        if (chestBattlePerformanceDatas == null) {
            LOGGER.error("getIdentityBattlePerformance battlePerformanceDatas is null");
            return new ArrayList<>();
        }
        List<CsChest.ChestIdentityBattlePerformanceInfo.Builder> list = new ArrayList<>();
        ChestIdentityBattlePerformanceConfigData dataConfig = ChestIdentityBattlePerformanceConfigData.getInstance();
        if (dataConfig == null){
            return new ArrayList<>();
        }

        for (Integer actorId : actorIds) {
            int chestActorType = actorId/10000;
            ChestIdentityBattlePerformanceData oneIdentityAttr = chestBattlePerformanceDatas.getChestIdentityBattlePerformanceData().get(actorId);
            CsChest.ChestIdentityBattlePerformanceInfo.Builder oneActor = CsChest.ChestIdentityBattlePerformanceInfo.newBuilder();
            oneActor.setActorId(actorId);
            for (ResChestIDMastery.ChestIdentityBattlePerformanceConfig chestIdentityBattlePerformanceConfig : dataConfig.dataArray) {
                if (!chestIdentityBattlePerformanceConfig.getActorTypeList().contains(chestActorType)){
                    continue;
                }
                if (chestIdentityBattlePerformanceConfig.getHide() == 1){
                    continue;
                }
                //记录类型的直接赋值
                CsChest.ChestIdentityBattlePerformance.Builder oneRecord = CsChest.ChestIdentityBattlePerformance.newBuilder();
                oneRecord.setType(chestIdentityBattlePerformanceConfig.getId());
                if (oneIdentityAttr != null){
                    ChestIdentityBattlePerformance onePerformanceAttr = oneIdentityAttr.getChestIdentityBattlePerformance().get(chestIdentityBattlePerformanceConfig.getId());
                    if (onePerformanceAttr != null) {
                        oneRecord.setValue(onePerformanceAttr.getValue());
                    }
                }
                oneActor.addBattlePerformance(oneRecord);
            }
            list.add(oneActor);
        }
        return list;
    }

    public void chestBattleSettlement(boolean isFromChaseSettlementInteraction, GlobalBattleResult battleResult) {
        try {
            // 更新身份熟练度
            updateChestIdentityProficiency(isFromChaseSettlementInteraction, battleResult);
        } catch (Exception e) {
            LOGGER.error("updateChestIdentityProficiency error, ", e);
        }
    }

    private void loadBattlePerformance() {
        if (chestBattlePerformanceDatas != null) {
            //已经加载过了
            return;
        }
        NKPair<TcaplusErrorCode, AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas> data = ChestIdentityBattlePerformanceDao.getChestIdentityBattlePerformance(
                player.getUid());
        //记录不存在  插入一下
        if (data.getKey() == TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST) {
            AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas.Builder builder = AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas.newBuilder();
            ChestIdentityBattlePerformanceDao.insertChestIdentityBattlePerformance(player.getUid(), builder.build());
        } else {
            if (data.getValue() == null) {
                //查询失败
                LOGGER.error("addBattlePerformance data is null");
                return;
            }
        }
        NKPair<TcaplusErrorCode, AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas> loadData = ChestIdentityBattlePerformanceDao.getChestIdentityBattlePerformance(
                player.getUid());
        if (loadData.getValue() == null) {
            LOGGER.error("addBattlePerformance data is null");
            return;
        }
        AttrChestIdentityBattlePerformanceDatas.proto_ChestIdentityBattlePerformanceDatas records = loadData.getValue();
        chestBattlePerformanceDatas = new ChestIdentityBattlePerformanceDatas();
        chestBattlePerformanceDatas.mergeFromDto(records);
    }

    /**
     * chest玩法查询玩家能否满足进入某个地图的消耗
     * @param req 请求
     * @return 错误码
     */
    public NKErrorCode handleChestCheckMapEntryCost(SsGamesvr.RpcChestCheckMapEntryCostReq.Builder req) {
        int matchTypeId = req.getMatchTypeId();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("handleChestCheckMapEntryCost matchTypeId:{}", matchTypeId);
        }

        Optional<ResChestMap.ChestMapSelectData> mapCfg = ChestMapSelectConfig.getInstance().getByMatchTypeId(req.getMatchTypeId());
        if (mapCfg.isEmpty()) {
            LOGGER.error("uid {} handleChestCheckMapEntryCost matchTypeId:{} not found",
                    player.getUid(), matchTypeId);
            return NKErrorCode.RoomMatchChestMapIdInvalid;
        }

        if (req.getActorIdType() == ChestActorIdType.CAIT_Boss_VALUE) {
            // 检查暗星入场消耗
            for (ResCommon.Item item : mapCfg.get().getEntryCostInfo().getBossActorTypeEntryCostItemsList()) {
                long playerItemCnt = player.getBagManager().getItemNumByItemId(item.getItemId());
                if (playerItemCnt < item.getItemNum()) {
                    LOGGER.error("uid {} handleChestCheckMapEntryCost matchTypeId:{} boss entry cost has {} need {} not enough",
                            player.getUid(), matchTypeId, playerItemCnt, item.getItemNum());
                    return NKErrorCode.RoomMatchChestMatchTypeBossEntryCostNotEnough;
                }
            }
        }

        return NKErrorCode.OK;
    }

    public void clearAllBagItem() {
        for (ChestItem chestItem : player.getUserAttr().getChestGameInfo().getChestItemDb().getChestItems().values()) {
            // todo Tlog Flow
        }
        player.getUserAttr().getChestGameInfo().getChestItemDb().clear();
    }

    // 每周排行榜结算
    private void weeklySettlementRank() {
        long currentTimeMs = DateUtils.currentTimeMillis();
        long weeklyRewardTime = ChestMiscConf.getInstance().getChestTitleRewardTime();
        if (player.getUserAttr().getChestGameInfo().getChestRankInfo().getRankRewardTime() >= weeklyRewardTime) {
            LOGGER.debug("weeklySettlementRank already reward, uid:{} rankRewardTime:{} weeklyRewardTime:{}",
                    player.getUid(), player.getUserAttr().getChestGameInfo().getChestRankInfo().getRankRewardTime(),
                    weeklyRewardTime);
            return;
        }
        try {
            List<TopRankJointInfo> rankInfo = null;
            try {
                var result = player.getRankManager().getBatchSnapshot(RankRule.RR_PlayMode_Chest, currentTimeMs / 1000);
                if (result.getValue().hasError()) {
                    LOGGER.info("player openid:{} uid:{}  getBatchSnapshot failed, time:{} error:{}",
                            player.getOpenId(), player.getUid(), currentTimeMs, result.getValue());
                    return;
                }
                rankInfo = result.getKey();
            } catch (Exception e) {
                LOGGER.error("player openid:{} uid:{},  getBatchSnapshot Exception", player.getOpenId(),
                        player.getUid(),
                        e);
                return;
            }

            if (rankInfo == null || rankInfo.isEmpty()) {
                LOGGER.debug("weeklySettlementRank rankInfo is empty, uid:{}", player.getUid());
                return;
            }
            settlementChestRank(rankInfo, currentTimeMs);
        } finally {
            player.getUserAttr().getChestGameInfo().getChestRankInfo().setRankRewardTime(weeklyRewardTime);
        }
    }

    private void settlementChestRank(List<TopRankJointInfo> rankInfo, long currentTimeMs) {
        for (TopRankJointInfo info : rankInfo) {
            RankingConf rankConf = RankingConfData.getInstance().get(info.getRankId());
            if (rankConf == null) {
                LOGGER.error("rankConf not found, rankId:{}", info.getRankId());
                continue;
            }
            var rankSeasonInfo = RankIdSeasonIdMapper.ofLast(info.getRankId(), currentTimeMs / 1000);
            if (rankSeasonInfo == null) {
                LOGGER.error("rankSeasonInfo not found, rankId:{}", info.getRankId());
                continue;
            }
            ChaseSideType actorType = ChaseSideType.forNumber(rankConf.getDescId());
            if (actorType == null) {
                LOGGER.error("actorType not found, rankId:{}", info.getRankId());
                continue;
            }
            List<Integer> globalRewardConf = null; // 总榜排行奖励
            List<Integer> geoRewardConf = null; // 地区榜排行奖励
            if (actorType == ChaseSideType.CST_DARKSTAR) {
                globalRewardConf = ChestMiscConf.getInstance().getChestMiscConfig()
                        .getChestDarkStarWeeklyRankTitleList();
                geoRewardConf = ChestMiscConf.getInstance().getChestMiscConfig()
                        .getChestDarkStarWeeklyLocalRankTitleList();
            } else {
                globalRewardConf = ChestMiscConf.getInstance().getChestMiscConfig()
                        .getChestXingBaoWeeklyRankTitleList();
                geoRewardConf = ChestMiscConf.getInstance().getChestMiscConfig()
                        .getChestXingBaoWeeklyLocalRankTitleList();
            }
            if (globalRewardConf.size() < 3 || geoRewardConf.size() < 3) {
                LOGGER.error("rewardConf size error, globalRewardConf:{} geoRewardConf:{}",
                        globalRewardConf, geoRewardConf);
                return;
            }
            int globalRank = globalRewardConf.get(0);
            int globalTitleItemId = globalRewardConf.get(1);
            int globalRewardMailId = globalRewardConf.get(2);

            int geoRank = geoRewardConf.get(0);
            int geoTitleItemId = geoRewardConf.get(1);
            int geoRewardMailId = geoRewardConf.get(2);

            if (info.getGlobalRankNo() <= globalRank) {
                sendWeeklyRankTitleRewardMail(player.getUid(), globalTitleItemId, globalRewardMailId);
                LOGGER.debug("weeklySettlementRank globalRank:{} globalTitleItemId:{} globalRewardMailId:{}",
                        info.getGlobalRankNo(), globalTitleItemId, globalRewardMailId);
            }

            for (var geoRankInfo : info.getGeoRankNoList()) {
                if (geoRankInfo.getValue() <= geoRank) {
                    sendWeeklyRankTitleRewardMail(player.getUid(), geoTitleItemId, geoRewardMailId);
                    LOGGER.debug("weeklySettlementRank geoRank:{} geoTitleItemId:{} geoRewardMailId:{}",
                            geoRankInfo.getValue(), geoTitleItemId, geoRewardMailId);
                    break;
                }
            }
        }
    }

    private static void sendWeeklyRankTitleRewardMail(long uid, int rewardId, int mailTempId) {
        MailAttachmentList.Builder mailReward = MailAttachmentList.newBuilder();
        mailReward.addList(MailAttachment.newBuilder().setItemIfo(
                ItemInfo.newBuilder().setItemId(rewardId).setItemNum(1).setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE)
                        .setExpireTimeMs(ChestMiscConf.getInstance()
                                .getChestTitleRewardExpireTime(DateUtils.currentTimeMillis()))));
        long mailId = MailInteraction.sendTemplateMail(uid, mailTempId, mailReward,
                MailInteraction.TlogSendReason.ChestWeeklyRankSettlement);
        if (mailId > 0) {
            LOGGER.debug("sendTemplateMail succeeded,  uid:{} rewardId:{} mailTempId:{} mailId:{}",
                    uid, rewardId, mailTempId, mailId);
        } else {
            LOGGER.error("sendTemplateMail err,  uid:{} rewardId:{} mailTempId:{} mailId:{}",
                    uid, rewardId, mailTempId, mailId);
        }
    }
}

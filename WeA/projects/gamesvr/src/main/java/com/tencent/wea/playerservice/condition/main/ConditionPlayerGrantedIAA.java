package com.tencent.wea.playerservice.condition.main;

import com.google.common.collect.Lists;
import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.condition.PlayerConditionProgress;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.PlayerGetItemEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResCondition.ResSubConditionInfo;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.SubConditionType;
import java.util.List;

public class ConditionPlayerGrantedIAA extends BasePlayerCondition {

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerGrantedIAA_VALUE;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerIAAOp)
    private void onEvent(PlayerGetItemEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    public long getInitConditionProgress(Player player, PlayerConditionProgress conditionProgress) {
        return getIfGrantedIaa(player, conditionProgress.getSubConditionList());
    }

    @Override
    public boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        return progress.setValue(getIfGrantedIaa(event.getPlayer(), progress.getSubConditionList()));
    }

    private long getIfGrantedIaa(Player player, List<ResSubConditionInfo> conditionProgress) {
        if (player.getIaaManager().isGmAllow()) {
            return 1;
        }

        List<Integer> allowTypes = Lists.newArrayList();

        for (var sub : conditionProgress) {
            if (sub.getType() == SubConditionType.SCT_CloudGameType_VALUE) {
                for (long value : sub.getValueList()) {
                    allowTypes.add((int) value);
                }

                return 0;
            }
        }

        return player.getIaaManager().isGranted(allowTypes) ? 1 : 0;
    }
}

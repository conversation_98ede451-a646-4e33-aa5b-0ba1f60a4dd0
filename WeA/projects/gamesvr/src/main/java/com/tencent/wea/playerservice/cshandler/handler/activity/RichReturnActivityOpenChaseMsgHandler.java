package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.activity.implement.RichReturnActivity;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.cshandler.GamesvrPbMsgHandlerFactory;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.CsHead;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.MsgTypes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class RichReturnActivityOpenChaseMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(RichReturnActivityOpenChaseMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsActivity.RichReturnActivityOpenChase_C2S_Msg reqMsg = (CsActivity.RichReturnActivityOpenChase_C2S_Msg)request;
        //NKErrorCode.UnknownError.throwError("RichReturnActivityOpenChaseMsgHandler not implemented");
        int activityId = reqMsg.getActivityId();
        BaseActivity activity = player.getActivityManager().getRunningActivity(activityId);
        if (activity == null){
            NKErrorCode.InvalidParams.throwError("activity not exit :{}",activityId);
        }
        if (!(activity instanceof RichReturnActivity)){
            NKErrorCode.InvalidParams.throwError("activity type error :{}",activityId);
        }
        RichReturnActivity richReturnActivity = (RichReturnActivity) activity;
        richReturnActivity.lottery();
        CsActivity.RichReturnActivityOpenChase_S2C_Msg.Builder respMsg = CsActivity.RichReturnActivityOpenChase_S2C_Msg.newBuilder();
        return respMsg;
    }
}

package com.tencent.wea.playerservice.outputcontrol;

import com.tencent.resourceloader.resclass.MallCommodityConf;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.wea.outputcontrol.BaseOutputInfo;
import com.tencent.wea.outputcontrol.BaseOutputModule;
import com.tencent.wea.outputcontrol.OutputModuleCtx;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.tcaplus.TcaplusDb.GeneralOutputModuleKey;
import com.tencent.wea.tcaplus.TcaplusDb.GeneralOutputModuleKey.Builder;
import com.tencent.wea.tlog.flow.TlogOutputControlCheckFlow;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.keywords.GeneralOutputModuleType;
import com.tencent.wea.xlsRes.keywords.GeneralOutputPeriodType;
import com.tencent.wea.xlsRes.keywords.MallCommodityLimit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class PlayerMallOutputModule extends BaseOutputModule {

    private static final Logger LOGGER = LogManager.getLogger(PlayerMallOutputModule.class);


    protected PlayerOutputInfo info;
    private final Player player;

    protected PlayerMallOutputModule(PlayerOutputInfo info, Player player) {
        super(GeneralOutputModuleType.GOMT_Player_Mall.getNumber());
        this.info = info;
        this.player = player;
    }

    @Override
    protected BaseOutputInfo getBaseInfo() {
        return info;
    }

    @Override
    protected MonitorId getMonitorId() {
        return MonitorId.attr_output_mall;
    }
    @Override
    protected MonitorId getSpecialMonitorId() {
        return MonitorId.attr_output_s_mall;
    }

    @Override
    public TlogOutputControlCheckFlow hireCheckFlow(OutputModuleCtx ctx) {
        return TlogOutputControlCheckFlow.hire(info.getPlayer());
    }

    @Override
    protected long calcPeriodKey(Builder moduleKey, long atSec) {
        int commodityId = moduleKey.getIntKey1();
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return 0;
        }

        if (commodityConf.getCanAccumulate()) {
            // 商品120048每天限购一个，但它还有特殊逻辑是按每天一个累计可购买额度，即中间n天没买了则这次允许购买n个，但限制n<=7。这个周期性并不是GOPT_Weekly(每个自然周)
//            return BaseOutputModule.calcPeriodKeyByPeriodType(GeneralOutputPeriodType.GOPT_Weekly, atSec);
        }

        if (commodityConf.getLimitType() == MallCommodityLimit.MCL_EachOnSaleLimit) {
            return commodityConf.getBeginTime().getSeconds();
        }

        GeneralOutputPeriodType periodType = transBuyLimitTypeToPeriodKey(commodityConf.getLimitType());
        return BaseOutputModule.calcPeriodKeyByPeriodType(periodType, atSec);
    }

    @Override
    protected long calcMaxCnt(Builder moduleKey, long periodKey) {
        int commodityId = moduleKey.getIntKey1();
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return 0;
        }
        if (commodityConf.getCanAccumulate()) {
            return commodityConf.getAccumulateMax();
        }
        return commodityConf.getLimitNum() + player.getWolfKillMgr().getMonthCardAddLimit(commodityConf);
    }

    public GeneralOutputModuleKey.Builder buildModuleKey(int commodityId) {
        return GeneralOutputModuleKey.newBuilder().setModuleType(getModuleType()).setIntKey1(commodityId);
    }

    private GeneralOutputPeriodType transBuyLimitTypeToPeriodKey(MallCommodityLimit limitType) {
        switch (limitType) {
            case MCL_DailyLimit:
                return GeneralOutputPeriodType.GOPT_Daily;
            case MCL_WeeklyLimit:
                return GeneralOutputPeriodType.GOPT_Weekly;
            case MCL_MonthlyLimit:
                return GeneralOutputPeriodType.GOPT_Monthly;
            case MCL_YearlyLimit:
                return GeneralOutputPeriodType.GOPT_Yearly;
            case MCL_LifeLongLimit:
                return GeneralOutputPeriodType.GOPT_Lifelong;
            case MCL_SeasonLimit:
                return GeneralOutputPeriodType.GOPT_Season;
            case MCL_EachOnSaleLimit:
                return GeneralOutputPeriodType.GOPT_Periods;
        }
        return GeneralOutputPeriodType.GOPT_None;
    }
}

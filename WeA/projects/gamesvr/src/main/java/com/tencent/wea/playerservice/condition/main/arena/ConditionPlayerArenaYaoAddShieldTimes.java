package com.tencent.wea.playerservice.condition.main.arena;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.condition.main.lobby.ConditionPlayerUltramanDamageMonster;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.battle.PlayerFinishLevelEvent;
import com.tencent.wea.protocol.common.BattleLevelEventDataForArena;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ConditionPlayerArenaYaoAddShieldTimes extends BasePlayerCondition {

    private static final Logger LOGGER = LogManager.getLogger(ConditionPlayerUltramanDamageMonster.class);

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerArenaYaoAddShieldTimes_VALUE;
    }

    @Override
    public long getAddValue(BasePlayerEvent event) {
        BattleLevelEventDataForArena battleLevelEventArenaData =
                ((PlayerFinishLevelEvent) event).getBattleLevelEventData().getArenaData();
        LOGGER.debug("battle level event arena data: {}", battleLevelEventArenaData);
        return battleLevelEventArenaData.getYaoAddShieldTimes();
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerFinishLevel)
    private void onEvent(PlayerFinishLevelEvent event) throws NKRuntimeException {
        if (!event.getBattleLevelEventData().getIsGiveUp()) {
            super.handleEvent(event);
        }
    }
}

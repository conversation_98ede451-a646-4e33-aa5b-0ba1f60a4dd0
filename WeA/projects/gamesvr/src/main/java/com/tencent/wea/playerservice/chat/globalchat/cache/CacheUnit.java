package com.tencent.wea.playerservice.chat.globalchat.cache;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @program: WeA
 * @description: 缓存单元
 * @author: nichtsun
 * @create: 2023-06-16
 **/

public class CacheUnit<T> {
    T data;
    private long lastUpdateTime;
    private AtomicBoolean isUpdating = new AtomicBoolean(false);

    public CacheUnit(T data, long lastUpdateTime) {
        this.data = data;
        this.lastUpdateTime = lastUpdateTime;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public boolean isUpdating() {
        return isUpdating.get();
    }

    public boolean acquireUpdateLock() {
        return this.isUpdating.compareAndSet(false, true);
    }

    public void releaseUpdateLock() {
        this.isUpdating.set(false);
    }
}

package com.tencent.wea.playerservice.cshandler.handler.chat;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.playerservice.chat.module.BaseChatModule;
import com.tencent.wea.playerservice.chat.module.custom.CustomChatModuleHelper;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsChat;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.common.ChatModuleInfo;
import com.tencent.wea.xlsRes.keywords.ChatModuleType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ChatBindModuleMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ChatBindModuleMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsChat.ChatBindModule_C2S_Msg reqMsg = (CsChat.ChatBindModule_C2S_Msg)request;
        CsChat.ChatBindModule_S2C_Msg.Builder rspMsg = CsChat.ChatBindModule_S2C_Msg.newBuilder();
        if (reqMsg.getChatModuleTypeVal() <= 0) {
            NKErrorCode.InvalidParams.throwError("miss chat module type");
        }
        ChatModuleType chatModuleType = ChatModuleType.forNumber(reqMsg.getChatModuleTypeVal());
        if (chatModuleType == null) {
            NKErrorCode.InvalidParams.throwError("invalid chat module type");
        }
        ChatModuleInfo chatModuleInfo = CustomChatModuleHelper.bindChatModule(player, reqMsg.getChatModuleTypeVal(),
                player.getPlayerChatManager().getChatModuleHub());
        if (chatModuleInfo == null) {
            NKErrorCode.ChatModuleBindFail.throwError("bind chat module failed");
        }
        rspMsg.setModuleInfo(chatModuleInfo);
        // 返回当前所有的聊天模块信息供客户端刷新
        for (BaseChatModule module : player.getPlayerChatManager().getChatModuleHub().getModules()) {
            rspMsg.addModuleInfoList(module.getChatModuleInfo());
        }
        return rspMsg;
    }
}

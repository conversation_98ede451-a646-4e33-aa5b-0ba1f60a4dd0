package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.rpc.RpcResult;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.protocol.SsCommon.TransmitGMCommandWithTargetUIDRes.Builder;
import com.tencent.wea.rpc.service.ActivityService;
import java.util.List;

/**
 * description: 设置星界奇遇每日任务进度
 *
 * <AUTHOR>
 * @date 2024/07/30
 */
public class SetThemeAdventureDailyTaskProgress implements GmHandler {

    /**
     * gm处理逻辑接口
     *
     * @param player 玩家
     * @param param gm参数列表
     * @return int
     */
    @Override
    public int handle(Player player, List<String> param) throws NKTimeoutException, RpcException {
        if (null == player || null == param || param.size() < 3) {
            return NKErrorCode.InvalidParams.getValue();
        }

        // 向activitysv发起请求
        SsCommon.TransmitGMCommandWithTargetUIDReq.Builder req = SsCommon.TransmitGMCommandWithTargetUIDReq.newBuilder();
        req.setCmdName("SetThemeAdventureDailyTaskProgress");
        req.setUid(player.getUid());
        req.addAllParam(param);
        try {
            RpcResult<Builder> result = ActivityService.get().transmitGMCommandWithTargetUID(req);
            if (!result.isOK()) {
                return -1;
            }
        } catch (Exception e) {
            return -2;
        }

        return 0;
    }
}

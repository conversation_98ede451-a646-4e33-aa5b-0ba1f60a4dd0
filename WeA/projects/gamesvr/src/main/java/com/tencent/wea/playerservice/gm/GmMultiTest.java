package com.tencent.wea.playerservice.gm;

import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.playerservice.cshandler.handler.player.GMCommandMsgHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead.CSHeader;
import com.tencent.wea.protocol.CsPlayer.GMCommand_C2S_Msg;
import com.tencent.wea.protocol.MsgTypes;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 多个GM测试 e.x.
 * GmPlayerRewardRetrieval 1;GmPlayerRewardRetrieval 8
 */
public class GmMultiTest implements GmHandler {

    private static final Logger LOGGER = LogManager.getLogger(GmMultiTest.class);

    @Override
    public int handle(Player player, List<String> param) {
        if (param.isEmpty()) {
            LOGGER.error("param list empty");
            return NKErrorCode.InvalidParams.getValue();
        }

        CSHeader.Builder csHeader = CSHeader.newBuilder().setType(MsgTypes.MSG_TYPE_GMCOMMAND_C2S_MSG);
        String gmStrs = param.get(1);

        for (String subGmStr : gmStrs.split(";")) {
            List<String> gmStr = Arrays.stream(subGmStr.split(" ")).collect(Collectors.toList());
            GMCommand_C2S_Msg.Builder gmCommand = GMCommand_C2S_Msg.newBuilder().setCmdName(gmStr.get(0));
            if (gmStr.size() > 1) {
                gmCommand.addAllParam(gmStr.subList(1, gmStr.size()));
            }
            new GMCommandMsgHandler().handle(player, csHeader.build(), gmCommand.build());
        }

        return NKErrorCode.OK.getValue();
    }
}

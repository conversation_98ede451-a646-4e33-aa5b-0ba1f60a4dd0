package com.tencent.wea.playerservice.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.PlayerUseDisplayBoardActionEvent;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;

public class ConditionUseDisplayBoardActionCumulative extends BasePlayerCondition {

    public ConditionUseDisplayBoardActionCumulative() { }

    @Override
    public int getType() {
        return ConditionType.ConditionType_UseDisplayBoardActionCumulative.getNumber();
    }

    @SubscribeEvent(routers = EventRouterType.ERT_UseDisplayBoardAction)
    private void onEvent(PlayerUseDisplayBoardActionEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    protected boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        // if (!checkSubCondition(event, progress.getSubConditionList())) {
        //     return false;
        // }
        long currentTimeMillis = DateUtils.currentTimeMillis();
        if (DateUtils.isSameDay(progress.getValueInternal(), currentTimeMillis)) {
            return false;
        }
        progress.setValueInternal(currentTimeMillis);
        return progress.increaseValue();
    }
}

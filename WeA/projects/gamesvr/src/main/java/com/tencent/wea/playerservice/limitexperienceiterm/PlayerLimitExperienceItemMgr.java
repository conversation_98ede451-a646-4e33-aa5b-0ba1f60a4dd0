package com.tencent.wea.playerservice.limitexperienceiterm;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.resourceloader.resclass.LimitedExperienceConf;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.wea.attr.LimitTimeExperienceItem;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResLimitedExperienceItem;
import com.tencent.wea.xlsRes.ResMisc;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;



public class PlayerLimitExperienceItemMgr extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(PlayerLimitExperienceItemMgr.class);
    private long lastRefreshTimeSecond = 0;
    private ResLimitedExperienceItem.LimitedExperienceConf currentExperienceConf = null;
    private int limitItemExperienceFreshTime = 0 ;

    public PlayerLimitExperienceItemMgr(Player player) {
        super(GameModuleId.GMI_LimitExperienceIterm, player);
        ResMisc.MiscConf miscConf = MiscConf.getInstance().getMiscConf();
        limitItemExperienceFreshTime = miscConf.getLimitItemExperienceFreshTime();
    }

    public void refreshLimitExperienceItem() {
        long currentTimeSec = Framework.currentTimeSec();
        if (currentExperienceConf == null || currentTimeSec > currentExperienceConf.getEndTime().getSeconds()) {
            tryRefreshNewLimitExperienceItem(currentTimeSec);
        }
        lastRefreshTimeSecond = currentTimeSec;
    }

    private void tryRefreshNewLimitExperienceItem(long currentTimeSec) {
        List<ResLimitedExperienceItem.LimitedExperienceConf> arrayList = LimitedExperienceConf.getInstance().getArrayList();
        LimitTimeExperienceItem limitTimeExperienceItem = player.getUserAttr().getLimitTimeExperienceItem();
        if (currentExperienceConf != null) {
            clearCurrentLimitExperienceItem();
        }
        if (arrayList != null) {
            for (ResLimitedExperienceItem.LimitedExperienceConf limitedExperienceConf : arrayList) {
                long beginTime = limitedExperienceConf.getBeginTime().getSeconds();
                long endTime = limitedExperienceConf.getEndTime().getSeconds();
                if (currentTimeSec > beginTime && currentTimeSec < endTime) {
                    currentExperienceConf = limitedExperienceConf;
                    limitTimeExperienceItem.setId(limitedExperienceConf.getId());
                    List<Long> itemIdList = limitedExperienceConf.getItemIdList();
                    for (Long itemId : itemIdList) {
                        limitTimeExperienceItem.addItermList(itemId);
                    }
                    break;
                }
            }
        }
    }
    public void onlineProc() {
        if (lastRefreshTimeSecond == 0 || Framework.currentTimeSec() > lastRefreshTimeSecond + limitItemExperienceFreshTime) {
            refreshLimitExperienceItem();
        }
    }


    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    @Override
    public void onRegister() throws NKCheckedException {

    }

    @Override
    public void afterRegister() throws NKCheckedException {

    }

    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    @Override
    public void onLoad() throws NKCheckedException {

    }

    @Override
    public void afterLoad() {

    }

    @Override
    public void prepareLogin() throws NKCheckedException {

    }

    void clearCurrentLimitExperienceItem() {
        player.getUserAttr().getLimitTimeExperienceItem().clear();
        currentExperienceConf = null;
    }

    @Override
    public void onLogin() throws NKCheckedException {
        long currentTimeSec = Framework.currentTimeSec();
        LimitTimeExperienceItem limitTimeExperienceItem = player.getUserAttr().getLimitTimeExperienceItem();
        if (limitTimeExperienceItem.getId() != 0) {
            ResLimitedExperienceItem.LimitedExperienceConf conf = LimitedExperienceConf.getInstance().get(limitTimeExperienceItem.getId());
            if (conf != null && currentTimeSec > conf.getBeginTime().getSeconds() && currentTimeSec < conf.getEndTime().getSeconds()) {
                currentExperienceConf = conf;
            } else {
                tryRefreshNewLimitExperienceItem(currentTimeSec);
            }
        } else {
            tryRefreshNewLimitExperienceItem(currentTimeSec);
        }
    }

    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    @Override
    public void onLogout() {

    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        //check();
    }

    @Override
    public void onMidNight() {
        refreshLimitExperienceItem();
    }


    @Override
    public void onWeekStart() {

    }
}

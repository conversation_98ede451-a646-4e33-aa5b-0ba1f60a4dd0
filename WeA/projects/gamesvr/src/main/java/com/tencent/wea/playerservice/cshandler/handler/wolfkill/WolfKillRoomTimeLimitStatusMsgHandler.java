package com.tencent.wea.playerservice.cshandler.handler.wolfkill;

import com.google.protobuf.Message;
import com.tencent.nk.commonframework.Framework;
import com.tencent.wea.g6.irpc.proto.competition.Competition;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.player.WujiConfigMgr;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsWolfkill;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class WolfKillRoomTimeLimitStatusMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(WolfKillRoomTimeLimitStatusMsgHandler.class);

    private void addEvent(CsWolfkill.WolfKillRoomTimeLimitStatus_S2C_Msg.Builder resp,
                          Competition.WolfKillTimeLimitedEvent event, long curTime, long monthCardEndTs) {
        CsWolfkill.WolfKillTimeLimitedItem.Builder item = CsWolfkill.WolfKillTimeLimitedItem.newBuilder();
        item.setStartTime(event.getStartTime());
        item.setEndTime(event.getEndTime());
        item.setTimeLimitEvent(event.getEventType());
        item.setPackageType(event.getPackageType());
        item.setMatchType(event.getMatchType());

        if (event.getEventType() == CsWolfkill.WolfKillTimeLimitedEvent.EVENT_EASTER_EGG_VALUE) {
            if (monthCardEndTs > curTime && monthCardEndTs > event.getEndTime()) {
                item.setEndTime(monthCardEndTs);
            }
        }

        // 跳转id
        item.setJumpID(event.getJumpId());
        item.setJumpType(event.getJumpType());
        // 图片
        item.setPic(event.getPic());
        // 标签文本
        if (event.getTipStartTime() <= curTime && curTime < event.getTipEndTime()) {
            item.setTipText(event.getTipText());
        }
        resp.addTimeLimited(item);

        // 废弃
        resp.addTimeLimitEvent(event.getEventType());
    }

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        CsWolfkill.WolfKillRoomTimeLimitStatus_C2S_Msg reqMsg = (CsWolfkill.WolfKillRoomTimeLimitStatus_C2S_Msg) request;
        CsWolfkill.WolfKillRoomTimeLimitStatus_S2C_Msg.Builder resp = CsWolfkill.WolfKillRoomTimeLimitStatus_S2C_Msg.newBuilder();
        Competition.NR3ERealTimeConfigRes nr3eConfig = WujiConfigMgr.getNR3ERealTimeConfig();

        if (nr3eConfig != null) {
            LOGGER.debug("nr3eConfig TimeLimitedEvent count:{}", nr3eConfig.getTimeLimitedEventCount());
            long curTime = Framework.currentTimeMillis() / 1000;
            long monthCardEndTs = player.getWolfKillMgr().getMonthCardEndTs() / 1000;
            if (nr3eConfig.getTimeLimitedEventCount() > 0) {
                List<Competition.WolfKillTimeLimitedEvent> list = nr3eConfig.getTimeLimitedEventList();
                for (Competition.WolfKillTimeLimitedEvent event : list) {
                    if (event.getEndTime() > curTime) {
                        // 一般活动
                        addEvent(resp, event, curTime, monthCardEndTs);
                    } else if (event.getEventType() == CsWolfkill.WolfKillTimeLimitedEvent.EVENT_EASTER_EGG_VALUE &&
                            monthCardEndTs > curTime) {
                        // 月卡彩蛋局
                        addEvent(resp, event, curTime, monthCardEndTs);
                    }
                }
            }
        }
        return resp;
    }
}

package com.tencent.wea.playerservice.event.common;

import com.tencent.eventcenter.EventConsumer;
import com.tencent.eventcenter.EventRouterEnum;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.keywords.BlessingCardSourceType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.EventType;
import java.util.Collection;
import java.util.Collections;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * description: 玩家获取春节集福活动福卡
 *
 * <AUTHOR>
 * @date 2024/01/16
 */
public class PlayerGetSpringBlessingCardEvent extends BasePlayerEvent {
    private static final Logger LOGGER = LogManager.getLogger(PlayerGetSpringBlessingCardEvent.class);

    private final int cardId;
    private final BlessingCardSourceType sourceType;

    // 玩家UID或者赞助商ID
    private final long sourceId;

    public PlayerGetSpringBlessingCardEvent(Player player, int cardId, BlessingCardSourceType sourceType, long sourceId) {
        super(player);
        this.cardId = cardId;
        this.sourceType = sourceType;
        this.sourceId = sourceId;
    }

    public int getCardId() {
        return cardId;
    }

    public BlessingCardSourceType getSourceType() {
        return sourceType;
    }

    @Override
    public long getStatisticParam() {
        return 0;
    }

    @Override
    public long getStatisticData() {
        return 0;
    }

    public long getSourceId() {
        return sourceId;
    }

    @Override
    public int getEventType() {
        return EventType.ET_PlayerGetSpringBlessingCard.getNumber();
    }

    @EventRouterEnum(router = EventRouterType.ERT_PlayerGainSpringBlessingCard)
    public Collection<? extends EventConsumer> routeToPlayer() {
        return Collections.singleton(player);
    }
}

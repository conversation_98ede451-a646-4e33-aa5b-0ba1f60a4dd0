package com.tencent.wea.playerservice.rank;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.tencent.lbs.LBSCodeUtils;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.rank.cacheclient.RankListCacheClient;
import com.tencent.rank.console.BackendConsole;
import com.tencent.rank.utils.RankAttrUtils;
import com.tencent.rank.utils.RankIdApolloIdMapper;
import com.tencent.rank.utils.RankIdSeasonIdMapper;
import com.tencent.rank.utils.RankRuleUtils;
import com.tencent.rank.utils.RankSettlementUtils;
import com.tencent.resourceloader.resclass.ClubCommonConf;
import com.tencent.resourceloader.resclass.LevelInfoData;
import com.tencent.resourceloader.resclass.MatchDateData;
import com.tencent.resourceloader.resclass.MatchDegreeTypeGroupData;
import com.tencent.resourceloader.resclass.MatchTypeData;
import com.tencent.resourceloader.resclass.QDTDegreeTypeData;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.RankingDisplayConfData;
import com.tencent.resourceloader.resclass.RankingRuleConfData;
import com.tencent.resourceloader.resclass.SeasonConfData;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.PlayerRankGeoInfo;
import com.tencent.wea.attr.PlayerRankSettlement;
import com.tencent.wea.attr.QualifyingDailyRankInfo;
import com.tencent.wea.attr.RankHistory;
import com.tencent.wea.attr.RankHistoryItem;
import com.tencent.wea.attr.RankInfoItem;
import com.tencent.wea.attr.RankInfoReportStatus;
import com.tencent.wea.g6.irpc.proto.ds_player.DsPlayer;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.MailInteraction.TlogSendReason;
import com.tencent.wea.playerservice.event.common.PlayerRankSettlementEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.rank.cache.SkipListRankCache;
import com.tencent.wea.playerservice.rank.fetcher.PlayerPublicFetcher;
import com.tencent.wea.playerservice.rank.fetcher.ScoreFetcher;
import com.tencent.wea.playerservice.rank.ranker.ClubRanker;
import com.tencent.wea.playerservice.rank.ranker.FriendRanker;
import com.tencent.wea.playerservice.rank.ranker.GeoRanker;
import com.tencent.wea.playerservice.rank.ranker.GlobalRanker;
import com.tencent.wea.playerservice.rank.ranker.Ranker;
import com.tencent.wea.playerservice.rank.ranker.Ranker.Entry;
import com.tencent.wea.playerservice.rank.ranker.Ranker.SetResult;
import com.tencent.wea.playerservice.rank.ranker.Rankers;
import com.tencent.wea.playerservice.rank.ranker.UgcRanker;
import com.tencent.wea.playerservice.rank.uploader.RedisZSetUploader;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.AttrPlayerRankGeoInfo.proto_PlayerRankGeoInfo;
import com.tencent.wea.protocol.AttrRankHistoryItem.proto_RankHistoryItem;
import com.tencent.wea.protocol.CsRank;
import com.tencent.wea.protocol.CsRank.ChangePlayerRankGeoInfo_C2S_Msg;
import com.tencent.wea.protocol.CsRank.ChangePlayerRankGeoInfo_S2C_Msg;
import com.tencent.wea.protocol.CsRank.DeletePlayerRankGeoInfo_C2S_Msg;
import com.tencent.wea.protocol.CsRank.DeletePlayerRankGeoInfo_S2C_Msg;
import com.tencent.wea.protocol.CsRank.FetchBatchInfo_C2S_Msg;
import com.tencent.wea.protocol.CsRank.FetchBatchInfo_S2C_Msg;
import com.tencent.wea.protocol.CsRank.FetchBatchSnapshotInfo_C2S_Msg;
import com.tencent.wea.protocol.CsRank.FetchBatchSnapshotInfo_S2C_Msg;
import com.tencent.wea.protocol.CsRank.FetchRankNoByScore_C2S_Msg;
import com.tencent.wea.protocol.CsRank.FetchRankNoByScore_S2C_Msg;
import com.tencent.wea.protocol.CsRank.RankTabInfo;
import com.tencent.wea.protocol.common.BanStatus;
import com.tencent.wea.protocol.common.BanType;
import com.tencent.wea.protocol.common.FreshPlayerRanksInfo;
import com.tencent.wea.protocol.common.GeoLevel;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.KeyValueInt32;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.MailAttachmentListOrBuilder;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.PlayerRankInfo;
import com.tencent.wea.protocol.common.RankId;
import com.tencent.wea.protocol.common.RankInfoPbItem;
import com.tencent.wea.protocol.common.RankSeasonInfo;
import com.tencent.wea.protocol.common.RankType;
import com.tencent.wea.protocol.common.SpecificUserTopRankInfo;
import com.tencent.wea.protocol.common.SpecificUserTopRankInfoList;
import com.tencent.wea.protocol.common.TopRankInfo;
import com.tencent.wea.protocol.common.TopRankJointInfo;
import com.tencent.wea.protocol.common.TopRankSnapshotInfo;
import com.tencent.wea.xlsRes.ResRanking;
import com.tencent.wea.xlsRes.ResRanking.RankingConf;
import com.tencent.wea.xlsRes.ResRanking.RankingRuleConf;
import com.tencent.wea.xlsRes.ResSeason;
import com.tencent.wea.xlsRes.keywords.ClubConfEnum;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.LevelCompletionType;
import com.tencent.wea.xlsRes.keywords.QualifyType;
import com.tencent.wea.xlsRes.keywords.QualifyingDegreeInfo;
import com.tencent.wea.xlsRes.keywords.QualifyingDegreeType;
import com.tencent.wea.xlsRes.keywords.RankOrderingType;
import com.tencent.wea.xlsRes.keywords.RankReportStatus;
import com.tencent.wea.xlsRes.keywords.RankRule;
import com.tencent.wea.xlsRes.keywords.RankUpdatePolicy;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 排行榜管理
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
public class RankManager extends PlayerModule {

    private static final Logger LOGGER = LogManager.getLogger(RankManager.class);
    private final FriendRanker friendRanker;
    private final GlobalRanker globalRanker;
    private final GeoRanker geoRanker;
    private final Map<Integer, Long> lastPreFetchRefresh;
    private final Deque<RefreshDescriptor> refreshQueue;
    private final Deque<Integer> preFetchRefreshQueue;
    private final Deque<NKPair<Integer, Integer>> rewardQueue;
    private final Map<NKPair<Integer, Integer>, SnapshotInfoCacheNode> snapshotInfoCache = new LinkedHashMap<>() {
        @Override
        protected boolean removeEldestEntry(Map.Entry<NKPair<Integer, Integer>, SnapshotInfoCacheNode> eldest) {
            return size() > getSnapshotInfoCacheCapacity();
        }
    };
    private long lastProcTs;
    private long lastRewardProcTs;
    private long batchInfoTs = DateUtils.currentTimeMillis();

    public RankManager(Player player) {
        super(GameModuleId.GMI_RankManager, player);

        friendRanker = new FriendRanker(player, RankManager::getScoreFetcher);
        globalRanker = new GlobalRanker(player);
        geoRanker = new GeoRanker(player);

        lastPreFetchRefresh = new HashMap<>();
        refreshQueue = new ArrayDeque<>();
        preFetchRefreshQueue = new ArrayDeque<>();
        rewardQueue = new ArrayDeque<>();
        lastProcTs = DateUtils.currentTimeMillis();
        lastRewardProcTs = DateUtils.currentTimeMillis();
    }

    private static Map<RankType, SetResult> ofFailureUpdateReturn(NKErrorCode errorCode) {
        return new HashMap<>() {{
            put(RankType.RT_Friend, SetResult.ofFailure(errorCode));
            put(RankType.RT_Global, SetResult.ofFailure(errorCode));
            put(RankType.RT_Geo, SetResult.ofFailure(errorCode));
        }};
    }

    private static ScoreFetcher getScoreFetcher(RankId rankId, RankSeasonInfo seasonConf) {
        RankingConf rankingConf = RankingConfData.getInstance().get(rankId.getId());
        if (rankingConf != null && (UgcRanker.isUgcRelated(rankId.getId()) || ClubRanker.isClubRelated(
                rankId.getId()))) {
            return ScoreFetcher.DUMMY;
        }

        return PlayerPublicFetcher.getInstance(seasonConf.getId());
    }

    public static Set<Integer> getGmRankIds(List<Integer> designatedRankIds, List<Integer> designatedRankRules,
            List<Integer> levelIds) {
        Set<Integer> res = new HashSet<>();
        for (int rankId : designatedRankIds) {
            if (rankId == 0) {
                continue;
            }

            RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null) {
                LOGGER.error("unknown rank id and skip it, rankId:{}", rankId);
                continue;
            }

            res.add(rankId);
        }

        for (int ruleId : designatedRankRules) {
            if (ruleId == 0) {
                continue;
            }

            RankingRuleConf ruleConf = RankingRuleConfData.getInstance().get(RankRule.forNumber(ruleId));
            if (ruleConf == null) {
                LOGGER.error("unknown rank rule, rule:{}", ruleId);
                continue;
            }

            res.addAll(RankingConfData.getInstance().getByRule(ruleConf.getRule()));
        }

        for (int levelId : levelIds) {
            if (levelId == 0) {
                continue;
            }

            RankingConfData.getInstance().getByLevelId(levelId).forEach(c -> res.add(c.getRankId()));
        }

        return res;
    }

    public static RefreshDescriptor ofNewRefresh(int rankId, int seasonId, int score) {
        return new RefreshDescriptor(rankId, seasonId, Lists.newArrayList(score));
    }

    public static RefreshDescriptor ofNewRefresh(int rankId, int seasonId, List<Integer> scores) {
        return new RefreshDescriptor(rankId, seasonId, scores);
    }

    private static long getSnapshotInfoCacheCapacity() {
        return PropertyFileReader.getRealTimeLongItem("rank_list_snapshot_cache_size", 10);
    }

    /**
     * 刷新排行榜分数，输入的成绩是整数
     *
     * @param rankId 排行榜ID
     * @param score 分数
     * @param seasonId 赛季ID，可以通过{@link RankIdSeasonIdMapper}获取
     * @return {@link NKErrorCode}
     */
    public NKErrorCode refresh(int rankId, int score, int seasonId) {
        offerRefreshJob(rankId, seasonId, Lists.newArrayList(score));
        return NKErrorCode.OK;
    }

    /**
     * 刷新排行榜分数，输入的成绩是整数数组，长度不超过3
     *
     * @param rankId 排行榜ID
     * @param scores 分数
     * @param seasonId 赛季ID，可以通过{@link RankIdSeasonIdMapper}获取
     * @return {@link NKErrorCode}
     */
    public NKErrorCode refresh(int rankId, int[] scores, int seasonId) {
        if (scores == null || scores.length == 0 || scores.length > 3) {
            LOGGER.error("player try to upload illegal score, uid:{} rank:{} score:{}", player.getUid(), rankId,
                    scores);
            return NKErrorCode.RankScoreEncodingFailure;
        }

        List<Integer> scoreFields = new ArrayList<>();
        for (int score : scores) {
            scoreFields.add(score);
        }

        offerRefreshJob(rankId, seasonId, scoreFields);
        return NKErrorCode.OK;
    }

    /**
     * 批量刷新排行榜分数
     *
     * @param descriptors 刷新描述符
     */
    public void batchRefresh(List<RefreshDescriptor> descriptors) {
        for (var descriptor : descriptors) {
            offerRefreshJob(descriptor.rankId, descriptor.seasonId, descriptor.scores);
        }
    }

    /**
     * 刷新排行榜分数并获取自身新分数在全服排行榜的排名，输入的成绩是整数
     *
     * @param rankId 排行榜ID
     * @param score 分数
     * @param seasonId 赛季ID，可以通过{@link RankIdSeasonIdMapper}获取
     * @return {@link PlayerRankInfo}>
     */
    public PlayerRankInfo refreshAndGetSelfInGlobal(int rankId, int score, int seasonId) {
        // remove undergoing refresh
        removeRefreshJob(rankId, seasonId);

        Map<RankType, SetResult> res = refreshRank(rankId, Collections.singletonList(score), seasonId);
        if (!res.containsKey(RankType.RT_Global)) {
            LOGGER.error("player failed to refresh score in global rank without result, uid:{} rank:{} score:{}",
                    player.getUid(), rankId, score);
            return null;
        }

        SetResult gRes = res.get(RankType.RT_Global);
        if (!gRes.errorCode.isOk()) {
            LOGGER.error("player failed to refresh score in global rank, uid:{} rank:{} score:{} err:{}",
                    player.getUid(), rankId, score, gRes.errorCode);
            return null;
        }

        if (!gRes.needUpdate || gRes.infoList.isEmpty()) {
            LOGGER.info("player actively fetch self due to no update, uid:{} score:{} rank:{}", player.getUid(), rankId,
                    score);
            return getSelf(rankId, seasonId, GeoLevel.GL_Unknown, RankType.RT_Global, Source.FromRemote, false);
        }

        Optional<TopRankInfo> info = Optional.of(gRes.infoList.get(0)).filter(SpecificUserTopRankInfo::getExists)
                .map(SpecificUserTopRankInfo::getInfo).filter(i -> checkSelfRes(i, List.of(score)));

        if (info.isEmpty()) {
            LOGGER.error("player failed to fetch self in global rank, uid:{} rank:{} score:{}", player.getUid(), rankId,
                    score);
            Monitor.getInstance().add.succ(MonitorId.attr_rank_self_not_found, 1);
            return null;
        }

        PlayerRankInfo.Builder builder = info.map(Rankers::preparePlayerRankInfo).orElse(null);
        return builder.build();
    }

    /**
     * 得到玩家自身排行数据。
     *
     * @param rankId 排行榜ID
     * @param seasonId 赛季ID，可以通过{@link RankIdSeasonIdMapper}获取
     * @return {@link Optional}<{@link Entry}>
     */
    public PlayerRankInfo getSelf(int rankId, int seasonId) {
        return getSelf(rankId, seasonId, GeoLevel.GL_Unknown, RankType.RT_Global, Source.FromRemote, false);
    }

    /**
     * 得到玩家自身排行数据
     *
     * @param rankId 排行榜ID
     * @param seasonId 赛季ID，可以通过{@link RankIdSeasonIdMapper}获取
     * @param level 地理范围层级
     * @param ranker 榜单类型
     * @param source 数据来源限制
     * @param retry 是否尝试重新更新未能成功刷新的成绩
     * @return {@link Optional}<{@link Entry}>
     */
    public PlayerRankInfo getSelf(int rankId, int seasonId, GeoLevel level, RankType ranker, Source source,
            boolean retry) {
        var currentMs = DateUtils.currentTimeMillis();

        if (retry) {
            offerPreFetchRefreshJob(rankId, currentMs);
        }

        Ranker rank = getRanker(ranker);
        RankId.Builder id = RankId.newBuilder().setId(rankId);
        if (ranker == RankType.RT_Geo) {
            int subId = getThisWeekGeoInfo(currentMs).getOrDefault(level.getNumber(), 0);
            if (subId == 0) {
                LOGGER.debug("geo info not found, uid:{} level:{}", player.getUid(), level.getNumber());
                return null;
            }

            id.setSubType(level.getNumber()).setSubId(subId);
        }

        int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(),
                RankIdSeasonIdMapper.ofId(rankId, seasonId));
        if (apolloId != 0) {
            id.setApolloId(apolloId);
        }

        switch (source) {
            case FromCache:
                return rank.getSelfRankIfInCache(id.build()).map(e -> e.rankInfo.build()).orElse(null);
            case FromRemote:
                return rank.getSelfRankFromRemote(id.build()).map(e -> e.rankInfo.build()).orElse(null);
            default:
                return rank.getSelfRank(id.build()).map(e -> e.rankInfo.build()).orElse(null);
        }
    }

    /**
     * 得到具体列表
     *
     * @param rankId 排行榜ID
     * @param from 起始排名
     * @param count 数量
     * @return {@link NKPair}<{@link List}<{@link Entry}>, {@link Optional}<{@link Entry}>>
     */
    public NKPair<List<Entry>, Optional<Entry>> getRankListAndSelf(int rankId, int from, int count) {
        return getRankListAndSelf(rankId, GeoLevel.GL_Unknown, RankType.RT_Global, from, count, false);
    }

    /**
     * 得到具体列表
     *
     * @param rankId 排行榜ID
     * @param level 地理范围层次
     * @param ranker 榜单类型
     * @param from 起始排名
     * @param count 数量
     * @param retry 是否尝试重新更新未能成功刷新的成绩
     * @return {@link NKPair}<{@link List}<{@link Entry}>, {@link Optional}<{@link Entry}>>
     */
    public NKPair<List<Entry>, Optional<Entry>> getRankListAndSelf(int rankId, GeoLevel level, RankType ranker,
            int from, int count, boolean retry) {

        if (retry) {
            offerPreFetchRefreshJob(rankId, DateUtils.currentTimeMillis());
        }

        switch (ranker) {
            case RT_Friend:
                return getRankListAndSelf(friendRanker, RankId.newBuilder().setId(rankId).build(), from, count);
            case RT_Global:
                return getRankListAndSelf(globalRanker, RankId.newBuilder().setId(rankId).build(), from, count);
            case RT_Geo:
                return getRankListAndSelf(geoRanker,
                        RankId.newBuilder().setId(rankId).setSubType(level.getNumber()).build(), from, count);
            default:
                LOGGER.error("unknown ranker type {}", ranker);
                return new NKPair<>(Lists.newArrayList(), Optional.empty());
        }
    }

    /**
     * 清除玩家在指定排行榜中的成绩
     *
     * @param rankId 排行榜ID
     * @return {@link NKErrorCode}
     */
    public NKErrorCode delete(int rankId) {
        return delete(Collections.singletonList(rankId));
    }

    /**
     * 清除玩家在指定排行榜中的成绩
     *
     * @param rankIds 排行榜ID
     * @return {@link NKErrorCode}
     */
    public NKErrorCode delete(List<Integer> rankIds) {
        List<Integer> res = new ArrayList<>();
        for (int rankId : rankIds) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null) {
                LOGGER.error("unknown rank id, uid:{} rankId:{}", player.getUid(), rankId);
                continue;
            }

            if (rankingConf.getRule() == RankRule.RR_Ugc_Exp) {
                LOGGER.error("ugc rank id is not managed by RankManager, uid:{} rank:{}", player.getUid(), rankId);
                continue;
            }

            res.add(rankId);
        }

        rankIds = res;
        NKErrorCode ret = NKErrorCode.OK;
        for (int rankId : rankIds) {
            removeRefreshJob(rankId);
            player.getUserAttr().getPlayerPublicGameData().removeRankInfo(rankId);
            resetRankInfoReportStatus(rankId);
        }

        NKErrorCode errorCode = friendRanker.removeSelf(rankIds);
        if (!errorCode.isOk()) {
            LOGGER.error("failed to clear friend rank for player {}: {}", player, errorCode);
            ret = errorCode;
        }

        errorCode = BackendConsole.getInstance()
                .removeOneUserApollo(player.getUid(), mapToApolloIds(rankIds), geoRanker.getAllInvolvingSubRankIds());

        if (!errorCode.isOk()) {
            LOGGER.error("failed to clear apollo rank for player {}: {}", player, errorCode);
            ret = errorCode;
        }

        errorCode = BackendConsole.getInstance()
                .removeOneUserZSet(player.getUid(), mapToApolloIds(rankIds));
        if (!errorCode.isOk()) {
            LOGGER.error("failed to clear zset rank for player {}: {}", player, errorCode);
            ret = errorCode;
        }

        return ret;
    }

    public NKErrorCode overwrite(int rankId, List<Integer> scores, int seasonId) {
        NKErrorCode errorCode = delete(rankId);
        if (!errorCode.isOk()) {
            LOGGER.error("failed to delete old rank, uid:{} rankId:{} err:{}", player.getUid(), rankId, errorCode);
            return errorCode;
        }

        if (seasonId == 0) {
            LOGGER.error("season not found, uid:{} rankId:{} season:{}", player.getUid(), rankId, seasonId);
            return NKErrorCode.ResNotFound;
        }

        removeRefreshJob(rankId, seasonId);
        var resultMap = refreshRank(rankId, scores, seasonId);
        boolean retry = resultMap.values().stream().anyMatch(r -> r.errorCode == NKErrorCode.RankUpdateFused);

        if (retry) {
            LOGGER.info("force fresh score again, uid:{} rankId:{}", player.getUid(), rankId);
            offerPreFetchRefreshJob(rankId, DateUtils.currentTimeMillis());
        }

        LOGGER.warn("score overwrite finished, uid:{} rank:{} result:{}", player.getUid(), rankId, errorCode);
        return errorCode;
    }

    public NKErrorCode banAll() {
        List<Integer> rankIds = new ArrayList<>();

        for (RankInfoItem item : player.getUserAttr().getPlayerPublicGameData().getRankInfo().values()) {
            rankIds.add(item.getType());
        }

        player.getUserAttr().getRankData().setHideAllFromOthers(true);
        return banRankScores(rankIds);
    }

    public NKErrorCode unbanAll() {
        if (!player.getUserAttr().getRankData().getHideAllFromOthers()) {
            LOGGER.debug("already unban rank score and skip, uid:{}", player.getUid());
            return NKErrorCode.OK;
        }

        List<Integer> rankIds = new ArrayList<>();

        for (RankInfoItem item : player.getUserAttr().getPlayerPublicGameData().getRankInfo().values()) {
            rankIds.add(item.getType());
        }

        NKErrorCode errorCode = unbanRankScores(rankIds);
        if (errorCode.hasError()) {
            LOGGER.error("failed to unban rank scores, uid:{}", player.getUid());
            return errorCode;
        }

        player.getUserAttr().getRankData().setHideAllFromOthers(false);
        LOGGER.info("unban successfully, uid:{}", player.getUid());
        return NKErrorCode.OK;
    }

    public void onFriendRemoved(long uid) {
        friendRanker.deleteFriendFromCache(uid);
    }

    /**
     * 清除玩家所有成绩
     *
     * @return {@link NKErrorCode}
     */
    public NKErrorCode resetAttr() {
        player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo().clear();
        player.getUserAttr().getPlayerPublicGameData().clearRankInfo();
        player.getUserAttr().getRankData().clear();

        return NKErrorCode.OK;

//        NKErrorCode errorCode = BackendConsole.getInstance()
//                .removeOneUserAll(player.getUid(), player.getOpenId(), player.getCreatorId(), player.getPlatId(),
//                        player.getUserAttr().getPlayerPublicBasicInfo().getAccountType(),
//                        mapToApolloIds(ids), subIds);
//        if (!errorCode.isOk()) {
//            LOGGER.error("failed to remove {} in backend: {}", player.getUid(), errorCode);
//        }
//        return errorCode;
    }

    public CsRank.GetRankTabRankIdList_S2C_Msg.Builder handleListReq(CsRank.GetRankTabRankIdList_C2S_Msg reqMsg) {
        CsRank.GetRankTabRankIdList_S2C_Msg.Builder resBuilder = CsRank.GetRankTabRankIdList_S2C_Msg.newBuilder();

        long currentSec = DateUtils.currentTimeSec();

        for (int tabId : reqMsg.getTabListList()) {
            RankTabInfo.Builder builder = RankTabInfo.newBuilder().setTabId(tabId);

            var conf = RankingDisplayConfData.getInstance().get(tabId);
            if (conf == null || !VersionUtil.isSatisfyVersionRestriction(player.getClientVersion64(),
                    conf.getLowerVersion(), conf.getHigherVersion(), Lists.newArrayList())) {
                LOGGER.debug("tab not compatible, uid:{} tab:{}", player.getUid(), tabId);

                builder.setVersionMatched(false);
                resBuilder.addTabList(builder);
                continue;
            }

            builder.setVersionMatched(true);
            long startSec = 0L;
            long endSec = 0L;

            for (int rankId : conf.getRankIdsList()) {
                RankingConf rankingConf = RankingConfData.getInstance().get(rankId);

                // check if rank is open
                if (rankingConf == null || !rankingConf.getIsShow()) {
                    LOGGER.debug("rank is closed, uid:{} tab:{} rank:{}", player.getUid(), tabId, rankId);
                    continue;
                }

                NKErrorCode errorCode = Rankers.checkRankListAccess(rankId);
                if (errorCode.hasError()) {
                    LOGGER.debug("access denied, uid:{} tab:{} rank:{} err:{}", player.getUid(), tabId, rankId,
                            errorCode);
                    continue;
                }

                RankSeasonInfo seasonInfo = RankIdSeasonIdMapper.ofTs(rankId, currentSec);
                if (seasonInfo == null) {
                    LOGGER.debug("season not found, uid:{} tab:{} rank:{}", player.getUid(), tabId, rankId);
                    continue;
                }

                if (rankingConf.getRule() == RankRule.RR_Qualify
                        || rankingConf.getRule() == RankRule.RR_Qualify_Challenger) {
                    var modeList = MatchDegreeTypeGroupData.getInstance().getByQualifyType(rankingConf.getDescId());

                    boolean isPermanent = false;
                    long modeStartSec = 0;
                    long modeEndSec = 0;
                    for (var mode : modeList) {
                        var matchConf = MatchTypeData.getInstance().get(mode);
                        if (matchConf == null) {
                            LOGGER.debug("unknown match type:{}", mode);
                            continue;
                        }

                        if (matchConf.getIsPermanent()) {
                            isPermanent = true;
                            continue;
                        }

                        var dateConf = MatchDateData.getInstance().getPlayMatchDate(mode);
                        if (dateConf == null) {
                            continue;
                        }

                        if (modeStartSec == 0 || dateConf.getStartTime().getSeconds() < modeStartSec) {
                            modeStartSec = dateConf.getStartTime().getSeconds();
                        }

                        if (modeEndSec == 0 || modeEndSec < dateConf.getEndTime().getSeconds()) {
                            modeEndSec = dateConf.getEndTime().getSeconds();
                        }
                    }

                    if (isPermanent) {
                        LOGGER.debug("mode is permanent, uid:{} tab:{} rank:{}", player.getUid(), tabId, rankId);
                        builder.addRankIds(rankId);
                        continue;
                    }

                    if (modeStartSec == 0 || modeEndSec == 0) {
                        LOGGER.debug("mode open time not found, uid:{} tab:{} rank:{}", player.getUid(), tabId, rankId);
                        continue;
                    }

                    startSec = modeStartSec;
                    endSec = modeEndSec;

                    boolean timeLimited =
                            modeStartSec != seasonInfo.getStartSec() || modeEndSec != seasonInfo.getEndSec();
                    if (timeLimited) {
                        builder.setTagType(1);
                    }

                    LOGGER.debug("mode open time found, uid:{} tab:{} rank:{} start:{} end:{}", player.getUid(), tabId,
                            rankId, startSec, endSec);
                    builder.addRankIds(rankId);
                    continue;
                }

                // check if level is open
                if (rankingConf.getLevelId() != 0) {
                    var levelConf = LevelInfoData.getInstance().get(rankingConf.getLevelId());
                    if (levelConf == null || levelConf.getIsCompleted() != LevelCompletionType.LCT_Completed) {
                        LOGGER.debug("level is closed, uid:{} tab:{} rank:{} level:{}", player.getUid(), tabId, rankId,
                                rankingConf.getLevelId());
                        continue;
                    }

                    if (levelConf.getLowestSeason() != 0 && seasonInfo.getId() < levelConf.getLowestSeason()) {
                        LOGGER.debug(
                                "level is closed for this season, uid:{} tab:{} rank:{} level:{} season:{} lowest:{}",
                                player.getUid(), tabId, rankId, rankingConf.getLevelId(), seasonInfo.getId(),
                                levelConf.getLowestSeason());
                        continue;
                    }

                    if (levelConf.getHighestSeason() != 0 && seasonInfo.getId() > levelConf.getHighestSeason()) {
                        LOGGER.debug(
                                "level is closed for this season, uid:{} tab:{} rank:{} level:{} season:{} highest:{}",
                                player.getUid(), tabId, rankId, rankingConf.getLevelId(), seasonInfo.getId(),
                                levelConf.getHighestSeason());
                        continue;
                    }

                    if (levelConf.getExcludeVersionsCount() != 0 && levelConf.getExcludedSeasonsList()
                            .contains(seasonInfo.getId())) {
                        LOGGER.debug("level is closed for this season, uid:{} tab:{} rank:{} level:{} season:{}",
                                player.getUid(), tabId, rankId, rankingConf.getLevelId(), seasonInfo.getId());
                        continue;
                    }

                    if (!VersionUtil.isSatisfyVersionRestriction(player.getClientVersion64(),
                            levelConf.getLowestVersion(), levelConf.getHighestVersion(),
                            levelConf.getExcludeVersionsList())) {
                        LOGGER.debug("level is incompatible, uid:{} tab:{} rank:{} level:{}", player.getUid(), tabId,
                                rankId, rankingConf.getLevelId());
                        continue;
                    }
                }

                // check if activity is open
                if (rankingConf.getActivityId() != 0) {
                    boolean active =
                            player.getActivityManager().getRunningActivity(rankingConf.getActivityId()) == null;
                    if (!active) {
                        LOGGER.debug("activity is close, uid:{} tab:{} rank:{} activity:{}", player.getUid(), tabId,
                                rankId, rankingConf.getActivityId());
                        continue;
                    }
                }

                if (seasonInfo.hasStartSec()) {
                    long sec = seasonInfo.getStartSec();
                    if (startSec == 0 || startSec > sec) {
                        startSec = sec;
                    }
                }

                if (seasonInfo.hasEndSec()) {
                    long sec = seasonInfo.getEndSec();
                    if (endSec == 0 || endSec < sec) {
                        endSec = sec;
                    }
                }

                builder.addRankIds(rankId);
            }

            if (startSec > 0) {
                builder.setStartSec(startSec);
            }

            if (endSec > 0) {
                builder.setEndSec(endSec);
            }

            resBuilder.addTabList(builder);
        }

        return resBuilder;
    }

    public CsRank.FetchTopRank_S2C_Msg.Builder handleFetchReq(CsRank.FetchTopRank_C2S_Msg reqMsg) {
        CsRank.FetchTopRank_S2C_Msg.Builder rspMsg = CsRank.FetchTopRank_S2C_Msg.newBuilder().setId(reqMsg.getId())
                .setType(reqMsg.getType());

        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            NKErrorCode errorCode = NKErrorCode.RankIsBanned;
            BanStatus banStatus = player.getBanStatus(BanType.BT_Rank);
            if (banStatus != null && banStatus.getBanBefore() > DateUtils.currentTimeMillis()) {
                player.sendBanInfoNtf(BanType.BT_Rank, banStatus.getBanBefore(), errorCode,
                        banStatus.getBanReason(), true);
            }
        }

        RankingConf rankingConf = RankingConfData.getInstance().get(reqMsg.getId());
        if (rankingConf == null) {
            LOGGER.error("cannot find rank conf for rank, rank:{}", reqMsg.getId());
            NKErrorCode.ResNotFound.throwError("no ranking conf found");
            return rspMsg;
        }

        if (!reqMsg.getWithoutInfo() && reqMsg.getCount() > 20) {
            LOGGER.error("ask too many info, rank:{} count:{}", reqMsg.getId(), reqMsg.getCount());
            NKErrorCode.RankInfoFetchExceedLimit.throwError("ask too many info");
            Monitor.getInstance().add.fail(MonitorId.attr_rank_info_fetch_exceed_limit, 1,
                    new String[]{Long.toString(reqMsg.getId())});
            return rspMsg;
        }

        Ranker ranker = getRanker(reqMsg.getType());
        NKErrorCode errorCode = ranker.checkReq(reqMsg);
        if (errorCode != NKErrorCode.OK) {
            if (PropertyFileReader.getRealTimeBooleanItem("rank_season_change_exception", true)) {
                errorCode.throwErrorIfNotOk("failed to pass the rank check");
            }
            return rspMsg;
        }

        long currentMs = DateUtils.currentTimeMillis();
        var seasonConf = RankIdSeasonIdMapper.ofTs(reqMsg.getId(), currentMs / 1000);

        if (seasonConf == null) {
            NKErrorCode.ResNotFound.throwError("season conf not found");
            return rspMsg;
        }

        if (rankingConf.getRule() == RankRule.RR_Club) {
            long delaySec = ClubCommonConf.getInstance().get(ClubConfEnum.CLUB_RANK_OLD_REMAIN_HOUR).getIntValue()
                    * DateUtils.ONE_HOUR_SECONDS;
            var lastSeasonConf = RankIdSeasonIdMapper.ofLast(reqMsg.getId(), currentMs / 1000);
            if (lastSeasonConf != null && currentMs / 1000 < seasonConf.getStartSec() + delaySec) {
                LOGGER.debug("using old club season, uid:{} rankId:{} old:{}", player.getUid(), reqMsg.getId(),
                        lastSeasonConf.getId());
                seasonConf = lastSeasonConf;
                rspMsg.setIsLastSeasonConf(true);
            }
        }

        var ruleConf = RankingRuleConfData.getInstance().get(rankingConf.getRule());
        if (ruleConf == null) {
            LOGGER.error("rule conf not found, uid:{} rankId:{}", player.getUid(), reqMsg.getId());
            NKErrorCode.ResNotFound.throwError("rule conf not found");
            return rspMsg;
        }

        if (ruleConf.hasBatchSnapshot()) {
            var spConf = ruleConf.getBatchSnapshot();
            if ((currentMs / 1000) <= seasonConf.getStartSec() + spConf.getDelayShowHours() * DateUtils.ONE_HOUR_SECONDS) {
                if (spConf.getReadPrevSeasonBeforeShow()) {
                    var lastSeasonConf = RankIdSeasonIdMapper.ofLast(reqMsg.getId(), currentMs / 1000);
                    if (lastSeasonConf != null) {
                        LOGGER.debug("using old club season, uid:{} rankId:{} old:{}", player.getUid(), reqMsg.getId(),
                                lastSeasonConf.getId());
                        seasonConf = lastSeasonConf;
                        rspMsg.setIsLastSeasonConf(true);
                    }
                }
            }
        }

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(reqMsg.getId());
        TconndApiAccount account = player.getAccountType();

        if (reqMsg.getAccount() != 0) {
            account = TconndApiAccount.forNumber(reqMsg.getAccount());
            if (account == null) {
                NKErrorCode.InvalidParams.throwError("invalid account, type:{}", reqMsg.getAccount());
                return rspMsg;
            }

            rspMsg.setAccount(reqMsg.getAccount());
        }

        int apolloId = RankIdApolloIdMapper.getApolloId(reqMsg.getId(), account, seasonConf);
        if (apolloId != 0) {
            rankIdBuilder.setApolloId(apolloId);
        }

        if (reqMsg.getType() == RankType.RT_Geo) {
            rankIdBuilder.setSubType(reqMsg.getGeoLevel().getNumber()).setSubId(reqMsg.getGeoCode());
            rspMsg.setGeoLevel(reqMsg.getGeoLevel()).setGeoCode(reqMsg.getGeoCode());

            if (!RankRuleUtils.isPlayMode("metro", rankingConf.getRule())) {
                if (!LBSCodeUtils.verifyAdministrationCode(reqMsg.getGeoLevel(), reqMsg.getGeoCode())) {
                    LOGGER.error("invalid lbs code and refuse access, uid:{} level:{} code:{}", player.getUid(),
                            reqMsg.getGeoLevel(), reqMsg.getGeoCode());
                    Monitor.getInstance().add.fail(MonitorId.attr_rank_geo_code_invalid, 1);
                    return rspMsg;
                }
            }
        }

        offerPreFetchRefreshJob(reqMsg.getId(), DateUtils.currentTimeMillis());

        RankId rankId = rankIdBuilder.build();
        var res = ranker.getSelfRankIfInCacheWithTopRankList(rankId, reqMsg.getFromIndex(), reqMsg.getCount(),
                reqMsg.getWithoutInfo(), reqMsg.getDeepRank(), reqMsg.getRealtimeSelf(), currentMs);

        if (rankingConf.getRule() == RankRule.RR_Qualify && reqMsg.getType() == RankType.RT_Global) {
            var qualify = rankingConf.getDescId();
            updateChallengerRankNoIfNeedy(qualify, false);
        }

        for (Entry top : res.value.key) {
            rspMsg.addRank(top.rankInfo);
            if (!reqMsg.getWithoutInfo()) {
                if (ClubRanker.isClubRelated(rankId.getId())) {
                    rspMsg.addClubInfo(top.clubInfo);
                } else {
                    rspMsg.addInfo(top.publicInfo);
                }
            }
        }

        if (res.value.value > 0) {
            int size = res.value.value;

            var rewardConf = RankSettlementUtils.getSnapshotRewardConf(rankId.getId(), seasonConf.getId());
            if (rewardConf != null) {
                size = RankSettlementUtils.regularizeSettlementRankSize(rewardConf, size);
                if (size != res.value.value) {
                    LOGGER.debug("rank size is regularized, uid:{} rankId:{} before:{} after:{}", player.getUid(),
                            rankId, res.value.value, size);
                }
            }

            rspMsg.setSize(size);
        }

        Entry self = res.key.orElse(null);
        if (self == null && !ClubRanker.isClubRelated(rankId.getId())) {
            TopRankInfo.Builder builder = getScoreFetcher(rankId, seasonConf).fetch(rankId, false, player).orElse(null);
            if (builder != null) {
                self = new Entry(builder, Rankers.getSelfPublicInfo(player, rankingConf));
            }
        }

        if (self != null) {
            rspMsg.setSelfRank(self.rankInfo);
            if (!reqMsg.getWithoutInfo()) {
                if (ClubRanker.isClubRelated(rankId.getId())) {
                    rspMsg.setSelfClubInfo(self.clubInfo);
                } else {
                    rspMsg.setSelfInfo(self.publicInfo);
                }
            }
        }

        return rspMsg;
    }

    public CsRank.FetchAroundRank_S2C_Msg.Builder handleFetchReq(CsRank.FetchAroundRank_C2S_Msg reqMsg) {
        CsRank.FetchAroundRank_S2C_Msg.Builder rspMsg = CsRank.FetchAroundRank_S2C_Msg.newBuilder()
                .setId(reqMsg.getId())
                .setType(reqMsg.getType());

        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            NKErrorCode errorCode = NKErrorCode.RankIsBanned;
            BanStatus banStatus = player.getBanStatus(BanType.BT_Rank);
            if (banStatus != null && banStatus.getBanBefore() > DateUtils.currentTimeMillis()) {
                player.sendBanInfoNtf(BanType.BT_Rank, banStatus.getBanBefore(), errorCode,
                        banStatus.getBanReason(), true);
            }
        }

        RankingConf rankingConf = RankingConfData.getInstance().get(reqMsg.getId());
        if (rankingConf == null) {
            LOGGER.error("cannot find rank conf for rank, rank:{}", reqMsg.getId());
            NKErrorCode.ResNotFound.throwError("no ranking conf found");
            return rspMsg;
        }

        NKErrorCode errorCode = Rankers.checkRankListAccess(reqMsg.getId());
        if (errorCode.hasError()) {
            LOGGER.error("failed to pass check, uid:{} rankId:{} err:{}", player.getUid(), reqMsg.getId(), errorCode);
            errorCode.throwError("failed to pass check");
            return rspMsg;
        }

        if (rankingConf.getRule() == RankRule.RR_Qualify_Challenger) {
            QualifyingDegreeInfo qualifyingInfo = player.getQualifyingManager()
                    .getQualifyingInfo(rankingConf.getDescId());
            if (!QDTDegreeTypeData.getInstance()
                    .isKingDegreeBySeason(qualifyingInfo.getSeasonId(), qualifyingInfo.getQualifyingInt())) {
                LOGGER.debug("non challenger try to get challenger rank no, uid:{}", player.getUid());
                return rspMsg;
            }
        }

        if (!reqMsg.getWithoutInfo() && reqMsg.getDown() + reqMsg.getUp() + 1 > 20) {
            LOGGER.error("ask too many info, rank:{} up:{} down:{}", reqMsg.getId(), reqMsg.getUp(), reqMsg.getDown());
            NKErrorCode.RankInfoFetchExceedLimit.throwError("ask too many info");
            Monitor.getInstance().add.fail(MonitorId.attr_rank_info_fetch_exceed_limit, 1,
                    new String[]{Long.toString(reqMsg.getId())});
            return rspMsg;
        }

        long currentMs = DateUtils.currentTimeMillis();
        Ranker ranker = getRanker(reqMsg.getType());

        var seasonConf = RankIdSeasonIdMapper.ofTs(reqMsg.getId(), currentMs / 1000);
        if (seasonConf == null) {
            NKErrorCode.ResNotFound.throwError("season conf not found");
            return rspMsg;
        }

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(reqMsg.getId());
        int apolloId = RankIdApolloIdMapper.getApolloId(reqMsg.getId(), TconndApiAccount.TCONND_ITOP_CHANNEL_WX,
                seasonConf);
        if (apolloId != 0) {
            rankIdBuilder.setApolloId(apolloId);
        }

        if (reqMsg.getType() == RankType.RT_Geo) {
            rankIdBuilder.setSubType(reqMsg.getGeoLevel().getNumber()).setSubId(reqMsg.getGeoCode());
            rspMsg.setGeoLevel(reqMsg.getGeoLevel()).setGeoCode(reqMsg.getGeoCode());

            if (!LBSCodeUtils.verifyAdministrationCode(reqMsg.getGeoLevel(), reqMsg.getGeoCode())) {
                LOGGER.error("invalid lbs code and refuse access, uid:{} level:{} code:{}", player.getUid(),
                        reqMsg.getGeoLevel(), reqMsg.getGeoCode());
                Monitor.getInstance().add.fail(MonitorId.attr_rank_geo_code_invalid, 1);
                return rspMsg;
            }
        }

        var rankId = rankIdBuilder.build();
        var res = ranker.getSelfRankIfInCacheWithAroundRankList(rankId, reqMsg.getUp(), reqMsg.getDown(),
                reqMsg.getWithoutInfo(), currentMs);

        for (Entry top : res.value.key) {
            rspMsg.addRank(top.rankInfo);
            if (ClubRanker.isClubRelated(rankId.getId())) {
                rspMsg.addClubInfo(top.clubInfo);
            } else {
                rspMsg.addInfo(top.publicInfo);
            }
        }

        Entry self = res.key.orElse(null);
        if (self == null && !ClubRanker.isClubRelated(rankId.getId())) {
            TopRankInfo.Builder builder = getScoreFetcher(rankId, seasonConf).fetch(rankId, false, player).orElse(null);
            if (builder != null) {
                self = new Entry(builder, Rankers.getSelfPublicInfo(player, rankingConf));
            }
        }

        if (self != null) {
            rspMsg.setSelfRank(self.rankInfo);
            if (ClubRanker.isClubRelated(rankId.getId())) {
                rspMsg.setSelfClubInfo(self.clubInfo);
            } else {
                rspMsg.setSelfInfo(self.publicInfo);
            }
        }

        return rspMsg;
    }

    public CsRank.FetchTopRankByScore_S2C_Msg.Builder handleFetchReq(CsRank.FetchTopRankByScore_C2S_Msg reqMsg) {
        CsRank.FetchTopRankByScore_S2C_Msg.Builder rspMsg = CsRank.FetchTopRankByScore_S2C_Msg.newBuilder()
                .setId(reqMsg.getId()).setType(reqMsg.getType());

        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            NKErrorCode errorCode = NKErrorCode.RankIsBanned;
            BanStatus banStatus = player.getBanStatus(BanType.BT_Rank);
            if (banStatus != null && banStatus.getBanBefore() > DateUtils.currentTimeMillis()) {
                player.sendBanInfoNtf(BanType.BT_Rank, banStatus.getBanBefore(), errorCode,
                        banStatus.getBanReason(), true);
            }
        }

        RankingConf rankingConf = RankingConfData.getInstance().get(reqMsg.getId());
        if (rankingConf == null) {
            LOGGER.error("cannot find rank conf for rank, rank:{}", reqMsg.getId());
            NKErrorCode.ResNotFound.throwError("no ranking conf found");
            return rspMsg;
        }

        RankingRuleConf ruleConf = RankingRuleConfData.getInstance().get(rankingConf.getRule());
        if (ruleConf == null) {
            LOGGER.error("cannot find rank rule conf for rank, rank:{}", reqMsg.getId());
            NKErrorCode.ResNotFound.throwError("no ranking conf found");
            return rspMsg;
        }

        Ranker ranker = getRanker(reqMsg.getType());

        long currentMs = DateUtils.currentTimeMillis();
        var seasonConf = RankIdSeasonIdMapper.ofTs(reqMsg.getId(), currentMs / 1000);
        if (seasonConf == null) {
            NKErrorCode.ResNotFound.throwError("season conf not found");
            return rspMsg;
        }

        NKErrorCode errorCode = Rankers.checkRankListAccess(rankingConf.getRankId());
        if (errorCode.hasError()) {
            errorCode.throwErrorIfNotOk("failed to pass access check, uid:{} rankId:{} err:{}", player.getUid(),
                    reqMsg.getId(), errorCode);
            return rspMsg;
        }

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(reqMsg.getId());
        TconndApiAccount account = player.getAccountType();

        if (reqMsg.getAccount() != 0) {
            account = TconndApiAccount.forNumber(reqMsg.getAccount());
            if (account == null) {
                NKErrorCode.InvalidParams.throwError("invalid account, type:{}", reqMsg.getAccount());
                return rspMsg;
            }

            rspMsg.setAccount(reqMsg.getAccount());
        }

        int apolloId = RankIdApolloIdMapper.getApolloId(reqMsg.getId(), account, seasonConf);
        if (apolloId != 0) {
            rankIdBuilder.setApolloId(apolloId);
        }

        if (reqMsg.getType() == RankType.RT_Geo) {
            rankIdBuilder.setSubType(reqMsg.getGeoLevel().getNumber()).setSubId(reqMsg.getGeoCode());
            rspMsg.setGeoLevel(reqMsg.getGeoLevel()).setGeoCode(reqMsg.getGeoCode());
        }

        offerPreFetchRefreshJob(reqMsg.getId(), currentMs);

        RankId rankId = rankIdBuilder.build();
        List<Integer> scores = Lists.newArrayList();
        int reqSize = reqMsg.getFromScoreCount();

        for (int i = 0; i < ruleConf.getScoreFieldLength(); i++) {
            scores.add(i < reqSize ? reqMsg.getFromScore(i) : 0);
        }

        var res = ranker.getSelfRankByScoreIfInCacheWithTopRankList(rankId, scores,
                reqMsg.getCount(), reqMsg.getWithoutInfo(), false);

        for (Entry top : res.value.key) {
            rspMsg.addRank(top.rankInfo);
            if (!reqMsg.getWithoutInfo()) {
                rspMsg.addInfo(top.publicInfo);
            }
        }

        rspMsg.setSize(res.value.value);

        Entry self = res.key.orElse(null);
        if (self == null && !ClubRanker.isClubRelated(rankId.getId())) {
            TopRankInfo.Builder builder = getScoreFetcher(rankId, seasonConf).fetch(rankId, false, player).orElse(null);
            if (builder != null) {
                self = new Entry(builder, Rankers.getSelfPublicInfo(player, rankingConf));
            }
        }

        if (self != null) {
            rspMsg.setSelfRank(self.rankInfo);
            if (!reqMsg.getWithoutInfo()) {
                if (ClubRanker.isClubRelated(rankId.getId())) {
                    rspMsg.setSelfClubInfo(self.clubInfo);
                } else {
                    rspMsg.setSelfInfo(self.publicInfo);
                }
            }
        }

        return rspMsg;
    }

    public ChangePlayerRankGeoInfo_S2C_Msg.Builder handleLBSReq(ChangePlayerRankGeoInfo_C2S_Msg reqMsg) {
        ChangePlayerRankGeoInfo_S2C_Msg.Builder resBuilder = ChangePlayerRankGeoInfo_S2C_Msg.newBuilder();

        PlayerRankGeoInfo lbs = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        long currentMs = DateUtils.currentTimeMillis();

        if (!LBSCodeUtils.verifyAdministrationCodes(reqMsg.getNation(), reqMsg.getProvince(), reqMsg.getCity(),
                reqMsg.getTown())) {
            LOGGER.error("failed to pass lbs param check, uid:{} nation:{} province:{} city:{} town:{}",
                    player.getUid(), reqMsg.getNation(), reqMsg.getProvince(), reqMsg.getCity(), reqMsg.getTown());
            NKErrorCode.RankLBSCodeInvalid.throwError("failed to pass lbs param check");
            return resBuilder;
        }

        if (lbs.getNation() == reqMsg.getNation() && lbs.getProvince() == reqMsg.getProvince()
                && lbs.getCity() == reqMsg.getCity() && lbs.getTown() == reqMsg.getTown()) {
            LOGGER.debug("lbs info not changed at all, uid:{}", player.getUid());
            return resBuilder;
        }

        if (DateUtils.isSameWeek(lbs.getLastUpdateTs(), currentMs)) {
            LOGGER.error("already change lbs in this week, uid:{} lastTs:{}", player.getUid(), lbs.getLastUpdateTs());
            NKErrorCode.RankGeoChangeReachLimit.throwError("change lbs too frequently");
            return resBuilder;
        }

        for (RankInfoReportStatus status : player.getUserAttr().getRankData().getRankReportStatus().values()) {
            if (status.getGeoStatus() != RankReportStatus.RRS_BANNED) {
                status.setGeoStatus(RankReportStatus.RRS_RETRY);
            }
        }

        // PlayerReportUtil.reportPlayerLbs(player, PlayerReportUtil.getLbsString(lbs));

        proto_PlayerRankGeoInfo.Builder oldInfo = lbs.getCopyDbBuilder();
        lbs.setNation(reqMsg.getNation()).setProvince(reqMsg.getProvince()).setCity(reqMsg.getCity())
                .setTown(reqMsg.getTown()).setIsOpen(true).setLastUpdateTs(currentMs);
        player.getFriendManager().addChangeField(PlayerPublicInfoField.PLAYER_GEO_INFO);
        afterLbsChange(oldInfo, currentMs);
        return resBuilder;
    }

    public DeletePlayerRankGeoInfo_S2C_Msg.Builder handleLBSReq(DeletePlayerRankGeoInfo_C2S_Msg reqMsg) {
        DeletePlayerRankGeoInfo_S2C_Msg.Builder resBuilder = DeletePlayerRankGeoInfo_S2C_Msg.newBuilder();
//
//        PlayerRankGeoInfo lbs = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
//
//        if (!lbs.getIsOpen()) {
//            LOGGER.debug("lbs not open right now, uid:{}", player.getUid());
//            return resBuilder;
//        }
//
//        for (RankInfoReportStatus status : player.getUserAttr().getRankData().getRankReportStatus().values()) {
//            if (status.getGeoStatus() != RankReportStatus.RRS_BANNED) {
//                status.setGeoStatus(RankReportStatus.RRS_RETRY);
//            }
//        }
//
//        long currentMs = DateUtils.currentTimeMillis();
//        proto_PlayerRankGeoInfoOrBuilder oldInfo = lbs.getCopyDbBuilder();
//        lbs.setIsOpen(false).setNation(0).setProvince(0).setCity(0).setTown(0).setLastUpdateTs(currentMs);
//
//        afterLbsChange(oldInfo, currentMs);
        return resBuilder;
    }

    public void deleteLBS() {
        PlayerRankGeoInfo lbs = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();

        if (!lbs.getIsOpen()) {
            LOGGER.debug("lbs not open right now, uid:{}", player.getUid());
            return;
        }

        for (RankInfoReportStatus status : player.getUserAttr().getRankData().getRankReportStatus().values()) {
            if (status.getGeoStatus() != RankReportStatus.RRS_BANNED) {
                status.setGeoStatus(RankReportStatus.RRS_RETRY);
            }
        }

        long currentMs = DateUtils.currentTimeMillis();
        proto_PlayerRankGeoInfo.Builder oldInfo = lbs.getCopyDbBuilder();
        lbs.clear().setIsOpen(false);
        player.getUserAttrMgr().collectAndSyncDirtyToClient();

        afterLbsChange(oldInfo, currentMs);
    }

    private void afterLbsChange(proto_PlayerRankGeoInfo.Builder oldInfo, long currentMs) {
        player.getUserAttr().getRankData().getLastGeoInfo().copyFromDto(oldInfo.build());
        player.getUserAttr().getRankData().setLastGeoChangeTs(currentMs);

        for (var rankItemInfo : player.getUserAttr().getPlayerPublicGameData().getRankInfo().values()) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankItemInfo.getType());
            if (rankingConf == null) {
                continue;
            }

            RankSeasonInfo seasonInfo = RankIdSeasonIdMapper.ofTs(rankItemInfo.getType(), currentMs / 1000);
            if (seasonInfo == null) {
                continue;
            }

            offerPreFetchRefreshJob(rankingConf.getRankId(), currentMs);
        }
    }

    public Map<Integer, Integer> getLastWeekGeoInfo(long currentMs) {
        var info = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (DateUtils.isSameWeek(currentMs, player.getUserAttr().getRankData().getLastGeoChangeTs())) {
            // geo changed this week, use last geo info
            info = player.getUserAttr().getRankData().getLastGeoInfo();
        }

        return RankAttrUtils.getRankSubTypesSubIds(info);
    }

    public Map<Integer, Integer> getThisWeekGeoInfo(long currentMs) {
        var info = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        return RankAttrUtils.getRankSubTypesSubIds(info);
    }

    private Ranker getRanker(RankType type) {
        switch (type) {
            case RT_Friend:
                return friendRanker;
            case RT_Global:
                return globalRanker;
            case RT_Geo:
                return geoRanker;
            default:
                return Ranker.DENIED;
        }
    }

    private boolean checkSelfRes(TopRankInfo info, List<Integer> expected) {
        return info.getScoreCount() == expected.size() && IntStream.range(0, expected.size())
                .allMatch(i -> Objects.equals(info.getScore(i), expected.get(i)));
    }

    private NKPair<List<Entry>, Optional<Entry>> getRankListAndSelf(Ranker ranker, RankId rankId, int from, int limit) {
        var res = ranker.getSelfRankIfInCacheWithTopRankList(rankId, from, limit, false, false, false,
                DateUtils.currentTimeMillis());
        return new NKPair<>(res.value.key, res.key);
    }

    private NKPair<Boolean, NKErrorCode> checkRankInfoItem(int rankId, int seasonId, List<Integer> scoreFields) {
        RankInfoItem rankInfoItem = getRankInfoItem(rankId);

        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            LOGGER.error("no season conf found, uid:{} rank:{} season:{}", player.getUid(), rankId, seasonId);
            return new NKPair<>(false, NKErrorCode.ResNotFound);
        }

        NKPair<RankUpdatePolicy, NKErrorCode> res = checkRankInfoItem(rankInfoItem, seasonConf);
        if (res.key == RankUpdatePolicy.RUP_Unknown) {
            LOGGER.error("player {} failed to find update policy for rank {}", player.getUid(), rankId);
            return new NKPair<>(false, NKErrorCode.ResNotFound);
        }

        // check if the rank is updated recently
        if (rankInfoItem.getUpdateTimeMs() == 0) {
            LOGGER.debug("player {} has no recent rank record on {}", player.getUid(), rankInfoItem.getType());
            resetRankInfoReportStatus(rankInfoItem.getType());
            return new NKPair<>(true, NKErrorCode.OK);
        }

        // compare ranks scores
        int[] newScores = scoreFields.stream().mapToInt(Integer::intValue).toArray();
        int[] oldScores = rankInfoItem.getScoreFieldsList().stream().mapToInt(Integer::intValue).toArray();
        int ret = SkipListRankCache.compareScoreFields(oldScores, newScores);
        if (ret == 0) {
            LOGGER.debug("player {} rank {} score={} and score={} is the same", player.getUid(), rankId, oldScores,
                    scoreFields);
            return new NKPair<>(false, NKErrorCode.OK);
        }

        LOGGER.debug("player {} is comparing {} with {} of rank {}", player.getUid(), rankInfoItem.getScoreFieldsList(),
                scoreFields, rankId);

        switch (res.key) {
            case RUP_When_Different:
                return new NKPair<>(true, NKErrorCode.OK);
            case RUP_When_Smaller:
                return new NKPair<>(ret > 0, NKErrorCode.OK);
            case RUP_When_Greater:
                return new NKPair<>(ret < 0, NKErrorCode.OK);
            default:
                return new NKPair<>(false, NKErrorCode.OK);
        }
    }

    private NKPair<RankUpdatePolicy, NKErrorCode> checkRankInfoItem(RankInfoItem rankInfoItem,
            RankSeasonInfo seasonConf) {
        // check if the rank id is valid
        RankingConf conf = RankingConfData.getInstance().get(rankInfoItem.getType());
        if (conf == null) {
            LOGGER.error("cannot find conf for rank {}", rankInfoItem.getType());
            return new NKPair<>(RankUpdatePolicy.RUP_Unknown, NKErrorCode.ResNotFound);
        }

        RankingRuleConf rule = RankingRuleConfData.getInstance().get(conf.getRule());
        if (rule == null || rule.getUpdatePolicy() == RankUpdatePolicy.RUP_Unknown) {
            LOGGER.error("cannot find update policy for rank {}", rankInfoItem.getType());
            return new NKPair<>(RankUpdatePolicy.RUP_Unknown, NKErrorCode.ResNotFound);
        }

        // refresh if season changed
        if (rule.getRefreshWhenSeasonChange() && rankInfoItem.getUpdateSeason() < seasonConf.getId()) {
            LOGGER.debug("rank is out of date and remove it, uid:{} rank:{} season:{}", player.getUid(),
                    rankInfoItem.getType(), rankInfoItem.getUpdateSeason());
            rankInfoItem.clear();
            resetRankInfoReportStatus(rankInfoItem.getType());
        }

        return new NKPair<>(rule.getUpdatePolicy(), NKErrorCode.OK);
    }

    private Map<RankType, SetResult> refreshRank(int rankId, List<Integer> scoreFields, int seasonId) {
        player.tryUnbanRank();
        // locate season
        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(), seasonConf);
        if (apolloId == 0 || seasonConf == null) {
            LOGGER.error("failed to find apolloId, uid:{} rankId:{} season:{}", player.getUid(), rankId, seasonId);
            return ofFailureUpdateReturn(NKErrorCode.ResNotFound);
        }

        long endTs = seasonConf.getEndSec() * 1000;
        long currentMs = DateUtils.currentTimeMillis();

        if (seasonConf.hasEndSec() && endTs < currentMs) {
            LOGGER.error(
                    "failed to update score due to deadline, uid:{} rankId:{} season:{} end_ts:{} deadline:{}",
                    player.getUid(), rankId, seasonConf.getId(), endTs, endTs);
            return ofFailureUpdateReturn(NKErrorCode.RankUpdateTsReachDeadline);
        }

        var ruleConf = Rankers.getRuleConf(rankId).orElse(null);
        if (ruleConf == null) {
            LOGGER.error("failed to find rule conf for rank, uid:{} rankId:{}", player.getUid(), rankId);
            return ofFailureUpdateReturn(NKErrorCode.ResNotFound);
        }

        if (ruleConf.hasBatchSnapshot()) {
            var spConf = ruleConf.getBatchSnapshot();
            if ((currentMs / 1000) <= seasonConf.getStartSec() + spConf.getDelayShowHours() * DateUtils.ONE_HOUR_SECONDS) {
                if (!spConf.getUpdateCurSeasonBeforeShow()) {
                    var lastSeasonConf = RankIdSeasonIdMapper.ofLast(rankId, currentMs / 1000);
                    if (lastSeasonConf != null) {
                        LOGGER.info("skip update score since snapshot not show yet, uid:{} rankId:{} season:{}",
                            player.getUid(), rankId, lastSeasonConf.getId());
                        return ofFailureUpdateReturn(NKErrorCode.RankSettlementOnFlight);
                    }
                }
            }
        }

        RankId rank = RankId.newBuilder().setId(rankId).setApolloId(apolloId).build();

        NKPair<Boolean, NKErrorCode> infoResult = checkRankInfoItem(rankId, seasonId, scoreFields);
        if (!infoResult.value.isOk()) {
            LOGGER.error("failed to refresh rank for player {} on rank {}: {}", player.getUid(), rankId,
                    infoResult.value);
            return ofFailureUpdateReturn(infoResult.value);
        }

        boolean isChanged = infoResult.key;
        boolean isInit = getRankInfoItem(rankId).getUpdateTimeMs() == 0;
        LOGGER.debug("player {} rank score {} at rank {} isChange:{} isInit:{}", player.getUid(), scoreFields, rankId,
                isChanged, isInit);
        RankInfoItem rankInfoItem = getRankInfoItem(rankId);
        RankInfoReportStatus status = getRankInfoReportStatus(rankId);

        // history item update, regardless of banned or not
        if (isChanged && RankSettlementUtils.getSnapshotRewardConf(rankId, seasonId) != null) {
            var historyItem = getRankHistoryItemAndUpdateRefreshTs(rankId, seasonId);
            historyItem.setRefreshTs(currentMs);
            for (int score : scoreFields) {
                historyItem.addScores(score);
            }
        }

        // check if hide all from others
        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            rankInfoItem.setHide(true);
            unmarkParticipated(rankId);
            status.setGlobalStatus(RankReportStatus.RRS_BANNED).setGeoStatus(RankReportStatus.RRS_BANNED);
        }

        Map<RankType, SetResult> res = new HashMap<>();
        var fResult = friendRanker.trySetSelfRank(rank, isChanged, status, currentMs, scoreFields, seasonId);
        res.put(RankType.RT_Friend, fResult);

        if (fResult.errorCode == NKErrorCode.RankUpdateNotSupport) {
            LOGGER.error("refresh score not support, uid:{} rankId:{}", player.getUid(), rankId);
            return res;
        }

        markParticipated(rankId, seasonId, rankInfoItem);

        RankRule rule = RankingConfData.getInstance().get(rankId).getRule();
        if (isInit) {
            boolean skip = false;
            if (rule == RankRule.RR_Qualify) {
                skip = PropertyFileReader.getRealTimeBooleanItem("rank_upload_qualify_delay_init", false);
            } else if (rule == RankRule.RR_Fashion) {
                skip = PropertyFileReader.getRealTimeBooleanItem("rank_upload_fashion_delay_init", false);
            }

            if (skip) {
                LOGGER.warn("delay init rank score to backend, uid:{} rankId:{} rule:{}", player.getUid(), rankId,
                        rule);
                if (status.getGlobalStatus() == RankReportStatus.RRS_OK) {
                    status.setGlobalStatus(RankReportStatus.RRS_RETRY);
                }
                res.put(RankType.RT_Global, SetResult.ofFailure(NKErrorCode.RankUpdateFused));

                if (geoRanker.isOpen()) {
                    if (status.getGeoStatus() == RankReportStatus.RRS_OK) {
                        status.setGeoStatus(RankReportStatus.RRS_RETRY);
                    }
                    res.put(RankType.RT_Geo, SetResult.ofFailure(NKErrorCode.RankUpdateFused));
                }

                return res;
            }
        }

        res.putAll(
                refreshScoresToBackend(rank, rankInfoItem, status, isChanged, seasonId, currentMs));
        return res;
    }

    private NKErrorCode preFetchRefresh(int rankId) {
        player.tryUnbanRank();
        if (player.getUserAttr().getPlayerPublicGameData().getRankInfo(rankId) == null) {
            LOGGER.debug("player {} has no record on {}, skip refresh", player.getUid(), rankId);
            return NKErrorCode.OK;
        }

        RankInfoReportStatus status = getRankInfoReportStatus(rankId);
        if (status.getGlobalStatus() == RankReportStatus.RRS_OK && status.getGeoStatus() == RankReportStatus.RRS_OK) {
            LOGGER.debug("player {} on rank {} is fully updated, no need to refresh", player.getUid(), rankId);
            return NKErrorCode.OK;
        }

        boolean hide = player.getUserAttr().getRankData().getHideAllFromOthers();
        if (hide || status.getGlobalStatus() == RankReportStatus.RRS_BANNED
                || status.getGeoStatus() == RankReportStatus.RRS_BANNED) {
            LOGGER.info("player skip rank refresh, uid:{} rankId:{} hide:{} global:{} geo:{}", player.getUid(), rankId,
                    hide, status.getGlobalStatus(), status.getGeoStatus());
            unmarkParticipated(rankId);
            return NKErrorCode.OK;
        }

        // locate season
        var seasonConf = RankIdSeasonIdMapper.ofNow(rankId);
        int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(), seasonConf);
        if (apolloId == 0 || seasonConf == null) {
            LOGGER.error("failed to find apolloId for rankId {}", rankId);
            return NKErrorCode.ResNotFound;
        }

        RankInfoItem rankInfoItem = getRankInfoItem(rankId);
        NKErrorCode errorCode = checkRankInfoItem(rankInfoItem, seasonConf).value;
        if (!errorCode.isOk()) {
            LOGGER.error("player {} failed to refresh rank info item on rank {}", player.getUid(), rankId);
            return errorCode;
        }

        if (rankInfoItem.getUpdateTimeMs() == 0) {
            LOGGER.debug("player {} has no recent rank record on {}", player.getUid(), rankId);
            resetRankInfoReportStatus(rankId);
            return NKErrorCode.OK;
        }

        markParticipated(rankId, seasonConf.getId(), rankInfoItem);

        RankId rank = RankId.newBuilder().setId(rankId).setApolloId(apolloId).build();
        Map<RankType, SetResult> res = refreshScoresToBackend(rank, rankInfoItem, status, false,
                seasonConf.getId(), DateUtils.currentTimeMillis());
        for (Map.Entry<RankType, SetResult> entry : res.entrySet()) {
            if (!entry.getValue().errorCode.isOk()) {
                LOGGER.error("player {} failed to retry to refresh rank {} to {} backend: {}", player.getUid(), rankId,
                        entry.getKey(), entry.getValue().errorCode);
                return entry.getValue().errorCode;
            }

            LOGGER.debug("player {} retry to refresh rank {} to {} backend", player.getUid(), rankId, entry.getKey());
        }

        return NKErrorCode.OK;
    }

    private Map<RankType, SetResult> refreshScoresToBackend(RankId rank, RankInfoItem rankInfoItem,
            RankInfoReportStatus status, boolean isChanged, int seasonId, long updateMs) {

        Map<RankType, SetResult> res = new HashMap<>();
        res.put(RankType.RT_Global,
                globalRanker.trySetSelfRank(rank, isChanged, status, updateMs, rankInfoItem.getScoreFieldsList(),
                        seasonId));
        res.put(RankType.RT_Geo,
                geoRanker.trySetSelfRank(rank, isChanged, status, updateMs, rankInfoItem.getScoreFieldsList(),
                        seasonId));

        return res;
    }

    private NKErrorCode banRankScores(List<Integer> rankIds) {
        // mark as ban
        for (int rankId : rankIds) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null) {
                LOGGER.error("detect unknown rank id, uid:{} rankId:{}", player.getUid(), rankId);
                continue;
            }

            if (rankingConf.getRule() == RankRule.RR_Ugc_Exp) {
                LOGGER.error("ugc rank score is not editable by RankManager, uid:{} rankId:{}", player.getUid(),
                        rankId);
                continue;
            }

            removeRefreshJob(rankId);
            friendRanker.hideFromFriends(rankId);

            unmarkParticipated(rankId);
            getRankInfoReportStatus(rankId).setGlobalStatus(RankReportStatus.RRS_BANNED)
                    .setGeoStatus(RankReportStatus.RRS_BANNED);
        }

        // remove from apollo
        long currentMs = DateUtils.currentTimeMillis();
        List<Integer> apolloIds = rankIds.stream()
                .map(rankId -> RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(),
                        RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000)))
                .filter(id -> id != 0).collect(Collectors.toList());

        NKErrorCode errApollo = BackendConsole.getInstance()
                .removeOneUserApollo(player.getUid(), apolloIds, geoRanker.getAllInvolvingSubRankIds());

        if (!errApollo.isOk()) {
            LOGGER.error("failed to remove rank scores from apollo, uid:{} rankIds:{} err:{}", player.getUid(), rankIds,
                    errApollo);
        }

        // remove from zset
        NKErrorCode errZSet = BackendConsole.getInstance().removeOneUserZSet(player.getUid(), apolloIds);
        if (!errZSet.isOk()) {
            LOGGER.error("failed to remove rank scores from zset, uid:{} rankIds:{} err:{}", player.getUid(), rankIds,
                    errZSet);
        }

        return errApollo.isOk() ? errZSet : errApollo;
    }

    private NKErrorCode unbanRankScores(List<Integer> rankIds) {
        // mark as unban
        for (int rankId : rankIds) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null || rankingConf.getRule() == RankRule.RR_Ugc_Exp) {
                LOGGER.warn("detect illegal rank id and remove, uid:{} rankId:{}", player.getUid(), rankId);
                player.getUserAttr().getPlayerPublicGameData().removeRankInfo(rankId);
                player.getUserAttr().getRankData().removeRankReportStatus(rankId);

                continue;
            }

            friendRanker.unHideFromFriends(rankId);
            var status = getRankInfoReportStatus(rankId);
            if (status.getGlobalStatus() == RankReportStatus.RRS_BANNED) {
                status.setGlobalStatus(RankReportStatus.RRS_RETRY);
            }

            if (status.getGeoStatus() == RankReportStatus.RRS_BANNED) {
                status.setGeoStatus(RankReportStatus.RRS_RETRY);
            }
            offerPreFetchRefreshJob(rankId, DateUtils.currentTimeMillis());
        }

        return NKErrorCode.OK;
    }

    private RankInfoReportStatus getRankInfoReportStatus(int rankId) {
        RankInfoReportStatus status = player.getUserAttr().getRankData().getRankReportStatus(rankId);
        if (status == null) {
            status = new RankInfoReportStatus();
            status.setRankId(rankId);
            player.getUserAttr().getRankData().putRankReportStatus(rankId, status);
        }

        return status;
    }

    private void resetRankInfoReportStatus(int rankId) {
        RankInfoReportStatus status = player.getUserAttr().getRankData().getRankReportStatus(rankId);
        if (status != null && status.getGlobalStatus() != RankReportStatus.RRS_BANNED
                && status.getGeoStatus() != RankReportStatus.RRS_BANNED) {
            status.clear();
        }
    }

    private RankInfoItem getRankInfoItem(int rankId) {
        RankInfoItem status = player.getUserAttr().getPlayerPublicGameData().getRankInfo(rankId);
        if (status == null) {
            status = new RankInfoItem();
            status.setType(rankId);
            player.getUserAttr().getPlayerPublicGameData().putRankInfo(rankId, status);
        }

        return status;
    }

    private void clearObsoleteSeasonRelatedData() {

        long currentSec = DateUtils.currentTimeSec();
        Set<Integer> deleted = new HashSet<>();

        for (var rankItem : player.getUserAttr().getPlayerPublicGameData().getRankInfo().values()) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankItem.getType());
            if (rankingConf == null) {
                deleted.add(rankItem.getType());
                continue;
            }

            if (hasParticipated(rankItem.getType(), rankItem.getUpdateSeason())) {
                updateHistoryScore(rankItem.getType(), rankItem.getUpdateSeason(), rankItem);
            }

            RankSeasonInfo seasonInfo = RankIdSeasonIdMapper.ofTs(rankItem.getType(), currentSec);
            if (seasonInfo != null && seasonInfo.getId() != rankItem.getUpdateSeason()) {
                deleted.add(rankItem.getType());
                continue;
            }
        }

        if (!deleted.isEmpty()) {
            LOGGER.warn("player clear obsolete rank data, uid:{} deleted:{}", player.getUid(), deleted);
            for (int rankId : deleted) {
                player.getUserAttr().getPlayerPublicGameData().removeRankInfo(rankId);
                resetRankInfoReportStatus(rankId);
            }
        }
    }

    private List<Integer> mapToApolloIds(List<Integer> rankIds) {
        long currentSec = DateUtils.currentTimeSec();
        return rankIds.stream().map(rankId -> RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(),
                RankIdSeasonIdMapper.ofTs(rankId, currentSec))).collect(Collectors.toList());
    }

    private void retryZSetAfterLogin(long currentMs) {
        if (RankListCacheClient.skipRedis()) {
            LOGGER.info("skip redis rank resent due to skipRedis option, uid:{}", player.getUid());
            return;
        }

        updateChallengerRankNoIfNeedy(QualifyType.QT_Main_VALUE, true);
    }

    private void retryAfterLogin(RankRule rankRule, long currentMs) {
        for (int rankId : RankingConfData.getInstance().getByRule(rankRule)) {
            RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null) {
                continue;
            }

            offerPreFetchRefreshJob(rankingConf.getRankId(), currentMs);
        }

        if (!player.getUserAttr().getRankData().getHideAllFromOthers()) {
            List<Integer> rankIds = Lists.newArrayList();
            for (var status : player.getUserAttr().getRankData().getRankReportStatus().values()) {
                if (status.getGlobalStatus() == RankReportStatus.RRS_BANNED
                        || status.getGeoStatus() == RankReportStatus.RRS_BANNED) {
                    rankIds.add(status.getRankId());
                }
            }

            if (!rankIds.isEmpty()) {
                LOGGER.warn("unban rank ids, uid:{} rankIds:{}", player.getUid(), rankIds);
                unbanRankScores(rankIds);
            }
        }
    }

    private void updateChallengerRankNoIfNeedy(int qualifyType, boolean upload) {
        var qualify = player.getQualifyingManager().getQualifyMgr(qualifyType);
        RankingConf rankingConf = RankingConfData.getInstance()
                .getByDescId(RankRule.RR_Qualify_Challenger, qualifyType);
        if (qualify != null && rankingConf != null) {
            QualifyingDegreeInfo qualifyingInfo = qualify.getQualifyingInfo();
            if (!QDTDegreeTypeData.getInstance()
                    .isKingDegreeBySeason(qualifyingInfo.getSeasonId(), qualifyingInfo.getQualifyingInt())) {
                return;
            }
            int rankId = rankingConf.getRankId();
            int seasonId = qualify.getQualifyingInfo().getSeasonId();
            RankSeasonInfo currentInfo = RankIdSeasonIdMapper.ofNow(rankId);

            if (upload && currentInfo != null && seasonId == currentInfo.getId()) {
                int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player.getAccountType(), currentInfo);

                var rankItem = getRankInfoItem(rankId);
                if (player.getUserAttr().getRankData().getHideAllFromOthers() || rankItem.getHide()) {
                    LOGGER.info("skip reset zset score due to hide, uid:{} rankId:{}", player.getUid(),
                            rankItem.getType());
                } else if (rankItem.getUpdateSeason() != currentInfo.getId()) {
                    LOGGER.info("skip reset zset score due to out-of-date, uid:{} rankId:{}", player.getUid(),
                            rankItem.getType());
                } else if (apolloId != 0) {
                    var rankBuilder = RankId.newBuilder().setId(rankId).setApolloId(apolloId);
                    RedisZSetUploader.getInstance()
                            .upload(seasonId, List.of(rankBuilder.build()), player.getUid(),
                                    rankItem.getUpdateTimeMs(), rankItem.getScoreFieldsList());
                    LOGGER.info("zset score uploaded, uid:{} rankId:{}", player.getUid(), rankId);
                }
            }

            var rankNo = BackendConsole.getInstance()
                    .getZSetRankNo(player.getUid(), player.getAccountType(), qualifyType, seasonId);

            if (rankNo.value.isOk()) {
                qualify.updateAttrQualifyingLatestRankNo(rankNo.key);
                LOGGER.info("refresh challenger degree by rank, uid:{} rankNo:{} qualify:{}", player.getUid(), rankNo,
                        qualifyType);
            }
        }
    }

    public int freshPlayerRankInfo(FreshPlayerRanksInfo info) {
        if (info.getPlayerRanksCount() <= 0) {
            return NKErrorCode.OK.value;
        }

        // 获取当前玩法类型ID
        long currentSec = DateUtils.currentTimeSec();
        boolean checkSeason = PropertyFileReader.getRealTimeBooleanItem("omd_rank_check_reason", false);
        for (RankInfoPbItem freshRankInfo : info.getPlayerRanksList()) {
            ResRanking.RankingConf rankingConf = RankingConfData.getInstance().get(freshRankInfo.getKey());
            if (rankingConf == null) {
                LOGGER.error("rank conf not found, uid:{} rankId:{}", player.getUid(), freshRankInfo.getKey());
                continue;
            }

            RankSeasonInfo season = RankIdSeasonIdMapper.ofTs(rankingConf.getRankId(), currentSec);
            if (season == null) {
                LOGGER.error("rank season not found, battleId:{} rankId:{}", player.getUid(), rankingConf.getRankId());
                continue;
            }

            if (checkSeason) {
                ResSeason.SeasonConf curSeasonConf = SeasonConfData.getInstance().getCurrSeasonConf();
                if (curSeasonConf == null) {
                    LOGGER.error("rank season curSeasonConf is null, battleId:{} rankId:{}"
                            , player.getUid(), rankingConf.getRankId());
                    continue;
                }
                if (!freshRankInfo.hasSeasonId()) {
                    LOGGER.error("rank season freshRankInfo dont't have season, battleId:{} rankId:{}"
                            , player.getUid(), rankingConf.getRankId());
                    continue;
                }
                if (freshRankInfo.getSeasonId() != curSeasonConf.getSeasonId()) {
                    LOGGER.error("rank season freshRankInfoSeason:{} != curSeason:{}, battleId:{} rankId:{}"
                            , freshRankInfo.getSeasonId(), curSeasonConf.getSeasonId()
                            , player.getUid(), rankingConf.getRankId());
                    continue;
                }
            }

            player.getRankManager().refresh(freshRankInfo.getKey(), freshRankInfo.getValue(), season.getId());
            if (freshRankInfo.getKey() >= 3007 && freshRankInfo.getKey() < 3100) {
                int scoreLevel = freshRankInfo.getValue() / 100000;
                Monitor.getInstance().add.succ(MonitorId.attr_tyc_tds_player_kill_player_num, 1,
                        new String[]{String.valueOf(scoreLevel)});
            }
            LOGGER.info("freshPlayerRankInfo log, uid: {}  rankId: {}, score: {}",
                    player.getUid(), freshRankInfo.getKey(), freshRankInfo.getValue());
        }

        return NKErrorCode.OK.value;
    }

    public DsPlayer.GetPlayerRankInfoReply.Builder getPlayerRankInfo(DsPlayer.GetPlayerRankInfoRequest req) {
        DsPlayer.GetPlayerRankInfoReply.Builder res = DsPlayer.GetPlayerRankInfoReply.newBuilder();
        res.setSeasonId(player.getSeasonId());
        if (req.getRankIdsCount() > 0) {
            for (int rankId : req.getRankIdsList()) {
                RankInfoItem rankInfoItem = getRankInfoItem(rankId);
                if (rankInfoItem.getUpdateSeason() > 0 && rankInfoItem.getScoreFieldsSize() > 0) {
                    RankInfoPbItem.Builder pbItem = RankInfoPbItem.newBuilder()
                            .setKey(rankId).setValue(rankInfoItem.getScoreFields(0))
                            .setSeasonId(rankInfoItem.getUpdateSeason());
                    res.addRankIds(pbItem);
                }
            }
        }

        return res;
    }

    @Override
    public void prepareRegister() throws NKCheckedException {
    }

    @Override
    public void onRegister() throws NKCheckedException {
    }

    @Override
    public void afterRegister() throws NKCheckedException {
    }

    @Override
    public void prepareLoad() throws NKCheckedException {
    }

    @Override
    public void onLoad() throws NKCheckedException {
    }

    @Override
    public void afterLoad() {
    }

    @Override
    public void prepareLogin() throws NKCheckedException {
    }

    @Override
    public void onLogin() throws NKCheckedException {
        List<Integer> rankIds = new ArrayList<>();

        for (var item : player.getUserAttr().getRankData().getRefreshQueueDb().values()) {
            offerRefreshJob(item.getType(), item.getUpdateSeason(), Lists.newArrayList(item.getScoreFieldsList()));
            rankIds.add(item.getType());
        }

        player.getUserAttr().getRankData().clearRefreshQueueDb();
        if (!rankIds.isEmpty()) {
            LOGGER.warn("find some refresh job not finish and offer again, uid:{} rankIds:{}",
                    player.getUid(), rankIds);
        }
    }

    @Override
    public void afterLogin(boolean todayFirstLogin) {
        long currentMs = DateUtils.currentTimeMillis();
        if (PropertyFileReader.getRealTimeBooleanItem("rank_upload_challenger_after_login", false)) {
            retryZSetAfterLogin(currentMs);
        }

        retryAfterLogin(RankRule.RR_Qualify, currentMs);
        retryAfterLogin(RankRule.RR_Fashion, currentMs);
        // 活动排行榜 如果刷新失败 也在登陆重试下
        retryAfterLogin(RankRule.RR_Activity_Descending, currentMs);

        offerAllRewardJobs();
        refreshQualifyDailyZSetRankNo(currentMs);

        clearObsoleteSeasonRelatedData();
        resetLBSSettingsIfInvalid();
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {
        preFetchRefreshForGeo();
    }

    @Override
    public void onLogout() {
        if (!refreshQueue.isEmpty()) {
            List<Integer> rankIds = new ArrayList<>();

            for (var req : refreshQueue) {
                RankInfoItem item = new RankInfoItem();
                item.setType(req.rankId).setUpdateSeason(req.seasonId);
                req.scores.forEach(item::addScoreFields);

                player.getUserAttr().getRankData().putRefreshQueueDb(req.rankId, item);
                rankIds.add(req.rankId);
            }

            refreshQueue.clear();
            LOGGER.error("find some refresh job not finish and persist for next login, uid:{} rankIds:{}",
                    player.getUid(), rankIds);
        }
    }

    @Override
    public void onMidNight() {
    }

    @Override
    public void onWeekStart() {
    }

    @Override
    public void onEveryHourStart() {
        offerAllRewardJobs();
        refreshQualifyDailyZSetRankNo(DateUtils.currentTimeMillis());
    }

    public void onlineProc() {
        long currentMs = DateUtils.currentTimeMillis();
        long itv = currentMs - lastProcTs;
        if (itv >= PropertyFileReader.getRealTimeLongItem("rank_proc_itv", 500)) {
            try {
                processRefreshJobAsync();
            } catch (Exception e) {
                LOGGER.error("failed to process refresh job, uid:{} err:{}", player.getUid(), e);
            }

            lastProcTs = currentMs;
        }

        itv = currentMs - lastRewardProcTs;
        if (itv >= PropertyFileReader.getRealTimeLongItem("rank_proc_itv", 1000)) {
            try {
                processRewardJobAsync();
            } catch (Exception e) {
                LOGGER.error("failed to process reward job, uid:{} err:{}", player.getUid(), e);
            }

            lastRewardProcTs = currentMs;
        }
    }

    private void offerRefreshJob(int rankId, int seasonId, List<Integer> scores) {
        var descriptor = new RefreshDescriptor(rankId, seasonId, scores);
        var iter = refreshQueue.iterator();
        while (iter.hasNext()) {
            var other = iter.next();
            if (other.rankId == descriptor.rankId) {
                if (other.seasonId > descriptor.seasonId) {
                    LOGGER.error("drop obsolete offer, uid:{} rankId:{} other_season:{} offer_season:{}",
                            player.getUid(), rankId, other.seasonId, descriptor.seasonId);
                    return;
                }

                LOGGER.info("remove obsolete refresh job, uid:{} rankId:{} old:[{},{}] new:[{},{}]", player.getUid(),
                        rankId, other.seasonId, other.scores, descriptor.seasonId, descriptor.scores);
                iter.remove();
            }
        }
        refreshQueue.offerLast(descriptor);
        preFetchRefreshQueue.removeIf(r -> r == rankId);
    }

    private void offerPreFetchRefreshJob(int rankId, long currentMs) {
        long expired = PropertyFileReader.getRealTimeLongItem("rank_retry_refresh_itv", 2000L);
        if (lastPreFetchRefresh.containsKey(rankId) && currentMs - lastPreFetchRefresh.get(rankId) < expired) {
            LOGGER.info("retry refresh too frequently, uid:{} rank:{}", player.getUid(), rankId);
            return;
        }

        // mark refresh time
        lastPreFetchRefresh.put(rankId, currentMs);

        for (int otherRankId : preFetchRefreshQueue) {
            if (otherRankId == rankId) {
                LOGGER.info("find on flight pre fetch refresh job, uid:{} rankId:{}", player.getUid(), rankId);
                return;
            }
        }

        for (var other : refreshQueue) {
            if (other.rankId == rankId) {
                LOGGER.debug("find on flight refresh job and skip, uid:{} rankId:{}", player.getUid(), rankId);
                return;
            }
        }

        preFetchRefreshQueue.offerLast(rankId);
    }

    private void removeRefreshJob(int rankId, int seasonId) {
        var iter = refreshQueue.iterator();
        while (iter.hasNext()) {
            var other = iter.next();
            if (other.rankId == rankId && other.seasonId <= seasonId) {
                LOGGER.info("remove obsolete refresh job, uid:{} rankId:{} season:{} score:{}", player.getUid(), rankId,
                        other.seasonId, other.scores);
                iter.remove();
            }
        }

        preFetchRefreshQueue.removeIf(r -> r == rankId);
    }

    private void removeRefreshJob(int rankId) {
        refreshQueue.removeIf(r -> r.rankId == rankId);
        preFetchRefreshQueue.removeIf(r -> r == rankId);
    }

    private void processRefreshJobAsync() throws NKCheckedException {
        if (refreshQueue.isEmpty() && preFetchRefreshQueue.isEmpty()) {
            return;
        }

        CurrentExecutorUtil.runJob(this::processRefreshJob, "rank_proc", true);
    }

    private boolean processRefreshJob() {
        var descriptor = refreshQueue.pollFirst();
        if (descriptor != null) {
            refreshRank(descriptor.rankId, descriptor.scores, descriptor.seasonId);
            return true;
        }

        Integer rankId = preFetchRefreshQueue.pollFirst();
        if (rankId != null) {
            preFetchRefresh(rankId);
        }

        return false;
    }

    private void markParticipated(int rankId, int seasonId, RankInfoItem rankItem) {
        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            return;
        }

        var attr = getSettlement(rankId);

        while (attr.getParticipatedSeasons().size() > 5) {
            var list = Lists.newArrayList(attr.getParticipatedSeasonsList());
            int earliest = Collections.min(list);
            attr.removeParticipatedSeasons(earliest);
        }

        attr.addParticipatedSeasons(seasonId);

        updateHistoryScore(rankId, seasonId, rankItem);
    }

    private void updateHistoryScore(int rankId, int seasonId, RankInfoItem rankItem) {
        if (!hasParticipated(rankId, seasonId)) {
            LOGGER.warn("skip update history score since not participated, uid:{} rankId:{} seasonId:{}",
                    player.getUid(), rankId, seasonId);
            return;
        }

        long currentMs = DateUtils.currentTimeMillis();
        var history = getHistory(rankId);

        var seasonItem = history.getItems(seasonId);
        if (seasonItem == null) {
            seasonItem = new RankHistoryItem();
            seasonItem.setSeasonId(seasonId);
            history.putItems(seasonId, seasonItem);
        }

        seasonItem.clearScores();
        for (int score : rankItem.getScoreFieldsList()) {
            seasonItem.addScores(score);
        }

        seasonItem.setRefreshTs(currentMs);
        history.setRefreshTs(currentMs);

        // sync with participatedSeasonIds

        List<Integer> deleted = Lists.newArrayList();
        for (var item : history.getItems().values()) {
            if (hasParticipated(rankId, item.getSeasonId())) {
                continue;
            }

            deleted.add(item.getSeasonId());
        }

        if (!deleted.isEmpty()) {
            LOGGER.info("delete history items, uid:{} rankId:{} seasonIds:{}", player.getUid(), rankId, deleted);
            deleted.forEach(history::removeItems);
        }
    }

    private void unmarkParticipated(int rankId) {
        getSettlement(rankId).clearParticipatedSeasons();
        getHistory(rankId).clearItems();
    }

    private PlayerRankSettlement getSettlement(int rankId) {
        PlayerRankSettlement info = player.getUserAttr().getRankData().getSettleInfos(rankId);
        if (info == null) {
            info = new PlayerRankSettlement();
            info.setRankId(rankId);
            player.getUserAttr().getRankData().putSettleInfos(rankId, info);
        }

        return info;
    }

    private RankHistory getHistory(int rankId) {
        var history = player.getUserAttr().getPlayerPublicHistoryData().getHistoryRankInfo(rankId);
        if (history == null) {
            history = new RankHistory();
            history.setRankId(rankId);
            player.getUserAttr().getPlayerPublicHistoryData().putHistoryRankInfo(rankId, history);
        }

        return history;
    }

    private boolean hasParticipated(int rankId, int seasonId) {
        PlayerRankSettlement info = player.getUserAttr().getRankData().getSettleInfos(rankId);
        if (info == null) {
            return false;
        }

        return info.getParticipatedSeasons().contains(seasonId);
    }

    private List<Integer> getHistoryScore(int rankId, int seasonId) {
        if (!hasParticipated(rankId, seasonId)) {
            LOGGER.debug("not participated, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId, seasonId);
            return Lists.newArrayList();
        }

        var history = player.getUserAttr().getPlayerPublicHistoryData().getHistoryRankInfo(rankId);
        if (history == null) {
            return Lists.newArrayList();
        }

        var item = history.getItems(seasonId);
        return item == null ? Lists.newArrayList() : Lists.newArrayList(item.getScoresList());
    }

    private void offerAllRewardJobs() {
        player.tryUnbanRank();

        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            LOGGER.error("player is banned, no snapshot reward should given, uid:{}", player.getUid());
            return;
        }

        List<NKPair<Integer, Integer>> rewardRankIdAndSeasonId = new ArrayList<>();

        for (var info : player.getUserAttr().getRankData().getSettleInfos().values()) {
            if (RankingConfData.getInstance().isOMDEndlessRule(info.getRankId())) {
                if (!info.getParticipatedSeasons().contains(5)
                        && !info.getRewardedSeasons().contains(5)
                        && info.getParticipatedSeasons().contains(1)) {
                    LOGGER.warn("before fix omd endless rank uid {} rank id {} info {}", player.getUid(),
                            info.getRankId(), info);
                    info.addParticipatedSeasons(5);
                    LOGGER.warn("after fix omd endless rank uid {} rank id {} info {}", player.getUid(),
                            info.getRankId(), info);
                }
            }
        }

        for (var info : player.getUserAttr().getRankData().getSettleInfos().values()) {
            Set<Integer> seasonIds = Sets.newHashSet(info.getParticipatedSeasonsList());
            seasonIds.removeAll(info.getRewardedSeasonsList());
            for (int seasonId : seasonIds) {
                if (RankSettlementUtils.getSnapshotRewardConf(info.getRankId(), seasonId) != null) {
                    rewardRankIdAndSeasonId.add(new NKPair<>(info.getRankId(), seasonId));
                }
            }
        }

        if (!rewardRankIdAndSeasonId.isEmpty()) {
            LOGGER.info("check reward for player, uid:{} reward:{}", player.getUid(), rewardRankIdAndSeasonId);
            for (var rs : rewardRankIdAndSeasonId) {
                int rankId = rs.key;
                int seasonId = rs.value;

                NKErrorCode errorCode = RankSettlementUtils.checkSnapshotAccess(rankId, seasonId);
                if (errorCode.isOk()) {
                    offerRewardJob(rankId, seasonId);
                    continue;
                }

                if (errorCode == NKErrorCode.RankSettlementExpire) {
                    onSettlementRewardExpired(rankId, seasonId);
                }
            }
        }
    }

    private void offerRewardJob(int rankId, int seasonId) {
        for (var kv : rewardQueue) {
            if (kv.key == rankId && kv.value == seasonId) {
                LOGGER.debug("found the same reward job, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId,
                        seasonId);
                return;
            }
        }

        if (rewardQueue.size() > PropertyFileReader.getRealTimeIntItem("rank_list_snapshot_reward_proc_size", 50)) {
            LOGGER.error("too much reward job remained, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId,
                    seasonId);
            return;
        }

        rewardQueue.offerLast(new NKPair<>(rankId, seasonId));
    }

    private void processRewardJobAsync() throws NKCheckedException {
        if (rewardQueue.isEmpty()) {
            return;
        }

        CurrentExecutorUtil.runJob(this::processRewardJob, "rank_reward_proc", true);
    }

    private boolean processRewardJob() {
        var rankIdSeasonId = rewardQueue.pollFirst();
        if (rankIdSeasonId == null) {
            return false;
        }

        if (player.getUserAttr().getRankData().getHideAllFromOthers()) {
            LOGGER.error("player is banned from reward, uid:{}", player.getUid());
            return false;
        }

        int rankId = rankIdSeasonId.key;
        int seasonId = rankIdSeasonId.value;

        if (shouldReward(rankId, seasonId)) {
            var conf = RankSettlementUtils.getSnapshotRewardConf(rankId, seasonId);
            if (conf == null) {
                LOGGER.error("failed to get settlement conf, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId,
                        seasonId);
                return false;
            }

            var seasonInfo = RankIdSeasonIdMapper.ofId(rankId, seasonId);
            if (seasonInfo == null) {
                LOGGER.error("failed to get season conf, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId,
                        seasonId);
                return false;
            }
            seasonId = seasonInfo.getId();

            NKErrorCode errorCode = RankSettlementUtils.checkSnapshotAccess(rankId, seasonId);
            if (errorCode.hasError()) {
                LOGGER.error("fail to get snapshot, uid:{} rankId:{} seasonId:{} err:{}", player.getUid(), rankId,
                        seasonId, errorCode);

                // if expire, mark
                if (errorCode == NKErrorCode.RankSettlementExpire) {
                    onSettlementRewardExpired(rankId, seasonId);
                    return true;
                }

                return false;
            }

            NKPair<NKPair<Integer, Integer>, NKErrorCode> res = BackendConsole.getInstance()
                    .getSnapshotRankNoAndSize(player.getUid(), rankId, seasonId, 0);
            if (!res.value.isOk()) {
                LOGGER.error("failed to get rank no, uid:{} rankId:{} seasonId:{} err:{}", player.getUid(), rankId,
                        seasonId, res.value);
                sendTlogFlow(rankId, seasonId, 0, 0, res.value);
                return false;
            }

            int rankNo = res.key.key;
            int size = res.key.value;

            RankHistoryItem historyItem = null;

            // find corresponding reward range and send rewards
            size = RankSettlementUtils.regularizeSettlementRankSize(conf, size);
            var rangeConf = RankSettlementUtils.findRewardRangeConf(conf, rankNo, size);
            if (rangeConf == null) {
                LOGGER.error("no range conf find, uid:{} rankId:{} seasonId:{} rankNo:{} size:{}", player.getUid(),
                        rankId, seasonId, rankNo, size);

                sendTlogFlow(rankId, seasonId, rankNo, size, NKErrorCode.OK);
                historyItem = markRewarded(rankId, seasonId, rankNo, 0);
            } else {
                MailAttachmentList.Builder attachmentList = MailAttachmentList.newBuilder();
                for (var item : rangeConf.getRewardItemsList()) {
                    var itemInfo = ItemInfo.newBuilder().setItemId(item.getItemId()).setItemNum(item.getItemNum());
                    long expireMs = item.getExpireDays() * DateUtils.ONE_DAY_MILLIS
                            + item.getExpireHours() * DateUtils.ONE_HOUR_MILLIS;

                    if (expireMs > 0) {
                        itemInfo.setExpireType(ItemExpireType.IET_RELATIVE_VALUE).setExpireTimeMs(expireMs);
                    }

                    attachmentList.addList(MailAttachment.newBuilder().setItemIfo(itemInfo));
                }

                List<String> params = new ArrayList<>();
                for (var param : rangeConf.getMailParamsList()) {
                    try {
                        params.add(param.replace("#", Integer.toString(rankNo)));
                    } catch (Exception e) {
                        LOGGER.error("failed to parse mail param, uid:{} rankId:{} conf:{} priority:{} err:{}",
                                player.getUid(), rankId, conf.getId(), rangeConf.getPriority(), e);
                        params.add(param);
                    }
                }

                long mailId = 0L;
                if (params.isEmpty()) {
                    mailId = MailInteraction.sendTemplateMail(player.getUid(), rangeConf.getMailId(), attachmentList,
                            TlogSendReason.rankSettlement);
                } else {
                    mailId = MailInteraction.sendTemplateMail(player.getUid(), rangeConf.getMailId(), attachmentList,
                            TlogSendReason.rankSettlement, params.toArray(new String[0]));
                }

                LOGGER.info("rank settlement reward sent, uid:{} rankId:{} seasonId:{} rankNo:{} size:{} priority:{}",
                        player.getUid(), rankId, seasonId, rankNo, size, rangeConf.getPriority());

                sendTlogFlow(rankId, seasonId, rankNo, size, attachmentList, mailId);
                historyItem = markRewarded(rankId, seasonId, rankNo, rangeConf.getPriority());
            }

            // dispatch event
            proto_RankHistoryItem.Builder builder = proto_RankHistoryItem.newBuilder();
            historyItem.copyToSs(builder);
            new PlayerRankSettlementEvent(player, rankId, builder, size, conf.getId(),
                    rangeConf == null ? 0 : rangeConf.getPriority()).dispatch();

            return true;
        }

        LOGGER.debug("detect duplicated reward, uid:{} rankId:{} season:{}", player.getUid(), rankId, seasonId);
        return false;
    }

    private void onSettlementRewardExpired(int rankId, int seasonId) {
        LOGGER.error("snapshot expire and not be mentioned anymore, uid:{} rankId:{} seasonId:{}", player.getUid(),
                rankId, seasonId);

        sendTlogFlow(rankId, seasonId, 0, 0, NKErrorCode.RankSettlementExpire);
        markRewarded(rankId, seasonId, 0, -1);
    }

    private RankHistoryItem markRewarded(int rankId, int seasonId, int rankNo, int reason) {
        RankHistoryItem historyItem = getRankHistoryItemAndUpdateRefreshTs(rankId, seasonId);

        historyItem.setRewardRankNo(rankNo).setRewardTs(DateUtils.currentTimeMillis()).setRewardReason(reason);
        getSettlement(rankId).getRewardedSeasons().add(seasonId);

        return historyItem;
    }

    private boolean shouldReward(int rankId, int seasonId) {
        var settlement = getSettlement(rankId);
        return settlement.getParticipatedSeasons().contains(seasonId) && !settlement.getRewardedSeasons()
                .contains(seasonId);
    }

    private RankHistoryItem getRankHistoryItemAndUpdateRefreshTs(int rankId, int seasonId) {
        var history = player.getUserAttr().getPlayerPublicHistoryData().getHistoryRankInfo(rankId);
        if (history == null) {
            history = new RankHistory();
            history.setRankId(rankId);
            player.getUserAttr().getPlayerPublicHistoryData().putHistoryRankInfo(rankId, history);
        }

        var item = history.getItems(seasonId);
        if (item == null) {
            item = new RankHistoryItem();
            item.setSeasonId(seasonId);
            history.putItems(seasonId, item);
        }

        history.setRefreshTs(DateUtils.currentTimeMillis());
        return item;
    }

    private void sendTlogFlow(int rankId, int seasonId, int rankNo, int size,
            MailAttachmentListOrBuilder attachmentList, long mailId) {

        StringJoiner joiner = new StringJoiner(";");
        for (var attachment : attachmentList.getListList()) {
            joiner.add(attachment.getItemIfo().getItemId() + ":" + attachment.getItemIfo().getItemNum());
        }

        TlogFlowMgr.sendRankSnapshotRewardFlow(player, rankId, seasonId, rankNo, size, joiner.toString(),
                mailId, NKErrorCode.OK);
    }

    private void sendTlogFlow(int rankId, int seasonId, int rankNo, int size, NKErrorCode errorCode) {
        TlogFlowMgr.sendRankSnapshotRewardFlow(player, rankId, seasonId, rankNo, size, "", 0, errorCode);
    }

    private void resetLBSSettingsIfInvalid() {
        var lbs = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (!lbs.getIsOpen() || LBSCodeUtils.verifyAdministrationCodes(lbs.getNation(), lbs.getProvince(),
                lbs.getCity(), lbs.getTown())) {
            return;
        }

        if (!PropertyFileReader.getRealTimeBooleanItem("rank_lbs_auto_reset", true)) {
            return;
        }

        LOGGER.error("detect invalid lbs config and reset, uid:{} nation:{} province:{} city:{} town:{}",
                player.getUid(), lbs.getNation(), lbs.getProvince(), lbs.getCity(), lbs.getTown());

        Monitor.getInstance().add.succ(MonitorId.attr_rank_geo_reset, 1);

        var oldLbs = lbs.getCopyDbBuilder();
        long currentMs = DateUtils.currentTimeMillis();

        lbs.clear().setIsOpen(false).setLastUpdateTs(currentMs);

        for (RankInfoReportStatus status : player.getUserAttr().getRankData().getRankReportStatus().values()) {
            if (status.getGeoStatus() != RankReportStatus.RRS_BANNED) {
                status.setGeoStatus(RankReportStatus.RRS_RETRY);
            }
        }

        player.getFriendManager().addChangeField(PlayerPublicInfoField.PLAYER_GEO_INFO);
        afterLbsChange(oldLbs, currentMs);
    }

    private void refreshQualifyDailyZSetRankNo(long currentMs) {
        for (int rankId : RankingConfData.getInstance().getByRule(RankRule.RR_Qualify_Challenger)) {
            var rankingConf = RankingConfData.getInstance().get(rankId);
            if (rankingConf == null || !rankingConf.hasDzSnapshot()) {
                continue;
            }

            var seasonInfo = RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000);
            if (seasonInfo == null || DateUtils.isSameDay(currentMs, seasonInfo.getStartSec() * 1000)) {
                continue;
            }

            var qualify = player.getQualifyingManager().getQualifyMgr(rankingConf.getDescId());
            if (qualify == null) {
                continue;
            }

            var info = qualify.getAttrQualifyingInfo();
            if (info.getDegreeTypeInt() != QualifyingDegreeType.QDT_Challenger_VALUE) {
                continue;
            }

            if (!hasParticipated(rankId, seasonInfo.getId())) {
                LOGGER.debug("no rank score participated yet, uid:{} rank:{}", player.getUid(), rankId);
                continue;
            }

            var status = getRankInfoReportStatus(rankId);
            if (status.getGlobalStatus() == RankReportStatus.RRS_BANNED
                    || status.getGeoStatus() == RankReportStatus.RRS_BANNED) {
                LOGGER.info("score is banned, uid:{} rank:{}", player.getUid(), rankId);
                continue;
            }

            int tag = RankSettlementUtils.ofDailyZSetSnapshotTag(currentMs,
                    rankingConf.getDzSnapshot().getTriggerTime());
            if (tag == info.getDailyRankTag()) {
                LOGGER.debug("daily tag is updated already, uid:{} qualify:{} tag:{}", player.getUid(),
                        info.getDegreeTypeInt(), tag);
                continue;
            }

            LOGGER.debug("prepare to get daily zset snapshot rank no, uid:{} rank:{} season:{} tag:{}", player.getUid(),
                    rankId, seasonInfo.getId(), tag);

            var res = BackendConsole.getInstance()
                    .getDailyZSetSnapshotRankNoFromRedis(player.getUid(), rankId, seasonInfo.getId(), tag);
            if (res.value.hasError()) {
                LOGGER.error("failed to get daily zset snapshot rank no, uid:{} rank:{} season:{} tag:{} err:{}",
                        player.getUid(), rankId, seasonInfo.getId(), tag, res.value);
                continue;
            }

            info.setDailyRankNo(res.key).setDailyRankTag(tag);
            LOGGER.info("daily tag updated, uid:{} qualify:{} tag:{} rank_no:{}", player.getUid(),
                    rankingConf.getDescId(), tag, info.getDailyRankNo());

            long expireTs = RankSettlementUtils.ofDailyZSetSnapshotExpireMs(currentMs,
                    rankingConf.getDzSnapshot().getTriggerTime(), seasonInfo);

            var dailyEquip = player.getUserAttr().getPlayerPublicEquipments()
                    .getQualifyDailyRankInfos(rankingConf.getDescId());
            if (dailyEquip == null) {
                dailyEquip = new QualifyingDailyRankInfo();
                dailyEquip.setQualifyType(rankingConf.getDescId());
                player.getUserAttr().getPlayerPublicEquipments()
                        .putQualifyDailyRankInfos(dailyEquip.getQualifyType(), dailyEquip);
            }

            dailyEquip.setRankNo(res.key).setExpireMs(expireTs);
            LOGGER.info("daily equip updated, uid:{} qualify:{} expire:{} rank_no:{}", player.getUid(),
                    dailyEquip.getQualifyType(), expireTs, dailyEquip.getRankNo());
        }
    }

    private void preFetchRefreshForGeo() {
        var lbs = player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo();
        if (!lbs.getIsOpen() || LBSCodeUtils.verifyAdministrationCodes(lbs.getNation(), lbs.getProvince(),
                lbs.getCity(), lbs.getTown())) {
            return;
        }

        boolean hide = player.getUserAttr().getRankData().getHideAllFromOthers();
        if (hide) {
            return;
        }

        List<Integer> rankIds = geoRanker.getDivergedRankIds();
        long currentMs = DateUtils.currentTimeMillis();
        LOGGER.warn("detect diverged geo rank items, uid:{} rankIds:{}", player.getUid(), rankIds);
        for (int rankId : rankIds) {
            var status = getRankInfoReportStatus(rankId);
            if (status.getGlobalStatus() == RankReportStatus.RRS_BANNED
                    || status.getGeoStatus() == RankReportStatus.RRS_BANNED) {
                continue;
            }

            status.setGeoStatus(RankReportStatus.RRS_RETRY);
            offerPreFetchRefreshJob(rankId, currentMs);
        }
    }

    private List<Integer> getParticipatedRankIds(List<Integer> rankIds, int seasonId) {
        List<Integer> res = Lists.newArrayList();
        for (int rankId : rankIds) {
            if (hasParticipated(rankId, seasonId)) {
                res.add(rankId);
            }
        }

        return res;
    }

    public NKPair<List<TopRankJointInfo>, NKErrorCode> getBatchSnapshot(RankRule rule, long currentSec) {

        var rankIds = RankingConfData.getInstance().getByRule(rule);
        if (rankIds.isEmpty()) {
            LOGGER.debug("no rank id found, uid:{} rule:{}", player.getUid(), rule);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.OK);
        }

        var seasonInfo = RankIdSeasonIdMapper.ofLast(rankIds.get(0), currentSec);
        if (seasonInfo == null) {
            LOGGER.error("season not found, uid:{} rule:{}", player.getUid(), rule);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.ResNotFound);
        }

        var activeRankIds = getParticipatedRankIds(rankIds, seasonInfo.getId());
        if (activeRankIds.isEmpty()) {
            LOGGER.info("no active rank id found, uid:{} rule:{}", player.getUid(), rule);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.OK);
        }

        LOGGER.debug("get participated rank ids, uid:{} seasonId:{} rule:{} rankIds:{}", player.getUid(),
                seasonInfo.getId(), rule, rankIds);

        TxStopWatch stopWatch = NKStopWatch.SW_BatchSnapshot.getStopWatch();

        try {
            stopWatch.mark("start");
            List<TopRankJointInfo.Builder> builders = Lists.newArrayList();

            var globalRes = BackendConsole.getInstance()
                    .getSnapshotRankNoFromRedis(rule, activeRankIds, 0, Lists.newArrayList(0), player.getUid(),
                            seasonInfo.getId());

            if (globalRes.value.hasError()) {
                stopWatch.mark("global_err");
                LOGGER.error("failed to get global, uid:{} rule:{} err:{}", player.getUid(), rule, globalRes.value);
                return new NKPair<>(Lists.newArrayList(), globalRes.value);
            }

            stopWatch.mark("global_done");

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("get snapshot global rank, uid:{} seasonId:{} rule:{} info:{}", player.getUid(),
                        seasonInfo.getId(), rule,
                        globalRes.key.stream().map(b -> b.getRankId() + ":" + b.getGlobalRankNo())
                                .collect(Collectors.joining(",", "[", "]")));
            }

            builders.addAll(globalRes.key);

            Map<Integer, Integer> geoIds = getLastWeekGeoInfo(currentSec * 1000);
            LOGGER.debug("get last week geo info, uid:{} geo:{}", player.getUid(), geoIds.entrySet());

            int province = geoIds.getOrDefault(GeoLevel.GL_Province_VALUE, 0);
            if (province != 0) {
                List<Integer> geos = Lists.newArrayList();
                geos.add(province);

                int city = geoIds.getOrDefault(GeoLevel.GL_City_VALUE, 0);
                if (city != 0) {
                    geos.add(city);
                }

                stopWatch.mark("geo_start");

                var geoRes = BackendConsole.getInstance()
                        .getSnapshotRankNoFromRedis(rule, activeRankIds, province, geos, player.getUid(),
                                seasonInfo.getId());

                if (geoRes.value.hasError()) {
                    stopWatch.mark("geo_err");
                    LOGGER.error("failed to get geo, uid:{} rule:{} err:{}", player.getUid(), rule, globalRes.value);
                    return new NKPair<>(Lists.newArrayList(), globalRes.value);
                }

                stopWatch.mark("geo_done");

                if (LOGGER.isDebugEnabled()) {
                    StringJoiner joiner = new StringJoiner(",", "[", "]");
                    for (var info : geoRes.key) {
                        var geoStr = info.getGeoRankNoBuilderList().stream()
                                .map(kv -> kv.getKey() + ":" + kv.getValue())
                                .collect(Collectors.joining(",", "[", "]"));
                        joiner.add(info.getRankId() + ":" + geoStr);
                    }

                    LOGGER.debug("get snapshot geo rank, uid:{} seasonId:{} rule:{} info:{}", player.getUid(),
                            seasonInfo.getId(), rule, joiner);
                }

                builders.addAll(geoRes.key);
            }

            stopWatch.mark("end");

            List<TopRankJointInfo> result = Lists.newArrayList();

            for (var info : RankSettlementUtils.mergeJointList(builders)) {
                int rankId = info.getRankId();
                var scores = getHistoryScore(rankId, seasonInfo.getId());
                if (scores.isEmpty()) {
                    LOGGER.error("failed to get score, uid:{} rankId:{} season:{}", player.getUid(), rankId,
                            seasonInfo.getId());
                }

                result.add(TopRankJointInfo.newBuilder(info).addAllScores(scores).build());
            }

            return new NKPair<>(result, NKErrorCode.OK);
        } finally {
            stopWatch.dump(500);
        }
    }

    public FetchBatchSnapshotInfo_S2C_Msg.Builder handleFetch(FetchBatchSnapshotInfo_C2S_Msg req) {
        FetchBatchSnapshotInfo_S2C_Msg.Builder resBuilder = FetchBatchSnapshotInfo_S2C_Msg.newBuilder()
                .setRankId(req.getRankId());

        var rankingConf = RankingConfData.getInstance().get(req.getRankId());
        if (rankingConf == null) {
            LOGGER.error("rank conf not found, uid:{} rank:{}", player.getUid(), req.getRankId());
            NKErrorCode.ResNotFound.throwError("rank conf not found, rank:{}", req.getRankId());
            return resBuilder;
        }

        long currentMs = DateUtils.currentTimeMillis();
        var seasonInfo = RankIdSeasonIdMapper.ofLast(req.getRankId(), currentMs / 1000);
        if (seasonInfo == null) {
            LOGGER.error("last season conf not found, uid:{} rank:{}", player.getUid(), req.getRankId());
            NKErrorCode.ResNotFound.throwError("last season conf not found, rank:{}", req.getRankId());
            return resBuilder;
        }

        NKErrorCode errorCode = RankSettlementUtils.checkBatchSnapshotAccess(rankingConf.getRule(), seasonInfo.getId());
        if (errorCode.hasError()) {
            LOGGER.error("failed to pass snapshot check, uid:{} rankId:{} season:{}", player.getUid(), req.getRankId(),
                    seasonInfo.getId());
            errorCode.throwError("failed to pass snapshot check, rankId:{} season:{}", req.getRankId(),
                    seasonInfo.getId());
            return resBuilder;
        }

        Map<Integer, Integer> geoIds = getLastWeekGeoInfo(currentMs);
        LOGGER.debug("get last week geo info, uid:{} geo:{}", player.getUid(), geoIds.entrySet());

        // update cache node if necessary
        NKPair<Integer, Integer> key = new NKPair<>(req.getRankId(), seasonInfo.getId());
        var node = snapshotInfoCache.get(key);
        if (node == null || Math.abs(node.ts - currentMs) > DateUtils.ONE_MINUTE_MILLIS) {
            LOGGER.debug("node expired and refresh, uid:{} key:{} old_ts:{}", player.getUid(), key,
                    node == null ? 0 : node.ts);
            node = getSnapshotInfo(key.key, key.value, geoIds, currentMs);
            snapshotInfoCache.put(key, node);
        }

        List<RankId> reqRankIds = Lists.newArrayList();
        reqRankIds.add(RankId.newBuilder().setId(req.getRankId()).build());
        geoIds.forEach(
                (k, v) -> reqRankIds.add(RankId.newBuilder().setId(req.getRankId()).setSubType(k).setSubId(v).build()));

        for (var reqRankId : reqRankIds) {
            resBuilder.addInfos(node.getBuilder(player.getUid(), reqRankId));
        }

        return resBuilder;
    }

    public FetchBatchInfo_S2C_Msg.Builder handleFetch(FetchBatchInfo_C2S_Msg req) {
        FetchBatchInfo_S2C_Msg.Builder resBuilder = FetchBatchInfo_S2C_Msg.newBuilder().setType(req.getType())
                .addAllRankIds(req.getRankIdsList());

        Map<Integer, Callable<SpecificUserTopRankInfoList.Builder>> tasks = Maps.newHashMap();
        long currentMs = DateUtils.currentTimeMillis();
        long expireMs = PropertyFileReader.getRealTimeIntItem("rank_list_batch_itv", 2) * DateUtils.ONE_SECOND_MILLIS;
        long elapseMs = Math.abs(currentMs - batchInfoTs);

        if (elapseMs < expireMs) {
            LOGGER.error("batch fetch too frequent, uid:{}", player.getUid());
            NKErrorCode.RankReqTooFrequent.throwError("batch fetch too frequent, interval:{}s", elapseMs / 1000);
            return resBuilder;
        }

        batchInfoTs = currentMs;

        Set<Integer> onFlight = Sets.newHashSet();
        for (var rankId : req.getRankIdsList()) {
            NKErrorCode errorCode = Rankers.checkRankListAccess(rankId);
            if (errorCode.hasError()) {
                if (errorCode == NKErrorCode.RankSeasonChangeOnFlight) {
                    onFlight.add(rankId);
                    continue;
                }

                LOGGER.error("failed pass access check, uid:{} rankId:{} err:{}", player.getUid(), rankId, errorCode);
                errorCode.throwError("failed pass access check, rankId:{}", rankId);
                return resBuilder;
            }
        }

        if (!onFlight.isEmpty()) {
            LOGGER.error("season on flight, uid:{} on_flight:{}", player.getUid(), onFlight);
            NKErrorCode.RankSeasonChangeOnFlight.throwError("season no flight, uid:{} on_flight:{}", player.getUid(),
                    onFlight);
            return resBuilder;
        }

        for (int rankId : req.getRankIdsList()) {
            tasks.put(rankId, () -> getSelfInternal(rankId, req.getType(), currentMs));
        }

        try {
            var results = CurrentExecutorUtil.batchSubmitJob(tasks, "batch_get_rank", true);
            for (int rankId : req.getRankIdsList()) {
                try {
                    var result = results.get(rankId);
                    if (result == null) {
                        continue;
                    }

                    var list = result.get(1000);
                    resBuilder.addSelfRanks(list);

                } catch (TimeoutException e) {
                    LOGGER.error("timeout to get self, uid:{} rank:{} type:{}", player.getUid(), rankId, req.getType());
                    var list = SpecificUserTopRankInfoList.newBuilder().setRankId(rankId);
                    resBuilder.addSelfRanks(list);
                }
            }

        } catch (NKCheckedException e) {
            LOGGER.error("failed to get rank, uid:{} rankIds:{} err:{}", player.getUid(), req.getRankIdsList(), e);
            e.getEnumErrCode().throwError("failed to get ranks");
            return resBuilder;
        }

        return resBuilder;
    }

    private SpecificUserTopRankInfoList.Builder getSelfInternal(int rankId, RankType rankType, long currentMs) {
        var builder = SpecificUserTopRankInfoList.newBuilder().setRankId(rankId);

        var seasonInfo = RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000);
        if (seasonInfo == null) {
            LOGGER.error("season not found, uid:{} rankId:{}", player.getUid(), rankId);
            return builder;
        }

        if (rankType != RankType.RT_Geo) {
            var self = SpecificUserTopRankInfo.newBuilder().setExists(false);

            var entry = getSelf(rankId, seasonInfo.getId(), GeoLevel.GL_Unknown, rankType, Source.FromCache, true);
            if (entry == null) {
                LOGGER.debug("self info not found, uid:{} rank:{} type:{}", player.getUid(), rankId, rankType);
                builder.addInfos(self);
                return builder;
            }

            LOGGER.debug("self info found, uid:{} rankId:{} type:{} rankNo:{}", player.getUid(), rankId, rankType,
                    entry.getRankNo());

            var info = TopRankInfo.newBuilder().setUid(player.getUid()).setRankNo(entry.getRankNo())
                    .addScore(entry.getScore()).addAllScore(entry.getExtraScoresList());

            self.setInfo(info).setExists(true);
            builder.addInfos(self);
            return builder;
        }

        var geo = getThisWeekGeoInfo(currentMs);
        Map<Integer, Callable<PlayerRankInfo>> tasks = Maps.newHashMap();

        for (var level : Lists.newArrayList(GeoLevel.GL_Province_VALUE, GeoLevel.GL_City_VALUE,
                GeoLevel.GL_Town_VALUE)) {
            int geoId = geo.getOrDefault(level, 0);
            if (geoId == 0) {
                continue;
            }

            tasks.put(level, () -> getSelf(rankId, seasonInfo.getId(), GeoLevel.forNumber(level), RankType.RT_Geo,
                    Source.FromCache, true));
        }

        LOGGER.debug("prepare to get self geo rank, uid:{} rank:{} levels:{}", player.getUid(), rankId, tasks.keySet());
        Map<Integer, SpecificUserTopRankInfo.Builder> specMap = Maps.newHashMap();

        try {
            var results = CurrentExecutorUtil.batchSubmitJob(tasks, "batch_geo_rank", true);
            for (var e : results.entrySet()) {
                var geoLevel = e.getKey();
                var spec = SpecificUserTopRankInfo.newBuilder().setExists(false).setSubRankType(geoLevel)
                        .setSubRankId(geo.getOrDefault(geoLevel, 0));

                try {
                    var result = e.getValue().get(1000);
                    if (result != null) {
                        var info = TopRankInfo.newBuilder().setUid(player.getUid()).setRankNo(result.getRankNo())
                                .addScore(result.getScore()).addAllScore(result.getExtraScoresList());

                        spec.setInfo(info).setExists(true);
                    }
                } catch (TimeoutException ex) {
                    LOGGER.error("timeout to get self info, uid:{} rank:{} level:{}", player.getUid(), rankId,
                            geoLevel);
                }

                specMap.put(geoLevel, spec);
            }

        } catch (NKCheckedException e) {
            LOGGER.error("failed to batch get rank, uid:{} rank:{} err:{}", player.getUid(), rankId,
                    e.getEnumErrCode());
        }

        specMap.forEach((k, v) -> builder.addInfos(v));
        return builder;
    }

    private SnapshotInfoCacheNode getSnapshotInfo(int rankId, int seasonId, Map<Integer, Integer> geoIds,
            long currentMs) {

        if (!PropertyFileReader.getRealTimeBooleanItem("rank_batch_snapshot_info_access", true)) {
            LOGGER.debug("deny of access due to realtime config, uid:{}", player.getUid());
            return new SnapshotInfoCacheNode(NKErrorCode.RankSettlementAccessDenied, currentMs);
        }

        var stopWatch = NKStopWatch.SW_Rank.getStopWatch("batch_snapshot_info");
        try {
            Map<Integer, TopRankInfo> entryMap = Maps.newHashMap();
            Map<Integer, Integer> selfMap = Maps.newHashMap();

            stopWatch.mark("start");

            // process global info

            var globalRes = BackendConsole.getInstance()
                    .getSnapshotRankNoAndEndOfListFromRedis(rankId, seasonId, 0, Lists.newArrayList(0),
                            player.getUid());
            if (globalRes.value.hasError()) {
                LOGGER.error("failed to get global, uid:{} rankId:{} err:{}", player.getUid(), rankId, globalRes.value);
                return new SnapshotInfoCacheNode(globalRes.value, currentMs);
            }

            // merge global result
            selfMap.putAll(globalRes.key.key);
            entryMap.putAll(globalRes.key.value);

            stopWatch.mark("global_ready");

            // process geo info

            int province = geoIds.getOrDefault(GeoLevel.GL_Province_VALUE, 0);
            if (province != 0) {
                List<Integer> geos = Lists.newArrayList();
                geos.add(province);

                int city = geoIds.getOrDefault(GeoLevel.GL_City_VALUE, 0);
                if (city != 0) {
                    geos.add(city);
                }

                var geoRes = BackendConsole.getInstance()
                        .getSnapshotRankNoAndEndOfListFromRedis(rankId, seasonId, province, geos, player.getUid());
                if (geoRes.value.hasError()) {
                    LOGGER.error("failed to get geo, uid:{} rankId:{} err:{}", player.getUid(), rankId, geoRes.value);
                    return new SnapshotInfoCacheNode(geoRes.value, currentMs);
                }

                // merge geo result
                selfMap.putAll(geoRes.key.key);
                entryMap.putAll(geoRes.key.value);

                stopWatch.mark("geo_eol");
            }

            List<TopRankSnapshotInfo.Builder> builders = Lists.newArrayList();

            // pack global
            var globalInfo = ofSnapshotInfo(rankId, seasonId, 0, 0, selfMap.getOrDefault(0, 0),
                    entryMap.getOrDefault(0, null));
            builders.add(globalInfo);

            // pack geo
            for (var level : Lists.newArrayList(GeoLevel.GL_Province_VALUE, GeoLevel.GL_City_VALUE)) {
                int geoCode = geoIds.getOrDefault(level, 0);
                if (geoCode == 0) {
                    continue;
                }

                var geoInfo = ofSnapshotInfo(rankId, seasonId, level, geoCode, selfMap.getOrDefault(geoCode, 0),
                        entryMap.getOrDefault(geoCode, null));
                builders.add(geoInfo);
            }

            var node = new SnapshotInfoCacheNode(builders, currentMs);

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("batch snapshot cache node updated, uid:{} rank:{} season:{} node:{}", player.getUid(),
                        rankId, seasonId, node);
            }

            return node;
        } finally {
            stopWatch.dump(500);
        }
    }

    private TopRankSnapshotInfo.Builder ofSnapshotInfo(int rankId, int seasonId, int level, int code, int rankNo,
            TopRankInfo entry) {
        RankId.Builder rank = RankId.newBuilder().setId(rankId);
        if (level != 0) {
            rank.setSubType(level).setSubId(code);
        }

        var builder = TopRankSnapshotInfo.newBuilder().setRankId(rank).setSeasonId(seasonId);
        if (entry != null) {
            TopRankInfo.Builder self = TopRankInfo.newBuilder().setUid(player.getUid());
            if (rankNo != 0) {
                self.setRankNo(rankNo);
            }

            var scores = getHistoryScore(rankId, seasonId);
            if (scores.isEmpty()) {
                LOGGER.debug("failed to get self rank score, uid:{} rankId:{} seasonId:{}", player.getUid(), rankId,
                        seasonId);
            }
            self.addAllScore(scores);

            builder.setEntry(entry).setSelf(self);
        }

        return builder;
    }

    public FetchRankNoByScore_S2C_Msg.Builder handleFetch(FetchRankNoByScore_C2S_Msg req) {
        FetchRankNoByScore_S2C_Msg.Builder resBuilder = FetchRankNoByScore_S2C_Msg.newBuilder();

        var rankingConf = RankingConfData.getInstance().get(req.getRankId());
        if (rankingConf == null) {
            LOGGER.error("failed to get rank conf, uid:{} rank:{}", player.getUid(), req.getRankId());
            NKErrorCode.ResNotFound.throwError("failed to get rank conf, rank:{}", req.getRankId());
            return resBuilder;
        }

        var ruleConf = RankingRuleConfData.getInstance().get(rankingConf.getRule());
        if (ruleConf == null) {
            LOGGER.error("failed to get rule conf, uid:{} rule:{}", player.getUid(), rankingConf.getRule());
            NKErrorCode.ResNotFound.throwError("failed to get rule conf, rank:{}", req.getRankId());
            return resBuilder;
        }

        NKErrorCode errorCode = Rankers.checkRankListAccess(rankingConf.getRankId());
        if (errorCode.hasError()) {
            LOGGER.error("failed to pass access check, uid:{} rankId:{} err:{}", player.getUid(),
                    rankingConf.getRankId(), errorCode);
            errorCode.throwError("failed to pass access check, rank:{}", req.getRankId());
            return resBuilder;
        }

        RankId.Builder rankId = RankId.newBuilder().setId(req.getRankId());
        long currentMs = DateUtils.currentTimeMillis();
        int apolloId = RankIdApolloIdMapper.getApolloId(req.getRankId(), currentMs / 1000);
        if (apolloId != 0) {
            rankId.setApolloId(apolloId);
        }

        resBuilder.setRankId(req.getRankId()).addAllScores(req.getScoresList());
        TopRankJointInfo.Builder jointInfo = TopRankJointInfo.newBuilder().setRankId(req.getRankId())
                .addAllScores(req.getScoresList());

        var originalScore = req.getScores(0);
        var adjustedScore = originalScore;

        if (ruleConf.getOrdering() == RankOrderingType.ROT_Ascending) {
            adjustedScore = originalScore == Integer.MAX_VALUE ? originalScore : (originalScore + 1);
        } else {
            adjustedScore = originalScore == 0 ? originalScore : (originalScore - 1);
        }

        LOGGER.debug("adjust score for estimation, uid:{} rankId:{} original:{} adjust:{}", player.getUid(),
                rankId.getId(), originalScore, adjustedScore);

        if (originalScore != adjustedScore) {
            int maxEstimationRankNo = PropertyFileReader.getRealTimeIntItem("rank_list_max_est_rank_no", 1000);

            final int estimateScore = adjustedScore;
            Map<Integer, Callable<NKPair<Integer, Integer>>> tasks = Maps.newHashMap();

            // add global
            tasks.put(0, () -> getScoreRankNoAndSize(globalRanker, rankId, estimateScore));

            // add geo if needed
            for (var kv : getThisWeekGeoInfo(currentMs).entrySet()) {
                RankId.Builder geoRankId = RankId.newBuilder(rankId.build()).setSubType(kv.getKey())
                        .setSubId(kv.getValue());
                tasks.put(kv.getValue(), () -> getScoreRankNoAndSize(geoRanker, geoRankId, estimateScore));
            }

            try {
                var results = CurrentExecutorUtil.batchSubmitJob(tasks, "estimate_rank_no", true);
                for (var result : results.entrySet()) {
                    int geoCode = result.getKey();
                    var res = result.getValue().get(2000);

                    int rankNo = res.key <= maxEstimationRankNo ? res.key : 0;
                    int size = res.value;

                    if (geoCode == 0) {
                        jointInfo.setGlobalRankNo(rankNo).setGlobalSize(size);
                        continue;
                    }

                    jointInfo.addGeoRankNo(KeyValueInt32.newBuilder().setKey(geoCode).setValue(rankNo));
                    jointInfo.addGeoSize(KeyValueInt32.newBuilder().setKey(geoCode).setValue(size));
                }

                resBuilder.setInfo(jointInfo);
            } catch (NKCheckedException e) {
                LOGGER.error("failed to rank no, uid:{} rankId:{} err:{}", player.getUid(), rankId.getId(), e);
                e.getEnumErrCode().throwError("failed to rank no");
                return resBuilder;
            } catch (TimeoutException e) {
                LOGGER.error("timeout to rank no, uid:{} rankId:{}", player.getUid(), rankId.getId());
                NKErrorCode.RankBackendNotResponse.throwError("failed to rank no");
                return resBuilder;
            }
        }

        return resBuilder;
    }

    private NKPair<Integer, Integer> getScoreRankNoAndSize(Ranker ranker, RankId.Builder rankId, int score) {
        var res = ranker.getSelfRankByScoreIfInCacheWithTopRankList(rankId.build(),
                Lists.newArrayList(score), 1, true, true).value;

        int rankNo = res.key.isEmpty() ? res.value + 1 : res.key.get(0).rankInfo.getRankNo();
        LOGGER.debug("estimate rank no, uid:{} rankId:{} rankNo:{}", player.getUid(), rankId.getId(), rankNo);

        return new NKPair<>(rankNo, res.value);
    }

    public enum Source {
        FromCache, FromRemote, CacheOrRemote
    }

    private static class SnapshotInfoCacheNode {

        private final Map<RankId, TopRankSnapshotInfo.Builder> builders;
        private final NKErrorCode errorCode;
        private final long ts;

        private SnapshotInfoCacheNode(List<TopRankSnapshotInfo.Builder> builders, long ts) {
            this.builders = Maps.newHashMap();
            builders.forEach(b -> this.builders.put(b.getRankId(), b));

            this.errorCode = NKErrorCode.OK;
            this.ts = ts;
        }

        private SnapshotInfoCacheNode(NKErrorCode errorCode, long ts) {
            this.builders = Maps.newHashMap();
            this.errorCode = errorCode;
            this.ts = ts;
        }

        private TopRankSnapshotInfo.Builder getBuilder(long uid, RankId rankId) {
            var res = builders.get(rankId);
            if (res != null) {
                return res;
            }

            LOGGER.debug("desired snapshot not found, uid:{} rankId:{} sub:{} err:{}", uid, rankId.getId(),
                    rankId.getSubId(), errorCode);
            return TopRankSnapshotInfo.newBuilder().setRankId(rankId);
        }

        @Override
        public String toString() {
            StringJoiner joiner = new StringJoiner(",", "[", "]");
            builders.forEach((k, v) -> joiner.add(Pb2JsonUtil.getPbMsg(k) + ":" + Pb2JsonUtil.getPbMsg(v)));

            return "SnapshotInfoCacheNode{" +
                    "builders=" + joiner +
                    ", errorCode=" + errorCode +
                    ", ts=" + ts +
                    '}';
        }
    }

    public static class RefreshDescriptor {

        private final int rankId;
        private final int seasonId;
        private final List<Integer> scores;

        private RefreshDescriptor(int rankId, int seasonId, List<Integer> scores) {
            this.rankId = rankId;
            this.seasonId = seasonId;
            this.scores = scores;
        }

        public static RefreshDescriptor of(int rankId, int seasonId, List<Integer> scores) {
            return new RefreshDescriptor(rankId, seasonId, scores);
        }

        public static RefreshDescriptor of(RankingConf rankingConf, RankSeasonInfo seasonInfo, int score) {
            return new RefreshDescriptor(rankingConf.getRankId(), seasonInfo.getId(), Lists.newArrayList(score));
        }
    }
}

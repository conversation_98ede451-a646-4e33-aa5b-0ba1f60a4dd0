package com.tencent.wea.playerservice.condition.main.xiaowo;

import com.tencent.condition.ConditionOperation;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.playerservice.condition.BasePlayerCondition;
import com.tencent.wea.playerservice.event.BasePlayerEvent;
import com.tencent.wea.playerservice.event.common.xiaowo.PlayerXiaowoWaterTreeEvent;
import com.tencent.wea.xlsRes.keywords.ConditionType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;

/**
 * <AUTHOR>
 */
public class ConditionPlayerXiaowoWaterTree extends BasePlayerCondition {

    public ConditionPlayerXiaowoWaterTree() {
        super();
    }

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerXiaowoWaterTree_VALUE;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerXiaowoWaterTree)
    private void onEvent(PlayerXiaowoWaterTreeEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    public boolean handleProgress(BasePlayerEvent event, ConditionOperation progress) {
        return progress.increaseValue();
    }
}

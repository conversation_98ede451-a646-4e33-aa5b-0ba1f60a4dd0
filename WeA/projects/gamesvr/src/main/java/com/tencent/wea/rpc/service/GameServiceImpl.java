package com.tencent.wea.rpc.service;

import com.google.common.base.Throwables;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.tencent.nk.annotation.RpcOneWay;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.commonframework.ServerEngine;
import com.tencent.nk.gamemodule.ServiceMgr;
import com.tencent.nk.transferplat.TransferPlatUtil;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.PlayerNotFoundException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BaseGenerator;
import com.tencent.nk.util.guid.CustomRoomIdGenerator;
import com.tencent.nk.util.guid.GuidType;
import com.tencent.nk.util.guid.UgcGenerator;
import com.tencent.resourceloader.resclass.VersionCompCtrlConf;
import com.tencent.rpc.RpcResult;
import com.tencent.sns.SnsUtil;
import com.tencent.tbuspp.TbusppUtil;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.tcaplus.dao.OpenIdToUidDao;
import com.tencent.tcaplus.dao.PlatFriendTableDao;
import com.tencent.tcaplus.dao.PlayerPublicDao;
import com.tencent.tcaplus.dao.PlayerPublicDao.PlayerPublicAttrKey;
import com.tencent.tcaplus.dao.RelationTableDao;
import com.tencent.timiCoroutine.LocalServiceSequentialWrapper;
import com.tencent.timiutil.errorcode.TimiEnum;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.NKStopWatch;
import com.tencent.timiutil.time.TxStopWatch;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.util.ReputationScoreUtil;
import com.tencent.wea.attr.CommonLimitInfo;
import com.tencent.wea.chatservice.Chat;
import com.tencent.wea.chatservice.Chat.BattleKey;
import com.tencent.wea.chatservice.Chat.RoomKey;
import com.tencent.wea.chatservice.GsLocalChatService;
import com.tencent.wea.framework.GSEngine;
import com.tencent.wea.framework.UgcRegisterTool;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.PlayerInteractionInvoker;
import com.tencent.wea.midas.MidasManager;
import com.tencent.wea.notice.NoticeManager;
import com.tencent.wea.playerservice.event.common.PlayerHallCoinRewardEvent;
import com.tencent.wea.playerservice.gm.GmIdipCmd;
import com.tencent.wea.playerservice.idiphandler.IdipPlayerUtil;
import com.tencent.wea.playerservice.interaction.ClientLogColoring;
import com.tencent.wea.playerservice.interaction.InteractionMgr;
import com.tencent.wea.playerservice.match.InformMatchResultMatchParams;
import com.tencent.wea.playerservice.player.AccountTransferTaskMgr;
import com.tencent.wea.playerservice.player.*;
import com.tencent.wea.playerservice.player.Player.LoadingState;
import com.tencent.wea.playerservice.playerref.BatchPlayerRef;
import com.tencent.wea.playerservice.playerref.PlayerRef;
import com.tencent.wea.playerservice.playerref.PlayerRefMgr;
import com.tencent.wea.playerservice.redpacket.RedPacketDestroyMgr;
import com.tencent.wea.playerservice.relation.OfflineRelationMgr;
import com.tencent.wea.playerservice.relation.SimpleDataManager;
import com.tencent.wea.playerservice.safety.PlayerReputationScoreMgr;
import com.tencent.wea.playerservice.tradingcard.cache.TradeInfoCache;
import com.tencent.wea.playerservice.tradingcard.manager.TradeMgr;
import com.tencent.wea.playerservice.ugc.manager.PlayerUgcManager;
import com.tencent.wea.protocol.AttrCommonLimitInfo.proto_CommonLimitInfo;
import com.tencent.wea.protocol.AttrSceneInfo;
import com.tencent.wea.protocol.CsChat.ChatGroupOperationNtf;
import com.tencent.wea.protocol.CsCompetition;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.protocol.CsRich;
import com.tencent.wea.protocol.CsXiaowo;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.RpcMsgTypes;
import com.tencent.wea.protocol.SsCommon;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.SsGamesvr.*;
import com.tencent.wea.protocol.SsGamesvr.RpcGiveSpringBlessingCardReq.Builder;
import com.tencent.wea.protocol.SsGamesvr.RpcIntellectualActivityCommonReq;
import com.tencent.wea.protocol.SsGamesvr.RpcIntellectualActivityReq;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendApplyTaskReq;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendApplyTaskRes;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendNoticeTaskReq;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendNoticeTaskRes;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendOperateTaskApplyReq;
import com.tencent.wea.protocol.SsGamesvr.RpcLuckyFriendOperateTaskApplyRes;
import com.tencent.wea.protocol.SsGamesvr.RpcMallDemandSuccessNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcMatchProcessNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcMessageSlipOpReq;
import com.tencent.wea.protocol.SsGamesvr.RpcMessageSlipOpRes;
import com.tencent.wea.protocol.SsGamesvr.RpcNoticePlayerDoJoinRoomReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNoticeTeamPartnerJoinMiniGameReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNoticeTeamPartnerJoinMiniGameRes;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyIntimacyChangeReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyPlayerOnlineStatusReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyRelationAgreeReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyRelationAgreeRes;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyRelationApplyReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyRelationApplyRes;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyStreamInteractionCmdReq;
import com.tencent.wea.protocol.SsGamesvr.RpcNotifyStreamInteractionCmdRes;
import com.tencent.wea.protocol.SsGamesvr.RpcPlatAddFriendRes;
import com.tencent.wea.protocol.SsGamesvr.RpcPlayerNoticeMsgNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcPlayerStreamSwitchReq;
import com.tencent.wea.protocol.SsGamesvr.RpcPlayerStreamSwitchRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRedPacketDestroyNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRegionClubPiiReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRegionClubPiiRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomBroadcastInfoReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomBroadcastInfoRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomLobbyGatherNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomLobbyGatherNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomMemberModifyNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomPositionExchangeNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomPositionExchangeNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomReservationNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomRoundInfoNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomStartMatchCancelNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomStartMatchCancelNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomStartMatchNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcRoomStartMatchNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneBaseDataNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneBaseDataNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionAcceptReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionAcceptRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionActiveReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionActiveRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionCancelReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionCancelRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionInviteReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInteractActionInviteRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInviteNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInviteNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInviteReplyNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSceneInviteReplyNtfRes;
import com.tencent.wea.protocol.SsGamesvr.RpcScenePlayerHallCoinRewardReq;
import com.tencent.wea.protocol.SsGamesvr.RpcScenePlayerHallCoinRewardRes;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamLLMCheckRes;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamLoginReq;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamLoginRes;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamLogoutReq;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamNpcChatReq;
import com.tencent.wea.protocol.SsGamesvr.RpcStreamNpcChatRes;
import com.tencent.wea.protocol.SsGamesvr.RpcSyncFriendInteractHistoryReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSyncScenePlayerInfoReq;
import com.tencent.wea.protocol.SsGamesvr.RpcSyncScenePlayerInfoRes;
import com.tencent.wea.protocol.SsGamesvr.RpcTeamJoinRoomConfirmNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcCoCreateEditorReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcCoCreatorDingReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcCommunityOpReportReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcCommunityOpReportRes;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcCreateRoleRes;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcGetPlayerPublicDataReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcGetPlayerPublicDataRes;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcGetRelationDataReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcGetRelationDataRes;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcLvChangeNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcMapStatusChangeNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUgcRoomPlayerRecommendMapNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUpdatePlayerUidCacheReq;
import com.tencent.wea.protocol.SsGamesvr.RpcUpdatePlayerUidCacheRes;
import com.tencent.wea.protocol.SsGamesvr.RpcVideoExamineResponseNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcXiaoWoDsInfoNtfReq;
import com.tencent.wea.protocol.SsGamesvr.RpcXiaoWoVerifyNtfReq;
import com.tencent.wea.protocol.common.BattleChatGroupKey;
import com.tencent.wea.protocol.common.BattleCreateReason;
import com.tencent.wea.protocol.common.BattleSideChatGroupKey;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.protocol.common.ChatGroupKey;
import com.tencent.wea.protocol.common.ChatType;
import com.tencent.wea.protocol.common.G6Common;
import com.tencent.wea.protocol.common.IntLongMap;
import com.tencent.wea.protocol.common.IntellectualActivityOperation;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.LongArray;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.MemberBaseInfo;
import com.tencent.wea.protocol.common.PlatFriendData;
import com.tencent.wea.protocol.common.PlatFriendList;
import com.tencent.wea.protocol.common.PlayerPublicInfo;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.RoomChatGroupKey;
import com.tencent.wea.protocol.common.TradingCardTradeInfo;
import com.tencent.wea.protocol.idip.IdipResponse;
import com.tencent.wea.protocol.idip.IdipRpcByUuidReq;
import com.tencent.wea.protocol.idip.IdipRpcByUuidRes;
import com.tencent.wea.protocol.idip.IdipRpcByZoneIdReq;
import com.tencent.wea.protocol.idip.IdipRpcByZoneIdRes;
import com.tencent.wea.protocol.idip.IdipRpcReq;
import com.tencent.wea.protocol.idip.IdipRpcRes;
import com.tencent.wea.redis.IntellectualActivityDao;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerPublic;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerUgcBuyPartnerTable;
import com.tencent.wea.tcaplus.TcaplusDb.RelationTable;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PiiClientLogColoringParams;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.xlsRes.keywords.RelationTypeEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import com.tencent.timiutil.time.DateUtils;

import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.IntStream;

public class GameServiceImpl implements GameService {

    private static final Logger LOGGER = LogManager.getLogger(GameServiceImpl.class);

    @Override
    public RpcResult<SsGamesvr.HelloRes.Builder> hello(SsGamesvr.HelloReq.Builder req) {
        LOGGER.error("hello {}", req);
        return RpcResult.create(SsGamesvr.HelloRes.newBuilder().setBody("hello" + req.getBody()));
    }

    @Override
    public RpcResult<SsCommon.PingPongRes.Builder> pingPong(SsCommon.PingPongReq.Builder req) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("ping from {}", TbusppUtil.ntoa(req.getSrc()));
        }
//        ServerEngine.getInstance().getHeartbeat().receivePing(req.getSrc());
        return RpcResult.create(SsCommon.PingPongRes.newBuilder());
    }

    @Override
    public RpcResult<SsCommon.KickOffRes.Builder> kickOff(SsCommon.KickOffReq.Builder req) {
        SsCommon.KickOffRes.Builder result = SsCommon.KickOffRes.newBuilder();
        result.setResult(ServerEngine.getInstance().getMetadataClient().doUnload(req.getType(), req.getUuid()));
        return RpcResult.create(result);
    }

    @Override
    public RpcResult<SsCommon.CacheLockPreemptRes.Builder> cacheLockPreempt(SsCommon.CacheLockPreemptReq.Builder req)
            throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.UnknownError);
    }

    @Override
    public RpcResult<SsCommon.GlobalServicesActionRes.Builder> globalServicesAction
            (SsCommon.GlobalServicesActionReq.Builder req) throws RpcException, NKTimeoutException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("ping from {}", req.toString());
        }
        return RpcResult.create(SsCommon.GlobalServicesActionRes.newBuilder());
    }

    @Override
    public RpcResult<SsCommon.SubscribeShareDataRes.Builder>
    subscribeShareData(SsCommon.SubscribeShareDataReq.Builder req) throws RpcException, NKTimeoutException {
        SsCommon.SubscribeShareDataRes.Builder result = SsCommon.SubscribeShareDataRes.newBuilder();
        return RpcResult.create(result);
    }

    @Override
    public RpcResult<SsCommon.BatchSubscribeShareDataRes.Builder>
    batchSubscribeShareData(SsCommon.BatchSubscribeShareDataReq.Builder req) throws RpcException, NKTimeoutException {
        NKErrorCode.UnsupportedShareDataType.throwError("gamesvr not support share data type {}",
                SsCommon.ShareDataType.forNumber(req.getObjectType()));
        return RpcResult.create(NKErrorCode.UnsupportedShareDataType);
    }

    @Override
    public RpcResult<SsCommon.UnsubscribeShareDataRes.Builder>
    unsubscribeShareData(SsCommon.UnsubscribeShareDataReq.Builder req) throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<SsCommon.ShareDataHeartBeatRes.Builder>
    shareDataHeartBeat(SsCommon.ShareDataHeartBeatReq.Builder req) throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.OK);
    }


    @Override
    public RpcResult<SsCommon.SyncShareDataChangeRes.Builder>
    syncShareDataChange(SsCommon.SyncShareDataChangeReq.Builder req) throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<SsCommon.ShareDataSyncRemoveSubscriberRes.Builder>
    shareDataSyncRemoveSubscriber(SsCommon.ShareDataSyncRemoveSubscriberReq.Builder req)
            throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<SsCommon.TestRes.Builder> test(SsCommon.TestReq.Builder req) {
        return RpcResult.create(SsCommon.TestRes.newBuilder().setUid(req.getUid()).setBody(req.getBody()));
    }

    @Override
    public Void rpcMatchSucc(SsGamesvr.RpcMatchSuccReq.Builder req)
            throws RpcException {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.info("rpcMatchSucc uid:{}, room:{}, req:{}",
                        req.getUid(), req.getRoomInfo().getRoomid(), req);
            }
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());

            if (req.getCreateReason() == BattleCreateReason.BCR_Migrate
                || req.getCreateReason() == BattleCreateReason.BCR_SceneChange) {
                player.informBattleDSJumpSuccess(req.getRoomInfo().getStatus(),
                        req.getGameId(), req.getModeId(), req.getPveStageId(),
                        req.getBattleInfo(), req.getResCode(), req.getErrMsg(), req.getWarmRoundType(),
                        req.getMatchDynamicConfigData(), req.getCreateReason());
            } else {
                InformMatchResultMatchParams matchParams = InformMatchResultMatchParams.builder()
                        .warmRoundType(req.getWarmRoundType())
                        .roomInfoId(req.getRoomInfoId())
                        .build();
                player.informMatchSuccess(req.getRoomInfo().getStatus(),
                        req.getGameId(), req.getModeId(), req.getPveStageId(),
                        req.getBattleInfo(), req.getResCode(), req.getErrMsg(),
                        req.getMatchDynamicConfigData(), matchParams);
            }
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcMatchSucc NKCheckedException uid:{}, room:{}, req:{}, e:{}",
                    req.getUid(), req.getRoomInfo().getRoomid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcMatchSucc PlayerNotFoundException uid:{}, room:{}, req:{}, e:{}",
                    req.getUid(), req.getRoomInfo().getRoomid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcEndBattle(SsGamesvr.RpcEndBattleReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onDestroyBattle(req.getBattleId(), req.getResult());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcEndBattle NKCheckedException uid:{}, battleid:{}, req:{}, e:{}",
                    req.getUid(), req.getBattleId(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcEndBattle PlayerNotFoundException uid:{}, battleid:{}, req:{}, e:{}",
                    req.getUid(), req.getBattleId(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcKickLoginPlayerRes.Builder> rpcKickLoginPlayer(
            SsGamesvr.RpcKickLoginPlayerReq.Builder req)
            throws RpcException, NKTimeoutException {
        int result = 0;
        // 踢线
        try {
            long playerUid = req.getPlayerUid();
            if (PlayerLogin.checkWhenRpcKick(playerUid)) {
                PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresentIgnoreStatus(playerUid);
                LocalServiceSequentialWrapper.ProtocolInfo protocolInfo = new LocalServiceSequentialWrapper.ProtocolInfo(
                        LocalServiceSequentialWrapper.ProtocolType.Kick, 0, playerUid, "");
                result = LocalServiceSequentialWrapper.get(GSEngine.getSpecInstance().getPlayerService())
                        .callJob(playerUid, ServiceMgr.DEFAULT_TIMEOUT,
                                () -> {
                                    try {
                                        LoadingState status = playerRef.getLoadingState();
                                        if (status == LoadingState.LS_Removing || status == LoadingState.LS_Registering
                                                || status == LoadingState.LS_Loading) {
                                            LOGGER.error("kick player status not allow:{}-{} ", playerUid, status);
                                            return -4;
                                        }
                                        playerRef.kickPlayer(NKErrorCode.ConcurrentLogin);
                                    } catch (PlayerNotFoundException e) {
                                        LOGGER.warn("kick player not found {}", req.getPlayerUid());
                                        return -1;
                                    } catch (NKCheckedException e) {
                                        LOGGER.error("NKCheckedException:{} ", e.getMessage());
                                        return -2;
                                    }
                                    return 0;
                                }, protocolInfo, "procKickReq", true);
            } else {
                LOGGER.error("player on login:{} ", req.getPlayerUid());
                result = -3;
            }
        } catch (NKCheckedException e) {
            LOGGER.error("NKCheckedException:{} ", e.getMessage());
            result = -2;
        }
        LOGGER.info("kickPlayer {} {}", req.getServerId(), req.getPlayerUid());
        return RpcResult.create(SsGamesvr.RpcKickLoginPlayerRes.newBuilder().setResult(result));
    }

    @Override
    public RpcResult<SsGamesvr.RpcInterPlayerInteractionRes.Builder>
    rpcInterPlayerInteraction(SsGamesvr.RpcInterPlayerInteractionReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getDest());
            if (!playerRef.isPlayerOnline()) {
                LOGGER.debug("GsImpl::rpcInterPlayerInteraction player not online uid:{}, ins:{}",
                        req.getDest(), req.getBody().getInstruction());
                return RpcResult.create(SsGamesvr.RpcInterPlayerInteractionRes.newBuilder().setResult(0));
            }
            playerRef.interact(req.getBody(), req.getSrc(), InteractionMgr.INTERACTION_SOURCE.RPC);
            return RpcResult.create(SsGamesvr.RpcInterPlayerInteractionRes.newBuilder().setResult(0));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcInterPlayerInteraction NKCheckedException uid:{}, ins:{}, e:{}",
                    req.getDest(), req.getBody().getInstruction(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcInterPlayerInteraction PlayerNotFound uid:{}, ins:{}",
                    req.getDest(), req.getBody().getInstruction());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcChatMsgNtf(RpcChatMsgNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onSendMsgNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcChatMsgNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcChatMsgNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcChatRemindNoticeNtf(RpcChatRemindNoticeNtfReq.Builder req) throws RpcException {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("player {} remind notice {}", req.getUid(), req.getMsgData());
            }
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onChatRemindNoticeNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcChatRemindNoticeNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcChatRemindNoticeNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }


    @Override
    public RpcResult<SsGamesvr.RpcRoomInvitationMsgNtfRes.Builder> rpcRoomInvitationMsgNtf(
            SsGamesvr.RpcRoomInvitationMsgNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode msgNotifyRet = player.roomInvitationMsgNotify(req.build());
            return RpcResult.create(msgNotifyRet, SsGamesvr.RpcRoomInvitationMsgNtfRes.newBuilder()
                    .setResult(msgNotifyRet.getValue()));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcRoomInvitationMsgNtf NKCheckedException uid:{}, room:{}, inviter:{}, e:{}",
                    req.getUid(), req.getRoomID(), req.getInviter(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcRoomInvitationMsgNtf PlayerNotFound uid:{}, room:{}, inviter:{}, e:{}",
                    req.getUid(), req.getRoomID(), req.getInviter(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcRoomMemberModifyNtf(
            RpcRoomMemberModifyNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomModifyNotify(req.build());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcRoomMemberModifyNtf NKCheckedException uid:{}, room:{}, req:{}, e:{}",
                    req.getUid(), req.getRoomID(), req.getModify(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcRoomMemberModifyNtf notFoundErr uid:{}, room:{}, req:{}, e:{}",
                    req.getUid(), req.getRoomID(), req.getModify(), notFoundErr);
        }
        return null;
    }
    @Override
    public Void rpcUgcRoomPlayerRecommendMapNtf(
            RpcUgcRoomPlayerRecommendMapNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.rpcUgcRoomPlayerRecommendMapNtf(req.build());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcUgcRoomPlayerRecommendMapNtf NKCheckedException uid:{},  req:{}, e:{}",
                  req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcUgcRoomPlayerRecommendMapNtf notFoundErr uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcRoomCancelResultNtfRes.Builder> rpcRoomCancelResultNtf(
            SsGamesvr.RpcRoomCancelResultNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomCancelResultNtfReq(req.build());
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcRoomCancelResultNtfRes.newBuilder().setResult(0));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcRoomCancelResultNtf NKCheckedException uid:{}, room:{}, e:{}",
                    req.getUid(), req.getRoomId(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcRoomCancelResultNtf PlayerNotFoundException uid:{}, room:{}, e:{}",
                    req.getUid(), req.getRoomId(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcSharedInviteNtfRes.Builder> rpcSharedInviteNtf(
            SsGamesvr.RpcSharedInviteNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomSharedInviteNtfReq(req.build());
            return RpcResult.create(NKErrorCode.OK);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcSharedInviteNtf NKCheckedException uid:{}, room:{}, e:{}",
                    req.getUid(), req.getRoomId(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcSharedInviteNtf PlayerNotFoundException uid:{}, room:{}, e:{}",
                    req.getUid(), req.getRoomId(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcGetPlayerHotDataRes.Builder> rpcGetPlayerHotData(RpcGetPlayerHotDataReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcGetPlayerHotDataRes.Builder res = player.handleRpcGetPlayerHotDataReq(req.build());
            return RpcResult.create(NKErrorCode.OK, res.setResult(0));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcGetPlayerHotData NKCheckedException uid:{}, reqUid:{}, e:{}",
                    req.getUid(), req.getReqUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcGetPlayerHotData PlayerNotFoundException uid:{}, reqUid:{}, e:{}",
                    req.getUid(), req.getReqUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcWantToJoinRes.Builder> rpcWantToJoin(SsGamesvr.RpcWantToJoinReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getTagPlayer());
            int result = player.onWantToJoinReq(req.build());
            return RpcResult.create(result, SsGamesvr.RpcWantToJoinRes.newBuilder().setErrorCode(result));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcWantToJoin NKCheckedException uid:{}, req:{}, e:{}",
                    req.getTagPlayer(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcWantToJoin PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getTagPlayer(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcWantToJoinResultRes.Builder> rpcWantToJoinResult(
            SsGamesvr.RpcWantToJoinResultReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getReqPlayer());
            int result = player.onWantToJoinResultReq(req.build());
            return RpcResult.create(NKErrorCode.OK);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcWantToJoinResult NKCheckedException uid:{}, req:{}, e:{}",
                    req.getReqPlayer(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcWantToJoinResult PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getReqPlayer(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public RpcResult<SsGamesvr.RpcBattleGameDateNtfRes.Builder> rpcBattleGameDateNtf(
            SsGamesvr.RpcBattleGameDateNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerId());
            if (null != player) {
                player.handleBattleRequestGameDataNtf(req);
            }
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcBattleGameDateNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcBattleGameDateNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcBattleGameDateNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcBattleSyncGameDateNtfRes.Builder> rpcBattleSyncGameDateNtf(
            SsGamesvr.RpcBattleSyncGameDateNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerId());
            if (null != player) {
                player.handleBattleSyncGameDataNtf(req);
            }
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcBattleSyncGameDateNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcBattleSyncGameDateNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcBattleSyncGameDateNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public Void rpcStartBattleNtf(
            SsGamesvr.RpcStartBattleNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomStartBattleNtf(req.build());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStartBattleNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcStartBattleNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcEnterLevelNtf(SsGamesvr.RpcEnterLevelNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onEnterLevelNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStartBattleNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcStartBattleNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcBattleSeedDateNtfRes.Builder> rpcBattleSeedDateNtf(
            SsGamesvr.RpcBattleSeedDateNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerId());
            if (null != player) {
                player.onBattleSeedNtf(req);
            }
            return RpcResult.create(NKErrorCode.OK.getValue(), SsGamesvr.RpcBattleSeedDateNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcBattleSeedDateNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcBattleSeedDateNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcBattleEndDataNtfRes.Builder> rpcBattleEndDataNtf(
            SsGamesvr.RpcBattleEndDataNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerId());
            if (player != null) {
                player.onBattleEndNtf(req);
            }

            return RpcResult.create(NKErrorCode.OK.getValue(), SsGamesvr.RpcBattleEndDataNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcBattleEndDataNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcBattleEndDataNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerId(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcCreateChatGroupRes.Builder> rpcCreateChatGroup(
            SsGamesvr.RpcCreateChatGroupReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcCreateChatGroupRes.Builder rspMsg = SsGamesvr.RpcCreateChatGroupRes.newBuilder();
        // 判断创建的聊天类型
        // 暂时只有room和battle两种
        MemberBaseInfo opMember = MemberBaseInfo.newBuilder().setUid(req.getLeaderId()).build();
        if (req.getRoomId() != 0) {
            // 创建对应的聊天
            try {
                Chat chat = new Chat(ChatGroupKey.newBuilder().setType(ChatType.CT_TeamGroup).build(),
                        new RoomKey(req.getRoomId()));
                ChatGroupKey groupKey = GsLocalChatService.get()
                        .createRoomChatGroup(req.getRoomId(), opMember, req.getMembers());
                rspMsg.setRoomGroupKey(RoomChatGroupKey.newBuilder().setGroupKey(groupKey));
            } catch (NKCheckedException e) {
                NKErrorCode.DBOpFailed.throwError("create room chat group failed : " + e);
            }
        }
        if (req.getBattleId() != 0) {
            // 统计所有的阵营
            HashSet<Integer> sideSet = new HashSet<>();
            for (MemberBaseInfo member : req.getMembers().getMemberInfoList()) {
                sideSet.add(member.getSide());
            }
            try {
                BattleChatGroupKey.Builder groupKey = BattleChatGroupKey.newBuilder();
                // 创建阵营聊天
                Integer[] sideArr = new Integer[sideSet.size()];
                for (int side : sideSet.toArray(sideArr)) {
                    Chat sideChat = new Chat(ChatGroupKey.newBuilder().build(), new BattleKey(req.getBattleId(), side));
                    ChatGroupKey sideGroupKey = GsLocalChatService.get()
                            .createBattleSideChatGroup(req.getBattleId(), side, opMember, req.getMembers());
                    groupKey.addSideGroupKeys(
                            BattleSideChatGroupKey.newBuilder().setSide(side).setSideGroupKey(sideGroupKey));
                }
                // 创建所有人聊天
                if (sideSet.size() > 1) {
                    Chat globalChat = new Chat(ChatGroupKey.newBuilder().build(), new BattleKey(req.getBattleId()));
                    ChatGroupKey globalKey = GsLocalChatService.get().createBattleChatGroup(req.getBattleId(), opMember,
                            req.getMembers());
                    groupKey.setGlobalGroupKey(globalKey);
                }
                rspMsg.setBattleGroupKey(groupKey);
            } catch (NKCheckedException e) {
                NKErrorCode.DBOpFailed.throwError("create battle chat group failed : " + e);
            }
        }
        return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
    }

    @Override
    public RpcResult<SsGamesvr.RpcGiveUpBattleRes.Builder> rpcGiveUpBattle(
            SsGamesvr.RpcGiveUpBattleReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcGiveUpBattleRes.Builder rspMsg = SsGamesvr.RpcGiveUpBattleRes.newBuilder();
        // PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());

        switch (req.getCode()) {
            case G6Common.QuitBattleCode.QUIT_BATTLE_CODE_LEAVE_VALUE:
                //player.informGiveUpBattle(req.getCode());
                break;
            case G6Common.QuitBattleCode.QUIT_BATTLE_CODE_FINISH_QUIT_VALUE:
                //player.roomStatusUpdate(RoomStatus.RS_TEAM_VALUE);
                break;
            default:
        }

        return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
    }

    @Override
    public RpcResult<SsGamesvr.RpcQueryCardTradeInfoRes.Builder> rpcQueryCardTradeInfo(SsGamesvr.RpcQueryCardTradeInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        TradingCardTradeInfo tradeInfo = TradeInfoCache.getInstance().getTradeInfo(req.getTradeId());
        if (tradeInfo == null) {
            return RpcResult.create(NKErrorCode.TradingCardGetTradeExpired);
        }
        SsGamesvr.RpcQueryCardTradeInfoRes.Builder rspMsg = SsGamesvr.RpcQueryCardTradeInfoRes.newBuilder();
        rspMsg.setInfo(tradeInfo);
        return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
    }

    @Override
    public RpcResult<SsGamesvr.RpcReceiveTradingCardRes.Builder> rpcReceiveTradingCard(SsGamesvr.RpcReceiveTradingCardReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcReceiveTradingCardRes.Builder rspMsg = SsGamesvr.RpcReceiveTradingCardRes.newBuilder();
        NKErrorCode errorCode = TradeMgr.idipCompleteGiveTrade(req.getUid(), req.getTradeId());
        rspMsg.setErrorCode(errorCode.getValue());
        return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
    }

    @Override
    public RpcResult<SsGamesvr.RpcQuitBattleNtfRes.Builder> rpcQuitBattleNtf(
            SsGamesvr.RpcQuitBattleNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            SsGamesvr.RpcQuitBattleNtfRes.Builder rspMsg = SsGamesvr.RpcQuitBattleNtfRes.newBuilder();
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onQuitBattleNtf(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcQuitBattleNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcQuitBattleNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public RpcResult<SsGamesvr.RpcBattleGameStartNtfRes.Builder> rpcBattleGameStartNtf(
            SsGamesvr.RpcBattleGameStartNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            SsGamesvr.RpcBattleGameStartNtfRes.Builder rspMsg = SsGamesvr.RpcBattleGameStartNtfRes.newBuilder();
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onBattleStartNtf(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), rspMsg);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcBattleGameStartNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcBattleGameStartNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcPrepareToJoinRoomRes.Builder> rpcPrepareToJoinRoom(
            SsGamesvr.RpcPrepareToJoinRoomReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcPrepareToJoinRoomRes.Builder rspMsg = SsGamesvr.RpcPrepareToJoinRoomRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            int ret = player.prepareToJoinRoom(req);
            return RpcResult.create(SsGamesvr.RpcPrepareToJoinRoomRes.newBuilder().setRet(ret));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcPrepareToJoinRoom NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcPrepareToJoinRoom PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcRoomCommonNtf(SsGamesvr.RpcRoomCommonNtfReq.Builder req)
            throws RpcException {
        try {
//            SsGamesvr.RpcPrepareToJoinRoomRes.Builder rspMsg = SsGamesvr.RpcPrepareToJoinRoomRes.newBuilder();
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomCommonNtf(req);
            return null;
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcRoomCommonNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req.getErrorCode(), nkErr);
            return null;
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcRoomCommonNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req.getErrorCode(), notFoundErr);
            return null;
        }
    }

    @Override
    public RpcResult<IdipRpcByZoneIdRes.Builder> idipRpcByZoneid(IdipRpcByZoneIdReq.Builder req)
            throws RpcException, NKTimeoutException {
        NKErrorCode.InvalidReq.throwError("unimplemented");
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcGmRes.Builder> rpcGm(SsGamesvr.RpcGmReq.Builder req) throws RpcException {
        int result = GmIdipCmd.handleIdipGmCmd(req);
        SsGamesvr.RpcGmRes.Builder resBuilder = SsGamesvr.RpcGmRes.newBuilder();
        resBuilder.setResult(result);
        return RpcResult.create(resBuilder);
    }

    @Override
    public Void rpcGmSendCsToast(SsGamesvr.RpcGmSendCsToastReq.Builder req) throws RpcException {
        if (!ServerEngine.getInstance().canUseGmCmd()) {
            return null;
        }

        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (null != playerRef) {
                playerRef.rpcGmSendCsToast(req);
            }
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcGmSendCsToast NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("rpcGmSendCsToast PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcSearchForUidRes.Builder> rpcSearchForUid(SsGamesvr.RpcSearchForUidReq.Builder req) throws RpcException {
        List<Long> result = GmIdipCmd.handleSearchForUidCmd(req);
        String openid = req.getOpenid();
        SsGamesvr.RpcSearchForUidRes.Builder resBuilder = SsGamesvr.RpcSearchForUidRes.newBuilder();
        try {
            for (Long uid : result) {
                PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
                String name = player.getName();
                SsGamesvr.RpcSearchForUidRes.PlayerBriefInfo.Builder playerInfoBuilder = SsGamesvr.RpcSearchForUidRes.PlayerBriefInfo.newBuilder();
                playerInfoBuilder.setUid(uid);
                playerInfoBuilder.setName(name);
                resBuilder.addInfo(playerInfoBuilder.build());
            }
        } catch (NKCheckedException | PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSearchForUid NKCheckedException openid:{}, req:{}, e:{}", openid, req, e);
            throw new RpcException(Throwables.getRootCause(e));
        }

        return RpcResult.create(resBuilder);
    }

    @Override
    public RpcResult<IdipRpcByUuidRes.Builder> idipRpcByUuid(IdipRpcByUuidReq.Builder req) throws RpcException {
        int cmdId = req.getReq().getCmdId();
        IdipResponse.Builder res;
        try {
            long playerUid = req.getUid();
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(playerUid);
            LocalServiceSequentialWrapper.ProtocolInfo protocolInfo = new LocalServiceSequentialWrapper.ProtocolInfo(
                    LocalServiceSequentialWrapper.ProtocolType.Idip, cmdId, playerUid, "");
            res = LocalServiceSequentialWrapper.get(GSEngine.getSpecInstance().getPlayerService()).callJob(playerUid,
                    ServiceMgr.DEFAULT_TIMEOUT,
                    () -> {
                        try {
                            return playerRef.idipRpcByUuid(cmdId, req.getReqBuilder());
                        } catch (PlayerNotFoundException e) {
                            LOGGER.error("GsImpl::idipRpcByUuid PlayerNotFoundException uid:{}, req:{}, e:{}",
                                    playerUid, req, e);
                            throw new NKRuntimeException(NKErrorCode.UserIsOffline);
                        }
                    }, protocolInfo, "procIdipReq", true);
            if (res == null) {
                throw new RpcException("null result");
            }
            IdipRpcByUuidRes.Builder resBuilder = IdipRpcByUuidRes.newBuilder();
            resBuilder.setRes(res);
            return RpcResult.create(resBuilder);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::idipRpcByUuid NKCheckedException uid:{}, req:{}, e:{}", req.getUid(), req, e);
            throw new RpcException(Throwables.getRootCause(e));
        }
    }


    @Override
    public RpcResult<IdipRpcRes.Builder> idipRpc(IdipRpcReq.Builder req) throws RpcException {
        int cmdId = req.getReq().getCmdId();
        IdipResponse.Builder res;
        try {
            res = IdipPlayerUtil.handleIdipReq(cmdId, null, req.getReqBuilder());
            if (res == null) {
                throw new RpcException("null result");
            }
            IdipRpcRes.Builder resBuilder = IdipRpcRes.newBuilder();
            resBuilder.setRes(res);
            return RpcResult.create(resBuilder);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::idipRpcByUuid NKCheckedException, req:{}, e:{}", req, e);
            throw new RpcException(Throwables.getRootCause(e));
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcInviteeLockInviterRes.Builder> rpcInviteeLockInviter(
            SsGamesvr.RpcInviteeLockInviterReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getInviter());
            SsGamesvr.RpcInviteeLockInviterRes.Builder rsp = SsGamesvr.RpcInviteeLockInviterRes.newBuilder()
                    .setResult(player.inviteeLockInviter(req));
            return RpcResult.create(rsp);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcInviteeLockInviter NKCheckedException uid:{}, req:{}, e:{}",
                    req.getInviter(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcInviteeLockInviter PlayerNotFoundException uid:{},  req:{}, e:{}",
                    req.getInviter(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public RpcResult<SsGamesvr.RpcExpressionNtfRes.Builder> rpcExpressionNtf(
            SsGamesvr.RpcExpressionNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
            if (null != player) {
                player.onBattleExpressionNtf(req);
            }
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcExpressionNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcExpressionNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcExpressionNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcGetLimitValueRes.Builder> rpcGetLimitValue(SsGamesvr.RpcGetLimitValueReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
            CommonLimitInfo limitInfo = player.getLimitInfo(req.getLimitType());
            proto_CommonLimitInfo.Builder commonLimitInfo = proto_CommonLimitInfo.newBuilder();
            commonLimitInfo.setLimitType(limitInfo.getLimitType());
            commonLimitInfo.addAllLimitInfo(limitInfo.getCopyCsBuilder().getLimitInfoList());
            SsGamesvr.RpcGetLimitValueRes.Builder rsp = SsGamesvr.RpcGetLimitValueRes.newBuilder()
                    .setResult(0).setLimitInfo(commonLimitInfo);
            return RpcResult.create(rsp);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcGetLimitValue NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcGetLimitValue PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcChatGroupOperationNtfRes.Builder> rpcChatGroupOperationNtf(
            RpcChatGroupOperationNtfReq.Builder req) throws RpcException, NKTimeoutException {
        RpcChatGroupOperationNtfRes.Builder rsp = RpcChatGroupOperationNtfRes.newBuilder().setResult(0);
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            ChatGroupOperationNtf.Builder ntf = ChatGroupOperationNtf.newBuilder()
                    .setOp(req.getOp()).setChatGroupKey(req.getChatGroupKey())
                    .setPlayerUid(req.getPlayerUid()).setSeqId(req.getSeqId());

            player.chatGroupOperationNtf(ntf);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcChatGroupOperationNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerUid(), req, e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(rsp);
    }

    @Override

    public RpcResult<SsGamesvr.RpcExitSceneNtfRes.Builder> rpcExitSceneNtf(
            SsGamesvr.RpcExitSceneNtfReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcExitSceneNtfRes.Builder rsp = SsGamesvr.RpcExitSceneNtfRes.newBuilder().setResult(0);
        return RpcResult.create(rsp);
    }

    @Override
    public RpcResult<SsGamesvr.RpcJumpSceneNtfRes.Builder> rpcJumpSceneNtf(
            SsGamesvr.RpcJumpSceneNtfReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcJumpSceneNtfRes.Builder rsp = SsGamesvr.RpcJumpSceneNtfRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            int ret = player.onJumpSceneNtf(req);
            return RpcResult.create(rsp.setResult(ret));
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcJumpSceneNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcJumpSceneNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }

    }

    @Override
    public RpcResult<RpcSyncScenePlayerInfoRes.Builder> rpcSyncScenePlayerInfo(RpcSyncScenePlayerInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSyncScenePlayerInfoRes.Builder rsp = SsGamesvr.RpcSyncScenePlayerInfoRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        try {
            rsp.setPublicInfo(player.getScenePlayerExtraInfo());
            return RpcResult.create(rsp);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSyncScenePlayerInfo PlayerNotFoundException uid:{}, req:{}, e:{}", req.getUid(),
                    req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSyncScenePlayerInfo NKCheckedException uid:{}, req:{}, e:{}", req.getUid(),
                    req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<RpcSceneBaseDataNtfRes.Builder> rpcSceneBaseDataNtf(RpcSceneBaseDataNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSceneBaseDataNtfRes.Builder rsp = SsGamesvr.RpcSceneBaseDataNtfRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        try {
            player.onSceneBaseDataNtf(req);
            rsp.setResult(NKErrorCode.OK.getValue());
            return RpcResult.create(rsp);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSyncScenePlayerInfo PlayerNotFoundException uid:{}, req:{}, e:{}", req.getUid(),
                    req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSyncScenePlayerInfo NKCheckedException uid:{}, req:{}, e:{}", req.getUid(),
                    req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }


    @Override
    public RpcResult<RpcClubKickOutNtfRes.Builder> rpcClubKickOutNtf(RpcClubKickOutNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcClubKickOutNtfRes.Builder rsp = RpcClubKickOutNtfRes.newBuilder().setResult(0);
//        try {
//            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
//            player.clubKickOutNtf(req);
//        } catch (NKCheckedException e) {
//            LOGGER.error("GsImpl::rpcClubKickOutNtf NKCheckedException uid:{}, req:{}, e:{}",
//                    req.getPlayerid(), req, e);
//            return RpcResult.create(e.getEnumErrCode());
//        }
        return RpcResult.create(rsp);
    }

    @Override
    public RpcResult<RpcClubDissolveNtfRes.Builder> rpcClubDissolveNtf(RpcClubDissolveNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcClubDissolveNtfRes.Builder rsp = RpcClubDissolveNtfRes.newBuilder().setResult(0);
//        try {
//            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
//            player.dissolveClubNtf(req);
//        } catch (NKCheckedException e) {
//            LOGGER.error("GsImpl::rpcClubDissolveNtf NKCheckedException , req:{}, e:{}",
//                     req, e);
//            return RpcResult.create(e.getEnumErrCode());
//        }
        return RpcResult.create(rsp);
    }

    @Override
    public RpcResult<RpcClubJoinNtfRes.Builder> rpcClubJoinNtf(RpcClubJoinNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcClubJoinNtfRes.Builder rsp = RpcClubJoinNtfRes.newBuilder().setResult(0);
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
            player.clubJoinNtf(req);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcClubJoinNtf NKCheckedException , req:{}, e:{}",
                    req, e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(rsp);
    }

    @Override
    public RpcResult<RpcCreateCLubCostRes.Builder> rpcCreateCLubCost(RpcCreateCLubCostReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcCreateCLubCostRes.Builder rsp = RpcCreateCLubCostRes.newBuilder();
        //严格把控资源消耗
        int res = 0;
        try {
            TxStopWatch stopWatch = NKStopWatch.SW_CreateClub.getStopWatch();
            stopWatch.mark("club get player info start:" + req.getPlayerid());
            final PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
            stopWatch.mark("club get player info end:" + req.getPlayerid());
            stopWatch.dump(0);

            res = player.createClubCostItem();

        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcClubJoinNtf NKCheckedException , req:{}, e:{}",
                    req, e);
            res = NKErrorCode.ClubServerInternalError.getValue();
            rsp.setResult(res);
            return RpcResult.create(rsp);
        }
        rsp.setResult(res);
        return RpcResult.create(rsp);
    }

    @Override
    public RpcResult<RpcClubDissolveApplyNtfRes.Builder> rpcClubDissolveApplyNtf(RpcClubDissolveApplyNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcClubDissolveApplyNtfRes.Builder rsp = RpcClubDissolveApplyNtfRes.newBuilder().setResult(0);
        return RpcResult.create(rsp);
    }

    @Override

    public RpcResult<RpcSceneInteractActionAcceptRes.Builder> rpcSceneInteractActionAccept(
            RpcSceneInteractActionAcceptReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSceneInteractActionAcceptRes.Builder rsp = SsGamesvr.RpcSceneInteractActionAcceptRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
        try {
            int ret = player.onHandleInteractActionAcceptMsg(req.build());
            return RpcResult.create(ret);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionAccept PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionAccept NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<RpcSceneInteractActionActiveRes.Builder> rpcSceneInteractActionActive(
            RpcSceneInteractActionActiveReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSceneInteractActionActiveRes.Builder rsp = SsGamesvr.RpcSceneInteractActionActiveRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
        try {
            int ret = player.onHandleInteractActionActiveMsg(req.build(), rsp);
            return RpcResult.create(ret, rsp);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionActive PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionActive NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<RpcSceneInteractActionInviteRes.Builder> rpcSceneInteractActionInvite(
            RpcSceneInteractActionInviteReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSceneInteractActionInviteRes.Builder rsp = SsGamesvr.RpcSceneInteractActionInviteRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
        try {
            int ret = player.onHandleInteractActionInviteMsg(req.build());
            return RpcResult.create(ret, rsp);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionInvite PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionInvite NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<RpcSceneInteractActionCancelRes.Builder> rpcSceneInteractActionCancel(
            RpcSceneInteractActionCancelReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcSceneInteractActionCancelRes.Builder rsp = SsGamesvr.RpcSceneInteractActionCancelRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerid());
        try {
            int ret = player.onHandleInteractActionCancelMsg(req.build());
            return RpcResult.create(ret, rsp);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionInvite PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInteractActionInvite NKCheckedException uid:{}, req:{}, e:{}",
                    req.getPlayerid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcSceneGetCurrentSceneIdRes.Builder> rpcSceneGetCurrentSceneId(
            SsGamesvr.RpcSceneGetCurrentSceneIdReq.Builder req) throws RpcException, NKTimeoutException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        SsGamesvr.RpcSceneGetCurrentSceneIdRes.Builder rsp = SsGamesvr.RpcSceneGetCurrentSceneIdRes.newBuilder();
        try {
            AttrSceneInfo.proto_SceneInfo sceneInfo = player.getSceneInfo();
            rsp.setSceneId(sceneInfo.getSceneid());
            rsp.setMapId(sceneInfo.getMapid());
            return RpcResult.create(NKErrorCode.OK, rsp);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcSceneGetCurrentSceneId NKCheckedException uid:{}, req:{}, e:\n",
                    req.getUid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcScenePlayerActionRes.Builder> rpcScenePlayerAction(
            SsGamesvr.RpcScenePlayerActionReq.Builder req) throws RpcException, NKTimeoutException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        try {
            player.onHandleSceneAction(req.build());
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcScenePlayerActionRes.newBuilder());
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcScenePlayerAction NKCheckedException uid:{}, req:{}, e:\n",
                    req.getUid(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }
    }


    @Override
    public RpcResult<RpcUpdatePlayerUidCacheRes.Builder> rpcUpdatePlayerUidCache(RpcUpdatePlayerUidCacheReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            LOGGER.debug("rpcUpdatePlayerUidCache, openID:{}", req.getOpenId());
//            PlayerUidMgr.getInstance().removeUidFromOpenId(req.getOpenId());
            return RpcResult.create(NKErrorCode.OK);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcUpdatePlayerUidCache NKCheckedException openId:{}, req:{}, e:{}",
                    req.getOpenId(), req.toString(), e);
            return RpcResult.create(NKErrorCode.SSReqFailed);
        }

    }

    @Override
    public RpcResult<SsGamesvr.RpcMatchSceneCreateSuccRes.Builder> rpcMatchSceneCreateSucc(
            SsGamesvr.RpcMatchSceneCreateSuccReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomMatchSceneCreateSuccNtf(req.build());
            return RpcResult.create(NKErrorCode.OK,
                    SsGamesvr.RpcMatchSceneCreateSuccRes.newBuilder().setResult(NKErrorCode.OK.getValue()));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcMatchSceneCreateSucc NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcMatchSceneCreateSucc PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcMatchJoinSuccRes.Builder> rpcMatchJoinSucc(
            SsGamesvr.RpcMatchJoinSuccReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomMatchJoinSuccNtf(req.build());
            return RpcResult.create(NKErrorCode.OK,
                    SsGamesvr.RpcMatchJoinSuccRes.newBuilder().setResult(NKErrorCode.OK.getValue()));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcMatchJoinSucc NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcMatchJoinSucc PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override

    public RpcResult<RpcScenePlayerHallCoinRewardRes.Builder> rpcScenePlayerHallCoinReward(
            RpcScenePlayerHallCoinRewardReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            PlayerHallCoinRewardEvent event = player.sendHallCoinReward(req.getGamePlayId(), req.getGamePlayUuid(),
                    req.getRewardResetType().getNumber());
            SsGamesvr.RpcScenePlayerHallCoinRewardRes.Builder rspBuilder =
                    SsGamesvr.RpcScenePlayerHallCoinRewardRes.newBuilder();
            rspBuilder.addAllRewardItemInfo(event.getRewardMap().values());
            return RpcResult.create(event.getResult(), rspBuilder);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcScenePlayerHallCoinReward NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcScenePlayerHallCoinReward PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }

    }

    @Override
    public Void rpcNotifyPlayerOnlineStatus(RpcNotifyPlayerOnlineStatusReq.Builder req) {
        BatchPlayerRef playerRef = PlayerRefMgr.getInstance()
                .createBatchPlayerRefIfPresent(req.getTargetFriendDataMap().keySet());
        try {
            playerRef.batchNotifyPlayerOnlineStatusReq(req);
        } catch (Exception e) {
            LOGGER.error("rpcNotifyPlayerOnlineStatus, {}", e.getMessage());
        }
        return null;


    }

    @Override
    public Void rpcBatchNotifyFriendChangeData(RpcBatchNotifyFriendChangeDataReq.Builder req) throws RpcException {
        HashMap<Long, ArrayList<PlayerFriendChangeData>> uidMsgMap = new HashMap<>();
        for (PlayerFriendChangeData changeData : req.getPlayerChangeDataList()) {
            for (long uid : changeData.getTargetFriendDataMap().keySet()) {
                ArrayList<PlayerFriendChangeData> dataList = uidMsgMap.computeIfAbsent(uid, k -> new ArrayList<>());
                dataList.add(changeData);
            }
        }
        BatchPlayerRef playerRef = PlayerRefMgr.getInstance().createBatchPlayerRefIfPresent(uidMsgMap.keySet());
        try {
            playerRef.batchNotifyPlayerFriendChangeDataReq(uidMsgMap);
        } catch (Exception e) {
            LOGGER.error("rpcBatchNotifyFriendChangeData, {}", e.getMessage());
        }
        return null;
    }

    @Override
    public RpcResult<RpcRoomLobbyGatherNtfRes.Builder> rpcRoomLobbyGatherNtf(RpcRoomLobbyGatherNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        return null;
    }

    @Override
    public RpcResult<RpcRoomStartMatchNtfRes.Builder> rpcRoomStartMatchNtf(RpcRoomStartMatchNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomStartMatchNtf(req.getRoomId(), req.getUniqueId(), req.getAutoConfirm());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomStartMatchNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
                    req.getUid(), req.getRoomId(), req.getUniqueId(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcRoomStartMatchNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
                    req.getUid(), req.getRoomId(), req.getUniqueId(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcRoomStartMatchCancelNtfRes.Builder> rpcRoomStartMatchCancelNtf(
            RpcRoomStartMatchCancelNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomStartMatchCancelNtf(req.getRoomId(), req.getUniqueId(), req.getCancelerId(),
                    req.getCancelType(), req.getCancelReason());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomStartMatchCancelNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
                    req.getUid(), req.getRoomId(), req.getUniqueId(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcRoomStartMatchCancelNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
                    req.getUid(), req.getRoomId(), req.getUniqueId(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<SsGamesvr.RpcLobbyJumpNtfRes.Builder> rpcLobbyJumpNtf(
            SsGamesvr.RpcLobbyJumpNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            IntLongMap playerFeature = player.handleRpcLobbyJumpNtfReq(req.build());
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcLobbyJumpNtfRes.newBuilder().setPlayerData(playerFeature));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcLobbyJumpNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcLobbyJumpNtf PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcLobbyInfoNtfRes.Builder> rpcLobbyInfoNtf(
            com.tencent.wea.protocol.SsGamesvr.RpcLobbyInfoNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleRpcLobbyInfoNtfReq(req.build());
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcLobbyInfoNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcLobbyInfoNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcLobbyInfoNtf PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public RpcResult<SsGamesvr.RpcPlayerUseFireworksItemNtfRes.Builder> rpcPlayerUseFireworksItemNtf(
            SsGamesvr.RpcPlayerUseFireworksItemNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handlePlayerUseFireworksItemNtfReq(req.build());
            return RpcResult.create(NKErrorCode.OK, SsGamesvr.RpcPlayerUseFireworksItemNtfRes.newBuilder());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcPlayerUseFireworksItemNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcPlayerUseFireworksItemNtf PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcGetPlayerLobbyIdRes.Builder> rpcGetPlayerLobbyId(
            SsGamesvr.RpcGetPlayerLobbyIdReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetPlayerLobbyIdRes.Builder res = player.handleRpcGetPlayerLobbyIdReq(req.build());
            if (res.getResult() != 0) {
                return RpcResult.create(NKErrorCode.forNumber(res.getResult()));
            }
            return RpcResult.create(NKErrorCode.OK, res.setResult(0));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcGetPlayerLobbyId NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcGetPlayerLobbyId PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcGetPlayerXiaoWoIdRes.Builder> rpcGetPlayerXiaoWoId(
            SsGamesvr.RpcGetPlayerXiaoWoIdReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetPlayerXiaoWoIdRes.Builder res = player.handleRpcGetPlayerXiaoWoIdReq(req.build());
            return RpcResult.create(NKErrorCode.OK, res.setResult(0));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcGetPlayerXiaoWoId NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcGetPlayerXiaoWoId PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }


    @Override
    public RpcResult<RpcDeliverGoodsRes.Builder> rpcDeliverGoods(RpcDeliverGoodsReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            LocalServiceSequentialWrapper.ProtocolInfo protocolInfo = new LocalServiceSequentialWrapper.ProtocolInfo(
                    LocalServiceSequentialWrapper.ProtocolType.DeliverGoods, 0, req.getUid(), "");
            LocalServiceSequentialWrapper.get(GSEngine.getSpecInstance().getPlayerService())
                    .runJob(req.getUid(),
                            () -> {
                                try {
                                    playerRef.handleRpcDeliverGoodsReq(req.build());
                                } catch (PlayerNotFoundException notFoundErr) {
                                    LOGGER.error("GsImpl::rpcDeliverGoods PlayerNotFoundException uid:{}, e:{}",
                                            req.getUid(), notFoundErr);
                                    return notFoundErr.getEnumErrCode();
                                }
                                return NKErrorCode.OK;
                            }, protocolInfo, "procDeliverGoodsReq", true);
            return RpcResult.create(NKErrorCode.OK);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcDeliverGoods NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcAllocateLobbyRes.Builder> rpcAllocateLobby(SsGamesvr.RpcAllocateLobbyReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcAllocateLobbyRes.Builder res = RpcAllocateLobbyRes.newBuilder();
            long lobbyId = player.handleAllocateLobby(SsCommon.LobbyJumpReasonCode.forNumber(req.getJumpReasonCode()));
            if (lobbyId == 0) {
                return RpcResult.create(NKErrorCode.LobbyFindFailed, res);
            }
            res.setLobbyId(lobbyId);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcAllocateLobby NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcAllocateLobby PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcMessageSlipOpRes.Builder> rpcMessageSlipOp(RpcMessageSlipOpReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcMessageSlipOpRes.Builder res = player.handleRpcMessageSlipOpReq(req.build());
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcMessageSlipOp NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcMessageSlipOp PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcMatchProcessNtf(RpcMatchProcessNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.roomMatchProcessNtf(req.getCurMatchedPlayerNumber(), req.getTargetMatchPlayerNumber());
        } catch (NKCheckedException | PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcMatchProcessNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcRecordPlayerTlog(SsGamesvr.RpcRecordPlayerTlogReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.recordPlayerTlog(req.build());
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcRecordPlayerTlog NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcRecordPlayerTlog PlayerNotFoundException uid:{}, e:{}",
                    req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<RpcSceneInviteNtfRes.Builder> rpcSceneInviteNtf(RpcSceneInviteNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getInviteeId());
            NKErrorCode errorCode = NKErrorCode.forNumber(
                    player.ntfSceneInvitation(req.getInviteType(), req.getInviterId(), req.getInviterName(),
                            req.getVersionGroupId(), req.getMapId()));
            if (!errorCode.isOk()) {
                return RpcResult.create(errorCode);
            }
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInviteNtf NKCheckedException uid:{}, e:{}",
                    req.getInviteeId(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInviteNtf PlayerNotFoundException uid:{}, e:{}",
                    req.getInviteeId(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcSceneInviteReplyNtfRes.Builder> rpcSceneInviteReplyNtf(RpcSceneInviteReplyNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getInviterId());
            player.ntfSceneReplyInvitation(req.getInviteType(), req.getInviteeId(), req.getInviteeName(),
                    req.getAccept());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcSceneInviteReplyNtf NKCheckedException uid:{}, e:{}",
                    req.getInviterId(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcSceneInviteReplyNtf PlayerNotFoundException uid:{}, e:{}",
                    req.getInviterId(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public Void rpcNotifyIntimacyChange(RpcNotifyIntimacyChangeReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            try {
                player.handleRpcNotifyIntimacyChange(req.getReqUid());
            } catch (Exception e) {
                LOGGER.error("rpcNotifyIntimacyChange, {}", e.getMessage());
            }
        }
        return null;
    }

    @Override

    public RpcResult<RpcNotifyRelationApplyRes.Builder> rpcNotifyRelationApply(RpcNotifyRelationApplyReq.Builder req) {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleRpcNotifyRelationApply(req.build());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcNotifyRelationMsg NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcNotifyRelationMsg PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcNotifyRelationAgreeRes.Builder> rpcNotifyRelationAgree(RpcNotifyRelationAgreeReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleRpcNotifyRelationAgree(req.build());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcNotifyRelationAgree NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcNotifyRelationAgree PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public RpcResult<SsGamesvr.RpcXiaoWoInfoNtfRes.Builder> rpcXiaoWoInfoNtf(SsGamesvr.RpcXiaoWoInfoNtfReq.Builder req)
            throws RpcException, NKTimeoutException {
        return RpcResult.create(NKErrorCode.OK);
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public Void rpcXiaoWoPlatNtf(SsGamesvr.RpcXiaoWoPlatNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        try {
            var ntf = CsXiaowo.XiaoWoPlatDataNtf.newBuilder();
            ntf.setNtfData(req.getNtfData()).setNtfType(req.getNtfType());

            player.xiaoWoPlatDataNtf(ntf);
        } catch (Exception e) {
            LOGGER.error("rpcXiaoWoPlatNtf uid:{}, e:",
                    req.getUid(), e);
        }
        return null;
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public Void rpcPartyInfoNtf(SsGamesvr.RpcPartyInfoNtfReq.Builder req) throws RpcException {
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcCompetitionEnrollSubMoneyRes.Builder> rpcCompetitionEnrollSubMoney(
            SsGamesvr.RpcCompetitionEnrollSubMoneyReq.Builder req) {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.competitionEnrollSubMoney(req);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcCompetitionSendMailRes.Builder> rpcCompetitionSendMail(RpcCompetitionSendMailReq.Builder req) {
        MailAttachmentList.Builder attList = MailAttachmentList.newBuilder();
        for (ItemInfo itemInfo : req.getAttatchmentListList()) {
            MailAttachment.Builder attachment = MailAttachment.newBuilder().setItemIfo(itemInfo);
            attList.addList(attachment);
        }
        long mailId = MailInteraction.sendTemplateMail(req.getUid(), req.getTemplateId(), attList, 0, null,
                MailInteraction.TlogSendReason.values()[req.getReason()], req.getTitleArgsList(),
                req.getContentArgsList());
        if (mailId < 0) {
            return RpcResult.create(NKErrorCode.CompetitionSendMailFailed);
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcCompetitionAddItemRes.Builder> rpcCompetitionAddItem(RpcCompetitionAddItemReq.Builder req) {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.competitionAddItem(req);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<RpcCompetitionTextCheckRes.Builder> rpcCompetitionTextCheck(
            RpcCompetitionTextCheckReq.Builder req) {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (!player.competitionTextCheck(req)) {
                return RpcResult.create(NKErrorCode.CompetitionTextCheckNotLawful);
            }
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getErrCode());
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     * @throws NKTimeoutException
     */
    @Override
    public RpcResult<SsGamesvr.RpcBulletinPublishRes.Builder> rpcBulletinPublish(
            SsGamesvr.RpcBulletinPublishReq.Builder req) {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        RpcResult<SsGamesvr.RpcBulletinPublishRes.Builder> result;
        try {
            result = player.bulletinPublish(req);
            return result;
        } catch (Exception e) {
            LOGGER.error("uid: {}, rpcBulletinPublish failed: {}", req.getUid(), e);
        }

        return RpcResult.create(NKErrorCode.BulletinPublishFailed);

    }

    @Override
    public RpcResult<RpcRoomPositionExchangeNtfRes.Builder> rpcRoomPositionExchangeNtf(
            RpcRoomPositionExchangeNtfReq.Builder req) throws RpcException, NKTimeoutException {
        RpcRoomPositionExchangeNtfRes.Builder rspBuilder = RpcRoomPositionExchangeNtfRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            rspBuilder.setResult(player.handleRoomPositionExchangeNtf(req));
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomPositionExchangeNtf NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
        return RpcResult.create(rspBuilder);
    }

    @Override
    public Void rpcRoomBroadcastInfo(RpcRoomBroadcastInfoReq.Builder req)
            throws RpcException {
        RpcRoomBroadcastInfoRes.Builder rspBuilder = RpcRoomBroadcastInfoRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            rspBuilder.setResult(player.handleRoomBroadcastInfo(req));
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomBroadcastInfo NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcRoomCoMatchInfoNtf(SsGamesvr.RpcRoomCoMatchInfoNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            player.hanldeRoomCoMatchInfoNtf(req);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomCoMatchInfoNtf NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcUgcMapStatusChangeNtf(RpcUgcMapStatusChangeNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player != null) {
                player.handleRpcNotifyMapStatus(req.getMapId(), req.getMapStatus(),req.getGoodStatus(),req.getAdvertStatus());
            } else {
                LOGGER.info("play not found uid:{}", req.getUid());
            }
        } catch (Exception e) {
            LOGGER.error("rpcUgcMapStatusChangeNtf uid:{}, e:",
                    req.getUid(), e);
        }

        return null;
    }

    @Deprecated
    @Override
    public Void rpcUgcLvChangeNtf(RpcUgcLvChangeNtfReq.Builder req) throws RpcException {

        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player != null) {
                player.setUgcLv(req.getUgcLv(), req.getIsClear());
            } else {
                LOGGER.info("play not found uid:{}", req.getUid());
            }
        } catch (Exception e) {
            LOGGER.error("rpcUgcLvChangeNtf uid:{}, e:",
                    req.getUid(), e);
        }
        return null;
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public Void rpcKickPlayerLogout(SsGamesvr.RpcKickPlayerLogoutReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.kickPlayerLogout(req.build());
        return null;
    }

    @Override
    public Void rpcXiaoWoKickNtf(SsGamesvr.RpcXiaoWoKickNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.xiaoWoKick(req.getXiaoWoId(), req.getReason());
        return null;
    }

    @Override
    public Void rpcFarmKickNtf(SsGamesvr.RpcFarmKickNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.farmKick(req.getFarmId(), req.getReason());
        return null;
    }

    @Override
    public Void rpcFarmNPCFishNtf(SsGamesvr.RpcFarmNPCFishNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.rpcFarmNPCFishNtf(req);
        return null;
    }

    @Override
    public Void rpcFarmBeTagNtf(SsGamesvr.RpcFarmBeTagNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.farmBeTag();
        return null;
    }

    @Override
    public Void rpcFarmClearMutexIDNtf(SsGamesvr.RpcFarmClearMutexIDNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
        player.rpcFarmClearMutexIDNtf(req);
        return null;
    }

    @Override
    public Void rpcFarmCreateNPCFarmNtf(RpcFarmCreateNPCFarmNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.rpcFarmCreateNPCFarmNtf(req);
        return null;
    }

    @Override
    public Void rpcFarmDispatchEventCondNtf(SsGamesvr.RpcFarmDispatchEventCondNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
        player.rpcFarmDispatchEventCondNtfReq(req);
        return null;
    }

    @Override
    public Void rpcFarmsvrCsNtfForward(SsGamesvr.RpcFarmsvrCsNtfForwardReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            player.farmSvrCsNtfSend(req);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcFarmsvrCsNtfForward NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcFarmsvrCsNtfForward PlayerNotFoundException uid:{}",
                    req.getPlayerUid());
        }
        return null;
    }

    @Override
    public RpcResult<RpcUgcGetRelationDataRes.Builder> rpcUgcGetRelationData(RpcUgcGetRelationDataReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcUgcGetRelationDataRes.Builder res = RpcUgcGetRelationDataRes.newBuilder();
        long uid = req.getUid();
        if (req.getRelationType() == RelationTypeEnum.RTE_PLAT_FRIEND_RELATION) {
            PlatFriendList list = PlatFriendTableDao.getPlatFriendList(uid);
            if (list != null) {
                res.addAllFriendData(list.getDataList());
            }
        } else {
            HashMap<Long, RelationTable> friendInfoMap = RelationTableDao.getPlayerRelation(req.getRelationType(), uid);
            friendInfoMap.forEach((friendUid, dbPlayerInfo) -> {
                res.addFriendData(
                        PlatFriendData.newBuilder().setUid(friendUid).setOpenid(dbPlayerInfo.getFriendOpenId()));
            });
        }
        return RpcResult.create(res);
    }

    @Override
    public RpcResult<RpcUgcGetPlayerPublicDataRes.Builder> rpcUgcGetPlayerPublicData(
            RpcUgcGetPlayerPublicDataReq.Builder req) throws RpcException, NKTimeoutException {
        RpcUgcGetPlayerPublicDataRes.Builder res = RpcUgcGetPlayerPublicDataRes.newBuilder();
        HashSet<PlayerPublicAttrKey> keys = new HashSet<>();
        req.getAttrFieldsList().forEach(field -> {
            keys.add(PlayerPublicAttrKey.valueOf(field));
        });
        PlayerPublicAttrKey[] attrKeys = keys.toArray(new PlayerPublicAttrKey[keys.size()]);
        LOGGER.debug("getPublic:{}", () -> (Object[]) attrKeys);
        final int ugcGetPlayerPublicDataMax = PropertyFileReader.getRealTimeIntItem("ugc_get_player_public_data_max", 10);
        Set<Long> uidSet = new HashSet<>();
        for (int i = 0; i < req.getUidCount() && i < ugcGetPlayerPublicDataMax; ++i) {
            var uid = req.getUid(i);
            uidSet.add(uid);
        }
        if (req.getUidCount() > ugcGetPlayerPublicDataMax) {
            LOGGER.info("uid list count:{} large then ugcGetPlayerPublicDataMax:{}", req.getUidCount(), ugcGetPlayerPublicDataMax);
        }
        Map<Long, PlayerPublic> playerPublicMap = PlayerPublicDao.batchGetPlayerPublicMap(uidSet, attrKeys);
        res.addAllPublicData(playerPublicMap.values());
        return RpcResult.create(res);
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcTestAddTestPlayerListRes.Builder> rpcUgcTestAddTestPlayerList(
            SsGamesvr.RpcUgcTestAddTestPlayerListReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            NKErrorCode resCode = player.handleUgcTestAddTestPlayerList(req.build());
            return RpcResult.create(resCode);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcUgcTestAddTestPlayerList NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcUgcTestAddTestPlayerList PlayerNotFoundException uid:{}, e:",
                    req.getPlayerUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcCoCreateInviteRes.Builder> rpcCoCreateInvite(RpcCoCreateInviteReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreateInviteMsgNtf(req.build());
            return RpcResult.create(resCode);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcCoCreateInvite NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcCoCreateInvite PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public Void rpcXiaoWoAttrNtf(SsGamesvr.RpcXiaoWoAttrNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            var ntf = CsXiaowo.XiaoWoAttrNtf.newBuilder();
            ntf.setXiaowoId(req.getXiaoWoId()).setIsFull(req.getIsFull());
            ntf.setXiaowoAttr(req.getXiaowoAttr());
            player.xiaoWoAttrNtf(ntf);
        } catch (Exception e) {
            LOGGER.error("rpcXiaoWoAttrNtf uid:{}, e:",
                    req.getUid(), e);
        }
        return null;
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     * @throws NKTimeoutException
     */
    @Override
    public Void rpcXiaoWoDsInfoNtf(RpcXiaoWoDsInfoNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        var dsNtf = CsXiaowo.XiaoWoDSInfoNtf.newBuilder();
        dsNtf.setGroupID(req.getGroupID());
        dsNtf.setDsAddr(req.getDsAddr());
        dsNtf.setAliasId(req.getAliasId());
        dsNtf.setDesModInfo(req.getDesModInfo());
        dsNtf.setDsaServiceId(req.getDsaServiceId());
        dsNtf.setDsaServiceName(req.getDsaServiceName());
        dsNtf.setDsAuthToken(req.getDsAuthToken());
        dsNtf.setFleetId(req.getFleetId());
        dsNtf.setGameSessID(req.getGameSessID());
        dsNtf.setXiaowoType(req.getXiaowoType());
        player.dsInfoNtf(dsNtf);
        return null;
    }

    @Override
    public RpcResult<RpcCoCreatorModifyRes.Builder> rpcCoCreatorModify(RpcCoCreatorModifyReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreatorModify(req.build());
            return RpcResult.create(resCode);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcCoCreatorModify NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcCoCreatorModify PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcPlayerStreamSwitchRes.Builder> rpcPlayerStreamSwitch(RpcPlayerStreamSwitchReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcPlayerStreamSwitchRes.Builder rspBuilder = RpcPlayerStreamSwitchRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            int resCode = player.streamSwitchOn(req);
            rspBuilder.setResult(resCode);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcPlayerStreamSwitch NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            rspBuilder.setResult(e.getEnumErrCode().getValue());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcPlayerStreamSwitch PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            rspBuilder.setResult(e.getEnumErrCode().getValue());
        }
        return RpcResult.create(rspBuilder);
    }

    @Override
    public RpcResult<RpcNotifyStreamInteractionCmdRes.Builder> rpcNotifyStreamInteractionCmd(RpcNotifyStreamInteractionCmdReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcNotifyStreamInteractionCmdRes.Builder rspBuilder = RpcNotifyStreamInteractionCmdRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            int resCode = player.notifyStreamInteractionCmd(req);
            rspBuilder.setResult(resCode);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcPlayerStreamSwitch NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            rspBuilder.setResult(e.getEnumErrCode().getValue());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcPlayerStreamSwitch PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            rspBuilder.setResult(e.getEnumErrCode().getValue());
        }
        return RpcResult.create(rspBuilder);
    }

    @Override
    public Void rpcUgcCreatorInfoChangeNtf(SsGamesvr.RpcUgcCreatorInfoChangeNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onUgcCreatorInfoChange(req.getCreatorInfo());
        } catch (Exception e) {
            LOGGER.error("ugc ntf game creator info changed failed, e", e);
        }
        return null;
    }

    @Override
    public Void rpcUgcCoCreateEditor(RpcUgcCoCreateEditorReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player != null) {
                player.handleEditorUgcMapNtf(req.getLayerId(), req.getNickName(), req.getEditorUid(), req.getUgcId(),
                        req.getItemInfosList());
            }
        } catch (Exception e) {
            LOGGER.error("ugc ntf game creator info changed failed, e", e);
        }
        return null;
    }

    @Override
    public Void rpcXiaoWoEventNtf(SsGamesvr.RpcXiaoWoEventNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onXiaowoEvent(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcXiaoWoEventNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcFarmEventNtf(SsGamesvr.RpcFarmEventNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onFarmEvent(req.build());
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcFarmEventNtf NKCheckedException uid:{}, e:",
                    req.getUid(), e);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcFarmEventNtf PlayerNotFoundException uid:{}",
                    req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcFarmHotSpringAchievementNtf(RpcFarmHotSpringAchievementNtfReq.Builder req) throws RpcException {
        try {
                PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
                player.onFarmHotSpringAchievementNtf(req.build());
            } catch (Exception e) {
                LOGGER.error("handle rpcFarmEventNtf failed, error: ", e);
        }
        return null;
    }

    public Void rpcCocEventNtf(SsGamesvr.RpcCocEventNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onCocEvent(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcFarmEventNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcFarmHotSpringTickNtf(SsGamesvr.RpcFarmHotSpringTickNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onFarmHotSpringTickNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcFarmEventNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcXiaowoTreeInfoNtf(SsGamesvr.RpcXiaowoTreeInfoNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            var ntf = CsXiaowo.XiaowoTreeInfoNtf.newBuilder();
            ntf.setLevel(req.getLevel());
            ntf.addAllTrees(req.getTreesList());
            player.xiaowoTreeInfoNtf(ntf);
        } catch (Exception e) {
            LOGGER.error("handle rpcXiaowoTreeInfoNtf failed, error: ", e);
        }
        return null;
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public Void rpcXiaoWoVerifyNtf(RpcXiaoWoVerifyNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.rpcXiaoWoVerifyNtf(req);
        return null;
    }

    @Override
    public Void rpcUgcCoCreatorDing(RpcUgcCoCreatorDingReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleSendUgcSaveNtf(req.getCreatorId(), req.getOwnUid(), req.getNickName(), req.getEditorType());
        } catch (Exception e) {
            LOGGER.error("ugc ntf game creator info changed failed, e", e);
        }
        return null;
    }

    @Override
    public Void rpcTeamJoinRoomConfirmNtf(RpcTeamJoinRoomConfirmNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.teamJoinRoomConfirmNtf(req.getUniqueId(), req.getRoomBriefInfo(), req.getExtraInfo());
        } catch (Exception e) {
            LOGGER.error("ugc ntf game creator info changed failed, e", e);
        }
        return null;
    }

    @Override
    public Void rpcReportTlogDataNtf(SsGamesvr.RpcReportTlogDataNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerId());
            player.handleRpcReportTlogDataNtfReq(req);
        } catch (Exception e) {
            LOGGER.error("handleRpcReportTlogDataNtfReq failed, exception {}", e);
        }
        return null;
    }

    @Override
    public Void rpcXiaowoHotNtf(SsGamesvr.RpcXiaowoHotNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleXiaowoHotNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle xiaowohotntf failed, {}", e);
        }

        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcPlatAddFriendRes.Builder> rpcPlatAddFriend(SsGamesvr.RpcPlatAddFriendReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcPlatAddFriendRes.Builder rspBuilder = RpcPlatAddFriendRes.newBuilder();
        rspBuilder.setResult(0);
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handlePlatAddFriendReq(req.getFriendUid());
        } catch (Exception e) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("rpcPlatAddFriend uid:{}, e:", req.getUid(), e);
            }
            // 不在线
            rspBuilder.setResult(OfflineRelationMgr.platAddFriend(req.getUid(), req.getFriendUid()));
        }
        return RpcResult.create(rspBuilder);
    }

    @Override
    public Void rpcBroadcastMsg(SsGamesvr.RpcBroadcastMsgReq.Builder req) throws RpcException {
        Message msg;
        try {
            msg = MsgTypes.parseFrom((int) req.getMsgId(), req.getMsgBody().toByteArray(), 0,
                    req.getMsgBody().size());
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("rpcBroadcastMsg failed, msgId:{}, e:{}", req.getMsgId(), e.toString());
            return null;
        }
        NoticeManager.getInstance().broadcastMsgToLocalServer((int) req.getMsgId(), msg.toBuilder());
        LOGGER.debug("rpcBroadcastMsg success, msgId:{}", req.getMsgId());
        return null;
    }


    @Override
    public Void rpcCompetitionCommonNtf(RpcCompetitionCommonNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            var ntf = CsCompetition.CompetitionCommonNtf.newBuilder().
                    setCmd(req.getCmd()).
                    setBody(req.getBody()).
                    setTimestamp(req.getTimestamp());
            player.competitionCommonNtf(ntf);
        } catch (Exception e) {
            LOGGER.error("handle rpcCompetitionCommonNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcCompetitionPromoteEvent(RpcCompetitionPromoteEventReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            player.CompetitionPromoteEvent(req.getSeason(), req.getGameType(), req.getCompType());
        } catch (Exception e) {
            LOGGER.error("handle rpcCompetitionPromoteEvent failed, error: ", e);
        }
        return null;
    }

    public RpcResult<SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.Builder> rpcRoomMidJoinFriendBattleSvr(
            SsGamesvr.RpcRoomMidJoinFriendBattleSvrReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.Builder res = null;
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            res = player.roomMidJoinFriendBattleSvr(req);
        }

        if (res == null) {
            res = SsGamesvr.RpcRoomMidJoinFriendBattleSvrRes.newBuilder()
                    .setResult(NKErrorCode.PlayerSearchNoResults.getValue());
        }
        return RpcResult.create(res);
    }

    @Override
    public RpcResult<RpcExitPlayerLastSceneRes.Builder> rpcExitPlayerLastScene(RpcExitPlayerLastSceneReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            player.handleExitLastScene(req);
        } catch (Exception e) {
            LOGGER.error("handle rpcExitPlayerLastScene failed, error: ", e);
        }
        return RpcResult.create(RpcExitPlayerLastSceneRes.newBuilder());
    }

    public Void rpcAigcGenVoiceResNtf(SsGamesvr.RpcAigcGenVoiceResNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleAigcGenVoiceResNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcAigcGenVoiceResNtf failed, {}", e);
        }

        return null;
    }

    @Override
    public Void rpcAigcGenImageResNtf(SsGamesvr.RpcAigcGenImageResNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleAigcGenImageResNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcAigcGenImageResNtf failed, {}", e);
        }

        return null;
    }

    @Override
    public Void rpcAigcChangeColorResNtf(SsGamesvr.RpcAigcChangeColorResNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleAigcChangeColorResNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcAigcChangeColorResNtf failed, {}", e);
        }

        return null;
    }

    @Override
    public Void rpcAigcGenAnicapResNtf(SsGamesvr.RpcAigcGenAnicapResNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleAigcGenAnicapResNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcAigcGenAnicapResNtf failed, {}", e);
        }

        return null;
    }

    @Override
    public Void rpcAigcGenAnswerResNtf(SsGamesvr.RpcAigcGenAnswerResNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleAigcGenAnswerResNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcAigcGenAnswerResNtf failed, {}", e);
        }
        return null;
    }


    @Override
    public Void rpcXiaoWoMoneyTreeDropReturn(SsGamesvr.RpcXiaoWoMoneyTreeDropReturnReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleMoneyTreeDropReturn(req);
        } catch (Exception e) {
            LOGGER.error("handle rpcXiaoWoMoneyTreeDropReturn failed, error: ", e);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcConfirmAssistFriendRes.Builder> rpcConfirmAssistFriend(
            SsGamesvr.RpcConfirmAssistFriendReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getFriendUid());
            if (player != null) {
                return RpcResult.create(player.rpcConfirmAssistFriend(req.getUid(), req.getActivityId()));
            }
            return RpcResult.create(NKErrorCode.UserNotFound);
        } catch (NKCheckedException e) {
            LOGGER.error("rpcConfirmAssistFriend NKCheckedException uid:{}", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcConfirmAssistFriend PlayerNotFoundException uid:{}", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public Void rpcClubNewApplyNtf(RpcClubNewApplyNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            return player.rpcClubNewApplyNtf(req.getCid(), req.getTimestamp());
        }

        return null;
    }

    @Override
    public Void rpcReputationScoreNotEnoughNtf(SsGamesvr.RpcReputationScoreNotEnoughNtfReq.Builder req)
            throws RpcException {
        LOGGER.debug("rpcReputationScoreNotEnoughNtf");
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleReputationScoreNotEnoughNtf(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcReputationScoreNotEnoughNtf failed, {}", e);
        }
        return null;
    }

    @RpcOneWay
    @Override
    public Void rpcSuccessInviteFriendLogin(SsGamesvr.RpcSuccessInviteFriendLoginReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getFriendUid());
        if (player != null) {
            player.rpcSuccessInviteFriendLogin(req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<RpcRegionClubPiiRes.Builder> rpcRegionClubPii(RpcRegionClubPiiReq.Builder req)
            throws RpcException, NKTimeoutException {
        PlayerInteraction.PlayerInteractionData.Builder piiBuilder = PlayerInteraction.PlayerInteractionData.newBuilder()
                .setInstruction(PlayerInteraction.PlayerInteractionInstruction.PII_CLUB_CHANGE);
        piiBuilder.getClubChangeParamsBuilder()
                .setChangeType(req.getChangeType().getNumber())
                .setBasicInfo(req.getBasicInfo());
        PlayerInteractionInvoker.interact(req.getUid(), piiBuilder);
        return RpcResult.create(0, RpcRegionClubPiiRes.newBuilder());
    }

    @Override
    public RpcResult<RpcCrossDBRes.Builder> rpcCrossDB(RpcCrossDBReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            switch (req.getDbMessageCase()) {
                case INTERACTION:
                    TcaplusManager.TcaplusRsp ret = TcaplusUtil.newInsertReq(req.getInteraction().toBuilder()).send();
                    if (!ret.isOK()) {
                        LOGGER.error("interact data save failed, dest:{}, body:{}, err:{}", req.getCrossKey(),
                                req.getInteraction(), ret.getResult());
                    }
                    break;
                case DBMESSAGE_NOT_SET:
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("handle rpcExitPlayerLastScene failed, error: ", e);
        }
        return RpcResult.create(RpcCrossDBRes.newBuilder());
    }

    @Override
    public RpcResult<RpcBatchGetPlayerPublicInfoRes.Builder> rpcBatchGetPlayerPublicInfo(
            RpcBatchGetPlayerPublicInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        if (req.getUidsCount() == 0) {
            NKErrorCode.InvalidParams.throwError("uid empty");
            return null;
        }
        List<PlayerPublicInfoField> reqFields = req.getFieldsList();
        List<PlayerPublicInfoField> fields = reqFields;
        boolean isContainLogoutTime = reqFields.contains(PlayerPublicInfoField.LOGOUT_TIME_MS);
        if (isContainLogoutTime) {
            fields = new ArrayList<>(reqFields.size() + 1);
            fields.addAll(reqFields);
            fields.add(PlayerPublicInfoField.LAST_KEEP_ALIVE_TIME);
        }

        List<Long> reqList = new ArrayList<>();
        reqList = req.getUidsList();

        RpcBatchGetPlayerPublicInfoRes.Builder rspMsg = RpcBatchGetPlayerPublicInfoRes.newBuilder();
        if (!reqList.isEmpty()) {
            Map<Long, PlayerPublicInfo.Builder> playerPublicInfoList = SimpleDataManager.batchGetPlayerPublic(null,
                    reqList, fields, req.getIgnoreHidePersonalProfile(), req.getSource());
            playerPublicInfoList.forEach((uid, data) -> {
                if (isContainLogoutTime) {
                    data.setLogoutTimeMs(data.getLastKeepAliveTime());
                }
                rspMsg.addPlayerPublicInfoList(data.build());
            });
        }

        return RpcResult.create(rspMsg);
    }

    @Override
    public Void rpcVideoExamineResponseNtf(RpcVideoExamineResponseNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleVideoExamineResponseNtf(req);
        } catch (Exception e) {
            LOGGER.error("handle rpcReputationScoreNotEnoughNtf failed, {}", e);
        }
        return null;
    }

    @Override
    public Void rpcRedPacketDestroyNtf(RpcRedPacketDestroyNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.handleRedPacketDestroyNtf(req);
        } catch (Exception e) {
            for (long packetUuid : req.getPacketUuidListList()) {
                NKErrorCode ret = RedPacketDestroyMgr.handle(packetUuid);
                if (!ret.isOk()) {
                    LOGGER.error("handleRedPacketDestroyNtf failed, packetUuid:{}, ret:{}", packetUuid, ret);
                }
            }
        }
        return null;
    }

    /**
     * @param req
     * @return
     * @throws RpcException
     */
    @Override
    public Void rpcPlayerNoticeMsgNtf(RpcPlayerNoticeMsgNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
        if (player != null) {
            player.rpcPlayerNoticeMsgNtf(req);
        }
        return null;
    }


    @Override
    public Void rpcIntellectualActivity(RpcIntellectualActivityReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.handleIntellectualActivityNtf(req);
        }

        return null;
    }

    @Override
    public Void rpcGiveSpringBlessingCard(Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getTargetPlayerUid());
        if (player != null) {
            player.rpcReceiveSpringBlessingCard(req.getSourcePlayerUid(), req.getBlessingCardId());
        }

        return null;
    }

    @Override
    public Void rpcIntellectualActivityCommon(RpcIntellectualActivityCommonReq.Builder req) throws RpcException {
        if (req.getType() == IntellectualActivityOperation.Player_Realtime_Number) {
            IntellectualActivityDao.setCommonNtf(req.getActivityId(), req.getVer(), req.getData());
        } else if (req.getType() == IntellectualActivityOperation.Activity_End) {
            IntellectualActivityDao.setActivityEndTime(req.getActivityId(), req.getEndTimeSec());
        } else {
            LOGGER.error("type {} not implememt in rpcIntellectualActivityCommon", req.getType());
        }
        return null;
    }

    @Override
    public Void rpcXiaoWoReceiveNewLiuYanMessageNtf(SsGamesvr.RpcXiaoWoReceiveNewLiuYanMessageNtfReq.Builder req)
            throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            CsXiaowo.XiaoWoReceiveNewLiuYanMessageNtf.Builder ntf = CsXiaowo.XiaoWoReceiveNewLiuYanMessageNtf.newBuilder();
            ntf.setNumber(req.getNumber());
            player.xiaowoReceiveNewLiuYanMessageNtf(ntf);
        }
        return null;
    }

    @Override
    public RpcResult<RpcRoomReservationNtfRes.Builder> rpcRoomReservationNtf(
            SsGamesvr.RpcRoomReservationNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            NKErrorCode res = player.roomReservationNtf(req.getReservationUid());
            return RpcResult.create(res.getValue());
        } else {
            return RpcResult.create(NKErrorCode.UserNotFound.getValue());
        }
    }

    @Override
    public Void rpcRoomReservationResponseNtf(SsGamesvr.RpcRoomReservationResponseNtfReq.Builder req)
            throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.roomReservationResponseNtf(req.getReservationUid(), req.getResponse());
        }
        return null;
    }

    @Override
    public Void rpcClubSyncNtf(RpcClubSyncNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.handleClubSyncNtf(req.build());
        }
        return null;
    }

    @RpcOneWay
    public Void rpcDanMuSendNtf(SsGamesvr.RpcDanMuSendNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onDanMuSendNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcDanMuSendNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcDanMuSendNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcUgcCoPlayRecordNtf(SsGamesvr.RpcUgcCoPlayRecordNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.rpcUgcCoPlayRecordNtf(req);
            return null;
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcUgcCoPlayRecordNtf NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return null;
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcUgcCoPlayRecordNtf PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return null;
        }
    }

    @Override
    public Void rpcForwardBattleSettlementNtf(SsGamesvr.RpcForwardBattleSettlementNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.rpcForwardBattleSettlementNtf(req);
            return null;
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcForwardBattleSettlementNtf NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return null;
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcForwardBattleSettlementNtf PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return null;
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder> rpcGetPlayerFriendListForIdip(
            SsGamesvr.RpcGetPlayerFriendListForIdipReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcGetPlayerFriendListForIdipRes.Builder builder = playerRef.rpcGetPlayerFriendListForIdip(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), builder);
        } catch (NKCheckedException nkErr) {
            LOGGER.error(
                    "GsImpl::rpcGetPlayerFriendListForIdip NKCheckedException uid:{}, friendType:{}, page:{}, e:{}",
                    req.getUid(), req.getFriendType(), req.getPage(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcGetPlayerFriendListForIdip PlayerNotFound uid:{}, friendType:{}, page:{}",
                    req.getUid(), req.getFriendType(), req.getPage());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcGetPlayerHasFriendRes.Builder> rpcGetPlayerHasFriend(RpcGetPlayerHasFriendReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcGetPlayerHasFriendRes.Builder builder = playerRef.rpcGetPlayerHasFriend(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), builder);
        } catch (NKCheckedException nkErr) {
            LOGGER.error(
                    "GsImpl::rpcGetPlayerHasFriend NKCheckedException uid:{}, friend:{}, e:{}",
                    req.getUid(), req.getFriendUid(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcGetPlayerHasFriend PlayerNotFound uid:{}, friend:{}", req.getUid(), req.getFriendUid());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcRoomOperationNtf(SsGamesvr.RpcRoomOperationNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.rpcRoomOperationNtf(req);
            return null;
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcForwardBattleSettlementNtf NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return null;
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcForwardBattleSettlementNtf PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return null;
        }
    }

    @RpcOneWay
    public Void rpcDanMuDeleteNtf(SsGamesvr.RpcDanMuDeleteNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onDanMuDeleteNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcDanMuDeleteNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcDanMuDeleteNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @RpcOneWay
    public Void rpcDanMuLikeNtf(SsGamesvr.RpcDanMuLikeNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onDanMuLikeNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcDanMuLikeNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcDanMuLikeNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<RpcStreamLoginRes.Builder> rpcStreamLogin(RpcStreamLoginReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcStreamLoginRes.Builder res = playerRef.rpcStreamLogin(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), res);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLogin NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLogin PlayerNotFound uid:{}", req.getUid());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcStreamLogout(RpcStreamLogoutReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcStreamLogout(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLogin NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLogin PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<RpcStreamNpcChatRes.Builder> rpcStreamNpcChat(RpcStreamNpcChatReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcStreamNpcChatRes.Builder res = playerRef.rpcStreamNpcChat(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), res);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLogin NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLogin PlayerNotFound uid:{}", req.getUid());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }
    }

    @Override
    public Void rpcBattlePlayerClientInfoModifyNtf(SsGamesvr.RpcBattlePlayerClientInfoModifyNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcBattlePlayerClientInfoModifyNtf(req);

        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLogin NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLogin PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcSendRoomMemberToMemberNtfToPlayer(SsGamesvr.RpcSendRoomMemberToMemberNtfToPlayerReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcSendRoomMemberToMemberNtfToPlayer(req);

        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcSendRoomMemberToMemberNtfToPlayer NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcSendRoomMemberToMemberNtfToPlayer PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcActivityOfReward(SsGamesvr.RpcActivityOfRewardReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onActivityOfRewardNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcActivityOfReward NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcActivityOfReward PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
        }

    @Override
    public RpcResult<SsGamesvr.RpcActivityReceiveRewardsNtfRes.Builder> rpcActivityReceiveRewardsNtf(
            SsGamesvr.RpcActivityReceiveRewardsNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            return RpcResult.create(NKErrorCode.OK.getValue(), playerRef.onActivityReceiveRewardsNtf(req));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcActivityReceiveRewardsNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcActivityReceiveRewardsNtf PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcActivityTeamUpdateNtf(SsGamesvr.RpcActivityTeamUpdateNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onActivityTeamUpdateNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcActivityTeamUpdateNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcActivityTeamUpdateNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }
    

    @Override
    public RpcResult<SsGamesvr.RpcAddPrayerCardRes.Builder> rpcAddPrayerCard(SsGamesvr.RpcAddPrayerCardReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode errorCode = playerRef.addPrayerCard(req.getItemId(), req.getItemNum());
            return RpcResult.create(errorCode);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcAddPrayerCard NKCheckedException uid:{}, e:",
                    req.getUid(), nkErr);
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcAddPrayerCard PlayerNotFound uid:{}",
                    req.getUid());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        }

    }


    @Override
    public RpcResult<SsGamesvr.RpcActivityGeneralNtfRes.Builder> rpcActivityGeneralNtf(
            SsGamesvr.RpcActivityGeneralNtfReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            return RpcResult.create(NKErrorCode.OK.getValue(), playerRef.onActivityGeneralNtf(req));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcActivityReceiveRewardsNtf NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcActivityReceiveRewardsNtf PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcStreamLLMCheckRes.Builder> rpcStreamLLMCheck(
            SsGamesvr.RpcStreamLLMCheckReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcStreamLLMCheckRes.Builder res = playerRef.rpcStreamLLMCheck(req);
            return RpcResult.create(NKErrorCode.OK.getValue(), res);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLLMCheck NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
            return RpcResult.create(nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLLMCheck PlayerNotFound uid:{}", req.getUid());
            return RpcResult.create(notFoundErr.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcRoomMidJoinBattleFailNtf(SsGamesvr.RpcRoomMidJoinBattleFailNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcRoomMidJoinBattleFailNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcStreamLogin NKCheckedException uid:{}, e:{}",
                    req.getUid(), nkErr.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.warn("GsImpl::rpcStreamLogin PlayerNotFound uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<RpcGetUgcBuyPartnerInfoRes.Builder> rpcGetUgcBuyPartnerInfo(RpcGetUgcBuyPartnerInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerUgcBuyPartnerTable table = MidasManager.getInstance()
                    .getUgcBuyPartner(req.getCreatorId(), req.getIsApply());
            RpcGetUgcBuyPartnerInfoRes.Builder res = RpcGetUgcBuyPartnerInfoRes.newBuilder();
            if (table != null) {
                return RpcResult.create(NKErrorCode.OK.getValue(), res.setInfo(table));
            }
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcGetUgcBuyPartnerInfo NKCheckedException uid:{}, e:{}",
                    req.getCreatorId(), nkErr.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
        return RpcResult.create(NKErrorCode.OK.getValue());
    }

    @Override
    public RpcResult<RpcGetWolfLastBattleTimeRes.Builder> rpcGetWolfLastBattleTime(RpcGetWolfLastBattleTimeReq.Builder req) {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetWolfLastBattleTimeRes.Builder res = playerRef.rpcGetWolfLastBattleTime(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcGetWolfLastBattleTime NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<RpcGetPlayerHasItemRes.Builder> rpcGetPlayerHasItem(RpcGetPlayerHasItemReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetPlayerHasItemRes.Builder res = playerRef.rpcGetPlayerHasItem(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcGetPlayerHasItem NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
    }
    }

    
    @Override
    public RpcResult<SsGamesvr.RpcUnlockCardRes.Builder> rpcUnlockCard(SsGamesvr.RpcUnlockCardReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            return RpcResult.create(playerRef.rpcUnlockCard(req));
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcUnlockCard NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcCheckCardLockRes.Builder> rpcCheckCardLock(SsGamesvr.RpcCheckCardLockReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcCheckCardLockRes.Builder res = playerRef.rpcCheckCardLock(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcCheckCardLock NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcFarmReceiveNewLiuYanMessageNtf(RpcFarmReceiveNewLiuYanMessageNtfReq.Builder req)
            throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            CsFarm.FarmReceiveNewLiuYanMessageNtf.Builder ntf = CsFarm.FarmReceiveNewLiuYanMessageNtf.newBuilder();
            ntf.setNumber(req.getNumber());
            player.farmReceiveNewLiuYanMessageNtf(ntf);
        }
        return null;
    }


    
    @Override
    public RpcResult<RpcGetPlayerRoomInfoRes.Builder> rpcGetPlayerRoomInfo(RpcGetPlayerRoomInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetPlayerRoomInfoRes.Builder res = playerRef.rpcGetPlayerRoomInfo(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcGetPlayerRoomInfo NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    public RpcResult<SsGamesvr.RpcApplyAppCreatorIdRes.Builder> rpcApplyAppCreatorId(
            SsGamesvr.RpcApplyAppCreatorIdReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            RpcApplyAppCreatorIdRes.Builder res = RpcApplyAppCreatorIdRes.newBuilder()
                    .setPlatOpenid(req.getPlatOpenid())
                    .setCreatorId(PlayerUgcManager.applyAppCreatorId(req.getPlatOpenid()));
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<RpcNoticeTeamPartnerJoinMiniGameRes.Builder> rpcNoticeTeamPartnerJoinMiniGame(
            RpcNoticeTeamPartnerJoinMiniGameReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcNoticeTeamPartnerJoinMiniGameRes.Builder res = playerRef.rpcNoticeTeamPartnerJoinMiniGame(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcNoticeTeamPartnerJoinMiniGame NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @RpcOneWay
    public Void rpcActivityInfoNtf(SsGamesvr.RpcActivityInfoNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.onActivityInfoNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("GsImpl::rpcActivityInfoNtf NKCheckedException uid:{}, req:{}, e:{}",
                    req.getUid(), req, nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("GsImpl::rpcActivityInfoNtf PlayerNotFoundException uid:{}, req:{}, e:{}",
                    req.getUid(), req, notFoundErr);
        }
        return null;
    }


    
    @Override
    public RpcResult<SsGamesvr.RpcRoomAskToJoinRes.Builder> rpcRoomAskToJoin(SsGamesvr.RpcRoomAskToJoinReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcRoomAskToJoinRes.Builder res = playerRef.rpcRoomAskToJoin(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcRoomAskToJoin NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcNoticePlayerDoJoinRoomRes.Builder> rpcNoticePlayerDoJoinRoom(
            RpcNoticePlayerDoJoinRoomReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcNoticePlayerDoJoinRoomRes.Builder res = playerRef.rpcNoticePlayerDoJoinRoom(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcNoticePlayerDoJoinRoom NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcPlayerReputationScoreBeReported(SsGamesvr.RpcPlayerReputationScoreBeReportedReq.Builder req)
            throws RpcException {
        if (!req.hasUid() || req.getUid() == 0L) {
            return null;
        }

        try {
            // 玩家在线信誉分被举报流程处理
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (playerRef != null) {
                playerRef.rpcPlayerReputationScoreBeReported(req);
            }
        } catch (Exception ex) {
            // 玩家离线信誉分被举报流程处理
            PlayerReputationScoreMgr.offlinePlayerReputationScoreBeReported(req.getUid(), req.getBattleId(),
                    req.getReporterUid(), req.getReportBehaviorId(), req.getModeId(), req.getCheckResult(),
                    req.getNickName());
        }

        Monitor.getInstance().add.total(MonitorId.attr_reputation_score_battle_report, 1, new String[]{
                ReputationScoreUtil.REPUTATION_SCORE_OPERATE_SVR_GAMESVR
        });

        return null;
    }

    @Override
    public Void rpcMatchReputationScoreNoEnoughNtf(SsGamesvr.RpcMatchReputationScoreNoEnoughNtfReq.Builder req)
            throws RpcException {
        if (!req.hasUid() || req.getUid() == 0L) {
            return null;
        }

        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcMatchReputationScoreNoEnoughNtf(req);
        } catch (NKCheckedException ex) {
            LOGGER.error("rpcMatchReputationScoreNoEnoughNtf catch NKCheckedException, uid:{}, ",
                    req.getUid(), ex);
        } catch (PlayerNotFoundException ex) {
            LOGGER.error("rpcMatchReputationScoreNoEnoughNtf catch PlayerNotFoundException, uid:{}, ",
                    req.getUid(), ex);
        }

        return null;
    }

    @Override
    public Void rpcPlayerTaskChangeNtf(SsGamesvr.RpcPlayerTaskChangeNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcTaskChangeNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcPlayerTaskChangeNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @RpcOneWay
    public Void rpcUgcMatchBattleRecordNtf(SsGamesvr.RpcUgcMatchBattleRecordNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcUgcMatchBattleRecordNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcUgcMatchBattleRecordNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @RpcOneWay
    public Void rpcNtfUpdateRunActivityIds(SsGamesvr.RpcNtfUpdateRunActivityIdsReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.setPlayerActivitySvrRunActivity(req.getCurRunActivityIdsList());
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcUgcMatchBattleRecordNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcNtfDeleteItemRes.Builder> rpcNtfDeleteItem(SsGamesvr.RpcNtfDeleteItemReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcNtfDeleteItemRes.Builder res = playerRef.rpcNtfDeleteItem(req);
            //tlog通知GameSvr删除道具RPC
            TlogFlowMgr.sendActivityOfRemoveItemRPCFlow(req.getUid(), req.getActivityId(), res.getResult(), req.getOrderNo());
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException ce) {
            LOGGER.error("GsImpl::rpcNtfDeleteItem NKCheckedException uid:{}", req.getUid(), ce);
            return RpcResult.create(ce.getEnumErrCode());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcNtfDeleteItem Exception uid:{}", req.getUid(), e);
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }


    @Override
    public RpcResult<SsGamesvr.RpcGetItemCountListRes.Builder> rpcGetItemCountList(SsGamesvr.RpcGetItemCountListReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcGetItemCountListRes.Builder builder = SsGamesvr.RpcGetItemCountListRes.newBuilder();
            for (Integer id : req.getItemIdList()) {
                long itemNumByItemId = playerRef.getItemNumByItemId(id);
                builder.putItems(id, itemNumByItemId);
            }
            return RpcResult.create(NKErrorCode.OK, builder);
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcGetItemCountRes.Builder> rpcGetItemCount(SsGamesvr.RpcGetItemCountReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            return RpcResult.create(NKErrorCode.OK,
                    SsGamesvr.RpcGetItemCountRes.newBuilder().setCount(playerRef.getItemNumByItemId(req.getItemId())));
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @RpcOneWay
    @Override
    public Void rpcSyncFriendInteractHistory(RpcSyncFriendInteractHistoryReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.syncFriendInteractHistoryData(req.getFriendUid(), req.getType(), req.getNewValue());
        } catch (Exception e) {
            LOGGER.error("rpcSyncFriendInteractHistory uid:{}, friend uid:{}, data:{}-{} error ",
                    req.getUid(), req.getFriendUid(), req.getType(), req.getNewValue(), e);
        }

        return null;
    }

    @Override
    public RpcResult<RpcGetPermanentItemCountRes.Builder> rpcGetPermanentItemCount(RpcGetPermanentItemCountReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            RpcGetPermanentItemCountRes.Builder res = RpcGetPermanentItemCountRes.newBuilder();
            for (int itemId : req.getItemIdList()) {
                long itemNum = playerRef.getItemNumByItemIdIgnoreTemp(itemId);
                res.putItemCountMap(itemId, itemNum);
            }
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcClientLogColoringRes.Builder> rpcClientLogColoring(
            SsGamesvr.RpcClientLogColoringReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (playerRef != null) {
                SsGamesvr.RpcClientLogColoringRes.Builder res = playerRef.rpcClientLogColoring(req);
                return RpcResult.create(NKErrorCode.OK, res);
            }
            SsGamesvr.RpcClientLogColoringRes.Builder res = SsGamesvr.RpcClientLogColoringRes.newBuilder();
            res.setCode(NKErrorCode.UserNotFound.getValue())
                    .setMsg("PlayerRef not found");
            return RpcResult.create(NKErrorCode.UserNotFound, res);
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            PiiClientLogColoringParams.Builder interactReq = ClientLogColoring.trans2interaction(req);
            boolean interactRet = PlayerInteractionInvoker.interact(req.getUid(),
                    PlayerInteractionData.newBuilder().setClientLogColoringParams(interactReq)
                            .setInstruction(PlayerInteractionInstruction.PII_CLIENT_LOG_COLORING));
            SsGamesvr.RpcClientLogColoringRes.Builder res = SsGamesvr.RpcClientLogColoringRes.newBuilder();
            res.setCode(interactRet ? NKErrorCode.OK.getValue() : NKErrorCode.UnknownError.getValue())
                    .setMsg(interactRet ? "saved" : "failed");
            return RpcResult.create(NKErrorCode.OK.getValue(), res);
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcDataStoreKvFragmentNtf(SsGamesvr.RpcDataStoreKvFragmentNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.handleDataStoreKvFragmentNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::DataStoreKvFragmentNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcDsActivityInfoUpdateNtf(SsGamesvr.RpcDsActivityInfoUpdateNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            ByteString activityCacheData = req.getDsAllActivityInfo();
            playerRef.setPlayerActivityCacheData(activityCacheData);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcDsActivityInfoUpdateNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcActivityReadDotNtf(SsGamesvr.RpcActivityReadDotNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcActivityReadDotNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcActivityReadDotNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    public RpcResult<SsGamesvr.RpcUgcCreateRoleRes.Builder> rpcUgcCreateRole(SsGamesvr.RpcUgcCreateRoleReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcUgcCreateRoleRes.Builder res = RpcUgcCreateRoleRes.newBuilder();
        int size = req.getOpenIdList().size();
        JSONArray ret = new JSONArray();
        for (int i = 0; i < size; i++) {
            String openId = req.getOpenId(i);
            String name = URLDecoder.decode(req.getName(i), StandardCharsets.UTF_8);
            JSONObject ob = new JSONObject();
            ret.put(ob);
            try {
                LOGGER.error("create role ugc openid={} name={}", openId, name);
                // 检查tcaplus表是否有这个角色openId,platId
                int platId = req.getPlatId();
                long uid = OpenIdToUidDao.getUidByOpenIdAndPlatId(openId, platId);
                if (uid == 0) {
                    String clientVersion = req.getClientVersion();
                    if (clientVersion.isEmpty()) {
                        clientVersion = VersionCompCtrlConf.getInstance().getMaxVersion();
                    }

                    String resVersion = req.getResVersion();
                    if (resVersion.isEmpty()) {
                        resVersion = "1.1.1.1";
                    }

                    LOGGER.error("create role ugc openid={} name={} clientVersion={} resVersion={}", openId, name,
                            clientVersion, resVersion);

                    String result = UgcRegisterTool.sendLoginMsg(openId, clientVersion, resVersion, req.getPlatId());
                    uid = Long.valueOf(result);
                    LOGGER.error("create role ugc openid={} name={} uid={}", openId, name, uid);
                } else {
                    LOGGER.error("create role ugc openid={} has create", openId);
                }

                PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefDoNotCall(Long.valueOf(uid));
                if (playerRef == null) {
                    LOGGER.error("create role ugc tcaplusPlayer == null openid={} name={} uid={}", openId, name,
                            uid);
                    String format = String.format("player not exist from tcaplus openid %s name %s uid %s", openId,
                            name,
                            uid);
                    throw new RuntimeException(format);
                }

                if (!req.getIgnoreNickname()) {
                    NKErrorCode nkErrorCode = playerRef.gmSetNickname(name);
                    if (nkErrorCode != NKErrorCode.OK) {
                        LOGGER.error("create role ugc gmSetNickname error openid={} name={} uid={} nkErrorCode={}",
                                openId, name,
                                uid, nkErrorCode);
                        String format = String.format(
                                "player gmSetNickname error openid %s name %s uid %s nkErrorCode %s", openId,
                                name,
                                uid, nkErrorCode);
                        throw new RuntimeException(format);
                    }
                }

                ob.put("open_id", openId);
                ob.put("uid", uid);
                ob.put("result", 0);
            } catch (Exception e) {
                LOGGER.error("rpcUgcCreateRole exception openId={} e=", openId, e);
                ob.put("result", 1);
                ob.put("ex", e.getMessage());
            }
        }
        res.setResult(0);
        res.setJsonRet(ret.toString());
        return RpcResult.create(NKErrorCode.OK, res);
    }

    @Override
    public RpcResult<RpcLuckyFriendApplyTaskRes.Builder> rpcLuckyFriendApplyTask(RpcLuckyFriendApplyTaskReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode result = playerRef.handleLuckyFriendApplyTask(req);
            return RpcResult.create(result, RpcLuckyFriendApplyTaskRes.newBuilder());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            return RpcResult.create(notFoundErr.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<RpcLuckyFriendOperateTaskApplyRes.Builder> rpcLuckyFriendOperateTaskApply(
            RpcLuckyFriendOperateTaskApplyReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.handleLuckyFriendOperateTaskApply(req);
            return RpcResult.create(NKErrorCode.OK, RpcLuckyFriendOperateTaskApplyRes.newBuilder());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            return RpcResult.create(notFoundErr.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<RpcLuckyFriendNoticeTaskRes.Builder> rpcLuckyFriendNoticeTask(
            RpcLuckyFriendNoticeTaskReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.handleLuckyFriendNoticeTask(req.getFriendUid());
            return RpcResult.create(NKErrorCode.OK, RpcLuckyFriendNoticeTaskRes.newBuilder());
        } catch (NKRuntimeException re) {
            return RpcResult.create(re.getEnumErrCode());
        } catch (PlayerNotFoundException notFoundErr) {
            return RpcResult.create(notFoundErr.getEnumErrCode());
        } catch (Exception e) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcUgcPlayerPublicAttrModifyNtf(SsGamesvr.RpcUgcPlayerPublicAttrModifyNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcUgcPlayerPublicAttrModifyNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcUgcPlayerPublicAttrModifyNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }


    
    @Override
    public Void rpcUgcPlayerPublicHeartbeatTimeoutNtf(SsGamesvr.RpcUgcPlayerPublicHeartbeatTimeoutNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcUgcPlayerPublicHeartbeatTimeoutNtf(req);
            return null;
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcUgcPlayerPublicHeartbeatTimeoutNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcActivityCommonNtf(RpcActivityCommonNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcActivityCommonNtf(req);
        } catch (Exception e) {
            LOGGER.error("rpcActivityCommonNtf send ntf {} to player {} error ", req.getUid(), req.getMsgType(), e);
        }

        return null;
    }
    
    @Override
    public Void rpcNr3e8CommonNtf(SsGamesvr.RpcNr3e8CommonNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (player == null) {
                return null;
            }
            var ntf = CsRich.Nr3e8CommonNtf.newBuilder().
                    setCmd(req.getCmd()).
                    setBody(req.getBody()).
                    setTraceId(req.getTraceId()).
                    setTimestamp(req.getTimestamp());
            player.nr3e8CommonNtf(ntf);
        } catch (Exception e) {
            LOGGER.error("handle rpcNr3e8CommonNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcFriendsBroadcast(SsGamesvr.RpcFriendsBroadcastReq.Builder req) throws RpcException {
        try {
            Method method = SnsUtil.getMethod(req.getMsgType());
            if (method == null) {
                LOGGER.error("rpcFriendsBroadcast not found method : {}", req.getMsgType());
                return null;
            }

            Message.Builder realReq = RpcMsgTypes.newBuilder(req.getMsgType(), ByteBuffer.wrap(req.getMsgByte().toByteArray()));
            for (long uid : req.getTargetUidList()) {
                try {
                    PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
                    method.invoke(playerRef, realReq);
                } catch (Exception e) {
                    LOGGER.error("rpcFriendsBroadcast player:{} ", uid, e);
                }
            }
        } catch (Exception e) {
            LOGGER.error("rpcFriendsBroadcast newBuilder ", e);
        }
        return null;
    }

    @Override
    public Void rpcHouseEventNtf(SsGamesvr.RpcHouseEventNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.onHouseEvent(req.build());
        } catch (Exception e) {
            LOGGER.error("handle rpcHouseEventNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcBattleCommonBroadcastInfoNtf(SsGamesvr.RpcBattleCommonBroadcastInfoNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            player.handleBattleCommonBroadcastInfoNtf(req);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcRoomBroadcastInfo NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcBattlePartnerCoMatchInfoNtf(SsGamesvr.RpcBattlePartnerCoMatchInfoNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            player.handleBattlePartnerCoMatchInfoNtf(req);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcBattlePartnerCoMatchInfoNtf NKCheckedException uid:{}, e:",
                    req.getPlayerUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcHouseKickNtf(SsGamesvr.RpcHouseKickNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.houseKick(req.getHouseId(), req.getReason(), req.getBuildingId());
        return null;
    }

    @Override
    public Void rpcCookKickNtf(SsGamesvr.RpcCookKickNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.cookKick(req.getCookId(), req.getReason());
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcBindPhoneRes.Builder> rpcBindPhone(SsGamesvr.RpcBindPhoneReq.Builder req)
            throws RpcException, NKTimeoutException {
        RpcBindPhoneRes.Builder ret = RpcBindPhoneRes.newBuilder();
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (playerRef != null) {
                playerRef.bindPhone(req.getType());
                ret.setCode(NKErrorCode.OK.getValue())
                        .setMsg("success");
            } else {
                ret.setCode(NKErrorCode.UserIsOffline.getValue())
                        .setMsg("player is offline");
            }
        } catch (NKRuntimeException re) {
            LOGGER.error("rpcBindPhone req:{}, runtime exception:", req, re);
            ret.setCode(re.getEnumErrCode().getValue())
                    .setMsg(re.getMessage());
        } catch (PlayerNotFoundException e) {
            ret.setCode(NKErrorCode.UserIsOffline.getValue())
                    .setMsg("player is offline");
        } catch (Exception e) {
            LOGGER.error("rpcBindPhone req:{}, ex:", req, e);
            ret.setCode(NKErrorCode.UnknownError.getValue())
                    .setMsg("internal server error");
        }
        return RpcResult.create(NKErrorCode.OK, ret);
    }

    @Override
    public Void rpcMallDemandSuccessNtf(RpcMallDemandSuccessNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid());
            player.rpcMallDemandSuccessNtf(req);
        } catch (Exception e) {
            LOGGER.error("handle rpcHouseEventNtf failed, error: ", e);
        }
        return null;
    }

    @Override
    public Void rpcWolfKillReportCommunicate(SsGamesvr.RpcWolfKillReportCommunicateReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.handleWolfKillReportCommunicate(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcWolfKillReportCommunicate uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcWolfKillBeReportCommunicate(SsGamesvr.RpcWolfKillBeReportCommunicateReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (playerRef != null) {
                playerRef.handleWolfKillBeReportCommunicate(req);
            }
        } catch (Exception e) {
            PlayerWolfKillMgr.dealWithOfflineCommunicateBeReport(req);
        }
        return null;
    }

    @Override
    public Void rpcWolfKillReportPassive(SsGamesvr.RpcWolfKillReportPassiveReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.handleWolfKillReportPassive(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcWolfKillReportPassive uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcWolfKillBeReportPassive(SsGamesvr.RpcWolfKillBeReportPassiveReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (playerRef != null) {
                playerRef.handleWolfKillBeReportPassive(req);
            }
        } catch (Exception e) {
            PlayerWolfKillMgr.dealWithOfflinePassiveBeReport(req);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcTradingCardInteractionRes.Builder> rpcTradingCardInteraction(SsGamesvr.RpcTradingCardInteractionReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getDestUid());
            NKErrorCode errorCode = NKErrorCode.forNumber(playerRef.doTradingCardInteraction(req.getData()));
            return RpcResult.create(errorCode, SsGamesvr.RpcTradingCardInteractionRes.newBuilder());
        } catch (Exception ex) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcTextTssCheckRes.Builder> rpcTextTssCheck(SsGamesvr.RpcTextTssCheckReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            SsGamesvr.RpcTextTssCheckRes.Builder resBuilder = playerRef.rpcTextTssCheck(req);
            return RpcResult.create(NKErrorCode.OK, resBuilder);
        } catch (NKCheckedException ex) {
            return RpcResult.create(ex.getEnumErrCode());
        } catch (PlayerNotFoundException ex) {
            return RpcResult.create(ex.getEnumErrCode());
        } catch (Exception ex) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcArenaGameInfoSyncNtf(SsGamesvr.RpcArenaGameInfoSyncNtfReq.Builder req)
            throws RpcException{
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcArenaGameInfoSyncNtf(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcArenaGameInfoSyncNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }
    
    @Override
    public Void rpcArenaCardEventNtf(SsGamesvr.RpcArenaCardEventNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcArenaCardEventNtf(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcArenaCardEventNtf uid:{} Exception: {}", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcArenaGameInfoLoginSyncFinishNtf(SsGamesvr.RpcArenaGameInfoLoginSyncFinishNtfReq.Builder req)
            throws RpcException{
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.rpcArenaGameInfoLoginSyncFinishNtf(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcArenaGameInfoSyncNtf uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcGameMatchResultRes.Builder> rpcGameMatchResult(SsGamesvr.RpcGameMatchResultReq.Builder req)
            throws RpcException, NKTimeoutException
    {
        NKErrorCode resCode = NKErrorCode.OK;
        SsGamesvr.RpcGameMatchResultRes.Builder resBuilder = SsGamesvr.RpcGameMatchResultRes.newBuilder();
        PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (playerRef != null) {
            try {
                resCode = playerRef.rpcLobbyMatchResult(req);
            } catch (Exception e) {
                LOGGER.error("GsImpl::rpcGameMatchResult uid:{} Exception:", req.getUid(), e);
            }
        } else {
            resCode = NKErrorCode.UserIsOffline;
        }
        return RpcResult.create(resCode, resBuilder);
    }

    @Override
    public RpcResult<SsGamesvr.RpcGameMatchCancelResultRes.Builder> rpcGameMatchCancelResult(
            SsGamesvr.RpcGameMatchCancelResultReq.Builder req) throws RpcException, NKTimeoutException {
        NKErrorCode resCode = NKErrorCode.OK;
        SsGamesvr.RpcGameMatchCancelResultRes.Builder resBuilder = SsGamesvr.RpcGameMatchCancelResultRes.newBuilder();
        PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (playerRef != null) {
            try {
                resCode = playerRef.rpcLobbyMatchCancelResult(req);
            } catch (Exception e) {
                LOGGER.error("GsImpl::rpcGameMatchCancelResult uid:{} Exception:", req.getUid(), e);
            }
        } else {
            resCode = NKErrorCode.UserIsOffline;
        }
        return RpcResult.create(resCode, resBuilder);
    }

    @Override
    public Void rpcGameModifyMatchingTeamInfoResultNtf(RpcGameModifyMatchingTeamInfoResultNtfReq.Builder req) throws RpcException {
        PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null == playerRef) {
            LOGGER.error("playerRef is null, uid:{}",
                    req.getUid());
            return null;
        }

        try {
            playerRef.rpcGameModifyMatchingTeamInfoResultNtf(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcGameMatchCancelResult uid:{} Exception:", req.getUid(), e);
        }

        return null;
    }

    @Override
    public Void cocPushPlayerMsgNonBlocking(SsGamesvr.CocPushPlayerMsgNonBlockingReq.Builder req) throws RpcException {
        SsGamesvr.CocPushPlayerMsgData msgData = req.getMsgData();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).cocPushPlayerMsg(msgData);
        } catch (PlayerNotFoundException fe) {
            // ignore
            LOGGER.debug("cocPushPlayerMsg failed cause of not found player! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType());
        } catch (Exception e) {
            LOGGER.error("cocPushPlayerMsg failed! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType(), e);
        }
        return null;
    }
    @Override
    public RpcResult<SsGamesvr.CocPushPlayerMsgBlockingRes.Builder> cocPushPlayerMsgBlocking(SsGamesvr.CocPushPlayerMsgBlockingReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocPushPlayerMsgData msgData = req.getMsgData();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).cocPushPlayerMsg(msgData);
        } catch (PlayerNotFoundException fe) {
            // ignore
            LOGGER.debug("cocPushPlayerMsg failed cause of not found player! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType());
        } catch (Exception e) {
            int errorCode = NKErrorCode.UnknownError.getValue();
            if (e instanceof IEnumedException) {
                errorCode = ((IEnumedException) e).getEnumErrCode().value;
            }
            throw new NKRuntimeException(errorCode, NKStringFormater.format("coc push msg to player failed! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType()), e);
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    @Override
    public RpcResult<SsGamesvr.CocQueryCommonItemCountRes.Builder> cocQueryCommonItemCount(SsGamesvr.CocQueryCommonItemCountReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocQueryCommonItemCountRes.Builder res = SsGamesvr.CocQueryCommonItemCountRes.newBuilder();
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            for (Integer id : req.getItemIdList()) {
                long itemNum = playerRef.getItemNumByItemId(id);
                res.putItems(id, itemNum);
            }
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }

    @Override
    public RpcResult<SsGamesvr.CocAddCommonItemRes.Builder> cocAddCommonItem(SsGamesvr.CocAddCommonItemReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocAddCommonItemRes.Builder res = SsGamesvr.CocAddCommonItemRes.newBuilder();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).onCocAddCommonItem(req.getItemsMap(), req.getReason());
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }


    @Override
    public RpcResult<SsGamesvr.CocConsumeCommonItemRes.Builder> cocConsumeCommonItem(SsGamesvr.CocConsumeCommonItemReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocConsumeCommonItemRes.Builder res = SsGamesvr.CocConsumeCommonItemRes.newBuilder();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).onCocConsumeCommonItem(req.getItemsMap(), req.getReason());
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }


    @Override
    public Void cocSyncPlayerStatus(SsGamesvr.CocSyncPlayerStatusReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).syncPlayerStatusToCocsvr();
        } catch (PlayerNotFoundException e) {
            // ignore
        } catch (Exception e) {
            LOGGER.error("cocSyncPlayerStatus failed! uid:[{}]", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void cocProsperityValueChangeNotify(SsGamesvr.CocProsperityValueChangeNotifyReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefDoNotCall(req.getUid()).onCocProsperityValueChange(req.getProsperityValue(), req.getProsperityDataVersion());
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return null;
    }

    @Override
    public Void cocCupsScoreChangeNotify(SsGamesvr.CocCupsScoreChangeNotifyReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefDoNotCall(req.getUid()).onCocCupsScoreChange(req.getCupsScore(), req.getScoreDataVersion());
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.CocFriendGiftQueryRes.Builder> cocFriendGiftQuery(SsGamesvr.CocFriendGiftQueryReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocFriendGiftQueryRes.Builder res = SsGamesvr.CocFriendGiftQueryRes.newBuilder();
        try {
            res.setFriendUid(PlayerRefMgr.getInstance().getPlayerRefDoNotCall(req.getUid()).onCocFriendGiftQuery(req));
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return RpcResult.create(NKErrorCode.OK, res);
    }

    @Override
    public RpcResult<SsGamesvr.CocGmClearGameServerInfoRes.Builder> cocGmClearGameServerInfo(SsGamesvr.CocGmClearGameServerInfoReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.CocGmClearGameServerInfoRes.Builder res = SsGamesvr.CocGmClearGameServerInfoRes.newBuilder();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).cocGmClearGameServerInfo(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKRuntimeException e) {
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }


    @Override
    public RpcResult<SsGamesvr.GetPlayerAllFriendUidRes.Builder> getPlayerAllFriendUid(SsGamesvr.GetPlayerAllFriendUidReq.Builder req) throws RpcException, NKTimeoutException {
        LongArray friendUidArray;
        try {
            friendUidArray = PlayerRefMgr.getInstance().getPlayerRefDoNotCall(req.getUid()).getAllFriendUid();
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        SsGamesvr.GetPlayerAllFriendUidRes.Builder result = SsGamesvr.GetPlayerAllFriendUidRes.newBuilder()
                .addAllFriendUid(friendUidArray.getArrayList());
        return RpcResult.create(result);
    }

    @Override
    public RpcResult<RpcUgcCommunityOpReportRes.Builder> rpcUgcCommunityOpReport(RpcUgcCommunityOpReportReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).ugcCommunityOpReport(req);
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return RpcResult.create(NKErrorCode.OK);
    }
    @Override
    public Void cocSyncPlayerAttrSnap2Gs(SsGamesvr.CocSyncPlayerAttrSnap2GsReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).cocSyncPlayerAttrSnap(req.getAttrSnap());
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return null;
    }
    
    @Override
    public RpcResult<RpcNR3E8EventRes.Builder> rpcNR3E8Event(RpcNR3E8EventReq.Builder req) throws RpcException, NKTimeoutException {
        RpcNR3E8EventRes.Builder res = RpcNR3E8EventRes.newBuilder();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).handleNR3E8Event(req);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcNR3E8Event uid:{} Exception:", req.getUid(), e);
            return RpcResult.create(NKErrorCode.UnknownError);
        }
    }

    @Override
    public Void rpcRoomRoundInfoNtf(RpcRoomRoundInfoNtfReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getPlayerUid()).roomRoundInfoNtf(req);
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return null;
    }
    @Override
    public Void rpcAddFriendIntimacy(SsGamesvr.RpcAddFriendIntimacyReq.Builder req) throws RpcException {
        LOGGER.debug("rpcAddFriendIntimacy uid is {}, intimacy is {}", req.getUid(), req.getIntimacy());
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).handleAddFriendIntimacy(req);
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcAddFriendIntimacy uid:{} Exception:", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void ntfPlayerSupportSuccess(SsGamesvr.NtfPlayerSupportSuccessReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).ntfPlayerSupportSuccess(req);
        } catch (NKCheckedException e) {
            throw e.toNKRuntimeException();
        } catch (PlayerNotFoundException e) {
            throw e.toNKRuntimeException();
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcAiNpcAddRoundRes.Builder> rpcAiNpcAddRound(SsGamesvr.RpcAiNpcAddRoundReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            int round = playerRef.rpcAiNpcAddRound(req);
            SsGamesvr.RpcAiNpcAddRoundRes.Builder res = SsGamesvr.RpcAiNpcAddRoundRes.newBuilder();
            res.setRoleId(req.getRoleId()).setRound(round);
            return RpcResult.create(res);
        } catch (NKCheckedException e) {
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            return RpcResult.create(e.getEnumErrCode());
        } catch (Exception e) {
            LOGGER.error("GsImpl::rpcAiNpcAddRound uid:{} Exception:", req.getUid(), e);
            return RpcResult.create(NKErrorCode.AinpcsvrAddRoundError);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcSendMailProcessRes.Builder> rpcUgcSendMailProcess(SsGamesvr.RpcUgcSendMailProcessReq.Builder req) {

        // 请求参数判断
        if (req.getUid() == 0L || req.getTemplateId() == 0 || req.getReason() == 0) {
            return RpcResult.create(NKErrorCode.InvalidParams);
        }
        if (MailInteraction.TlogSendReason.values().length <= req.getReason()) {
            return RpcResult.create(NKErrorCode.InvalidParams);
        }

        try {
            MailAttachmentList.Builder attList = MailAttachmentList.newBuilder();
            for (ItemInfo itemInfo : req.getAttatchmentListList()) {
                MailAttachment.Builder attachment = MailAttachment.newBuilder().setItemIfo(itemInfo);
                attList.addList(attachment);
            }

            // 邮件发送
            long mailId = MailInteraction.sendTemplateMail(req.getUid(), req.getTemplateId(), attList, 0,
                    null, MailInteraction.TlogSendReason.values()[req.getReason()], req.getTitleArgsList(),
                    req.getContentArgsList());
            if (mailId < 0) {
                return RpcResult.create(NKErrorCode.MailOperationError);
            }
        } catch (Exception ex) {
            return RpcResult.create(NKErrorCode.UnknownError);
        }
        return RpcResult.create(NKErrorCode.OK);
    }
    @Override
    public Void arenaPushMsgNonBlocking(SsGamesvr.ArenaPushMsgNonBlockingReq.Builder req) throws RpcException {
        SsGamesvr.ArenaPushPlayerMsgData msgData = req.getMsgData();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).arenaPushPlayerMsg(msgData);
        } catch (PlayerNotFoundException fe) {
            // ignore
            LOGGER.debug("arenaPushPlayerMsg failed cause of not found player! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType());
        } catch (Exception e) {
            LOGGER.error("arenaPushPlayerMsg failed! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType(), e);
        }
        return null;
    }
    @Override
    public RpcResult<SsGamesvr.ArenaPushMsgBlockingRes.Builder> arenaPushMsgBlocking(SsGamesvr.ArenaPushMsgBlockingReq.Builder req) throws RpcException, NKTimeoutException {
        SsGamesvr.ArenaPushPlayerMsgData msgData = req.getMsgData();
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).arenaPushPlayerMsg(msgData);
        } catch (PlayerNotFoundException fe) {
            // ignore
            LOGGER.debug("arenaPushPlayerMsg failed cause of not found player! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType());
        } catch (Exception e) {
            int errorCode = NKErrorCode.UnknownError.getValue();
            if (e instanceof IEnumedException) {
                errorCode = ((IEnumedException) e).getEnumErrCode().value;
            }
            throw new NKRuntimeException(errorCode, NKStringFormater.format("arena push msg to player failed! uid:[{}] msgType:[{}]", req.getUid(), msgData.getMsgType()), e);
        }
        return RpcResult.create(NKErrorCode.OK);
    }

    /**
     * 检测是否可以加载缓存数据 离线在线均使用
     *
     * @param req
     * @return
     * @throws RpcException
     * @throws NKTimeoutException
     */
    @Override
    public RpcResult<SsGamesvr.GetLoadCacheDataAbilityRes.Builder> getLoadCacheDataAbility(GetLoadCacheDataAbilityReq.Builder req) throws RpcException, NKTimeoutException {
        // 请求参数判断
        if (req == null || req.getOpenId().isEmpty()) {
            return RpcResult.create(NKErrorCode.InvalidParams);
        }

        try {
            SsGamesvr.GetLoadCacheDataAbilityRes.Builder res = SsGamesvr.GetLoadCacheDataAbilityRes.newBuilder();
            int canLoad = TransferPlatUtil.checkCanLoadCacheData(req.getOpenId());
            if (canLoad != NKErrorCode.OK.getValue()){
                LOGGER.warn("getLoadCacheDataAbility canLoad false");
            }
            res.setCanLoad(canLoad);
            return RpcResult.create(res);
        }catch (Exception ex) {
           LOGGER.error("getLoadCacheDataAbility check catch exception", ex);
           TimiEnum errorCode = (ex instanceof IEnumedException) ? ((IEnumedException) ex).getEnumErrCode()
                    : NKErrorCode.UnknownError;
           throw new NKRuntimeException(errorCode, NKStringFormater.format("getLoadCacheDataAbility failed! openId:[{}]", req.getOpenId(), ex));
        }
    }

    @Override
    public RpcResult<RpcBatchAccountTransferTaskRes.Builder> rpcBatchAccountTransferTask(RpcBatchAccountTransferTaskReq.Builder req) throws RpcException, NKTimeoutException {
        // 请求参数判断
        if (req == null || req.getUid() == 0L) {
            return RpcResult.create(NKErrorCode.InvalidParams);
        }

        try {
            // 开启新的协程处理账号注销任务处理
//            CurrentExecutorUtil.runJob(() -> {
//                AccountTransferTaskMgr.getInstance().accountTransferTaskBatchProcess();
//                return null;
//            }, "accountTransferTaskBatchProcess", false);
//            LOGGER.debug("DoReleaseTransferLockReq trigger account transfer task process logic success");
        } catch (Exception ex) {
            TimiEnum errorCode = (ex instanceof IEnumedException) ? ((IEnumedException) ex).getEnumErrCode()
                    : NKErrorCode.UnknownError;
            LOGGER.error("DoReleaseTransferLockReq trigger account transfer task process logic failed, code:{}", errorCode);
        }

        return RpcResult.create(NKErrorCode.OK);
    }

    public Void rpcBatchUgcCoCreateEditor(SsGamesvr.RpcBatchUgcCoCreateEditorReq.Builder req) throws RpcException {

        if (req.getUidListCount() == 0 || req.getUgcId() == 0L) {
            return null;
        }

        GameService service = GameService.get();
        if (service == null) {
            return null;
        }

        // 遍历uid列表, 挨个给每个用户发送地图共创编辑请求
        for (long uid : req.getUidListList()) {
            SsGamesvr.RpcUgcCoCreateEditorReq.Builder reqBuilder = SsGamesvr.RpcUgcCoCreateEditorReq.newBuilder();
            reqBuilder.setUid(uid);
            reqBuilder.setEditorUid(req.getEditorUid());
            reqBuilder.setUgcId(req.getUgcId());
            reqBuilder.setLayerId(req.getLayerId());
            reqBuilder.setNickName(req.getNickName());

            try {
                service.rpcUgcCoCreateEditor(reqBuilder);
            } catch (RpcException e) {
                LOGGER.error("rpcUgcCoCreateEditor catch exception, uid:{}, ugcId:{}, ", uid, req.getUgcId());
            }
        }

        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcAllocIdRes.Builder> rpcAllocId(SsGamesvr.RpcAllocIdReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            int allocType = req.getAllocType();
            RpcAllocIdRes.Builder res = RpcAllocIdRes.newBuilder();
            if (allocType==GuidType.GUID_TYPE_CUSTOM_ROOM_ID.getValue()){
                long allocId = CustomRoomIdGenerator.getInstance().allocGuid();
                res.setAllocId(allocId);
                return RpcResult.create(res);
            }

            GuidType guidType =  GuidType.forNumber(allocType);
            BaseGenerator baseGenerator = new BaseGenerator(guidType, Framework.getInstance().getWorldId(), 1000, 100000);
            long allocId = baseGenerator.allocGuid();
            res.setAllocId(allocId);
            return RpcResult.create(res);

        } catch (Exception ex) {
            LOGGER.error("RpcAllocId, error", ex);
        }

        return RpcResult.create(NKErrorCode.UnknownError);
    }
    
    public Void rpcTeamQuickJoinConfirmDoneNtf(SsGamesvr.RpcTeamQuickJoinConfirmDoneNtfReq.Builder req)
            throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            Set<Long> uidList = new HashSet<>(req.getMemberListList());
            player.teamQuickJoinConfirmDoneNtf(uidList, req.getQuickJoinInfo(), req.getUniqueId());
        } catch (Exception e) {
            LOGGER.error("ntf team quick join config ntf, e", e);
        }
        return null;
    }

    public Void rpcMobaFootballGoalBroadcastNtf(SsGamesvr.RpcMobaFootballGoalBroadcastNtfReq.Builder req) throws RpcException {
        try {
            PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).mobaFootballGoalBroadcastNtf(req);
        } catch (Exception e) {
            LOGGER.error("rpcMobaFootballGoalBroadcastNtf failed, player uid:{}", req.getUid(), e);
        }
        return null;
    }

    @Override
    public Void rpcStarPDsInfoNtf(RpcStarPDsInfoNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPDsInfoNtf(req);
        }
        return null;
    }

    public RpcResult<SsGamesvr.RpcArenaGetBattleRecordsRes.Builder> rpcArenaGetBattleRecords(SsGamesvr.RpcArenaGetBattleRecordsReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            return RpcResult.create(NKErrorCode.OK, PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid()).arenaGetBattleRecords(req));
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcArenaGetBattleRecords failed cause of not found player! uid({})", req.getUid(), e);
            return RpcResult.create(NKErrorCode.InvalidParams, SsGamesvr.RpcArenaGetBattleRecordsRes.newBuilder());
        } catch (NKTimeoutException e) {
            LOGGER.error("rpcArenaGetBattleRecords failed cause of timeout! uid({}), req({})", req.getUid(), req, e);
            return RpcResult.create(NKErrorCode.Timeout, SsGamesvr.RpcArenaGetBattleRecordsRes.newBuilder());
        } catch (Exception e) {
            LOGGER.error("rpcArenaGetBattleRecords failed cause of unknownException! uid({}), req({})", req.getUid(), req, e);
            return RpcResult.create(NKErrorCode.UnknownError, SsGamesvr.RpcArenaGetBattleRecordsRes.newBuilder());
        }
    }

    @Override
    public Void rpcAddplayerResultNtf(RpcAddplayerResultNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPAddPlayerResultNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPPlayerNtf(RpcStarPPlayerNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.starPPlayerNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPApplyNtf(RpcStarPApplyNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getApplyUid());
        if (player != null) {
            player.starPApplyNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPApplyResultNtf(RpcStarPApplyResultNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getApplyUid());
        if (player != null) {
            player.starPApplyResultNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPPrepareMigrateDsNtf(RpcStarPPrepareMigrateDsNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getToUid());
        if (player != null) {
            player.ntfPrepareMigrateDsInfo(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPPvpStartFailNtf(SsGamesvr.RpcStarPPvpStartFailNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.ntfStarPPvpStartFail(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPAttrNtf(RpcStarPAttrNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getToUid());
        if (player != null) {
            player.starPAttrNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPGroupAttrNtf(RpcStarPGroupAttrNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getToUid());
        if (player != null) {
            player.starPGroupAttrNtf(req);
        }
        return null;
    }

    @Override
    public Void rpcStarPInviteNtf(RpcStarPInviteNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getInviteeUid());
        if (player != null) {
            player.starPInviteNtf(req);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPLotteryReviseRewardRes.Builder> rpcStarPLotteryReviseReward(SsGamesvr.RpcStarPLotteryReviseRewardReq.Builder req) throws RpcException, NKTimeoutException {
        var res = SsGamesvr.RpcStarPLotteryReviseRewardRes.newBuilder();
        long uid = req.getUid();
        var playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
        if (playerRef == null) {
            var errorCode = NKErrorCode.StarPLotteryPlayerNotExist;
            res.setErrorCode(errorCode.getValue());
            return RpcResult.create(errorCode, res);
        }
        var reviseRewardRes = playerRef.handleStarPLotteryReviseReward(req.getItemsList(),req.getBillNo());
        res.setErrorCode(reviseRewardRes.getValue());
        return RpcResult.create(reviseRewardRes, res);
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPLotterySendRewardRes.Builder> rpcStarPLotterySendReward(SsGamesvr.RpcStarPLotterySendRewardReq.Builder req) throws RpcException, NKTimeoutException {
        var res = SsGamesvr.RpcStarPLotterySendRewardRes.newBuilder();
        long uid = req.getUid();
        var playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
        if (playerRef == null) {
            var errorCode = NKErrorCode.StarPLotteryPlayerNotExist;
            res.setErrorCode(errorCode.getValue());
            return RpcResult.create(errorCode, res);
        }
        var sendRewardRes = playerRef.handleStarPLotterySendReward(req.getItemsList(),req.getBillNo());
        res.setErrorCode(sendRewardRes.getValue());
        return RpcResult.create(sendRewardRes, res);
    }

    @Override
    public Void rpcStarPAblerToApplyAdminNtf(SsGamesvr.RpcStarPAblerToApplyAdminNtfReq.Builder req)
            throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getToUid());
        if (player != null) {
            player.starPApplyAdminNtf(req);
        }
        return null;
    }

    public RpcResult<SsGamesvr.RpcStarPSendCardChatMsgRes.Builder> rpcStarPSendCardChatMsg(SsGamesvr.RpcStarPSendCardChatMsgReq.Builder req)
            throws RpcException, NKTimeoutException {
        var res = SsGamesvr.RpcStarPSendCardChatMsgRes.newBuilder();
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            var msgData = ChatMsgData.newBuilder()
                    .setFromId(req.getUid())
                    .setMsgType(ChatMsgType.CMT_StarPCard)
                    .setStarPCardInfo(req.getCardInfo());
            player.sendMsg(req.getGroup(), msgData.build(), DateUtils.currentTimeMillis(), false, true);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException e) {
            LOGGER.error("rpcStarPSendCardChatMsg failed, uid({}) exception({})", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode(), res);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcStarPSendCardChatMsg failed, uid({}) exception({})", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }

    @Override
    public Void rpcStarPTipsNtf(SsGamesvr.RpcStarPTipsNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            playerRef.sendStarPTipsNtf(req);
        } catch (NKCheckedException e) {
            LOGGER.error("rpcStarPTipsNtf error, uid({}) code({}) err({})",
                    req.getUid(), req.getCode(), e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcStarPTipsNtf error, uid({}) code({}) err({})",
                    req.getUid(), req.getCode(), e.getEnumErrCode());
        }
        return null;
    }

    @Override
    public Void rpcStarPUpdateUserInfo(RpcStarPUpdateUserInfoReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.starPUpdateUserInfo(req);
        }

        // 不依赖player在线
        if (req.hasPublicInfo()) {
            if (player != null) {
                player.updateStarPPublic(req.getPublicInfo());
            } else {
                PlayerStarPMgr.updateStarPPublic(null, req.getUid(), req.getPublicInfo());
            }
        }

        if (req.hasPublicInfo2()) {
            if (player != null) {
                player.updateStarPPublic2(req.getPublicInfo2());
            } else {
                PlayerStarPMgr.updateStarPPublic2(null, req.getUid(), req.getPublicInfo2());
            }
        }

        if (req.hasStarPlayerInfo()) {
            if (player != null) {
                player.updateStarPlayerInfo(req.getStarPlayerInfo());
            } else {
                PlayerStarPMgr.updateStarPlayerInfo(null, req.getUid(), req.getStarPlayerInfo());
            }
        }
        return null;
    }




    // @Override
    // public  Void rpcRoomConfirmEnterNtf(RpcRoomConfirmEnterNtfReq.Builder req)
    //         throws RpcException, NKTimeoutException {
        // try {
        //     PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        //     player.roomConfirmEnterNtf(req.getRoomId(), req.getUniqueId(), req.getMatchMemberList(),
        //             req.getResultType());
        // } catch (NKCheckedException e) {
        //     LOGGER.error("GsImpl::rpcRoomConfirmEnterNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
        //             req.getUid(), req.getRoomId(), req.getUniqueId(), e);
        //     return RpcResult.create(e.getEnumErrCode());
        // } catch (PlayerNotFoundException e) {
        //     LOGGER.error("GsImpl::rpcRoomConfirmEnterNtf NKCheckedException uid:{}, roomId:{} uniqueId:{} e:{}",
        //             req.getUid(), req.getRoomId(), req.getUniqueId(), e);
        //     return RpcResult.create(e.getEnumErrCode());
        // }
    //     return null;
    // }



    // public  Void rpcRoomConfirmEnterProgressNtf(
    //         RpcRoomConfirmEnterProgressNtfReq.Builder req)
    //         throws RpcException, NKTimeoutException {
        // try {
        //     PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        //     player.roomConfirmEnterProgressNtf(req);
        //     player.starPOnCancelConfirmEnterResult(req);
        // } catch (NKCheckedException e) {
        //     LOGGER.error("GsImpl::rpcRoomConfirmEnterProgressNtf ", e);
        //     return RpcResult.create(e.getEnumErrCode());
        // } catch (PlayerNotFoundException e) {
        //     LOGGER.error("GsImpl::rpcRoomConfirmEnterProgressNtf ", e);
        //     return RpcResult.create(e.getEnumErrCode());
        // }
    //     return null;
    // }



    @Override
    public Void rpcStarPEnterGameNtf(RpcStarPEnterGameNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPEnterGameNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.StarPEnterGameNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcStarPEnterGameNtf NKCheckedException uid:{}, room_id:{}, starPId:{}, e:{}", req.getUid(),
                    nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcStarPEnterGameNtf notFoundErr uid:{}, room_id:{}, starPId:{}, e:{}", req.getUid(),
                    notFoundErr);
        }
        return null;
    }


    @Override
    public Void rpcReportPlayerDsOfflineNtf(SsCommon.RpcReportPlayerDsOfflineNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.playerExitStarPWorldIdNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcStarPEnterGameNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcStarPEnterGameNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }



    @Override
    public Void rpcStarPChatChannelNtf(SsGamesvr.RpcStarPChatChannelNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPChatChannelNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.starPChatChannelNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcStarPChatChannelNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcStarPChatChannelNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcStarPRoleChatInfoNtf(SsGamesvr.RpcStarPRoleChatInfoNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPRoleChatInfoNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.starPRoleChatInfoNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcStarPRoleChatInfoNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcStarPRoleChatInfoNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcStarPChatGroupInfoNtf(SsGamesvr.RpcStarPChatGroupInfoNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPChatGroupInfoNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.starPChatGroupInfoNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcStarPChatGroupInfoNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcStarPChatGroupInfoNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcChatCurInfoNtf(SsGamesvr.RpcChatCurInfoNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcChatCurInfoNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.starChatCurInfoNtfNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcChatCurInfoNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcChatCurInfoNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPCheckInformationLawfulRes.Builder> rpcStarPCheckInformationLawful(SsGamesvr.RpcStarPCheckInformationLawfulReq.Builder req)
            throws RpcException, NKTimeoutException {
        var res = SsGamesvr.RpcStarPCheckInformationLawfulRes.newBuilder();
        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            for (int i = 0; i < req.getInformationCount(); ++i) {
                var info = req.getInformation(i);
                res.addResult(playerRef.checkInformationLawful(info.getContent(), info.getScene()));
            }
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException e) {
            LOGGER.error("rpcStarPCheckInformationLawful failed, uid({}) exception({})", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode(), res);
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcStarPCheckInformationLawful failed, uid({}) exception({})", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode(), res);
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPDSGetPlayerPublicInfoRes.Builder> rpcStarPDSGetPlayerPublicInfo(SsGamesvr.RpcStarPDSGetPlayerPublicInfoReq.Builder req)
            throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPDSGetPlayerPublicInfo req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        List<Long> reqList = List.of(req.getUid());

        var rspMsg = SsGamesvr.RpcStarPDSGetPlayerPublicInfoRes.newBuilder();
        Map<Long, PlayerPublicInfo.Builder> playerPublicInfoList = SimpleDataManager.batchGetPlayerPublic(null,
                reqList, req.getFieldsList(), true, SimpleDataManager.PlayerPublicSourceStarPDSGetPlayerState);
        playerPublicInfoList.forEach((uid, data) -> {
            rspMsg.setPlayerPublicInfo(data.build());
        });

        return RpcResult.create(rspMsg);
    }

    @Override
    public Void rpcTouchedStarPModeNtf(SsGamesvr.RpcTouchedStarPModeNtfReq.Builder req) throws RpcException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcTouchedStarPModeNtf req : {}", Pb2JsonUtil.getPbMsg(req));
        }
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            player.touchedStarPModeNtf(req);
        } catch (NKCheckedException nkErr) {
            LOGGER.error("rpcTouchedStarPModeNtf NKCheckedException uid:{}, e:{}", req.getUid(), nkErr);
        } catch (PlayerNotFoundException notFoundErr) {
            LOGGER.error("rpcTouchedStarPModeNtf notFoundErr uid:{},  e:{}", req.getUid(), notFoundErr);
        }
        return null;
    }

    @Override
    public Void rpcStarPExitDsNtf(SsGamesvr.RpcStarPExitDsNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getExitUserUid());
        if(player != null){
            player.starPExitDsNtf(req);
        }
        return null;
    }
    @Override
    public Void rpcStarPEnterDsNtf(SsGamesvr.RpcStarPEnterDsNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPDsEnterNtf(req);
        } else {
            LOGGER.debug("rpcStarPEnterDsNtf error! player not found! spId:{}", req.getStarPId());
        }
        return null;
    }

    public RpcResult<SsGamesvr.RpcStarPVerifyGuildNameRes.Builder> rpcStarPVerifyGuildName(SsGamesvr.RpcStarPVerifyGuildNameReq.Builder req)
            throws RpcException, NKTimeoutException
    {
        SsGamesvr.RpcStarPVerifyGuildNameRes.Builder retBuilder = SsGamesvr.RpcStarPVerifyGuildNameRes.newBuilder();
        long uid = req.getUid();
        long starPId = req.getStarPId();
        long guildId = req.getGuildId();
        String guildName = req.getGuildName();
        Boolean onlyVerify = req.getOnlyVerify();
        Boolean isAutoCreate = req.getIsAutoCreate();

        LOGGER.info("Ds irpcStarPDsVerifyGuildName starPId:{} uid:{}, guildId:{} guildName:{} onlyVerify:{} isAutoCreate:{}",
                starPId, uid, guildId, guildName, onlyVerify, isAutoCreate);

        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            if (!isAutoCreate) {
                NKErrorCode result = playerRef.VerifyGuildName(starPId, guildId, guildName, onlyVerify);
                return RpcResult.create(retBuilder.setResult(result.getValue()));
            }
            else {
                var result = playerRef.GetSuffixIdForAutoCreateGuild(guildName);
                return RpcResult.create(retBuilder.setResult(result.getKey().getValue()).setSuffixId(result.getValue()));
            }
        } catch (NKCheckedException nkErr) {
            LOGGER.error("Ds rpcStarPVerifyGuildName NKCheckedException uid:{}, e:{}", uid, nkErr);
            return RpcResult.create(retBuilder.setResult(nkErr.getErrCode()));
        }
    }

    public RpcResult<SsGamesvr.RpcStarPDsReleaseGuildNameRes.Builder> rpcStarPDsReleaseGuildName(SsGamesvr.RpcStarPDsReleaseGuildNameReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcStarPDsReleaseGuildNameRes.Builder retBuilder = SsGamesvr.RpcStarPDsReleaseGuildNameRes.newBuilder();
        long uid = req.getUid();
        long starPId = req.getStarPId();
        long guildId = req.getGuildId();
        String guildName = req.getGuildName();

        try {
            PlayerRef playerRef = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            NKErrorCode result = playerRef.ReleaseGuildName(starPId, guildId, guildName);
            return RpcResult.create(retBuilder.setResult(result.getValue()));
        } catch (NKCheckedException nkErr) {
            LOGGER.error("Ds rpcStarPDsReleaseGuildName NKCheckedException uid:{}, e:{}", uid, nkErr);
            return RpcResult.create(retBuilder.setResult(nkErr.getErrCode()));
        }
    }

    @Override
    public Void rpcStarPAssistOrderStatusNtf(SsGamesvr.RpcStarPAssistOrderStatusNtfReq.Builder req) throws RpcException{
        long uid = req.getUid();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPAssistOrderStatusNtf(req);
        } else {
            LOGGER.debug("rpcStarPAssistOrderStatusNtf error! player not found! uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcStarPAssistOrderChangeNtf(SsGamesvr.RpcStarPAssistOrderChangeNtfReq.Builder req) throws RpcException{
        long uid = req.getUid();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPAssistOrderChangeNtf(req);
        } else {
            LOGGER.debug("rpcStarPAssistOrderChangeNtf error! player not found! uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public Void rpcStarPFriendIntimacyChangeNtf(SsGamesvr.RpcStarPFriendIntimacyChangeNtfReq.Builder req) throws RpcException{
        long uid = req.getUid();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (null != player) {
            player.starPFriendIntimacyChangeNtf(req);
        } else {
            LOGGER.debug("rpcStarPFriendIntimacyChangeNtf error! player not found! uid:{}", req.getUid());
        }
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPGetFriendInfoRes.Builder> rpcStarPGetFriendInfo(SsGamesvr.RpcStarPGetFriendInfoReq.Builder req)
            throws RpcException, NKTimeoutException {
        SsGamesvr.RpcStarPGetFriendInfoRes.Builder resBuilder = SsGamesvr.RpcStarPGetFriendInfoRes.newBuilder();
        long uid = req.getUid();

        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
        if (player == null) {
            LOGGER.debug("rpcStarPFriendIntimacyChangeNtf error! player not found! uid:{}", req.getUid());
            return RpcResult.create(resBuilder.setResult(NKErrorCode.StarPFriendIntimacyPlayerNotExist.getValue()));
        }
        NKErrorCode result = player.getAllFriendInfo(resBuilder);
        return RpcResult.create(resBuilder.setResult(result.getValue()));
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPGetChatInfoRes.Builder> rpcStarPGetChatInfo(
                SsGamesvr.RpcStarPGetChatInfoReq.Builder req) throws RpcException, NKTimeoutException {

        var resBuilder = RpcStarPGetChatInfoRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player == null) {
            LOGGER.debug("rpcStarPGetChatInfo error! player not found! uid:{}", req.getUid());
            return RpcResult.create(resBuilder.setResult(NKErrorCode.StarPFriendIntimacyPlayerNotExist.getValue()));
        }
        var chatInfo = player.getStarPChatInfo();
        resBuilder.setChatInfo(chatInfo);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("rpcStarPGetChatInfo uid:{} chatInfo:{}", req.getUid(), chatInfo);
        }

        return RpcResult.create(resBuilder);
    }

    @Override
    public RpcResult<SsGamesvr.RpcStarPSocInteractionRatioGetInfoRes.Builder> rpcStarPSocInteractionRatioGetInfo(
            SsGamesvr.RpcStarPSocInteractionRatioGetInfoReq.Builder req) throws RpcException, NKTimeoutException {
        var rspMsg = SsGamesvr.RpcStarPSocInteractionRatioGetInfoRes.newBuilder();
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if(null == player) {
            LOGGER.debug("rpcStarPSocInteractionRatioGetInfo error! player not found! uid:{}", req.getUid());
            return RpcResult.create(rspMsg.setResult(0));
        }

        //
        NKErrorCode errorCode = player.getStarPSocInteractionRatioGetInfo(rspMsg);
        return RpcResult.create(rspMsg.setResult(errorCode.getValue()));

    }
    
    public RpcResult<RpcStarPJoinGuildNtfRes.Builder> rpcStarPJoinGuildNtf(RpcStarPJoinGuildNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        if (player != null) {
            player.starPJoinGuildNtf(req);
        }
        RpcStarPJoinGuildNtfRes.Builder res = RpcStarPJoinGuildNtfRes.newBuilder();
        return RpcResult.create(NKErrorCode.OK, res);
    }


    /**
     * @param req
     * @throws NKTimeoutException
     */
    @Override
    public Void rpcObserverDsInfoNtf(RpcObserverDsInfoNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.handleObserveDsInfo(req.getDsAddr(), req.getDesModInfo(), req.getMatchType(), req.getBattleId());
        return null;
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcCoCreateMultiEditApplyRes.Builder> rpcUgcCoCreateMultiEditApply(
            SsGamesvr.RpcUgcCoCreateMultiEditApplyReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreateMultiEditApplyNtf(req.build());
            RpcUgcCoCreateMultiEditApplyRes.Builder res = RpcUgcCoCreateMultiEditApplyRes.newBuilder()
                    .setResult(resCode.getValue());
            return RpcResult.create(resCode, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::rpcUgcCoCreateMultiEditApply NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::rpcUgcCoCreateMultiEditApply PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcCoCreateMultiEditReplyRes.Builder> rpcUgcCoCreateMultiEditReply(
            SsGamesvr.RpcUgcCoCreateMultiEditReplyReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreateMultiEditReplyNtf(req.build());
            RpcUgcCoCreateMultiEditReplyRes.Builder res = RpcUgcCoCreateMultiEditReplyRes.newBuilder()
                    .setResult(resCode.getValue());
            return RpcResult.create(resCode, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditReply NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditReply PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcCoCreateMultiEditRejectEnterRes.Builder> rpcUgcCoCreateMultiEditRejectEnter(
            SsGamesvr.RpcUgcCoCreateMultiEditRejectEnterReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreateMultiEditRejectEnterNtf(req.build());
            RpcUgcCoCreateMultiEditRejectEnterRes.Builder res = RpcUgcCoCreateMultiEditRejectEnterRes.newBuilder()
                    .setResult(resCode.getValue());
            return RpcResult.create(resCode, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditRejectEnter NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditRejectEnter PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcUgcCoCreateMultiEditDataUpdateRes.Builder> rpcUgcCoCreateMultiEditDataUpdate(
            SsGamesvr.RpcUgcCoCreateMultiEditDataUpdateReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleCoCreateMultiEditDataUpdateNtf(req.build());
            RpcUgcCoCreateMultiEditDataUpdateRes.Builder res = RpcUgcCoCreateMultiEditDataUpdateRes.newBuilder()
                    .setResult(resCode.getValue());
            return RpcResult.create(resCode, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditDataUpdate NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleCoCreateMultiEditDataUpdate PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<SsGamesvr.RpcAllocUniqueIdRes.Builder> rpcAllocUniqueId(SsGamesvr.RpcAllocUniqueIdReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            RpcAllocUniqueIdRes.Builder res = RpcAllocUniqueIdRes.newBuilder();
            if (AllocUniqueIdType.forNumber(req.getType()) == null) {
                LOGGER.error("GsImpl::rpcAllocUniqueId InvalidParams type:{}", req.getType());
                return RpcResult.create(NKErrorCode.InvalidParams);
            }

            if (req.getCount() <= 0) {
                LOGGER.error("GsImpl::rpcAllocUniqueId InvalidParams count:{}", req.getCount());
                return RpcResult.create(NKErrorCode.InvalidParams);
            }

            if (req.getType() == AllocUniqueIdType.ALLOC_UNIQUE_ID_TYPE_UGC_ID_VALUE) {
                IntStream.range(0, req.getCount()).forEach(index ->
                    res.addIds(UgcGenerator.getInstance().allocGuid())
                );
            }

            res.setResult(NKErrorCode.OK.getValue());
            return RpcResult.create(res);
        } catch (Exception ex) {
            LOGGER.error("GsImpl::rpcAllocUniqueId Exception: ", ex);
        }
        return RpcResult.create(NKErrorCode.UnknownError);
    }

    public Void rpcBattleSceneSwitchNtf(SsGamesvr.RpcBattleSceneSwitchNtfReq.Builder req) throws RpcException {
        PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
        player.battleSceneSwitchNtf(req.getBattleId(), req.getSceneId(), req.getTargetSceneId());
        return null;
    }

    @Override
    public RpcResult<RpcMidasBuyRes.Builder> rpcMidasBuy(RpcMidasBuyReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            NKErrorCode resCode = player.handleRpcMidasBuy(req);
            RpcMidasBuyRes.Builder res = RpcMidasBuyRes.newBuilder().setResult(resCode.getValue());
            return RpcResult.create(resCode, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleRpcMidasBuy NKCheckedException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleRpcMidasBuy PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcWolfKillCheckWarmBattleRes.Builder> rpcWolfKillCheckWarmBattle(RpcWolfKillCheckWarmBattleReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            long uid = req.getUid();
            int matchId = req.getMatchId();
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            RpcWolfKillCheckWarmBattleRes.Builder res = player.handleRpcWolfKillCheckWarmBattle(req);
            LOGGER.debug("GsImpl::rpcWolfKillCheckWarmBattle role is {}, uid is {}, matchId is {}", res.getRole(), uid, matchId);
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleRpcWolfKillCheckWarmBattle NKCheckedException uid:{}, e:", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleRpcWolfKillCheckWarmBattle PlayerNotFoundException uid:{}, e:", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public RpcResult<RpcWolfKillAddComeBackWarmBattleRes.Builder> rpcWolfKillAddComeBackWarmBattle(RpcWolfKillAddComeBackWarmBattleReq.Builder req)
            throws RpcException, NKTimeoutException {
        try {
            long uid = req.getUid();
            LOGGER.debug("GsImpl::rpcWolfKillAddComeBackWarmBattle uid is {}", uid);
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            player.handleRpcWolfKillAddComeBackWarmBattle();
            RpcWolfKillAddComeBackWarmBattleRes.Builder res = RpcWolfKillAddComeBackWarmBattleRes.newBuilder();
            return RpcResult.create(NKErrorCode.OK, res);
        } catch (NKCheckedException e) {
            LOGGER.error("GsImpl::handleRpcWolfKillAddComeBackWarmBattle NKCheckedException uid:{}, e:", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("GsImpl::handleRpcWolfKillAddComeBackWarmBattle PlayerNotFoundException uid:{}, e:", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        }
    }

    @Override
    public Void rpcRoomStartMatchErrorDetailNtf(RpcRoomStartMatchErrorDetailNtfReq.Builder req) throws RpcException {
        try {
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(req.getUid());
            if (null == player) {
                // offline了
                LOGGER.warn("rpcRoomStartMatchErrorDetailNtf PlayerNotFound uid:{}",
                        req.getUid());
            } else {
                player.handleRoomStartMatchErrorDetailNtf(req);
            }
        } catch (NKCheckedException e) {
            LOGGER.error("rpcRoomStartMatchErrorDetailNtf NKCheckedException uid:{}, e:",
                    req.getUid(), e);
        } catch (PlayerNotFoundException e) {
            LOGGER.warn("rpcRoomStartMatchErrorDetailNtf PlayerNotFoundException uid:{}, e:",
                    req.getUid(), e);
        } catch (Exception e) {
            LOGGER.error("rpcRoomStartMatchErrorDetailNtf Exception uid:{}, e:",
                    req.getUid(), e);
        }

        return null;
    }

    /**
     * chest玩法查询玩家能否满足进入某个地图的消耗
     * @param req chest玩法查询玩家能否满足进入某个地图的消耗请求
     * @return chest玩法查询玩家能否满足进入某个地图的消耗响应
     * @throws RpcException
     * @throws NKTimeoutException
     */
    @Override
    public RpcResult<RpcChestCheckMapEntryCostRes.Builder> rpcChestCheckMapEntryCost(RpcChestCheckMapEntryCostReq.Builder req) throws RpcException, NKTimeoutException {
        try {
            long uid = req.getUid();
            PlayerRef player = PlayerRefMgr.getInstance().getPlayerRefIfPresent(uid);
            NKErrorCode errorCode = player.handleChestCheckMapEntryCost(req);
            return RpcResult.create(errorCode, RpcChestCheckMapEntryCostRes.newBuilder());
        } catch (NKCheckedException e) {
            LOGGER.error("rpcChestCheckMapEntryCost uid:{} catch NKCheckedException e:", req.getUid(), e);
            return RpcResult.create(e.getEnumErrCode());
        } catch (PlayerNotFoundException e) {
            LOGGER.error("rpcChestCheckMapEntryCost uid:{} catch PlayerNotFoundException uid:{}:", req.getUid());
            return RpcResult.create(e.getEnumErrCode());
        }
    }
}

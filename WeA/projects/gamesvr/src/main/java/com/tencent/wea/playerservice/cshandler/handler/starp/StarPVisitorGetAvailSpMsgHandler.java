package com.tencent.wea.playerservice.cshandler.handler.starp;

import com.google.protobuf.Message;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplus.starp.dao.StarPUid2BaseGroupTableDao;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsStarp;
import com.tencent.wea.protocol.SsStarpguildsvr.StarPGetGuildBriefInfoReq;
import com.tencent.wea.protocol.SsStarpguildsvr.StarPGetGuildBriefInfoRes;
import com.tencent.wea.protocol.common.EnmStarPBaseGroupType;
import com.tencent.wea.protocol.common.PlayerPublicInfo.Builder;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.StarPPublicUserInfo;
import com.tencent.wea.rpc.service.StarpguildService;
import com.tencent.wea.simpleData.PlayerPublic;
import com.tencent.wea.starp.StarPConfs;
import com.tencent.wea.tcaplus.TcaplusDb.StarPUid2BaseGroupTable;
import java.util.Collections;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class StarPVisitorGetAvailSpMsgHandler extends AbstractGsClientRequestHandler {

    private static final Logger LOGGER = LogManager.getLogger(StarPVisitorGetAvailSpMsgHandler.class);


    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request) {
        var resp = CsStarp.StarPVisitorGetAvailSp_S2C_Msg.newBuilder();
        if (!StarPConfs.isStarPOpen()) {
            return resp;
        }
        if (player.getPlayerStateMgr().isMatching()) {
            resp.setErrorCode(NKErrorCode.RoomStateIsMatching.getValue());
            return resp;
        }
        CsStarp.StarPVisitorGetAvailSp_C2S_Msg reqMsg = (CsStarp.StarPVisitorGetAvailSp_C2S_Msg) request;
        // 先保持旧版正常使用! 后续代码要干掉
        if (reqMsg.getStarPWorIdCount() > 0 && reqMsg.getFriendUid() == 0) {
            player.getPlayerStarPMgr().getAvailSp(reqMsg.getStarPWorId(0), 0, false, resp);
            return resp;
        }
        long friendUid = reqMsg.getFriendUid();
        Map<Long, Builder> playerPublicData = PlayerPublic.batchGetPlayerPublic(Collections.singleton(friendUid),
                Collections.singletonList(PlayerPublicInfoField.STARP_ROLE_INFO));
        Builder friendInfo = playerPublicData.get(friendUid);
        if (friendInfo == null) {
            resp.setErrorCode(NKErrorCode.StarPLotteryPlayerNotExist.getValue());
        } else {
            StarPPublicUserInfo starPPublicUserInfo = friendInfo.getStarPPublicUserInfo();
            // 若玩家存在部落,则优先去部落世界
            long starPWorldId;
            boolean checkFriend = false;
            if (starPPublicUserInfo.getGlobalGuildId() > 0 && starPPublicUserInfo.getGuildStarPWorldId() > 0) {
                starPWorldId = starPPublicUserInfo.getGuildStarPWorldId();
            } else {
                starPWorldId = starPPublicUserInfo.getCommonInfo().getStarPWorldId();
                long mainTerminalUID = starPPublicUserInfo.getMainTerminalUID();
                if (mainTerminalUID <= 0 || starPWorldId <= 0) {
                    // 好友尚未建立据点，无法拜访
                    resp.setErrorCode(NKErrorCode.StarPFriendNoTerminal.getValue());
                    return resp;
                }
                checkFriend = true;
            }
            LOGGER.debug("StarPVisitorGetAvailSpMsgHandler GuildId:{} GuildStarPWorldId:{} starPWorldId:{} mainTerminalUID:{}",
                    starPPublicUserInfo.getGlobalGuildId(),starPPublicUserInfo.getGuildStarPWorldId(),
                    friendInfo.getStarPPublicUserInfo().getCommonInfo().getStarPWorldId(),
                    starPPublicUserInfo.getMainTerminalUID());
            // 尝试预占位
            player.getPlayerStarPMgr().getAvailSp(starPWorldId, friendUid, checkFriend, resp);
        }
        return resp;
    }


    public long getGuildStarPId(long uid) {
        long starPWorldId = 0;
        try {
            NKPair<NKErrorCode, StarPUid2BaseGroupTable> tableItem = StarPUid2BaseGroupTableDao
                    .getTcaplusStarPUid2BaseGroupTableItem(uid,
                            EnmStarPBaseGroupType.ENM_STARP_BASEGROUP_TYPE_GUILD.getNumber());
            if (tableItem.getKey().isOk() && tableItem.getValue() != null) {
                long guildId = tableItem.getValue().getBaseGroupId();
                // 根据部落id去获取部落的数据
                RpcResult<StarPGetGuildBriefInfoRes.Builder> briefInfo = StarpguildService.get()
                        .starPGetGuildBriefInfo(StarPGetGuildBriefInfoReq.newBuilder().setGuildId(guildId));
                if (briefInfo.isOK()) {
                    starPWorldId = briefInfo.getData().getGuildInfo().getStarPId();
                    LOGGER.debug("getGuildStarPId starPWorldId:{} guildId:{} uid:{}",
                            briefInfo.getData().getGuildInfo().getStarPId(), guildId, uid);
                }
            }
        } catch (Exception e) {
            LOGGER.error("getGuildStarPId error! uid:{}", uid, e);
        }
        return starPWorldId;
    }
}

package com.tencent.wea.playerservice.cshandler.handler.activity;

import com.google.protobuf.Message;
import com.tencent.wea.playerservice.cshandler.AbstractGsClientRequestHandler;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.protocol.CsActivity;
import com.tencent.wea.protocol.CsHead;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ActivityListAllMsgHandler extends AbstractGsClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(ActivityListAllMsgHandler.class);

    @Override
    public Message.Builder handle(Player player, CsHead.CSHeader header, Message request)  {
        CsActivity.ActivityListAll_C2S_Msg reqMsg = (CsActivity.ActivityListAll_C2S_Msg)request;
        CsActivity.ActivityListAll_S2C_Msg.Builder rspBuilder = CsActivity.ActivityListAll_S2C_Msg.newBuilder();
        player.getActivityManager().handleActivityListAll(reqMsg, rspBuilder)
                .throwErrorNoStackIfNotOk();
        return rspBuilder;
    }
}
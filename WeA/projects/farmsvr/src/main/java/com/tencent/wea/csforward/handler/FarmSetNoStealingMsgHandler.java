package com.tencent.wea.csforward.handler;

import com.google.protobuf.Message;
import com.tencent.farm.FarmRainbow;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.resourceloader.resclass.FarmSysConf;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.protocol.CsFarm;
import com.tencent.wea.csforward.FarmAbstractForwardClientRequestHandler;
import com.tencent.wea.farmservice.store.Farm;
import com.tencent.wea.protocol.common.TlogRequiredFields;
import com.tencent.wea.xlsRes.ResFarmRainbowConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FarmSetNoStealingMsgHandler extends FarmAbstractForwardClientRequestHandler {
    private static final Logger LOGGER = LogManager.getLogger(FarmSetNoStealingMsgHandler.class);
    public Message.Builder specHandle(Farm farm, Message request, long playerUid, TlogRequiredFields tlogRequiredFields)  {
        CsFarm.FarmSetNoStealing_C2S_Msg reqMsg = (CsFarm.FarmSetNoStealing_C2S_Msg)request;
        CsFarm.FarmSetNoStealing_S2C_Msg.Builder rspMsg = CsFarm.FarmSetNoStealing_S2C_Msg.newBuilder();

        if (!FarmRainbow.isFarmModuleOpenNow(farm.getUid(), ResFarmRainbowConfig.FarmModuleIdEnum.FMI_NoStealing)) {
            NKErrorCode.FarmNoStealingNotOpen.throwErrorSmart();
        }

        int startHour = reqMsg.getStartHour();
        if (startHour < -1 || startHour > 23) {
            LOGGER.error("set no stealing time invalid {} -> {}", farm.getUid(), startHour);
            NKErrorCode.InvalidParams.throwErrorSmart();
        }

        var lastSetTimeMs = farm.getAttr().getFarmStealingInfo().getSetNoStealingTimeMs();
        if (lastSetTimeMs > 0) {
            var cdHour = (int) FarmSysConf.getIntWithDefault(FarmSysConf.ID.NoStealingResetCDHour, 168);
            var now = Framework.currentTimeMillis();
            if (now - lastSetTimeMs < cdHour * DateUtils.ONE_HOUR_MILLIS) {
                LOGGER.error("no stealing cd not expired {} -> {} {}", farm.getUid(), lastSetTimeMs, cdHour);
                NKErrorCode.FarmBeStolenProtectCDNotExpired.throwErrorSmart();
            }
        }

        farm.getSocialManager().setNoStealingTime(startHour);
        if (farm.hasCook()) {
            farm.getCook().getAttr().getCookBasicInfo().setNoStealingStartHour(startHour);
        }

        return rspMsg;
    }
}
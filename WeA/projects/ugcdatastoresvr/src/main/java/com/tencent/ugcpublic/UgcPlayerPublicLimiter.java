package com.tencent.wea.ugcpublic;

import com.tencent.lru.LRUCache;
import com.tencent.util.RateLimiter;
import com.tencent.wea.mgr.UgcPlayerPublicMgr;
import com.tencent.wea.tcaplus.TcaplusDb.UgcPlayerPublic;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
public class UgcPlayerPublicLimiter {
    public static float getLimitTimeDuration() {
      return PropertyFileReader.getRealTimeFloatItem("ugc_player_public_limit_time_duration", 5); //限制时间间隔
    }
    public static int getQueryAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_query_attrs_limit_max_count", 100); //玩家被获取互动数据存档总频率限定：5秒100次
    }
    public static int getModifyAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_modify_attrs_limit_max_count", 200); //玩家被修改互动数据存档总频率限定：5秒200次
    }
    public static int getQueryArayAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_query_array_attrs_limit_max_count", 100); //玩家被获取互动列表存档总频率限定：5秒100次
    }
    public static int getModifyArrayAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_modify_array_attrs_limit_max_count", 200); //玩家被修改互动列表存档总频率限定：5秒200次
    }

    public static int getOpQueryAttrsLimitMaxCount() {
      return PropertyFileReader.getRealTimeIntItem("ugc_player_public_op_query_attrs_limit_max_count", 2); //玩家被获取互动数据存档总频率限定：5秒2次
    }
    public static int getOpModifyAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_op_modify_attrs_limit_max_count", 20); //玩家被修改互动数据存档总频率限定：5秒20次
    }
    public static int getOpQueryArayAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_op_query_array_attrs_limit_max_count", 10); //玩家被获取互动列表存档总频率限定：5秒10次
    }
    public static int getOpModifyArrayAttrsLimitMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_op_modify_array_attrs_limit_max_count", 20); //玩家被修改互动列表存档总频率限定：5秒20次
    }

    public static boolean checkQueryAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_query_attrs_limit", true); //玩家被获取互动数据存档总频率限定开关
    }
    public static boolean checkModifyAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_modify_attrs_limit", true); //玩家被修改互动数据存档总频率限定开关
    }
    public static boolean checkQueryArayAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_query_array_attrs_limit", true); //玩家被获取互动列表存档总频率限定开关
    }
    public static boolean checkModifyArrayAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_modify_array_attrs_limit", true); //玩家被修改互动列表存档总频率限定开关
    }


    public static boolean checkOpQueryAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_op_query_attrs_limit", true); //玩家被获取互动数据存档总频率限定开关
    }
    public static boolean checkOpModifyAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_op_modify_attrs_limit", true); //玩家被修改互动数据存档总频率限定开关
    }
    public static boolean checkOpQueryArayAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_op_query_array_attrs_limit", true); //玩家被获取互动列表存档总频率限定开关
    }
    public static boolean checkOpModifyArrayAttrsLimit() {
        return PropertyFileReader.getRealTimeBooleanItem("ugc_player_public_check_op_modify_array_attrs_limit", true); //玩家被修改互动列表存档总频率限定开关
    }    
    public static int checkOpPlayerMaxCount() {
        return PropertyFileReader.getRealTimeIntItem("ugc_player_public_check_op_player_max_count", 16); //玩家被获取互动数据存档总频率限定开关
    }

    public static float getBatchGetAttrsLimitTimeDuration() {
      return PropertyFileReader.getRealTimeFloatItem("ugc_player_public_batch_get_attrs_limit_time_duratoin", 10); //BatchGet限制时间间隔
    }
    public static int getBatchGetAttrsLimitMaxCount() {
      return PropertyFileReader.getRealTimeIntItem("ugc_player_public_batch_get_attrs_limit_max_count", 2000); //BatchGet频率限定：10秒2000个属性
    }


    private static final Logger LOGGER = LogManager.getLogger(UgcPlayerPublicLimiter.class);
    private final long ugcId;
    private final long uid;
    private final RateLimiter getPublicAttrsRateLimiter = new RateLimiter("get_public_attrs");
    private final RateLimiter modifyPublicAttrsRateLimiter = new RateLimiter("modify_public_attrs");
    private final RateLimiter getPublicArrayAttrsRateLimiter = new RateLimiter("get_public_array_attrs");
    private final RateLimiter modifyPublicArrayAttrsRateLimiter = new RateLimiter("modify_public_array_attrs");

    private final RateLimiter batchGetPublicAttrsLimiter = new RateLimiter("batch_get_public_attrs_limiter");
 
    //对发起操作的玩家限频      
    private final LRUCache<Long, RateLimiter> getPublicAttrsOpRateLimiterMap;
    private final LRUCache<Long, RateLimiter> modifyPublicAttrsOpRateLimiterMap;
    private final LRUCache<Long, RateLimiter> getPublicArrayAttrsOpRateLimiterMap;
    private final LRUCache<Long, RateLimiter> modifyPublicArrayAttrsOpRateLimiterMap;
    public UgcPlayerPublicLimiter(long ugcId, long uid) {
        this.ugcId = ugcId;
        this.uid = uid;
        int checkOpPlayerMaxCount =checkOpPlayerMaxCount();
        getPublicAttrsOpRateLimiterMap = new LRUCache<Long, RateLimiter>(checkOpPlayerMaxCount,false);
        modifyPublicAttrsOpRateLimiterMap = new LRUCache<Long, RateLimiter>(checkOpPlayerMaxCount,false);
        getPublicArrayAttrsOpRateLimiterMap = new LRUCache<Long, RateLimiter>(checkOpPlayerMaxCount,false);
        modifyPublicArrayAttrsOpRateLimiterMap = new LRUCache<Long, RateLimiter>(checkOpPlayerMaxCount,false);
    }
    private boolean checkGetAttrsRate() {
        if(!checkQueryAttrsLimit()) {
            return true;
        }
        int maxCount = getQueryAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        return getPublicAttrsRateLimiter.consume(maxCount, maxCount/time, 1);
    }
    private boolean checkModifyAttrsRate() {
        if(!checkModifyAttrsLimit()) {
            return true;
        }
        int maxCount = getModifyAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        return modifyPublicAttrsRateLimiter.consume(maxCount, maxCount/time, 1);

    }
    private boolean checkGetArrayAttrsRate() {
        if(!checkQueryArayAttrsLimit()) {
            return true;
        }
        int maxCount = getQueryArayAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        return getPublicArrayAttrsRateLimiter.consume(maxCount, maxCount/time, 1);
    }
    private boolean checkModifyArrayAttrsRate() {
        if(!checkModifyArrayAttrsLimit()) {
            return true;
        }
        int maxCount = getModifyArrayAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        return modifyPublicArrayAttrsRateLimiter.consume(maxCount, maxCount/time, 1);
    }

    public boolean checkOpGetAttrsRate(long opUid) {
        if(!checkGetAttrsRate()) {
            LOGGER.error("checkGetAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_get_attrs_limit, 1);
            return false;
        }
        if(!checkOpQueryAttrsLimit()) {
            return true;
        }
        int maxCount = getOpQueryAttrsLimitMaxCount();
        float time = getLimitTimeDuration();

        RateLimiter limiter = getPublicAttrsOpRateLimiterMap.computeIfAbsent(opUid, key -> new RateLimiter("op_get_public_attrs"));
        boolean r = limiter.consume(maxCount, maxCount/time, 1);
        if(r == false) {
            LOGGER.error("checkOpGetAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_op_get_attrs_limit, 1);
        }
        return r;
    }
    public boolean checkOpModifyAttrsRate(long opUid) {
        if(!checkModifyAttrsRate()) {
            LOGGER.error("checkModifyAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_modify_attrs_limit, 1);
            return false;
        }
        if(!checkOpModifyAttrsLimit()) {
            return true;
        }
        int maxCount = getOpModifyAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        RateLimiter limiter = modifyPublicAttrsOpRateLimiterMap.computeIfAbsent(opUid, key -> new RateLimiter("op_modify_public_attrs"));
        boolean r = limiter.consume(maxCount, maxCount/time, 1);
        if(r == false) {
            LOGGER.error("checkOpModifyAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_op_modify_attrs_limit, 1);
        }
        return r;
    }
    public boolean checkOpGetArrayAttrsRate(long opUid) {
        if(!checkGetArrayAttrsRate()) {
            LOGGER.error("checkGetArrayAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_get_array_attrs_limit, 1);
            return false;
        }
        if(!checkOpQueryArayAttrsLimit()) {
            return true;
        }
        int maxCount = getOpQueryArayAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
      
        RateLimiter limiter = getPublicArrayAttrsOpRateLimiterMap.computeIfAbsent(opUid, key -> new RateLimiter("op_get_public_array_attrs"));
        boolean r = limiter.consume(maxCount, maxCount/time, 1);
        if(r == false) {
            LOGGER.error("checkOpGetArrayAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_op_get_array_attrs_limit, 1);
        }
        return r;
    }
    public boolean checkOpModifyArrayAttrsRate(long opUid) {
        if(!checkModifyArrayAttrsRate()) {
            LOGGER.error("checkModifyArrayAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_modify_array_attrs_limit, 1);
            return false;
        }
        if(!checkOpModifyArrayAttrsLimit()) {
            return true;
        }
        int maxCount = getOpModifyArrayAttrsLimitMaxCount();
        float time = getLimitTimeDuration();
        RateLimiter limiter = modifyPublicArrayAttrsOpRateLimiterMap.computeIfAbsent(opUid, key -> new RateLimiter("op_modify_public_array_attrs"));
        boolean r = limiter.consume(maxCount, maxCount/time, 1);
        if(r == false) {
            LOGGER.error("checkOpModifyArrayAttrsRate rate limit, ugcId:{} uid:{} opUid:{}", ugcId, uid, opUid);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_op_modify_array_attrs_limit, 1);
        }
        return r;
    }
    public boolean checkBatchGetAttrsRate(int count) {
        int maxCount = getBatchGetAttrsLimitMaxCount();
        float time = getBatchGetAttrsLimitTimeDuration();
        boolean r = batchGetPublicAttrsLimiter.consume(maxCount, maxCount/time, count);
        if(r == false) {
            LOGGER.error("checkBatchGetAttrsRate rate limit, ugcId:{} uid:{} count:{}", ugcId, uid, count);
            UgcPlayerPublicMgr.getInstance().getMonitorAddStats().addStats(MonitorId.attr_ugc_player_public_op_batch_get_attrs_limit, 1);
        }
        return r;
    }
}

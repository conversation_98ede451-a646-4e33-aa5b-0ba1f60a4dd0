package com.tencent.wea.metadataservice.metadata;

import com.tencent.nk.metadata.MetadataServer;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.tbuspp.TbusppInstance;
import com.tencent.wea.metadataservice.metadata.router.LobbyRouter;
import com.tencent.wea.protocol.SsProxysvr;
import com.tencent.wea.protocol.common.MetaDataType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 元数据管理器
 *
 * <AUTHOR>
 * @date 2019/12/04
 */
public class MetadataManager {

    private static final Logger LOGGER = LogManager.getLogger(MetadataManager.class);
    private MetadataServer metadata;
    private CacheChecker checker;

    public static MetadataManager getInstance() {
        return MetadataManager.LazyHolder.INSTANCE;
    }

    public int onInit() {
        // 初始化缓存
        int capacity = PropertyFileReader.getIntItem("proxy_metadata_cache_capacity", 2000000);
        long timeout = PropertyFileReader.getLongItem("proxy_metadata_lock_timeout_ms", 4000L);
        metadata = new MetadataServer(capacity, timeout, new Adapter());
        checker = new CacheChecker();
        return 0;
    }

    public int onReload() {
        // 重置缓存
        boolean reset = PropertyFileReader.getRealTimeBooleanItem("reset_proxy_metadata_cache", false);
        if (reset) {
            metadata.reset();
        }
        return 0;
    }

    // 通过querySrc传入请求来源
    public int queryMetadata(int type, long uuid, String querySrc) {
        if (type == MetaDataType.MDT_MapId_VALUE || type == MetaDataType.MDT_SceneId_VALUE ||
                type == MetaDataType.MDT_ClubID_VALUE || type == MetaDataType.MDT_DsMapId_VALUE ||
                type == MetaDataType.MDT_BattleId_VALUE || type == MetaDataType.MDT_DsDB_VALUE) {
            return RoutingDataAccess.getInstance().get(MetaDataType.forNumber(type), uuid, 0);
        } else if (type == MetaDataType.MDT_LobbyId_VALUE) {
            return LobbyRouter.getInstance().getSvrRoute(uuid);
        } else if (type == MetaDataType.MDT_OfflinePlayer_VALUE) {
            int svr = metadata.query(MetaDataType.MDT_Player_VALUE, uuid, querySrc);
            if (svr == 0) {
                // 在线查不到就用离线
                svr = metadata.query(MetaDataType.MDT_OfflinePlayer_VALUE, uuid, querySrc);
            }
            return svr;
        } else {
            return metadata.query(type, uuid, querySrc);
        }
    }

    public int updateMetadata(int type, long uuid, int server, long count, SsProxysvr.ObjectAction action) {
        return metadata.update(type, uuid, server, count, action);
    }

    public int deleteMetadata(int type, long uuid) {
        metadata.delete(type, uuid);
        return 0;
    }

    private boolean doNotCache(long uuid) {
        return checker.invalid(uuid);
    }

    private static class LazyHolder {

        private static final MetadataManager INSTANCE = new MetadataManager();
    }

    private static class Adapter extends MetadataServer.Adapter {

        @Override
        public boolean doNotCache(long uuid) {
            return MetadataManager.getInstance().doNotCache(uuid);
        }

        @Override
        public int allocServer(int type, long uuid) {
            ObjectBalance balance = ObjectBalance.getBalance(type);
            if (balance == null) {
                LOGGER.error("balance policy of type {} is not defined", type);
                return 0;
            }
            int server = balance.allocServer(uuid);
            if (server == 0) {
                LOGGER.info("alloc no server for {}-{}", type, uuid);
                return 0;
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("alloc server {} for {}-{}", server, type, uuid);
            }
            return server;
        }

        @Override
        public long getRunCount(int server) {
            return 0;
        }

        @Override
        public boolean isServerOK(int server) {
            return TbusppInstance.isInstanceOnline(server);
        }
    }
}

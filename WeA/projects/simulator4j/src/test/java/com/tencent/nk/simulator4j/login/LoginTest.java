package com.tencent.nk.simulator4j.login;

import com.tencent.nk.simulator4j.core.SimulatorEnv;
import com.tencent.nk.simulator4j.core.TestFlowLauncher;
import com.tencent.wea.simulator4j.flow.LoginFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;

/**
 * 登录测试
 *
 * <AUTHOR>
 * @date 2020/10/23 8:26 下午
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SimulatorEnv.class)
public class LoginTest {

    /**
     * 日志记录器
     */
    private static final Logger LOGGER = LogManager.getLogger(LoginTest.class);

    /**
     * 初始化
     */
    @BeforeAll
    public static void init() {
        LOGGER.debug("LoginTest start");
    }

    /**
     * 结束
     */
    @AfterAll
    public static void end() {
        LOGGER.debug("LoginTest end");
    }

//    @Test
//    @DisplayName(value = "登录")
//    @Order(2)
//    public void testLogin() {
//        LoginFlow flow = new LoginFlow();
//        SingleFlowTestScene testScene = new SingleFlowTestScene(flow);
//        testScene.init(0, newPlayers);
//        Assertions.assertEquals(NKErrorCode.OK, testScene.launch());
//    }

    /**
     * 测试注册
     */
    @Test
    @DisplayName(value = "注册登录")
    @Order(1)
    public void testRegister() {
        LoginFlow flow = new LoginFlow();
        TestFlowLauncher.launch(flow);
    }
}

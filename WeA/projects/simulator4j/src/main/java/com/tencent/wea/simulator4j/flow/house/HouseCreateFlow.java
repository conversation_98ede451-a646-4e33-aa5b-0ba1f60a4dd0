package com.tencent.wea.simulator4j.flow.house;

import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.flow.com.AbstractFlow;

public class HouseCreateFlow extends AbstractFlow {
    /**
     * 运行
     *
     * @param player 玩家
     * @param args   arg参数
     */
    @Override
    public void run(SimPlayer player, Object... args) {
        player.runTransaction(MsgTypes.MSG_TYPE_HOUSECREATE_C2S_MSG, args);
    }
}

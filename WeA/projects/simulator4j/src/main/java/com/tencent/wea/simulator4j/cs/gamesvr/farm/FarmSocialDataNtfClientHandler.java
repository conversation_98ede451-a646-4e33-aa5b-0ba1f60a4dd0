package com.tencent.wea.simulator4j.cs.gamesvr.farm;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class FarmSocialDataNtfClientHandler extends AbstractPbMsgClientNtfHandler {
    private static final Logger LOGGER = LogManager.getLogger(FarmSocialDataNtfClientHandler.class);


    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsFarm.FarmSocialDataNtf body = (CsFarm.FarmSocialDataNtf) bodyMsg;
    }

}
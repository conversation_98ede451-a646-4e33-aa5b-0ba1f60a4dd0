package com.tencent.wea.simulator4j.cs.gamesvr.ams;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class AMSItemResultNtfClientHandler extends AbstractPbMsgClientNtfHandler {
    private static final Logger LOGGER = LogManager.getLogger(AMSItemResultNtfClientHandler.class);


    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsAms.AMSItemResultNtf body = (CsAms.AMSItemResultNtf) bodyMsg;
    }

}
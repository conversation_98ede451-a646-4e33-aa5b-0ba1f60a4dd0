package com.tencent.wea.simulator4j.flow.album;

import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.flow.com.AbstractFlow;

public class AlbumPicWallGetMyListFlow extends AbstractFlow {

    @Override
    public void run(SimPlayer player, Object... args) {
        player.runTransaction(MsgTypes.MSG_TYPE_GETPICWALLMYPICLIST_C2S_MSG, args);
    }
}

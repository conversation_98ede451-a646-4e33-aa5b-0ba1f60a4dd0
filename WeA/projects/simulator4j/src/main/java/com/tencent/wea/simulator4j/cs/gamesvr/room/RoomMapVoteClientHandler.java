package com.tencent.wea.simulator4j.cs.gamesvr.room;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class RoomMapVoteClientHandler extends AbstractPbMsgClientS2CHandler {
    private static final Logger LOGGER = LogManager.getLogger(RoomMapVoteClientHandler.class);

    @Override
    public Message buildRequest(SimPlayer player, Object... args) {
        CsRoom.RoomMapVote_C2S_Msg.Builder req = CsRoom.RoomMapVote_C2S_Msg.newBuilder();
        return req.build();
    }

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsRoom.RoomMapVote_S2C_Msg body = (CsRoom.RoomMapVote_S2C_Msg) bodyMsg;
    }

}
package com.tencent.wea.simulator4j.flow.room;

import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.flow.com.AbstractFlow;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ModifyModeFlow extends AbstractFlow {

    /**
     * 日志记录器
     */
    protected static final Logger LOGGER = LogManager.getLogger(ModifyModeFlow.class);

    @Override
    public void run(SimPlayer player, Object... args) {
        player.runTransaction(MsgTypes.MSG_TYPE_ROOMMODIFYMODMODE_C2S_MSG, args);
    }
}

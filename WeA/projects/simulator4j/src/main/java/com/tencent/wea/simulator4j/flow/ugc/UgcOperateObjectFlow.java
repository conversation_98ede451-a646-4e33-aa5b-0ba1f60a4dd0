package com.tencent.wea.simulator4j.flow.ugc;

import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.flow.com.AbstractFlow;

/**
 * 创建流
 *
 * <AUTHOR>
 * @date 2022/03/25
 */
public class UgcOperateObjectFlow extends AbstractFlow {

    /**
     * 运行
     *
     * @param player 玩家
     * @param args 参数
     */
    @Override
    public void run(SimPlayer player, Object... args) {
        player.runTransaction(MsgTypes.MSG_TYPE_UGCOBJECTOPERATE_C2S_MSG, args);
    }
}

package com.tencent.wea.simulator4j.cs.gamesvr.raffle;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.CsRaffle;
import com.tencent.wea.protocol.CsRaffle.RafflePurchase;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.cs.AbstractPbMsgClientS2CHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class TestRaffleClientHandler extends AbstractPbMsgClientS2CHandler {
    private static final Logger LOGGER = LogManager.getLogger(TestRaffleClientHandler.class);

    @Override
    public Message buildRequest(SimPlayer player, Object... args) {
        CsRaffle.TestRaffle_C2S_Msg.Builder req = CsRaffle.TestRaffle_C2S_Msg.newBuilder();
        req.setRaffleId((int) args[0]);
        for (int i = 1; i < args.length; i++) {
            req.addPurchases((RafflePurchase) args[i]);
        }
        return req.build();
    }

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsRaffle.TestRaffle_S2C_Msg body = (CsRaffle.TestRaffle_S2C_Msg) bodyMsg;
    }

}
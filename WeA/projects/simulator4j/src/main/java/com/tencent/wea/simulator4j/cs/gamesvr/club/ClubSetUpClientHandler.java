package com.tencent.wea.simulator4j.cs.gamesvr.club;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsClub;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.simulator4j.com.player.SimPlayer;
import com.tencent.wea.simulator4j.cs.AbstractPbMsgClientS2CHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ClubSetUpClientHandler extends AbstractPbMsgClientS2CHandler {

    private static final Logger LOGGER = LogManager.getLogger(ClubSetUpClientHandler.class);

    @Override
    public Message buildRequest(SimPlayer player, Object... args) {
        CsClub.ClubSetUp_C2S_Msg.Builder req = CsClub.ClubSetUp_C2S_Msg.newBuilder();
        req.setAutoAgree(Boolean.valueOf(args[0].toString()));
        return req.build();
    }

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
    }

}
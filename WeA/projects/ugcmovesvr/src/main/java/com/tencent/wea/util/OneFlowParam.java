package com.tencent.wea.util;

import com.google.gson.annotations.SerializedName;

public class OneFlowParam {
    @SerializedName("roleInfo")
    public RoleInfoParam roleInfoParam = new RoleInfoParam();

    public static class RoleInfoParam {
        public String nickname;
        public int gender;
        public int face;
        public int suit;

        public String toString() {
            return String.format("nickname:%s gender:%d face:%d suit:%d", nickname, gender, face, suit);
        }
    }

    public String toString() {
        return "roleInfo" + roleInfoParam.toString();
    }
}

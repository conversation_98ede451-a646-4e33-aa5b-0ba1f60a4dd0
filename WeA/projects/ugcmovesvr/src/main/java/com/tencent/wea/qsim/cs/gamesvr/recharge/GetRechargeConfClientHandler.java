package com.tencent.wea.qsim.cs.gamesvr.recharge;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.qsim.com.player.SimPlayer;
import com.tencent.wea.qsim.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class GetRechargeConfClientHandler extends AbstractPbMsgClientS2CHandler {
    private static final Logger LOGGER = LogManager.getLogger(GetRechargeConfClientHandler.class);

    @Override
    public Message buildRequest(SimPlayer player, Object... args) {
        CsRecharge.GetRechargeConf_C2S_Msg.Builder req = CsRecharge.GetRechargeConf_C2S_Msg.newBuilder();
        return req.build();
    }

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsRecharge.GetRechargeConf_S2C_Msg body = (CsRecharge.GetRechargeConf_S2C_Msg) bodyMsg;
    }

}
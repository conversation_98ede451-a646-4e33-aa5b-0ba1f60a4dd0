package com.tencent.wea.qsim.cs.gamesvr.player;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.protocol.*;
import com.tencent.wea.qsim.com.player.SimPlayer;
import com.tencent.wea.qsim.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlayerPrayClientHandler extends AbstractPbMsgClientS2CHandler {
    private static final Logger LOGGER = LogManager.getLogger(PlayerPrayClientHandler.class);

    @Override
    public Message buildRequest(SimPlayer player, Object... args) {
        CsPlayer.PlayerPray_C2S_Msg.Builder req = CsPlayer.PlayerPray_C2S_Msg.newBuilder();
        return req.build();
    }

    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsPlayer.PlayerPray_S2C_Msg body = (CsPlayer.PlayerPray_S2C_Msg) bodyMsg;
    }

}
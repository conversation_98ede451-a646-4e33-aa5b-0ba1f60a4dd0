package com.tencent.wea.qsim.cs.gamesvr.notice;

import com.google.protobuf.Message;
import com.tencent.wea.protocol.CsHead;
import com.tencent.wea.qsim.com.player.SimPlayer;
import com.tencent.wea.qsim.cs.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class PlayerItemChangeNtfClientHandler extends AbstractPbMsgClientNtfHandler {
    private static final Logger LOGGER = LogManager.getLogger(PlayerItemChangeNtfClientHandler.class);


    @Override
    public void onSuccess(SimPlayer player, CsHead.CSHeader header, Message bodyMsg) {
        //CsNotice.PlayerItemChangeNtf body = (CsNotice.PlayerItemChangeNtf) bodyMsg;
    }

}
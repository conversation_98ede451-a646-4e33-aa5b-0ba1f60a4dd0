package com.tencent.wea.action.servlet;

import static com.tencent.nk.util.NKErrorCode.UgcPlatInvalidJson;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.protobuf.Message;
import com.tencent.nk.util.NKPair;
import com.tencent.util.JsonUtil;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.db.SavedDao;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgReq;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgReq.Builder;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgRes;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

public class SaveDataAction extends BaseAction<Builder> {

    private static final Logger LOGGER = LogManager.getLogger(SaveDataAction.class);

    @Override
    public Message.Builder execute(UgcServletMsgReq.Builder req) {
        String body = req.getBody();
        JsonObject reqBody;
        UgcServletMsgRes.Builder ret = UgcServletMsgRes.newBuilder();
        JSONObject retData = new JSONObject();
        try {
            reqBody = JsonParser.parseString(body).getAsJsonObject();
        } catch (Exception e) {
            LOGGER.error("saved data req:{} invalid, e:", body, e);
            retData.put("code", UgcPlatInvalidJson.getValue())
                    .put("msg", "saved data action req is not valid json");
            ret.setContent(retData.toString());
            return ret;
        }
        String table = JsonUtil.getString(retData, reqBody, "table");
        if (retData.has("code") && retData.getInt("code") != 0) {
            ret.setContent(retData.toString());
            LOGGER.error("saved data action param with table name req:{}, ret:{}",
                    reqBody, retData);
            return ret;
        }

        NKPair<Integer, String> saveRet = SavedDao.handleSaved(table, reqBody);
        LOGGER.info("saved data action req:{}, ret:{}", reqBody, saveRet);

        retData.put("code", saveRet.getKey())
                .put("msg", saveRet.getValue());

        ret.setContent(retData.toString());
        return ret;
    }

    @Override
    public void executeRun(UgcServletMsgReq.Builder builder) {

    }

    @Override
    public Message.Builder errorExecute(Exception e) {
        return null;
    }
}

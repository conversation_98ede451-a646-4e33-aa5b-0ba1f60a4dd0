package com.tencent.wea.action.servlet;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplus.dao.UgcPlayerInfoDao;
import com.tencent.ugc.CommonUtil;
import com.tencent.ugc.UgcPathManager;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.action.ActionUtil;
import com.tencent.wea.action.BaseAction;
import com.tencent.wea.protocol.SsUgcplatsvr;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgReq;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgReq.Builder;
import com.tencent.wea.protocol.SsUgcplatsvr.UgcServletMsgRes;
import com.tencent.wea.protocol.SsUgcsvr;
import com.tencent.wea.protocol.common.*;
import com.tencent.wea.rpc.service.UgcService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * ugc地图/资产保存类(for管理端使用)
 */
public class UgcResApplyKeyInfoAction extends BaseAction<UgcServletMsgReq.Builder> {

    private static final Logger LOGGER = LogManager.getLogger(UgcResApplyKeyInfoAction.class);

    @Override
    public Message.Builder execute(Builder builder) {
        UgcServletMsgRes.Builder ret = UgcServletMsgRes.newBuilder();

        // 将json字符串转换成pb结构体数据
        String body = builder.getBody();
        SsUgcplatsvr.UgcResApplyKeyInfo.Builder reqInfoBuilder = SsUgcplatsvr.UgcResApplyKeyInfo.newBuilder();
        try {
            ActionUtil.fromJsonIgnoreUnknownFields(body, reqInfoBuilder);
        } catch (Exception ex) {
            LOGGER.error("invalid json:{}", body);
            ret.setContent(genRetStr(NKErrorCode.UnknownError.getValue(), "invalid json"));
            return ret;
        }

        // 请求参数检查
        NKPair<Integer, String> checkRet = checkParam(reqInfoBuilder);
        if (checkRet.key != 0) {
            LOGGER.error("checkParam failed, result:{}, {}", checkRet.getKey(), checkRet.value);
            ret.setContent(genRetStr(checkRet.key, checkRet.value));
            return ret;
        }

        // 获取ugc玩家信息
        TcaplusDb.UgcPlayerInfo dbUgcPlayerInfo = UgcPlayerInfoDao.getUgcPlayerInfo(reqInfoBuilder.getCreatorId(),
                CommonUtils.SimpleUgcPlayerInfoFields);
        if (dbUgcPlayerInfo == null) {
            LOGGER.error("UgcPlayerInfo is null, creatorId:{}", reqInfoBuilder.getCreatorId());
            ret.setContent(genRetStr(NKErrorCode.UgcPlayerInfoFail.getValue(), "invalid ugc player info"));
            return ret;
        }

        // 构造请求参数
        SsUgcsvr.RpcUgcApplyKeyInfoReq.Builder reqBuilder = SsUgcsvr.RpcUgcApplyKeyInfoReq.newBuilder();
        reqBuilder.setCreatorId(reqInfoBuilder.getCreatorId());
        reqBuilder.setUgcId(reqInfoBuilder.getUgcId());
        reqBuilder.setInstanceType(reqInfoBuilder.getInstanceType());
        reqBuilder.setUgcResType(reqInfoBuilder.getUgcResType());
        reqBuilder.setBucket(dbUgcPlayerInfo.getCommonInfo().getBucket());
        if (reqInfoBuilder.getUgcResType() == UgcResType.EURT_3DModel_VALUE) {
            // 由于3D模型资源较大, 暂时使用单独的cos申请原因
            reqBuilder.addApplyReason(ApplyReason.AR_3D_MODEL_SAVE_VALUE);
        }
        reqBuilder.addApplyReason(ApplyReason.AR_GROUP_OBJECT_VALUE);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("uid:{}, creatorId:{}, reqInfoBuilder:{}, reqBuilder:{}",
                    reqInfoBuilder.getUid(), reqInfoBuilder.getCreatorId(), Pb2JsonUtil.getPbMsg(reqInfoBuilder),
                    Pb2JsonUtil.getPbMsg(reqBuilder));
        }

        try {
            // 调用rpc接口
            UgcService service = UgcService.get();
            RpcResult<SsUgcsvr.RpcUgcApplyKeyInfoRes.Builder> rpcResult = service.rpcUgcApplyKeyInfo(reqBuilder);

            // 判断返回码
            if (!rpcResult.isOK()) {
                LOGGER.error("rpcUgcApplyKeyInfo failed, uid:{}, creatorId:{}, ret:{}",
                        reqInfoBuilder.getUid(), reqInfoBuilder.getCreatorId(), rpcResult.getRet());
                ret.setContent(genRetStr(rpcResult.getRet(), "ugc apply key info failed"));
                return ret;
            }

            int code = rpcResult.getData().getResult();
            if (code != NKErrorCode.OK.getValue()) {
                LOGGER.error("rpcUgcApplyKeyInfo failed, uid:{}, creatorId:{}, ret:{}",
                        reqInfoBuilder.getUid(), reqInfoBuilder.getCreatorId(), code);
                ret.setContent(genRetStr(code, "ugc apply key info failed"));
                return ret;
            }

            JsonObject jsonData = new JsonObject();
            jsonData.addProperty("ugcId", reqInfoBuilder.getUgcId());
            jsonData.addProperty("bucket", CommonUtil.getClientBucket(dbUgcPlayerInfo.getCommonInfo().getBucket()));
            jsonData.addProperty("region", CommonUtil.getRegion(dbUgcPlayerInfo.getCommonInfo().getBucket()));
            jsonData.addProperty("cosCoverPath",
                    UgcPathManager.CoverPath.getUgcCoverPath(reqInfoBuilder.getInstanceType(),
                            reqInfoBuilder.getUgcResType(), reqInfoBuilder.getCreatorId(), reqInfoBuilder.getUgcId(),
                            true));
            jsonData.addProperty("cosPbinPath",
                    UgcPathManager.MapDataPath.getUgcResPath(reqInfoBuilder.getInstanceType(),
                            reqInfoBuilder.getUgcResType(), reqInfoBuilder.getCreatorId(), reqInfoBuilder.getUgcId(),
                            true));
            for (SsUgcsvr.RpcUgcApplyKeyInfoRes.UgcApplyKeyInfo info : rpcResult.getData().getInfosList()) {
                if (info.getApplyReason() == ApplyReason.AR_3D_MODEL_SAVE_VALUE &&
                        reqInfoBuilder.getUgcResType() == UgcResType.EURT_3DModel_VALUE) {
                    jsonData.add("keyInfo",
                            JsonParser.parseString(JsonFormat.printer().print(info.getInfo())));
                } else if (info.getApplyReason() == ApplyReason.AR_GROUP_OBJECT_VALUE) {
                    jsonData.add("coverKeyInfo",
                            JsonParser.parseString(JsonFormat.printer().print(info.getInfo())));
                    if (reqInfoBuilder.getUgcResType() != UgcResType.EURT_3DModel_VALUE) {
                        jsonData.add("keyInfo",
                                JsonParser.parseString(JsonFormat.printer().print(info.getInfo())));
                    }
                }
            }

            ret.setContent(getResultStr(NKErrorCode.OK.getValue(), "success", jsonData));
        } catch (Exception ex) {
            LOGGER.error("rpcUgcApplyKeyInfo catch exception, uid:{}, creatorId:{}, ",
                    reqInfoBuilder.getUid(), reqInfoBuilder.getCreatorId(), ex);
            ret.setContent(genRetStr(NKErrorCode.UnknownError.getValue(), "unknown error"));
        }

        return ret;
    }

    @Override
    public void executeRun(Builder builder) {

    }

    @Override
    public Message.Builder errorExecute(Exception e) {
        UgcServletMsgRes.Builder ret = UgcServletMsgRes.newBuilder();
        return ret.setResult(NKErrorCode.UnknownError.getValue());
    }

    /**
     * 请求参数检查
     * @param reqInfoBuilder 请求结构体
     * @return 返回码->返回信息键值对
     */
    private NKPair<Integer, String> checkParam(SsUgcplatsvr.UgcResApplyKeyInfo.Builder reqInfoBuilder) {

        if (reqInfoBuilder.getUid() == 0L) {
            return new NKPair<>(NKErrorCode.InvalidParams.getValue(), "param uid is 0");
        }

        if (reqInfoBuilder.getCreatorId() == 0L) {
            return new NKPair<>(NKErrorCode.InvalidParams.getValue(), "param creatorId is 0");
        }

        if (reqInfoBuilder.getUgcId() == 0L) {
            return new NKPair<>(NKErrorCode.InvalidParams.getValue(), "param ugcId is 0");
        }

        if (UgcInstanceType.forNumber(reqInfoBuilder.getInstanceType()) == null) {
            return new NKPair<>(NKErrorCode.InvalidParams.getValue(), "param instanceType is invalid");
        }

        if (UgcResType.forNumber(reqInfoBuilder.getUgcResType()) == null) {
            return new NKPair<>(NKErrorCode.InvalidParams.getValue(), "param resType is invalid");
        }

        return new NKPair<>(NKErrorCode.OK.getValue(), "success");
    }
}
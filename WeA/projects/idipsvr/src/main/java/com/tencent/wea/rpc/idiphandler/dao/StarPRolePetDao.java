package com.tencent.wea.rpc.idiphandler.dao;

import com.google.protobuf.InvalidProtocolBufferException;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.wea.protocol.AttrStarPItem;
import com.tencent.wea.rpc.idiphandler.util.IdipConstants;
import com.tencent.wea.tcaplus.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashSet;
import java.util.Set;

import com.tencent.wea.spdbcomm.SPDsCommonDbInfoTableCacheHelper;
import com.tencent.wea.spdbcomm.SPDsCommonDbInfoTableCache;
import com.google.protobuf.Message;


//角色相关数据库啾灵操作
public class StarPRolePetDao {
    private static final Logger LOGGER = LogManager.getLogger(com.tencent.wea.rpc.idiphandler.dao.StarPRolePetDao.class);

    //获取啾灵相关信息
    public static StarPDsCommonDbInfoView<AttrStarPItem.proto_StarPItem> getPetInfo(long starpWorldId, long petUid) throws InvalidProtocolBufferException {
        SPDsCommonDbInfoTableCache.Key key = new SPDsCommonDbInfoTableCache.Key(starpWorldId, IdipConstants.DB_KEY_MARK_STARP_ITEM, petUid, -1, -1, -1, -1, -1);
        Message.Builder dscommonDbInfoBuilder = SPDsCommonDbInfoTableCacheHelper.getPartkeyBuilder(key);
        TcaplusManager.TcaplusReq itemGetReg = TcaplusUtil.newGetByPartKeyReq(dscommonDbInfoBuilder);
        TcaplusManager.TcaplusRsp itemGetRsp = itemGetReg.send();

        if (itemGetRsp == null) {
            LOGGER.error("get item return null, starpWorldId={} petUid={}", starpWorldId, petUid);
            return null;
        }

        if (!itemGetRsp.isOK()) {
            LOGGER.error("get item error, starpWorldId={} petUid={}", starpWorldId, petUid);
            return null;
        }
        for (TcaplusManager.TcaplusRecordGroup rspData : itemGetRsp.getRspDataList()) {
            for (TcaplusManager.TcaplusRecordData<?> rData : rspData.getRecordList()) {
                TcaplusDb.DsCommonDbInfoTable profile = SPDsCommonDbInfoTableCacheHelper.transComm(starpWorldId, rData.msg);
                byte[] bs = profile.getCommonDsDbAttr().toByteArray();
                return new StarPDsCommonDbInfoView<>(profile.getKey1(), profile.getKey2(), profile.getKey3(), profile.getKey4(),  profile.getKey5(), profile.getKey6(), profile.getKey7(), profile.getKey8(), AttrStarPItem.proto_StarPItem.parseFrom(bs));
            }
        }
        return null;
    }

    // 设置玩家宠物等级
    public static TcaplusManager.TcaplusRsp setPetData(long starPWorldId,long petUid, StarPDsCommonDbInfoView<AttrStarPItem.proto_StarPItem> viewItem){
        TcaplusDb.DsCommonDbInfoTable.Builder petTableBuilder = TcaplusDb.DsCommonDbInfoTable.newBuilder()
                .setKey1(starPWorldId)
                .setKey2(IdipConstants.DB_KEY_MARK_STARP_ITEM)
                .setKey3(petUid)
                .setKey4(viewItem.getKey4())
                .setKey5(viewItem.getKey5())
                .setKey6(viewItem.getKey6())
                .setKey7(viewItem.getKey7())
                .setKey8(viewItem.getKey8())
                .setCommonDsDbAttr(viewItem.getData().toByteString());
        var newBuilder = SPDsCommonDbInfoTableCacheHelper.swtichMsg(starPWorldId, petTableBuilder.build());
        Set<String> changeField = new HashSet<>();
        changeField.add(IdipConstants.DB_FIELD_COMMON_DS_DB_ATTR);
        TcaplusManager.TcaplusReq petSetReg = TcaplusUtil.newReplaceReq(newBuilder).setChangeField(changeField);
        TcaplusManager.TcaplusRsp petSetRsp = petSetReg.send();
        return petSetRsp;
    }

}


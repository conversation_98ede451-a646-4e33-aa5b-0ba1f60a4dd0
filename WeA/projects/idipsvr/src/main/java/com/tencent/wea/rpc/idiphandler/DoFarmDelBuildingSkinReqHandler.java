//generated by tool
package com.tencent.wea.rpc.idiphandler;
import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.protocol.idip.*;
import com.tencent.wea.rpc.IdipRpcRouter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DoFarmDelBuildingSkinReqHandler implements IdipMsgHandler {
    public static final Logger LOGGER = LogManager.getLogger(DoFarmDelBuildingSkinReqHandler.class);

    @Override
    public IdipResponse.Builder handle(IdipRequest.Builder request) {
        DoFarmDelBuildingSkinReq.Builder req =  request.getDoFarmDelBuildingSkinReqBuilder();
        DoFarmDelBuildingSkinRsp.Builder res = DoFarmDelBuildingSkinRsp.newBuilder();

        try {
            res = (DoFarmDelBuildingSkinRsp.Builder) IdipRpcRouter.rpcCall(request.getCmdId(), req.getUid(), req);
        } catch (Exception ex) {
            LOGGER.error("DoFarmDelBuildingSkinRsp unexpected error, e:{}", ex.toString());
            NKErrorCode.UnknownError.throwError("catch unknown error, openId:{}, platId:{}, uid:{}",
                    req.getOpenId(), req.getPlatId(), req.getUid());
        }

        IdipResponse.Builder idipResponse = IdipResponse.newBuilder().setDoFarmDelBuildingSkinRsp(res);
        return idipResponse;
    }
}
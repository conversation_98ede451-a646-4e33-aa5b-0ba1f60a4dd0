//generated by tool
package com.tencent.wea.rpc.idiphandler;

import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.tcaplus.dao.OpenIdToUidDao;
import com.tencent.wea.interaction.player.DoModifyFriendRelationInteraction;
import com.tencent.wea.protocol.idip.*;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class DoModifyFriendRelationReqHandler implements IdipMsgHandler {
    public static final Logger LOGGER = LogManager.getLogger(DoModifyFriendRelationReqHandler.class);

    @Override
    public IdipResponse.Builder handle(IdipRequest.Builder request) {
        DoModifyFriendRelationReq.Builder req =  request.getDoModifyFriendRelationReqBuilder();
        DoModifyFriendRelationRsp.Builder res = DoModifyFriendRelationRsp.newBuilder();

        // 参数检查
        if (!req.hasUid() || !req.hasRelation()) {
            NKErrorCode.InvalidParams.throwError("params [Uid or Relation] not exist");
        }

        // 获取uid, 优先从req中获取, 获取不到时基于openid和platid从db表里获取
        long uid = req.getUid();
        if (uid == 0L) {
            if (!req.hasOpenId() || !req.hasPlatId()) {
                NKErrorCode.InvalidParams.throwError("params [OpenId or PlatId] not exist");
            }

            uid = OpenIdToUidDao.getUidByOpenIdAndPlatId(req.getOpenId(), req.getPlatId());
        }
        if (uid == 0L) {
            NKErrorCode.IdipNoSuchUser.throwError("IdipNoSuchPlayer, openid:{}, uid:{}",
                    req.hasOpenId() ? req.getOpenId() : "", uid);
        }

        long friendUid = req.hasFriendUid() ? req.getFriendUid() : 0L;
        if (friendUid == 0L) {
            if (!req.hasFriendOpenId() || !req.hasFriendPlatId()) {
                NKErrorCode.InvalidParams.throwError("params [FriendOpenId or FriendPlatId] not exist");
            }
            friendUid = OpenIdToUidDao.getUidByOpenIdAndPlatId(req.getFriendOpenId(), req.getFriendPlatId());
        }
        if (friendUid == 0L) {
            NKErrorCode.IdipNoSuchUser.throwError("IdipNoSushPlayer, openid:{}, uid:{}", req.hasFriendOpenId() ? req.getFriendOpenId() : "", friendUid);
        }

        // 操作用户好友亲密度修改interaction结构体拼接
        PlayerInteraction.PiiModifyFriendRelationParams.Builder params =
                PlayerInteraction.PiiModifyFriendRelationParams.newBuilder()
                        .setFriendOpenId(req.hasFriendOpenId() ? req.getFriendOpenId() : "")
                        .setFriendPlatId(req.hasFriendPlatId() ? req.getFriendPlatId() : 0)
                        .setFriendUid(friendUid)
                        .setRelation(req.getRelation())
                        .setSource(req.hasSource() ? req.getSource() : 0)
                        .setSerial(req.hasSerial() ? req.getSerial() : "");
        PlayerInteraction.PlayerInteractionData.Builder builder = PlayerInteraction.PlayerInteractionData.newBuilder()
                .setInstruction(PlayerInteraction.PlayerInteractionInstruction.PII_MODIFY_FRIEND_RELATION)
                .setModifyFriendRelationParams(params.build());
        LOGGER.debug("idip modify player friend relation, uid:{}, friendUid:{}, param:{}", uid, friendUid, params);

        try {
            // 发起修改用户好友亲密度, 先存储数据到db, 在线则转发到gamesvr处理
            DoModifyFriendRelationInteraction.sendDoModifyFriendRelation(uid, builder);
        } catch (Exception ex) {
            LOGGER.error("DoModifyFriendRelationReqHandler notify user unexpected error, uid:{}, friendUid:{}, e:{}",
                    uid, friendUid, ex);
            NKErrorCode.UnknownError.throwError("catch unknown error, uid:{}, friendUid:{}", uid, friendUid);
        }

        // 单边补发
        boolean isUnilateral = 1 == req.getIsUnilateral();
        if (!isUnilateral) {
            // 被操作用户好友亲密度修改interaction结构体拼接
            PlayerInteraction.PiiModifyFriendRelationParams.Builder friendParams =
                    PlayerInteraction.PiiModifyFriendRelationParams.newBuilder()
                            .setFriendOpenId(req.hasOpenId() ? req.getOpenId() : "")
                            .setFriendPlatId(req.hasPlatId() ? req.getPlatId() : 0)
                            .setFriendUid(uid)
                            .setRelation(req.getRelation())
                            .setSource(req.hasSource() ? req.getSource() : 0)
                            .setSerial(req.hasSerial() ? req.getSerial() : "");
            PlayerInteraction.PlayerInteractionData.Builder friendBuilder =
                    PlayerInteraction.PlayerInteractionData.newBuilder()
                            .setInstruction(PlayerInteraction.PlayerInteractionInstruction.PII_MODIFY_FRIEND_RELATION)
                            .setModifyFriendRelationParams(friendParams.build());
            LOGGER.debug("idip modify player friend relation, uid:{}, friendUid:{}, param:{}",
                    uid, friendUid, friendParams);

            try {
                // 发起修改用户好友亲密度, 先存储数据到db, 在线则转发到gamesvr处理
                DoModifyFriendRelationInteraction.sendDoModifyFriendRelation(friendUid, friendBuilder);
            } catch (Exception ex) {
                LOGGER.error("DoModifyFriendRelationReqHandler notify user unexpected error, uid:{}, friendUid:{}, e:{}", uid, friendUid, ex);
                NKErrorCode.UnknownError.throwError("catch unknown error, uid:{}, friendUid:{}", uid, friendUid);
            }
        }

        res.setResult(0);
        res.setRetMsg("success");
        IdipResponse.Builder idipResponse = IdipResponse.newBuilder().setDoModifyFriendRelationRsp(res);
        return idipResponse;
    }
}
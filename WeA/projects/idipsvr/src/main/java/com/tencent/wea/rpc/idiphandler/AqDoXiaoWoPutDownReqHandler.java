//generated by tool
package com.tencent.wea.rpc.idiphandler;

import com.tencent.nk.idip.IdipMsgHandler;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.wea.interaction.player.DoModifyPlayerRegionInteraction;
import com.tencent.wea.protocol.idip.AqDoXiaoWoPutDownReq;
import com.tencent.wea.protocol.idip.AqDoXiaoWoPutDownRsp;
import com.tencent.wea.protocol.idip.IdipRequest;
import com.tencent.wea.protocol.idip.IdipResponse;
import com.tencent.wea.rpc.IdipRpcRouter;
import com.tencent.wea.tcaplus.db.PlayerInteraction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class AqDoXiaoWoPutDownReqHandler implements IdipMsgHandler {
    public static final Logger LOGGER = LogManager.getLogger(AqDoXiaoWoPutDownReqHandler.class);

    @Override
    public IdipResponse.Builder handle(IdipRequest.Builder request) {
        AqDoXiaoWoPutDownReq.Builder req = request.getAqDoXiaoWoPutDownReqBuilder();
        AqDoXiaoWoPutDownRsp.Builder res = AqDoXiaoWoPutDownRsp.newBuilder();
        try {
            res = (AqDoXiaoWoPutDownRsp.Builder) IdipRpcRouter.rpcCall(request.getCmdId(), req.getUid(), req);
        } catch (Exception ex) {
            LOGGER.error("XiaoWoPutDownReqHandler unexpected error, e:{}", ex.toString());
            NKErrorCode.UnknownError.throwError("rpcCall Exception");
        }

        PlayerInteraction.PlayerInteractionData.Builder builder = PlayerInteraction.PlayerInteractionData.newBuilder()
                .setInstruction(PlayerInteraction.PlayerInteractionInstruction.PII_CLEAR_PARTY);

        try {
            // 发起修改道具操作请求, 先存储数据到db, 在线则转发到gamesvr处理
            DoModifyPlayerRegionInteraction.sendDoModifyPlayerRegion(req.getUid(), builder);
        } catch (Exception ex) {
            LOGGER.error("AqDoXiaoWoPutDownReqHandler notify user unexpected error, uid:{}, e:{}",
                    req.getUid(), ex);
            NKErrorCode.UnknownError.throwError("catch unknown error, uid:{}", req.getUid());
        }

        IdipResponse.Builder idipResponse = IdipResponse.newBuilder().setAqDoXiaoWoPutDownRsp(res);
        return idipResponse;
    }
}
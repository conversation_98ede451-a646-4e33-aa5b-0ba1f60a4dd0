package com.tencent.wea.interaction.player;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class VerbalViolationReputationScoreUnPunishInteraction {
    private static final Logger LOGGER = LogManager.getLogger(VerbalViolationReputationScoreUnPunishInteraction.class);

    public static boolean sendVerbalViolationReputationScoreUnPunish(long playerUid, com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData.Builder data){
        return PlayerInteractionInvoker.interact(playerUid, data);
    }
}

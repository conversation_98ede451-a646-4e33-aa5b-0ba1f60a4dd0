package com.tencent.wea.interaction.player;

import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.resourceloader.resclass.MailIniConfData;
import com.tencent.resourceloader.resclass.MailTemplateConfData;
import com.tencent.resourceloader.resclass.UGCEditorMapTemplate;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.tcaplusattr.NumericAttr;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.timiutil.tool.FunctionUtil;
import com.tencent.wea.protocol.SsUgcsvr.RpcGetUgcPublishMapReq;
import com.tencent.wea.protocol.SsUgcsvr.RpcGetUgcPublishMapRes.Builder;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.MailContent;
import com.tencent.wea.protocol.common.MailExtraData;
import com.tencent.wea.protocol.common.MailSource;
import com.tencent.wea.protocol.common.MailSourceType;
import com.tencent.wea.protocol.common.MailUgcData;
import com.tencent.wea.protocol.common.MailUgcMapData;
import com.tencent.wea.protocol.common.PublishItem;
import com.tencent.wea.protocol.common.UgcTableBatchGetSource;
import com.tencent.wea.rpc.service.UgcService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PiiRemindNewMailParams;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionData;
import com.tencent.wea.tcaplus.db.PlayerInteraction.PlayerInteractionInstruction;
import com.tencent.wea.tlog.TlogFlowMgr;
import com.tencent.wea.xlsRes.ResMailTemplate;
import com.tencent.wea.xlsRes.ResUGCEditor;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.MailExtraType;
import com.tencent.wea.xlsRes.keywords.MailType;
import com.tencent.wea.xlsRes.keywords.UGCMapType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class MailInteraction {

    public static final Integer EN_LANGUAGE_ID = 2;     // 英语语种配置id
    private static final Logger LOGGER = LogManager.getLogger(MailInteraction.class);
    public static final String groupVersion = "0";

    public static long sendMail(long receiverUid, String title, String txt, MailType type, MailAttachmentList attList,
            long senderId, String senderName, String url, long sendUnixMs, long expireUnixMs,
            MailSourceType mailSourceType, boolean isOversea, int sourceCfgid, TlogSendReason sendReason,
            MailExtraParam extraParam, boolean isStarred, long platSvrMailCfgId) {
        TcaplusDb.PlayerMail mail = buildMail(receiverUid, title, txt, type, attList, senderId, senderName, url,
                sendUnixMs, expireUnixMs, mailSourceType, isOversea, sourceCfgid, extraParam, isStarred, platSvrMailCfgId);
        return sendMail(mail, sendReason);
    }

    private static NKPair<ItemExpireType, Long> getMailExpireInfo(ResMailTemplate.MailTemplateConfData mailConf, int index) {
        ItemExpireType expireType = ItemExpireType.IET_RELATIVE;
        long expireTimeMs = 0L;
        if (mailConf.getAttachmentExpireCount() > index) {
            if (mailConf.getAttachmentExpire(index) > 0) {
                expireTimeMs = mailConf.getAttachmentExpire(index) * DateUtils.ONE_DAY_MILLIS;
            }
        } else if (mailConf.getAttachmentExpireTimestampCount() > index) {
            expireTimeMs = mailConf.getAttachmentExpireTimestamp(index).getSeconds() * DateUtils.ONE_SECOND_MILLIS;
            expireType = ItemExpireType.IET_ABSOLUTE;
        }
        return new NKPair<>(expireType, expireTimeMs);
    }

    public static boolean mailNotInTime(ResMailTemplate.MailTemplateConfData mailConf) {
        return !mailInTime(mailConf);
    }

    public static boolean mailInTime(ResMailTemplate.MailTemplateConfData mailConf) {
        if (!mailConf.hasTimeInfo()) {
            return true;
        }
        long now = DateUtils.currentTimeSec();
        if (now < mailConf.getTimeInfo().getBeginTime().getSeconds()) {
            return false;
        }
        if (!mailConf.getTimeInfo().hasEndTime()) {
            return true;
        }
        return now <= mailConf.getTimeInfo().getEndTime().getSeconds();
    }

    public static void fillMailExtraData(ResMailTemplate.MailTemplateConfData mailConf, MailExtraData.Builder mailDatabuilder) {
        if (mailConf.hasLowestVersion()) {
            mailDatabuilder.setLowestVersion(mailConf.getLowestVersion());
        }
        if (mailConf.hasHighestVersion()) {
            mailDatabuilder.setHighestVersion(mailConf.getHighestVersion());
        }
        if (!mailConf.getMailBuyItemList().isEmpty()) {
            mailDatabuilder.addAllMailBuyItem(mailConf.getMailBuyItemList());
        }
        if (mailConf.hasMailContentImg()) {
            mailDatabuilder.setMailContentImg(mailConf.getMailContentImg());
        }
        switch (mailConf.getMailAttachmentTypeContentCase()) {
            case UGCIDLIST: {
                List<Long> ugcIds = mailConf.getUgcIdList().getUgcIdListList();
                MailUgcData mailUgcData = getMailUgcUgcData(UgcTableBatchGetSource.UTBGS_SendPersonalTemplateMail.getNumber(), ugcIds);
                mailDatabuilder.setMailUgcData(mailUgcData);
                break;
            }
            default:
                break;
        }
    }

    public static long sendTemplateMail(long receiverUid, long senderId, int templateId, MailExtraParam extraParams,
            TlogSendReason reason, Object... formatArgs) {
        ResMailTemplate.MailTemplateConfData mailConf = MailTemplateConfData.getInstance().getMailTemplate(templateId);
        if (mailConf == null) {
            LOGGER.error("get mail conf error, mail template id:{}", templateId);
            return -1;
        }
        if (mailNotInTime(mailConf)) {
            LOGGER.error("send mail not in time. templateId:{} timeConf:{}", templateId, mailConf.getTimeInfo());
            return -1;
        }
        long expireUnixMs;
        if (mailConf.hasMailExpire() && mailConf.getMailExpire() > 0) {
            expireUnixMs = Framework.currentTimeMillis() + mailConf.getMailExpire() * 86400000L;
        } else {
            expireUnixMs = getMailDefaultExpireUnixMs(mailConf.getType());
        }

        MailAttachmentList.Builder attList = MailAttachmentList.newBuilder();
        for (int i = 0; i < mailConf.getAttachmentsList().size(); i++) {
            NKPair<ItemExpireType, Long> expireInfo = getMailExpireInfo(mailConf, i);
            MailAttachment.Builder mailAttachment = MailAttachment.newBuilder()
                    .setItemIfo(ItemInfo.newBuilder()
                            .setItemId(mailConf.getAttachments(i).getItemId())
                            .setItemNum(mailConf.getAttachments(i).getItemNum())
                            .setExpireTimeMs(expireInfo.getValue())
                            .setExpireType(expireInfo.getKey().getNumber())
                            .build());
            attList.addList(mailAttachment);
        }
        String txt = mailConf.getMailContent();
        if (formatArgs.length != 0) {
            txt = NKStringFormater.format(txt, formatArgs);
        }

        // 填充额外数据
        if (0 != templateId) {
            if (Objects.isNull(extraParams)) {
                extraParams = new MailExtraParam(MailExtraType.MET_MAIL_SYSTEM);
            }
            MailExtraData.Builder mailDataBuilder = Objects.isNull(extraParams.extraData) ? MailExtraData.newBuilder() : extraParams.extraData.toBuilder();
            fillMailExtraData(mailConf, mailDataBuilder);
            extraParams.extraData = mailDataBuilder.build();
        }

        TcaplusDb.PlayerMail mail = buildMail(receiverUid, mailConf.getMailTitle(), txt, mailConf.getType(),
                attList.build(), senderId, mailConf.getSender(), mailConf.getUrl(), 0, expireUnixMs,
                MailSourceType.MT_Source_Template_Xls, false, templateId, extraParams, mailConf.getIsStarred(), 0L);
        return sendMail(mail, reason);
    }

    public static long sendTemplateMailWithExtraData(long receiverUid, int templateId, long senderUid,
                                                     MailExtraData extraData, TlogSendReason reason, Object... formatArgs) {
        ResMailTemplate.MailTemplateConfData mailConf = MailTemplateConfData.getInstance().getMailTemplate(templateId);
        if (mailConf == null) {
            LOGGER.error("get mail conf error, mail template id:{}", templateId);
            return -1;
        }
        if (mailNotInTime(mailConf)) {
            LOGGER.error("send mail not in time. templateId:{} timeConf:{}", templateId, mailConf.getTimeInfo());
            return -1;
        }
        long expireUnixMs;
        if (mailConf.hasMailExpire() && mailConf.getMailExpire() > 0) {
            expireUnixMs = Framework.currentTimeMillis() + mailConf.getMailExpire() * 86400000L;
        } else {
            expireUnixMs = getMailDefaultExpireUnixMs(mailConf.getType());
        }

        MailAttachmentList.Builder attList = MailAttachmentList.newBuilder();
        for (int i = 0; i < mailConf.getAttachmentsList().size(); i++) {
            NKPair<ItemExpireType, Long> expireInfo = getMailExpireInfo(mailConf, i);
            MailAttachment.Builder mailAttachment = MailAttachment.newBuilder()
                    .setItemIfo(ItemInfo.newBuilder()
                            .setItemId(mailConf.getAttachments(i).getItemId())
                            .setItemNum(mailConf.getAttachments(i).getItemNum())
                            .setExpireTimeMs(expireInfo.getValue())
                            .setExpireType(expireInfo.getKey().getNumber())
                            .build());
            attList.addList(mailAttachment);
        }
        String txt = mailConf.getMailContent();
        if (formatArgs.length != 0) {
            txt = NKStringFormater.format(txt, formatArgs);
        }

        // 填充额外数据
        if (0 != templateId) {
            MailExtraData.Builder mailDataBuilder = Objects.isNull(extraData) ? MailExtraData.newBuilder() : extraData.toBuilder();
            fillMailExtraData(mailConf, mailDataBuilder);
            extraData = mailDataBuilder.build();
        }

        TcaplusDb.PlayerMail mail = buildMail(receiverUid, mailConf.getMailTitle(), txt, mailConf.getType(),
                attList.build(), senderUid, mailConf.getSender(), mailConf.getUrl(), 0, expireUnixMs,
                MailSourceType.MT_Source_Template_Xls, false, templateId, null, mailConf.getIsStarred(), 0L);
        return sendMail(mail.toBuilder().setExtraData(extraData).build(), reason);
    }

    public static long sendTemplateMail(long receiverUid, int templateId, TlogSendReason reason, Object... formatArgs) {
        return sendTemplateMail(receiverUid, 0L, templateId, null, reason, formatArgs);
    }

    public static long sendTemplateMail(long receiverUid, int templateId, MailAttachmentList.Builder attList,
                                        TlogSendReason reason, String... formatArgs) {
        return sendTemplateMail(receiverUid, templateId, attList, 0, null, reason, null, Arrays.asList(formatArgs));
    }

    public static long sendTemplateMail(long receiverUid, int templateId, MailAttachmentList.Builder attList, long senderUid,
            MailExtraParam extraParams, TlogSendReason reason, List<String> titleFmtArgs, List<String> contentFmtArgs) {
        return sendTemplateMail(receiverUid, templateId, attList, senderUid, extraParams, reason, titleFmtArgs,
                contentFmtArgs, 0);
    }

    public static long sendTemplateMail(long receiverUid, int templateId, MailAttachmentList.Builder attList, long senderUid,
            MailExtraParam extraParams, TlogSendReason reason, List<String> titleFmtArgs, List<String> contentFmtArgs, long sendUnixMs) {
        ResMailTemplate.MailTemplateConfData mailConf = MailTemplateConfData.getInstance().getMailTemplate(templateId);
        if (mailConf == null) {
            LOGGER.error("get mail conf error, mail template id:{}", templateId);
            return -1;
        }
        if (mailNotInTime(mailConf)) {
            LOGGER.error("send mail not in time. templateId:{} timeConf:{}", templateId, mailConf.getTimeInfo());
            return -1;
        }
        long expireUnixMs;
        if (mailConf.hasMailExpire() && mailConf.getMailExpire() > 0) {
            expireUnixMs = Framework.currentTimeMillis() + mailConf.getMailExpire() * 86400000L;
        } else if (mailConf.hasMailExpireTimestamp() && mailConf.getMailExpireTimestamp().getSeconds() > 0) {
            expireUnixMs = mailConf.getMailExpireTimestamp().getSeconds() * 1000L;
        } else {
            expireUnixMs = getMailDefaultExpireUnixMs(mailConf.getType());
        }
        String title = mailConf.getMailTitle();
        if (titleFmtArgs != null && titleFmtArgs.size() != 0) {
            title = NKStringFormater.format(title, titleFmtArgs.toArray());
        }
        String txt = mailConf.getMailContent();
        if (contentFmtArgs != null && contentFmtArgs.size() != 0) {
            txt = NKStringFormater.format(txt, contentFmtArgs.toArray());
        }

        // 填充额外数据
        if (0 != templateId) {
            if (Objects.isNull(extraParams)) {
                extraParams = new MailExtraParam(MailExtraType.MET_MAIL_SYSTEM);
            }
            MailExtraData.Builder mailDataBuilder = Objects.isNull(extraParams.extraData) ? MailExtraData.newBuilder() : extraParams.extraData.toBuilder();
            fillMailExtraData(mailConf, mailDataBuilder);
            extraParams.extraData = mailDataBuilder.build();
        }

        TcaplusDb.PlayerMail mail = buildMail(receiverUid, title, txt, mailConf.getType(),
                attList.build(), senderUid, mailConf.getSender(), mailConf.getUrl(), sendUnixMs, expireUnixMs,
                MailSourceType.MT_Source_Template_Xls, false, templateId, extraParams, mailConf.getIsStarred(), 0L);
        return sendMail(mail, reason);
    }

    public static long sendMailInSilence(long receiverUid, String title, String txt, MailType type,
                                         MailAttachmentList attList, long senderId, String senderName, String url,
                                         long sendUnixMs, long expireUnixMs, MailSourceType mailSourceType,
                                         boolean isOversea, int sourceCfgid, TlogSendReason reason, String amsSerial,
            MailExtraParam extraParams, boolean isStarred, long platSvrMailCfgId) {
        TcaplusDb.PlayerMail mail = buildMail(receiverUid, title, txt, type, attList, senderId, senderName, url,
                sendUnixMs, expireUnixMs, mailSourceType, isOversea, sourceCfgid, extraParams, isStarred, platSvrMailCfgId);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("receiverUid:{}, mail:{}", receiverUid, mail);
        }
        return sendMail2Db(mail, reason, amsSerial);
    }

    private static TcaplusDb.PlayerMail buildMail(long receiverUid, String title, String contentText, MailType type,
                                                  MailAttachmentList attList, long senderId, String senderName,
                                                  String url, long sendUnixMs, long expireUnixMs, MailSourceType source,
            boolean isOversea, int sourceCfgId, MailExtraParam extraParams, boolean isStarred, long platSvrMailCfgId) {
        if (sendUnixMs == 0) {
            sendUnixMs = Framework.currentTimeMillis();
        }
        if (expireUnixMs == 0) {
            expireUnixMs = getMailDefaultExpireUnixMs(type);
        }
        MailSource.Builder mailSourceBuilder = MailSource.newBuilder()
                .setSourceType(source.getNumber())
                .setIsOverSea(isOversea ? 1 : 0)
                .setCfgId(sourceCfgId)
                .setIsStarred(isStarred)
                .setPlatSvrMailCfgId(platSvrMailCfgId);

        MailContent.Builder urlBuilder = MailContent.newBuilder();
        if (extraParams != null) {
            if (extraParams.cdKey != null && !extraParams.cdKey.isEmpty()) {
                urlBuilder.setCdKey(extraParams.cdKey);
                mailSourceBuilder.setExtraType(MailExtraType.MET_CD_KEY);
            }

            // 如果发送方指定了extraType, 用发送方指定的
            if (extraParams.extraType != null && extraParams.extraType != MailExtraType.MET_Invalid) {
                mailSourceBuilder.setExtraType(extraParams.extraType);
            }
        }
        if (url != null && !url.isEmpty() && !url.isBlank()) {
            urlBuilder.setText(url);
        }

        TcaplusDb.PlayerMail.Builder mailBuilder = TcaplusDb.PlayerMail.newBuilder()
                .setTitle(title)
                .setId(getUniqueMailId(receiverUid))
                .setUid(receiverUid)
                .setSenderId(senderId)
                .setSendTime(sendUnixMs)
                .setExpireTime(expireUnixMs)
                .setSourceInfo(mailSourceBuilder)
                .setUrl(urlBuilder);

        if (extraParams != null && extraParams.extraData != null) {
            mailBuilder.setExtraData(extraParams.extraData);
        }

        // MailSourceType.MT_Source_Default || MT_Source_Template_Xls || sourceCfgId == 0
        // 按照之前的方式填充 邮件title\senderName\content
        if (source == MailSourceType.MT_Source_Default
                || source == MailSourceType.MT_Source_Template_Xls
                || sourceCfgId == 0) {
            mailBuilder.setContent(MailContent.newBuilder().setText(contentText == null ? "" : contentText))
                    .setSenderName(senderName);
        }

        if (attList != null && (attList.getListCount() != 0 || attList.hasShowOnlyAttachment())) {
            mailBuilder.setAttachments(attList);
        }

        mailBuilder.setStatus(genStatus(mailBuilder, type));
        return mailBuilder.build();
    }

    private static long sendMail(TcaplusDb.PlayerMail mail, TlogSendReason reason) {
        long ret = sendMail2Db(mail, reason, "");
        if (ret == -1) {
            return -1;
        }

        try {
            CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                PlayerInteractionData.Builder data = PlayerInteractionData.newBuilder()
                        .setInstruction(PlayerInteractionInstruction.PII_REMIND_NEW_MAIL)
                        .setRemindNewMailParams(PiiRemindNewMailParams.newBuilder()
                                .setMailId(mail.getId())
                                .setDbMail(mail.toByteString())
                                .build());
                PlayerInteractionInvoker.interact(mail.getUid(), data, mail.getSenderId());
                return null;
            }, "NewMailNotify", true);
        } catch (NKCheckedException ex) {
            LOGGER.error("NewMailNotify run job fail", ex);
        }
        return mail.getId();
    }

    private static long sendMail2Db(TcaplusDb.PlayerMail mail, TlogSendReason reason, String amsSerial) {
        // 写入邮件信息到db
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newInsertReq(mail.toBuilder()).send();
        Monitor.getInstance().add.total(MonitorId.mail_send_mail, 1);

        // 判断写入请求是否正常
        if (rsp.isOK()) {
            TlogFlowMgr.logMailFlow(mail, TlogOpType.NEW, reason, amsSerial);
            Monitor.getInstance().add.succ(MonitorId.mail_send_mail, 1);
            // 写入正常则返回邮件id
            return mail.getId();
        }

        Monitor.getInstance().add.fail(MonitorId.mail_send_mail, 1);
        LOGGER.error("MailInteraction::sendMail2Tcaplus err:{}", rsp.getResult());

        return -1;
    }

    public static long getMailDefaultExpireUnixMs(MailType type) {
        return Framework.currentTimeMillis() + MailIniConfData.getInstance().getMailDefaultLifeTime(type) * 86400000L;
    }

    public static long getUniqueMailId(long uid) {
        return NumericAttr.incr(uid, NumericAttr.AttrKey.CURR_MAIL_ID);
    }

    public static int genStatus(TcaplusDb.PlayerMail.Builder mailBuilder, MailType type) {
        int status;
        if (mailBuilder.hasAttachments() && mailBuilder.getAttachments().getListCount() != 0) {
            status = 0b10;
        } else {
            status = 0b00;
        }
        if (mailBuilder.hasUrl() && mailBuilder.getUrl().hasText()) {
            status |= 0b100;
        }

        return status | (((char) type.getNumber()) << 8);
    }

    public static int getTypeInt(long status) {
        return (int) ((status >>> 8) & 0xff);
    }

    public static enum TlogSendReason {
        unknown,
        gmCmd,
        idip,
        excel,
        clubKick,
        clubDissolve,
        qaInvest,
        friendSendGift,
        settleSeason,
        platShareInvite,
        bagGetItemNtfMail,
        platPrivilegesGift,
        activityTakeawayInviteeReward,
        permitUnreceivedRewards,
        mallDemand,
        mallGive,
        coCreatorMail,
        compReward, // 赛事奖励
        itemExpired, // 道具过期
        shareGiftCompensate, // 分享礼包自动补发
        redEnvelopeInGameReward, // 红包雨
        clubJoinApprove,    //加入社团审批通过
        clubJoinDenied,     //加入社团审批拒绝
        redPacketExpired,   // 红包过期
        redPacketAllReceived, // 红包已领完
        farmingReward, // 种田产出
        springBlessingCDKey,    // 春节集福CDKey
        clubBecomeOwner,    // 成为社团长
        clubBecomeManager,  // 成为社团管理员
        clubRemoveManager,  // 取消社团管理员
        fpsReturnItem,  // 撤离模式返还道具
        giveInterServerGift, // 赠送一元幸启
        amsSendNormal,
        amsSendCompensation,
        kungFuPandaRacingRank,  // 功夫熊猫竞速排行榜
        stickerActivityExpire,  // 贴纸簿活动过期
        rankSettlement,  // 榜单结算
        intimacyRecommend,  // 亲密关系推荐
        settlementSeason,  // 赛季重置邮件
        albumPicCheckFail, // 相册图片审核不通过
        recommendMatchType, // 限时推荐过期未领奖
        achievementReissueUgcBadge,  // 成就补发UGC徽章
        activityHYNResend,  // 半周年庆导航栏活动补发
        reputationScoreReport,  // 信誉分举报
        rewardCompensate, // 奖励补发
        reputationScoreChange,  // 信誉分变化
        kuromiTechouExpire,  // 酷洛米的手账本活动过期
        clubWeekSettle,  // 社团周结算
        clubChallengeJoinPlayerLimited, // 社团挑战贡献人数已达上限
        clubChallengeExpire, // 活动过期
        bpExpire, // BP过期
        onlineEarningExpire, // 网赚过期
        wolfKillReport, // 狼人杀举报
        wolfKillSubReputation, // 狼人杀扣分
        friendUseIntimacyItem, // 使用亲密度道具
        farmBuildingObtainMainExp, // 农场建筑提供主等级经验
        iaaCloudToApp,
        GroupingReturn,  // 团购返利活动结算
        raffleTestWater,
        WeekendIceBroken, // 周末破冰活动
        ActivityPrayerCard, // 祈福牌赠送
        LuckyRebateActivitySettlement, // 幸运返利结算
        FarmCollectionAwardFurniture, // 收藏品奖励家具
        UpgradeCheckInManualActivity, // 每周循环签到活动
        MonthCardExpiredRemind, // 月卡过期提醒
        FactionBattleReward,//阵营pk奖励
        TravelDogReward, // 旅行狗狗旅行奖励
        CocBattleEnd,                   // coc战斗结束
        CocVillagerFavorUpgrade,        // coc村民好感度升级
        CocTimeLimitWorkerHurtExpired,  // coc临时工人小屋到期提醒
        CocFriendVillagerRelationBreak, // 好友村民，好友关系解除
        TradingCardCycleReward, // 卡牌循环条周过期奖励
        BirthdayCardGive,               // 生日贺卡赠送
        ChaseIdentityBattleSettlement,               // 大王专精对局结算补发邮件
        ChaseOldPlayerUnlockIdentityMail,               // 老玩家一次性身份解锁邮件
        RichDayRewardMail,           // 大富翁玩法每日邮件奖励
        ChaseOldPlayerUnlockIdentityByDressMail,               // 老玩家通过皮肤一次性身份解锁邮件
        FarmDailyAward,     // 农场天天领
        ActivityInflateRedPacket, // 膨胀爆红包活动补发
        OneDollarRaffle, //一元抽奖活动周末礼包
        PcWebToPcSimulateLogin, // pcWeb转PC模拟器登录
        CloudToVa, // 云游转VA
        UgcMapRankReward, // ugc地图排行榜奖励
        CupsReissue, // 奖杯赐福宝箱补发
        FashionShare, // 时装分享好友

        UgcStarWorldBPCoinTipsMail,               // UGC星世界BP漫游币使用提醒邮件
        UgcMapSimpleActivityExpireReward, // Ugc活动奖励补发
        ChestWeeklyRankSettlement, // Chest周排行发奖
    }

    public static enum TlogOpType {
        INVALID(0),
        NEW(1),                 // 收到新邮件
        GET_ATTACHMENTS(2),     // 领取附件
        DEL(3),                 // 删除邮件
        READ(4),                // 标记已读
        GET_ATT_FAILED(5),      // 领取附件失败
        ;
        private final long value;

        TlogOpType(long value) {
            this.value = value;
        }

        public long getValue() {
            return value;
        }
    }

    public static enum TlogOpSubType {
        INVALID(0),
        OPERATE(1),         // 玩家主动操作
        QUICK_OPERATE(2),   // 一键操作
        EXPIRED(3),         // 过期
        OVERFLOW(4),        // 邮件超上限
        ;
        private final long value;

        TlogOpSubType(long value) {
            this.value = value;
        }

        public long getValue() {
            return value;
        }
    }

    public static class MailExtraParam {
        public MailExtraType extraType = MailExtraType.MET_Invalid;
        public String cdKey = "";
        public MailExtraData extraData = null;

        public MailExtraParam() { }

        public MailExtraParam(MailExtraType extraType) {
            this.extraType = extraType;
        }

        public MailExtraParam(MailExtraData extraData) {
            this.extraData = extraData;
        }

        public MailExtraParam(MailExtraType extraType, MailExtraData extraData) {
            this.extraType = extraType;
            this.extraData = extraData;
        }
    }

    public static String getIdipMailLowestVersionRainbow() {
        return PropertyFileReader.getRealTimeItem("idip_mail_lowest_version", /*"*********"*/"");
    }

    public static MailUgcData getMailUgcUgcData(int source/* UgcTableBatchGetSource */, List<Long> ugcIdList) {
        return getMailUgcUgcData(source, ugcIdList, true);
    }

    /** 从 ugcsvr 获取地图的简单信息填充到邮件内 */
    public static MailUgcData getMailUgcUgcData(int source/* UgcTableBatchGetSource */, List<Long> ugcIdList, boolean isTemplate) {
        MailUgcData.Builder mailUgcData = MailUgcData.newBuilder();
        int reqSize = ugcIdList.size();
        List<MailUgcMapData> ugcMapList = new ArrayList<>(reqSize);
        for (long ignored : ugcIdList) {
            ugcMapList.add(MailUgcMapData.newBuilder().build());
        }

        try {
            RpcGetUgcPublishMapReq.Builder rpcReq = RpcGetUgcPublishMapReq.newBuilder()
                    .addAllMapIdList(ugcIdList)
                    .setSource(source);
            RpcResult<Builder> rpcResult = UgcService.get().rpcGetUgcPublishMap(rpcReq);
            if (rpcResult.getRet() != 0) {
                LOGGER.error("get mail ugc error. source:{} ret:{} ugcIdList:{}", source, rpcResult.getRet(), ugcIdList);
                if (!isTemplate) {
                    NKErrorCode.forNumberOrUnknown(rpcResult.getRet()).throwError("get mail ugc error {}", rpcResult.getRet());
                }
                return mailUgcData.build();
            }
            if (rpcResult.getData().getResult() != 0) {
                LOGGER.error("get mail ugc error. source:{} result:{} ugcIdList:{}", source, rpcResult.getData().getResult(), ugcIdList);
                if (!isTemplate) {
                    NKErrorCode.forNumberOrUnknown(rpcResult.getRet()).throwError("get mail ugc error {}", rpcResult.getData().getResult());
                }
                return mailUgcData.build();
            }
            if (rpcResult.getData().getMapInfosList().isEmpty()) {
                LOGGER.error("get mail ugc error. map list empty. source:{}", source);
                if (!isTemplate) {
                    NKErrorCode.forNumberOrUnknown(rpcResult.getRet()).throwError("get mail ugc error. map list empty. {}", ugcIdList);
                }
                return mailUgcData.build();
            }

            Map<Long, Integer> ugcIdToIndex = new HashMap<>(reqSize);
            for (int i = 0; i < reqSize; ++i) {
                ugcIdToIndex.put(ugcIdList.get(i), i);
            }
            for (PublishItem mapInfo : rpcResult.getData().getMapInfosList()) {
                long ugcId = mapInfo.getUgcId();
                int templateId = mapInfo.getTemplateId();
                ResUGCEditor.Item_UGCEditorMapTemplate template = UGCEditorMapTemplate.getInstance().get(templateId);
                // 禁止兽人UGC
                if (Objects.nonNull(template) && template.getType() == UGCMapType.OMDGame) {
                    LOGGER.error("get mail ugc error. is omd ugc. templateId:{} ugcId:{} source:{} ugcIdList:{}", templateId, ugcId, source, ugcIdList);
                    if (!isTemplate) {
                        NKErrorCode.UgcMapError.throwError("get mail ugc error. is omd ugc {} {}", ugcId, ugcIdList);
                    }
                }

                int index = ugcIdToIndex.getOrDefault(ugcId, -1);
                if (-1 != index) {
                    MailUgcMapData mapData = MailUgcMapData.newBuilder()
                            .setUgcId(ugcId)
                            .addAllMetaInfo(mapInfo.getMetaInfoList())
                            .setBucket(mapInfo.getBucket())
                            .setName(mapInfo.getName())
                            .setTags(mapInfo.getTags()).build();
                    ugcMapList.set(index, mapData);
                    LOGGER.debug("get mail ugc result. source:{} ugcId:{} index:{}", source, ugcId, index);
                } else {
                    LOGGER.error("unexpected ugcId:{} found in response", ugcId);
                }
            }
        } catch (NKRuntimeException e) {
            throw e;
        } catch (Throwable e) {
            LOGGER.error("get mail ugc error. source:{} ugcIdList:{} ex:{}", source, ugcIdList, FunctionUtil.exceptionToString(e));
        }

        return mailUgcData
                .addAllMailUgcMapData(ugcMapList)
                .build();
    }
}


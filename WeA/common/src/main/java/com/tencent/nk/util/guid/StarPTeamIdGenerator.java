package com.tencent.nk.util.guid;

import com.tencent.nk.commonframework.Framework;

/**
 * <AUTHOR>
 */
public class StarPTeamIdGenerator extends BaseGenerator {

    private StarPTeamIdGenerator() {
        super(GuidType.GUID_TYPE_STARP_TEAM_ID, Framework.getInstance().getWorldId(), 1000, 100000);
    }

    public static StarPTeamIdGenerator getInstance() {
        return StarPTeamIdGenerator.InstanceHolder.instance;
    }

    private static class InstanceHolder {

        public static StarPTeamIdGenerator instance = new StarPTeamIdGenerator();
    }
}
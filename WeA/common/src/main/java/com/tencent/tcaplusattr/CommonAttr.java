package com.tencent.tcaplusattr;

import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.tcaplus.TcaplusManager;
import com.tencent.tcaplus.TcaplusUtil;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.xlsRes.keywords.Programmer;
import com.tencent.wechatrobot.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.ArrayList;

// <AUTHOR>
// 没有特殊的理由不直接调用
public class CommonAttr {
    private static final Logger LOGGER = LogManager.getLogger(CommonAttr.class);

    public static long get(long uuid, CommonAttrType type, long key, long timeKey) {
        return get(uuid, type.getValue(), key, timeKey);
    }

    public static long get(long uuid, CommonAttrType type, long key) {
        return get(uuid, type.getValue(), key, 0);
    }


    // key缺少类型限定, 不推荐直接调用此函数
    public static long get(long uuid, long type, long key, long timeKey) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setAttrKey(key)
                .setTimeKey(timeKey);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetReq(req).send();
        if (!rsp.isOK()) {
            if (rsp.getResult() == TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST) {
                LOGGER.debug("CommonAttr::get uuid:{}, type:{}, key:{}, timeKey:{} record not exist",
                        uuid, type, key, timeKey);
            } else {
                LOGGER.error("CommonAttr::get uuid:{}, type:{}, key:{}, timeKey:{} caught err:{}",
                        uuid, type, key, timeKey, rsp.getResult());
            }
            return 0;
        }
        TcaplusManager.TcaplusRecordData<?> data = rsp.firstRecordData();
        TcaplusDb.CommonNumericAttr attr = (TcaplusDb.CommonNumericAttr) data.msg;
        return attr.getAttrValue();
    }


    public static boolean set(long uuid, CommonAttrType type, long key, long timeKey, long value) {
        return set(uuid, type.getValue(), key, timeKey, value);
    }

    // key缺少类型限定, 不推荐直接调用此函数
    public static boolean set(long uuid, long type, long key, long timeKey, long value) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setAttrKey(key)
                .setTimeKey(timeKey)
                .setAttrValue(value);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newReplaceReq(req).send();
        if (rsp.isOK()) {
            return true;
        }
        LOGGER.error("CommonAttr::set uuid:{}, type:{}, key:{}, timeKey:{}, value:{} caught err:{}",
                uuid, type, key, timeKey, value, rsp.getResult());
        return false;
    }

    public static TcaplusErrorCode init(long uuid, CommonAttrType type, long key, long timeKey, long value) {
        return init(uuid, type.getValue(), key, timeKey, value);
    }

    // key缺少类型限定, 不推荐直接调用此函数
    public static TcaplusErrorCode init(long uuid, long type, long key, long timeKey, long value) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setAttrKey(key)
                .setTimeKey(timeKey)
                .setAttrValue(value);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newInsertReq(req).send();
        if (rsp.isOK()) {
            return TcaplusErrorCode.GEN_ERR_SUC;
        } else {
            LOGGER.error("CommonAttr::init uuid:{}, type:{}, key:{}, timeKey:{}, value:{} caught err:{},",
                    uuid, type, key, timeKey, value, rsp.getResult());
            return rsp.getResult();
        }
    }

    // 若记录不存在, 则置为inc
    public static long incr(long uuid, CommonAttrType type, long key, long timeKey, long inc) {
        return incr(uuid, type.getValue(), key, timeKey, inc);
    }

    // 若记录不存在, 则置为inc
    public static long incr(long uuid, long type, long key, long timeKey, long inc) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setAttrKey(key)
                .setTimeKey(timeKey)
                .setAttrValue(inc);
        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newIncreaseReq(req).send();
        if (rsp.isOK()) {
            if (rsp.hasFirstRecord()) {
                return ((TcaplusDb.CommonNumericAttr) rsp.firstRecordData().msg).getAttrValue();
            }
        } else {
            if (rsp.getResult() == TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST) {
                TcaplusErrorCode ret = init(uuid, type, key, timeKey, inc);
                if (ret == TcaplusErrorCode.GEN_ERR_SUC) {
                    return inc;
                }
                if (ret == TcaplusErrorCode.SVR_ERR_FAIL_RECORD_EXIST) {
                    return incr(uuid, type, key, timeKey, inc);
                }
            }
        }
        LOGGER.error("CommonAttr::incr uuid:{}, type:{}, key:{}, timeKey:{}, inc:{} caught err:{}",
                uuid, type, key, timeKey, inc, rsp.getResult());
        WechatLog.debugPanicWithNoticer(Programmer.wenhuazheng,
                "CommonAttr::incr return -1, uuid:{}, type:{}, key:{}, timeKey:{}, inc:{}, err:{}",
                uuid, type, key, timeKey, inc, rsp.getResult());
        return -1;
    }

    @Nullable
    public static ArrayList<TcaplusDb.CommonNumericAttr> getByType(long uuid, long type, long timeKey) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setTimeKey(timeKey);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newGetByPartKeyReq(req).send();
        if (!rsp.isOKIgnoreRecordNotExist()) {
            LOGGER.error("CommonAttr::getByType met err uid:{}, type:{}, timeKey:{} tcaplus err:{}",
                    uuid, type, timeKey, rsp.getResult());
            return null;
        }

        ArrayList<TcaplusDb.CommonNumericAttr> recordList = new ArrayList<>();
        for (TcaplusManager.TcaplusRecordGroup dataGroup : rsp.getRspDataList()) {
            for (TcaplusManager.TcaplusRecordData<?> data : dataGroup.getRecordList()) {
                TcaplusDb.CommonNumericAttr attr = (TcaplusDb.CommonNumericAttr) data.msg;
                recordList.add(attr);
            }
        }
        return recordList;
    }

    public static boolean delByType(long uuid, long type, long timeKey) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setTimeKey(timeKey);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newDeleteByPartKey(req).send();
        if (!rsp.isOK()) {
            LOGGER.error("CommonAttr::delByType met err uid:{}, type:{}, timeKey:{} tcaplus err:{}",
                    uuid, type, timeKey, rsp.getResult());
            return false;
        }

        return true;
    }

    public static boolean del(long uuid, CommonAttrType type, long key, long timeKey) {
        return del(uuid, type.getValue(), key, timeKey);
    }

    public static boolean del(long uuid, long type, long key, long timeKey) {
        TcaplusDb.CommonNumericAttr.Builder req = TcaplusDb.CommonNumericAttr.newBuilder()
                .setUuid(uuid)
                .setAttrType(type)
                .setAttrKey(key)
                .setTimeKey(timeKey);

        TcaplusManager.TcaplusRsp rsp = TcaplusUtil.newDeleteReq(req).send();
        if (!rsp.isOK()) {
            LOGGER.error("CommonAttr::del uuid:{}, type:{}, key:{}, timeKey:{} caught err:{}",
                    uuid, type, key, timeKey, rsp.getResult());
            return false;
        }
        return true;
    }


    public static long incr(long uuid, CommonAttrType type, long key, long timeKey) {
        return incr(uuid, type, key, timeKey, 1);
    }

    public static long incr(long uuid, CommonAttrType type, long key) {
        return incr(uuid, type, key, 0, 1);
    }

    // 具体类型代表的含义@wenhuazheng
    public enum CommonAttrType {
        NUMERIC_ATTR(1),            // 公共数字属性
        FLASH_ATTR(2),              // 公共定期属性
        SERVER_ATTR(3),             // 服务器属性
        PERSONAL_ATTR(4),           // 私人数字属性
        UGC_SAVEWORK_MAP_RECORD(8),     // UGC保存地图
        UGC_GIVELIKE_MAP_RECORD(9),     // UGC点赞地图
        UGC_SUB_EDITOR_RECORD(10),     // UGC订阅
        COMMON_GUIDE(11),
        UGC_PASS_LEVEL_RECORD(12),     // UGC地图最佳记录
        UGC_SHARE_EDITOR_RECORD(13),     // UGC分享
        UGC_PLAY_MAP_RECORD(14),     // UGC地图游玩

        RELATED_ATTR_START(100000), // 关联数字属性
        RELATED_ATTR_END(199999),   // 关联数字属性
        ;

        private final int value;

        CommonAttrType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}

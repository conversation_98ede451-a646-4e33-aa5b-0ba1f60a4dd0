package com.tencent.rank.console;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.tencent.cache.Cache.CacheResult;
import com.tencent.cache.CacheErrorCode;
import com.tencent.cache.CacheUtil;
import com.tencent.coLoadingCache.CoLoadingCache;
import com.tencent.coLoadingCache.CoLoadingCache.LockKeyBuilder;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.IEnumedException;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKTimeoutException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.random.util.RandomInRange;
import com.tencent.rank.cacheclient.RankListCacheClient;
import com.tencent.rank.cacheclient.RankListCacheClient.RankListWithTs;
import com.tencent.rank.utils.RankAttrUtils;
import com.tencent.rank.utils.RankIdApolloIdMapper;
import com.tencent.rank.utils.RankIdBackendMapper;
import com.tencent.rank.utils.RankIdSeasonIdMapper;
import com.tencent.rank.utils.RankQualifyScoreUtils;
import com.tencent.rank.utils.RankSettlementUtils;
import com.tencent.rank.utils.RankZSetScoreUtils;
import com.tencent.resourceloader.resclass.QDTSeasonCfgData;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.RankingRuleConfData;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplus.dao.PlayerPublicDao;
import com.tencent.tcaplus.dao.PlayerPublicDao.PlayerPublicAttrKey;
import com.tencent.tcaplus.dao.PlayerTableDao;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.coroutine.CoroHandle;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.protocol.AttrRankInfoItem.proto_RankInfoItem;
import com.tencent.wea.protocol.AttrRankInfoReportStatus.proto_RankInfoReportStatus;
import com.tencent.wea.protocol.SsRanksvr.RpcRemoveOneUserTopRankReq;
import com.tencent.wea.protocol.SsRanksvr.RpcRemoveOneUserTopRankRes;
import com.tencent.wea.protocol.SsRanksvr.RpcRemoveTopRankReq;
import com.tencent.wea.protocol.SsRanksvr.RpcRemoveTopRankRes;
import com.tencent.wea.protocol.SsRanksvr.RpcRemoveTopRankRes.Builder;
import com.tencent.wea.protocol.SsRanksvr.RpcTopRankFetchByScoreReq;
import com.tencent.wea.protocol.SsRanksvr.RpcTopRankRedisRefreshAndFetchReq;
import com.tencent.wea.protocol.SsRanksvr.RpcTopRankRedisRefreshAndFetchRes;
import com.tencent.wea.protocol.SsRanksvr.TopRankCoordinationType;
import com.tencent.wea.protocol.SsUgcplatsvr.EventType;
import com.tencent.wea.protocol.SsUgcplatsvr.RpcCreatorReportReq;
import com.tencent.wea.protocol.SsUgcplatsvr.RpcCreatorReportRes;
import com.tencent.wea.protocol.common.GeoLevel;
import com.tencent.wea.protocol.common.KeyValueInt32;
import com.tencent.wea.protocol.common.RankId;
import com.tencent.wea.protocol.common.RankSeasonInfo;
import com.tencent.wea.protocol.common.RankType;
import com.tencent.wea.namedenum.servertype.WeAServerType;
import com.tencent.wea.protocol.common.TopRankBackendType;
import com.tencent.wea.protocol.common.TopRankInfo;
import com.tencent.wea.protocol.common.TopRankJointInfo;
import com.tencent.wea.rpc.service.RankService;
import com.tencent.wea.rpc.service.UgcplatService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.Player;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerOrBuilder;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerPublic;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerPublicOrBuilder;
import com.tencent.wea.xlsRes.ResRanking.RankingConf;
import com.tencent.wea.xlsRes.ResRanking.RankingRuleConf;
import com.tencent.wea.xlsRes.keywords.RankOrderingType;
import com.tencent.wea.xlsRes.keywords.RankReportStatus;
import com.tencent.wea.xlsRes.keywords.RankRule;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import io.lettuce.core.KeyValue;
import io.lettuce.core.ScoredValue;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class BackendConsole {

    private static final Logger LOGGER = LogManager.getLogger(BackendConsole.class);
    private final CoLoadingCache<NKPair<Integer, Long>, Integer> cachedZSetRank;
    private final CoLoadingCache<String, Integer> cachedSettlementSize;
    private final CoLoadingCache<String, Integer> cachedBatchSettlementSize;
    private final CoLoadingCache<String, Boolean> cachedPlatDataSynced;
    private final CoLoadingCache<String, Integer> cachedDailyZSetSettlementSize;
    private final Map<TopRankBackendType, ConsoleWriter> writers;
    private final Map<TopRankBackendType, ConsoleReader> readers;

    private BackendConsole() {
        int capacity = PropertyFileReader.getIntItem("rank_list_zset_rankno_size", 10000);
        long expire = PropertyFileReader.getIntItem("rank_list_zset_rankno_expire", 60000);
        cachedZSetRank = new CoLoadingCache.Builder<NKPair<Integer, Long>, Integer>().setCapacity(capacity)
                .setLockKeyBuilder(new LockKeyBuilder<>("rank_list/zset_rank", NKPair::toString))
                .expireAfterWrite(expire).setLoader(kv -> getZSetRankNoFromRedis(kv.key, kv.value)).build();

//        capacity = PropertyFileReader.getIntItem("rank_list_settlement_rankno_size", 10000);
//        expire = PropertyFileReader.getIntItem("rank_list_settlement_rankno_expire", 60000);
//        cachedSettlementRank = new CoLoadingCache.Builder<NKPair<String, Long>, Integer>().setCapacity(capacity)
//                .expireAfterWrite(expire).setLoader(kv -> getSnapshotRankNoFromRedis(kv.key, kv.value)).build();

        capacity = PropertyFileReader.getIntItem("rank_list_settlement_size", 10);
        expire = PropertyFileReader.getIntItem("rank_list_settlement_expire", 60000);
        cachedSettlementSize = new CoLoadingCache.Builder<String, Integer>().setCapacity(capacity)
                .setLockKeyBuilder(new LockKeyBuilder<>("rank_snap/normal", s -> s))
                .expireAfterWrite(expire).setLoader(this::getSnapshotRankSizeFromRedis).build();
        cachedBatchSettlementSize = new CoLoadingCache.Builder<String, Integer>().setCapacity(capacity)
                .setLockKeyBuilder(new LockKeyBuilder<>("rank_snap/batch", s -> s))
                .expireAfterWrite(expire).setLoader(this::getBatchSnapshotRankSizeFromRedis).build();
        cachedPlatDataSynced = new CoLoadingCache.Builder<String, Boolean>().setCapacity(capacity)
                .setLockKeyBuilder(new LockKeyBuilder<>("rank_snap/sync", s -> s))
                .expireAfterWrite(expire).setLoader(this::getPlatDataSyncedFromRedis).build();
        cachedDailyZSetSettlementSize = new CoLoadingCache.Builder<String, Integer>().setCapacity(capacity)
                .setLockKeyBuilder(new LockKeyBuilder<>("rank_snap/daily", s -> s))
                .expireAfterWrite(expire).setLoader(this::getDailyZSetSnapshotRankSizeFromRedis).build();

        writers = new HashMap<>();
        readers = new HashMap<>();

        var topN = new RankSvrConsoleReaderWriter(TopRankBackendType.TRBT_Apollo_Trank);
        var topNext = new RankSvrConsoleReaderWriter(TopRankBackendType.TRBT_Apollo_TopNext);
        var plat = new RankSvrConsoleReaderWriter(TopRankBackendType.TRBT_Plat);
        var zset = new RedisZSetConsoleReaderWriter(topN);

        writers.put(TopRankBackendType.TRBT_Apollo_Trank, topN);
        readers.put(TopRankBackendType.TRBT_Apollo_Trank, topN);

        writers.put(TopRankBackendType.TRBT_Apollo_TopNext, topNext);
        readers.put(TopRankBackendType.TRBT_Apollo_TopNext, topNext);

        writers.put(TopRankBackendType.TRBT_Plat, plat);
        readers.put(TopRankBackendType.TRBT_Plat, plat);

        writers.put(TopRankBackendType.TRBT_Redis_ZSet, zset);
        readers.put(TopRankBackendType.TRBT_Redis_ZSet, zset);
    }

    public static BackendConsole getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private NKErrorCode insertFakeQualifyRankScores(int rankId, List<NKPair<Integer, Integer>> subIds,
            List<NKPair<Long, Integer>> integrals) {
        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("unknown rank id, id:{}", rankId);
            return NKErrorCode.ResNotFound;
        }

        if (rankingConf.getRule() != RankRule.RR_Qualify && rankingConf.getRule() != RankRule.RR_Qualify_Challenger) {
            LOGGER.error("rank id not qualify type, id:{}", rankId);
            return NKErrorCode.InvalidParams;
        }

        long currentMs = DateUtils.currentTimeMillis();
        RankSeasonInfo seasonConf = RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000);
        if (seasonConf == null) {
            LOGGER.error("season conf not found, rank:{}", rankId);
            return NKErrorCode.ResNotFound;
        }

        var qConf = QDTSeasonCfgData.getInstance().get(seasonConf.getId());
        if (qConf == null) {
            LOGGER.error("qualify conf not found, rank:{}", rankId);
            return NKErrorCode.ResNotFound;
        }

        boolean mustChallenger = rankingConf.getRule() == RankRule.RR_Qualify_Challenger;

        List<NKPair<Long, Integer>> qualifyScores = Lists.newArrayList();
        List<NKPair<Long, Integer>> challengerScores = Lists.newArrayList();

        for (var kv : integrals) {
            long uid = kv.key;
            int integral = kv.value;

            int score = RankQualifyScoreUtils.ofIntegralScore(seasonConf.getId(), integral, mustChallenger);
            if (score == 0) {
                LOGGER.error("qualify score cannot found");
                continue;
            }

            qualifyScores.add(new NKPair<>(uid, score));
            if (RankQualifyScoreUtils.isChallenger(seasonConf.getId(), score)) {
                challengerScores.add(new NKPair<>(uid, RankQualifyScoreUtils.reformatChallengerScore(integral)));
            }
        }

        int mode = rankingConf.getDescId();
        rankingConf = RankingConfData.getInstance().getByDescId(RankRule.RR_Qualify, mode);
        if (rankingConf != null) {
            int apolloId = RankIdApolloIdMapper.getApolloId(rankingConf.getRankId(),
                    TconndApiAccount.TCONND_ITOP_CHANNEL_WX, seasonConf);

            Map<Long, NKErrorCode> res = BackendConsole.getInstance()
                    .batchWriteScoreToApolloRankList(rankId, seasonConf.getId(), qualifyScores, currentMs,
                            Collections.emptyList(),
                            RankIdBackendMapper.forWrite(rankId, RankType.RT_Global));
            List<Long> errorList = res.entrySet().stream().filter(kv -> kv.getValue().hasError()).map(Entry::getKey)
                    .collect(Collectors.toList());

            LOGGER.info("write fake scores to apollo global rank list, apolloId:{} errorList:{}", apolloId, errorList);

            if (!subIds.isEmpty()) {
                res = BackendConsole.getInstance()
                        .batchWriteScoreToApolloRankList(rankId, seasonConf.getId(), qualifyScores,
                                currentMs, subIds, RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo));
                errorList = res.entrySet().stream().filter(kv -> kv.getValue().hasError()).map(Entry::getKey)
                        .collect(Collectors.toList());
                LOGGER.info("write fake scores to apollo geo rank list, apolloId:{} errorList:{}", apolloId, errorList);
            }
        }

        rankingConf = RankingConfData.getInstance().getByDescId(RankRule.RR_Qualify_Challenger, mode);
        if (rankingConf != null) {
            int apolloId = RankIdApolloIdMapper.getApolloId(rankingConf.getRankId(),
                    TconndApiAccount.TCONND_ITOP_CHANNEL_WX, seasonConf);

            RankListCacheClient.WriterClient zsetWriter = RankListCacheClient.getZSetWriter();
            RankId rank = RankId.newBuilder().setId(rankId).setApolloId(apolloId).build();
            NKErrorCode errorCode = zsetWriter.refresh(rank, TopRankBackendType.TRBT_Redis_ZSet, false,
                    challengerScores.stream().map(kv -> TopRankInfo.newBuilder().setUid(kv.key).addScore(kv.value)
                            .setLastUpdateTs(currentMs).build()).collect(Collectors.toList()), currentMs, 0);

            if (!errorCode.isOk()) {
                LOGGER.error("failed to write to zset, apolloId:{} err:{}", apolloId, errorCode);
            }

            BackendConsole.getInstance()
                    .batchWriteScoreToApolloRankList(rankId, seasonConf.getId(), challengerScores,
                            currentMs, Collections.emptyList(),
                            RankIdBackendMapper.forWrite(rankId, RankType.RT_Global));
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode insertFakeRankScores(int rankId, TconndApiAccount account, int num, int min, int max,
            List<NKPair<Integer, Integer>> subIds) {
        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("unknown rank id, id:{}", rankId);
            return NKErrorCode.ResNotFound;
        }

        if (rankingConf.getRule() == RankRule.RR_Ugc_Exp) {
            return NKErrorCode.Unsupported;
        }

        long currentMs = DateUtils.currentTimeMillis();
        RankSeasonInfo seasonConf = RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000);
        if (seasonConf == null) {
            LOGGER.error("season conf not found");
            return NKErrorCode.ResNotFound;
        }

        RandomInRange rnd = new RandomInRange();
        List<NKPair<Long, Integer>> rankList = IntStream.range(0, num)
                .mapToObj(i -> new NKPair<>(currentMs + i, rnd.rand(min, max))).collect(Collectors.toList());

        // if involving qualify, check if challenger
        if (rankingConf.getRule() == RankRule.RR_Qualify || rankingConf.getRule() == RankRule.RR_Qualify_Challenger) {
            return insertFakeQualifyRankScores(rankId, subIds, rankList);
        }

        // if involving level, sec -> ms
        if (rankingConf.getLevelId() != 0) {
            rankList = rankList.stream().map(rankEntry -> new NKPair<>(rankEntry.key, rankEntry.value * 1000))
                    .filter(kv -> kv.value > 0).collect(Collectors.toList());
            LOGGER.warn("sec auto map to ms, size={}/{} list={}", num, rankList.size(), rankList);
        }

        int apolloId = RankIdApolloIdMapper.getApolloId(rankId, account, seasonConf);

        Map<Long, NKErrorCode> res = BackendConsole.getInstance()
                .batchWriteScoreToApolloRankList(rankId, seasonConf.getId(), rankList, currentMs,
                        Collections.emptyList(),
                        RankIdBackendMapper.forWrite(rankId, RankType.RT_Global));
        LOGGER.info("write fake scores to apollo global rank list, apolloId:{} result:{}", apolloId, res);

        if (!subIds.isEmpty()) {
            res = BackendConsole.getInstance().batchWriteScoreToApolloRankList(rankId, seasonConf.getId(), rankList,
                    currentMs, subIds, RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo));
            LOGGER.info("write fake scores to apollo geo rank list, apolloId:{} result:{}", apolloId, res);
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode updateOneUserGlobalScore(int rankId, int seasonId, long uid, List<Integer> scores,
            long updateTs, boolean force, boolean async) {
        var type = RankIdBackendMapper.forWrite(rankId, RankType.RT_Global);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown write backend, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.RankBackendNotFound;
        }

        ConsoleWriter writer = writers.getOrDefault(type, ConsoleWriter.IGNORE);
        if (async) {
            try {
                CurrentExecutorUtil.runJob(
                        () -> writer.updateOneUserScore(rankId, seasonId, uid, Lists.newArrayList(), scores, updateTs,
                                force),
                        "update_global_console", true);
            } catch (NKCheckedException e) {
                LOGGER.error("failed to update global score, uid:{} rank:{}", uid, rankId);
                return e.getEnumErrCode();
            }
        }

        return writer.updateOneUserScore(rankId, seasonId, uid, Lists.newArrayList(), scores, updateTs, force);
    }

    public NKErrorCode updateOneUserGeoScore(int rankId, int seasonId, Map<Integer, Integer> geoIds, long uid,
            List<Integer> scores, long updateTs, boolean force, boolean async) {
        if (geoIds.isEmpty()) {
            LOGGER.error("geo id is empty, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.InvalidParams;
        }

        List<NKPair<Integer, Integer>> subIds = Lists.newArrayList();
        geoIds.forEach((k, v) -> subIds.add(new NKPair<>(k, v)));

        var type = RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown write backend, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.RankBackendNotFound;
        }

        ConsoleWriter writer = writers.getOrDefault(type, ConsoleWriter.IGNORE);
        if (async) {
            try {
                CurrentExecutorUtil.runJob(
                        () -> writer.updateOneUserScore(rankId, seasonId, uid, subIds, scores, updateTs, force),
                        "update_geo_console", true);
            } catch (NKCheckedException e) {
                LOGGER.error("failed to update geo score, uid:{} rank:{}", uid, rankId);
                return e.getEnumErrCode();
            }
        }

        return writer.updateOneUserScore(rankId, seasonId, uid, subIds, scores, updateTs, force);
    }

    public NKErrorCode removeOneUserGlobalAndGeoScore(int rankId, int seasonId, Map<Integer, Integer> geoIds, long uid,
            boolean async) {

        List<Callable<NKErrorCode>> tasks = Lists.newArrayList();

        var type = RankIdBackendMapper.forWrite(rankId, RankType.RT_Global);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown global write backend, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.RankBackendNotFound;
        }

        ConsoleWriter globalWriter = writers.getOrDefault(type, ConsoleWriter.IGNORE);
        tasks.add(() -> globalWriter.removeOneUserScore(rankId, seasonId, uid, Lists.newArrayList()));

        List<NKPair<Integer, Integer>> subIds = Lists.newArrayList();
        geoIds.forEach((k, v) -> subIds.add(new NKPair<>(k, v)));

        if (!subIds.isEmpty()) {
            type = RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo);
            ConsoleWriter geoWriter = writers.getOrDefault(type, ConsoleWriter.IGNORE);
            if (geoWriter != ConsoleWriter.IGNORE) {
                tasks.add(() -> geoWriter.removeOneUserScore(rankId, seasonId, uid, subIds));
            }
        }

        try {
            var results = CurrentExecutorUtil.batchSubmitJob(tasks, "remove_global_geo_console", true);
            if (async) {
                return NKErrorCode.OK;
            }

            NKErrorCode res = NKErrorCode.OK;
            for (var result : results) {
                try {
                    NKErrorCode errorCode = result.get(1000);
                    if (errorCode.hasError()) {
                        LOGGER.error("failed to remove one user score, uid:{} rank:{} err:{}", uid, rankId, errorCode);
                        res = errorCode;
                    }

                } catch (TimeoutException e) {
                    LOGGER.error("timeout to remove one user score, uid:{} rank:{}", uid, rankId);
                }
            }

            return res;
        } catch (NKCheckedException e) {
            LOGGER.error("failed to remove one user score, uid:{} rank:{} err:{}", uid, rankId, e);
            return e.getEnumErrCode();
        }
    }

    public NKErrorCode migrateOneUserGeoScore(int rankId, int seasonId, Map<Integer, Integer> fromIds,
            Map<Integer, Integer> toIds, long uid, List<Integer> scores, long updateTs, boolean force, boolean async) {
        if (fromIds.isEmpty() && toIds.isEmpty()) {
            LOGGER.error("geo id is empty, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.InvalidParams;
        }

        List<NKPair<Integer, Integer>> fromSubIds = Lists.newArrayList();
        fromIds.forEach((k, v) -> fromSubIds.add(new NKPair<>(k, v)));

        List<NKPair<Integer, Integer>> toSubIds = Lists.newArrayList();
        toIds.forEach((k, v) -> toSubIds.add(new NKPair<>(k, v)));

        var type = RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown write backend, uid:{} rankId:{}", uid, rankId);
            return NKErrorCode.RankBackendNotFound;
        }

        ConsoleWriter writer = writers.getOrDefault(type, ConsoleWriter.IGNORE);
        if (async) {
            try {
                CurrentExecutorUtil.runJob(
                        () -> writer.migrateOneUserScore(rankId, seasonId, uid, fromSubIds, toSubIds, scores, updateTs,
                                force),
                        "migrate_geo_console", true);
            } catch (NKCheckedException e) {
                LOGGER.error("failed to migrate geo score, uid:{} rank:{}", uid, rankId);
                return e.getEnumErrCode();
            }
        }

        return writer.migrateOneUserScore(rankId, seasonId, uid, fromSubIds, toSubIds, scores, updateTs, force);
    }

    public NKPair<Optional<TopRankInfo>, NKErrorCode> getOneUserGlobalScore(int rankId, int seasonId, long uid) {
        var type = RankIdBackendMapper.forRead(rankId, RankType.RT_Global);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown read backend, uid:{} rankId:{}", uid, rankId);
            return new NKPair<>(Optional.empty(), NKErrorCode.RankBackendNotFound);
        }

        ConsoleReader reader = readers.getOrDefault(type, ConsoleReader.NOWHERE);
        return reader.getOneUser(RankId.newBuilder().setId(rankId), seasonId, uid);
    }

    public NKPair<Optional<TopRankInfo>, NKErrorCode> getOneUserGeoScore(int rankId, NKPair<Integer, Integer> subId,
            int seasonId, long uid) {

        if (subId == null || subId.getKey() == 0) {
            LOGGER.error("unknown subId, uid:{} rankId:{} sub:{}", uid, rankId, subId);
            return new NKPair<>(Optional.empty(), NKErrorCode.InvalidParams);
        }

        var type = RankIdBackendMapper.forRead(rankId, RankType.RT_Geo);
        if (type == TopRankBackendType.TRBT_Unknown) {
            LOGGER.error("unknown read backend, uid:{} rankId:{}", uid, rankId);
            return new NKPair<>(Optional.empty(), NKErrorCode.RankBackendNotFound);
        }

        ConsoleReader reader = readers.getOrDefault(type, ConsoleReader.NOWHERE);
        return reader.getOneUser(RankId.newBuilder().setId(rankId).setSubType(subId.key).setSubId(subId.value),
                seasonId, uid);
    }

    private NKErrorCode removeApolloInternal(int apolloId) {
        try {
            var conf = RankingConfData.getInstance().getByApolloId(apolloId);
            if (conf == null) {
                LOGGER.error("conf not found, apollo:{}", apolloId);
                return NKErrorCode.ResNotFound;
            }

            List<TopRankBackendType> bks = Lists.newArrayList(RankIdBackendMapper.forRemove(conf.getRankId()));
            if (bks.isEmpty()) {
                LOGGER.debug("nothing needs to be done, apollo:{}", apolloId);
                return NKErrorCode.OK;
            }

            RpcResult<RpcRemoveTopRankRes.Builder> result = RankService.get().rpcRemoveTopRank(
                    RpcRemoveTopRankReq.newBuilder().addAllBackend(bks).setApolloId(apolloId));

            if (!result.isOK()) {
                LOGGER.error("error occur during remove {}: {}", apolloId, result.getRet());
                return NKErrorCode.forNumber(result.getRet());
            }

            NKErrorCode errorCode = NKErrorCode.forNumber(result.getData().getResult());
            if (!errorCode.isOk()) {
                LOGGER.error("error occur during remove {}: {}", apolloId, errorCode);
                return errorCode;
            }

            LOGGER.warn("remove rank {} successfully", apolloId);
            return NKErrorCode.OK;
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("error occur during remove {}: {}", apolloId, e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    public Map<Integer, NKErrorCode> remove(List<Integer> apolloIds, MonitorId metric) {
        Map<Integer, Callable<NKErrorCode>> jobs = new HashMap<>();
        apolloIds.forEach(apolloId -> jobs.put(apolloId, () -> removeApolloInternal(apolloId)));

        try {
            Map<Integer, NKErrorCode> res = new HashMap<>();

            CurrentExecutorUtil.batchSubmitJob(jobs, "batchRemoveRankIds", true).forEach((k, v) -> {
                try {
                    NKErrorCode errorCode = v.get(1000);
                    if (!errorCode.isOk()) {
                        LOGGER.error("failed to remove apolloId {}: {}", k, errorCode);
                        Monitor.getInstance().add.fail(metric, 1);
                        res.put(k, errorCode);
                        return;
                    }

                    LOGGER.warn("remove apolloId {}", k);
                    Monitor.getInstance().add.succ(metric, 1);
                    res.put(k, NKErrorCode.OK);
                } catch (TimeoutException e) {
                    LOGGER.error("remove apolloId {} timeout", k);
                    Monitor.getInstance().add.timeout(metric, 1);
                    res.put(k, NKErrorCode.RankDeleteFailure);
                }
            });

            return res;
        } catch (NKCheckedException e) {
            LOGGER.error("failed to batch remove apollo ids: {}", e.getEnumErrCode());
            return Collections.emptyMap();
        }
    }

    public NKErrorCode removeOneUserAll(PlayerOrBuilder player, PlayerPublicOrBuilder playerPublic) {
        if (player == null || playerPublic == null) {
            LOGGER.error("must not be null, player_is_null:{} playerPublic_is_null:{}", player == null,
                    playerPublic == null);
            return NKErrorCode.InvalidParams;
        }

        if (player.getUid() != playerPublic.getUid()) {
            LOGGER.error("uid conflict, player:{} public:{}", player.getUid(), playerPublic.getUid());
            return NKErrorCode.InvalidParams;
        }

        long uid = player.getUid();
        String openId = player.getOpenid();
        long creatorId = playerPublic.getPublicProfile().getCreatorId();
        int platId = player.getPlatid();
        TconndApiAccount loginType = playerPublic.getPublicBasicInfo().getAccountType();
        List<Integer> apolloIds = playerPublic.getPublicGameData().getRankInfoList().stream()
                .map(proto_RankInfoItem::getType)
                .map(rankId -> RankIdApolloIdMapper.getApolloId(rankId, player))
                .filter(apolloId -> apolloId != 0).collect(Collectors.toList());

        List<NKPair<Integer, Integer>> subIds = getPlayerAllInvolvingSubRankIds(player);
        LOGGER.warn("prepare to remove one user all rank score, uid:{} openId:{} creatorId:{} plat:{} apollo:{} sub:{}",
                uid, openId, creatorId, platId, apolloIds, subIds);

        return removeOneUserAll(uid, openId, creatorId, platId, loginType, apolloIds, subIds);
    }

    public NKErrorCode removeOneUserAll(long uid, String openId, long creatorId, int platId, TconndApiAccount loginType,
            List<Integer> apolloIds, List<NKPair<Integer, Integer>> subIds) {

        List<Callable<NKErrorCode>> jobs = List.of(() -> {
            NKErrorCode errorCode = removeOneUserUgc(uid, openId, creatorId, platId, loginType);
            if (!errorCode.isOk()) {
                LOGGER.error("failed to remove user ugc rank, uid:{} err:{}", uid, errorCode);
                return errorCode;
            }

            return NKErrorCode.OK;
        }, () -> {
            NKErrorCode errorCode = removeOneUserZSet(uid, apolloIds);
            if (!errorCode.isOk()) {
                LOGGER.error("failed to remove user zset rank, uid:{} apollo:{} err:{}", uid, apolloIds, errorCode);
                return errorCode;
            }

            return NKErrorCode.OK;
        }, () -> {
            NKErrorCode errorCode = removeOneUserApollo(uid, apolloIds, subIds);
            if (!errorCode.isOk()) {
                LOGGER.error("failed to remove user apollo rank, uid:{} apollo:{} sub:{} err:{}", uid, apolloIds,
                        subIds, errorCode);
                return errorCode;
            }

            return NKErrorCode.OK;
        });

        try {
            List<CoroHandle<NKErrorCode>> results = CurrentExecutorUtil.batchSubmitJob(jobs, "remove_user_rank", true);

            for (CoroHandle<NKErrorCode> result : results) {
                try {
                    NKErrorCode errorCode = result.get(5000);
                    if (!errorCode.isOk()) {
                        LOGGER.error("remove one user failed, uid:{} err:{}", uid, errorCode);
                        return errorCode;
                    }

                } catch (TimeoutException e) {
                    LOGGER.error("remove one user timeout, uid:{}", uid);
                    return NKErrorCode.RankBackendNotResponse;
                }
            }

            LOGGER.info("player rank info removed from backends, uid:{}", uid);
            return NKErrorCode.OK;
        } catch (NKCheckedException e) {
            LOGGER.error("remove one user failed, uid:{} err:{}", uid, e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    public NKErrorCode removeOneUserApollo(long uid, Map<Integer, Integer> geoIds, List<TopRankBackendType> backends) {
        List<NKPair<Integer, Integer>> subIds = new ArrayList<>();
        geoIds.forEach((k, v) -> subIds.add(new NKPair<>(k, v)));

        return removeOneUserApolloInternal(uid, Lists.newArrayList(RankingConfData.getInstance().getAllApolloIds()),
                subIds, backends);
    }

    public NKErrorCode removeOneUserApollo(long uid, List<Integer> apolloIds, List<NKPair<Integer, Integer>> subIds) {
        Map<TopRankBackendType, List<Integer>> backendApolloIds = new HashMap<>();
        for (int apolloId : apolloIds) {
            var conf = RankingConfData.getInstance().getByApolloId(apolloId);
            if (conf == null) {
                continue;
            }

            int rankId = conf.getRankId();
            for (var bk : RankIdBackendMapper.forRemove(rankId)) {
                backendApolloIds.computeIfAbsent(bk, k -> new ArrayList<>()).add(apolloId);
            }
        }

        List<TopRankBackendType> globalBk = Lists.newArrayList(TopRankBackendType.TRBT_Apollo_Trank,
                TopRankBackendType.TRBT_Plat);
        Set<Integer> globalIds = Sets.newHashSet();
        globalBk.forEach(bk -> globalIds.addAll(backendApolloIds.getOrDefault(bk, Collections.emptyList())));
        var errorCode = removeOneUserApolloInternal(uid, Lists.newArrayList(globalIds), Lists.newArrayList(), globalBk);
        if (errorCode.hasError()) {
            LOGGER.error("failed to remove one user apollo global, uid:{} bk:{} err:{}", uid, globalBk, errorCode);
        }

        List<TopRankBackendType> geoBk = Lists.newArrayList(TopRankBackendType.TRBT_Apollo_TopNext,
                TopRankBackendType.TRBT_Plat);
        Set<Integer> geoIds = Sets.newHashSet();
        geoBk.forEach(bk -> geoIds.addAll(backendApolloIds.getOrDefault(bk, Collections.emptyList())));
        var errorCode1 = removeOneUserApolloInternal(uid, Lists.newArrayList(geoIds), subIds, geoBk);
        if (errorCode1.hasError()) {
            LOGGER.error("failed to remove one user apollo geo, uid:{} bk:{} err:{}", uid, globalBk, errorCode1);
            errorCode = errorCode1;
        }

        return errorCode;
    }

    public NKErrorCode removeOneUserApolloInternal(long uid, List<Integer> apolloIds,
            List<NKPair<Integer, Integer>> subIds, List<TopRankBackendType> backends) {

        List<List<Integer>> groups = Lists.partition(apolloIds,
                PropertyFileReader.getRealTimeIntItem("rank_remove_one_user_batch_size", 10));
        long wait = PropertyFileReader.getRealTimeLongItem("rank_remove_one_user_wait", 1000);

        List<Callable<NKErrorCode>> jobs = groups.stream().map(ids -> (Callable<NKErrorCode>) () -> {
            try {
                RpcResult<RpcRemoveOneUserTopRankRes.Builder> result = RankService.get().rpcRemoveOneUserTopRank(
                        RpcRemoveOneUserTopRankReq.newBuilder().addAllBackend(backends).addAllApolloIds(ids).setUid(uid)
                                .addAllSubTypes(subIds.stream().map(NKPair::getKey).collect(Collectors.toList()))
                                .addAllSubIds(subIds.stream().map(NKPair::getValue).collect(Collectors.toList())));

                if (!result.isOK()) {
                    NKErrorCode errorCode = NKErrorCode.forNumber(result.getRet());
                    LOGGER.error("failed to remove one user score, uid:{} apollo:{} sub:{} bk:{} err:{}", uid,
                            apolloIds, subIds, backends, errorCode);
                    return errorCode;
                }

                NKErrorCode errorCode = NKErrorCode.forNumber(result.getData().getResult());
                if (!errorCode.isOk()) {
                    LOGGER.error("failed to remove one user score, uid:{} apollo:{} sub:{} bk:{} err:{}", uid,
                            apolloIds, subIds, backends, errorCode);
                    return errorCode;
                }

                LOGGER.info("clear apollo score, uid:{} apolloId:{}", uid, apolloIds);
                return NKErrorCode.OK;
            } catch (RpcException e) {
                LOGGER.error("error occur during remove {}: {}", uid, e.getEnumErrCode());
                return e.getEnumErrCode();
            }
        }).collect(Collectors.toList());

        try {
            List<CoroHandle<NKErrorCode>> tasks = CurrentExecutorUtil.batchSubmitJob(jobs, "rank_rm_apollo", true);

            for (CoroHandle<NKErrorCode> task : tasks) {
                try {
                    NKErrorCode errorCode = task.get(wait);
                    if (!errorCode.isOk()) {
                        LOGGER.error("failed to remove one user rank, uid:{} err:{}", uid, errorCode);
                        return errorCode;
                    }
                } catch (TimeoutException e) {
                    LOGGER.error("timeout to remove one user rank, uid:{}", uid);
                    return NKErrorCode.RankBackendNotResponse;
                }
            }

            return NKErrorCode.OK;
        } catch (NKCheckedException e) {
            LOGGER.error("failed to remove one user rank, uid:{} err:{}", uid, e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    public NKErrorCode removeZSet(List<Integer> apolloIds) {
        List<Callable<NKErrorCode>> tasks = apolloIds.stream().map(apolloId -> (Callable<NKErrorCode>) () -> {
            try {
                RpcResult<Builder> rpcResult = RankService.get().rpcRemoveTopRank(
                        RpcRemoveTopRankReq.newBuilder().setApolloId(apolloId)
                                .addBackend(TopRankBackendType.TRBT_Redis_ZSet));

                if (!rpcResult.isOK()) {
                    NKErrorCode errorCode = NKErrorCode.forNumber(rpcResult.getRet());
                    LOGGER.error("failed to remove zset, apolloId:{} err:{}", apolloId, errorCode);
                    return errorCode;
                }

                NKErrorCode errorCode = NKErrorCode.forNumber(rpcResult.getData().getResult());
                if (!errorCode.isOk()) {
                    LOGGER.error("failed to remove zset, apolloId:{} err:{}", apolloId, errorCode);
                    return errorCode;
                }

                return errorCode;
            } catch (RpcException | NKTimeoutException e) {
                LOGGER.error("failed to remove zset, apolloId:{} err:{}", apolloId, e.getEnumErrCode());
                return e.getEnumErrCode();
            }
        }).collect(Collectors.toList());

        try {
            List<CoroHandle<NKErrorCode>> results = CurrentExecutorUtil.batchSubmitJob(tasks, "remove_zset", true);
            for (CoroHandle<NKErrorCode> result : results) {
                NKErrorCode errorCode = result.get();
                if (!errorCode.isOk()) {
                    LOGGER.error("failed to remove zset, err:{}", errorCode);
                    return errorCode;
                }
            }

            return NKErrorCode.OK;
        } catch (NKCheckedException e) {
            LOGGER.error("failed to submit remove zset job, apolloIds:{} err:{}", apolloIds, e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    public NKErrorCode removeOneUserZSet(long uid, List<Integer> apolloIds) {
        for (int apolloId : apolloIds) {
            RankingConf rankingConf = RankingConfData.getInstance().getByApolloId(apolloId);
            if (rankingConf == null || rankingConf.getRule() != RankRule.RR_Qualify_Challenger) {
                continue;
            }

            CacheResult<Long> res = CacheUtil.RankListZSet.zremCacheForString(Long.toString(apolloId),
                    Long.toString(uid));

            if (res.errCode != CacheErrorCode.OK) {
                LOGGER.error("failed to clear zset, uid:{} apolloId:{} err:{}", uid, apolloId, res.errCode);
                return NKErrorCode.RankListRedisErrorCodeReturn;
            }

            LOGGER.info("clear zset score, uid:{} apolloId:{}", uid, apolloId);
        }

        return NKErrorCode.OK;
    }

    public NKErrorCode removeOneUserUgc(long uid, String openId, long creatorId, int platId,
            TconndApiAccount loginType) {
        try {
            RpcResult<RpcCreatorReportRes.Builder> rpcResult = UgcplatService.get()
                    .rpcCreatorReport(
                            RpcCreatorReportReq.newBuilder().setEventType(EventType.RemoveAccount_VALUE).setUid(uid)
                                    .setOpenId(openId).setCreatorId(creatorId).setPlatId(platId).setIsCreator(1)
                                    .setLoginType(loginType.getNumber())
                                    .setSource(Framework.getInstance().getServerType() == WeAServerType.ST_GameServer ? 1
                                            : 0));

            if (!rpcResult.isOK()) {
                NKErrorCode errorCode = NKErrorCode.forNumber(rpcResult.getRet());
                LOGGER.error("failed to clear ugc score, uid:{} err:{}", uid, errorCode);
                return errorCode;
            }

            NKErrorCode errorCode = NKErrorCode.forNumber(rpcResult.getData().getResult());
            if (!errorCode.isOk()) {
                LOGGER.error("failed to clear ugc score, uid:{} err:{}", uid, errorCode);
                return errorCode;
            }

            LOGGER.info("clear ugc score, uid:{}", uid);
            return NKErrorCode.OK;
        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("failed to clear ugc score, uid:{} err:{}", uid, e.getEnumErrCode());
            return e.getEnumErrCode();
        }
    }

    public NKPair<Integer, NKErrorCode> getZSetRankListSize(int apolloId) {
        CacheResult<Long> res = CacheUtil.RankListZSet.zcardCacheForString(Long.toString(apolloId));
        if (!res.isOk()) {
            Monitor.getInstance().add.fail(MonitorId.attr_rank_get_size, 1);
            LOGGER.error("failed to get zset rank list size, key:{} err:{}", apolloId, res.errCode);
            return new NKPair<>(0, NKErrorCode.RankListRedisErrorCodeReturn);
        }

        Monitor.getInstance().add.succ(MonitorId.attr_rank_get_size, 1);
        Monitor.getInstance().max.succ(MonitorId.attr_rank_size, res.val, new String[]{"zset_{}" + apolloId});
        return new NKPair<>(Math.toIntExact(res.val), NKErrorCode.OK);
    }

    public NKErrorCode resizeZSetRankList(int apolloId, int targetSize) {
        NKPair<Integer, NKErrorCode> currentSize = getZSetRankListSize(apolloId);
        if (!currentSize.value.isOk()) {
            LOGGER.error("failed to get current zset rank list size, key:{}", apolloId);
            return currentSize.value;
        }

        if (targetSize >= currentSize.key) {
            LOGGER.info("current size is smaller than expected, key:{} cur:{} exp:{}", apolloId, currentSize.key,
                    targetSize);
            return NKErrorCode.OK;
        }

        CacheResult<Long> res = CacheUtil.RankListZSet.zremrangebyrankCacheForString(Long.toString(apolloId),
                targetSize, -1);
        if (!res.isOk()) {
            Monitor.getInstance().add.fail(MonitorId.attr_rank_resize, 1);
            LOGGER.error("failed to resize rank list, key:{} target:{} err:{}", apolloId, targetSize, res.errCode);
            return NKErrorCode.RankListRedisErrorCodeReturn;
        }

        Monitor.getInstance().add.succ(MonitorId.attr_rank_resize, 1);
        LOGGER.warn("resize rank list, key:{} target:{}", apolloId, targetSize);
        return NKErrorCode.OK;
    }

    public NKPair<RankListWithTs, NKErrorCode> getRankList(RankId rankId, TopRankBackendType backend,
            boolean image, int count, boolean acceptRetry) {

        String key = RankListCacheClient.makeKey(rankId, backend, image);
        int hash = key.hashCode();

        try {
            RpcResult<RpcTopRankRedisRefreshAndFetchRes.Builder> rspResult = RankService.get()
                    .rpcTopRankRedisRefreshAndFetch(
                            RpcTopRankRedisRefreshAndFetchReq.newBuilder().setRedisKeyHash(hash).setRankId(rankId)
                                    .setBackend(backend).setFromIdx(1).setCount(count).setImageData(image)
                                    .setRetryOk(acceptRetry));

            if (!rspResult.isOK()) {
                LOGGER.error("on receive rpc error on ask top rank redis refresh_and_fetch key={}", key);
                Monitor.getInstance().add.fail(MonitorId.attr_rank_fetched_from_ranksvr, 1);
                return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.forNumber(rspResult.getRet()));
            }

            NKErrorCode errorCode = NKErrorCode.forNumber(rspResult.getData().getResult());

            if (!errorCode.isOk()) {
                LOGGER.error("on receive result error on ask top rank redis refresh_and_fetch key={}", key);
                Monitor.getInstance().add.fail(MonitorId.attr_rank_fetched_from_ranksvr, 1);
                return new NKPair<>(RankListCacheClient.EMPTY, errorCode);
            }

            Monitor.getInstance().add.succ(MonitorId.attr_rank_fetched_from_ranksvr, 1);
            return new NKPair<>(new RankListWithTs(rspResult.getData().getInfoList(), rspResult.getData().getTs(),
                    rspResult.getData().getTotalSize()), NKErrorCode.OK);

        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("failed to ask top rank redis refresh_and_fetch key={}: {}", key, e.getEnumErrCode());
            Monitor.getInstance().add.fail(MonitorId.attr_rank_fetched_from_ranksvr, 1);
            return new NKPair<>(RankListCacheClient.EMPTY, e.getEnumErrCode());
        }
    }

    public NKPair<RankListWithTs, NKErrorCode> getRankListByScore(RankId rankId, TopRankBackendType backend,
            boolean image, List<Integer> fromScore, int count) {
        try {
            var rspResult = RankService.get().rpcTopRankFetchByScore(
                    RpcTopRankFetchByScoreReq.newBuilder().setRankId(rankId).setBackend(backend.getNumber())
                            .setFromScore(fromScore.get(0)).setCount(count).setImageData(image));

            if (!rspResult.isOK()) {
                NKErrorCode errorCode = NKErrorCode.forNumberOrUnknown(rspResult.getRet());
                LOGGER.error("on receive rpc error on fetch by score, rankId:{} ret:{}", Pb2JsonUtil.getPbMsg(rankId),
                        errorCode);
                return new NKPair<>(RankListCacheClient.EMPTY, errorCode);
            }

            NKErrorCode errorCode = NKErrorCode.forNumberOrUnknown(rspResult.getData().getResult());

            if (!errorCode.isOk()) {
                LOGGER.error("on receive rpc error on fetch by score, rankId:{} ret:{}", Pb2JsonUtil.getPbMsg(rankId),
                        errorCode);
                return new NKPair<>(RankListCacheClient.EMPTY, errorCode);
            }

            return new NKPair<>(new RankListWithTs(rspResult.getData().getInfoList(), rspResult.getData().getTs(),
                    rspResult.getData().getTotalSize()), NKErrorCode.OK);

        } catch (RpcException | NKTimeoutException e) {
            LOGGER.error("on receive rpc error on fetch by score, rankId:{} ret:{}", Pb2JsonUtil.getPbMsg(rankId), e);
            return new NKPair<>(RankListCacheClient.EMPTY, e.getEnumErrCode());
        }
    }

    public NKPair<Integer, NKErrorCode> getZSetRankNo(long uid, TconndApiAccount account, int qualifyType,
            int seasonId) {

        RankingConf rankingConf = RankingConfData.getInstance()
                .getByDescId(RankRule.RR_Qualify_Challenger, qualifyType);
        if (rankingConf == null) {
            LOGGER.error("qualify has no rank conf, uid:{} qualify:{}", uid, qualifyType);
            return new NKPair<>(0, NKErrorCode.ResNotFound);
        }

        RankSeasonInfo seasonInfo = RankIdSeasonIdMapper.ofId(rankingConf.getRankId(), seasonId);
        int apolloId = RankIdApolloIdMapper.getApolloId(rankingConf.getRankId(), account, seasonInfo);

        if (seasonInfo == null || apolloId == 0) {
            LOGGER.error("qualify has no rank season conf, uid:{} qualify:{} seasonId:{}", uid, qualifyType, seasonId);
            return new NKPair<>(0, NKErrorCode.ResNotFound);
        }

        if (seasonInfo.hasEndSec()
                && seasonInfo.getEndSec() + DateUtils.ONE_DAY_SECONDS < DateUtils.currentTimeSec()) {
            LOGGER.error("rank season is expired, uid:{} qualify:{} seasonId:{}", uid, qualifyType, seasonId);
            return new NKPair<>(0, NKErrorCode.RankListAccessDenied);
        }

        int rankNo = cachedZSetRank.get(new NKPair<>(apolloId, uid));
        return new NKPair<>(rankNo, NKErrorCode.OK);
    }

    public NKPair<NKPair<Integer, Integer>, NKErrorCode> getSnapshotRankNoAndSize(long uid, int rankId, int seasonId,
            int sub) {

        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("failed to get rank conf, rankId:{}", rankId);
            return new NKPair<>(new NKPair<>(0, 0), NKErrorCode.ResNotFound);
        }

        if (rankingConf.getRule() == RankRule.RR_Club) {
            LOGGER.error("club rank snapshot is sending to clubsvr other than redis, rankId:{}", rankId);
            return new NKPair<>(new NKPair<>(0, 0), NKErrorCode.RankSettlementAccessDenied);
        }

        if (RankSettlementUtils.useBatch(rankingConf.getRule())) {
            LOGGER.error("please use batch method, rule:{}", rankingConf.getRule());
            return new NKPair<>(new NKPair<>(0, 0), NKErrorCode.RankSettlementAccessDenied);
        }

        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            LOGGER.error("failed to get season conf, rankId:{} seasonId:{}", rankId, seasonId);
            return new NKPair<>(new NKPair<>(0, 0), NKErrorCode.ResNotFound);
        }

        NKErrorCode errorCode = RankSettlementUtils.checkSnapshotAccess(rankId, seasonId);
        if (!errorCode.isOk()) {
            LOGGER.error("not ready for snapshot query, rankId:{} seasonId:{} err:{}", rankId, seasonId, errorCode);
            return new NKPair<>(new NKPair<>(0, 0), errorCode);
        }

        String snapshotId = RankSettlementUtils.ofSnapshotId(rankId, seasonId, sub);
        int size = cachedSettlementSize.get(snapshotId);
        if (size == 0) {
            LOGGER.debug("settlement on flight, uid:{} rankId:{} seasonId:{}", uid, rankId, seasonId);
            return new NKPair<>(new NKPair<>(0, 0), NKErrorCode.RankSettlementOnFlight);
        }

        var rankNo = getSnapshotRankNoFromRedis(snapshotId, uid);
        if (rankNo.value.hasError()) {
            LOGGER.error("failed to get rank no, uid:{} rankId:{} seasonId:{} err:{}", uid, rankId, seasonId,
                    rankNo.value);
            return new NKPair<>(new NKPair<>(0, 0), rankNo.value);
        }

        return new NKPair<>(new NKPair<>(rankNo.key, size), NKErrorCode.OK);
    }

    public NKPair<Integer, NKErrorCode> getSnapshotSizeRegardlessOfReadiness(int rankId, int seasonId,
            int sub) {
        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("failed to get rank conf, rankId:{}", rankId);
            return new NKPair<>(0, NKErrorCode.ResNotFound);
        }

        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            LOGGER.error("failed to get season conf, rankId:{} seasonId:{}", rankId, seasonId);
            return new NKPair<>(0, NKErrorCode.ResNotFound);
        }

        if (rankingConf.getRule() == RankRule.RR_Club) {
            LOGGER.error("club rank snapshot is sending to clubsvr other than redis, rankId:{}", rankId);
            return new NKPair<>(0, NKErrorCode.RankSettlementAccessDenied);
        }

        if (RankSettlementUtils.useBatch(rankingConf.getRule())) {
            int size = cachedBatchSettlementSize.get(
                    RankSettlementUtils.ofSnapshotId(rankingConf.getRule(), seasonId, sub));
            if (size == 0) {
                LOGGER.debug("settlement on flight, rankId:{} seasonId:{}", rankId, seasonId);
                return new NKPair<>(0, NKErrorCode.RankSettlementOnFlight);
            }

            return new NKPair<>(size, NKErrorCode.OK);
        }

        String snapshotId = RankSettlementUtils.ofSnapshotId(rankId, seasonId, sub);
        int size = cachedSettlementSize.get(snapshotId);
        if (size == 0) {
            LOGGER.debug("settlement on flight, rankId:{} seasonId:{}", rankId, seasonId);
            return new NKPair<>(0, NKErrorCode.RankSettlementOnFlight);
        }

        return new NKPair<>(size, NKErrorCode.OK);
    }

    public boolean isPlatDataSynced(int rankId, List<Integer> geoLevels) {
        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("failed to get rank conf, rankId:{}", rankId);
            return false;
        }

        if (geoLevels.isEmpty()) {
            String snapshotId = RankSettlementUtils.ofSnapshotId(rankId, 1, 0);
            return cachedPlatDataSynced.get(snapshotId);
        }

        List<String> snapshotIds = Lists.newArrayList();
        for (int subId : geoLevels) {
            String snapshotId = RankSettlementUtils.ofSnapshotId(rankId, 1, subId);
            snapshotIds.add(snapshotId);
        }

        var res = cachedPlatDataSynced.getAll(snapshotIds);
        for (var snapshotId : snapshotIds) {
            if (!res.getOrDefault(snapshotId, false)) {
                LOGGER.error("plat data not sync, snapshotId:{}", snapshotId);
                return false;
            }
        }

        return true;
    }

    private int getZSetRankNoFromRedis(int apolloId, long uid) {
        String[] param = new String[]{Integer.toString(2)};
        Monitor.getInstance().add.succ(MonitorId.attr_rank_fetch_zset_score, 1, param);

        CacheResult<Long> rank = CacheUtil.RankListZSet.zrankCacheForString(Long.toString(apolloId),
                Long.toString(uid));

        if (!rank.isOk()) {
            LOGGER.debug("failed to get zset rank no, apolloId:{} uid:{}", apolloId, uid);
            return 0;
        }

        return (int) (rank.val + 1);
    }

    private NKPair<Integer, NKErrorCode> getSnapshotRankNoFromRedis(String snapshotId, long uid) {
        CacheResult<String> res = CacheUtil.RankListSnapshot.hget(snapshotId, Long.toString(uid));
        if (res.errCode != CacheErrorCode.OK) {
            LOGGER.debug("failed to get settlement rank no, snapshotId:{} uid:{}", snapshotId, uid);
            return new NKPair<>(0, NKErrorCode.RedisOpFail);
        }

        try {
            return new NKPair<>(res.val == null ? 0 : Integer.parseInt(res.val), NKErrorCode.OK);
        } catch (Exception e) {
            LOGGER.error("failed to parse result, snapshotId:{} uid:{}", snapshotId, uid);
            return new NKPair<>(0, NKErrorCode.RankNoDecodingFailure);
        }
    }

    public NKPair<List<TopRankJointInfo.Builder>, NKErrorCode> getSnapshotRankNoFromRedis(RankRule rule,
            List<Integer> rankIds, int geoSetId, List<Integer> geoIds, long uid, int seasonId) {

        var seasonInfo = RankIdSeasonIdMapper.ofId(rankIds.get(0), seasonId);
        if (seasonInfo == null) {
            LOGGER.debug("no season conf found, rank:{} season:{}", rankIds, seasonId);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.ResNotFound);
        }

        NKErrorCode errorCode = RankSettlementUtils.checkBatchSnapshotAccess(rule, seasonId);
        if (!errorCode.isOk()) {
            LOGGER.error("not ready for snapshot rule, rankId:{} seasonId:{} err:{}", rule, seasonId, errorCode);
            return new NKPair<>(Lists.newArrayList(), errorCode);
        }

        String snapshotId = RankSettlementUtils.ofSnapshotId(rule, seasonId, geoSetId);
        int size = cachedBatchSettlementSize.get(snapshotId);
        if (size == 0) {
            LOGGER.debug("settlement on flight, uid:{} rule:{} seasonId:{}", uid, rule, seasonId);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.RankSettlementOnFlight);
        }

        Map<String, NKPair<Integer, Integer>> fields = Maps.newHashMap();
        for (int rankId : rankIds) {
            for (int geoId : geoIds) {
                var field = RankSettlementUtils.ofRankListBatchField(rankId, geoId, uid);
                fields.put(field, new NKPair<>(rankId, geoId));

                // query total size
                var totalSizeField = RankSettlementUtils.ofRankListBatchFieldTotalSize(rankId, geoId);
                fields.put(totalSizeField, new NKPair<>(rankId, geoId));
            }
        }

        int num = PropertyFileReader.getRealTimeIntItem("rank_list_snapshot_geo_fetch_size", 20);
        var groups = Lists.partition(Lists.newArrayList(fields.keySet()), num);
        List<Callable<NKPair<NKPair<Map<String, Integer>, Map<String, Integer>>, NKErrorCode>>> tasks = new ArrayList<>();
        for (var group : groups) {
            tasks.add(() -> {
                CacheResult<List<KeyValue<String, String>>> res = CacheUtil.RankListSnapshotBatch.hmget(snapshotId,
                        group);
                if (!res.isOk()) {
                    LOGGER.error("failed to get batch snapshot, snapshotId:{} fields:{} err:{}", snapshotId, group,
                            res.errCode);
                    return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), NKErrorCode.RedisOpFail);
                }

                Map<String, Integer> rankNoMap = Maps.newHashMap();
                Map<String, Integer> totalSizeMap = Maps.newHashMap();

                for (var kv : res.val) {
                    if (RankSettlementUtils.isRankListBatchFieldTotalSize(kv.getKey())) {
                        String str = kv.getValueOrElse("");
                        int totalSize = StringUtils.isNumeric(str) ? Integer.parseInt(str) : 0;
                        totalSizeMap.put(kv.getKey(), totalSize);

                        continue;
                    }

                    String str = kv.getValueOrElse("");
                    int rankNo = StringUtils.isNumeric(str) ? Integer.parseInt(str) : 0;
                    rankNoMap.put(kv.getKey(), rankNo);
                }

                return new NKPair<>(new NKPair<>(rankNoMap, totalSizeMap), NKErrorCode.OK);
            });
        }

        try {
            Map<Integer, Map<Integer, Integer>> rankGeoRankNo = Maps.newHashMap();
            Map<Integer, Map<Integer, Integer>> rankGeoTotalSize = Maps.newHashMap();

            var results = CurrentExecutorUtil.batchSubmitJob(tasks, "batch_snapshot", true);
            for (var result : results) {
                var res = result.get(1000);
                if (res.value.hasError()) {
                    LOGGER.error("failed to get batch snapshot result, uid:{} rule:{} err:{}", uid, rule, res.value);
                    return new NKPair<>(Lists.newArrayList(), res.value);
                }

                var rankNoMap = res.key.key;
                var totalSizeMap = res.key.value;

                for (var e : rankNoMap.entrySet()) {
                    var key = e.getKey();
                    if (!fields.containsKey(key)) {
                        continue;
                    }

                    var rankGeo = fields.get(key);
                    rankGeoRankNo.computeIfAbsent(rankGeo.key, k -> Maps.newHashMap()).put(rankGeo.value, e.getValue());
                }

                for (var e : totalSizeMap.entrySet()) {
                    var key = e.getKey();
                    if (!fields.containsKey(key)) {
                        continue;
                    }

                    var rankGeo = fields.get(key);
                    rankGeoTotalSize.computeIfAbsent(rankGeo.key, k -> Maps.newHashMap())
                            .put(rankGeo.value, e.getValue());
                }
            }

            List<TopRankJointInfo.Builder> builders = Lists.newArrayList();
            for (var e : rankGeoRankNo.entrySet()) {
                TopRankJointInfo.Builder builder = TopRankJointInfo.newBuilder().setRankId(e.getKey());

                var totalSizeMap = rankGeoTotalSize.getOrDefault(e.getKey(), Maps.newHashMap());

                for (var ee : e.getValue().entrySet()) {
                    int geoId = ee.getKey();
                    int rankNo = ee.getValue();
                    int totalSize = totalSizeMap.getOrDefault(geoId, 0);

                    if (geoId == 0) {
                        builder.setGlobalRankNo(rankNo).setGlobalSize(totalSize);
                        continue;
                    }

                    builder.addGeoRankNo(KeyValueInt32.newBuilder().setKey(geoId).setValue(rankNo));
                    builder.addGeoSize(KeyValueInt32.newBuilder().setKey(geoId).setValue(totalSize));
                }

                builders.add(builder);
            }

            return new NKPair<>(builders, NKErrorCode.OK);
        } catch (NKCheckedException e) {
            LOGGER.error("failed to get batch snapshot result, uid:{} rule:{} err:{}", uid, rule, e);
            return new NKPair<>(Lists.newArrayList(), e.getEnumErrCode());
        } catch (TimeoutException e) {
            LOGGER.error("failed to get batch snapshot result, uid:{} rule:{} err:{}", uid, rule, e);
            return new NKPair<>(Lists.newArrayList(), NKErrorCode.RankBackendNotResponse);
        }
    }

    public NKPair<NKPair<Map<Integer, Integer>, Map<Integer, TopRankInfo>>, NKErrorCode> getSnapshotRankNoAndEndOfListFromRedis(
            int rankId, int seasonId, int geoSetId, List<Integer> geoIds, long uid) {
        var rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.debug("no rank conf found, rank:{} season:{}", rankId, seasonId);
            return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), NKErrorCode.ResNotFound);
        }

        var seasonInfo = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonInfo == null) {
            LOGGER.debug("no season conf found, rank:{} season:{}", rankId, seasonId);
            return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), NKErrorCode.ResNotFound);
        }

        var rule = rankingConf.getRule();
        NKErrorCode errorCode = RankSettlementUtils.checkBatchSnapshotAccess(rule, seasonId);
        if (!errorCode.isOk()) {
            LOGGER.error("not ready for snapshot rule, rankId:{} seasonId:{} err:{}", rule, seasonId, errorCode);
            return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), errorCode);
        }

        String snapshotId = RankSettlementUtils.ofSnapshotId(rule, seasonId, geoSetId);
        int size = cachedBatchSettlementSize.get(snapshotId);
        if (size == 0) {
            LOGGER.debug("settlement on flight, rule:{} seasonId:{}", rule, seasonId);
            return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), NKErrorCode.RankSettlementOnFlight);
        }

        Map<String, Integer> fields = Maps.newHashMap();
        for (int geoId : geoIds) {
            var field = RankSettlementUtils.ofRankListBatchFieldEndOfList(rankId, geoId);
            fields.put(field, geoId);

            field = RankSettlementUtils.ofRankListBatchField(rankId, geoId, uid);
            fields.put(field, geoId);
        }

        CacheResult<List<KeyValue<String, String>>> hmgetRes = CacheUtil.RankListSnapshotBatch.hmget(snapshotId,
                Lists.newArrayList(fields.keySet()));
        if (!hmgetRes.isOk()) {
            LOGGER.error("failed to get batch snapshot, snapshotId:{} fields:{} err:{}", snapshotId, fields.keySet(),
                    hmgetRes.errCode);
            return new NKPair<>(new NKPair<>(Maps.newHashMap(), Maps.newHashMap()), NKErrorCode.RedisOpFail);
        }

        Map<Integer, TopRankInfo> eol = Maps.newHashMap();
        Map<Integer, Integer> self = Maps.newHashMap();

        for (var kv : hmgetRes.val) {
            var field = kv.getKey();
            if (!fields.containsKey(field)) {
                continue;
            }

            String str = kv.getValueOrElse("");
            if (str.isBlank()) {
                continue;
            }

            if (RankSettlementUtils.isRankListBatchFieldEndOfList(field)) {
                var ss = str.split("#");
                try {
                    var info = TopRankInfo.newBuilder().setUid(Long.parseLong(ss[0]))
                            .setRankNo(Integer.parseInt(ss[1])).setLastUpdateTs(Long.parseLong(ss[2]))
                            .addScore(Integer.parseInt(ss[3]));
                    eol.put(fields.get(field), info.build());
                } catch (Exception e) {
                    LOGGER.error("failed to parse snapshot eol, snapshot:{} field:{} value:{}", snapshotId,
                            kv.getKey(), str);
                }

                continue;
            }

            if (StringUtils.isNumeric(str)) {
                int rankNo = Integer.parseInt(str);
                self.put(fields.get(field), rankNo);
            }
        }

        return new NKPair<>(new NKPair<>(self, eol), NKErrorCode.OK);
    }

    public NKPair<Integer, NKErrorCode> getDailyZSetSnapshotRankNoFromRedis(long uid, int rankId, int seasonId,
            long tag) {

        String snapshotId = RankSettlementUtils.ofSnapshotId(rankId, seasonId, 0, tag);
        int size = cachedDailyZSetSettlementSize.get(snapshotId);
        if (size == 0) {
            LOGGER.debug("settlement on flight, uid:{} rankId:{} seasonId:{} tag:{}", uid, rankId, seasonId, tag);
            return new NKPair<>(0, NKErrorCode.RankSettlementOnFlight);
        }

        CacheResult<String> res = CacheUtil.RankListSnapshotZSetDaily.hget(snapshotId, Long.toString(uid));
        if (res.errCode != CacheErrorCode.OK) {
            LOGGER.error("failed to get daily zset snapshot, uid:{} snapshot:{} err:{}", uid, snapshotId, res.errCode);
            return new NKPair<>(0, NKErrorCode.RedisOpFail);
        }

        if (!StringUtils.isNumeric(res.val)) {
            LOGGER.debug("rank no not found, uid:{} snapshot:{} rank_no:{}", uid, snapshotId, res.val);
            return new NKPair<>(0, NKErrorCode.OK);
        }

        return new NKPair<>(Integer.parseInt(res.val), NKErrorCode.OK);
    }

    private int getSnapshotRankSizeFromRedis(String snapshotId) {
        var res = RankSettlementUtils.getProgress(TopRankCoordinationType.TRCT_Settlement, snapshotId);
        if (res.value.hasError() || res.key == null) {
            LOGGER.error("failed to get progress, id:{} err:{}", snapshotId, res.value);
            return 0;
        }

        if (res.key.getFinishTs() != 0 && res.key.getFinishTs() <= DateUtils.currentTimeMillis()) {
            int grant = 0;
            int size = 0;

            for (var kv : res.key.getInfosList()) {
                if (kv.getKey().equals("actual_size")) {
                    try {
                        size = Integer.parseInt(kv.getValue());
                    } catch (Exception e) {
                        LOGGER.error("failed to parse actual_size, val:{} err:{}", kv.getValue(), e);
                    }
                } else if (kv.getKey().equals("grant_access")) {
                    try {
                        grant = Integer.parseInt(kv.getValue());
                    } catch (Exception e) {
                        LOGGER.error("failed to parse grant_access, val:{} err:{}", kv.getValue(), e);
                    }
                }
            }

            if (grant != 1) {
                LOGGER.error("snapshot not allow to access, snapshot:{} progress:{}", snapshotId,
                        Pb2JsonUtil.getPbMsg(res.key));
                return 0;
            }

            return size;
        }

        return 0;
    }

    private int getBatchSnapshotRankSizeFromRedis(String snapshotId) {
        var res = RankSettlementUtils.getProgress(TopRankCoordinationType.TRCT_BatchSettlement, snapshotId);
        if (res.value.hasError() || res.key == null) {
            LOGGER.error("failed to get progress, id:{} err:{}", snapshotId, res.value);
            return 0;
        }

        if (res.key.getFinishTs() != 0 && res.key.getFinishTs() <= DateUtils.currentTimeMillis()) {
            boolean ok = RankSettlementUtils.getProgressKeyValueBoolean(res.key, "complete", false);
            if (!ok) {
                LOGGER.error("snapshot not allow to access, snapshot:{} progress:{}", snapshotId,
                        Pb2JsonUtil.getPbMsg(res.key));
                return 0;
            }
        }

        return RankSettlementUtils.getProgressKeyValueInt(res.key, "fetch_size", 0);
    }

    private boolean getPlatDataSyncedFromRedis(String snapshotId) {
        var res = RankSettlementUtils.getProgress(TopRankCoordinationType.TRCT_PlatDataSync, snapshotId);
        if (res.value.hasError() || res.key == null) {
            LOGGER.error("failed to get progress, id:{} err:{}", snapshotId, res.value);
            return false;
        }

        return RankSettlementUtils.getProgressKeyValueBoolean(res.key, "complete", false);
    }

    private int getDailyZSetSnapshotRankSizeFromRedis(String snapshotId) {
        try {
            var res = RankSettlementUtils.getProgress(TopRankCoordinationType.TRCT_DailyZSetSettlement, snapshotId);
            if (res.value.hasError() || res.key == null) {
                LOGGER.error("failed to get progress, id:{} err:{}", snapshotId, res.value);
                return 0;
            }

            if (res.key.getFinishTs() != 0 && res.key.getFinishTs() <= DateUtils.currentTimeMillis()) {
                boolean ok = RankSettlementUtils.getProgressKeyValueInt(res.key, "grant_access", 0) == 1;
                if (!ok) {
                    LOGGER.error("snapshot not allow to access, snapshot:{} progress:{}", snapshotId,
                            Pb2JsonUtil.getPbMsg(res.key));
                    return 0;
                }

                return RankSettlementUtils.getProgressKeyValueInt(res.key, "actual_size", 0);
            }

            return 0;
        } catch (Exception e) {
            LOGGER.error("failed to get size, snapshot:{} err:{}", snapshotId, e);
            return 0;
        }
    }

    public NKPair<RankListWithTs, NKErrorCode> getZSetRankList(int apolloId, int count) {
        return getZSetRankList(apolloId, 1, count);
    }

    public NKPair<RankListWithTs, NKErrorCode> getZSetRankList(int apolloId, int from, int count) {
        var rankSize = CacheUtil.RankListZSet.zcardCacheForString(String.valueOf(apolloId));
        if (!rankSize.isOk()) {
            LOGGER.error("failed to get redis apolloId={}: {}", apolloId, rankSize.errCode);
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.RankListRedisErrorCodeReturn);
        }

        CacheResult<List<ScoredValue<String>>> rankList = CacheUtil.RankListZSet.zrangeWithScoreCacheForString(
                String.valueOf(apolloId), from - 1, from + count - 2);
        if (rankList.errCode != CacheErrorCode.OK) {
            LOGGER.error("failed to get redis apolloId={}: {}", apolloId, rankList.errCode);
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.RankListRedisErrorCodeReturn);
        }

        if (rankList.val == null) {
            LOGGER.debug("apolloId {} not exists in redis", apolloId);
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.OK);
        }

        try {
            List<TopRankInfo> res = RankZSetScoreUtils.decode(rankList.val);
            return new NKPair<>(new RankListWithTs(res, DateUtils.currentTimeMillis(), Math.toIntExact(rankSize.val)),
                    NKErrorCode.OK);
        } catch (NKCheckedException e) {
            LOGGER.error("failed to decode zset result, apolloId:{} topN:{} err:{}", apolloId, count,
                    e.getEnumErrCode());
            return new NKPair<>(RankListCacheClient.EMPTY, e.getEnumErrCode());
        }
    }

    public NKPair<RankListWithTs, NKErrorCode> getZSetRankListByScore(int apolloId, int fromScore, int count) {
        var rankSize = CacheUtil.RankListZSet.zcardCacheForString(String.valueOf(apolloId));
        if (!rankSize.isOk()) {
            LOGGER.error("failed to get redis apolloId={}: {}", apolloId, rankSize.errCode);
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.RankListRedisErrorCodeReturn);
        }

        var rankingConf = RankingConfData.getInstance().getByApolloId(apolloId);
        if (rankingConf == null) {
            LOGGER.error("rank conf not found, apolloId:{}", apolloId);
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.ResNotFound);
        }

        var ruleConf = RankingRuleConfData.getInstance().get(rankingConf.getRule());
        if (ruleConf == null) {
            LOGGER.error("rule conf not found, rule:{}", rankingConf.getRule());
            return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.ResNotFound);
        }

        try {

            // in this case, wrap redis-score with ts=0 to ensure inclusive bounding

            double score = RankZSetScoreUtils.encode(fromScore, 0,
                    ruleConf.getOrdering() == RankOrderingType.ROT_Ascending);

            CacheResult<List<ScoredValue<String>>> rankList = CacheUtil.RankListZSet.zrangeByScoreWithScoreCacheForString(
                    String.valueOf(apolloId), score, Double.POSITIVE_INFINITY, 0, count);
            if (rankList.errCode != CacheErrorCode.OK) {
                LOGGER.error("failed to get redis apolloId={}: {}", apolloId, rankList.errCode);
                return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.RankListRedisErrorCodeReturn);
            }

            if (rankList.val == null) {
                LOGGER.debug("apolloId {} not exists in redis", apolloId);
                return new NKPair<>(RankListCacheClient.EMPTY, NKErrorCode.OK);
            }

            List<TopRankInfo> res = RankZSetScoreUtils.decode(rankList.val);
            return new NKPair<>(new RankListWithTs(res, DateUtils.currentTimeMillis(), Math.toIntExact(rankSize.val)),
                    NKErrorCode.OK);
        } catch (NKCheckedException e) {
            LOGGER.error("failed to decode zset result, apolloId:{} topN:{} err:{}", apolloId, count,
                    e.getEnumErrCode());
            return new NKPair<>(RankListCacheClient.EMPTY, e.getEnumErrCode());
        }
    }

    public Map<Long, NKErrorCode> batchWriteScoreToApolloRankList(int rankId, int seasonId,
            List<NKPair<Long, Integer>> scores, long updateTs, List<NKPair<Integer, Integer>> subRankIds,
            TopRankBackendType backend) {

        ConsoleWriter writer = writers.getOrDefault(backend, ConsoleWriter.IGNORE);

        Map<Long, Callable<NKErrorCode>> tasks = scores.stream().map(kv -> new NKPair<>(kv.key,
                        (Callable<NKErrorCode>) () -> writer.updateOneUserScore(rankId, seasonId, kv.key, subRankIds,
                                Lists.newArrayList(kv.value), updateTs, false)))
                .collect(Collectors.toMap(NKPair::getKey, NKPair::getValue));

        try {
            Map<Long, NKErrorCode> res = new HashMap<>();

            Map<Long, CoroHandle<NKErrorCode>> results = CurrentExecutorUtil.batchSubmitJob(tasks,
                    "write_apollo_rank_list", true);

            for (Map.Entry<Long, CoroHandle<NKErrorCode>> result : results.entrySet()) {
                try {
                    NKErrorCode errorCode = result.getValue().get(500);
                    if (!errorCode.isOk()) {
                        LOGGER.error(
                                "failed insert score to apollo rank list, uid:{} rankId:{} sub:{} type:{} err:{}",
                                result.getKey(), rankId, subRankIds, backend, errorCode);
                    }

                    res.put(result.getKey(), errorCode);
                } catch (TimeoutException e) {
                    LOGGER.error(
                            "failed insert score to apollo rank list, uid:{} rankId:{} sub:{} type:{} err:timeout",
                            result.getKey(), rankId, subRankIds, backend);
                    res.put(result.getKey(), NKErrorCode.RankBackendNotResponse);
                }
            }

            return res;
        } catch (NKCheckedException e) {
            LOGGER.error("failed to submit write apollo rank score tasks, rankId:{} sub:{} type:{} err:{}",
                    rankId, subRankIds, backend, e.getEnumErrCode());
            return scores.stream().collect(Collectors.toMap(NKPair::getKey, kv -> e.getEnumErrCode()));
        }
    }

    public List<NKPair<Integer, Integer>> getPlayerAllInvolvingSubRankIds(PlayerOrBuilder player) {
        return RankAttrUtils.getAllRankSubTypesSubIds(player);
    }

    public List<NKPair<Integer, Integer>> getPlayerSubRankIds(PlayerOrBuilder player) {
        List<NKPair<Integer, Integer>> res = new ArrayList<>();
        RankAttrUtils.getRankSubTypesSubIds(player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo())
                .forEach((k, v) -> res.add(new NKPair<>(k, v)));
        return res;
    }

    public NKErrorCode banAll(long uid) {
        TcaplusDb.Player player = PlayerTableDao.getTcaplusPlayer(uid);
        if (player == null) {
            LOGGER.error("query Player table failed, uid:{}", uid);
            return NKErrorCode.IdipNoSuchUser;
        }

        TcaplusDb.PlayerPublic playerPublic = PlayerPublicDao.getPlayerPublicFromTcaplus(uid,
                PlayerPublicAttrKey.PublicGameData);
        if (playerPublic == null) {
            LOGGER.error("query PlayerPublic table failed, uid:{}", uid);
            return NKErrorCode.IdipNoSuchUser;
        }

        TcaplusDb.Player.Builder playerBuilder = player.toBuilder();
        TcaplusDb.PlayerPublic.Builder playerPublicBuilder = playerPublic.toBuilder();

        setAllRankBannedAttr(playerBuilder, playerPublicBuilder, true);

        NKErrorCode errorCode = PlayerTableDao.updateTcaplusPlayer(uid, playerBuilder);
        if (!errorCode.isOk()) {
            LOGGER.error("update Player table failed, uid:{}, err:{}", uid, errorCode);
            return errorCode;
        }

        // 更新PlayerPublic表数据
        errorCode = PlayerPublicDao.updatePlayerPublicToTcaplus(uid, playerPublicBuilder);
        if (!errorCode.isOk()) {
            LOGGER.error("update PlayerPublic table failed, uid:{}, err:{}", uid, errorCode);
            return errorCode;
        }

        errorCode = banAllRankScores(uid, playerBuilder, playerPublicBuilder);
        if (!errorCode.isOk()) {
            LOGGER.error("update backend failed, uid:{}, err:{}", uid, errorCode);
            return errorCode;
        }

        LOGGER.warn("ban all ranks for player, uid:{}", player.getUid());
        return NKErrorCode.OK;
    }

    public NKErrorCode unbanAll(long uid) {
        TcaplusDb.Player player = PlayerTableDao.getTcaplusPlayer(uid);
        if (player == null) {
            LOGGER.error("query Player table failed, uid:{}", uid);
            return NKErrorCode.IdipNoSuchUser;
        }

        TcaplusDb.PlayerPublic playerPublic = PlayerPublicDao.getPlayerPublicFromTcaplus(uid,
                PlayerPublicAttrKey.PublicGameData);
        if (playerPublic == null) {
            LOGGER.error("query PlayerPublic table failed, uid:{}", uid);
            return NKErrorCode.IdipNoSuchUser;
        }

        TcaplusDb.Player.Builder playerBuilder = player.toBuilder();
        TcaplusDb.PlayerPublic.Builder playerPublicBuilder = playerPublic.toBuilder();

        setAllRankBannedAttr(playerBuilder, playerPublicBuilder, false);

        NKErrorCode errorCode = PlayerTableDao.updateTcaplusPlayer(uid, playerBuilder);
        if (!errorCode.isOk()) {
            LOGGER.error("update Player table failed, uid:{}, err:{}", uid, errorCode);
            return errorCode;
        }

        // 更新PlayerPublic表数据
        errorCode = PlayerPublicDao.updatePlayerPublicToTcaplus(uid, playerPublicBuilder);
        if (!errorCode.isOk()) {
            LOGGER.error("update PlayerPublic table failed, uid:{}, err:{}", uid, errorCode);
            return errorCode;
        }

        LOGGER.warn("unban all ranks for player, uid:{}", player.getUid());
        return NKErrorCode.OK;
    }

    public NKErrorCode deleteOneUserScore(Player.Builder player, PlayerPublic.Builder playerPublic,
            List<Integer> rankIds, long currentMs) {
        List<Integer> apolloIds = Lists.newArrayList();
        List<Integer> rankIdsReg = Lists.newArrayList();

        for (int rankId : rankIds) {
            RankingConf conf = RankingConfData.getInstance().get(rankId);
            if (conf != null && conf.getRule() != RankRule.RR_Ugc_Exp) {
                int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player);
                if (apolloId != 0) {
                    apolloIds.add(apolloId);
                    rankIdsReg.add(rankId);
                }
            }
        }

        if (apolloIds.size() != rankIds.size()) {
            LOGGER.warn("detect non apollo rank ids, uid:{} rankId:{} mapped:{}", player.getUid(), rankIds, rankIdsReg);
        }

        LOGGER.warn("start delete one user score, uid:{} rankId:{} apolloIds:{}", player.getUid(), rankIdsReg,
                apolloIds);

        RankAttrUtils.deleteRankInfoItem(player, playerPublic, rankIdsReg);
        RankAttrUtils.resetRankInfoReportStatusIfNotBanned(player, rankIdsReg);

        NKErrorCode errorCode = removeOneUserZSet(player.getUid(), apolloIds);
        if (!errorCode.isOk()) {
            LOGGER.error("failed to delete zset score, uid:{} apolloIds:{} err:{}", player.getUid(), apolloIds,
                    errorCode);
        }

        List<NKPair<Integer, Integer>> subIds = getPlayerAllInvolvingSubRankIds(player);
        NKErrorCode errorCode2 = removeOneUserApollo(player.getUid(), apolloIds, subIds);
        if (!errorCode2.isOk()) {
            LOGGER.error("failed to delete apollo score, uid:{} apolloIds:{} err:{}", player.getUid(), apolloIds,
                    errorCode2);
        }

        LOGGER.warn("finish delete one user score, uid:{} rankId:{} apolloIds:{}", player.getUid(), rankIdsReg,
                apolloIds);
        return errorCode2.isOk() ? errorCode : errorCode2;
    }

    public NKErrorCode overwriteOneUserScore(Player.Builder player, PlayerPublic.Builder playerPublic, int rankId,
            List<Integer> scores, long currentMs) {
        var seasonConf = RankIdSeasonIdMapper.ofTs(rankId, currentMs / 1000);
        if (seasonConf == null) {
            LOGGER.error("player cannot overwrite score due to lack of season conf, uid:{} rankId:{}", player.getUid(),
                    rankId);
            return NKErrorCode.ResNotFound;
        }

        RankingConf rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("cannot find ranking conf, uid:{}", player.getUid());
            return NKErrorCode.ResNotFound;
        }

        RankingRuleConf ruleConf = RankingRuleConfData.getInstance().get(rankingConf.getRule());
        if (ruleConf == null) {
            LOGGER.error("cannot find ranking rule conf, uid:{}", player.getUid());
            return NKErrorCode.ResNotFound;
        }

        int apolloId = RankIdApolloIdMapper.getApolloId(rankId, player);
        if (apolloId == 0) {
            LOGGER.error("cannot found apollo id, uid:{} rank:{}", player.getUid(), rankId);
            return NKErrorCode.ResNotFound;
        }

        LOGGER.warn("start overwrite one user score, uid:{} rankId:{}", player.getUid(), rankId);
        boolean banned = RankAttrUtils.isHideFromOthers(player);

        RankAttrUtils.processRankInfoItem(player, playerPublic, rankId,
                item -> setRankAttr(item, scores, seasonConf.getId(), currentMs, banned));

        if (banned) {
            RankAttrUtils.processRankInfoReportStatus(player, rankId,
                    status -> status.setGlobalStatus(RankReportStatus.RRS_BANNED)
                            .setGeoStatus(RankReportStatus.RRS_BANNED));
            LOGGER.warn("finish overwrite one user score, uid:{} rankId:{} banned:{}", player.getUid(), rankId, banned);
            return NKErrorCode.OK;
        }

        // set non-ugc rankings, skip eol checking
        try {
            var writer = writers.getOrDefault(RankIdBackendMapper.forWrite(rankId, RankType.RT_Global),
                    ConsoleWriter.IGNORE);

            NKErrorCode errorCode = writer.updateOneUserScore(rankId, seasonConf.getId(), player.getUid(),
                    Lists.newArrayList(), scores, currentMs, true);
            if (!errorCode.isOk()) {
                LOGGER.error("failed to upload global, uid:{} rank:{} err:{}", player.getUid(), rankId, errorCode);
                RankAttrUtils.processRankInfoReportStatus(player, rankId,
                        b -> b.setGlobalStatus(RankReportStatus.RRS_RETRY));
                return errorCode;
            }

            RankAttrUtils.processRankInfoReportStatus(player, rankId, b -> b.setGlobalStatus(RankReportStatus.RRS_OK));
        } catch (Exception e) {
            LOGGER.error("failed to upload global, uid:{} rank:{} err:{}", player.getUid(), rankId, e);
            RankAttrUtils.processRankInfoReportStatus(player, rankId,
                    b -> b.setGlobalStatus(RankReportStatus.RRS_RETRY));
            return (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                    : NKErrorCode.UnknownError;
        }

        // for zset ranking, direct upload

        if (ruleConf.getRule() == RankRule.RR_Qualify_Challenger) {
            try {
                double score = RankZSetScoreUtils.encode(scores.get(0), currentMs,
                        ruleConf.getOrdering() == RankOrderingType.ROT_Ascending);

                CacheResult<?> uploadRes = CacheUtil.RankListZSet.zaddCacheForString(Long.toString(apolloId),
                        score, Long.toString(player.getUid()));
                if (uploadRes.errCode != CacheErrorCode.OK) {
                    LOGGER.error("failed to upload to zset, uid:{} apolloId:{} err:{}", player.getUid(), apolloId,
                            uploadRes.errCode);
                    return NKErrorCode.RankListRedisErrorCodeReturn;
                }
            } catch (NKCheckedException e) {
                LOGGER.error("failed to upload to zset, uid:{} apolloId:{} err:{}", player.getUid(), apolloId,
                        e.getEnumErrCode());
                return e.getEnumErrCode();
            }
        }

        // for lbs ranking
        //
        //  1. remove old scores if exists
        //  2. report new scores

        if (RankingRuleConfData.writable(ruleConf, RankType.RT_Geo)) {
            List<NKPair<Integer, Integer>> fromSubIds = getPlayerAllInvolvingSubRankIds(player);
            List<NKPair<Integer, Integer>> toSubIds = getPlayerSubRankIds(player);

            var writer = writers.getOrDefault(RankIdBackendMapper.forWrite(rankId, RankType.RT_Geo),
                    ConsoleWriter.IGNORE);

            if (!fromSubIds.isEmpty()) {
                try {
                    NKErrorCode errorCode = writer.removeOneUserScore(rankId, seasonConf.getId(), player.getUid(),
                            fromSubIds);
                    if (!errorCode.isOk()) {
                        LOGGER.error("failed to overwrite geo step I, uid:{} rank:{} sub:{} err:{}",
                                player.getUid(), rankId, fromSubIds, errorCode);
                        RankAttrUtils.processRankInfoReportStatus(player, rankId,
                                b -> b.setGeoStatus(RankReportStatus.RRS_RETRY));
                        return errorCode;
                    }

                    // lbs ranking is deleted and reset belonging sub ranks

                    LOGGER.warn("remove geo rank score, uid:{} rank:{} sub:{}", player.getUid(), rankId,
                            fromSubIds);
                    RankAttrUtils.processRankInfoReportStatus(player, rankId,
                            b -> setGeoStatus(b, RankReportStatus.RRS_OK, Maps.newHashMap()));
                } catch (Exception e) {
                    LOGGER.error("failed to overwrite geo step I, uid:{} rank:{} sub:{} err:{}", player.getUid(),
                            rankId, fromSubIds, e);
                    RankAttrUtils.processRankInfoReportStatus(player, rankId,
                            b -> b.setGeoStatus(RankReportStatus.RRS_RETRY));
                    return (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                            : NKErrorCode.UnknownError;
                }
            }

            if (!toSubIds.isEmpty()) {
                Map<Integer, Integer> destination = RankAttrUtils.getRankSubTypesSubIds(
                        player.getUserAttr().getPlayerPublicGameData().getRankGeoInfo());

                try {
                    NKErrorCode errorCode = writer.updateOneUserScore(rankId, seasonConf.getId(), player.getUid(),
                            toSubIds, scores, currentMs, true);
                    if (!errorCode.isOk()) {
                        LOGGER.error("failed to overwrite geo step II, uid:{} rank:{} sub:{} err:{}",
                                player.getUid(), rankId, toSubIds, errorCode);
                        RankAttrUtils.processRankInfoReportStatus(player, rankId,
                                b -> setGeoStatus(b, RankReportStatus.RRS_RETRY, destination));
                        return errorCode;
                    }

                    // lbs ranking is reported and set belonging sub ranks

                    LOGGER.warn("report geo rank score, uid:{} rank:{} sub:{}", player.getUid(), rankId,
                            fromSubIds);
                    RankAttrUtils.processRankInfoReportStatus(player, rankId,
                            b -> setGeoStatus(b, RankReportStatus.RRS_OK, destination));
                } catch (Exception e) {
                    LOGGER.error("failed to overwrite geo step II, uid:{} rank:{} sub:{} err:{}", player.getUid(),
                            rankId, toSubIds, e);
                    RankAttrUtils.processRankInfoReportStatus(player, rankId,
                            b -> setGeoStatus(b, RankReportStatus.RRS_RETRY, destination));
                    return (e instanceof IEnumedException) ? (NKErrorCode) ((IEnumedException) e).getEnumErrCode()
                            : NKErrorCode.UnknownError;
                }
            }
        }

        LOGGER.warn("finish overwrite one user score, uid:{} rankId:{} banned:{}", player.getUid(), rankId, banned);
        return NKErrorCode.OK;
    }

    private void setGeoStatus(proto_RankInfoReportStatus.Builder b, RankReportStatus status,
            Map<Integer, Integer> lbs) {
        b.setGeoStatus(status).setNation(lbs.getOrDefault(GeoLevel.GL_Nation_VALUE, 0))
                .setProvince(lbs.getOrDefault(GeoLevel.GL_Province_VALUE, 0))
                .setCity(lbs.getOrDefault(GeoLevel.GL_City_VALUE, 0))
                .setTown(lbs.getOrDefault(GeoLevel.GL_Town_VALUE, 0));
    }

    private void setAllRankBannedAttr(Player.Builder player, PlayerPublic.Builder playerPublic, boolean ban) {
        RankAttrUtils.setHideFromOthers(player, ban);
        RankAttrUtils.processEachRankInfoReportStatus(player,
                builder -> builder.setGlobalStatus(ban ? RankReportStatus.RRS_BANNED : RankReportStatus.RRS_RETRY)
                        .setGeoStatus(ban ? RankReportStatus.RRS_BANNED : RankReportStatus.RRS_RETRY));
        RankAttrUtils.processEachRankInfoItem(player, playerPublic, b -> b.setHide(ban));
    }

    private NKErrorCode banAllRankScores(long uid, Player.Builder player, PlayerPublic.Builder playerPublic) {
        List<Integer> apolloIds = playerPublic.getPublicGameData().getRankInfoList().stream()
                .map(proto_RankInfoItem::getType)
                .map(rankId -> RankIdApolloIdMapper.getApolloId(rankId, player))
                .filter(apolloId -> apolloId != 0).collect(Collectors.toList());

        List<NKPair<Integer, Integer>> subRankIds = getPlayerAllInvolvingSubRankIds(player);
        NKErrorCode errApollo = removeOneUserApollo(uid, apolloIds, subRankIds);

        if (!errApollo.isOk()) {
            LOGGER.error("failed to remove rank scores from apollo, uid:{} apolloIds:{} err:{}", player.getUid(),
                    apolloIds, errApollo);
        }

        // remove from zset
        NKErrorCode errZSet = BackendConsole.getInstance().removeOneUserZSet(player.getUid(), apolloIds);
        if (!errZSet.isOk()) {
            LOGGER.error("failed to remove rank scores from zset, uid:{} apolloIds:{} err:{}", player.getUid(),
                    apolloIds, errZSet);
        }

        return errApollo.isOk() ? errZSet : errApollo;
    }

    private void setRankAttr(proto_RankInfoItem.Builder item, List<Integer> scores, int seasonId, long currentMs,
            boolean banned) {
        item.setHide(banned).setUpdateTimeMs(currentMs)
                .setUpdateSeason(seasonId).clearScoreFields().addAllScoreFields(scores);
    }

    private static class InstanceHolder {

        private static final BackendConsole INSTANCE = new BackendConsole();
    }
}

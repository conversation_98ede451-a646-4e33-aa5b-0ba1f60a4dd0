package com.tencent.rank.utils;

import com.google.common.collect.Maps;
import com.tencent.cache.Cache.CacheResult;
import com.tencent.cache.CacheUtil;
import com.tencent.distributedLock.DistributedLockInfo;
import com.tencent.distributedLock.DistributedLockType;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.resourceloader.resclass.MiscConfArena;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.resourceloader.resclass.RankingRuleConfData;
import com.tencent.resourceloader.resclass.RankingSnapshotRewardConfData;
import com.tencent.timiutil.coroutine.CoroHandle;
import com.tencent.timiutil.format.NKStringFormater;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.Pb2JsonUtil;
import com.tencent.wea.protocol.SrRankcache.RankCoordinationProgressReq;
import com.tencent.wea.protocol.SsRanksvr.TopRankBatchSettlementParam;
import com.tencent.wea.protocol.SsRanksvr.TopRankBatchSettlementParamOrBuilder;
import com.tencent.wea.protocol.SsRanksvr.TopRankCoordinationType;
import com.tencent.wea.protocol.SsRanksvr.TopRankSettlementParam;
import com.tencent.wea.protocol.SsRanksvr.TopRankSettlementParamOrBuilder;
import com.tencent.wea.protocol.SsRanksvr.TopRankZSetSettlementParam;
import com.tencent.wea.protocol.SsRanksvr.TopRankZSetSettlementParamOrBuilder;
import com.tencent.wea.protocol.common.GeoLevel;
import com.tencent.wea.protocol.common.KeyValueInt32;
import com.tencent.wea.protocol.common.RankId;
import com.tencent.wea.protocol.common.RankSeasonInfo;
import com.tencent.wea.protocol.common.TopRankBackendType;
import com.tencent.wea.protocol.common.TopRankJointInfo;
import com.tencent.wea.protocol.common.TopRankJointInfoOrBuilder;
import com.tencent.wea.xlsRes.ResCommon.DailyTimeInfo;
import com.tencent.wea.xlsRes.ResRanking.RankRewardRange;
import com.tencent.wea.xlsRes.ResRanking.RankingBatchSnapshotConf;
import com.tencent.wea.xlsRes.ResRanking.RankingSnapshotRewardConf;
import com.tencent.wea.xlsRes.keywords.RankRule;
import com.tencent.wea.xlsRes.keywords.TconndApiAccount;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RankSettlementUtils {

    private static final Logger LOGGER = LogManager.getLogger(RankQualifyScoreUtils.class);

    private static final Map<RankRule, BatchSnapshotAccess> batchAccess = new HashMap<>();

    static {
        batchAccess.put(RankRule.RR_PlayMode_MOBA, new MobaAccess());
        batchAccess.put(RankRule.RR_PlayMode_MOBA5V5, new Moba5V5Access());
    }

    public static String ofSnapshotId(int rankId, int seasonId, int sub) {
        return NKStringFormater.format("{}_{}_{}_{}", rankId, seasonId, sub == 0 ? 1 : 2, sub);
    }

    public static String ofSnapshotId(int rankId, int seasonId, int sub, long tag) {
        return NKStringFormater.format("{}_{}_{}_tag{}", rankId, seasonId, sub, tag);
    }

    public static String ofSnapshotId(RankRule rankRule, int seasonId, int subId) {
        return NKStringFormater.format("rule{}_{}_{}", rankRule.getNumber(), seasonId, subId);
    }

    public static String ofProgressKey(TopRankCoordinationType type, String snapshotId) {
        return type.getNumber() + "_" + snapshotId;
    }

    public static String ofRankListBatchField(int rankId, int geoId, long uid) {
        return NKStringFormater.format("{}_{}_{}", rankId, geoId, uid);
    }

    public static String ofRankListBatchFieldEndOfList(int rankId, int geoId) {
        return NKStringFormater.format("{}_{}_eol", rankId, geoId);
    }

    public static String ofRankListBatchFieldTotalSize(int rankId, int geoId) {
        return NKStringFormater.format("{}_{}_total", rankId, geoId);
    }

    public static boolean isRankListBatchFieldEndOfList(String field) {
        return !StringUtils.isBlank(field) && field.endsWith("_eol");
    }

    public static boolean isRankListBatchFieldTotalSize(String field) {
        return !StringUtils.isBlank(field) && field.endsWith("_total");
    }

    public static boolean useBatch(RankRule rule) {
        return batchAccess.containsKey(rule);
    }


    public static DistributedLockInfo ofDistributedLockInfo(int rankId, TopRankBackendType backend, int subId) {

        // key:  backend.subId.rankId
        // bits: [8].[16].[32]

        long key = (((long) (backend.getNumber() & 0xFF)) << 48) + (((long) (subId & 0xFFFF)) << 32) + rankId;

        return new DistributedLockInfo(DistributedLockType.DISTRIBUTED_LOCK_TYPE_RANK_SNAPSHOT,
                Framework.getInstance().getServerId(), key);
    }

    public static DistributedLockInfo ofDistributedLockInfo(TopRankSettlementParamOrBuilder param) {
        return ofDistributedLockInfo(param.getRankId().getId(), param.getBackend(), param.getRankId().getSubType());
    }

    public static DistributedLockInfo ofDistributedLockInfo(int rankRule) {
        var rule = RankRule.forNumber(rankRule);
        int rankId = RankingConfData.getInstance().getByRuleFirstOrDefault(rule, 0);

        return ofDistributedLockInfo(rankId, TopRankBackendType.TRBT_Unknown, 0);
    }

    public static DistributedLockInfo ofDistributedLockInfo(TopRankBatchSettlementParamOrBuilder param) {
        return ofDistributedLockInfo(param.getRankRule());
    }

    public static DistributedLockInfo ofDistributedLockInfo(TopRankZSetSettlementParamOrBuilder param) {
        return ofDistributedLockInfo(param.getRankId(), TopRankBackendType.TRBT_Redis_ZSet, 0);
    }

    public static NKPair<RankCoordinationProgressReq.Builder, NKErrorCode> getProgress(String snapshotId) {
        return getProgress(TopRankCoordinationType.TRCT_Settlement, snapshotId);
    }

    public static NKPair<RankCoordinationProgressReq.Builder, NKErrorCode> getProgress(TopRankCoordinationType type,
            String snapshotId) {
        String progressKey = ofProgressKey(type, snapshotId);

        CacheResult<RankCoordinationProgressReq.Builder> res = CacheUtil.RankListCoordinationProgress.getCachePb(
                progressKey);

        if (res.val == null) {
            LOGGER.error("settlement progress not found, snapshotId:{}, progressKey:{}", snapshotId, progressKey);
            return new NKPair<>(null, NKErrorCode.RankCoordinationProgressNotFound);
        }

        if (!res.isOk()) {
            LOGGER.error("failed to get settlement progress, snapshotId:{}, progressKey:{}", snapshotId, progressKey);
            return new NKPair<>(null, NKErrorCode.RedisOpFail);
        }

        return new NKPair<>(res.val, NKErrorCode.OK);
    }

    public static boolean getProgressKeyValueBoolean(RankCoordinationProgressReq.Builder progress, String key,
            boolean defaultValue) {
        String value = getProgressKeyValue(progress, key).orElse("");
        if (value.isBlank()) {
            return defaultValue;
        }

        try {
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            LOGGER.error("failed to parse value, str:{}", value);
            return defaultValue;
        }
    }

    public static int getProgressKeyValueInt(RankCoordinationProgressReq.Builder progress, String key,
            int defaultValue) {
        String value = getProgressKeyValue(progress, key).orElse("");
        if (value.isBlank()) {
            return defaultValue;
        }

        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            LOGGER.error("failed to parse value, str:{}", value);
            return defaultValue;
        }
    }

    public static long getProgressKeyValueLong(RankCoordinationProgressReq.Builder progress, String key,
            long defaultValue) {
        String value = getProgressKeyValue(progress, key).orElse("");
        if (value.isBlank()) {
            return defaultValue;
        }

        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            LOGGER.error("failed to parse value, str:{}", value);
            return defaultValue;
        }
    }

    public static String getProgressKeyValue(RankCoordinationProgressReq.Builder progress, String key,
            String defaultValue) {
        return getProgressKeyValue(progress, key).orElse(defaultValue);
    }

    private static Optional<String> getProgressKeyValue(RankCoordinationProgressReq.Builder progress, String key) {
        for (var kv : progress.getInfosList()) {
            if (key.equals(kv.getKey())) {
                try {
                    return Optional.of(kv.getValue());
                } catch (Exception e) {
                    LOGGER.error("failed to parse kv, kv:{}", Pb2JsonUtil.getPbMsg(kv));
                }
            }
        }

        return Optional.empty();
    }

    public static RankRewardRange findRewardRangeConf(RankingSnapshotRewardConf rewardConf, int rankNo, int size) {
        size = regularizeSettlementRankSize(rewardConf, size);
        if (size == 0) {
            LOGGER.error("zero size is not prepared for reward, reward:{} size:{}", rewardConf.getId(), size);
            return null;
        }

        RankRewardRange closed = null;
        RankRewardRange open = null;

        for (var conf : rewardConf.getRewardRangeList()) {
            int fromRankNo = conf.getFromIndex();
            if (fromRankNo == 0 && conf.getFromPercent() > 0) {
                fromRankNo = size * conf.getFromPercent() / 100;
            }

            int toRankNo = conf.getToIndex();
            if (toRankNo == 0 && conf.getToPercent() > 0) {
                toRankNo = size * conf.getToPercent() / 100;
            }

            // it is an open reward range
            if (toRankNo == 0) {
                LOGGER.debug("find open reward conf, reward:{} priority:{}", rewardConf.getId(), conf.getPriority());
                open = conf;
                continue;
            }

            // it is a closed reward range

            if (fromRankNo > toRankNo) {
                LOGGER.debug("invalid segment, reward:{} priority:{} from:{} to:{} size:{}", rewardConf.getId(),
                        conf.getPriority(), fromRankNo, toRankNo, size);
                continue;
            }

            if (rankNo < fromRankNo || toRankNo < rankNo) {
                continue;
            }

            if (closed == null || closed.getPriority() > conf.getPriority()) {
                LOGGER.debug("find closed reward conf, reward:{} priority:{} size:{}", rewardConf.getId(),
                        conf.getPriority(), size);
                closed = conf;
            }
        }

        return closed == null ? open : closed;
    }

    public static List<NKPair<RankRewardRange, NKPair<Integer, Integer>>> viewRewardRangeConf(
            RankingSnapshotRewardConf rewardConf, int size) {

        size = regularizeSettlementRankSize(rewardConf, size);

        List<NKPair<RankRewardRange, NKPair<Integer, Integer>>> res = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            if (i % 200 == 0) {
                try {
                    CoroHandle.sleep(20);
                } catch (InterruptedException e) {
                    LOGGER.error("");
                }
            }

            var conf = findRewardRangeConf(rewardConf, i, size);
            if (conf == null) {
                continue;
            }

            if (res.isEmpty() || !res.get(res.size() - 1).key.equals(conf)) {
                res.add(new NKPair<>(conf, new NKPair<>(i, i)));
                continue;
            }

            res.get(res.size() - 1).value.value = i;
        }

        var conf = findRewardRangeConf(rewardConf, 0, size);
        if (conf != null) {
            res.add(new NKPair<>(conf, new NKPair<>(0, 0)));
        }

        return res;
    }

    protected static long parseToSec(long currentSec, DailyTimeInfo dailyTimeInfo) {
        long startSec = DateUtils.getDayBeginTimeMs(currentSec * 1000) / 1000;
        return startSec + dailyTimeInfo.getHour() * DateUtils.ONE_HOUR_SECONDS + dailyTimeInfo.getMinute() * 60L
                + dailyTimeInfo.getSecond();
    }

    private static int ofDailyZSetSnapshotTag(long currentMs) {
        int year = DateUtils.getYear(currentMs) % 1_0000;
        int month = DateUtils.getMonth(currentMs) % 100;
        int day = DateUtils.getDayOfMonth(currentMs) % 100;

        return year * 1_0000 + month * 100 + day;
    }

    public static int ofDailyZSetSnapshotTag(long currentMs, DailyTimeInfo dailyTimeInfo) {
        long dailySec = parseToSec(currentMs / 1000, dailyTimeInfo);
        if (currentMs / 1000 >= dailySec) {
            return ofDailyZSetSnapshotTag(currentMs);
        }

        return ofDailyZSetSnapshotTag(DateUtils.getDayBeginTimeMs(currentMs) - 1000L);
    }

    public static long ofDailyZSetSnapshotExpireMs(long currentMs, DailyTimeInfo dailyTimeInfo,
            RankSeasonInfo seasonInfo) {
        long expireMs = DateUtils.getDayEndTimeMs(currentMs) + dailyTimeInfo.getHour() * DateUtils.ONE_HOUR_MILLIS
                + dailyTimeInfo.getMinute() * DateUtils.ONE_MINUTE_MILLIS
                + dailyTimeInfo.getSecond() * DateUtils.ONE_SECOND_MILLIS;

        long delayMs = PropertyFileReader.getRealTimeLongItem("rank_list_snap_daily_delay_expire_min", 70)
                * DateUtils.ONE_MINUTE_MILLIS;
        expireMs = expireMs + delayMs;

        if (seasonInfo.hasEndSec() && expireMs > seasonInfo.getEndSec() * 1000) {
            return seasonInfo.getEndSec() * 1000;
        }

        return expireMs;
    }


    public static NKErrorCode checkSnapshotAccess(int rankId, int seasonId) {
        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            LOGGER.debug("season conf not found, rankId:{} seasonId:{}", rankId, seasonId);
            return NKErrorCode.ResNotFound;
        }

        var rewardConf = getSnapshotRewardConf(rankId, seasonId);
        if (rewardConf == null) {
            LOGGER.debug("reward conf not found, rankId:{} seasonId:{}", rankId, seasonId);
            return NKErrorCode.ResNotFound;
        }

        seasonId = seasonConf.getId();
        long currentSec = DateUtils.currentTimeSec();

        int delaySec = PropertyFileReader.getRealTimeIntItem("rank_list_snapshot_access_delay_min", 30) * 60;
        long rewardSec = seasonConf.hasRewardSec() ? seasonConf.getRewardSec() : (seasonConf.getEndSec() + delaySec);

        if (currentSec < rewardSec) {
            LOGGER.debug("reward sec not reach, rankId:{} seasonId:{} rewardSec:{} sec:{}", rankId, seasonId,
                    seasonConf.getRewardSec(), currentSec);
            return NKErrorCode.RankRewardNotReachTime;
        }

        int intervalDays = DateUtils.getDayInterval(seasonConf.getEndSec() * 1000, DateUtils.currentTimeMillis());
        if (rewardConf.getExpireDays() > 0 && intervalDays > rewardConf.getExpireDays()) {
            LOGGER.debug("settlement expired, rankId:{} seasonId:{}", rankId, seasonId);
            return NKErrorCode.RankSettlementExpire;
        }

        return NKErrorCode.OK;
    }

    public static NKErrorCode checkBatchSnapshotAccess(RankRule rule, int seasonId) {
        var rankIds = RankingConfData.getInstance().getByRule(rule);
        if (rankIds.isEmpty()) {
            LOGGER.debug("rank id not found, rule:{} seasonId:{}", rule, seasonId);
            return NKErrorCode.ResNotFound;
        }

        var seasonConf = RankIdSeasonIdMapper.ofId(rankIds.get(0), seasonId);
        if (seasonConf == null) {
            LOGGER.debug("season conf not found, rule:{} seasonId:{}", rule, seasonId);
            return NKErrorCode.ResNotFound;
        }

        seasonId = seasonConf.getId();
        long currentSec = DateUtils.currentTimeSec();
        if (currentSec < seasonConf.getEndSec()) {
            LOGGER.error("season not end, rule:{} season:{}", rule, seasonId);
            return NKErrorCode.RankRewardNotReachTime;
        }

        var access = batchAccess.get(rule);
        if (access == null) {
            access = tryGetGeneralAccess(rule);
            if (access == null) {
                LOGGER.debug("with geo snapshot access denied, rule:{}", rule);
                return NKErrorCode.RankSettlementAccessDenied;
            }

            LOGGER.debug("using general access control, rule:{}", rule);
        }

        long elapseSec = currentSec - seasonConf.getEndSec();
        int intervalDays = DateUtils.getDayInterval(seasonConf.getEndSec() * 1000, DateUtils.currentTimeMillis());

        if (elapseSec < access.delaySec(seasonConf)) {
            LOGGER.debug("reward sec not reach, rule:{} seasonId:{} rewardSec:{} sec:{}", rule, seasonId,
                    seasonConf.getRewardSec(), currentSec);
            return NKErrorCode.RankRewardNotReachTime;
        }

        if (intervalDays > access.expireDays(seasonConf)) {
            LOGGER.debug("settlement expired, rankId:{} seasonId:{}", rule, seasonId);
            return NKErrorCode.RankSettlementExpire;
        }

        return NKErrorCode.OK;
    }


    public static RankingSnapshotRewardConf getSnapshotRewardConf(int rankId, int seasonId) {
        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf == null) {
            LOGGER.error("season conf not found, rankId:{} seasonId:{}", rankId, seasonId);
            return null;
        }

        seasonId = seasonConf.getId();
        var rankingConf = RankingConfData.getInstance().get(rankId);
        if (rankingConf == null) {
            LOGGER.error("rank conf not found, rankId:{}", rankId);
            return null;
        }

        for (var conf : RankingSnapshotRewardConfData.getInstance().getByRankIds(rankId)) {
            if (seasonId < conf.getFromSeason()) {
                continue;
            }

            if (conf.hasToSeason() && conf.getToSeason() < seasonId) {
                continue;
            }

            return conf;
        }

        return null;
    }

    public static int regularizeSettlementRankSize(RankingSnapshotRewardConf conf, int actualSize) {
        if (conf == null) {
            return actualSize;
        }

        if (conf.hasMaxSize() && conf.getMaxSize() < actualSize) {
            return conf.getMaxSize();
        }

        if (conf.hasMinSize() && actualSize < conf.getMinSize()) {
            return conf.getMinSize();
        }

        return actualSize;
    }

    public static TopRankSettlementParam.Builder ofSnapshotParam(int rankId, int seasonId, int capacity,
            TopRankBackendType backend, int expireDays) {
        TopRankSettlementParam.Builder builder = TopRankSettlementParam.newBuilder().setImageData(useImage())
                .setBackend(backend).setFromIdx(1)
                .setCount(capacity).setExpireDays(expireDays)
                .setSnapshotId(RankSettlementUtils.ofSnapshotId(rankId, seasonId, 0));

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(rankId);
        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf != null) {
            rankIdBuilder.setApolloId(
                    RankIdApolloIdMapper.getApolloId(rankId, TconndApiAccount.TCONND_ITOP_CHANNEL_WX, seasonConf));
            builder.setSeasonId(seasonConf.getId());
            seasonId = seasonConf.getId();
        }

        builder.setRankId(rankIdBuilder).setSeasonId(seasonId);
        return builder;
    }

    public static TopRankSettlementParam.Builder ofClubSnapshotParam(int rankId, int seasonId,
            TopRankBackendType backend, int sub, int capacity) {
        TopRankSettlementParam.Builder builder = TopRankSettlementParam.newBuilder().setImageData(useImage())
                .setFromIdx(1).setSnapshotId(RankSettlementUtils.ofSnapshotId(rankId, seasonId, sub))
                .setBackend(backend).setCount(capacity);

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(rankId);
        if (sub != 0) {
            rankIdBuilder.setSubType(sub);
        }

        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf != null) {
            rankIdBuilder.setApolloId(
                    RankIdApolloIdMapper.getApolloId(rankId, TconndApiAccount.TCONND_ITOP_CHANNEL_WX, seasonConf));
            builder.setSeasonId(seasonConf.getId());
            seasonId = seasonConf.getId();
        }

        builder.setRankId(rankIdBuilder).setSeasonId(seasonId);
        return builder;
    }

    public static TopRankSettlementParam.Builder ofPlatDataSyncParam(int rankId, int sub, int capacity) {
        TopRankBackendType backend =
                sub == 0 ? TopRankBackendType.TRBT_Apollo_Trank : TopRankBackendType.TRBT_Apollo_TopNext;

        var seasonInfo = RankIdSeasonIdMapper.ofNow(rankId);
        int seasonId = seasonInfo == null ? 0 : seasonInfo.getId();

        TopRankSettlementParam.Builder builder = TopRankSettlementParam.newBuilder().setImageData(useImage())
                .setFromIdx(1).setSnapshotId(RankSettlementUtils.ofSnapshotId(rankId, seasonId, sub))
                .setBackend(backend).setCount(capacity).setSeasonId(seasonId);

        RankId.Builder rankIdBuilder = RankId.newBuilder().setId(rankId)
                .setApolloId(RankIdApolloIdMapper.getApolloId(rankId, RankIdSeasonIdMapper.ofId(rankId, seasonId)));

        if (sub != 0) {
            rankIdBuilder.setSubType(sub);
        }

        builder.setRankId(rankIdBuilder);
        return builder;
    }

    public static TopRankBatchSettlementParam.Builder ofBatchSnapshotParam(RankRule rankRule, int seasonId,
            Map<GeoLevel, Integer> capacities, int expireDays, int geoSetId) {

        var builder = TopRankBatchSettlementParam.newBuilder().setRankRule(rankRule.getNumber()).setSeasonId(seasonId)
                .setExpireDays(expireDays).setGeoSetId(geoSetId)
                .setSnapshotId(RankSettlementUtils.ofSnapshotId(rankRule, seasonId, geoSetId));

        capacities.forEach((k, v) -> builder.addLevels(k.getNumber()).addCapacities(v));
        return builder;
    }

    public static TopRankZSetSettlementParam.Builder ofDailyZSetSnapshotParam(int rankId, int seasonId, long tag,
            int capacity, int expireDays) {

        var builder = TopRankZSetSettlementParam.newBuilder().setRankId(rankId).setFromIdx(1).setCount(capacity)
                .setExpireDays(expireDays);

        var seasonConf = RankIdSeasonIdMapper.ofId(rankId, seasonId);
        if (seasonConf != null) {
            builder.setApolloId(
                    RankIdApolloIdMapper.getApolloId(rankId, TconndApiAccount.TCONND_ITOP_CHANNEL_WX, seasonConf));
            builder.setSeasonId(seasonConf.getId());
            seasonId = seasonConf.getId();
        }

        builder.setSeasonId(seasonId);
        builder.setSnapshotId(ofSnapshotId(rankId, seasonId, 0, tag));

        return builder;
    }

    private static boolean useImage() {
        return PropertyFileReader.getRealTimeBooleanItem("rank_list_snapshot_image", false);
    }

    public static List<TopRankJointInfo> mergeJointList(List<? extends TopRankJointInfoOrBuilder> original) {

        Set<Integer> rankIds = new HashSet<>();
        Map<Integer, Integer> global = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> geo = new HashMap<>();

        for (var brief : original) {
            if (brief.getGlobalRankNo() != 0) {
                rankIds.add(brief.getRankId());
                global.put(brief.getRankId(), brief.getGlobalRankNo());
            }

            if (brief.getGeoRankNoCount() == 0) {
                continue;
            }

            rankIds.add(brief.getRankId());
            var kvMap = geo.computeIfAbsent(brief.getRankId(), k -> new HashMap<>());
            brief.getGeoRankNoList().forEach(kv -> kvMap.put(kv.getKey(), kv.getValue()));
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("collect joint list, rankIds:{} global:{} geo:{}", rankIds, global, geo);
        }

        List<TopRankJointInfo> res = new ArrayList<>();
        for (int rankId : rankIds) {
            TopRankJointInfo.Builder builder = TopRankJointInfo.newBuilder().setRankId(rankId);
            int rankNo = global.getOrDefault(rankId, 0);
            if (rankNo != 0) {
                builder.setGlobalRankNo(rankNo);
            }

            var rankNoMap = geo.getOrDefault(rankId, Maps.newHashMap());
            rankNoMap.forEach((k, v) -> builder.addGeoRankNo(KeyValueInt32.newBuilder().setKey(k).setValue(v)));
            res.add(builder.build());
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("merge joint list, before:{} after:{}",
                    original.stream().map(Pb2JsonUtil::getPbMsg).collect(Collectors.joining(",", "[", "]")),
                    res.stream().map(Pb2JsonUtil::getPbMsg).collect(Collectors.joining(",", "[", "]")));
        }

        return res;
    }

    public interface BatchSnapshotAccess {

        long delaySec(RankSeasonInfo seasonInfo);

        int expireDays(RankSeasonInfo seasonInfo);
    }

    private static BatchSnapshotAccess tryGetGeneralAccess(RankRule rule) {
        var ruleConf = RankingRuleConfData.getInstance().get(rule);
        if (ruleConf != null && ruleConf.hasBatchSnapshot()) {
            return new GeneralAccess(ruleConf.getBatchSnapshot());
        }

        return null;
    }

    private static class MobaAccess implements BatchSnapshotAccess {

        @Override
        public long delaySec(RankSeasonInfo seasonInfo) {
            var misc = MiscConfArena.getInstance().getMiscConfArena().getRank();
            return misc.getDelayShowHour() * DateUtils.ONE_HOUR_SECONDS;
        }

        @Override
        public int expireDays(RankSeasonInfo seasonInfo) {
            var misc = MiscConfArena.getInstance().getMiscConfArena().getRank();
            return misc.getExpireDays();
        }
    }

    private static class Moba5V5Access implements BatchSnapshotAccess {

        @Override
        public long delaySec(RankSeasonInfo seasonInfo) {
            var misc = MiscConfArena.getInstance().getMiscConfArena().getRank();
            return misc.getDelayShowHour() * DateUtils.ONE_HOUR_SECONDS;
        }

        @Override
        public int expireDays(RankSeasonInfo seasonInfo) {
            var misc = MiscConfArena.getInstance().getMiscConfArena().getRank();
            return misc.getExpireDays();
        }
    }

    private static class GeneralAccess implements BatchSnapshotAccess {

        private final RankingBatchSnapshotConf conf;

        private GeneralAccess(RankingBatchSnapshotConf conf) {
            this.conf = conf;
        }

        @Override
        public long delaySec(RankSeasonInfo seasonInfo) {
            return conf.getDelayShowHours() * DateUtils.ONE_HOUR_SECONDS;
        }

        @Override
        public int expireDays(RankSeasonInfo seasonInfo) {
            return conf.getExpireDays();
        }
    }
}

package com.tencent.rank.utils;

import com.google.common.collect.Lists;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.resclass.RankingConfData;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.xlsRes.ResRanking.RankingConf;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RankIdLevelIdMapper {

    private static final Logger LOGGER = LogManager.getLogger(RankIdLevelIdMapper.class);

    private RankIdLevelIdMapper() {
    }

    public static RankingConf forRead(int levelId, int playMode) {
        return forRead(levelId, playMode, DateUtils.currentTimeSec());
    }

    public static RankingConf forRead(int levelId, int playMode, long sec) {
        return forRead(ResLoader.getResHolder(), levelId, playMode, sec);
    }

    public static RankingConf forRead(ResHolder resHolder, int levelId, int playMode, long sec) {
        var list = RankingConfData.getInLoadingInstance(resHolder).getByLevelId(levelId);
        if (list.isEmpty()) {
            LOGGER.debug("rank id not found, level:{}", levelId);
            return null;
        }

        var seasonConf = RankIdSeasonIdMapper.ofTs(resHolder, list.get(0).getRankId(), sec);
        if (seasonConf == null) {
            LOGGER.error("season not found, level:{}", levelId);
            return null;
        }

        return RankingConfData.getInLoadingInstance(resHolder)
                .getByLevelId(levelId, playMode, seasonConf.getId() <= 10);
    }

    public static RankingConf ofIdipWrite(int levelId, int playMode) {
        return RankingConfData.getInstance().getByLevelId(levelId, levelId, playMode == 0);
    }

    public static List<RankingConf> forWrite(int levelId, int playMode) {
        return forWrite(levelId, playMode, DateUtils.currentTimeSec());
    }

    public static List<RankingConf> forWrite(int levelId, int playMode, long sec) {
        return forWrite(ResLoader.getResHolder(), levelId, playMode, sec);
    }

    public static List<RankingConf> forWrite(ResHolder resHolder, int levelId, int playMode, long sec) {
        var list = RankingConfData.getInLoadingInstance(resHolder).getByLevelId(levelId);
        if (list.isEmpty()) {
            LOGGER.debug("rank id not found, level:{}", levelId);
            return Lists.newArrayList();
        }

        var seasonConf = RankIdSeasonIdMapper.ofTs(resHolder, list.get(0).getRankId(), sec);
        if (seasonConf == null) {
            LOGGER.error("season not found, level:{}", levelId);
            return Lists.newArrayList();
        }

        List<RankingConf> res = Lists.newArrayList();
        var conf = RankingConfData.getInLoadingInstance(resHolder).getByLevelId(levelId, playMode, true);
        if (conf != null) {
            res.add(conf);
        }

        if (seasonConf.getId() > 10) {
            conf = RankingConfData.getInLoadingInstance(resHolder).getByLevelId(levelId, playMode, false);
            if (conf != null) {
                res.add(conf);
            }
        }

        return res;
    }
}

package com.tencent.ugc.data;

import com.tencent.wea.protocol.common.UgcInstanceType;
import com.tencent.wea.protocol.common.UgcMapMetaInfo;
import java.util.ArrayList;
import java.util.List;

public class UgcReportData {

    public static class CoverParamData {
        public long ugcId = 0;
        public int resType = 0;
        public boolean isResPrivate = false;
        public UgcInstanceType instanceType = UgcInstanceType.CommonInstance;
        public List<UgcMapMetaInfo> mapMetaInfoList  = new ArrayList<>();
    }


    public static class CoverResultData {
        public long ugcId = 0;
        public int mainIndex = 0;
        public List<String> coverList = new ArrayList<>();
    }

    public static class UgcTlogData {

        // 创建房间时候的ugcId
        public long createRoomUgcId = 0;
        public int mapSource = 0;
        public int canvasID = 0;
        public String tabDesc = "";
        public String subTabName = "";
        public int exposePos = 0;

        @Override
        public String toString() {
            return "UgcTlogData{" +
                    "ugcId=" + createRoomUgcId +
                    ", mapSource=" + mapSource +
                    ", canvasID=" + canvasID +
                    ", tabDesc='" + tabDesc + '\'' +
                    ", subTabName='" + subTabName + '\'' +
                    ", exposePos=" + exposePos +
                    // ", platSvrMailCfgId=" + platSvrMailCfgId +
                    // ", mailSourceCfgId=" + mailSourceCfgId +
                    '}';
        }
    }
}

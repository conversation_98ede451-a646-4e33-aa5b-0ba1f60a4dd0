package com.tencent.condition.event.player.common;

import com.tencent.condition.ConditionManager;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.wea.protocol.common.EventDataId;
import com.tencent.wea.protocol.common.EventTypeId;

public class ChaseIdentityWeekRankSettlementEvent extends BaseConditionEvent {

    public ChaseIdentityWeekRankSettlementEvent(ConditionManager conditionManager) {
        super(conditionManager);
    }

    @Override
    public int getEventType() {
        return EventTypeId.ET_ChaseIdentityWeekRankSettlement_VALUE;
    }


    public ChaseIdentityWeekRankSettlementEvent setRankNo(int rankNo) {
        setIntData(EventDataId.EDT_RankNo_VALUE, rankNo);
        return this;
    }

    public int getRankNo() {
        return getIntData(EventDataId.EDT_RankNo_VALUE);
    }

    public ChaseIdentityWeekRankSettlementEvent setGeoLevel(int geoLevel) {
        setIntData(EventDataId.EDT_GeoLevel_VALUE, geoLevel);
        return this;
    }

    public int getGeoLevel() {
        return getIntData(EventDataId.EDT_GeoLevel_VALUE);
    }

}

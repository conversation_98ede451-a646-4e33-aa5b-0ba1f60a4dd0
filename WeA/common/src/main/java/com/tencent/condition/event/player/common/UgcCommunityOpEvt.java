package com.tencent.condition.event.player.common;

import com.tencent.condition.ConditionManager;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.wea.protocol.common.EventDataId;
import com.tencent.wea.protocol.common.EventTypeId;

public class UgcCommunityOpEvt extends BaseConditionEvent {

    public UgcCommunityOpEvt(ConditionManager conditionManager) {
        super(conditionManager);
    }

    public int getOpType() {
        return getIntData(EventDataId.EDT_UgcCommunityOpType.getNumber());
    }

    public UgcCommunityOpEvt setOpType(int opType) {
        setIntData(EventDataId.EDT_UgcCommunityOpType.getNumber(), opType);
        return this;
    }

    @Override
    public int getEventType() {
        return EventTypeId.ET_PlayerUgcCommunityOp.getNumber();
    }
}

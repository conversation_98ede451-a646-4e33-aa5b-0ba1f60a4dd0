package com.tencent.condition.main;

import com.tencent.condition.ConditionOperation;
import com.tencent.condition.event.BaseConditionEvent;
import com.tencent.condition.event.player.common.WholeGameProcessPointEvent;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.xlsRes.keywords.ConditionType;

public class WholeGameProcessPoint extends BaseCondition {

    @Override
    public int getType() {
        return ConditionType.ConditionType_WholeGameProcessPoint_VALUE;
    }

    @SubscribeEvent
    private void onEvent(WholeGameProcessPointEvent event) throws NKRuntimeException {
        super.handleEvent(event);
    }

    @Override
    protected void handleCondition(BaseConditionEvent event, ConditionOperation condition) {
        WholeGameProcessPointEvent eventData = (WholeGameProcessPointEvent) event;
        if (checkSubCondition(eventData, condition.getSubConditionList())) {
            condition.setValue(eventData.getWholeGameProcessPoint());
        }
    }
}

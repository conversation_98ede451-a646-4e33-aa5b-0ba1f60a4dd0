package com.tencent.condition.main;

import com.tencent.condition.event.player.common.UgcCommunityOpEvt;
import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.wea.xlsRes.keywords.ConditionType;

public class UgcCommunityPublishNoteCond extends BaseCondition {

    @Override
    public int getType() {
        return ConditionType.ConditionType_PlayerUgcCommunityPublishNote.getNumber();
    }

    @SubscribeEvent
    private void onEvent(UgcCommunityOpEvt event) throws NKRuntimeException {
        if (event.getOpType() != 1) {
            return;
        }
        handleEvent(event);
    }
}

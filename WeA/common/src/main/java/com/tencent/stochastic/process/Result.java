package com.tencent.stochastic.process;

import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.stochastic.process.attrwrapper.AttrWrapper;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class Result<T> {

    private static final Logger LOGGER = LogManager.getLogger(Result.class);

    public final List<Integer> seq;
    public final NKErrorCode errorCode;
    private final AttrWrapper<T> wrapper;

    protected Result(List<Integer> seq, NKErrorCode errorCode, AttrWrapper<T> wrapper) {
        this.seq = Collections.unmodifiableList(seq);
        this.errorCode = errorCode;
        this.wrapper = wrapper;
    }

    public static <T> Result<T> ofFailure(NKErrorCode errorCode) {
        LOGGER.error("stochastic result failure, err:{}", errorCode);
        return new Result<>(Lists.newArrayList(), errorCode, null);
    }

    public static <T> Result<T> ofSuccess(List<Integer> seq, AttrWrapper<T> wrapper) {
        return new Result<>(seq, NKErrorCode.OK, wrapper);
    }


    /**
     * 接受认可该结果
     *
     * @return {@link NKErrorCode}
     */
    public NKErrorCode accept() {
        if (errorCode.hasError()) {
            LOGGER.error("stochastic result has error thereby should not accept, err:{}", errorCode);
            return errorCode;
        }

        return wrapper.merge();
    }

    public T internalBeginState() {
        return wrapper.getOrigin();
    }

    public T internalAfterState() {
        return wrapper.getCheckpoint();
    }

    public ByteString internalStateDifference() {
        return wrapper.delta();
    }

    public AttrWrapper<T> getWrapper() {
        return wrapper;
    }

    public boolean hasError() {
        return errorCode.hasError();
    }

    public boolean isOk() {
        return errorCode.isOk();
    }

    @Override
    public String toString() {
        return errorCode.hasError() ? errorCode.name : Objects.toString(seq);
    }

}

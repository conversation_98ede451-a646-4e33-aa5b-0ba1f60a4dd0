package com.tencent.recommend;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.tencent.cl5.PolarisUtil;
import com.tencent.coHttp.CoHttpClient;
import com.tencent.coHttp.CoHttpClientBuilder;
import com.tencent.http.HttpApi;
import com.tencent.http.HttpApi.Response;
import com.tencent.http.HttpApi.StateName;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.wea.protocol.CsRelation;
import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.Header;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

public class RecentBattlePlayerMgr {
    private static final Logger LOGGER = LogManager.getLogger(RecentBattlePlayerMgr.class);

//    private static CoHttpClient client;

    private static boolean platRecentMatchPlayerSwitch;
    private static String platRecentMatchPlayerUrl;
    private static String platRecentMatchPlayerEnv;
    private static String gatewayIdentifyKey;
    private static String gatewayIdentifyVal;

    private static final BasicResponseHandler BASIC_RESPONSE_HANDLER = new BasicResponseHandler();

    private final Gson gson = new Gson();

    RecentBattlePlayerMgr() {
//        client = CoHttpClientBuilder.
//                create(2).
//                setMaxConnPerRoute(100).
//                setMaxConnTotal(200).
//                build();
        reload();
    }

    public static RecentBattlePlayerMgr getInstance() {
        return RecentBattlePlayerMgr.InstanceHolder.instance;
    }

    public void reload() {

        platRecentMatchPlayerSwitch = PropertyFileReader.getRealTimeBooleanItem("plat_recent_match_player_switch", true);
        platRecentMatchPlayerUrl = PropertyFileReader.getRealTimeItem("plat_recent_match_player_url", "192002628:651782");
        platRecentMatchPlayerEnv = PropertyFileReader.getRealTimeItem("plat_recent_match_player_env", "Production");
        gatewayIdentifyKey = PropertyFileReader.getRealTimeItem("plat_recent_match_player_gateway_identify_key", "X-Ntm-Destination-Service");
        gatewayIdentifyVal = PropertyFileReader.getRealTimeItem("plat_recent_match_player_gateway_identify_value", "yuanmeng-recent-match-player-test");
    }

    /**
     * @param uid
     * @param openId
     * @param battleType 请求的玩法,如果是在大厅，传-1
     *
     * @return
     */
    public RecentBattlePlayerRspParams reqRecentMatchPlayer(Long uid, String openId, int battleType) {
        if (!platRecentMatchPlayerSwitch) {
            LOGGER.debug("platRecentMatchPlayerSwitch:{}", platRecentMatchPlayerSwitch);
            return null;
        }
//        NKPair<String, Integer> hostInfo = PolarisUtil.discover(platRecentMatchPlayerUrl, platRecentMatchPlayerEnv);
//        if (hostInfo == null) {
//            LOGGER.debug("RecentBattlePlayerMgr discover fail, {}-{}", platRecentMatchPlayerUrl,platRecentMatchPlayerEnv);
//            NKErrorCode.PolarisDiscoveryFail.throwError("RecentBattlePlayerMgr discover fail, {}-{}", platRecentMatchPlayerUrl,
//                    platRecentMatchPlayerEnv);
//            return null;
//        }
//        String url = "http://" + hostInfo.getKey() + ":" + hostInfo.getValue();
//        URI uri;
//        try {
//            uri = new URIBuilder(url).build();
//            HttpPost httpPost = new HttpPost(uri);
//            httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
//            httpPost.addHeader(gatewayIdentifyKey,gatewayIdentifyVal);

        JSONObject postBody = new JSONObject();
        if (PropertyFileReader.getRealTimeBooleanItem("plat_recent_match_player_param_only_uid", true)) {
            postBody.put("uid", String.format("%d", uid));
        } else {
            postBody.put("uid", String.format("%s#%d", openId, uid));
        }
        postBody.put("svrid", String.valueOf(Framework.getInstance().getWorldId()));
        postBody.put("battle_type", String.valueOf(battleType));

//            httpPost.setEntity(new StringEntity(postBody.toString()));
//            LOGGER.debug("http post: {}", httpPost);
//            StringBuilder headerStr = new StringBuilder();
//            for (Header header : httpPost.getAllHeaders()) {
//                headerStr.append(header.getName()).append(": ").append(header.getValue()).append(" ");
//            }
//            LOGGER.debug("do post url:{}, header:{} content:{}", httpPost.getURI(), headerStr.toString(), EntityUtils.toString(httpPost.getEntity()));

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json;charset=UTF-8");
        headerMap.put(gatewayIdentifyKey, gatewayIdentifyVal);

        Response response = HttpApi.requestPostForPolaris(StateName.RecentMatchPlayer.name(),
                platRecentMatchPlayerUrl,
                platRecentMatchPlayerEnv, "", postBody.toString(), headerMap);
        if (!response.isOk()) {
            LOGGER.error("reqRecentMatchPlayer uri init fail ret={}", response.getErrorCode());
            return null;
        }

//            String response = client.execute(httpPost, BASIC_RESPONSE_HANDLER);
        LOGGER.debug("http response:{}", response);
        return parseHttpResult(response.getBody());
//        } catch (java.net.URISyntaxException ex) {
//            LOGGER.error("uri init fail {}", ex.toString());
//            NKErrorCode.ReqRecentMatchPlayerFail.throwError("uri init fail");
//        } catch (IOException ex) {
//            LOGGER.error("send req fail {}", ex.toString());
//            NKErrorCode.ReqRecentMatchPlayerFail.throwError("uri init fail");
//        } catch (Exception e) {
//            LOGGER.error("send req fail ", e);
//        }
//        return null;
    }

    private RecentBattlePlayerRspParams parseHttpResult(String response) {
        JsonElement jsonElement = gson.fromJson(response, JsonElement.class);
        if (jsonElement.isJsonObject()) {
            JsonObject obj = jsonElement.getAsJsonObject();
            String recId = obj.get("rec_id").getAsString();
            String algoType = obj.get("algo_type").getAsString();
            JsonArray items = obj.get("rec_list").getAsJsonArray();
            RecentBattlePlayerRspParams rspParams = new RecentBattlePlayerRspParams(recId,algoType);
            for (JsonElement element : items) {
                JsonObject item = element.getAsJsonObject();
                CsRelation.RecentBattlePlayer.Builder recentBattlePlayer = CsRelation.RecentBattlePlayer.newBuilder();
                recentBattlePlayer.setUid(item.get("uid").getAsLong());
                recentBattlePlayer.setReasonId(item.get("reason_id").getAsInt());
                recentBattlePlayer.setReasonText(item.get("reason_text").getAsString());
                rspParams.addRecentBattlePlayer(recentBattlePlayer);
            }
            return rspParams;
        } else {
            LOGGER.error("parseHttpResult err, response:{}", response);
            return null;
        }
    }


    private static class InstanceHolder {
        public static RecentBattlePlayerMgr instance = new RecentBattlePlayerMgr();
    }
}

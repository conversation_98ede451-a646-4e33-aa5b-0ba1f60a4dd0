package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResChestMisc;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;



public class ChestRankMiscConf extends ResTable<ResChestMisc.ChestMiscConfig> {



    private static final Logger LOGGER = LogManager.getLogger(ChestRankMiscConf.class);



    Map<Integer, ResChestMisc.ChestMiscConfig> internalDataMap;



    public ChestRankMiscConf() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResChestMisc.ChestMiscConfig.newBuilder());

    }



    public static ChestRankMiscConf getInstance() {

        return (ChestRankMiscConf) ResLoader.getResHolder().getResTableInstance("ChestRankMiscConf");

    }



    public static ChestRankMiscConf getInLoadingInstance(ResHolder resHolder) {

        return (ChestRankMiscConf) resHolder.allResMap.get("ChestRankMiscConf");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResChestMisc.ChestMiscConfig.class;

    }



    public ResChestMisc.ChestMiscConfig get(Integer id)

    {

        return Optional.ofNullable(internalDataMap.get(id))

        .orElse(null);

    }

}


package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.timiutil.time.DateUtils;
import com.tencent.wea.xlsRes.ResChestBackpackTagOuterClass.ChestBagMiscConfig;
import com.tencent.wea.xlsRes.ResChestMisc;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import com.tencent.wea.xlsRes.ResChestMisc.ChestMiscConfig;
import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;


public class ChestMiscConf extends ResTable<ResChestMisc.ChestMiscConfig> {



    private static final Logger LOGGER = LogManager.getLogger(ChestMiscConf.class);


    Map<Integer, ResChestMisc.ChestMiscConfig> internalDataMap;



    public ChestMiscConf() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResChestMisc.ChestMiscConfig.newBuilder());

    }



    public static ChestMiscConf getInstance() {

        return (ChestMiscConf) ResLoader.getResHolder().getResTableInstance("ChestMiscConf");

    }



    public static ChestMiscConf getInLoadingInstance(ResHolder resHolder) {

        return (ChestMiscConf) resHolder.allResMap.get("ChestMiscConf");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResChestMisc.ChestMiscConfig.class;

    }


    public ResChestMisc.ChestMiscConfig get(Integer id)

    {

        return Optional.ofNullable(internalDataMap.get(id))

        .orElse(null);

    }

    public ChestMiscConfig getChestMiscConfig() {

        return get(1);

    }

    public long getChestTitleRewardExpireTime(long currentTimeMs) {
        String chestWeeklyTitleRewardExpireInfoStr = ChestMiscConf.getInstance().getChestMiscConfig()
                .getChestWeeklyTitleExpireInfo();
        if (chestWeeklyTitleRewardExpireInfoStr.isEmpty()) {
            return currentTimeMs;
        }
        String[] rewardConf = chestWeeklyTitleRewardExpireInfoStr.split(",");
        if (rewardConf.length < 2) {
            LOGGER.error("getChestTitleRewardExpireTime format error:{}", chestWeeklyTitleRewardExpireInfoStr);
            return currentTimeMs;
        }
        int rewardWeek = Integer.parseInt(rewardConf[0].trim());
        String[] rewardExpireTimeStr = rewardConf[1].trim().split(":");
        if (rewardExpireTimeStr.length != 3) {
            LOGGER.error("getChestTitleRewardExpireTime format error:{}", chestWeeklyTitleRewardExpireInfoStr);
            return currentTimeMs;
        }
        int rewardHour = Integer.parseInt(rewardExpireTimeStr[0].trim());
        int rewardMinute = Integer.parseInt(rewardExpireTimeStr[1].trim());
        int rewardSecond = Integer.parseInt(rewardExpireTimeStr[2].trim());
        long rewardExpireTime = DateUtils.getThisWeekMsBy(DateUtils.currentTimeMillis(), rewardWeek, rewardHour,
                rewardMinute, rewardSecond);
        return rewardExpireTime;
    }

    public long getChestTitleRewardTime() {
        String chestWeeklyTitleRewardInfoStr = ChestMiscConf.getInstance().getChestMiscConfig()
                .getChestWeeklyTitleRewardTime();
        if (chestWeeklyTitleRewardInfoStr.isEmpty()) {
            return 0;
        }
        String[] rewardConf = chestWeeklyTitleRewardInfoStr.split(",");
        if (rewardConf.length < 2) {
            LOGGER.error("getChestTitleRewardExpireTime format error:{}", chestWeeklyTitleRewardInfoStr);
            return 0;
        }
        int rewardWeek = Integer.parseInt(rewardConf[0].trim());
        String[] rewardExpireTimeStr = rewardConf[1].trim().split(":");
        if (rewardExpireTimeStr.length != 3) {
            LOGGER.error("getChestTitleRewardExpireTime format error:{}", chestWeeklyTitleRewardInfoStr);
            return 0;
        }
        int rewardHour = Integer.parseInt(rewardExpireTimeStr[0].trim());
        int rewardMinute = Integer.parseInt(rewardExpireTimeStr[1].trim());
        int rewardSecond = Integer.parseInt(rewardExpireTimeStr[2].trim());
        long rewardTime = DateUtils.getThisWeekMsBy(DateUtils.currentTimeMillis(), rewardWeek, rewardHour,
                rewardMinute, rewardSecond);
        return rewardTime;
    }

}


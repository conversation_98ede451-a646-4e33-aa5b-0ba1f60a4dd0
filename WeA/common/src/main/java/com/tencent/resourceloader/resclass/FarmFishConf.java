
package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.wea.protocol.common.FarmSpecialFishType;
import com.tencent.wea.xlsRes.ResFarmFish;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;
import kotlin.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class FarmFishConf extends ResTable<ResFarmFish.FarmFishConf> {

    private static final Logger LOGGER = LogManager.getLogger(FarmFishConf.class);

    Map<Integer, ResFarmFish.FarmFishConf> internalDataMap;
    private Map<Pair<Integer, Integer>, ArrayList<Pair<Integer, Integer>>> fishInPool = new HashMap<>(); // key是pair<layer,bait>, value是<fishId,基础权重>
    private Map<Integer, ArrayList<Integer>> fishInLayer = new HashMap<>(); // 每一层有什么鱼
    private Map<Integer, ArrayList<Integer>> normalFishInLayer = new HashMap<>(); // 每一层有什么常规鱼

    // typedef
    // 层map->大小map->鱼list（品质排序）
    static final private class LayerScaleRarityMap extends HashMap<Integer, Map<Integer, List<ResFarmFish.FarmFishConf>>> {}
    // 大小->鱼
    static final private class ScaleFishMap extends HashMap<Integer, List<ResFarmFish.FarmFishConf>>{};

    // 按鱼的品质比较
    private final static Comparator<ResFarmFish.FarmFishConf> compFishRarityDown =
            Comparator.comparingInt(ResFarmFish.FarmFishConf::getRarity).reversed();

    LayerScaleRarityMap layerScaleRarityMap;

    public FarmFishConf() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResFarmFish.FarmFishConf.newBuilder());
    }

    public static FarmFishConf getInstance() {
        return (FarmFishConf) ResLoader.getResHolder().getResTableInstance("FarmFishConf");
    }

    public static FarmFishConf getInLoadingInstance(ResHolder resHolder) {
        return (FarmFishConf) resHolder.allResMap.get("FarmFishConf");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        Map<Pair<Integer, Integer>, ArrayList<Pair<Integer, Integer>>> newFishInPool = new HashMap<>();
        Map<Integer, ArrayList<Integer>> newFishInLayer = new HashMap<>();
        Map<Integer, ArrayList<Integer>> newNoTagFishInLayer = new HashMap<>();
        for (var conf : internalDataMap.values()) {
            for (var layer : conf.getLayerList()) {
                for (var bait : conf.getBaitWithWeightList()) {
                    var key = new Pair<>(layer, bait.getId());
                    newFishInPool.putIfAbsent(key, new ArrayList<>());
                    newFishInPool.get(key).add(new Pair<>(conf.getId(), bait.getRandomWeight()));
                }
                newFishInLayer.putIfAbsent(layer, new ArrayList<>());
                newFishInLayer.get(layer).add(conf.getId());
                if(conf.getTagType() == FarmSpecialFishType.FSFT_Normal.getNumber()){
                    newNoTagFishInLayer.putIfAbsent(layer, new ArrayList<>());
                    newNoTagFishInLayer.get(layer).add(conf.getId());
                }
            }
        }
        fishInPool = newFishInPool;
        fishInLayer = newFishInLayer;
        normalFishInLayer = newNoTagFishInLayer;

        arrangeFishInLayer();
    }

    @Override
    public Class getMessageClass() {
        return ResFarmFish.FarmFishConf.class;
    }

    // 返回这个层数和鱼饵能得到的鱼+基础权重
    public static ArrayList<Pair<Integer, Integer>> getFishInPool(int layer, int bait) {
        var key = new Pair<>(layer, bait);
        return getInstance().fishInPool.get(key);
    }

    // 返回这个层数有哪些鱼 （成就用）
    public static ArrayList<Integer> getFishInLayer(int layer) {
        return getInstance().fishInLayer.get(layer);
    }

    public static ArrayList<Integer> getNormalFishInLayer(int layer) {
        return getInstance().normalFishInLayer.get(layer);
    }

    public static ResFarmFish.FarmFishConf get(Integer fishId) {
        return getInstance().internalDataMap.get(fishId);
    }

    public static int getFishRarity(Integer fishId) {
        var conf = getInstance().internalDataMap.get(fishId);
        if (conf == null) {
            return 0;
        }
        return conf.getRarity();
    }

    // 返回某层某大小的鱼，并按品质由高级到低级排序
    public static List<ResFarmFish.FarmFishConf> getFishInLayer(int layer, int scale) {
        return Optional.ofNullable(getInstance().layerScaleRarityMap.get(layer)).map(m -> m.get(scale)).orElse(null);
    }

    private void arrangeFishInLayer() {
        layerScaleRarityMap = new LayerScaleRarityMap();

        for (var entry : fishInLayer.entrySet()) {
            var layer = entry.getKey();
            var fishList = entry.getValue();

            // 大小map->鱼list
            var fishScaleMap = new ScaleFishMap();
            for (var fishID : fishList) {
                var conf = internalDataMap.get(fishID);
                if (conf.getTagType() != 0) {
                    continue; // 锦鲤等特殊鱼不能放鱼缸
                }
                fishScaleMap.computeIfAbsent(conf.getFishScale(), id->new ArrayList<>()).add(conf);
            }
            // 对每种大小，按品质降序
            for (var fishEntry : fishScaleMap.entrySet()) {
                fishEntry.getValue().sort(compFishRarityDown);
            }

            layerScaleRarityMap.put(layer, fishScaleMap);
        }
    }
}


package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.wea.xlsRes.ResActivity;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ActivityHYWarmUpCheckinData extends ResTable<ResActivity.ActivityHYWarmUpCheckinConf> {
    private static final Logger LOGGER = LogManager.getLogger(ActivityHYWarmUpCheckinData.class);

    Map<Integer, ResActivity.ActivityHYWarmUpCheckinConf> internalDataMap;

    public ActivityHYWarmUpCheckinData() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResActivity.ActivityHYWarmUpCheckinConf.newBuilder());
    }

    public static ActivityHYWarmUpCheckinData getInstance() {
        return (ActivityHYWarmUpCheckinData) ResLoader.getResHolder().getResTableInstance("ActivityHYWarmUpCheckinData");
    }

    public static ActivityHYWarmUpCheckinData getInLoadingInstance(ResHolder resHolder) {
        return (ActivityHYWarmUpCheckinData) resHolder.allResMap.get("ActivityHYWarmUpCheckinData");
    }


    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }

    @Override
    public Class getMessageClass() {
        return ResActivity.ActivityHYWarmUpCheckinConf.class;
    }

    public ResActivity.ActivityHYWarmUpCheckinConf getConfById(int key) {
        return internalDataMap.get(key);
    }
}

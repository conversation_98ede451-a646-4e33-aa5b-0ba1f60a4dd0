package com.tencent.resourceloader.resclass;


import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResChestBackpackTagOuterClass;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import com.tencent.wea.xlsRes.ResChestBackpackTagOuterClass.ChestBagMiscConfig;
import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;


import java.util.Map;

import java.util.Optional;


public class ChestBagMiscConf extends ResTable<ResChestBackpackTagOuterClass.ResChestBagMiscConf> {


    private static final Logger LOGGER = LogManager.getLogger(ChestBagMiscConf.class);


    Map<Integer, ResChestBackpackTagOuterClass.ResChestBagMiscConf> internalDataMap;


    public ChestBagMiscConf() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResChestBackpackTagOuterClass.ResChestBagMiscConf.newBuilder());

    }


    public static ChestBagMiscConf getInstance() {

        return (ChestBagMiscConf) ResLoader.getResHolder().getResTableInstance("ChestBagMiscConf");

    }


    public static ChestBagMiscConf getInstance(String version) {

        return (ChestBagMiscConf) ResLoader.getResHolder().getResTableInstance("ChestBagMiscConf");

    }


    public static ChestBagMiscConf getInLoadingInstance(ResHolder resHolder) {

        return (ChestBagMiscConf) resHolder.allResMap.get("ChestBagMiscConf");

    }


    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }


    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }


    @Override

    public Class getMessageClass() {

        return ResChestBackpackTagOuterClass.ResChestBagMiscConf.class;

    }


    public ResChestBackpackTagOuterClass.ResChestBagMiscConf get(Integer id) {

        return Optional.ofNullable(internalDataMap.get(id))

                .orElse(null);

    }

    public ChestBagMiscConfig getChestBagMiscConfig() {

        return get(1).getChestBagMiscConfig();

    }
}


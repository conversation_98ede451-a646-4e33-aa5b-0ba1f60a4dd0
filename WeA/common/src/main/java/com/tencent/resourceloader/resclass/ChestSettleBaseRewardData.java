package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.wea.xlsRes.ResChestSettlement;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.resourceloader.ResHolder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Optional;

public class ChestSettleBaseRewardData extends ResTable<ResChestSettlement.ChestSettleBaseRewardConfig> {

    private static final Logger LOGGER = LogManager.getLogger(ChestSettleBaseRewardData.class);

    Map<Integer, ResChestSettlement.ChestSettleBaseRewardConfig> internalDataMap;

    public ChestSettleBaseRewardData() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResChestSettlement.ChestSettleBaseRewardConfig.newBuilder());
    }

    public static ChestSettleBaseRewardData getInstance() {
        return (ChestSettleBaseRewardData) ResLoader.getResHolder().getResTableInstance("ChestSettleBaseRewardData");
    }

    public static ChestSettleBaseRewardData getInstance(String version) {
            return (ChestSettleBaseRewardData) ResLoader.getResHolder().getResTableInstance("ChestSettleBaseRewardData");
    }

    public static ChestSettleBaseRewardData getInLoadingInstance(ResHolder resHolder) {
        return (ChestSettleBaseRewardData) resHolder.allResMap.get("ChestSettleBaseRewardData");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public Class getMessageClass() {
        return ResChestSettlement.ChestSettleBaseRewardConfig.class;
    }

    public ResChestSettlement.ChestSettleBaseRewardConfig get(Integer id)
    {
        return Optional.ofNullable(internalDataMap.get(id))
        .orElse(null);
    }
}

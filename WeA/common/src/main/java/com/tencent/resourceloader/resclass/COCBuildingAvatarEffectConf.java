package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.wea.xlsRes.ResCOCBuild;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class COCBuildingAvatarEffectConf extends ResTable<ResCOCBuild.COCBuildingAvatarEffectConf> {

    private static final Logger LOGGER = LogManager.getLogger(COCBuildingAvatarEffectConf.class);

    Map<Integer, ResCOCBuild.COCBuildingAvatarEffectConf> internalDataMap;
    List<Integer> sortedFashionScoreKeyList;

    public COCBuildingAvatarEffectConf() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResCOCBuild.COCBuildingAvatarEffectConf.newBuilder());
    }

    public static COCBuildingAvatarEffectConf getInstance() {
        return (COCBuildingAvatarEffectConf) ResLoader.getResHolder().getResTableInstance("COCBuildingAvatarEffectConf");
    }

    public static COCBuildingAvatarEffectConf getInLoadingInstance(ResHolder resHolder) {
        return (COCBuildingAvatarEffectConf) resHolder.allResMap.get("COCBuildingAvatarEffectConf");
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        //对key进行排序，用于后续范围查找
        sortedFashionScoreKeyList = internalDataMap.keySet().stream().sorted().collect(Collectors.toList());
    }

    @Override
    public Class getMessageClass() {
        return ResCOCBuild.COCBuildingAvatarEffectConf.class;
    }

    public ResCOCBuild.COCBuildingAvatarEffectConf get(Integer fashionScore) {
        return Optional.ofNullable(internalDataMap.get(fashionScore))
                .orElse(null);
    }

    public ResCOCBuild.COCBuildingAvatarEffectConf getBuildingAvatarEffectConf(int fashionScore) {
        int keyCnt = sortedFashionScoreKeyList.size();
        if (keyCnt == 0) {
            return null;
        }
        //sortedFashionScoreKeyList {100,200,350...}
        // 前开后闭,key值代表这条配置的上限，且是闭区间，时尚分<=100的情况，读key=100的配置
        for (int i = 0; i < keyCnt; ++i) {
            int targetScoreKey = sortedFashionScoreKeyList.get(i);
            if (fashionScore <= targetScoreKey) {
                return internalDataMap.getOrDefault(targetScoreKey, null);
            }
        }
        //要配一个大于最大可能值的配置，理论上不会走到这里
        return null;
    }
}


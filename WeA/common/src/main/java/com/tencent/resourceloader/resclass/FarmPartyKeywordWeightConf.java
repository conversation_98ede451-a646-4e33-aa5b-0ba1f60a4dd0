package com.tencent.resourceloader.resclass;

import com.tencent.nk.util.Tuple;
import com.tencent.nk.util.exception.ResLoadFailException;
import com.tencent.resourceloader.ResHolder;
import com.tencent.resourceloader.ResLoader;
import com.tencent.resourceloader.ResTable;
import com.tencent.wea.xlsRes.ResFarmHot;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class FarmPartyKeywordWeightConf extends ResTable<ResFarmHot.FarmPartyKeywordWeightConf> {
    private static final Logger LOGGER = LogManager.getLogger(FarmPartyKeywordWeightConf.class);

    Map<Integer, ResFarmHot.FarmPartyKeywordWeightConf> internalDataMap;
    List<Tuple.T3<List<String>, Float, Integer>> keywordWeightList = new ArrayList<>();

    public FarmPartyKeywordWeightConf() {
        internalDataMap = dataMap;
        generateKeyMetaData(ResFarmHot.FarmPartyKeywordWeightConf.newBuilder());
    }

    public static FarmPartyKeywordWeightConf getInstance() {
        return (FarmPartyKeywordWeightConf) ResLoader.getResHolder().getResTableInstance("FarmPartyKeywordWeightConf");
    }

    public static FarmPartyKeywordWeightConf getInLoadingInstance(ResHolder resHolder) {
        return (FarmPartyKeywordWeightConf) resHolder.allResMap.get("FarmPartyKeywordWeightConf");
    }

    public static List<Tuple.T3<List<String>, Float, Integer>> getKeywordWeightList() {
        return getInstance().keywordWeightList;
    }

    @Override
    public void checker(ResHolder resHolder) throws ResLoadFailException {
    }

    @Override
    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {
        keywordWeightList.clear();
        for (var conf : internalDataMap.values()) {
            keywordWeightList.add(Tuple.New(new ArrayList<>(conf.getKeywordList()), conf.getWeight(), conf.getType()));
        }
    }

    @Override
    public Class getMessageClass() {
        return ResFarmHot.FarmPartyKeywordWeightConf.class;
    }

    public ResFarmHot.FarmPartyKeywordWeightConf get(Integer hotId)
    {
        return Optional.ofNullable(internalDataMap.get(hotId))
                .orElse(null);
    }
}

package com.tencent.drop;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.xlsRes.ResDropInfoConf;
import com.tencent.resourceloader.resclass.DropItemInfoConfig;
import com.tencent.resourceloader.resclass.DropInfoConfig;

/**
 * <AUTHOR>
 * @desc    掉落系统
 */

public abstract class BaseDropSystem {
    private static final Logger LOGGER = LogManager.getLogger(BaseDropSystem.class);
    private boolean isInit = false;
    private final Random random = new Random();
    private static final int MAX_RECURSION_DEPTH = 3;  // 最大递归深度

    // 构造参数用表数据
    public BaseDropSystem() {}

    // 子类克重写的init方法,用于一些自定义的初始化
    protected void init() {

    }

    private void tryInit() {
        if (isInit) return;
        init();
        isInit = true;
    }

    /**
     * 单次掉落计算接口
     * @param dropId    掉落配置id
     */
    public List<ItemInfo> drop(int dropId) {
        return batchDrop(dropId, 1);
    }

    /**
     * 批量掉落计算接口
     * @param dropId    掉落配置id
     * @param count     计算次数
     */
    public List<ItemInfo> batchDrop(int dropId, int count) {
        tryInit();
        List<ItemInfo> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            calculateDrop(dropId, 0, result);
        }

        printDropResult(dropId, result);
        LOGGER.debug("Batch drop calculation completed for dropId[{}] count[{}] totalItems[{}]",
                dropId, count, result.size());
        return result;
    }

    /*
     * 进行掉落计算
     * @param dropId    掉落配置id
     * @param depth     递归深度
     * */
    private void calculateDrop(int dropId, int depth, List<ItemInfo> result) {
        if (depth > MAX_RECURSION_DEPTH) {
            LOGGER.warn("Max recursion depth[{}] exceeded for dropId[{}]", MAX_RECURSION_DEPTH, dropId);
            return;
        }

        ResDropInfoConf.DropInfoConfig dropCfg = DropInfoConfig.getInstance().get(dropId);
        if (dropCfg == null) {
            LOGGER.error("DropInfoConfig not found for dropId[{}]", dropId);
            return;
        }

        if (dropCfg.getType() == 1) {
            // Process fixed drops
            for (ResDropInfoConf.FixedItemInfo fixedItem : dropCfg.getFixedItemList()) {
                result.add(buildItemInfo(fixedItem.getItemId(), fixedItem.getItemNum(), dropId));
            }
        } else if (dropCfg.getType() == 2) {
            // Process reward group drops
            for (int dropItemId : dropCfg.getDropItemIdsList()) {
                processDropItemGroup(dropItemId, depth, result);
            }
        } else {
            LOGGER.error("Unknown drop type[{}] for dropId[{}]", dropCfg.getType(), dropId);
        }
    }

    /*
     * 计算掉落的物品组
     * @param dropItemId    掉落物品组id
     * @param depth         递归深度
     * */
    private void processDropItemGroup(int dropItemId, int depth, List<ItemInfo> result) {
        Map<Integer, List<ResDropInfoConf.DropItemInfoConfig>> groupedConfigs =
                DropItemInfoConfig.getInstance().getByDropIdAndCalcType(dropItemId);
        if (groupedConfigs.isEmpty()) {
            LOGGER.error("No drop item configs found for dropItemId[{}]", dropItemId);
            return;
        }

        // 处理单独计算的掉落Item
        List<ResDropInfoConf.DropItemInfoConfig> independentConfigs = groupedConfigs.get(1);
        if (independentConfigs != null && !independentConfigs.isEmpty()) {
            processIndependentCalculation(independentConfigs, depth, result);
        }

        // 处理权重计算的掉落Item
        List<ResDropInfoConf.DropItemInfoConfig> weightedConfigs = groupedConfigs.get(2);
        if (weightedConfigs != null && !weightedConfigs.isEmpty()) {
            processWeightedCalculation(weightedConfigs, depth, result);
        }
    }

    /*
     * 每行单独计算掉落
     * @param configs    掉落物品组
     * @param depth     递归深度
     * */
    private void processIndependentCalculation(List<ResDropInfoConf.DropItemInfoConfig> configs,
                                               int depth, List<ItemInfo> result) {
        for (ResDropInfoConf.DropItemInfoConfig cfg : configs) {
            long denominator = cfg.getDenominator();
            if (denominator <= 0) {
                LOGGER.error("Invalid denominator[{}] for dropItemId[{}]", denominator, cfg.getId());
                continue;
            }

            long probability = cfg.getProbability();
            if (0 >= probability) {
                // 这里是正常情况,应策划强烈要求,允许有0概率的配置出现
                LOGGER.debug("Invalid probability[{}] for dropItemId[{}]", probability, cfg.getId());
                continue;
            }

            long randomValue = Math.abs(random.nextLong()) % denominator;
            if (randomValue < cfg.getProbability()) {
                processSingleDropItem(cfg, depth, result);
            }
        }
    }

    /*
     * 权重掉落计算
     * @param configs    掉落物品组
     * @param depth     递归深度
     * */
    private void processWeightedCalculation(List<ResDropInfoConf.DropItemInfoConfig> configs,
                                            int depth, List<ItemInfo> result) {
        // 计算总权重
        long totalWeight = 0;
        for (ResDropInfoConf.DropItemInfoConfig cfg : configs) {
            totalWeight += cfg.getProbability();
        }

        if (totalWeight <= 0) {
            LOGGER.error("Total weight[{}] is invalid for weighted calculation", totalWeight);
            return;
        }

        // 随机选择
        long randomValue = Math.abs(random.nextLong()) % totalWeight;
        long accumulated = 0;

        for (ResDropInfoConf.DropItemInfoConfig cfg : configs) {
            accumulated += cfg.getProbability();
            if (randomValue < accumulated) {
                processSingleDropItem(cfg, depth, result);
                return;
            }
        }
    }

    /*
     * 处理单次掉落的物品
     * @param cfg    掉落物品组
     * @param depth     递归深度
     * */
    private void processSingleDropItem(ResDropInfoConf.DropItemInfoConfig cfg,
                                       int depth, List<ItemInfo> result) {
        if (cfg.getDropType() == 1) {
            // Direct item drop
            int itemCount = randBetween(cfg.getMinItemCount(), cfg.getMaxItemCount());
            ItemInfo itemInfo = buildItemInfo(cfg.getItemId(), itemCount, cfg.getId());
            result.add(itemInfo);
            LOGGER.debug("Added item from dropType 1 itemId[{}] count[{}] configId[{}]",
                    itemInfo.getItemId(), itemInfo.getItemNum(), cfg.getId());            
        } else if (cfg.getDropType() == 2) {
            // calculateDrop(cfg.getCitationId(), depth + 1, result);
            processDropItemGroup(cfg.getCitationId(), depth + 1, result);
            LOGGER.debug("Processing citation drop citationId[{}] currentDepth[{}]",
                    cfg.getCitationId(), depth);
        } else {
            LOGGER.error("Unknown drop type[{}] for dropItemId[{}]", cfg.getDropType(), cfg.getId());
        }
    }

    /*
     * 构建物品信息
     * @param itemId    物品id
     * @param num       物品数量
     * @param dropId    掉落配置id
     * */
    private ItemInfo buildItemInfo(int itemId, int num, int dropId) {
        return ItemInfo.newBuilder()
                .setItemId(itemId)
                .setItemNum(num)
                .build();
    }

    // rand闭区间[min,max]
    private int randBetween(int min, int max) {
        if (max < min) {
            LOGGER.warn("max[{}] is less than min[{}] using min", max, min);
            return min;
        }
        if (max == min) return min;
        return min + random.nextInt(max - min + 1);
    }

    /*
    * 打印掉落结果
    * */
    private void printDropResult(int dropId, List<ItemInfo> dropResult) {
        if (LOGGER.isDebugEnabled()) {
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("[");

            for (int i = 0; i < dropResult.size(); i++) {
                ItemInfo item = dropResult.get(i);
                resultBuilder.append("ItemInfo{itemId=")
                        .append(item.getItemId())
                        .append(", itemNum=")
                        .append(item.getItemNum())
                        .append("}");

                if (i < dropResult.size() - 1) {
                    resultBuilder.append("|");
                }
            }

            resultBuilder.append("]");
            LOGGER.debug("Drop calculation completed for dropId[{}] result[{}]", dropId, resultBuilder.toString());
        }
    }
}

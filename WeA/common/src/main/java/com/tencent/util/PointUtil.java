package com.tencent.util;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2020/12/1 3:07 下午
 */
public class PointUtil {
    private static final Logger LOGGER = LogManager.getLogger(PointUtil.class);

    /**
     * 判断点target是否在线段p1p2上
     *
     * @param target 目标点
     * @param p1     端点
     * @param p2     端点
     * @return
     */
    public static boolean isDotInLine(int[] target, int[] p1, int[] p2) {
        int[] tp1 = new int[]{p1[0] - target[0], p1[1] - target[1]};
        int[] tp2 = new int[]{p2[0] - target[0], p2[1] - target[1]};
        if (tp1[0] * tp2[1] - tp1[1] * tp2[0] != 0) {
            return false;
        }
        return (Math.min(p1[0], p2[0]) <= target[0]) && (target[0] <= Math.max(p1[0], p2[0]))
                && (Math.min(p1[1], p2[1]) <= target[1]) && (target[1] <= Math.max(p1[1], p2[1]));
    }

    /**
     * 判断target是否在points所围成的多边形内（边和端点也算）
     *
     * @param target 目标点
     * @param points 顺时针开始，多边形端点
     * @return
     */
    public static boolean isDotInPolygon(int[] target, int[][] points) {
        boolean result = false;
        for (int i = 0; i < points.length; i++) {
            int[] p1 = points[i];
            int[] p2 = points[(i + 1) % points.length];
            if (isDotInLine(target, p1, p2)) {
                result = true;
                break;
            }
            if ((p1[1] > target[1] != p2[1] > target[1]) && (target[0] < (target[1] - p1[1]) * (p1[0] - p2[0]) / (p1[1] - p2[1]) + p1[0])) {
                result = !result;
            }
        }
        return result;
    }

    /**
     * 判断target是否在矩形内（边和端点也算）
     *
     * @param target      目标点
     * @param leftUp      左上角
     * @param rightBottom 右下角
     * @return
     */
    public static boolean isDotInRect(int[] target, int[] leftUp, int[] rightBottom) {
        return (target[0] >= leftUp[0] && target[0] <= rightBottom[0] && target[1] >= leftUp[1] && target[1] <= rightBottom[1]);
    }

    /**
     * 计算任意多边形面积
     *
     * @param points 指定顺序
     * @return
     */
    public static int areaPolygon(int[][] points) {
        //至少三个点才可围成一多边形
        if (points.length <= 3) {
            return 0;
        }
        int area = 0;
        for (int i = 0; i < points.length; i++) {
            int[] p1 = points[i];
            int[] p2 = points[(i + 1) % points.length];
            area += 0.5 * (p1[0] * p2[1] - p2[0] * p1[1]);
        }
        return Math.abs(area);
    }

    /**
     * 计算任意多边形面积
     *
     * @param points 顺时针/逆时针 点
     * @return
     */
    public static int areaPointPolygon(int[] points) {
        if (points.length < 6) {
            return 0;
        }
        int[][] result = new int[points.length / 2][2];
        for (int i = 0; i < (points.length / 2); i++) {
            result[i] = new int[]{points[2 * i], points[2 * i + 1]};
        }
        return areaPolygon(result);
    }

    /**
     * 返回指定面积范围四边形的一组点
     *
     * @param pointLimit
     * @param areaDownLimit 面积下限（包含）
     * @param areaUpLimit   面积上限（包含）
     * @return 指定顺序的点 顺时针 or 逆时针
     */
    public static int[] calcTargetQuadrilateralArea(int pointLimit, int areaDownLimit, int areaUpLimit) {
        int a, b, c, d;
        int[] points = new int[0];
        for (a = 0; a < pointLimit / 2; a++) {
            for (b = a + 1; b < pointLimit; b++) {
                for (c = 0; c < pointLimit / 2; c++) {
                    for (d = c + 1; d < pointLimit / 2; d++) {
                        points = new int[]{a, b, b, a, d, c, c, d};
                        int area = Math.abs(areaPointPolygon(points));
                        if (area >= areaDownLimit && area <= areaUpLimit) {
                            return points;
                        }
                    }
                }
            }
        }
        return points;
    }
}

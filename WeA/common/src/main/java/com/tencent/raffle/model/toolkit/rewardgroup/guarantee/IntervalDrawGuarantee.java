package com.tencent.raffle.model.toolkit.rewardgroup.guarantee;

import com.google.common.collect.Maps;
import com.tencent.raffle.record.Record;
import com.tencent.resourceloader.ResHolder;
import com.tencent.wea.protocol.common.RaffleCommonHint;
import com.tencent.wea.xlsRes.keywords.RaffleIntervalDrawGuaranteeConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IntervalDrawGuarantee extends BaseGuarantee {

    private static final Logger LOGGER = LogManager.getLogger(IntervalDrawGuarantee.class);
    protected final int interval;
    protected final Map<Integer, Integer> weights;
    protected final String text;

    public IntervalDrawGuarantee(Record record, int type, RaffleIntervalDrawGuaranteeConf conf) {
        super(record, conf.getPoolId(), type);
        this.interval = conf.getInterval();
        this.weights = new HashMap<>();
        this.text = conf.getText();
        conf.getItemsList().forEach(item -> weights.put(item.getGroupId(), item.getWeight()));
    }

    @Override
    public Map<Integer, Integer> getGroupWeightInternal(ResHolder resHolder,
            Map<Integer, Map<Integer, Integer>> inGroupWeights) {

        int currentDraw = getCounter() + 1;

        if (currentDraw < interval) {
            LOGGER.debug("minorGDraw counter={} not reach max for pool={} yet, skip it", currentDraw, getPoolId());
            return Maps.newHashMap();
        }

        return weights;
    }

    @Override
    public void confirmInternal(ResHolder resHolder, int groupId, int rewardId) {
        if (this.weights.containsKey(groupId)) {
            setCount(0);
            LOGGER.debug("reset counter, pool:{} group:{}", getPoolId(), groupId);
            return;
        }

        addCount(1);
    }

    @Override
    public Optional<RaffleCommonHint> hintInternal(ResHolder resHolder,
            Map<Integer, Map<Integer, Integer>> inGroupWeights, long currentMs) {

        if (text.isBlank()) {
            return Optional.empty();
        }

        RaffleCommonHint.Builder builder = RaffleCommonHint.newBuilder().setPoolId(getPoolId())
                .setType(type).setCurrentProgress(getCounter()).setTargetProgress(interval).setText(text);

        return Optional.of(builder.build());
    }
}

package com.tencent.tmempool;

import com.google.protobuf.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public abstract class BaseShmProtoMessageData<KeyType, ProtoMessageType extends GeneratedMessageV3> implements SharedMemoryDataInterface<KeyType, ProtoMessageType> {
    private static final Logger LOGGER = LogManager.getLogger(BaseShmProtoMessageData.class);

    private ProtoMessageType.Builder messageBuilder = null;
    private Class<ProtoMessageType> protoMessageClass = null;
    /**
     * 只保存一份byte buffer，因为更新接口是线程安全的，同时只有一个操作该buffer
     */
    private ByteBuffer directByteBuffer = null;

    protected BaseShmProtoMessageData(ProtoMessageType.Builder messageBuilder, Class<ProtoMessageType> cls) {
        this.messageBuilder = messageBuilder;
        protoMessageClass = cls;
    }

    @Override
    public int init(int unitSize) {
        directByteBuffer = ByteBuffer.allocateDirect(unitSize);

        return 0;
    }

    @Override
    public byte[] toByteArray(ProtoMessageType message) {
        if (message == null) {
            return null;
        }

        return message.toByteArray();
    }

    @Override
    public ByteBuffer toByteBuffer(ProtoMessageType message) {
        if (message == null) {
            return null;
        }
        directByteBuffer.clear();
        if (message.getSerializedSize() > directByteBuffer.remaining()) {
            LOGGER.error("bytebuffer is not enough: expected:{} actual:{}", message.getSerializedSize(), directByteBuffer.remaining());
            return null;
        }
        CodedOutputStream outputStream = CodedOutputStream.newInstance(directByteBuffer);
        try {
            message.writeTo(outputStream);
        } catch (IOException e) {
            LOGGER.error("worldPointTable.writeTo failed", e);
            return null;
        }

        directByteBuffer.limit(message.getSerializedSize());
        return directByteBuffer;
    }

    @Override
    public ProtoMessageType parseFrom(byte[] data) {
        try {
            Message message = messageBuilder.clear().mergeFrom(data).build();
            if (protoMessageClass.isInstance(message)) {
                return protoMessageClass.cast(message);
            } else {
                return null;
            }
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("mergeFrom failed", e);
        }

        return null;
    }

    @Override
    public ProtoMessageType parseFrom(ByteBuffer data) {
        CodedInputStream inputStream = CodedInputStream.newInstance(data);
        try {
            Message message = messageBuilder.clear().mergeFrom(inputStream).build();
            if (protoMessageClass.isInstance(message)) {
                return protoMessageClass.cast(message);
            }
        } catch (Exception e) {
            LOGGER.error("parseFrom ByteBuffer failed", e);
        }

        return null;
    }
}

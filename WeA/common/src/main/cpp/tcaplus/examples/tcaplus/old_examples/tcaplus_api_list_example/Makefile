#  @file $RCSfile: Make<PERSON>le,v $
#    Makefile for tsf4g-security module
#    $Id: <PERSON><PERSON><PERSON>,v 1.1.1.1 2008-05-28 07:35:00 kent Exp $
#  <AUTHOR> kent $
#  @date $Date: 2008-05-28 07:35:00 $
#  @version 1.0
#  @note Editor: Vim 6.3, Gcc 4.0.2, tab=4
#  @note Platform: Linux


include ../../tsf4websns_examples.mk

TARGETS = \
	SHELL \

all: $(TARGETS)

SHELL:
	@$(MAKE) -f Makefile.real

#------------------------------------------------------
clean:
	-make -f Makefile.real clean
	$(RM) *.o *.d *.log


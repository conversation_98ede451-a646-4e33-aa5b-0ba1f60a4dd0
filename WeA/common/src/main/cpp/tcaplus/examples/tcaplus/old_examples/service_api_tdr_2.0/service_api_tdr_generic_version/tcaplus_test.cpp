/******************************************************************************
 *File Name	:	service_api_tdr_generic_version.cpp
 *Version		:	1.0
 *Author		:	j<PERSON><PERSON><PERSON><PERSON><PERSON>
 *Created		:	2014-07-07
 *Description	:   ͨ��version��ֹ����д�������⸲������,��������update��Ϊд�����������request��record��versionΪ1��
 *History		:      1
 *Date		:	2014-10-30
 *Author	       :	kenzfliang
 *Modify	       :	Created file
 *Copyright     :      Copyright  1998 - 2014 Tencent. All Rights Reserved
 ******************************************************************************/


#include "table_test.h"
#include "types.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pal/pal.h>
#include <tlog/tlog.h>
#include <tloghelp/tlogload.h>
#include "tcaplus_service_api.h"

using namespace TcaplusService;
using namespace tcaplus_tb;

/**
 * This Example App, Zone, Table has been deployed on "http://tcaplusdev.oa.com/"
 * App: example_app(2), Zone: example_zone(3)
 * You can find that on "http://tcaplusdev.oa.com/app/index.php/serviceApply/businessAreaAccess/2"
 * So, you can run this example with out apply any app , zone  or table;
 * Just Make And Run It!
 *
 * ��Example��ص�app,zone,table�Ѿ�������tcaplus���Լ�Ⱥ"tcaplusdev.oa.com"
 * ��,�����������κε�app���������example;
 * ��ֻ��Ҫmake����˴��뼴������.
 */
static const int APP_ID = 2;
static const int ZONE_ID = 3;
static const char* SIGNATURE = "FE6533875C8385C3";
static const char* TABLE_TEST_NAME = "GenericTableTest";

static const char* DIR_URL = "tcp://10.12.16.181:9999";



static int PrintRequest(TcaplusService::TcaplusServiceRequest* request);


/************************************************************************
 *������: TestFillRequestTDRUpdate

 *����     : �����������
 **************************************************************************/
static int TestFillRequestTDRUpdate(int32_t uin, char* name, int32_t iCurVersion, TcaplusService::TcaplusServiceRequest*& request, LPTLOGCATEGORYINST log_handler)
{
	printf("[%s]\n", __FUNCTION__);


	//�����ڴ˴������û��Զ�����Ϣ���첽����ID��������Ϣ���кţ�����Ӧ��־
	int ret = request->Init(TCAPLUS_API_UPDATE_REQ, NULL, 0, 0, 0, 0);
	if(0 != ret)
	{
		tlog_error(log_handler, 0, 0, "request.Reset(TCAPLUS_API_UPDATE_REQ) failed. ret:%d", ret);
		return ret;
	}

	//Ҳ����ͨ�����º�����������
	const char* user_buff = "Hello!";
	request->SetUserBuff(user_buff, strlen(user_buff));  //�����û��Զ�����Ϣ
	uint64_t async_id = 2;
	request->SetAsyncID(async_id);	//�����첽����ID
	char result_flag = 2;
	request->SetResultFlag(result_flag);  //����Ӧ���Ƿ�Я���ֶ����ݷ���,1��ʾҪ���ر���ֶε���������,2��ʾҪ���ر����¼�������ֶ���������
	int32_t sequence = 20;
	request->SetSequence(sequence); //�����������к�

	TcaplusService::TcaplusServiceRecord* record = request->AddRecord();
	if (NULL == record)
	{
		tlog_error(log_handler, 0, 0, "request->AddRecord() failed.");
		return -1;
	}

	//���ø��µ�key��Ϣ
	GenericTableTest table_test;
	table_test.iUin = uin;
	snprintf(table_test.szName, sizeof(table_test.szName), "%s", name);

	//���ø��µ�value��Ϣ
	table_test.iLevel = 13;
	table_test.bCount = 1;
	table_test.items[0]= 0x19191919;

	ret = record->SetData(table_test);
	if(0 != ret)
	{
		tlog_error(log_handler, 0, 0, "record->SetData() failed. ret:%d", ret);
		return ret;
	}

	//���ð汾��¼�ţ�<=0��ʾ����ע�汾�š�����������õ�version��server�м�¼��version��һ�»ᵼ��д����ʧ��(replace��update��delete)
	record->SetVersion(iCurVersion);//������һ��Ҫ��SetData()֮�����Ч

	//�Կ��ӻ��ķ�ʽ��ӡ��������Ϣ��������
	PrintRequest(request);

	return 0;

}


/************************************************************************
 *������: TestParseRespGenericSimple

 *����      : �Խ��յ�����Ӧ�����н����ʹ���
 **************************************************************************/
static int TestParseRespGenericSimple(LPTLOGCATEGORYINST log_handler, TcaplusService::TcaplusServiceResponse* response)
{
	// ����Ӧ��Ϣ���Կ��ӻ��ķ�ʽ��ӡ����
	size_t print_buffer_size = 1024 * 1024;
	char* print_buffer = new char[print_buffer_size];
	printf("---------- response dump ----------\n%s\n\n", response->Print(print_buffer, print_buffer_size));


	int32_t result = response->GetResult();  //��ȡ�����Ľ����0��ʾ�ɹ�
	if(0 == result)
	{
		printf("Execute write successfully!\n");
		printf("the result is %d\n", result);
	}
	else if(result == TcapErrCode::SVR_ERR_FAIL_INVALID_VERSION)
	{
		printf("Execute write failed!\n");
		printf("the result is %d\n", result);
		printf("the record request iVersion is disaccord to server iVersion , you should update client data.\n");
	}
	else
	{
		printf("Execute write fail for other reason!\n");
	}

	delete[] print_buffer;

	return 0;
}


/************************************************************************
 *������: PrintRequest

 *����     : ��������Ϣ�����Կ��ӻ���ʽ��ʾ����
 **************************************************************************/
static int PrintRequest(TcaplusService::TcaplusServiceRequest* request)
{
	size_t print_buffer_size = 1024 * 1024;
	char* print_buffer = new char[print_buffer_size];
	printf("---------- request dump ----------\n%s\n\n", request->Print(print_buffer, print_buffer_size));

	//����ͨ�����µĺ�����ȡ������Ϣ��������
	//TCaplusApiCmds cmd = request->GetCmd(); //��ȡ�������������

	const char* table_name = NULL;
	size_t table_name_size = 0;
	table_name = request->GetTableName(&table_name_size);  //��ȡ��������ı���
	printf("table name is %s, the length of the table name is %"PRIuS"\n", table_name, table_name_size);

	const char* get_user_buff = NULL;
	size_t buff_len = 0;
	get_user_buff = request->GetUserBuff(&buff_len);  //��ȡ�û��Զ������Ϣ
	printf("the user buff is %s, the length is %"PRIuS"\n", get_user_buff, buff_len);

	int32_t seq = request->GetSequence();  //��ȡ������Ϣ���к�
	printf("sequence = %d\n", seq);

	uint64_t get_asyn_id = request->GetAsynID();  //��ȡ�첽����ID
	printf("asyn_id = %"PRIu64"\n", get_asyn_id);

	char get_result_flag = request->GetResultFlag(); //��ȡ��Ӧ��־
	printf("result_flag = %d\n", get_result_flag);

	delete[] print_buffer;

	return 0;

}



/************************************************************************
 *������: ReceiveResponse

 *���� 	    : ������Ӧ��Ϣ��
 **************************************************************************/
static int ReceiveResponse(TcaplusService::TcaplusServer& tcaplus_server, TcaplusService::TcaplusServiceResponse*& response, LPTLOGCATEGORYINST log_handler)
{
	unsigned int sleep_us = 10000;  // 10����
	unsigned int sleep_count = 1000;    // ���ȴ�1000��
	do
	{
		usleep(sleep_us);

		// ����������Ϣ�����ֶ�ʱ���¼� ,����һ�������ķ���
		//�ͽ��ղ����������һ�θú���
		tcaplus_server.OnUpdate();

		response = NULL;
		int ret = tcaplus_server.RecvResponse(response);  // ������Ӧ
		if(ret < 0)
		{
			tlog_error(log_handler, 0, 0, "tcaplus_server.RecvResponse failed. ret:%d", ret);
			return ret;
		}
		if(1 == ret) // �յ�һ���ذ�
		{
			break;
		}
	}while((--sleep_count) > 0);

	if(0 == sleep_count) //������Ӧ����ʱ
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.RecvResponse wait timeout.");
		return -1;
	}

	return 0;

}





/************************************************************************
 *������: InitLog

 *���� 	    : ��ʼ����־
 **************************************************************************/
static int InitLog(LPTLOGCTX& log_ctx, LPTLOGCATEGORYINST& log_handler, TcaplusService::TLogger*& tlogger)
{

	log_ctx = tlog_init_from_file("tlogconf.xml");
	if (NULL == log_ctx)
	{
		fprintf(stderr, "tlog_init_from_file failed.\n");
		return -1;
	}

	log_handler = tlog_get_category(log_ctx, "test");
	if (NULL == log_handler)
	{
		fprintf(stderr, "tlog_get_category(\"test\") failed.\n");
		return -1;
	}

	tlogger = new TcaplusService::TLogger(log_handler);

	return 0;

}



/************************************************************************
 *������: InitServiceApi

 *���� 	    : ��ʼ��service api
 **************************************************************************/
static int InitServiceApi(TcaplusService::TcaplusServer& tcaplus_server, TcaplusService::TLogger* tlogger, LPTLOGCATEGORYINST log_handler)
{
	int ret = tcaplus_server.Init(tlogger, /*module_id*/0, APP_ID, ZONE_ID, SIGNATURE);
	if (0 != ret)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.Init failed. ret:%d", ret);
		return ret;
	}

	// ���Ŀ¼������
	ret = tcaplus_server.AddDirServerAddress(DIR_URL);
	if (0 != ret)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.AddDirServerAddress(\"%s\") failed. ret:%d", DIR_URL, ret);
		return ret;
	}


	// ע�����ݱ�����dir����������֤����ȡ��·�ɣ�
	ret = tcaplus_server.RegistTable(TABLE_TEST_NAME, NULL, /*timeout_ms*/10000);
	if(0 != ret)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.RegistTable(\"%s\") failed. ret:%d", TABLE_TEST_NAME, ret);
		return ret;
	}


	//���ӱ��Ӧ������tcaplus proxy������
	ret = tcaplus_server.ConnectAll(/*timeout_ms*/10000, 0);
	if(0 != ret)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.ConnectAll failed. ret:%d", ret);
		return ret;
	}

	tcaplus_server.SetCheckHeartbeatInterval(DEFAULT_CHECK_HEARTBEAT_INTERVAL);//�����������ļ����

	return 0;

}

int main(int argc, char* argv[])
{
	if (argc != 4)
	{
		printf("\nUSAGE\n");
		printf("%s\t[uin]\t[name]\t[cur_version]\n",argv[0]);
		printf("for example: ./tcaplus_service_api_test 1001 test 3\n");
		return -1;
	}

	int32_t uin = atoi(argv[1]);
	char* name = argv[2];
	int32_t iCurVersion = atoi(argv[3]);


	// ��ʼ����־
	LPTLOGCTX log_ctx = NULL;
	LPTLOGCATEGORYINST log_handler = NULL;
	TcaplusService::TLogger* tlogger = NULL;
	int ret = InitLog(log_ctx, log_handler, tlogger);
	if (0 != ret)
	{
		tlog_error(log_handler, 0, 0, "InitLog() failed!");
		return -1;
	}


	//��ʼ��  service api
	TcaplusService::TcaplusServer tcaplus_server;
	ret = InitServiceApi(tcaplus_server, tlogger, log_handler);
	if (0 != ret)
	{
		tlog_error(log_handler, 0, 0, "InitServiceApi() failed!");
		return -1;
	}


	// ���������Ϣ��
	TcaplusService::TcaplusServiceRequest* request = tcaplus_server.GetRequest(TABLE_TEST_NAME);
	if (NULL == request)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.GetRequest(\"%s\") failed.", TABLE_TEST_NAME);
		return -1;
	}

	ret = TestFillRequestTDRUpdate(uin, name, iCurVersion, request, log_handler);
	if (0 != ret)
	{
		tlog_error(log_handler, 0, 0, "TestFillRequestTDRUpdate() failed!");
		return -1;
	}


	//����������Ϣ��
	ret = tcaplus_server.SendRequest(request);
	if(ret != 0)
	{
		tlog_error(log_handler, 0, 0, "tcaplus_server.SendRequest failed. ret:%d", ret);
		return ret;
	}


	// ������Ӧ��Ϣ��
	TcaplusService::TcaplusServiceResponse* response = NULL;
	ret = ReceiveResponse(tcaplus_server, response, log_handler);
	if(0 == ret)
	{
		tlog_debug(log_handler, 0, 0, "ReceiveResponse() successed.");
	}
	else
	{
		tlog_error(log_handler, 0, 0, "ReceiveResponse() failed!");
		return -1;
	}


	//�����ʹ�����Ӧ��Ϣ��
	TestParseRespGenericSimple(log_handler, response);


	// ��Դ�ͷţ��˳���������ʾ������δ�ϸ���ͨ��Ӧȷ�������˳����������Զ��ͷ���Դ��
	delete tlogger;
	tcaplus_server.Fini();
	tlog_fini_ctx(&log_ctx);

	return 0;
}

# ================================================================
#Makefile for tcaplus example
#
# Date:   2016-09-14
#
# Copyright (C) 2016 Architechure IRED TENCENT
#
# ================================================================

CPPFILE=$(wildcard *.cpp)

LIBS += -L $(TSF4G_HOME)/lib -L$(TCAPLUS_HOME)/lib -Wl,-Bstatic -ltcaplusserviceapi -ltsf4g_r -lscew -lexpat -Wl,-Bdynamic -lpthread
 
INC = -I$(TSF4G_HOME)/include -I$(TCAPLUS_HOME)/include/tcaplus_service -I../../../C++_common_for_tdr1.0

.PHONY: all clean 

all:
	g++ -o mytest $(CPPFILE) $(INC) ${LIBS}  

clean:
	rm -f mytest     mytest.log*

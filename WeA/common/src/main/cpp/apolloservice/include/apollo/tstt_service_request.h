#ifndef _TSTT_SERVICE_REQUEST_
#define _TSTT_SERVICE_REQUEST_

#include "apollo_service_common.h"
#include "tstt_service_types.h"

namespace apollo_service_api {

class SttBaseReqImp;
class SttBaseReq {
 public:
  SttBaseReq() {};
  virtual ~SttBaseReq() {};

  virtual SttBaseReqImp* GetImp() {
    return NULL;
  }
};

class TSttSttReqImp;
class TSttSttReq : public SttBaseReq {
 public:
  TSttSttReq();
  ~TSttSttReq();
 public:
  int SetFileID(const char* szFileID);
 public:
  virtual SttBaseReqImp* GetImp() {
    return (SttBaseReqImp*)m_pImp;
  }
  TSttSttReqImp* m_pImp;
};

class TChatRoomReqImp;
class TChatRoomReq : public SttBaseReq {
 public:
  TChatRoomReq();
  ~TChatRoomReq();
 public:
  int SetBusinessID(const char* BusinessID);
  int SetBusinessKey(const char* BusinessKey);
  int SetRoomName(const char* room_name);
  int SetRoomLifeTimeSecond(unsigned int sec);
  int SetReadUserID(const char* read_user_id);
  int SetReadWriteUserID(const char* read_write_user_id);
 public:
  virtual SttBaseReqImp* GetImp() {
    return (SttBaseReqImp*)m_pImp;
  }
  TChatRoomReqImp* m_pImp;
};
}
#endif










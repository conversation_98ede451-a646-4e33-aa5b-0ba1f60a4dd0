#ifndef TONLINE_SERVICE_H_
#define TONLINE_SERVICE_H_


#include "apollo_service_common.h"
#include "tonline_service_common.h"

struct tagTLogCategoryInst;
typedef struct tagTLogCategoryInst TLOGCATEGORYINST;

namespace apollo_service_api {
class ApolloService;
class ApolloServiceImp;
class BaseConnMgr;
class IServiceEventListener;
class OnlineServiceImp;

class OnlineService : protected BaseService {
 public:
  OnlineService();
  virtual  ~OnlineService();

 public:

  /// @brief  获取"用户上线上报" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineLoginReq* Get_TOnlineLoginReq(const char* pCBData, uint32_t uiCBLen);

  /// @brief  获取"更新用户信息" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineUpdateReq* Get_TOnlineUpdateReq(const char* pCBData, uint32_t uiCBLen);

  /// @brief  获取"用户下线上报" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineLogoutReq* Get_TOnlineLogoutReq(const char* pCBData, uint32_t uiCBLen);


  /// @brief  获取"用户下线上报(严格)" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineStrictLogoutReq* Get_TOnlineStrictLogoutReq(const char* pCBData,
      uint32_t uiCBLen);


  /// @brief  获取"查询用户状态" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineQueryReq* Get_TOnlineQueryReq(const char* pCBData, uint32_t uiCBLen);

  /// @brief  获取"刷新用户时间戳" 请求类;
  ///
  /// @param[IN]   pCBData  异步回调数据，响应包中会携带回来该数据供应用层处理(为null  时表示不使用异步回调数据)
  /// @param[IN]   uiCBLen   异步回调数据长度(为0 时表示不使用异步回调数据)，最大长度为1024
  ///
  /// @return !NULL: 成功， NULL:失败
  TOnlineRefreshReq* Get_TOnlineRefreshReq(const char* pCBData, uint32_t uiCBLen);


  /// @brief  发送请求到后端TONLINE 服务化系统
  ///
  /// @param[IN]   pReqClass   通过上述6个函数获取到的命令字请求类，如TOnlineQueryReq*   TOnlineOnlineReq*  ……等
  ///                                       注意获取到请求类后，需要调用其SetPara() 等成员函数设置好请求参数后，才能调用本函数发送请求
  ///
  /// @return 0: 成功  !0:失败
  int SendReq(OnlineBaseReq* pReqClass);

 public:
  friend class ApolloService;
  friend class ApolloServiceImp;


  /*下面函数及变量由API 内部调用及管理，应用无需关注及调用*/
 protected:
  virtual IServiceEventListener* GetImp() {
    return (IServiceEventListener*)m_pImp;
  }
  virtual int Init(uint32_t uiBusinessId, void* pResponseListener,
                   TLOGCATEGORYINST* pLoger,
                   BaseConnMgr* pSrvConnMgr, void* runstat);

  OnlineServiceImp*  m_pImp;
  TLOGCATEGORYINST* m_pLogger;

};

}

#endif

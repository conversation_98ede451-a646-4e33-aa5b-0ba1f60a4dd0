//
// This file was generated by irpc_plugin which is a self-defined pb compiler plugin, do not edit it!!!
// All rights reserved by Tencent Corporation
//

#pragma once

#include <memory>
#include <string>

#include "ds_event_subscriber.pb.h"

#include "irpc/common/status.h"
#include "irpc/server/rpc_service_impl.h"
#include "irpc/client/rpc_client_impl.h"
#include "irpc/common/irpc_error.h"

namespace ds_event_subscriber {

class DsEventSubscriber: public g6::irpc::RPCServiceImpl {
public:
  DsEventSubscriber();

  ~DsEventSubscriber() override;

  std::string GetRPCServiceName() override { return std::string("/ds_event_subscriber.DsEventSubscriber"); }

  virtual g6::irpc::Status CreateGameSessionResultEvent(g6::irpc::ServerContextPtr context, const ds_event_subscriber::CreateGameSessionResultEventRequest* request, ds_event_subscriber::CreateGameSessionResultEventReply* response);

  virtual g6::irpc::Status GameSessionEndEvent(g6::irpc::ServerContextPtr context, const ds_event_subscriber::GameSessionEndEventRequest* request, ds_event_subscriber::GameSessionEndEventReply* response);
};

class DsEventSubscriberRPCClient: public g6::irpc::RPCClientImpl {
public:
  DsEventSubscriberRPCClient();

  ~DsEventSubscriberRPCClient() override;

  virtual g6::irpc::Status CreateGameSessionResultEvent(const g6::irpc::ClientContextPtr& context, const ds_event_subscriber::CreateGameSessionResultEventRequest& request, ds_event_subscriber::CreateGameSessionResultEventReply* response);
  virtual g6::irpc::Future<ds_event_subscriber::CreateGameSessionResultEventReply> AsyncCreateGameSessionResultEvent(const g6::irpc::ClientContextPtr& context, const ds_event_subscriber::CreateGameSessionResultEventRequest& request);

  virtual g6::irpc::Status GameSessionEndEvent(const g6::irpc::ClientContextPtr& context, const ds_event_subscriber::GameSessionEndEventRequest& request, ds_event_subscriber::GameSessionEndEventReply* response);
  virtual g6::irpc::Future<ds_event_subscriber::GameSessionEndEventReply> AsyncGameSessionEndEvent(const g6::irpc::ClientContextPtr& context, const ds_event_subscriber::GameSessionEndEventRequest& request);
};

} // end namespace ds_event_subscriber
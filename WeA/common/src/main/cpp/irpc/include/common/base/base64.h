// Copyright [2020] <Tencent>
#ifndef COMMON_BASE_BASE64_H_
#define COMMON_BASE_BASE64_H_

#include <string>

namespace g6 {

class Base64 {
 public:
  Base64() {}
  virtual ~Base64() {}

  static bool Encode(const std::string& src, std::string* dst, bool urlsafe = false);

  static bool Decode(const std::string& src, std::string* dst);

 private:
  // 根据在Base64编码表中的序号求得某个字符
  static inline char Base2Chr(unsigned char n);

  // 求得某个字符在Base64编码表中的序号
  static inline unsigned char Chr2Base(char c);

  inline static int Base64EncodeLen(int n);
  inline static int Base64DecodeLen(int n);
};

}  // namespace g6

#endif  // COMMON_BASE_BASE64_H_

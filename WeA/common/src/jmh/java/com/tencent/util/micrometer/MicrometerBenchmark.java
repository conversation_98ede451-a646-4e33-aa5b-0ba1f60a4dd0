// package com.tencent.util.micrometer;

// import com.tencent.timiutil.monitor.MonitorId;
// import com.tencent.timiutil.monitor.Monitor;
// import com.tencent.nk.commonframework.Framework;
// import com.tencent.util.micrometer.registry.ProfileMeterRegistry;
// import org.openjdk.jmh.annotations.*;
// import org.openjdk.jmh.runner.Runner;
// import org.openjdk.jmh.runner.RunnerException;
// import org.openjdk.jmh.runner.options.Options;
// import org.openjdk.jmh.runner.options.OptionsBuilder;
// import org.apache.logging.log4j.LogManager;
// import org.apache.logging.log4j.Logger;

// import java.util.HashMap;
// import java.util.concurrent.TimeUnit;

// @BenchmarkMode({Mode.Throughput, Mode.AverageTime})
// @Warmup(iterations = 0)
// @Measurement(iterations = 1, time = 10, timeUnit = TimeUnit.SECONDS)
// @OutputTimeUnit(TimeUnit.SECONDS)
// @Fork(value = 1)
// @Threads(1)
// @State(Scope.Benchmark)
// @OperationsPerInvocation

// public class MicrometerBenchmark {
//     private static final Logger LOGGER = LogManager.getLogger(MicrometerBenchmark.class);

//     ProfileMeterRegistry sampleRegistry =
//             ProfileMeterRegistry.newBuilder().setIntervals(60 * 1000L).setLogger(LOGGER).setName("benchmark").build();
//     HashMap<Long, Long> testMap = new HashMap<>();

//     /**
//      * builder模式DistributionSummary
//      */
//     @Benchmark
//     public void testBuilderDistribution() {
//         sampleRegistry.recordDistributionSummary("summary", 100);
//     }

//     /**
//      * builder模式Counter
//      */
//     @Benchmark
//     public void testBuilderCounter() {
//         sampleRegistry.count("counterTest");
//     }


//     /**
//      * builder模式Gauge
//      */
//     @Benchmark
//     public Double testBuilderGauge() {
//         testMap.put(Framework.currentTimeMillis(), Framework.currentTimeMillis());
//         return sampleRegistry.getGauge("gaugeTest", testMap, HashMap::size).value();
//     }

//     /**
//      * 全局Metrics模式
//      */
//     @Benchmark
//     public void testMetricsDistribution() {
//         MicroMeterStatistic.getInstance().recordHistogramSummary("benchmark", "summary", 100);
//     }

//     /**
//      * baseline智研接口
//      */
//     @Benchmark
//     public void testZhiYanMonitorBaseline() {
//         Monitor.getInstance().add.total(MonitorId.attr_framework_proc, 1);
//     }

//     public static void main(String[] args) throws RunnerException {
//         Options opt = new OptionsBuilder()
//                 .include(MicrometerBenchmark.class.getSimpleName())
//                 .build();
//         new Runner(opt).run();
//     }
// }

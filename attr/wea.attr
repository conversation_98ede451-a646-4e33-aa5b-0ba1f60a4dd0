@import "../excel/basic/proto/ResKeywords.proto"

@UID
   most    int64   1
   least   int64   2

@BagInfoDb
   item            map<Bag>   1         # 背包
   observingItem   map<Bag>   2         #
   createRoleItem  set<int32> 3 +nocs   # 创建角色时已赠送的item, 杂项中的regItem
   actionSeting    bool       4         #道具设置


@Bag
   id         int32   1   +key   # 背包ID
   gridNum    int32   2          # 背包格子数量

@GameplayItem
   uuid                 int64       1 +key      # 唯一id
   itemId               int32       2           # 道具id
   itemNum              int64       3           # 道具数量
   expireMs             int64       4           # 过期时间
   itemChangeReason     int32       5           # 道具变化原因
   itemChangeSubReason  int64       6           # 道具变化二级原因
   billNo               string      7           # 订单号

@GameplayItemSet
   featureId    int32               1 +key       # 玩法id
   itemList     map<GameplayItem>   2            # 道具列表

@GameplayAddItemBillNo
   billNo       string         1   +key     # 订单号
   addTimeMs    int64          2            # 时间

@ItemInfoDb
   item            map<Item>   1      # 背包中的物品
   observingItem   map<Item>   2      #
   historyItemType map<HistoryItemTypeInfo> 3 # 历史累计不同类型道具数量
   historyItemUsed map<HistoryItemUsedInfo> 4 # 历史累计道具ID使用数量
   gameplayTempItem map<GameplayItemSet> 5 +nocs # 副玩法未发放道具暂存
   gameplayAddItemBillNo map<GameplayAddItemBillNo> 6 +nocs # 副玩法请求添加道具订单号
   itemDetail      map<ItemDetailInfo> 7     # 道具展示状态

@Item
   id           int64          1   +key   # 道具UUID
   itemId       int32          2          # 道具配置ID
   number       int64          3          # 道具数量
   cooling      int64          4   +nocs  # 冷却时间
   expireMs     int64          5          # 过期时间
   bagTag       int32          6   +nocs  # 背包页签ID
   gridId       int32          7   +nocs  # 背包格子ID
   getTimeMs    int64          8          # 获得时间
   updateMs     int64          9          # 更新时间
   equipPos     int32          10  +nocs  # 穿戴位置
   expireType   int32          11         # 过期时间类型  ItemExpireType
   status       int32          12         # 道具状态
   lessorUid    int64          13         # 出租方UID，用于租借时装时标识对方
   favoriteTime int64          14         # 道具收藏时间(0表示未收藏)

@ChestItem 
   id           int64          1   +key   # 道具UUID
   itemId       int32          2          # 道具配置ID
   number       int64          3          # 道具数量
   gridId       int32          4          # 背包格子ID
   equipPos     int32          5          # 装配的格子ID
   attr         map<KvIL>      6          # 道具属性

@ItemDetailInfo
   itemUUId      int64               1 +key       # 道具UUID
   showStatus    int32               2            # 道具显示状态
   extendAttr    map<KvIL>           3            # 道具扩展属性字段 ItemExtendAttrType -> value

@HistoryItemTypeInfo
   type       com.tencent.wea.xlsRes.ItemType       1 +key    # 道具类型
   num        int64                                 2         # 道具数量,不包含限时道具
   tempNum    int64                                 3         # 限时道具数量

@HistoryItemUsedInfo
   itemId     int32                                 1 +key    # 道具配置ID
   num        int64                                 2         # 道具数量,不包含限时道具
   tempNum    int64                                 3         # 限时道具数量

@RewardItemInfo
   id              int64         1    +key  # 道具id
   num             int64         2          # 道具数量
   expireTimeMs    int64         3          # 过期时间
   expireType      int32         4          # 过期时间类型   ItemExpireType

@BasicInfo # 用户基本信息
   loginTimeMs                 int64            1
   logoutTimeMs                int64            2
   heartBeatTimeMs             int64            4   +nocs   #心跳时间
   lastHeartBeatTimeMs         int64            5   +nocs   #上次心跳时间
   totalLoginTimeMs            int64            6
   isRegistering               bool             7   +nocs   #是否在注册
   last8ClockRefreshTimeMs     int64            8   +nocs   #上次早上8点更新时间
   lastWeekRefreshTimeMs       int64            9   +nocs   #上周更新时间
   lastLoginTimeMs             int64            10           #上次登录时间
   onlineMillisRefreshAt8      int64            11  +nocs   #当日在线时长(每日8点刷新)
   lastMidnightRefreshTimeMs   int64            12			#上次凌晨刷新时间
   belongLogicZoneId           int64            13
   threeClockRefreshTimeMs     int64            14   +nocs   #上次早上3点更新时间
   sixClockRefreshTimeMs       int64            15   +nocs   #上次早上6点更新时间
   lastRecruitPublishTimeMs    int64            16   +nocs   #上次组队招募发布时间
   fiveClockRefreshTimeMs      int64            17   +nocs   #上次早上5点更新时间
   onTimeDelayMs               int64            18   +nocs   #2分钟内的随机毫秒数
   dailyOnlineTimeMs           int64            19   +nocs   #当日在线时长
   beforeMidnightTimeMs        int64            20   +nocs   #午夜前刷新时间点
   everyHourRefreshTimeMs      int64            21   +nocs   #上次整点更新时间
   farmDailyOnlineTimeMs       int64            22   +nocs   #当日农场内累计在线时长
   qqCloudGameAuth             int32            23           #qq小游戏授权状态, 1:需要提示授权, 2:已授权, 3:已拒绝, 4:无需拦截, 5:出错放行, 6:开关放行
   limitNotifyTimes            bool             24           #限制上线稳固后通知好友一次
   clientWhiteListSign         int32            25   +nodb        #添加白名单后下发给客户端的标记，数据读取配表字段client值

@StatCluster # 统计信息汇总
   dailyGraph                 map<StatGraph>    1           # 每日统计信息
   dailyKeyOrigin             int64             2           # 记录原点 all recording daily time key >= dailyKeyOrigin

@StatGraph # 用户统计信息
   timeKey                    int64             1   +key    # 时间戳作为KEY 一天的开始时间 in seconds
   onlineTimeMs               int64             2           # 累计在线时长

@GuideTaskExtraInfo
   seqId         int32                  1      # 执行步骤,步骤只能增加不能减少

@WeeklyTaskExtraInfo
   value   int64   1      #进度

@TaskLifeTime
   showBeginTime             int64               1             # 任务展示时间
   showEndTime               int64               2
   doBeginTime               int64               3             # 可以进行任务的时间
   doEndTime                 int64               4

@TaskTimeInfo +oneof
   taskLifeTime              TaskLifeTime        1             # 任务开始结束时间
   notUseAttrTime            bool                2             # 不使用属性系统的时间

@NewbieTaskInfo
   unlockDay                 int32               1             # 解锁到第几天

@UgcCreativeTaskInfo
   unlockStage               int32               1             # 解锁到第几阶段

@LobbyEggFindKindCountInfo
  eggKindId                  int32      1    +key    # 彩蛋类型
  eggCount                   int32      2            # 获得数量

@LobbyTaskInfo
  eggFindKindCountMap       map<LobbyEggFindKindCountInfo>    1     # //各种类型彩蛋的积累获得次数


@PlayTopicTaskInfo
  topicId                  int32      1    +key
  playCount                int32      2
  passCount                int32      3

@UgcTaskInfo
  playBlueTopicTask        map<PlayTopicTaskInfo>       1
  playGoldTopicTask        map<PlayTopicTaskInfo>       2
  ugcMapTotalPlayUv        int32                        3
  ugcMapPeakPlayUv         int32                        4
  ugcNewYearMapPassed      set<int64>                   5
  ugcTaskDeduplication     map<UgcTaskDeduplication>    6
  lastEnterLobbyMs         int64                        7  +nodb
  lastEnterLobbyId         int64                        8  +nodb
  appreciateScore          int32                        9  +nocs
  appreciateScoreTotal     int32                        10 +nocs

@UgcTaskDeduplication
  taskId                   int32        1 +key
  values                   set<int64>   2


@TaskExtraInfo
   newbieTaskInfo            NewbieTaskInfo      1             # 新手任务信息
   creativeTaskInfo          UgcCreativeTaskInfo 2             # Ugc任务信息
   lobbyTaskInfo             LobbyTaskInfo       3             # 大厅任务信息
   ugcTaskInfo               UgcTaskInfo         4             # Ugc任务
   returnTaskInfo            ReturnTaskInfo      5

@ReturnTaskInfo
  lastGameTime             int32       1
  lastEnterFarmTime        int32       2


@Task
  id                      int32                                 1   +key      # 任务id
  conditionGroup          ConditionGroup                        4             # 条件组
  status                  com.tencent.wea.xlsRes.TaskStatus     6             # 任务状态
  repeatNum               int32                                 7             # 重复完成次数
  timeInfo                TaskTimeInfo                          8             # 时间信息
  lastCompleteTime        int64                                 9             # 上次完成时间
  lastTriggerTime         int64                                 10            # 上次触发时间
  rewardLock              bool                                  11            # 锁住奖励不可领取

@TaskFinish
  id              int32               1   +key              # 任务id
  completeTime    int64               2                     # 完成时间

@TaskInfo
  runningTask          map<Task>                1                       # 运行中的任务
  version              int64                    3                       #
  lastRefreshTime      map<TaskRefreshTime>     4                       # 上次刷新时间
  extraInfo            TaskExtraInfo            5                       # 额外信息
  finishTask           map<TaskFinish>          6                       # 完成的任务
  optionalReward       map<OptionalRewardInfo>  7                       # 可选奖励任务信息

@OptionalRewardInfo
   taskId                   int32          1  +key      # 任务ID
   rewardIndex              int32          2            # 选择的奖励下标 -1为未选择

@TaskRefreshTime
  taskType          int32      1   +key     # 任务类型
  time              int64      2            # 时间

@StrStrPair
   id               string                 1 +key
   value            string                 2                                3

@ExchangePair #单个兑换对
   id                        int64                              1 +key
   conditionGroup            ConditionGroup                     2       #条件组
   exchangeCount             int64                              3       #已兑换次数

@ExchangeCenter
   exchangePair              map<ExchangePair>                    1

@MessageSlipRedDotInfo # 留言板红点数据
  id                    int64                    1 +key
  favourRedDot          bool                     2   +nodb    # 点赞红点
  favourReadIndex       int32                    3            # 已读点赞数量
  commentRedDot         bool                     4   +nodb    # 评论红点
  commentReadIndex      int32                    5            # 已读评论数量

@SelfFavourInfo     # 点赞列表
  id                    int64                    1 +key
  favourTimeMs          int64                    2          # 点赞时间

@SelfMessageSlip
  selfSlipId              set<int64>                      1       #我的留言
  slipRedDotMap           map<MessageSlipRedDotInfo>      2       #留言红点信息
  lastSlipTimeMs          int64                           3       #上次留言时间
  selfFavourSlipMap       map<SelfFavourInfo>             4       #点赞列表

@ActivityStatus # 活动状态
   fsmStatus      com.tencent.wea.xlsRes.ActivityFSMStatus   1
   beginTimeSec   int64                                      2
   endTimeSec     int64                                      3

@ActivityLabel
  id                         int64                                1    +key
  content                    string                               2
  labelLevel                 int64                                3

@ActivityRedDot
  redDotType                int32                   1    +key       #见 ActivityRedDotType
  redDotIds                 set<int64>              2

@TaskCompleteInfo
   taskId                  int32                           1   +key    # 任务id
   completeTimeMs          set<int64>                      2           # 完成时间，考虑任务可重复完成

@ActivityTaskCompleteInfo
   taskList                 map<TaskCompleteInfo>           1       # 任务完成信息

@ActivityUnit # 活动
   id                        int32                                1    +key
   redDotShow                bool                                 3    +nocs        # 是否显示红点
   isRead                    bool                                 4    +nocs        # 是否已读
   detailData                ActivityDetail                       5                 # 活动详情
   createTimeMs              int64                                6    +nocs        # 活动创建时间
   clickRedDotInfo           map<ActivityRedDot>                  7    +nocs        # 点击消失红点
   clearRedDotInfo           ClearRedDotInfo                      8    +nocs        # 擦除红点逻辑
   taskCompleteInfo          ActivityTaskCompleteInfo             9    +nocs        # 任务完成记录
   isHiddenInCli             int32                                10                # 是否客户端屏蔽活动(默认0显示 其他不显示)
   modules                   map<ActivityModule>                  11   +nocs        # 功能模块

@ClearRedDotInfo
   filterTask               set<int32>                    1    # 已经擦除红点的任务
   filterCommodity          set<int32>                    2    # 已经擦除红点的商品ID
   updateTimeMs             int64                         3    # 更新时间

@ActivityModule
  moduleType                int32                       1 +key      # 模块类型
  moduleData                ActivityModuleData          2           # 模块数据

@ActivityModuleData +oneof
  quizData                  QuizData                      1     # 答题
  lotteryData               LotteryData                   2     # 抽奖

@LotteryData
  lotteryCount          int32       1         # 已抽次数
  lotteryHistory        map<KvLL>   2         # 抽奖历史(configId,time)
  lotteryResultMap      map<KvII>   3         # 抽奖结果计数统计(configId,count)


@QuizQuestionData
  id                int32           1    +key       # 题目id
  answeredChoice    set<int32>      3               # 已答过的选项id 仅支持单选
  correct           bool            4               # 答题是否正确

@QuizData
  questionList      map<QuizQuestionData>   1   # 题目列表
  isReward          bool                    2   # 是否已领奖

@ActivityDetail +oneof
  luckyMoneyData            LuckyMoneyActivity            1   # 红包类活动
  shareActiveData           ShareActiveActivity           2   # 实时干预活动
  wealthBankData            WealthBankActivity            3   # 理财银行活动
  checkInPlanData           CheckInPlanActivity           4   # 打卡计划活动
  interServerGiftData       InterServerGiftActivity       5   # 全服礼包活动
  takeawayData              TakeawayActivity              6   # 挂机外卖活动
  depositData               PlayerDepositInfo             7   # 招财储蓄活动
  commonIntValue            int32                         8   # 活动通用IntValue
  luckyBalloonData          LuckyBalloonData              9   # 幸运气球活动
  recruitData               PlayerRecruiteInfo            10  # 召集令活动
  timeLimitedCheckInData    TimeLimitedCheckInActivity    11  # 开学返利活动
  scratchOffTicketsData     ScratchOffTicketsActivity     12  # 刮刮乐类活动
  redPacketData             RedPacketActivity             13  # 春节发红包活动
  accumulateBlessingsData   AccumulateBlessingsInfo       14  # 积攒福气活动
  superLinearRedeem         SuperLinearRedeem             15  # 超级线性兑换
  teamRankData              TeamRankActivity              16  # 组队排位
  ultramanThemeData         UltramanThemeActivity         17  # 奥特曼主题活动
  luckyStarData             LuckyStarActivity             18  # 福星手账簿活动
  springBlessingCollection  SpringBlessingCollectionAttr  19  # 春节集福活动
  springPrayActivityData    SpringPrayActivityAttr        20  # 财神祈福活动
  useItemShareActivityData  UseItemShareActivity          21  # 使用道具分享活动
  ultramanThemeTeam         UltramanThemeTeam             22  # 奥特曼多人小队活动
  upgradeCheckInManualData  UpgradeCheckInManualActivity  23  # 升级版打卡手册活动
  lotteryDrawData           LotteryDrawActivity           24  # 不放回抽奖活动
  kungFuPandaData           KungFuPandaData               25  # 功夫熊猫活动
  stickerData               StickerData                   26  # 贴纸簿活动
  competitionWarmUpAttr     CompetitionWarmUpActivityAttr 27  # 赛事热身活动
  intelligenceStationData   IntelligenceStationData       28  # 娱乐情报站活动
  flyingChessActivityInfo   FlyingChessActivityInfo       29  # 飞行棋活动(走格子)
  hynActivityData           HalfYearNavigationActivity    30  # 半周年庆导航栏活动
  musicOrderData            MusicOrderActivityAttr        31  # 音乐订单活动
  hywarmupActiviyData       HalfYearWarmUpActivity        32  # 半周年预热打卡活动
  wishActivityData          WishActivityData              33  # 心愿活动数据
  clubChallengeActivityData  ClubChallengeActivity        34  # 社团挑战
  animalHandbookData        AnimalHandbookActivityAttr    35  # 动物图鉴活动
  monopolyActivityData      MonopolyActivityData          36  # 大富翁活动
  minesweeperActivityData   MinesweeperActivity           37  # 扫雷活动
  groupingReturnData        GroupingReturnData            38  # 团购返利
  themeAdventureData        ThemeAdventureData            39  # 星际奇遇活动
  paidUnlockActivityData    PaidUnlockActivityData        40  # 付费解锁活动
  bookOfFriends             BookOfFriends                 41  # 农场花房
  wishingTreeActivityData   WishingTreeActivityData       42  # 许愿树活动
  fishingHallOfFameData     FishingHallOfFameActivityData 43  # 钓鱼名人堂活动
  luckyTurntableData        LuckyTurntableActivityData    44  # 幸运币转盘
  weekenIceBrokenData       WeekenIceBrokenData           45  # 周末破冰
  danceOutfitData           DanceOutfitData               46  # 舞会换装活动数据
  prayerCardData            PrayerCardActivityData        47  # 福牌活动数据(国庆活动)
  superCoreRankData         SuperCoreRankActivityData     48  #
  conanWarmupData           ConanWarmupActivityData       49  # 柯南预热活动数据
  luckyRebateData           LuckyRebateActivityData       50  # 幸运返利活动数据
  captureShadowData         CaptureShadowData             51  # 追击黑影人(周年庆收口活动)
  werewolfFullReducedConfData  WerewolfFullReducedConfData   52  # 狼人满减活动数据
  fishingFameData           FishingFameActivityData       53  # 钓鱼名人堂二期
  doubleDiamondActivityData DoubleDiamondActivityData     54  # 双倍星钻活动
  travelingDogData          TravelingDogData              55  # 旅行狗狗日历
  anniversaryMoba           AnniversaryMoba               56  # MOBA周年庆-送虞姬
  pickFactionActivityData   PickFactionActivityData       57  # 阵营pk活动
  mobaSquadDrawRedPacketData MobaSquadDrawRedPacketData   59  # moba组队抽红包活动
  commonArrayList           CommonArrayList               60  # 活动通用ArrayList
  fashionFundData           FashionFundData               61  # 时装基金活动
  findPartnerData           FindPartnerData               62  # 匹配搭子活动
  foodFestivalData          FoodFestivalData              63  # 农场美食节活动
  scoreGuideData            ScoreGuideData                64  # 评分引导活动
  newYearSign               NewYearSign                   65  # 新春签到
  springSlipData            SpringSlipData                66  # 新春福签活动
  farmBuffWish              FarmBuffWish                  67  # 农场回流助力
  mobaRandomVote            MobaRandomVote                68  # 峡谷随机投票
  farmReturningTask         FarmReturningTask             69  # 农场回流-任务
  amusementParkData         AmusementParkData             70  # 游乐园活动
  puzzleData                ActivityPuzzleData            71  # 拼图活动
  twoPeopleSquad            AttrTwoPeopleSquadData        72  # 双人成团活动数据
  mobaChallengeData         MobaChallengeData             73  # 峡谷挑战活动数据
  conanIpActiveData         ConanIpActiveData             74  # 柯南二期ip活跃数据
  wolfReturnData            WolfReturnData                75  # 狼人召回数据
  farmDailyAwardActivity    FarmDailyAwardActivity        76  # 农场天天领活动
  featureIntegrationData    FeatureIntegrationData        77  # 特色玩法内容整合模板数据
  farmAnwserData            FarmAnwserData                78  # 农场答题数据
  testActTeamData           TestActTeamData               79  # 队伍demo数据
  inflateRedPacket          AttrInflateRedPacketData      80  # 膨胀爆红包活动数据
  treasureLevelUpData       TreasureLevelUpData           81  # 宝箱升级活动
  restaurantThemed          AttrRestaurantThemed          82  # 餐厅主题活动
  flashRaceCheeringDate     FlashRaceCheeringData         83  # 闪电助威活动
  qingShuangTrialData       QingShuangTrialData           84  # 晴霜试炼
  farmDragonData            FarmDragonData                85  # 农场组队
  levelUpChallenge          AttrLevelUpChallenge          86  # 重返农场-冲级挑战
  wolfTeamChestData         WolfTeamChestData             87  # 狼人组队宝箱
  summerVacationBPData      SummerVacationBPData          88  # 大型暑期BP活动
  oneDollarRaffleActivity   OneDollarRaffleData           89  # 一元抽奖活动
  summerNavigationBarData   SummerNavigationBarData       90  # 暑期导航栏
  treasureHunt              AttrTreasureHunt              91  # 秘境寻宝
  summerFlashMobData        SummerFlashMobData            92  # 暑期快闪
  collectDivineFarmerData   CollectDivineFarmerData       93  # 神农集灵活动
  mysteryShop               MysteryShop                   94  # 神秘商店
  displayBoardActivity      AttrDisplayBoardActivity      95  # 星友滴滴板活动
  superAffordableCard       SuperAffordableCard           96  # 超值卡活动
  richReturnActivityData    RichReturnActivityData        97  # rich短期回流追赶活动
  sevenDayCheckIn           AttrSevenDayCheckIn           98  # 七日签到BP活动
  smashEgg                  SmashEggData                  99  # 砸蛋活动
  returnLinearRedeem        ReturnLinearRedeemActivity    100 # 线性兑换和存钱罐活动
  exchangeChallenge         ExchangeChallengeData         101 # 兑换挑战活动
  farmReturnWelfareData     FarmReturnWelfareData         102 # 农场回归福利

@ExchangeChallengeData
    challengeStatus    map<KvII>        1  # 挑战状态
    curChallenge       int32            2  # 当前选择挑战

@ReturnLinearRedeemActivity
  piggyBank            int32         1   # 当前存钱罐金额

@SmashEggData
    rewards            map<KvLL>        1  # 获取的奖励
    restTimeMs         int64            2 +nocs  #  刷新时间

@SuperAffordableCard
  buyCard             bool          1   # 是否购买超值卡
  ownBigReward        set<int32>    2   # 拥有过的大奖itemId
  countItemNum        int32         5   # 统计道具数量

@CollectDivineFarmerData
  periodicRewardsSet        set<int32>          1   # 周期性已领取奖励


  @SummerFlashMobData
    taskWeek                int32                              1 # 当前任务索引
    refreshTime             int64                              2 +nocs  # 刷新时间


@SummerNavigationBarData
  baJiRewardInfo          map<SummerNavigationBaJiReward>    1 # 吧唧奖励

@SummerNavigationBaJiReward
  taskId                 int32                          1  +key  #奖励对应任务id
  status                 int32                          2        #奖励状态 0未完成 1可领取 2已领取

@OneDollarRaffleData
  awardList                    map<UpgradeAwardInfo>          1                #领奖列表
  isUpgrade                    bool                           2                #是否已升级
  awardCount                   int32                          3                #领奖次数
  lastAwardTimeMs              int64                          4                #上次领奖时间
  expiredMailLimit             bool                           5                #补充邮件次数限制

@UpgradeAwardInfo
  id                           int32                          1    +key
  isGetRewards                 bool                           2    #奖励是否已领取

@SummerVacationBPData
  lastLevel                    int32                           1#上次等级
  nowLevel                     int32                           2#等级
  rewardedLevel                set<int32>                      3#等级
  exp                          int32                           4#经验
  rewardedBuff                 set<int32>                      5#等级


@WolfTeamChestData
  teamId                    int64                           1
  wolfTeamAwardRecordMap    map<WolfTeamAwardRecordData>    2  # 抽奖记录
  residueCount              int32                           3  # 剩余的抽奖次数
  itemCount                 map<KvLL>                       4  # 抽中的道具数量itemId,count
  lotteryDrawsCount         int32                           5  # 已抽奖的次数
  todayCount                int32                           6  # 当天抽奖次数


@WolfTeamAwardRecordData
  createTime               int64    1     +key       # 记录创建的时间
  itemId                   int32    2                # 道具ID
  itemCnt                  int64    3                # 道具数量
  type                     int32    4                # 1消耗抽奖次数,2每日免费,3GM


@FarmDragonData
  teamId              int64               1
  personalPoint       int32               2       # 个人积分

@FlashRaceCheeringData
  voteNum                 int32         1       # 当前总投票数
  voteCacheData           map<KvLL>     2       # 等待增加的票缓存


@TreasureLevelUpData
  energy                  int32         1       # 当前能量
  boxId                   int32         2       # 当前宝箱Id
  lastRefreshTime         int64         3       # 上次刷新时间
  boxStatus               int32         4       # 宝箱状态 0未领取 1已领取

@QingShuangTrialData
  trialScore          int32             1       # 试炼积分
  receivedReward      int32[]           2       # 领取过的积分奖励
  fiveDimensions      int32[]           3       # 五维图



@FeatureIntegrationData
  curAwardNum       int32                1       # 当天已领取奖励数
  pictureSet        set<int32>           2       # 已点击的展示期图片id

@FarmAnwserData      #农场答题数据
  answeredDays        int32             1       # 已答题到第几天
  isCompletetask      bool              2       # 这个任务是否已经完成

@ConanIpActiveData
  buff        bool        1   # 是否有buff,true有buff,每日0点重置
  awardCount  int32       2   # 领取奖励次数,每日0点重置

@WolfReturnData
   wolfReturnState            int32                     1                 # 狼人召回状态 1不可召回 2可召回
   wolfRewardRecord           map<KvLL>                 2                 # 狼人奖励 id-已领取次数
   redClickOpen               int32                     3                 # 红点标记
   wolfHistory                map<WolfHistoryData>      4                 # 邀请历史记录
   sendUid                    int64                     5                 # 邀请人uid
   coinGetRecord              map<KvLL>                 6                 # 活动代币获取数量记录
   eventRecord                map<KvLL>                 7                 # 触发事件次数
   conditionGroup             ConditionGroup            8                 # 条件组
   receiveList                set<int64>                9                 # 接收自己邀请的玩家(临时)
   newWolf                    int32                     10                 # 是否狼人新玩家
   receiveChannel              int32                    11                 # 接收邀请时渠道

@WolfHistoryData
    id                   string                               1   +key    # uid+times
    uid                   int64                               2           # 触发人uid
    times                 int64                               3           # 触发时间
    conditionId           int32                               4           #
    itemRecord             map<KvLL>                          5           # 获取的道具信息 key:itemId value:itemCount

@FarmReturningTask
   currentTaskId              int32                     1                 # 当前任务
   taskProcess                int32                     2                 # 当前任务进度index

@MobaRandomVote
  todayVoteEventId          int32                         1 +nocs  # 今日投票事件ID
  lastVoteTimeMs            int64                         2 +nocs  # 上次投票时间
  todayVoteNum              int32                         3 +nocs  # 今日投票次数
  totalVoteNum              int32                         4 +nocs  # 累计投票次数

@MysteryShop
  DrawCommodityIds          set<int32>                    1 +nocs  # 神秘商店抽取商品
  todayRefreshCount         int32                         2 +nocs  # 今日刷新次数
  lastRefreshTimeMs         int64                         3 +nocs  # 上次刷新时间
  alreadyBuyCommodityIds    set<int32>                    4 +nocs  # 神秘商店已购买商品

@FarmBuffWish
  commonArrayList           CommonArrayList               1  # 助力奖励领取状态
  buffInfo                  map<KvLL>                     2  # 增益buff过期时间

@NewYearSign
  commonArrayList           CommonArrayList               1  # 已签到日期
  reSignTimes               int32                         2  # 补签获得次数
  reSignUseTimes            int32                         3  # 补签使用次数

@FindPartnerQuestionAnswer
  questionId            int32         1   +key    # 题目id
  choiceId              int32         2           # 选项id

@FindPartnerData
  questionList          map<FindPartnerQuestionAnswer>      1           # 题目列表
  partnerUid            int64                               2           # 搭子uid
  trophyCnt             int32                               3           # 已获得奖杯数量

@AttrTwoPeopleSquadData
  itemNum               int64                               1           # 已获得合柿值数量

@ActivityTwoPeopleSquadData
  memberData            map<TwoPeopleSquadMemberData>       1           # 双人成团-小队成员数据
  memberTaskData        map<TwoPeopleSquadTaskData>         2           # 双人成团-小队任务数据

@TwoPeopleSquadMemberData
  uid                   int64                               1   +key    # uid
  loginEpochMillis      int64                               2           # 上次登陆时间
  lastOnlineEpochMillsRefreshEveryDay     int64             3           # 上次在线时间, 每天刷新1次
  battleTimes           int64                               4           # 对局数
  lastBattleEpochMillis int64                               5           # 上次对局时间戳毫秒

@TwoPeopleSquadTaskData
  taskId                int32                               1   +key    # 任务id
  completeData          map<TwoPeopleSquadTaskCompleteData> 2           # 完成完成时间戳毫秒

@TwoPeopleSquadTaskCompleteData
  completePlayerUid     int64                               1   +key    # 完成者
  completeEpochMillis   int64                               2           # 完成完成时间戳毫秒

@AttrInflateRedPacketData
  currMoney             int32                               1           # 当前红包金额
  # isCharged             bool                                2           # 是否充值过任意一档红包
  isUnlock              bool                                2           # 是否解锁(充值过任意一档红包)
  canInflateTimes       int32                               3           # 可膨胀次数
  redPacketMoney        map<InflateRedPacketMoney>          4           # 每个金额的红包信息
  needUpdate            bool                                5           # 是否需要把活动数据需要同步到小队中
  attrUnlockedUidList   int64[]                             6           # 已经算过膨胀次数的uid列表
  memberNum             int32                               7           # 已解锁小队人数
  fullEpochSecs         int64                               8           # 小队满员时间戳秒

@InflateRedPacketMoney
  money                 int32                               1   +key    # 红包金额
  isFirstTimeInflated   bool                                2           # 是否已经首次免费膨胀
  inflateNum            int32                               3           # 当前膨胀数量(累计已获得钻石数量)
  inflatedTimes         int32                               4           # 已膨胀次数
  chargeEpochSecs       int64                               5           # 充值时间戳秒
  receiveEpochSecs      int64                               6           # 领取时间戳秒

@ActivityInflateRedPacketData
  memberData            map<InflateRedPacketMemberData>     1           # 小队成员数据
  # unlockedUidList       set<int64>                          2           # 已经算过膨胀次数的uid列表
  unlockedUidList       int64[]                             2           # 已经算过膨胀次数的uid列表

@InflateRedPacketMemberData
  uid                   int64                               1   +key    # uid
  isUnlock              bool                                2           # 是否解锁
  money                 int32                               3           # 当前膨胀数量红包金额
  inflateNum            int32                               4           # 当前膨胀数量 (÷初始数量为比例)

@ScoreGuideData
  conditionGroup          ConditionGroup                        1 +nocs   # 条件组
  totalGuideTimes         int32                                 2 +nocs   # 累计弹窗次数
  nextGuideTime           int64                                 3 +nocs   # 下次可触发弹窗时间(时间戳)
  activityStatus          int32                                 4 +nocs   # 用户活动状态(0.未完成 1.已完成)


@MobaChallengeData
  challengeLevelinfo      map<ChallengeLevelinfo>              1    #任务状态

@ChallengeLevelinfo
  levelId                 int32                                 1 +key         #关卡id
  levelStauts             int32                                 2         #关卡状态


@FashionFundData
  purchasedGoods        set<int32>             1      #已购买的FashionFundData配置id
  beforeReturn          set<int32>             2      #FashionFundData配置id,
  afterReturn           set<int32>             3      #FashionFundData配置id

@CommonArrayList
   list                     int64[]                   1  #通用List

@MobaSquadDrawRedPacketData
   squadInfos               map<MobaSquadDrawRedPacketSquadInfo>    1 +nocs   # 组队数据
   mobaTaskInfo             map<MobaSquadDrawRedPacketSquadTask>    2         # 任务数据
   remainingDrawCount       int32                                   3         # 剩余抽奖次数
   refreshTimeMs            int64                                   4 +nocs   # 刷新时间MS
   taskRecord               MobaSquadDrawRedPacketDailyTaskRecord   5 +nocs   # 历史任务的记录
   historyTaskRecord        map<MobaSquadDrawRedPacketDailyTaskRecord> 6 +nocs #历史的每日任务记录


@MobaSquadDrawRedPacketSquadInfo
   dateTime             string             1 +key        # 组队的时间 yyyy-mm-dd
   memberIds            set<int64>         2             # 组队成员

@MobaSquadDrawRedPacketDailyTaskRecord
   dateTime             string             1 +key        # 组队的时间 yyyy-mm-dd
   mobaTaskInfo         map<MobaSquadDrawRedPacketSquadTask>    2         # 任务数据

@MobaSquadDrawRedPacketSquadTask
   taskId               int32              1 +key        #任务ID
   taskCompleteCount    int32              2             #完成次数

@AmusementParkData
   limits               map<AmusementParkLimit>     1     #限制数据

@AmusementParkLimit
   limitId             int32               1 +key       #limit限制的ID
   value               int32               2            #limit的当前值
   lastRefreshTime     int64               3            #上次刷新时间

@RichReturnActivityData
   drawCnt             int32               1            # 砸蛋次数
   lotteryRewards      map<KvII>           2            # 已抽到的数据
   startTimeMs         int64               3            # 开始时间
   endTimeMs           int64               4            # 结束时间

@FarmReturnWelfareData
   farmLv              int32               1            # 活动开启时的农场等级
   drawLevelReward     set<int32>          2            # 已经领取过的等级奖励
   farmBuff            int32               3            # 抽取到的buff
   enterUgcFarm        int32               4            # 进入过UGC农场 0未进入 1进入了

@AnniversaryMoba
   bigRewardKey          string            1              # 赠礼码
   drawRewardIndex       set<int32>        2              # 已抽中奖励index

@WerewolfFullReducedConfData
  cartDataAttr     map<WerewolfFullReducedCartDataAttr> 1
  werewolfFullReducedConf map<WerewolfFullReducedConf> 2

@WerewolfFullReducedCartDataAttr
  commodityId             int32         1 +key                # 商品id
  itemNums                int32         2                     # 商品数量

@WerewolfFullReducedConf
   couponID                int32         1 +key           # 优惠券ID
   limitNum                int32         2                # 限领数量
   useTime                 int64         3                # 使用时间
   accumulatedAmount       int64         4                # 大奖已累计金额
   receiveFinalist         bool          5                # 大奖是否已领取
   extraGiftNum            int32         6                # 额外赠送数量

@CaptureShadowData
    canRewardFreeReward       bool                              1  # 是否领取免费奖励 true代表领取过了
    captureCount              int32                             2  # 抓捕成功次数
    receivedOneTimeRewardIds  int32[]                           3  # 已经领取过的一次性奖励id
    playerGrid                int32                             4  # 玩家所在格子-相对起点的位置
    targetGrid                int32                             5  # 黑影人所在格子-相对起点的位置
    mapCount                  int32                             6  # 地图刷新次数
    targetBuff                int32                             7  # 黑影人麻痹buff次数
    shoeGuaranteeTimes        int32                             8  # 增强鞋当前保底次数
    beginGrid                 int32                             9  # 当前圈起始位置-绝对位置
    receivedCaptureRewardId   int32[]                           10 # 领取过的抓捕奖励id
    costCurrencyNum           int32                             11 # 已经消耗的代币数量



@DanceOutfitData
    lastRefreshDailyTimeMs       int64                    1  # 上次每日重置时间
	danceOutfitSelectId          int32                    2  # 当前生成的服装id
	danceOutfitEquipId           int32                    3  # 当前穿戴的服装id
	danceOutfitEquipMs           int64                    4  # 上次穿戴服装时间
	danceOutfitChangeMs          int64                    5  # 上次变装时间-穿戴或者使用道具都会更新
	dressOutfitEquipHistory      map<OutfitHistoryData>   6  # 最近穿戴的服装历史记录
	dressOutfitGearHistory       int32[]                  7  # 最近生成的三件时装
	dressOutfitHistoryMaxCount   int32                    8  # 获得舞会服装最大值
	dressOutfitCollectHistory    map<DressOutfitCollectHistory> 9  # 收藏的服装数据

@OutfitHistoryData
  index                       int64                             1 +key  # index
  collectState                bool                              2       # 是否收藏
  outfitEquipId               int32                             3       # 服装id
  sortId                      int32                             4       # 位置

@DressOutfitCollectHistory
  sortId                       int32                            1 +key  # sortId 收藏的位置
  index                        int64                            2       # 收藏的index


@WeekenIceBrokenData
   rewardKey                string                  1   # 开奖号码
   rewardState              int32                   2   # 0:未购买 1:未开奖 2:已中奖
   rewardItem               int32                   3   # 中奖道具
   buyTimeMs                int64                   4   # 参与时间

@PrayerCardInfo # 祈福牌数据
  prayerCardId              int32           1 +key      # 祈福牌id
  isLooked                  bool            2           # 是否查看过(查看即领奖(跟策划文档中描述保持一致))
  acquireNum                int32           3           # 已获取数量

@PrayerCardGiveRecord # 祈福牌赠送记录
  giveNum                   int64           1 +key      # 赠送数量作为key
  prayerCardId              int32           2           # 祈福牌id
  uid                       int64           3           # 赠送的玩家uid
  giveEpochSecs             int64           4           # 赠送时间戳(秒)

@PrayerCardFriendDailyGive # 祈福牌好友赠送信息
  uid                       int64           1 +key      # 赠送的玩家uid
  giveEpochSecs             int64           2           # 最后一次赠送时间戳(秒)
  giveNum                   int32           3           # 每天赠送的数量

@PrayerCardRewardActivityData # 领取奖励信息
  activityId               int32                            1 +key           # 活动id
  rewardItemInfo           map<PrayerCardRewardItemInfo>    2                # 奖励信息

@PrayerCardRewardItemInfo
  rewardItemId             int32            1 +key      # 奖励道具id
  totalNum                 int32            2           # 领取奖励总数量

@PrayerCardActivityData
  prayerCardInfo                     map<PrayerCardInfo>                        1       # 祈福牌数据
  prayerCardGiveRecord               map<PrayerCardGiveRecord>                  2       # 祈福牌赠送记录
  giveNum                            int32                                      3       # 赠送数量
  friendDailyGive                    map<PrayerCardFriendDailyGive>             4       # 祈福牌好友赠送信息
  rewardActivityData                 map<PrayerCardRewardActivityData>          5       # 领取奖励信息/

@SuperCoreRankActivityItemConsumeData
  id                        int64                              1 +key       # 道具id
  num                       int32                              2            # 道具数量

@SuperCoreRankActivityData
  initActiveDays            int32                                       2  +nocs     # 初始化活跃天数
  initTotalRmb              int32                                       3  +nocs     # 初始化中充值金额元
  itemConsumes              map<SuperCoreRankActivityItemConsumeData>   4  +nocs     # 活动期间关心的道具消耗
  lastSuccReportRankScore   int64                                       5  +nocs     # 最近一次成功上报排行榜的分数

@LuckyRebateActivityData
  consumptionAmount         int32                                       1           # 消费金额
  rewardLevel               int32                                       2           # 奖励等级
  rewardSettlement          int32                                       3           # 奖励结算
  intermediateResult        int32                                       4   +nocs   # 中间抽奖结果
  numberLimitIndex          int32                                       5   +nocs   # 中奖序号
  drawCnt                   int32                                       6   +nocs   # 抽奖次数
  topDrawCnt                int32                                       7   +nocs   # 大奖抽奖次数

@LuckyTurntableActivityData
  roundInfo                  map<LuckyTurntableRoundInfo>      1       # 轮次信息
  curRoundId                 int32                             3       # 当前轮次ID

@LuckyTurntableRoundInfo
  id                         int32                             1 +key  # 轮次ID
  isOpen                     bool                              2       # 是否已开启
  openTurntableId            int32                             3       # 开启的转盘ID

@DoubleDiamondActivityData
  levelData                  map<DoubleDiamondLevelData>      1       # 档位信息

@DoubleDiamondLevelData
  id                         int32                             1 +key  # 档位id
  rebateNum                  int32                             2       # 额外返钻次数

@TravelingDogData
  activityId                  int32                            1       # 活动id
  dogData                     map<TravelingDogInfoData>        2       # 档位信息
  dogStatus                   int32                            3       # 狗狗出行状态
  dogDate                     int32                            4       # 狗狗出行日期
  allCoinNum                  int64                            5       # 获取货币数量
  loginTime                   int64                            6       # 登录时间
  goHomeTime                  int64                            7       # 出行归来时间

@TravelingDogInfoData
   date                       int32                             1 +key  # 日期
   id                         int32                             2       # 画片Id

@FoodFestivalData
   activityId                  int32                            1       # 活动id
   elvesData                   map<ElvesData>                   2       # 档位信息
   allCoinNum                  int64                            3       # 获取货币数量
   lastChargedTime             int64                            4       # 上次收取货币时间
   bigRewardState              int32                            5       # 大奖领取状态 0未领取 1已领取

@ElvesData
   id                         int32                             1 +key  # 精灵id
   curGood                    int32                             2       # 当前好感度
   goodReward                 map<GoodReward>                   3       # 奖励档位

@GoodReward
   good                       int32                             1  +key # 好感度
   reward                     int32                             2       # 奖励档位


@PaidUnlockActivityData
  midasGot            string[]          1         # 已购买的商品列表

@FishingActivityRecordOfRank # 钓鱼名人堂活动记录(按排行榜id)
  rankId             int32             1 +key   # 排行榜id
  finalScore         int32             2        # 鱼分数
  fishId             int32             3        # 鱼id
  updateEpochSecs    int64             4        # 更新分数-秒时间戳

@FishingActivityRecord # 钓鱼名人堂活动记录
  activityId         int32             1 +key   # activityId
  fishingActivityRecord            map<FishingActivityRecordOfRank>      2        # 钓鱼名人堂活动记录(按排行榜id)

@FishingHallOfFameActivityData
  fishingActivityRecord            map<FishingActivityRecord>      1        # 钓鱼名人堂活动记录

@ConanWarmupActivityData # 柯南预热活动数据
  answeredDays        int32             1       # 已答题到第几天
  rewardedDaysList    set<int32>        2       # 已领奖的天数列表

@FishingFameRecord
  index                     int32       1 +key   # 索引(layer*1000+timeBucket)
  finalScore                int64       2        # 鱼分数
  fishId                    int32       3        # 鱼id

@FishingFameActivityData # 钓鱼名人堂二期
  fishingFameRecord         map<FishingFameRecord>      1        # 钓鱼名人堂活动记录
  lastRedDotTimeBucket      int32                       2        # 上次红点通知时段

@RestaurantThemedFood
  foodId                  int32                                 1 +key    # 印章id
  num                     int32                                 2         # 印章数量

@RestaurantThemedNpc
  npcId                   int32                                 1 +key    # npcId
  foodIdList              map<RestaurantThemedFood>             2         # 已获取的印章
  collectAllEpochMillis   int64                                 3         # 印章集齐时间
  servingNum              int32                                 4         # 上菜次数

@RestaurantThemedShareRecord
  id                      string                                1 +key    # foodId + receiverUid + epochMillis
  foodId                  int32                                 2         # 印章id
  npcId                   int32                                 3         # npcId
  receiverUid             int64                                 4         # 领取者uid
  epochMillis             int64                                 5         # 领取时间戳毫秒

@RestaurantThemedReceiveRecord
  epochMillis             int64                                 1 +key    # 领取时间戳毫秒
  npcId                   int32                                 2         # npcId # todo hdz
  foodId                  int32                                 3         # 印章id
  sharerUid               int64                                 4         # 分享者uid

@RestaurantThemedReceiveHistory
  foodId                  int32                                 1 +key    # 印章id
  uid                     set<int64>                            2         # 已经领取过印章的其他玩家uid

@AttrRestaurantThemed # 餐厅主题活动
  npcData                 map<RestaurantThemedNpc>              1         # NPC数据
  shareRecord             map<RestaurantThemedShareRecord>      2         # 分享记录(别人领取后才算成功)
  receiveRecord           map<RestaurantThemedReceiveRecord>    3         # 领取记录(通过别人的分享链接领取)
  receiveHistory          map<RestaurantThemedReceiveHistory>   4         # 领取历史
  lastReceiveEpochMillis  int64                                 5         # 上一次领取时间戳毫秒
  receivedTimes           int32                                 6         # 已领取次数(每天最多领取3次)
  isStage2                bool                                  7         # 是否到第2阶段

@TreasureHuntHistoryData
  excavateNum             int32                                 1 +key    # key
  itemId                  int32                                 2         # 道具id
  itemCnt                 int64                                 3         # 道具数量
  excavateEpochSecs       int64                                 4         # 时间戳秒

@AttrTreasureHunt # 秘境寻宝活动
  treasureHuntHistory     map<TreasureHuntHistoryData>          1         # 挖宝历史(最大30条)
  lotteryDrawNum          int32                                 2         # 已抽次数
  excavateNum             int32                                 3         # 挖掘次数

@AttrDisplayBoardLotteryData
  id                      int32                                 1 +key    # 抽奖id
  lotteryNum              int32                                 2         # 已抽次数

@AttrDisplayBoardLotteryHistory
  lotteryEpochMills       int64                                 1 +key    # 时间戳毫秒
  itemId                  int32[]                               2         # 道具id
  itemCnt                 int64[]                               3         # 道具数量

@AttrDisplayBoardActivity # 星友滴滴板活动
  trialEpochSecs          int64                                 1         # 试用时间戳秒
  lotteryTotalNum         int32                                 3         # 总共已抽次数
  lotteryEpochMills       int64                                 2         # 时间戳毫秒
  lotteryHistory          map<AttrDisplayBoardLotteryHistory>   4         # 历史

@AttrSevenDayCheckIn
  lastFreshSecs           int64                                 1         # 上次计算累计登录时间的时间戳每秒
  loginCumulativeDays     int32                                 2         # 累计登录天数
  checkInData             map<AttrSevenDayCheckInData>          3         # 档位信息

@AttrSevenDayCheckInData
  paidMoney               int32                                 1 +key    # 付费档位(0:免费)
  chargeEpochSecs         int64                                 2         # 充值时间戳秒(0:未充值)
  rewardedDays            set<int32>                            3         # 已领取天数

@AttrLevelUpChallenge # 重返农场-冲级挑战
  critEpochSecs           int64                                 1         # 暴击时间戳秒
  sampleGroupId           int32                                 2         # 暴击样本组id

@PickFactionActivityData # 阵营pk活动
  playerPickFactionData         map<PickFactionRecord>      1        # 阵营PK活动

@PickFactionRecord
  index                     int32       1 +key   # 阵营配置ID
  pickFaction               string      2        # 选择阵营
  getBigReward              bool        3        # 是否领取大奖
  settleFaction             bool        4        # 是否已经结算阵营奖励
  taskScore                 int32       5        # 任务获取的应援值
  battleScore               int32       6        # 对局获取的应援值
  dailyBattleAddScore       int32       7        # 对局每日获取的应援值
  refresTimeMs              int64       8        # 对局获取的应援值刷新时间

@ThemeAdventureDailyTaskData  # 星界奇遇每日任务数据
  gameId              int32             1 +key  # 玩法ID
  score               int32             2       # 进度积分
  maxScore            int32             3       # 最大积分，达到后不再增加积分
  rewardConfigIndex   set<int32>        4       # 已领取奖励的阶段ID

@ThemeAdventureRewardUpgradeData # 星界奇遇盲盒升级数据
  id                  int64             1 +key  # 索引ID
  itemId              int32[]           2       # 各次升级品质对应的礼包ID
  finalItemId         int32             3       # 最终的礼包ID
  rewarded            bool              4       # 是否已领取奖励
  sourceItemId        int32             5       # 源礼包ID
  orderNo             string            6 +nocs # 流水ID

@ThemeAdventureData # 星界奇遇活动数据
  lastRefreshDailyTaskTimeMs  int64                                 1       # 上次刷新每日任务活动时间
  dailyTaskData               map<ThemeAdventureDailyTaskData>      2       # 每日任务数据
  rewardUpgradeData           map<ThemeAdventureRewardUpgradeData>  3 +nocs # 奖励升级数据

@GroupingReturnData
   groupId                  int64                     1   # 组队ID
   rewardStage              int32                     2   # 已领奖ID
   buyCommodity             bool                      3   # 已购买商品

@IntelligenceStationData
  totalValue        int32            1  # 废弃
  dailyValue        int32            2
  alreadyReward     set<int32>       3  # 废弃

@LuckyStarInfo
   uniqueId             int64             1     +key    # 福星卡唯一id
   starId               int32             2             # 福星id
   blessId              int32             3             # 祝福语
   giveLock             bool              4             # 锁定，不可赠送
   getTimeMs            int64             5             # 获得时间
   giverUid             int64             6             # 赠送人uid

@LuckyStarActivity
   roundNum             int32               1   +nocs   # 第几阶段
   starInfo             map<LuckyStarInfo>  2   +nocs   # 福星卡信息
   isReward             bool                3   +nocs   # 是否已领奖
   roundDrawNum         int32               4   +nocs   # 本阶段已抽奖次数
   receiveStarNum       int32               5   +nocs   # 活动期间已领取别人赠送的数量
   giveRequiredMap      set<int64>          6   +nocs   # 获得索要的列表

@SpringSlipData
   roundNum             int32               1           # 第几阶段
   rewardInfo           map<SpringSlipRewardInfo>  2    # 福签获奖统计信息
   normalAssistRequest  string              3   +nocs   # 普通助力请求id
   assistRecord         map<SpringSlipAssistRecord>  4    # 福签助力记录
   beAssistedRecord     map<SpringSlipAssistRecord>  5    # 福签被助力记录
   giveRecord           map<SpringSlipTradeRecord>  6    # 福签共享记录
   receiveRecord        map<SpringSlipTradeRecord>  7    # 福签接受记录
   limitInfo            map<SpringSlipRewardLimit>  8    # 福签

@SpringSlipRewardInfo
   itemId                int32             1     +key    # 奖品id
   itemNum               int32             2             # 奖品数量

@SpringSlipRewardLimit
  id                      int64             1     +key        # 奖品id
  rewardNum               int32             2             # 活动次数

@SpringSlipAssistRecord
   recordId              string            1     +key   # 助力id
   assistUid             int64             2            # 助力人id
   beAssistUid           int64             3            # 被助力人id
   assistTimeMs          int64             4            # 助力时间
   assistType            int32             5            # 助力类型

@SpringSlipTradeRecord
   recordId              string            1     +key   # 福签交易记录
   giveUid               int64             2            # 共享人人id
   receiveUid           int64             3             # 索要人id
   tradeTimeMs          int64             4            # 交易时间
   tradeType            int32             5            # 交易类型
   slipId               int32             6            # 交易福签id

@UltramanThemeTeam
    teamId              int32               1           # 小队id
    isCaptain           bool                2           # 是否是队长
    members             set<int64>          3           # 队伍成员
    friendshipValTeam   int32               4           # 小队友情值
    friendshipVal       int32               5           # 个人友情值
    joinTeam            bool                6           # 是否加入了队伍
    joinTeamSec         int64               7           # 入队时间
    friendshipValDaySt  int32               8           # 增加的个人友情值
    friendshipValDayEd  int32               9           # 结束时个人友情值
    yesterdayEndMs      int64               10          # 昨天结束的时间

@UltramanThemeActivity
    selfCollectionProgress int32                1   # 个人收集进度
    hasVisitedStory        set<int32>           2   # 已经访问过的剧情
    hasAchievedReward      set<int32>           3   # 已经获得奖励
    isInTeam               bool                 4   # 是否加入了队伍(废弃)

@UpgradeCheckInManualActivity
  lastCheckInTimeMs              int64                          1                #上次打卡时间
  checkInList                    map<UpgradeCheckInInfo>        2                #打卡列表
  isUpgrade                      bool                           3                #是否已升级
  checkInCount                   int32                          5                #打卡次数

@UpgradeCheckInInfo
  id                        int32                  1    +key
  isGetBaseRewards          bool                   2                #基础奖励是否已领取
  isGetHigherRewards        bool                   3                #高级奖励是否已领取

@FarmDailyAwardActivity
  lastCheckInTimeMs              int64                          1                #上次打卡时间
  checkInList                    map<UpgradeCheckInInfo>        2                #打卡列表
  isUpgrade                      bool                           3                #是否已升级
  checkInCount                   int32                          5                #打卡次数
  weekendAwardTime               int64                          6                #领取周奖励时间
  activiytBuyTimes                int32                          7                #活动期间购买次数
@KungFuPandaData
  lastResetTimeMs                    int64                        1                #上次重置时间
  helpDataList                       map<KungFuPandaHelpData>     2     +nocs      #助力列表
  latestRacingCostTimeMs             int32                        3                #最新竞速最终耗时
  feedingCount                       int32                        4                #投喂数量
  latestInitRacingCostTimeMs         int32                        5                #最新竞速初始耗时
  totalFeedNoodleCount               int32                        6                #累计投喂汤面数量
  racingRankRewardRecord             map<RacingRankRewardRecord>  7     +nocs      #竞速排行榜奖励数据

@KungFuPandaHelpData
  id                       int32                1    +key
  friendUid                int64                2               #助力好友
  helpTimeMs               int32                3               #助力时间毫秒
  updateTimeMs             int64                4               #更新时间

@RacingRankRewardRecord
  rankDay                      int64                1    +key
  updateTimeMs                 int64                2           #更新时间

@TeamRankActivity #组队排位
   teamRankTimes           map<TeamRankInfo>    1

@TeamRankInfo
   id                      int32                1   +key
   usedTimes               int32                2
   updateTime              int64                3

@SuperLinearRedeem #超级线性兑换 奖励
   currentValue              int32                      1 # 当前进度
   rewardInfo                map<RewardStatusInfo>      2 # 奖励状态

@RewardStatusInfo
   rewardId                 int32                                 1 +key #奖励ID
   rewardStatus             com.tencent.wea.xlsRes.RewardStatus   2      #领奖状态

@AccumulateBlessingsInfo
    blessingValue         int32        1          # 福气值
    assistFriends         int64[]      2          # 助力好友列表
    assistCnt             int32        3          # 今日助力次数
    onlineTimeMs          int64        4          # 今日在线时长
    onlineRewardStatus    bool         5          # 已领取今日在线奖励
    loginCumulative       int32        6          # 累计登录天数
    loginRewardStatus     int32[]      7          # 已领取累计登录奖励
    lastLoginDay          int64        8  +nocs   # 上次登录时间
    invitationId          int64        9          # 邀请id

@TimeLimitedCheckInAttr # 时间段签到数据
  configIndex            int32       1 +key  # 时间段配置索引ID，从0开始
  checkInCount           int32       2       # 登录次数
  rewardedCheckInCount   set<int32>  3       # 已领取奖励的签到天数

@TimeLimitedCheckInActivity # 开学返利活动
  unlockTimeMs              int64                           1 # 解锁时间，<=0 表示未解锁
  lastCheckInTimeMs         int64                           2 # 上次登录时间
  checkInAttr               map<TimeLimitedCheckInAttr>     3 # 签到数据

@SpringBlessingCollectionCardInfo # 春节集福卡牌数据
  cardId          int32                                             1 +key  # 福字卡牌ID
  status          com.tencent.wea.xlsRes.SpringBlessingCardStatus   2       # 状态
  rewardId        int32                                             3       # 已领取的奖励，用于展示
  sourceType      com.tencent.wea.xlsRes.BlessingCardSourceType     4 +nocs # 来源
  sourceId        int64                                             5 +nocs # 来源ID，主要记录投放的玩家ID，或者赞助商的ID

@SpringBlessingGetCdKeyAttr
  sponsorId       int32       1 +key  # 赞助商ID
  getCDKeyCnt     int32       2       # 获得CDKey次数
  lotteryCardCnt  int32       3       # 抽取福字次数

@SpringBlessingCollectionAttr # 春节集福活动数据
  refreshTimeMs       int64                                       1         # 刷新时间
  blessingCard        map<SpringBlessingCollectionCardInfo>       2         # 收集的卡牌列表
  stallLotteryCnt     int32                                       5         # 福字铺抽取次数
  sponsorLotteryCnt   int32                                       6         # 赞助商抽取次数 # 废弃，改成按赞助商计数
  giveCardCnt         int32                                       7         # 赠送福卡次数
  dayIndex            int32                                       8         # 天数索引，用来获取任务奖励
  shareTimeMs         int64                                       9         # 分享时间
  getTaskRewardTimeMs int64                                       10        # 领取任务奖励时间
  getCDKeyCntAttr     map<SpringBlessingGetCdKeyAttr>             11 +nocs  # 今日获取CDKey数量

@SpringPrayActivityAttr # 春节祈福财神活动
  todayPray     bool                                         1 # 今天已经找财神祈福了
  useLuckyBuff  bool                                         2 # 今天已经使用财神buff了

@InterServerGiftActivity
   buyNum                   int32                   1       # 已购买次数
   progressRewardedItem     set<int32>              2       # 已领取进度奖励
   finalRewardedItem        set<int32>              3       # 已领取终极大奖
   unlockPieces             set<int32>              4       # 已解锁拼图

@CheckInInfo
   weekDay              int32                                 1 +key   # 周几
   state                com.tencent.wea.xlsRes.CheckInState   2        # 签到状态
   checkTime            int64                                 3        # 签到时间

@WealthBankActivity
   checkInDays          map<CheckInInfo>    1   # 签到状态
   deposit              int32               2   # 存款
   makeUpTimes          int32               3   # 可补签次数
   depositReceivedNum   int32               5   # 存款已领取数量

@CheckInPlanActivity
   checkedDays              set<int32>          1   # 已签到
   drawnReward              set<int32>          3   # 已抽中的奖励
   lastNormalCheckTime      int64               4   # 上次正常打卡时间
   canDrawRewardNum         int32               5   # 可抽奖次数
   checkCumTimes            int32               6   # 累计打卡次数
   makeUpTimes              int32               7   # 可补签次数

@ShareActiveActivity
   state                   com.tencent.wea.xlsRes.RewardStatus    1  #奖励状态

@GiftRandomIndexInfo
    index                 int32              1      +key    # 序列号
    randomCount           int32              2              # 命中次数

@GiftRandomGroupInfo
    groupId               int32                         1      +key    # 奖励组ID
    randomIndexInfo       map<GiftRandomIndexInfo>      2              # 奖励命中信息

@GiftPackageRandomRecord
   giftId                 int32                         1   +key
   randomGroupInfo        map<GiftRandomGroupInfo>      2              # 奖励命中信息

@HalfYearNavigationDayInfo
    day                     int32           1    +key    # 第几天
    checkedIds              set<int32>      2            # 当天已经打卡的id
    isRewarded              bool            3            # 是否已经领奖

@HalfYearNavigationActivity
    checkedDays             map<HalfYearNavigationDayInfo>          1   # 每日活动记录

@HalfYearWarmUpDayInfo
    day                     int32           1    +key    # 第几天
    isMakeUp                bool            2            # 是否为补签
    isRewarded              bool            3            # 是否已经领奖
    checkInTimeStamp        int64           4            # 打卡时间戳

@HalfYearWarmUpActivity
    checkedDays             map<HalfYearWarmUpDayInfo>   1   # 每日活动记录
    makeUpTimes             int32                        2   # 补签次数
    makeUpDay               int32                        3   # 补签次数所属天数
    rewardedList            set<int32>                   4   # 已领取的进度奖励

@WishActivityData # 心愿活动
    giftIds                  set<int32>                1  # 已经确定的奖励
    giftState                int32                     2  # 心愿礼物状态 0.未完成 1.已领取
    wishVal                  int32                     3  # 心愿值
    raffleTicketGetCnt       int32                     4  # 抽奖券获取数量
	helpUid                  int64                     5  # 绑定助力好友Id
	helpState                int32                     6  # 助力状态 0.未完成 1.已完成
	helpAddVal               int32                     7  # 助力可以给好友增加心愿值数量
	recordData               map<WishRecordData>       8  # 心愿值获取记录
	recordDataByAward        map<WishAwardRecordData>  9  # 抽奖记录
	code                     int64                     10 # 邀请码
    helpedOpenIds            set<string>               11 # 助力过自己的Openid列表
	todayBeHelpCnt           int32                     12 # 今日被助力次数
	selfOpenId               string                    13 # 自己的openid
	lastResetTime            int64                     14 # 上次数据重置时间
    helpUidList              set<int64>                15 # 本次活动帮助过人的记录
    todayHelpCnt             int32                     16 # 今日助力次数

@WishTaskData # 心愿任务完成记录
    taskId                   int32               1    # 任务ID
	compCnt                  int32               2    # 完成次数

@WishRecordData # 心愿值获取记录
    createTime               int64               1 +key # 记录创建的时间
	type                     int32               2      # 记录类型 0.玩家助力 1.登陆任务 2.分享任务
	name                     string              3      # 记录产生对象昵称, 当type=0时才有
	addWishVal	             int32               4      # 增加的心愿值
	helperUid	             int64               5      # 助力玩家的uid, 当type=0时才有

@WishAwardRecordData # 心愿抽奖记录
    createTime               int64               1 +key # 记录创建的时间
	type                     int32               2      # 记录类型 0.心愿领奖 1.抽奖
	awardInfo                map<WishAwardData>  3      # 心愿值增加记录信息

@WishAwardData # 心愿奖品信息
    id                       int64    1    +key    # 记录ID
    itemId                   int32    2            # 道具ID
	itemCnt                  int64    3            # 道具数量

@TestActTeamData # 组队demo测试活动数据
    teamId                       int64                     1  # 队伍ID
    score                        int32                     2  # 积分

@WishingTreeActivityData # 许愿树活动
    treeId                        int32                     1  # 许愿树id
    wishRewardState               int32                     2  # 许愿奖励状态 0.未领取 1.已领取
    wishingRecord                 map<WishingRecord>        3  # 许愿记录
    reservationRewardState        int32                     4  # 预约奖励状态 0.未领取 1.已预约

@WishingRecord
      wishDay                 int64           1  +key   # 许愿时间-时间戳，精确到day
      amsPackageGroupId       int32           2         # ams礼包组id
      createTime              int64           3         # 许愿创建时间

@MonopolyActivityData # 大富翁
    curIndex                  int32                           1           # 当前索引 从1开始
    hasReceiveReward          bool                            2           # 是否已操作奖励,只对当前格子生效，所有奖励类格子都基于此字段
    cumulativeInfo            map<MonopolyCumulativeData>     3           # 积累奖励信息
    finishedRoundNum          int32                           4           # 已完成圈数
    finishedStepNum           int32                           5           # 已前进步数
    receivedRoundReward       set<int32>                      6           # 已领取的圈奖励
    receivedStepReward        set<int32>                      7           # 已领取的步数奖励

    guaranteeDrawCount        int32                           10   +nocs   # 必中后抽取次数
    drawInfo                  map<MonopolyDrawData>           11    +nocs   # 抽奖信息

@MonopolyCumulativeData # 大富翁累积信息
    gridId                 int32        1   +key    # 格子ID
    cumulativeCount        int32        2           # 累积次数

@MonopolyDrawData  # 大富翁抽奖信息
    poolId                  int32       1   +key    # 奖池ID
    missCount               int32       2           # 已抽取次数

@LuckyMoneyActivity
  shareTimeMs              int64                        1                #首次分享时间
  shareList                map<LuckyMoneyInfo>          2                #已分享红包列表
  recvList                 map<LuckyMoneyInfo>          3                #已领取红包列表
  randomCount              int32                        4   +nocs        #抽奖保底次数(废弃)
  randomRecord             map<GiftPackageRandomRecord> 5   +nocs        #抽奖记录(废弃)
  friendRandomCount        int32                        6   +nocs        #好友红包保底次数(废弃)
  showEndTimeMs            int64                        7   +nocs        #红包活动结束时间

@LuckyMoneyInfo
  id                       int64                   1    +key
  luckyMoneyIndex          int32                   2                #红包编号
  sharePlayerUid           int64                   3                #分享者uid
  receivedCount            int32                   4                #领取次数
  rewards                  map<RewardItemInfo>     5                #红包奖励

@ScratchOffTicketsActivity
  lastScratchTimeMs              int64                          1                #上次刮刮乐时间
  ticketList                     map<ScratchOffTicketInfo>      2                #已刮列表
  isOpenHigher                   bool                           3                #是否已升级高级奖励
  randomRecord                   map<GiftPackageRandomRecord>   4   +nocs        #抽奖记录
  ticketCount                    int32                          5                #刮刮乐次数
  baseRewardRandomCount          int32                          6                #基础奖励保底次数
  higherRewardRandomCount        int32                          7                #高级奖励保底次数

@UseItemShareActivity
  shareCount                     int32                          1                #分享次数
  shareRecord                    map<UseItemShareRecord>        2   +nocs        #分享记录
  changeRecord                   map<ItemChangeRecord>          3   +nocs        #道具变动记录

@UseItemShareRecord
   shareId                  int64                               1   +key
   shareTimeMs              int64                               2                #分享时间
   shareUrl                 string                              3   +nodb        #分享链接

@ItemChangeRecord
   id                       int32                               1   +key
   itemId                   int32                               2                #道具id
   itemCount                int32                               3                #道具数量
   changeTimeMs             int64                               4                #变动时间
   changeReason             int32                               5                #变动途径(ItemChangeReason)
   changeSubReason          int64                               6                #变动子途径

@ScratchOffTicketInfo
  id                        int32                  1    +key
  baseRewards               map<RewardItemInfo>    2                #基础奖励
  higherRewards             map<RewardItemInfo>    3                #高级奖励
  isGetBaseRewards          bool                   4                #基础奖励是否已领取
  isGetHigherRewards        bool                   5                #高级奖励是否已领取

@RedPacketActivity
  sentList                 map<RedPacketSentDetail>     1                #已发送红包列表
  recvList                 map<RedPacketRecvDetail>     2                #已领取红包列表
  recvCount                int32                        3                #已领取红包数量(废弃)
  isSentListDirty          bool                         4                #已发送红包列表是否有变更(废弃)

@RedPacketSentDetail
  packetUuid               int64                   1    +key        #红包uuid
  packetId                 int32                   2                #红包配置id
  ts                       int64                   3                #发放时间
  senderUid                int64                   4                #发送红包玩家的uid
  recvCount                int32                   5                #已拾取的数量
  totalCount               int32                   6                #红包总数量
  details                  map<RedPacketSentRecvDetail>     7       #红包拾取详情
  isDirty                  bool                    8                #是否有变更

@RedPacketSentRecvDetail
  id                       int32                   1    +key
  receiverUid              int64                   2                #拾取红包玩家的uid
  ts                       int64                   3                #拾取时间
  rewards                  map<RewardItemInfo>     4                #红包奖励
  replyMsg                 string                  5                #答谢消息
  placeType                int32                   6                #位置类型
  placeId                  int64                   7                #位置id

@RedPacketRecvDetail
  packetUuid               int64                   1    +key        #红包uuid
  packetId                 string                  2                #红包配置id
  ts                       int64                   3                #拾取时间
  senderUid                int64                   4                #发送红包玩家的uid
  rewards                  map<RewardItemInfo>     5                #红包奖励
  replyMsg                 string                  6                #答谢消息
  placeType                int32                   7                #位置类型
  placeId                  int64                   8                #位置id

@CompetitionWarmUpStageAttr   # 赛事热身活动阶段配置
  stageId                 int32         1 +key    # 阶段ID
  score                   int32         2         # 积分
  gameTimes               int32         3         # 对局次数
  claimedGameTimesReward  set<int32>    4         # 已领取的对局次数奖励
  claimedScoreReward      set<int32>    5         # 已领取的积分奖励

@CompetitionWarmUpActivityAttr  # 赛事热身活动属性
  stageAttr   map<CompetitionWarmUpStageAttr>   1

@MusicOrderOrderInfo
  orderId           int32                                       1   +key    # 订单乐章ID
  state             com.tencent.wea.xlsRes.MusicOrderStateType  2           # 订单状态

@MusicOrderNoteInfo
  itemId            int32       1  +key     # 道具ID
  dropNum           int32       2           # 当日掉落个数
  isLimited         bool        3           # 是否触发当日掉落上限

@MusicOrderDailyInfo
  orders                map<MusicOrderOrderInfo>        1   +nocs   # 当天刷新的订单列表
  resetCount            int32                           2   +nocs   # 当天重置订单次数（废弃）
  notes                 map<MusicOrderNoteInfo>         3           # 当日音符掉落信息
  refreshTime           int64                           4   +nocs   # 最后刷新时间戳（ms）
  day                   int32                           5   +nocs   # 当前生效的配置ID

@MusicOrderModeNoDropInfo
  modeId            int32       1   +key    # 玩法模式ID
  noDropCount       int32       2           # 连续判定不掉落次数（注意不是对局数，一局可能判定多次；触发保底后清零）

@MusicOrderResetCountInfo
  day               int32       1   +key    # 用刷出的天数作为key
  remainCount       int32       2           # 剩余重置次数
  expireTime        int64       3           # 失效时间（ms）

@MusicOrderActivityAttr # 音乐订单活动属性
  dailyInfo             MusicOrderDailyInfo             1           # 每日刷新的数据
  orderCompleteNum      int32                           2           # 已完成的订单数量
  modeNoDropInfo        map<MusicOrderModeNoDropInfo>   3   +nocs   # 没有任何音符掉落的连续对局数
  claimedTaskRewards    set<int32>                      4           # 已领取订单任务奖励的累计完成订单数
  resetCountInfo        map<MusicOrderResetCountInfo>   5   +nocs   # 订单重置次数数据
  dayHistory            int32[]                         6   +nocs   # 记录刷新过的天数
  WolfFinishOrderNum    int32                           7           # 狼人心愿活动的订单完成数

@AnimalHandbookAnimalInfo # 图鉴信息
  uniqueId          int64           1   +key    # 图鉴唯一id
  animalId          int32           2           # 动物ID
  canGive           bool            3           # 是否可赠送（每个动物第一个获得的图鉴不可赠送）
  getTimeMs         int64           4           # 获得时间
  giverUid          int64           5           # 赠送人uid

@AnimalHandbookAnimalGetInfo # 动物获得信息
  animalId          int32           1   +key    # 动物ID
  noGetCount        int32           2           # 连续判定为此动物所属物种但非此动物的次数

@AnimalHandbookColonyInfo
  colonyId          int32                                           1   +key    # 聚集地ID
  state             com.tencent.wea.xlsRes.AnimalHandbookStateType  2           # 聚集地状态
  speciesId         int32                                           3           # 物种id (仅在state为AHST_Trapping或AHST_Captured时有效)
  completeTimeMs    int64                                           4           # 诱捕完成时间 (仅在state为AHST_Trapping或AHST_Captured时有效)
  animalId          int32                                           5           # 动物id (仅在state为AHST_Captured时有效)
  freeClaimCount    int32                                           6           # 聚集地已免费获取次数
  isFreeGet         bool                                            7           # 是否可以免费领取
  animalRepeatCount int32                                           8           # 动物已连续获得次数

@AnimalHandbookItemInfo
  itemId        int32       1   +key    # 道具id
  itemNum       int32       2           # 道具数量

@AnimalHandbookPlayerInfo
  uid               int64       1   +key    # uid
  openId            string      2           # openId
  name              string      3           # 昵称
  gender            int32       4           # 性别
  profile           string      5           # 头像
  headFrameType     int32       6           # 头像框
  headFrameUuid     int64       7           # 头像框
  headFrameId       int32       8           # 头像框

@AnimalHandbookGiveHistory
  uniqueId          int64                       1   +key    # 图鉴唯一id
  animalInfo        AnimalHandbookAnimalInfo    2           # 图鉴详细信息
  receiver          AnimalHandbookPlayerInfo    3           # 领取人信息

@AnimalHandbookReceiveHistory
  uniqueId          int64                       1   +key    # 图鉴唯一id
  animalInfo        AnimalHandbookAnimalInfo    2           # 图鉴详细信息
  giver             AnimalHandbookPlayerInfo    3           # 赠送人信息

@AnimalHandbookActivityAttr # 动物图鉴活动属性
  animalInfo            map<AnimalHandbookAnimalInfo>       1   +nocs   # 动物图鉴信息
  rewardedAnimals       set<int32>                          2   +nocs   # 记录已获得过的动物图鉴
  rewardedSpecies       set<int32>                          3   +nocs   # 已领取图鉴集满奖励的物种
  isFinalPrizeRewarded  bool                                4   +nocs   # 是否已领取终极奖励
  animalGetInfo         map<AnimalHandbookAnimalGetInfo>    5   +nocs   # 动物获得信息
  colonyInfo            map<AnimalHandbookColonyInfo>       6   +nocs   # 聚集地信息
  weekGiveTime          int32                               7           # 本周赠送次数
  lastGiveTimeMs        int64                               8   +nocs   # 上次赠送时间（ms）
  giveHistory           map<AnimalHandbookGiveHistory>      9   +nocs   # 赠送历史
  receiveHistory        map<AnimalHandbookReceiveHistory>   10  +nocs   # 领取历史
  dayGiveTime           int32                               11          # 每日赠送次数

@Activity # 活动
   activityUnits   map<ActivityUnit>   1              #所有活动

@ActivityFinished # 结束的活动
   id                   int64             1  +key
   finishTimeSec        int64             2  +nocs    #实际结束时间

@ActivityCenter # 活动中心
   activity                  Activity                    1           #活动集合

@FittingSlots
   currId               int32                               1           # 当前穿戴的槽位
   slots                map<FittingSlot>                    2
   defaultDresses       map<DressInfo>                      3           # 默认装扮
   createRoleDresses    bool                                4           # 是否完成创角选择装扮
   lastUpdateTime       int32                               5  +nocs    # 上一次更新的时间
   showIds              set<int32>                          6           # 展示的槽位
   randomOpen           bool                                7           # 外观搭配随机切换开启
   tmpRandomSlot        int32                               8  +nocs    # 对局前临时保存的槽位
   defaultDressChange   bool                                9           # 创角装扮修改标记
   fittingItemRecord    map<KvLL>                           10          # 记录装扮栏解锁需要的item-count

@FittingSlot
   id           int32                               1 +key #槽位号
   dresses      map<DressInfo>                      2
   unlock       bool                                3 +nocs #是否解锁
   random       bool                                4       #是否随机切换

@DressInfo
   dressType        com.tencent.wea.xlsRes.ItemType     1 +key  #装扮类型
   dressUuid        int64                               2
   dressItemId      int32                               3
   outLookRandom    set<int64>                          4       # 随机染色装扮(染色道具UUID)

@CombinationItems
    name                string          1       # 组合名字      (废弃)
    combinationItems    int64[]         2       # 组合道具id
    content             string          3       # 道具聊天文本
    id                  int64           4 +key  # id

@CombinationInteraction
    id              int64                   1 +key  # 互动组合的id
    name            string                  2       # 互动组合名
    items           map<CombinationItems>   3       # 互动组合配置
    createTimeMs    int64                   4       # 单个组合新建的时间
    iconId          int32                   5       # 设置的外显图标id 是items.begin()->combinationItems[iconId]
    iconName        string                  6       # 图标名

@CombinationSettings
    combinationInteractions         map<CombinationInteraction>     1   # 组合配置集合
    settingId                       int64                           2   # 历史设置分配的最大id

@Interaction   # 表情, 动作
    pos             int32                                   1 +key
    itemUuid        int64                                   2
    combination     CombinationItems                        3           # 组合互动表情 (废弃)
    type            com.tencent.wea.xlsRes.InteractionType  4           # 交互类型
    combinationId   int64                                   5           # 互动组合的id
    AccessoriesId   Accessories                             6           # 佩饰

@Accessories
   accessoriesId                     int64        1
   accessoriesUUId                   int64        2
   vehicleItemId                     int32        3
@Condition
   id                     int32        1   +key
   conditionStatus        int32        3
   finishCount            int32        4   +nocs   # 完成次数
   value                  int64        6           # condition中value字段
   valueInternal          int64        1001 +nocs  # 服务器内部辅助字段
   valueList              int64[]      1002 +nocs  # 服务器内部辅助字段

@ConditionGroup
   conditions          map<Condition>                               2
   status              com.tencent.wea.xlsRes.ConditionGroupStatus  5

@PlayerProfileInfo
    accessToken             string      20 +nodb
    accountType             com.tencent.wea.xlsRes.TconndApiAccount 24  # 账号类型
    vipExp                  int32       25 +nocs
    registerDeviceId        string      26 +nocs      # 注册设备id
    originPlatNickname      string      27 +nocs      # 原始平台昵称, 用于生成推荐昵称,不可直接使用
    wxMiniGameSessionKey    string      28 +nocs      # 微信小游戏密钥(不能给客户端)
    inviteRegisterUid       int64       29 +nocs      # 邀请注册的玩家UID
    prefabNickname          string      30 +nocs      # H5预注册昵称
    checkedNickname         string      31 +nodb      # 预检测通过的昵称
    checkedNicknameSec      int32       32 +nodb      # 预检测通过的昵称的检测时间
    nicknameCheckNote       map<NicknameCheckNote>  33 +nocs
    mobileGearLevel         int32       34 +nocs +nodb   #手机档位 low = 1;//低 middle = 2;//中 high = 3;//高 super = 4;//高档
    inviteRecallUid         int64       35 +nocs      # 邀请回流的玩家uid

@NicknameCheckNote
    checkReason             int32       1   +key      # 检测原因
    checkTimes              int32       2             # 每日检测昵称次数(0点重置)
    nextAvailableMs         int64       3             # 下次可检测昵称时间

@ShocksDegreeIDInfo
   degreeID             int32 1 +key  #小段位
   status                com.tencent.wea.xlsRes.RewardStatus          2       #领取状态 0 未触发 1可领取 2已领取

@ShocksRewardInfo
   qualifyingType          com.tencent.wea.xlsRes.QualifyingDegreeType  1 +key  #排行类型
   degreeRewardMap         map<ShocksDegreeIDInfo>                      2 #小段位奖励

@QualifyingBattleRecord
    battleNum               int32                                        1 # 比赛场次
    allLevelFirst           int32                                        2 # 连续三局第一名
    levelWinNum             int32[]                                      3 # 关卡获胜场次数


@QualifyingDetailInfo
   winNum                   int32                                        1 # 获胜场次
   totalLevelNum            int32                                        2 # 总轮次场次
   battleRecord             QualifyingBattleRecord                       3 # 单人排位赛
   teamBattleRecord         QualifyingBattleRecord                       4 # 团队排位赛

@QualifyingTypeInfo
   qualifyType             int32                        1  +key       # 段位类型
   qualifyInfo             QualifyingInfo               2             # 当前赛季段位信息
   historyQualifyInfo      map<QualifyingInfo>          3  +nocs      # 历史赛季段位信息
   seasonMail              map<SeasonMail>              4  +nocs      # 赛季邮件（根据客户端版本号发送）

@SeasonMail
    season                  int32       1   +key    # 赛季id
    degreeTypeInt           int32       2           # 大段位继承
    degreeId                int32       3           # 小段位继承
    oldSeason               int32       4           # 旧赛季id
    oldDegreeTypeInt        int32       5           # 旧赛季-大段位
    oldDegreeId             int32       6           # 旧赛季-小段位
    oldMaxDegreeTypeInt     int32       7           # 旧赛季最高段位-大段位
    oldMaxDegreeId          int32       8           # 旧赛季最高段位-小段位

@QualifyingInfo
    degreeType             com.tencent.wea.xlsRes.QualifyingDegreeType  1 # 段位 (M9废弃)
    degreeID               int32                                        2 # 小段位ID
    degreeStar             int32                                        3 # 小段位星级
    qualifyingIntegral     int32                                        4 # 段位积分总分
    lastWinTime            int64                                        5 # 上次胜利的时间戳
    maxDedegreeType        int32                                        6 # 当前赛季的最高段位
    maxDegreeID            int32                                        7 # 当前赛季的最高段位的小段位ID
    maxStar                int32                                        8 # 当前赛季的最高小段位的星级
    maxIntegral            int32                                        9 # 当前赛季最高分
    season                 int32                                        10 +key # 当前赛季
    firstDegreeProtect     int32                                        12 # 晋级后首局段位保护
    battleNum              int32                                        15 # 排位完成次数
    showReward             int32                                        16 # 是否达成展示奖励条件
    topRankNo              int32                                        17 # 当前赛季的最高排名，仅王者段位
    protectedScore         int32                                        18 # 乐园保护分
    degreeTypeInt          int32                                        19 # 段位int值 方便后续线上扩展
    latestRankNo           int32                                        20 # 当前赛季的最新排名，仅王者段位
    alreadySendMail        int32                                        21 # 已发送赛季重置邮件(废弃)
    continueWinTimes       int32                                        22 # 连胜次数
    maxContinueWinTimes    int32                                        23 # 最大连胜次数
    mailRewardId           set<int32>                                   24 +nocs # 已发送段位奖励
    dailyRankNo            int32                                        25 # 每日结算排名，仅王者段位
    dailyRankTag           int32                                        26 # 每日结算排名时间，仅王者段位
    winNum                 int32                                        27 # 获胜场次

@QualifyingExtraIntegralInfo
    id                 int32                                        1 +key # id
    finishNum          int32                                        2      # 完成次数
    updateTimeMs       int64                                        3      # 更新时间

@PersonalityStateInfo
   state               int32                               1       #状态id
   updateTimeMs        int64                               2       #设置时间
   expiredTimeMs       int64                               3       # 道具类型的过期时间

@IntimatePlayerInfo
   uid                   int64                        1   +key    # 玩家id
   intimateId            int32                        2           # 亲密关系类型
   level                 int32                        3           # 亲密度等级
   intimacy              int64                        4           # 亲密度
   hasNewIntimate        bool                         5           # 新增关系标签
   addTime               int64                        6           # 新增关系时间

@IntimateRelationInfo
   hide                  bool                         1           # 是否隐藏
   intimatePlayer        map<IntimatePlayerInfo>      2           # 亲密关系信息

# 平台特权信息
@PlatPrivilegesInfo
  platPrivileges  int32     1           # 平台特权情况, 详情见枚举PlatPrivilegesType所示
  expireTimeMs    int64     2           # 平台特权过期时间
  isSendGift      bool      3   +nocs   # 是否发送礼包
  updateTime      int64     4   +nocs   # 数据更新时间

# 动作信息
@HomePageActionInfo
  actionId        int32         1   +key    # 动作id
  expireTime      int64         2           # 过期时间

# 高级手持物和载具, 双人和多人, 自定义密友信息
@InteractionInfo
  index                     int32                     1 +key      # 位置(可以由客户端定义, 比如1~5代表副座1~5)
  uid                       int64                     2           # uid(0:空 1:系统NPC(如小红狐) 其他:正常玩家uid)
  isShowFriend              bool                      3           # 是否主页展示好友信息

# 自定义动作配置信息
@HomePageActionShowInfo
  selfActionList            map<HomePageActionInfo>   1           # 单人自定义序列
  interActionList           map<HomePageActionInfo>   2           # 双人自定义序列
  interactionUid            int64                     3           # 双人自定义好友id
  showOptions               int32                     4           # 0-不展示 1-单人展示 2-双人展示 3-单人载具 4-双人载具
  vehicle                   int32                     5           # 载具
  dressItemInfo             map<DressItemInfo>        6           # 装扮信息
  showOrangeGearButton      bool                     7           # 选了主题的情况下是否显示橙装动画 按钮勾选状态
  showAnimationButton       bool                     8           # 选了展示动作的情况下是否显示动画 按钮勾选状态
  interactionInfo           map<InteractionInfo>      9           # 高级手持物和载具
  vehicleAccessories        Accessories               10          # 载具对应的佩饰

# 玩家公开展示信息
@PlayerPublicProfileInfo
   nickname        string                                  1           # 昵称
   gender          int32                                   2           # 性别  1-男 2-女 0-未知
   profile         string                                  3           # 头像url (maybe)
   signature       string                                  4           # 个性签名
   exp             int64                                   6           # 经验
   level           int32                                   7           # 等级
   platAvatarInfo  string                                  8           # avatar信息
   openId          string                                  11          # 玩家openId
   platId          int32                                   12          # 平台ID
   vipLv           int32                                   13          # 充值等级
   vipExp          int32                                   14          # 充值经验
   labels          set<int32>                              15          # 标签
   personalityState  PersonalityStateInfo                  16          # 状态
   createRoleProfile    bool                               17          # 是否完成创角选择昵称性别
   shortUid        int64                                   18          # 玩家短uid
   channelId       int32                                   19          # 玩家授权渠道id, 1: 微信; 2: 手Q
   intimateRelationInfo IntimateRelationInfo               20          # 亲密关系信息
   creatorId       int64                                   21          # ugc用户专属Id，中介者，与客户端沟通采用gamesvr中的uid
   platProvileges  PlatPrivilegesInfo                      22          # 平台特权信息
   platOpenId      string                                  23  +nocs   # 平台openid
   ugcAuthType     com.tencent.wea.xlsRes.UgcAuthType      24          # UGC认证类型
   ugcAuthDesc     string                                  25          # UGC认证描述
   atXiaowo        bool                                    26          # 当前是否在小窝内（含自己的或他人的）
   location        PlayerRankGeoInfo                       27          # 玩家位置
   clubInfo        map<ClubBriefData>                      28          #加入的公会信息 待删除
   clubIds         set<int64>                              29          # 加入的公会ID
   returning       bool                                    30          # 回归用户标志
   returnExpiredSec int64                                  31          # 回归用户过期时间
   kungFuPandaRacingCostTimeMs      int32                  32          # 功夫熊猫竞速时间
   homepageAction  HomePageActionShowInfo                  33          # 主页动作展示信息
   ugcExp             int64                                34           # ugc星世界经验
   ugcLevel           int32                                35           # ugc星世界等级
   platNickName    string                                  36  +nocs    # 平台昵称
   atFarm          bool                                    37          # 当前是否在农场（含自己的或他人的）
   catchphrase          string                             38          # 个性签名-新版
   cupsNum         int32                                   39           # 奖杯数
   atHouse         bool                                    40          # 当前是否在农场小屋（含自己的或他人的）
   stickFriends    map<StickFriendInfo>                    41          # 好友置顶信息
   fashionLv       int32                                   42          # 外观图鉴大等级
   fashionSubLv    int32                                   43          # 外观图鉴小等级
   birthdayMonthDay int32                                  44          # 生日月*100+生日日
   farmReturning    KvLL                                   45          # 农场回归标记 (0/1) => expireTimeSec
   birthdayVisibleRange com.tencent.wea.xlsRes.BirthdayVisibleRange    46          # 生日可见范围(同步到DS)
   atRich          bool                                    47          # 当前是否在大富翁（含自己的或他人的）
   atCook         bool                                     48          # 当前是否在餐厅（含自己的或他人的）
   appearanceRoadShow map<AppearanceRoadShowInfo>          49          # 时尚之路展示套装信息
   curCupsCycle    int32                                   50          # 当前选择周目
   cupsTotal       int32                                   51          # 所有周目奖杯
   cupsNumCycleMap map<KvLL>                               52          # 奖杯周目数据
   fashionRevCount       int32                             53          # 可领取时装分享次数



@AppearanceRoadShowInfo
   type                  int32                      1  +key      # 类型
   suitId                int32                      2            # 套装ID
   id                    int32                      3            # 染色ID


@PlayerStatusDetail
   status         com.tencent.wea.xlsRes.PlayerStateType    1          # 玩家状态
   startTimestamp int64                                     2          # 状态开始时间

@PlayerStatusDetails
   onlineStatus         PlayerStatusDetail                  1          # 玩家在线状态，online/offline
   sceneStatus          PlayerStatusDetail                  2          # 玩家场景状态，局内，大厅，家园等，额外信息读PlayerPublicSceneData
   roomStatus           PlayerStatusDetail                  3          # 玩家组队状态
   specialStatus        PlayerStatusDetail                  4          # 特殊状态，编辑器/单机游玩等

# 生日数据
@AttrBirthdayData
  birthdayBasic             AttrBirthdayBasicData           1           # 生日基础数据
  birthdayFriend            AttrBirthdayToFriend            2           # 生日数据-好友
  birthdayCardItemData      map<AttrBirthdayCardItemData>   3           # 生日贺卡道具
  birthdayBlessingRecord    map<AttrBirthdayBlessingRecord> 4           # 生日祝福记录
  blessingRecordIsRedDot    bool                            5           # 生日祝福记录红点
  officialWelfare           AttrBirthdayOfficialWelfare     6           # 生日官方福利
  onDueDateCardReceived     map<AttrOnDueDateCardData>      7           # 收到的"到期送"的贺卡
  onDueDateCardSent         map<AttrOnDueDateCardData>      8           # 发出的"到期送"的贺卡
  remindData                AttrBirthdayRemindData          9           # 生日提醒数据

# "到期送"的贺卡
@AttrOnDueDateCardData
  blessingRecordId          string                          1 +key      # recordId
  senderUid                 int64                           2           # 赠送者uid
  sendeeUid                 int64                           3           # 被赠者uid
  sendEpochMillis           int64                           4           # 赠送时间戳毫秒
  birthdayMonthDay          int32                           5           # 被赠送者生日(生日月*100+生日日)
  birthdayCardData          AttrBirthdayCardData            6           # 贺卡内容
  mailId                    int64                           7           # 关联的邮件id

# 生日基础数据
@AttrBirthdayBasicData
  lastBirthdayMonthDay            int32                     1           # 上一次设置的 生日月*100+生日日
  twoTimesAgoBirthdayMonthDay     int32                     2           # 上上次
  lastSettingEpochSecs            int64                     3           # 上一次设置生日时间戳
  twoTimesAgoSettingEpochSecs     int64                     4           # 上上次
  unhandledBirthdayCardReceiveEpochSecs     int64           5           # 收到的"到期送"的贺卡 上次检测时间
  unhandledBirthdayCardSendEpochSecs        int64           6           # 发出的"到期送"的贺卡 上次检测时间

# 生日数据-好友
@AttrBirthdayToFriend
  birthdayFriendData        map<AttrBirthdayToFriendData>   1           # 好友生日数据

# 生日官方福利
@AttrBirthdayOfficialWelfare
  officialWelfareAwardEpochSecs               int64         1           # 生日官方福利上一次领取时间戳秒
  officialWelfareReissueTriggerEpochSecs      int64         2           # 生日官方福利补发逻辑: 触发时间戳秒 (触发时间+14天内可以领取)
  officialWelfareReissueAwardEpochSecs        int64         3           # 生日官方福利补发逻辑: 补发奖励领取时间戳秒

# 生日数据-好友
@AttrBirthdayToFriendData
  friendUid                 int64                           1 +key      # 好友uid
  birthdayMonthDay          int32                           2           # 生日月*100+生日日
  lastRemindEpochSecs       int64                           3           # 上一次提醒好友生日的时间戳秒
  twoTimesAgoRemindEpochSecs int64                          4           # 上上次
  lastSendEpochMillis       int64                           5           # 上一次赠送好友时间戳毫秒
  sentEpochMillis           int64[]                         7           # 已经赠送的时间戳毫秒
  #sendTimes                 int32                           6           # 已经赠送好友次数

# 生日提醒数据
@AttrBirthdayRemindData
  friendUidList             int64[]                         1           # 好友uid列表

# 生日祝福记录
@AttrBirthdayBlessingRecord
  blessingRecordId          string                          1 +key      # recordId
  senderUid                 int64                           2           # 赠送者 uid
  blessingEpochMillis       int64                           3           # 祝福时间戳毫秒
  blessingType              int32                           4           # 祝福类型: 祝福/贺卡/礼物  # 参考 BirthdayBlessingType
  birthdayMonthDay          int32                           5           # 生日月*100+生日日
  blessingConfId            int32                           6           # 祝福配置表id       # 祝福类型为"祝福"时
  birthdayGift              MallGiftCard                    7           # 外观类商品赠礼卡信息 # 祝福类型为"礼物"时
  birthdayCardData          AttrBirthdayCardData            8           # 贺卡内容           # 祝福类型为"贺卡"时
  giveCommidityList         map<CommodityInfo>              9           # 商品              # 祝福类型为"礼物"时
  mailId                    int64                           10          # 关联的邮件id
  isDeleted                 bool                            11          # 是否从背包删除

# 生日贺卡道具
@AttrBirthdayCardItemData
  birthdayCardId            string                          1 +key      # recordId
  itemUUID                  int64                           2           # itemUUID
  itemId                    int32                           3           # 道具配置id
  mailId                    int64                           4           # 关联的邮件id
  birthdayCardData          AttrBirthdayCardData            5           # 贺卡内容

# 生日贺卡数据
@AttrBirthdayCardData
  birthdayCardConfigId      int32                           1           # 贺卡配置表id(本期就一个贺卡主题,此字段为0)
  blessingConfigId          int32                           2           # 祝福语配置表id
  customContent             string                          3           # 自定义祝福语
  cardEpochMillis           int64                           4           # 贺卡实际时间戳毫秒("到期送"的贺卡, 会跟实际时间不一样)

@RoomExtraInfo
   roomType                 int32                           1           # 队伍/房间/ugc房间 取RoomType的枚举值
   playId                   int32                           2           # 玩法id
   curMemberNum             int32                           3           # 当前人数
   maxMemberNum             int32                           4           # 满员人数

# 玩家公开实时状态
@PlayerPublicLiveStatus
   roomId             int64                                   43 +nocs    # roomId
   roomIsFull         bool                                    44 +nocs    # 队伍是否已满
   status             com.tencent.wea.xlsRes.PlayerStateType  24          # 玩家状态信息
   roomStatus         com.tencent.wea.xlsRes.RoomStateType    25          # 房间状态信息
   lastKeepAliveTs    int64                                   26 +nocs    # 保活时间
   battleMatchType    int32                                   27 +nocs    # 对局玩法ID
   battleJoinTs       int64                                   28 +nocs    # 对局进入时间（秒）
   miniGameConsole    bool                                    29 +nodb    # 小游戏状态
   statusDetails      PlayerStatusDetails                     30          # 玩家状态细节信息
   roomExtraInfo      RoomExtraInfo                           31          # room额外信息
   battleUgcId        int64                                   32 +nocs    # 对局UGCID

@BindAccountInfo
   accountType   com.tencent.wea.xlsRes.TconndApiAccount  1  +key # 账号类型
   timestamp     int32                                    2  # 与该账号绑定的Unix时间戳

@PlayerPublicBasicInfo
   channelId        int32                                 1              # 渠道号
   svrId            int32                                 2  +nocs       # 服务器编号
   osVersion        string                                3  +nocs       # 客户端操作系统版本
   netWork          string                                4  +nocs       # 客户端网络环境
   loginTimeMs      int64                                 5              # 登陆时间
   logoutTimeMs     int64                                 6              # 登出时间
   registerTimeMs   int64                                 7              # 注册时间
   systemSoftware   string                                8  +nocs       # 系统
   systemHardware   string                                9  +nocs       # 移动终端机型
   accountType      com.tencent.wea.xlsRes.TconndApiAccount 10  +nocs    # 账号类型
   registerRegionId int32                                 11 +nocs       # 注册时玩家地区id
   bindAccountInfos map<BindAccountInfo>                  12 +nocs       # 已绑定的账号类型列表
   regChannelDis    string                                13             # 注册分发渠道
   trackId          string                                14 +nocs       # B站导量标识
   accountState     int32                                 15 +nocs       # 账号状态, 见枚举AccountState所示
   loginPlat        com.tencent.wea.xlsRes.PlayerLoginPlat  16 +nocs       # 登录平台
   clientDeviceType int32                                 17 +nocs        # 客户端设备类型
   deeplinkInfos    map<DeeplinkInfo>                     18 +nocs        # 历史deeplink信息

 @DeeplinkInfo
    deeplinkKey    string   1  +key   # deeplink唯一标识
    loginTimeMs    int64    2         # deeplink登入时间

@PlayerGamePlay
    featureType     int32                                   1   +key    # 玩法类型
    pakVersion      int64                                   2           # 二进制版本
    confVersion     int64                                   3           # 配置版本

@ABTestSetting
    abtestId                            int64                                       1   +key    # 实验id
    settingVal                          int32                                       2           # 设置值，0-false 1-true，考虑拓展性及通用性

# 玩家公开游戏设置
@PlayerPublicGameSettings
   privilegeSwitch                    int32                                       1           # 特权开关  0-隐藏按钮，1-不响应点击，2-响应点击  未启用 del
   privilegeLevel                     int32                                       2           # 特权等级  红钻等级: 0-代表未开通，1-开通低档，2-开通中档，3-开通高档  未启用 del
   hideFriendReq                      bool                                        3           # 拒绝好友申请
   rejectStrangerChatMsg              bool                                        4           # 拒绝接收陌生人的私聊消息
   benefitCardEnable                  bool                                        5           # 权益卡是否有效 del
   clientVersion64                    int64                                       6  +nocs    # 64位客户端版本号, 用以取代以前的32位版本号
   DSVersion64                        int64                                       7  +nocs    # 64位ds版本号
   hideQualifyingInfo                 bool                                        8  +nocs    # 隐藏段位信息
   hidePersonalProfile                bool                                        9           # 个人信息隐藏-个人信息
   hideBattleHistory                  bool                                        10          # 个人信息隐藏-历史战机
   hideSuitBook                       bool                                        11          # 个人信息隐藏-套装图鉴
   hideUgcInfo                        bool                                        12          # 个人信息隐藏-UGC
   hideRoomInvitation                 bool                                        13          # 屏蔽组队邀请
   hideLobbyInvitation                bool                                        14          # 屏蔽大厅邀请
   clientLanguage                     int32                                       15          # 客户端语言
   hideIntimateRelation               bool                                        16          # 隐藏亲密关系
   qqChatSyncStatus                   uint32                                      17          # QQ消息同步状态  1-关闭, 2-开启
   hideIntimateRelationTab            bool                                        18          # 个人信息隐藏-亲密关系
   hideLocation                       bool                                        19          # 个人信息隐藏-定位
   hideQQFriendReq                    bool                                        20          # 拒绝 QQ 好友申请
   allowRecommendInfoCollect          bool                                        21          # 是否允许微信好友获取你的游戏动态信息的设定
   hideClubInfo                       bool                                        22          # 隐藏星团信息
   hideTitle                          bool                                        23          # 个人信息隐藏-隐藏称号
   hidePersonalityState               bool                                        24          # 个人信息隐藏-隐藏心情
   headPublicInfoType                 int32                                       25          # 头顶个人展示的是称号还是心情 EHeadPublicInfoType 枚举值
   customRoomAutoStartWhenFull        bool                                        26          # 自定义房间（ogc ugc）满员时自动开局，仅作为房主时创建的对局有效
   hidePlayerStatus                   bool                                        27          # 个人信息隐藏-隐身
   hidePlayerStatusWeekCnt            int32                                       28          # 个人信息隐藏-隐身本周使用次数
   hideWorldLobbyChat                 bool                                        29          # 隐藏世界、广场聊天
   hideWorldLobbyChatInit             bool                                        30  +nocs   # 隐藏世界、广场聊天，初始化设置
   needActionConfirm                  bool                                        31          # 双人动作确认
   hidePlayerStatusTime               int64                                       32          # 个人信息隐藏-隐身时间
   hidePlayerStatusSwitch             bool                                        33  +nodb   # 个人信息隐藏-隐身开关
   hideReservation                    bool                                        34          # 屏蔽预约邀请
   showSeasonFashion                  bool                                        35  +nocs   # 展示赛季时尚手册-默认隐藏 #废弃
   seasonFashionShowStatus            com.tencent.wea.xlsRes.FeatureShowStatus    36          # 赛季时尚手册显示状态
   intimateOnlineNotice               com.tencent.wea.xlsRes.FeatureOpenStatus    37          # 亲密好友上线提醒
   hideRoomExtraInfo                  bool                                        38          # 隐藏组队详情
   gameProtectionMainSwitch           bool                                        39          # 游戏防护总开关
   gameProtectionSpecificSwitch       GameProtectionSpecificSwitch                40          # 游戏防护详细开关
   notAllowRecommendFriends           bool                                        41          # 不允许推荐好友
   notSearchedByUid                   bool                                        42          # 不被UID搜索到
   demandSwitch                       int32                                       43          # 允许索要选项(0-默认允许;1-不允许;2-仅允许亲密)
   hideProfileToFriend                bool                                        44          # 对好友隐藏个人信息
   hideProfileToStranger              bool                                        45          # 对陌生人隐藏个人信息
   arenaTargetSelectionStrategy       int32                                       46          # moba游戏中优先攻击的目标，0 血量百分比最低 1 血量绝对值最低 2 距离最近单位
   arenaIsAutoNormalAttack            bool                                        47          # moba游戏中是否进行自动攻击
   arenaVoiceType                     int32                                       48          # moba游戏中的语音播报类型
   lobbyModeType                      int32                                       49          # 大厅模式类型，0-星宝广场，1-组队秀界面
   gamePlay                           map<PlayerGamePlay>                         51  +nocs   # 多玩法信息
   showCups                           bool                                        52          # 展示奖杯
   strangerFollow                     bool                                        53          # 陌生人跟随
   lobbyModeTypeForAbTest             map<ABTestSetting>                          54          # 大厅模式设置，仅ab实验中使用
   showCustomActionSelectFriendInfo   bool                                        55          # 是否开启双人动作展示好友信息的开关
   arenaCameraControlType             int32                                       56          # moba游戏中的相机模式
   hideArenaBattleHistory             bool                                        57          # arena历史战绩隐藏
   hideEntertainmentQualifyInfo       bool                                        58          # 隐藏娱乐玩法段位
   photoLibraryDescription            int32                                       59          # 相册访问权限(0-默认允许;1-不允许;2-仅允许亲密)
   birthdayVisibleRange               com.tencent.wea.xlsRes.BirthdayVisibleRange 60          # 生日可见范围
   profileShowCollection              set<int32>                                  61          # 个人信息界面展示项 ProfileShowType
   raffleUseTagDailyLimit             bool                                        62          # 使用tagDailyLimit字段代替祈愿每日上限dailyLimit
   raffleDailyLimitInBlackList        bool                                        63 +nocs    # 是否在tagDailyLimit的黑名单，客户端勿读
   acquireEquipPopUpEquipSlot         bool                                        64          # 获得装扮时立即装扮是否拉起装扮栏
   raffleDailyLimitInIdipWhiteList    bool                                        65 +nocs    # 是否在tagDailyLimit的IDIP白名单，客户端勿读
   currentActionState                 int32                                       66          # 当前动作状态(0-默认;1-正在拍照中)
   offAinpcChatPush                   bool                                        67          # 关闭好好鸭智能推送
   hideFashionScore                   bool                                        68          # 隐藏时尚分
   hideSubHistoryMaxQualifyInfo       bool                                        69          # 隐藏副玩法历史最高段位
   profileTopInfo                     map<KvII>                                   70          # 个人信息界面置顶信息
   playerGameGearSetting              GameGearSettings                            71          # 透传到battle中的备战设置
   chaseGameSetting                   ChaseGameSettings                           72          # 大王battle设置
   receiveGiftSwitch                  int32                                       73          # 允许赠送选项(0-默认允许;1-不允许;2-仅允许亲密)
   hideArenaHeroSkin                  bool                                        74          # arena英雄皮肤隐藏

@ChaseGameSettings
   newBieGuideOpen                  int32                                          1           # 是否开启新手指引, 0开启，1关闭

@GameProtectionSpecificSwitch
    forbiddenPublicChat               bool                                        1             # 禁用公共聊天
    forbiddenStrangerChat             bool                                        2             # 禁止陌生人聊天
    forbiddenStrangerRoomInvitation   bool                                        3             # 禁止陌生人组队邀请
    forbiddenStrangerVisitXiaowo      bool                                        4             # 禁止陌生人访问小窝

@GameGearSettings
    hideFashionScores                 map<HideFashionScoreSetting>                1             # 时尚分隐藏

@HideFashionScoreSetting
    gameMode                          int32                                        1  +key      # 玩法模式
    hide                              bool                                         2            # 是否隐藏

@PlayerPublicFriendData # 玩家好友相关公开数据
  intimateRelationUnlockExtraCnt      int32     1 +nocs    # 亲密好友额外解锁数量，此处仅用于其他玩家读取

@BattleSettlementMVPInfo # 对局结算获取mvp数据信息
  lastMVPTS      int64     1 +nocs    # 上一次获取mvp时间戳
  matchTypeID    int32     2 +nocs    # matchTypeID

@CommunityChannelIconInfo # 社区频道icon信息
  arenaMVPInfo             BattleSettlementMVPInfo             1 +nocs   # 峡谷对局结算获取mvp数据信息
  wolfKillMVPInfo          BattleSettlementMVPInfo             2 +nocs   # 狼人杀对局结算获取mvp数据信息
  identityIconID int32     3            # 身份 id
  HeroIconID     int32     4            # 英雄 id

# 玩家公开游戏数据
@PlayerPublicGameData
   #   gameTimes                int32                               1           # 游戏次数
   rankInfo                 map<RankInfoItem>                   2  +nocs    # 玩家排行数据
   qualifyingInfo           QualifyingInfo                      3           # 段位信息（主玩法）
   detailInfo               QualifyingDetailInfo                4           # 赛季详细记录
   extraIntegralInfo        map<QualifyingExtraIntegralInfo>    5           # 额外得分信息
   #   numberOfLoginDay         int32                               6           # 累计登陆天数
   totalOnlineTime          int64                               7  +nocs    # 总的在线时间ms
   #   following                int32                               8           # 关注数
   #   likedTimes               int32                               9           # 被点赞次数
   rankGeoInfo              PlayerRankGeoInfo                   10          # 地区排行榜归属地
   monthCardInfo            map<MonthCardInfo>                  11          # 月卡
   permitInfo               AttrPermitInfo                      12          # 当前通行证信息
   playerGameTimes          map<PlayerGameTime>                 13          # 游戏次数数据
   statCluster              StatCluster                         14 +nocs    # 统计信息汇总
   qualifyTypeInfo          map<QualifyingTypeInfo>             15          # 各个玩法的赛季段位信息
   showQualifyType          int32                               16          # 当前好友信息展示段位类型
   abTestInfo               map<ABTestInfo>                     17 +nocs    # ABTest信息 给publicplayer用
   playerGameTimesSeasonID  int32                               18 +nocs    # 游戏次数数据归属的赛季ID
   bpInfo                   map<BPPublicInfo>                   19 +nocs    # BP通行证信息
   publicFriendData         PlayerPublicFriendData              20          # 好友公开数据
   communityChannelIconInfo CommunityChannelIconInfo            21          # 社区频道icon信息
   commonlyUsedHeroId       int32                               22 +nocs    # 常用英雄id
   tradingCardCollectionInfos     TradingCardCollectionInfos    23          # 玩家卡牌收集信息
   farmSquadActivityLuckyFlag   bool                            24          # 农场小队活动的幸运标记
   intimateMotion           map<IntimateRelationMotionAttr>     25 +nocs    # 亲密关系互动动作
   tradingCardCollectionCardInfos TradingCardCollectionCardInfos 26 +nocs   # 玩家具体卡牌收集数据
   danceHighestScore            int32                           27          # 手持物乐队最高分
   showSubHistoryMaxQualifyType int32                           28          # 当前好友信息展示副玩法历史最高段位类型
   seasonPlayedMatchTypeIds     set<int32>                      29 +nocs    # 本赛季玩过的玩法ID


# 玩家公开装备装扮
@PlayerPublicEquipments
   dressCount                   int32                                   1           # 皮肤
   dressUpInfos                 set<int32>                              2           # 装扮信息
   equipDressInfo               map<EquipDressInfo>                     3           # 穿戴信息(时装、特效、表情等)-废弃
   equipItemInfo                map<EquipItemInfo>                      4           # 装备道具信息-废弃
   fashionValue                 int32                                   5           # 当前赛季时尚度
   dressItemInfo                map<DressItemInfo>                      6           # 穿戴信息(铭牌、称号、头像框等)
   fashionValues                map<SeasonFashion>                      7           # 全部赛季的时尚度
   activeSuitBook               set<int32>                              8           # 套装图鉴
   backupDressUpInfos           set<int32>                              9    +nocs  # 备用装扮信息 slotId = 5
   initSeasonFashionSuitTimeMs  int64                                   10   +nocs  # 初始化赛季时尚手册时间
   profileTheme                 int32                                   11          # 背景图
   dressUpDetailInfos           map<DressUpDetailInfo>                  12          # 装扮信息dressUpInfos的详细信息
   fashionScores                int32[]                                 13   +nocs  # 对局时尚分展示
   teamShowTheme                int32                                   14          # 组队秀背景图
   dressUpValue                 int32                                   15          # 装扮值
   qualifyDailyRankInfos        map<QualifyingDailyRankInfo>            16          # 每日段位排名
   readyBattleBagInfo           map<DressItemInfo>                      17          # 备战装饰背包信息
   animeDressOutline            int32                                   18          # 二次元装扮描边 0/1
   displayBoardInfo             AttrDisplayBoardInfo                    19          # 推图展板
   ornamentCustomDress          OrnamentCustomDress                     20          # 配饰自定义

@DressUpDetailInfo
   itemId                      int32                           1            +key  # dressUpInfos中的道具ID
   status                      int32                           2                  # 道具状态
   showStatus                  int32                           3                  # 道具个人信息展示状态
   itaBagBadgeUrl              string                          4                  # 痛包吧唧装扮Url
   cosVersion                  string                          5                  # cos版本号
   extendAttr                  map<KvIL>                       6                  # 道具扩展属性字段 ItemExtendAttrType -> value

@SeasonFashionEquipBook # 赛季时尚手册数据
  itemType        com.tencent.wea.xlsRes.ItemType   1 +key    # 装扮类型
  itemId          set<int32>                        2         # 获得的装扮道具ID列表

@SeasonFashionBattleDataDetail  # 赛季时尚手册战斗数据
  dataId        int32         1 +key    # 数据ID
  dataValue     int64         2         # 数据值，如果是百分比则放大100倍，不保留小数

@SeasonFashionQualifyMaxDegreeInfo # 赛季时尚手册-各种模式排位最高段位记录cs
  qualifyType            int32                                        1  +key   # 段位类型
  degreeType             com.tencent.wea.xlsRes.QualifyingDegreeType  2         # 段位 (M9废弃)
  degreeID               int32                                        3         # 小段位ID
  degreeStar             int32                                        4         # 小段位星级
  qualifyingIntegral     int32                                        5         # 段位积分总分

@SeasonFashionBattleData   # 赛季时尚手册数据
  battleMetaDataDetail      map<SeasonFashionBattleDataDetail>      1 +nocs # 单个赛季的元数据集
  battleDataDetail          map<SeasonFashionBattleDataDetail>      2 +nocs # 单个赛季的时尚数据集
  qualifyingMaxDegreeInfo   map<SeasonFashionQualifyMaxDegreeInfo>  3 +nocs # 各个模式排位的最高段位信息
  playerGameTimes           map<PlayerGameTime>                     4 +nocs # 游戏次数数据
  modeCupsData              map<ModeCups>                           5 +nocs # 奖杯数据

@SeasonFashion
   seasonId        int32                                   1  +key
   fashionValue    int32                                   2
   equipBook       map<SeasonFashionEquipBook>             3        # 时尚手册
   battleData      SeasonFashionBattleData                 4        # 战斗数据

@UserSeasonNoteBookAttr # 单个赛季手册数据
  seasonId                  int32                                   1 +key   # 赛季ID
  battleMetaDataDetail      map<SeasonFashionBattleDataDetail>      2 +nocs # 单个赛季的元数据集

@UserAllSeasonNoteBookAttr # 玩家所有赛季手册手册，用于存储不用存放在PlayerPublic中的数据，避免过大
  seasonBattleData                map<UserSeasonNoteBookAttr>       1        # 各个赛季数据
  battleDataRelocateTimeMs        int64                             2 +nocs  # 赛季战斗数据转存时间，0表示没有
  removeOldBattleMetaDataTimeMs   int64                             3 +nocs  # 移除存储在 FashionValue 中的战斗元数据的时间
  clearZeroBattleDataSeasonId     set<int32>                        4 +nocs  # 清理了0值战斗数据的赛季ID集合
  clearZeroBattleMetaDataSeasonId set<int32>                        5 +nocs  # 清理了0值战斗元数据的赛季ID集合

@SeasonReview
   seasonReviewEventProgressData   map<SeasonReviewEventProgressData>     1          # 赛季回顾进度数据
   beginRecordSeasonId             int32                                  2          # 开始记录赛季回顾数据的赛季ID
   seasonId                        int32                                  3          # 赛季ID
   seasonReviewRedPointInfo        map<SeasonReviewRedPointInfo>          4          # 赛季回顾红点信息

@SeasonReviewRedPointInfo
    seasonId                        int32                                 1  +key    # 赛季ID
    showRedPoint                    int32                                 2          # 0:不显示红点 1:显示

@SeasonReviewEventProgressData
    eventType int32                                       1  +key   # 事件类型
    eventValue int64                                      2         # 事件值
    eventUpdateTime int64                                 3         # 事件最后一次更新时间


# 玩家公开历史数据
@PlayerPublicHistoryData
    historyQualifyingInfo         map<QualifyingInfo>      1           # 历史段位信息
    shocksRewardMap               map<ShocksRewardInfo>    2           # 获取过的冲段奖励
    historyRankInfo               map<RankHistory>         3           # 排行榜记录
    hourChargeInfo                map<HourChargeInfo>      4     +nocs # 历史每小时充值金额

# 玩家公开场景数据
@PlayerPublicSceneData
    lobbyInfo       PublicLobbyInfo                        1           # 大厅信息
    xiaoWoInfo      PublicXiaoWoInfo                       2           # 小窝信息
    farmInfo        PublicFarmInfo                         3           # 农场信息
    houseInfo       PublicHouseInfo                        4           # 农场小屋信息
    farmType        int32                                  5           # 当前所在农场类型 common.FarmType
    richInfo        PublicRichInfo                         6           # 大富翁信息
    cookInfo        PublicCookInfo                         7           # 农场餐厅信息

@RankInfoItem
   type                      int32                         1 +key  #排行类型
   scoreFields               int32[]                       2       #排行值
   updateTimeMs              int64                         3       #最后更新时间
   updateSeason              int32                         4       #最后更新所处赛季
   hide                      bool                          5       #是否隐藏
   subItems                  map<RankInfoSubItem>          6       #子分数

@BattleResultData
   id                   int64                         1   +key   # 逻辑ID 自增
   result               int32                         2          # 战斗结果 胜负平
   endTime              int64                         3          # 战斗结束时间
   battleRole           int32                         4          # 特色玩法角色
   levelRounds          map<BattleLevelRoundData>     5          # 关卡ID
   specialBattleData    SpecialBattleData             6          # 特殊战斗数据

@SpecialBattleData
   isOneRound          bool                 1         #天天晋级赛触发一轮制

@BattleLevelRoundData
   round            int32                   1   +key   # 轮次
   levelId          int32                   2          # 关卡

@BattleModeData
   id               int32                                   1   +key   # 模式ID
   recentBattleResultData     map<BattleResultData>         2          # 最近30场战斗结果

@PlayerPublicSummaryInfo
   battleModeData     map<BattleModeData>         1    +nocs  # 最近30场战斗结果

@PlayerStatisticInfo
   type    int32   1   +key   # 统计类型
   value   int64   2          # value

@GameplayEventSerialId
   serialId     string  1   +key    # 事件序列号
   reportTimeMs int64   2           # 上报时间

@PlayerEventData
   statistics   map<PlayerStatisticInfo>    1
   reportGameplayEventSerialId map<GameplayEventSerialId>  2    # 副玩法上报事件去重

@ActivityAddNewAidInfo
   configVersion   int64   1
   toAddNewIndex   int32   2

@HOKMobaTagInfo
   ext1            int32   1   # 手游moba竞技能力，1-未识别，2-低，3-中，4-高
   ext2            int32   2   # 手游moba经验，1-未识别，2-低，3-中，4-高
   updateTime      int64   3   # 标签更新时间
   reserve1        int32   4   # 预留1
   reserve2        string  5   # 预留2
   reserve3        int64   6   # 预留3
   reserve4        string  7   # 预留4

@HOKABTestInfo
   testFlag        int32   1   # 是否参与新手指引局后的温暖局实验 1 参与， 0 不参与
   userType        int32   2   # 用户类型 testFlag是1时生效 0 未完成新手指引 1 普通， 2 小白， 3 高手
   updateTime      int64   3   # testFlag标记更新时间
   reserve1        int32   4   # 预留1
   reserve2        string  5   # 预留2
   reserve3        int64   6   # 预留3
   reserve4        string  7   # 预留4

@HOKXiaoBaiWarmRoundABTestInfo
   score           int32   1   # 上一局的mvp分数
   roundCount      int32   2   # 累计打了多少局
   freeCount       int32   3   # 自由模式累计打了多少局
   rankCount       int32   4   # 排位累计打了多少局
   roundType       int32   5   # 用户类型 testFlag是1时生效 0 未完成新手指引 1 普通， 2 小白， 3 高手
   updateTime      int64   6   # 更新时间
   reserve1        int32   7   # 预留1
   reserve2        string  8   # 预留2
   reserve3        int64   9   # 预留3
   reserve4        string  10  # 预留4

@HOKAIInviteShowInviteData
   showCount           int32   1     +nocs # 已经弹出的数量，每日重置
   zeroDayTime     int64   2     +nocs # 更新时间
   showFlag        int32   3   # 客户端是否需要在进入玩法后发起AI邀请标记，1是，0否
   showFlagTime    int64   4   # 客户端需要发起AI标记的时间
   reserve1        int32   5   # 预留1
   reserve2        string  6   # 预留2
   reserve3        int64   7   # 预留3
   reserve4        string  8   # 预留4

@HOKAIInviteDailyData
   inviteCount           int32   1   # 当日已触发场次
   zeroDayTime     int64   2   # 更新时间
   reserve1        int32   3   # 预留1
   reserve2        string  4   # 预留2
   reserve3        int64   5   # 预留3
   reserve4        string  6   # 预留4

@HOKAIInviteInfo
   showInfo        HOKAIInviteShowInviteData   1   # 客户发起邀请标记信息
   inviteInfo      HOKAIInviteDailyData        2   +nocs # 每日邀请信息
   reserve1        int32   3     +nocs # 预留1
   reserve2        string  4     +nocs # 预留2
   reserve3        int64   5     +nocs # 预留3
   reserve4        string  6     +nocs # 预留4

@HOKLosePerfScoreInfo
   updateTimeMs     int64       1       #   更新时间
   finishNum        int32       2       #   完成次数

@HOKAttrInfo
   mobaTagInfo              HOKMobaTagInfo                 1 +nocs    # moba品类标签
   abTestInfo               HOKABTestInfo                  2 +nocs    # 新手温暖据局之后的abTest信息
   xiaoBaiAbTestInfo        HOKXiaoBaiWarmRoundABTestInfo  3 +nocs    # 小白用户新手温暖据局信息
   aiInviteInfo             HOKAIInviteInfo                4          # ai邀请的信息
   losePerfScoreInfo        HOKLosePerfScoreInfo           5 +nocs    # 每日结算失败不扣分信息

@RewardCostItem
   itemId           int32                     1 +key
   num              int32                     2

@WolfKillRewardItem
   rewardTime       int64                     1 +key
   itemId           int32                     2
   num              int32                     3

@WolfKillNewUserTipsInfo
   eventId          int32                     1 +key
   num              int32                     2

@RewardComponent
   rewardId         int32                                          1 +key # 奖励id
   status           com.tencent.wea.xlsRes.RewardStatus            2      # 领奖状态
   auto             bool                                           3      # 是否自动领奖
   costItem         map<RewardCostItem>                            4      # 需要扣除的道具

@AchievementInfo # 成就信息
   achievements        map<DBAchievementInfo>                   4         # 成就进度
   achievementComplete map<AchievementCompleteInfo>             5         # 成就完成信息

@AchievementCompleteInfo # 已完成成就
   id                   int32                                1    +key       # 成就id
   stageInfo            map<AchievementStageCompleteInfo>    4               # 阶段信息

@AchievementStageCompleteInfo # 已完成的阶段成就
   stageId              int32                           1    +key       # 阶段id
   time                 int64                           2               # 结束时间
   rewarded             bool                            3               # 是否领奖
   conditionIndex       int32                           4               # 完成的条件序号

@DBAchievementInfo # 成就信息
   id                       int32                                        1   +key   # 成就id
   value                    int64                                        2          # 进度值
   valueList                int64[]                                      1002 +nocs # 服务器内部辅助字段
   valueInternal            int64                                        1003 +nocs # 服务器内部辅助字段
   conditionData            map<AchievementDataInfo>                     3       # 条件数据

@AchievementDataInfo
  idx                      int32                                        1   +key   # 条件序号
  value                    int64                                        2          # 进度值
  valueList                int64[]                                      1002 +nocs # 服务器内部辅助字段
  valueInternal            int64                                        1003 +nocs # 服务器内部辅助字段



@CoinInfo
   coinType   int32  1   +key
   coinNum    int64  2

@Money
   coin                    map<CoinInfo>       1      # 各种货币
   saveAmt                 int64               2      # 累计充值星钻数量
   firstSaveFlag           bool                3      # 是否充值过
   buyLock                 map<MidasLockInfo>  4 +nocs # 支付锁
   saveRmb                 int64               6       # 累计实际花费人民币，充值代币花费的钱不计入，单位：分
   presentFailOrder        map<MidasPresentFailOrder>   7  +nocs # 赠送失败订单
   midasChargeInfo         map<MidasChargeInfo>         8 # 米大师充值信息
   presentBalance          int64                9  +nocs  # 当前余额中属于赠送星钻的数量
   sumPresent            int64                10  +nocs  # 累计赠送星钻数量
   sumBalance             int64                11  +nocs  # 历史总增加的游戏币余额
   sumCost                int64                12  +nocs  # 历史总消耗的游戏币余额
   platSaveAmt             int64               13   +nocs   # 端外累计充值星钻数量(不走米大师充值的渠道，例如心悦)
   hourChargeInfo         map<HourChargeInfo>         14 +nocs # 每小时充值金额  废弃
   buyOrderInfo            BuyOrderInfo         15  +nocs   # 直购订单
   changePlatMidasDiff     ChangePlatMidasDiff  16          # 平台迁移米大师数据差值

@ChangePlatMidasDiff
   fromPlatId           int32       1           # 之前的平台ID
   syncTimeMs           int64       2           # 同步时间
   presentBalanceDiff   int64       3           # 赠送账户星钻差值
   sumSaveDiff          int64       4           # 累计充值星钻差值
   sumPresentDiff       int64       5           # 累计赠送星钻差值
   sumBalanceDiff       int64       6           # 历史总增加的游戏币差值
   sumCostDiff          int64       7           # 历史总消耗的游戏币差值

@BuyOrderInfo
   tradeNo              string              1       # 订单号
   buyTimeMs            int64               2       # 下单时间
   offerId              string              3       # 下单时所用米大师offerId

@HourChargeInfo
   hourTime             int32               1 +key # 小时时间戳
   moneyNum              int32              2      # 充值金额：角

@MidasChargeInfo
   productId               string             1  +key # 米大师商品id
   num                     int32              2       # 充值次数

@MidasPresentFailOrder
   billNo                     string            1  +key
   presentCnt                 int32             2       # 赠送数量
   addTime                    int64             3
   retryNum                   int32             4       # 重试次数
   busBillNo                  string            5       # 内部订单号
   failPayToken               string            6       # 失败时用的paytoken
   presentReason        com.tencent.wea.xlsRes.ItemChangeReason  7  # 废弃（不用枚举）
   presentReasonValue         int32             8
   subReasonData              SubReasonData     9

@SubReasonData
   subReason                 int64              1
   changeReservedParams      int64[]            2

@MidasLockInfo
   productId                string            1  +key
   ts                       int64             2

@MallInfo
   commonMallInfo          CommonMallInfo          1            # 普通商城
   scenePackageInfo        map<ScenePackageInfo>   2            # 场景礼包
   mallDemandInfo          MallDemandInfo          3            # 索要信息
   mallGiveRecord          map<MallGiveRecord>     4 +nocs      # 赠送记录
   mallDeletedGiveRecord   set<int32>              5 +nocs      # 标记删除赠送记录
   expiredScenePackageId   set<int32>              6 +nocs      # 已经推送且过期的场景礼包ID
   recScenePackageInfo     RecScenePackageInfo     7 +nocs      # 场景礼包推荐信息
   recvLimitRecords        map<CommodityInfo>      8 +nocs      # 累计接收数量有上限的商品记录
   themeMallData           map<ThemeMallInfo>      9 +nocs      # 主题商城数据
   mallRedPointInfo        map<MallRedPoint>       10 +nocs     # 商城红点

@MallRedPoint
   mallId                  int32                         1 +key  # 商城ID
   redPointTypeInfo        map<MallRedPointStatus>       2       # 红点信息

@MallRedPointStatus
   redPointType             int32                 1 +key     # 红点类型 MallRedPointType
   status                   int32                 2          # 红点状态
   updateTime               int64                 3          # 更新时间戳(秒)

@ThemeMallInfo
   id                       int32                 1  +key  # 主题ID
   buyNum                   int32                 2        # 已购买数量
   discount                 int32                 3        # 折扣

@RecScenePackageInfo
   dailyRecCnt              int32            1           # 每日推荐次数
   weeklyRecCnt             int32            2           # 每周推荐次数
   lastRecTimeMs            int64            3


@MallDemandInfo
    demandCount             int32           1             # 索要次数
    giveCount               int32           2             # 赠送次数

@CommodityInfo
    id                      int32           1   +key      # 商品id
    num                     int32           2             # 商品数量

@MallGiftCard
    cardType                int32           1             # 赠礼卡模板类型。无赠礼卡则填0
    wordsId                 int32           2             # 赠礼卡默认祝福语id。自定义祝福语则填0
    wordsContent            string          3             # 赠礼卡自定义祝福语内容

@MallGiveRecord
    giveId                  int32                   1  +key       # 记录id
    friendUid               int64                   2             # 好友uid
    giveItemList            map<RewardItemInfo>     3             # 赠送道具
    giveTime                int64                   4             # 赠送时间
    giveCommidityList       map<CommodityInfo>      5             # 赠送的商品
    card                    MallGiftCard            6             # 外观类商品赠礼卡信息
    mailId                  int64                   7             # 关联接收方邮件id。未使用

@CommonMallInfo
   boughtRecord      map<BuyRecordStruct>       1        # 购买记录
   nextRefreshTime   int64                      2        # 下次刷新时间
   commodityNtf      map<CommodityRedPoint>     3  +nocs # 商品红点开关

@BuyRecordStruct
   commodityId       int32   1   +key
   buyNum            int32   2          # 已购买数量
   expireTimeMs      int64   3          # 下次刷新时间
   updateTimeMs      int64   4          # 购买记录更新时间
   currentCanBuyNum  int32   5          # 当前可购买数量

@CommodityRedPoint
   commodityId       int32   1   +key
   showRedPoint      int32   2               # 0:不显示红点 1:显示

@ScenePackageInfo
   id                      int32                               1   +key
   pushTimeMs              int64                               2
   conditionGroup          ConditionGroup                      3   +nocs     # 条件组
   buyedCommodityId        int32                               4             # 已购买商品ID
   recid                   string                              5             # 推荐请求，上报到rcmdinfo字段，点击流要上报
   expTag                  string                              6             # 实验标签，上报到abtestinfo字段，点击流和抽卡日志都要上报
   buyTimeMs               int64                               7   +nocs     # 礼包购买时间

@MallWishListPublic
   commoditys           map<MallWishCommodity>  1        # 商品列表

@MallWishCommodity
   commodityId       int32                  1   +key
   hasOwn            bool                   2           # 是否已拥有
   notSale           bool                   3           # 是否未上架
   hideToOthers      bool                   4           # 是否对其他人不可见
   addTimeMs         int64                  5           # 添加时间(毫秒)

@ModSettings
    modQuality              int32   1       # 画质(具体数值代表的含义客户端自行确定) del
    simplifySpecialEffects  bool    2       # 简化局内特效
    hideNick                bool    3       # 隐藏局内昵称
    bgmVolume               float   4       # 背景音量(0-100) del
    soundEffectsVolume      float   5       # 音效音量(0-100) del
    noteRatio               float   6       # 音符速率(默认1.5) del
    hideEmoji               bool    7       # 隐藏局内表情
    actionSeting            bool    8       #道具设置

@FpsSettings
    shootMode               int32[]    1    # 不同玩法下射击模式
    viewMode                int32[]    2    # 不同玩法下视角
    aimBtnRotate            bool       3    # 瞄准按钮控制转向
    sniperFireMode          int32      4    # 狙击枪开火模式
    joyStickMode            int32      5    # 摇杆模式
    cameraSensitivity       float      6    # 视角灵敏度
    aimCameraSensitivity    float      7    # 狙击枪视角灵敏度
    openGyro                bool       8    # 启用陀螺仪
    gyroSensitivity         float      9    # 陀螺仪灵敏度
    aimGyroSensitivity      float      10   # 陀螺仪开镜灵敏度
    isSetByPlayer           bool       11   # 玩家是否修改过
    sightCameraSensitivity  float[]    12   # 准镜视角灵敏度
    sightGyroSensitivity    float[]    13   # 准镜陀螺仪灵敏度


@TYCFpsSettings
    gameMode                int32      1  +key      #  玩法类型通用
    shootMode               int32      12    # 不同玩法下射击模式
    viewMode                int32      2    # 不同玩法下视角
    aimBtnRotate            bool       3    # 瞄准按钮控制转向
    sniperFireMode          int32      4    # 狙击枪开火模式
    joyStickMode            int32      5    # 摇杆模式
    cameraSensitivity       float      6    # 视角灵敏度
    aimCameraSensitivity    float      7    # 狙击枪视角灵敏度
    openGyro                bool       8    # 启用陀螺仪
    gyroSensitivity         float      9    # 陀螺仪灵敏度
    aimGyroSensitivity      float      10   # 陀螺仪开镜灵敏度
    isSetByPlayer           bool       11   # 玩家是否修改过
    leftAimBtn              bool       13   # 左侧瞄准按钮开关
    showSniperScope         bool       14   # 显示狙击镜

@HOKSettings
    targetSelectionStrategy int32      1    # 优先攻击目标
    isAutoNormalAttack      bool       2    # 自动普通攻击
    cameraControlType       int32      3    # 镜头设置
    shopPosition            int32      4    # 商店位置
    mapPosition             int32      5    # 小地图位置
    autoBuyCard             bool       6    # 快捷购买装备
    showEnemyHeadIconOutOfScreen      bool       7    # 是否展示超出屏幕的头像
    autoAddSkill            bool       8    # 自动加点
    minAngle                int32      9    # 最小俯仰角度
    maxAngle                int32      10   # 最大俯仰角度
    cameraSensitivity       float      11   # 镜头灵敏度
    settingMap              map<HOKExpandSetting> 12  # 扩展设置
    isDefenceTowerRemind    bool       13   # 防御塔受击提醒
    showActivityTitle       bool       14   # 活动称号是否常驻显示

@HOKExpandSetting
    settingKey              string     1    +key # 设置名称
    settingValue            string     2         # 设置值

@SquadActivityHistoryData
    activityId              int32                                       1   +key    # 活动id
    memberUidList           set<int64>                                  2           # 成员uid列表
    lastTimestamp           int64                                       3           # 最后更新时间
    playerInvited           set<int64>                                  4           # 已邀请玩家列表

@ActivityHistoryData
    squadData                       map<SquadActivityHistoryData>               1           # 小队相关历史数据
    updateForesightSubscribed       set<int32>                                  2           # 已订阅的版本前瞻活动



@SquadActivityHistoryPlusData
    groupType               int32                                       1   +key    # 活动组队类型
    activityId              int32                                       2           # 活动id
    memberUidList           set<int64>                                  3           # 成员uid列表
    lastTimestamp           int64                                       4           # 最后更新时间
    playerInvited           set<int64>                                  5           # 已邀请玩家列表

@ActivityHistoryPlusData
    squadData                       map<SquadActivityHistoryPlusData>               1           # 小队相关历史数据
    updateForesightSubscribed       set<int32>                                      2           # 已订阅的版本前瞻活动




@ModNote
    visited                     int32                       1       # 已访问人数(用于限制db增长)
    currLvOnlineTime            int32                       2       # 当前等级在线时长
    qrCodePoint                 int32                       3       # 二维码同玩积分
    activityHistoryData         ActivityHistoryData         4       # 活动历史数据
    recentGameStartCount        int32                       5       # 最近开局次数(上个登陆日)
    todayGameStartCount         int32                       6       # 今日开局次数
    recentPlaySwitchCount       int32                       7       # 最近玩法切换次数(上个登陆日)
    todayPlaySwitchCount        int32                       8       # 今日玩法切换次数
    updateTime                  int64                       9       # 数据更新时间
    totalPlaySwitchCount        int32                       10      # 总玩法切换次数
    updateTimePlaySwitch        int64                       11      # 玩法切换的最后更新时间
    activityHistoryPlusData     ActivityHistoryPlusData     12       # 活动历史数据

# 玩家福袋信息
@PlayerBlessBagInfo
  bagId             int64   1               # 福袋id
  expireTimeMs      int64   2               # 福袋分享过期时间
  updateTime        int64   3     +nocs     # 数据更新时间

# 直播相关设置
@StreamSetting
  streamRight           bool                            1           # 开播权利
  onStream              bool                            2           # 是否在开播
  lastStreamTimestamp   int64                           3           # 上次开播时间
  streamToken           string                          4   +nocs   # 开播token
  streamPlatType        int32                           5   +key    # 直播平台类型 取值参考StreamPlatType
  streamPlayInfoId      string                          6           # 直播玩法id
  playState             int32                           7           # 玩法状态

# moba打赏
@ArenaTipInfo
  generousValue         int64                           1            # 慷慨值
  popularValue          int64                           2            # 人气值

@QQTeamInfo
  roomCreateTaskId          string                       1   +nocs   # 群应用开黑任务id
  roomTaskExpiredTime       int64                        2   +nocs   # 群应用开黑任务过期时间

@QQApplicationInfo
  qqTeamInfo                QQTeamInfo                  1   +nocs   # 开黑信息

@WelfareHistoryBill
    transactionNo               string                          1   +key    # 订单号
    billType                    int32                           2           # 1-xhh 2-medal
    billNum                     int32                           3           # 补发数量
    recordTimeMs                int64                           4           # 订单时间
    retryTimes                  int32                           5           # 重试次数

@WelfareAerospaceTechEdData
    bills                       map<WelfareHistoryBill>         1       # 待重试订单

@WelfareData
    aerospaceTechEdData         WelfareAerospaceTechEdData      1           # 航天科普数据

@WhiteListInfo
    type                        int32                           1   +key    # 白名单类型 参考 HotResWhiteListType
    openTime                    int64                           2           # 白名单开启时间，0一直开
    closeTime                   int64                           3           # 白名单关闭时间，0一直开

@UserLabel
    id                          int32                           1   +key    # 标签id
    numVals                     int64[]                         2           # 标签值,数字类型
    strVals                     string[]                        3           # 标签值,字符类型
    expiredTime                 int64                           4   +nocs   # 过期时间

@UserConcertTicketInfo  # 演唱会纪念票信息
  concertId       int32   1 +key    # 演唱会ID
  ticketId        int64   2         # 纪念票ID
  starId          int32   3         # 明星ID
  expressionId    int32   4         # 表情ID
  lightBoardId    int32   5         # 灯牌ID

@UserConcertData    # 玩家演唱会信息
  concertTicket   map<UserConcertTicketInfo>    1 # 纪念票信息

@UgcMatchRecord # ugc匹配记录
  playId      int64       1 +key  # 唯一id（存储为对局id）
  playTime    int64       2       # 对局时间
  playUgcId   int64       3       # 对局的地图
  playName    string      4       # 游玩的图集或地图名
  playUgcName string      5       # 游玩的地图名

@UgcMatchInfo # ugc匹配信息
  matchRecords  map<UgcMatchRecord> 1 #记录

@UgcMiniGamePlayInfo # ugc小游戏游玩记录
  playRecords  map<UgcMatchRecord> 1 #记录

@PakDownloadInfo
  pakGroupId                    int32                           1   +key    # 分包组id
  downloaded                    bool                            2           # 下载与否

@ClientPakInfo
  pakInfo                       map<PakDownloadInfo>            1           # 分包数据
  detailPakInfo                 map<PakDownloadInfo>            2           # 细节分包数据

@PushTopicHandle
  index                         string                          1  +key     # handle 索引
  topic                         string                          2           # 主题名字
  partition                     string                          3           # 分片索引
  upTimeSec                     int64                           4           # 更新时间

@PlayerPushTopic
  handle                      map<PushTopicHandle>              1

@LobbyLobbyMatchHistoryInfo
  matchID                       int64                           1   +key     #匹配id
  timeMS                        int64                           2            #时间戳
  uid                           int64                           3            #uid

@LobbyMatchInfo
  matchID                       int64                           1           #匹配id
  state                         com.tencent.wea.xlsRes.LobbyMatchStatusEnum    2  #当前状态
  timeMillSec                   int64                           3           #开始时间
  matchTypeID                   int32                           4           #玩法模式id
  matchSvrID                    int32                           5           #匹配服务节点
  roomInfoID                    int32                           6           #房间配置ID
  ruleID                        int32                           7           #规则ID
  matchHistoryList              map<LobbyLobbyMatchHistoryInfo> 8   +nocs   #匹配历史记录

@CommunityEntryRewardInfo  # 社区入口领奖信息，记录哪些社区入口的奖励已经领取
    id      int32      1        +key            # 配置ID，如有多个社区入口奖励，可通过此id区分

@UserAttr   +listenable,+root #玩家数据
    uid                         int64                          1    +brief  # 玩家id
    playerProfileInfo           PlayerProfileInfo              2
    basicInfo                   BasicInfo                      3            # 基础信息
    playerEventData             PlayerEventData                4    +nocs   # 事件数据
    activityAddNewAidInfo       ActivityAddNewAidInfo          5    +nocs   # 上架辅助信息
    activityCenter              ActivityCenter                 6            # 新活动中心
    RoomInfo                    AttrRoomInfo                   7            # 当前房间状态
    relationMapInfo             RelationMapInfo                8            # 关系信息
    itemInfo                    ItemInfoDb                     9            # 道具信息
    playerPublicProfileInfo     PlayerPublicProfileInfo        10           # 玩家可公开个人信息
    bagInfo                     BagInfoDb                      11           # 背包信息
    achievementInfo             AchievementInfo                12   +nocs   # 成就信息
    money                       Money                          13           # 玩家游戏货币
    mallInfo                    MallInfo                       14           # 商城信息
    exchangeCenter              ExchangeCenter                 16           # 兑换中心
    collectionInfo              map<CollectionInfos>           17           # 图鉴
    taskInfo                    TaskInfo                       18   +nocs   # 任务关系
    playerPublicSummaryInfo     PlayerPublicSummaryInfo        19   +nocs   # 服务器之间玩家公开摘要信息
    openedLevels                set<int32>                     20   +nocs   # 通关的关卡
    battleInfo                  BattleInfo                     21           # 战场信息
    modSettings                 ModSettings                    24           # 游戏设置
    guideTasks                  set<int32>                     25           # 完成的引导任务ID
    modNote                     ModNote                        26           # 游戏数据备忘
    commonLimit                 map<CommonLimitInfo>           27           # 通用限制
    publicChatInfo              PublicChatInfo                 28           # 公共聊天信息
    lastDressOutLook            map<LastDressOutLookInfo>      29           # 最后穿戴的装扮信息
    clubInfo                    AttrClubInfo                   30           # 当前公会信息
    permitInfo                  AttrPermitInfo                 31           # 当前通行证信息
    targetEquipInfo             map<TargetEquipInfo>           32           # 位置穿戴信息
    sceneInfo                   AttrSceneInfo                  33           # 场景信息
    abTestInfo                  map<ABTestInfo>                34           # ABTest信息
    specReward                  SpecRewardInfo                 35           # 特殊奖励状态信息
    benefitCardInfo             BenefitCardInfo                36           # 权益卡
    commonGiftBuyInfo           map<CommonGiftBuyInfo>         37   +nocs   # 礼包购买记录
    idipTaskInfo                IdipTaskInfo                   38   +nocs   # idip 任务记录
    rewardNtf                   map<RewardNtfInfo>             39           # 需要弹窗的奖励
    playerPublicEquipments      PlayerPublicEquipments         41
    playerPublicGameData        PlayerPublicGameData           42
    playerPublicGameSettings    PlayerPublicGameSettings       43
    playerPublicHistoryData     PlayerPublicHistoryData        44
    playerPublicLiveStatus      PlayerPublicLiveStatus         45
    playerPublicSceneData       PlayerPublicSceneData          46
    unLockGameModeSet           set<int32>                     47           # 解锁玩法
    clientCacheData             map<ClientCache>               48           # 客户端云端存储
    lobbyInfo                   LobbyInfo                      49           # 大厅信息
    seasonInfo                  SeasonInfo                     50           # 赛季信息
    rankData                    PlayerRankData                 51   +nocs   # 排行信息
    raffleInfo                  map<PlayerRaffleInfo>          52           # 抽奖信息
    activitySquad               ActivitySquadInfo              53           # 活动小队
    levelIllustration           LevelIllustration              54           # 关卡图鉴
    fittingSlots                FittingSlots                   55           # 试衣间
    interactions                map<Interaction>               56           # 交互表情动作槽位
    rechargeInfo                RechargeInfo                   57           # 充值和vip的一些信息
    ugcMapSetInfo               UgcMapSetInfo                  58           # ugc地图设置信息
    selfMessageSlip             SelfMessageSlip                59   +nocs   # 我的留言
    mailCache                   MailCache                      61   +nocs   # 邮件缓存
    levelRecord                 LevelRecord                    62   +nocs   # 关卡记录，收集关卡关于赛季、玩法等维度的数据
    raffleGroupInfo             map<PlayerRaffleGroupInfo>     63           # 抽奖组信息
    playerPublicBasicInfo       PlayerPublicBasicInfo          64           #
    qaInvest                    map<QAInvestInfo>              65           # 问卷调查
    matchStatics                MatchStaticsDb                 66           # 匹配信息
    unLockLabels                map<LabelInfo>                 67           # 个性化标签
    prayInfo                    PrayInfo                       68           # 祈福数据
    qaInvestTag                 map<QAInvestTag>               69   +nocs   # 问卷标签
    itemPackageLimit            map<ItemPackageLimit>          70   +nocs   # 礼包限制计数
    ugcDailyStage               UgcDailyStage                  71   +nocs   # ugc日常闯关
    xiaoWoInfo                  XiaoWoInfo                     72           # 小窝信息
    partyInfo                   PartyInfo                      73           # 派对
    ugcOpInfo                   UgcOpInfo                      74           # ugc粉丝订阅操作额外信息
    waitSettlementBattle        map<WaitBattleInfo>            75    +nocs  # 未结算的battle
    playerBlessBagInfo          PlayerBlessBagInfo             76           # 玩家福袋信息
    ugcStarWorld            	  UgcStarWorld                   77           # ugc新版巡游
    roguelikeInfo               RoguelikeInfo                  78           # 肉鸽玩法数据
    roguelikeExtraInfo          RoguelikeExtraInfo             79           # (已废弃)肉鸽玩法额外数据
    roguelikeTalentData         RoguelikeTalentData            80           # (已废弃)肉鸽玩法天赋数据
    multiPlayerSquad            MultiPlayerSquadInfo           81           # 多人小队
    ugcAccountInfo              UgcAccountInfo                 82           # ugc账号信息
    streamSetting               StreamSetting                  83           # 开播设置 - 废弃
    snsInvitationInfo           map<PlayerSnsInvitationInfo>   84           # 分享邀请
    snsInvitationAccepted       map<PlayerSnsAcceptInfo>       85           # 助力记录
    fireworksInfo               FireworksInfo                  86           # 烟花活动信息
    redEnvelopeRainActs         RedEnvelopeRainActivities      87           # 红包雨活动
    commonConditionAttr         map<CommonConditionGroup>      88           # 通用功能解锁条件进度保存
    idipInfo                    PlayerIdipInfo                 89           # idip修改的数据
    qqApplicationInfo           QQApplicationInfo              90           # qq群应用相关信息
    dsUserDBInfoMap             map<DsUserDBInfo>              91           # DS中玩家需要存储的信息集合(不再使用，移动到了tcaplus中UserDsDBTable独立存储)
    fpsSettings                 FpsSettings                    92   +nocs   # FPS玩法设置
    roguelikeTaskData           RoguelikeTaskData              93   +nocs   # (已废弃)肉鸽玩法任务数据
    specialFaceAttr             map<SpecialFace>               94           # 专属拍脸数据
    marqueeNoticeInfo           MarqueeNoticeInfo              95   +nocs   # 全服跑马灯数据
    welfareData                 WelfareData                    96   +nocs   # 公益相关数据
    returningInfo               ReturningInfoDb                97           # 回归数据
    newYearPilotInfo            NewYearPilotInfo               98   +nodb   # 新年活动数据
    fixTag                      map<KeyValStr>                 99   +nocs   # 玩家数据修复标记
    intellectualActivity        IntellectualActivity           100  +nocs   # 脑力达人活动
    tycFpsSettings              map<TYCFpsSettings>            101   +nocs  # tycFPS玩法设置
    ugcCoPlayInfo               UgcCoPlayInfo                  102  +nocs   # UGC同游记录
    ugcCollection               UgcCollection                  103          # UGC合集
    recentActivity              AttrRecentActivity             104  +nocs   # 玩家近期活跃数据
    aiChatInfo                  AiChatInfo                     105          # 智能npc对话信息
    whiteListInfo               map<WhiteListInfo>             106          # 白名单信息
    stickFriends                map<StickFriendInfo>           107          # 好友置顶信息
    ugcBuyGoodsInfo             UgcBuyGoodsInfo                108          # ugc地图内购信息
    grayTagsInfo                PlayerGrayTagsInfo             109          # 玩家灰度标签信息
    userLabels                  map<UserLabel>                 110          # 用户标签信息
    activitySquadDetails        map<ActivitySquadDetail>       111          # 小队活动信息集合
    concertData                 UserConcertData                112 +nocs    # 演唱会数据
    ugcSlotKeyInfo              UgcSlotInfo                    113          # ugc快捷方式 (全局)
    farmInfo                    FarmInfo                       114          # 农场信息
    gameTvInfo                  GameTvInfo                     115          # 电视台信息
    xlsWhiteList                map<XlsWhiteList>              116          # 配置的白名单
    combinationSettings         CombinationSettings            117          # 组合的设置
    ugcGrowUpInfo               UgcGrowUpInfo                  118          # ugc星世界成长(星世界等级、活跃、徽章等)
    bubble                      BubbleConfig                   119          # 气泡入口配置
    album                       AlbumInfo                      120          # 相册
    matchUnlock                 map<MatchUnlockInfo>           121 +nocs    # 玩法模式-解锁
    qqbot                       SubscribeQqRobt                122          # qq订阅机器人数据
    recommendMatchType          RecommendMatchType             123          # 限时娱乐推荐
    guideStatistics             GuideStatistics                124          # 引导数据统计
    matchTypeHistory            MatchTypeHistory               125          # 玩法模式-历史玩过
    friendRecommendInfo         FriendRecommendInfo            126          # 好友推荐信息
    iaaInfo                     map<PlayerIAAInfo>             127          # IAA信息
    entertainmentGuide          EntertainmentGuide             128          # 娱乐向导
    ugcMatchInfo                UgcMatchInfo                   129 +nocs    # ugc匹配信息
    algoRecommendMatchInfo      AlgoRecommendMatchInfo         130          # 算法专属玩法推荐
    rewardCompensate            RewardCompensateInfo           131          # 奖励补领信息
    secondaryPassword           SecondaryPassword              132          # 二级密码
    cupsInfo                    CupsInfo                       133          # 奖杯信息
    pixuiRedDot                 map<PixuiRedDotInfo>           134 +nocs    # pixui红点信息
    wolfKillInteractions         map<Interaction>              135          # 狼人交互表情动作槽位
    allSeasonNoteBookAttr       UserAllSeasonNoteBookAttr      136          # 用于存放不用放在PlayerPublic中的赛季手册数据
    bpInfo                      map<BPInfo>                    137          # BP通行证信息
    biSeq                       RaffleBISeq                    138          # 抽奖BI推荐顺序
    clientLogColoring           map<ClientLogColoring>         139          # 账号日志着色
    unLockPreparationWidgetSet  set<int32>                     140          # 解锁备战按钮
    lastUpdateMainVersionTimeMs int64                          141          # 上次更新大版本的时间
    ugcActivityInfo             UgcActivityInfo                142          # 需要同步的ugc活动数据
    gameTimesStatistics         GameTimesStatistics            143          # 副玩法次数统计
    arenaGameData               ArenaGameData                  144          # arena玩法game数据
    iaaData                     PlayerIAAData                  145          # IAA数据
    houseInfo                   HouseInfo                      147          # 农场小屋信息
    wolfKillSeasonRewardList    map<WolfKillRewardItem>        148 +nocs    # 狼人一局获取的奖励物品，用于合并发送
    clientPakInfo               ClientPakInfo                  149          # 客户端分包信息
    raffleStash                 map<RaffleStash>               150          # 抽奖组暂存信息
    wolfKillNewUserTips         map<WolfKillNewUserTipsInfo>   151          # 狼人局内新手提示
    streamSettingList           map<StreamSetting>             152          # 所有的开播信息
    arenaTipInfo                ArenaTipInfo                   153          # moba打赏记录
    arenaSettings               ArenaSettings                  154          # moba 设置
    playerLevelEstimation       PlayerLevelEstimation          155          # 已评价关卡ID集合
    hokSettings                 HOKSettings                    156          # hok玩法设置
    playerGameActionInfos       PlayerGameActionInfos          157          # 需要记录的玩家游戏内动作
    hokAttrInfo                 HOKAttrInfo                    158          # hok的属性信息
    guidedDiscoverInfo          GuidedDiscoverInfo             159          # 发现引导信息
    rewardRetrieval             AttrRewardRetrievalData        160          # 奖励找回
    mallWishList                MallWishListPublic             161          # 商城心愿单
    unLockGameModeHistorySet    set<int32>                     162          # 历史解锁过的玩法
    appearanceRoad              AppearanceRoad                 163          # 时尚之路
    wolfKillInfo                WolfKillInfo                   164          # 狼人杀的其他信息
    tradingCardData             TradingCardData                165          # 卡牌数据
    pushTopicInfo               PlayerPushTopic                166          # 推送信息
    lobbyMatchInfo              LobbyMatchInfo                 167          # 万松书院匹配信息
    cocModeInfo                 CocModeInfo                    168          # coc玩法数据
    birthdayData                AttrBirthdayData               169          # 生日数据
    seasonReview                SeasonReview                   170          # 赛季回顾统计数据
    shareGiftInfo               ShareGiftInfo                  171          # 分享礼包数据
    farmReturningInfo           FarmReturningInfo              172          # 农场回流活动管理
    chaseGameData               ChaseGameData                  173          # 大王玩法game数据
    safetyCheck                 SafetyCheck                    174          # 需要安全性检查的结构
    ugcMiniGamePlayInfo         UgcMiniGamePlayInfo            175 +nocs    # UGC小游戏专区游玩记录
    passwordCodeDataList        map<PasswordCodeData>          176  +nocs   # 口令码任务数据
    richInfo                    NR3E8RichInfo                  177          # 大富翁信息
    generalRedDotMap           map<GeneralRedDotInfo>          178  +nocs   # 通用红点数据
    danceData                   DanceData                      179          # 乐队舞台信息
    gameReturnModeData          GameModeReturnData             180          # 玩法回流数据
    cookInfo                    CookInfo                       181          # 农场餐厅信息
    chaseGameDataNoPublic       ChaseGameDataNoPublic          182          # 大王玩法game数据,不存public
    limitTimeExperienceItem     LimitTimeExperienceItem        183          # 限时体验道具
    starPInfo                   StarPInfo                      184  +nocs   # 啾灵数据
    starPChatInfo               StarPChatInfo                  185          # 啾灵聊天系统信息(适用元梦大厅&SP玩法)
    dailyVictoryRecord          map<ArenaDailyVictoryRecord>   186  +nocs   # 记录最近N天moba对战的胜利次数
    sendHokExperienceCard       int32                          187  +nocs   # 是否发送5v5新手体验卡 0：未发送  1：已发送
    upgradeCheckHistoryBuyTimes int32                          188          # 打卡手册历史购买次数最后一天前
    farmDailySumBuyTimes        int32                          189          # 农场天天领总购买次数
    upgradeCheckLastDayBuyTime  int32                          190          # 打卡手册最后一天购买次数
    flashRaceCheerAi            map<KvLL>                      191          # 闪电赛助威数据 KEY-阶段截止时间戳 value-周期内是否助威 0未助威 1已助威
    fashionSkilluse             set<int32>                     192          # 时装技能使用
    playerPakPlayRecord         map<PlayerPakPlayRecord>       193          # 玩家最近x周玩法次数记录
    forceOpenMatchType          set<int32>                     194          # 强制打开的玩法id
    playerLoginPlat             set<int32>                     195          # 玩家登入过的平台
    itaBagData                  map<ItaBagInfo>                196          # 痛包 itemUUID->ItaBagInfo
    dailyAwardSumBuyInfoMap     map<DailySumBuyTimesInfo>      197          # 天天领总购买次数
    communityEntryRewardInfo    map<CommunityEntryRewardInfo>  198          # 社区入口奖励领取信息
    displayBoard                AttrDisplayBoardData           200          # 推图展板
    masterPathData              MasterPathData                 201          # 大师之路
    pcWebToSimulatorMailTimes   int32                          202          # pcweb 转 pc 模拟器邮件发送次数
    battleSettlementItemBuffData BattleSettlementItemBuffData  203          # 战斗结算道具buff
    commonSevenDayTask          map<CommonSevenDayTaskData>    204  +nocs   # 通用7日任务
    battleDateData              map<BattleDateData>            205  +nocs   # 玩法最近30个游玩日期
    kaibo                       KaiboInfo                      206          # 开播相关信息
    checkConditionFinishId      set<int32>                     207          # 已经完成的判断类型条件
    stochasticStates            StochasticStateDelegations     209  +nocs   # 随机产出状态管理
    cloudUnLockPreparationWidgetSet  set<int32>                208          # 云游戏解锁备战按钮
    endToEndRewards             map<EndToEndRewardsTime>       210          # 端转端奖励
    onlineEarningData           OnlineEarningData              211  +nocs   # 网赚系统
    fashionShareData            FashionShareData               212          # 皮肤共享
    modeRecommendLimit          bool                           213          # 玩法推荐限制
    gameplaySkillScore          map<KvIF>                      214          # 玩法id对应的实力分matchTypeId->skillScore
    chestGameInfo               ChestGameInfo                  215          # Chest玩法数据

@ChestGameInfo
    chestItemDb                 ChestItemDb                          1        # ChestItemDb
    chestCollectionItemInfo     map<ChestCollectionItemInfo>         2 +nocs  # 收藏品数据
    currentRole                 ChestRoleInfo                        3        # 当前角色
    chestIdentityProficiencyInfo ChestIdentityProficiencyInfo        4        # Chest身份
    chestBattleInfo             map<ChestBattleInfo>                 5 +nocs  # chest对局数据
    chestRankInfo               ChestRankInfo                        6        # 带出道具价值排行榜数据

@ChestRankInfo
   lastUpdateTime              int64                       1        # 最后更新时间
   chestRankValues             map<ChestActorRankValue>    2        # 
   rankRewardTime              int64                       3        # 排行榜结算时间

@ChestBattleCarryOutInfo
   battleId                  int64                      1 +key 
   battleTime                int64                      2
   itemValues                int64                      3

@ChestActorRankValue
   actorType                 int32                         1 +key   # 阵营ID 暗星/星宝
   topNValueList             map<ChestBattleCarryOutInfo>  2 +nocs  # 带出最多价值的的前X局
   weeklyTotalValue          int64                         3        # 计入排行榜分数

@ChestBattleInfo
   battleId                  int64                      1 +key   # battleId
   battleCreateTime          int64                      2        # 对局开始时间
   chestEquipItemsBackup     map<ChestItem>             3        # chest备战背包道具备份
   safeHouseItems            map<ChestItem>             4        # 存入安全屋的道具

@ChestItemDb
    chestItems               map<ChestItem>             1  # ChestItems

@ChestCollectionItemInfo
    collectionId             int32                      1 +key  # 收藏品ID
    curLevel                 int32                      2       # 当前等级
    curExp                   int32                      3       # 当前经验值
    totalExp                 int32                      4       # 累计经验值
    totalCostItemNum         int32                      5       # 累计消耗道具数量
    entryInfo                map<EntryInfo>             6       # 词条信息
    isEntryPending           bool                       7       # 词条洗练状态 0 无待确认洗练 1 有待确认洗练
    itemIsUnlock             bool                       8       # 收藏品是否解锁
    lockEntryId              set<int32>                 9       # 被锁定的词条ID
    itemIsNewUnlock          bool                       10      # 是否新解锁

@EntryInfo
    entryId                  int32                      1 +key  # 词条ID
    isUnlock                 bool                       2       # 词条是否解锁
    currentEntryValue        int32                      3       # 当前洗练值
    candidateEntryValue      int32                      4       # 待确认洗练值

@ChestRoleInfo
   actorId                    int32                                        1           # 角色Id
   actorType                  int32                                        2           # 角色类型

@ChestIdentityProficiencyInfo
    completeFirstCheckOpenFightPower   int32                             1  +nocs   # 首次解锁专精是否成功
    chestIdentityProficiencyDatas map<ChestIdentityProficiencyData>      2  +nocs   # Chest熟练度
    dailyAddProficiency                int32                             3  +nocs   # 每日获取的总计熟练度
    refreshTimeMs                      int64                             4  +nocs   # 刷新的时间戳

@ChestIdentityProficiencyData   #大王身份熟练度 30条
    identityId                          int32           1  +key     # 身份ID
    proficiency                         int32           2           # 熟练度
    unlockBiography                     set<int32>      3           # 已解锁的角色小传
    unlockSpecialization                int32           4           # 是否解锁专精
    claimedProgressReward               set<int32>      5           # 已领取过的进度奖励
    lastPlayTime                        int64           6           # 最后游玩时间
    unReadBiography                     set<int32>      7           # 未读的角色小传

@FashionShareData
    shareCount             int32                       1  # 当前已共享次数
    shareLastResetTime     int64                       2  # 上次共享次数刷新时间
    revCount               int32                       3  # 当前已借用次数
    revLastResetTime       int64                       4  # 上次借用次数刷新时间
    fashionShareInfo       map<FashionShareInfo>       5  # 时装共享信息

@FashionShareInfo
    fashionId              int32                                  1 +key         # 时装id
    fashionSharePlayerMap  map<FashionSharePlayerInfo>            2              # 被共享人uid-共享记录信息

@FashionSharePlayerInfo
    toUid                   int64                                1 +key         # 时装id
    shareBeginMs            int64                                2              # 共享开始时间
    shareEndMs              int64                                3              # 共享结束时间

@EndToEndRewardsTime
    id                     int32                                1 +key         # 转端id
    rewardsTimes           int32                                2              # 奖励次数

@UgcMapSimpleActivity
  detailInfo       map<UgcMapSimpleActivityDetailInfo>            1

@UgcMapSimpleActivityDetailInfo
  activityType                int32             1  +key
  activityId                  int32             2
  taskInfo                    map<UgcMapSimpleActivityTaskInfo> 3
  extraLongValueList          int64[]           4
  taskCommonValue             int32             5


@UgcMapSimpleActivityTaskInfo
  taskId                int32             1  +key         # id
  value                 int32             2
  status                int32             3       # 0未完成  1已完成 2已领奖

@CommonSevenDayTaskData
    id                     int32                                1 +key         # 启用id
    playerType             int32                                2              # 1.新玩家 2.老玩家
    unLockDay              int32                                3              # 已解锁天数
    popup                  bool                                 4              # 自动弹窗 false.需要 true.不需要
    popupCount             int32                                5              # 自动弹窗对局数统计
    animationStatus        map<SevenDayTaskAnimation>           6              # 播放过的演出

@SevenDayTaskAnimation
   day            int32        1   +key         # 天数
   pv             bool         2                # pv
   openAnimation  bool         3                # 开始演出
   endAnimation   bool         4                # 结束演出

@BattleDateData
    gameTypeId             int32        1   +key         # 玩法根节点类型
    dateList               set<int32>   2                # 游玩日期列表

@ItaBagInfo
   itemId                    int32                   1        +key  # 痛包道具UUID
   itemUUID                  int64                   2              # 痛包道具UUID
   cosUrl                    string                  3              # cos存储信息
   badgeInfo                 map<KvLL>               4              # itemId => num
   cosVersion                string                  5              # cos版本号
   jsonVersion               string                  6              # json版本号

@DailySumBuyTimesInfo
    typeId                   int32                   1   +key              # 玩法类型id
    buyTimes                 int32                   2                     # 总购买次数

@LimitTimeExperienceItem
    id                       int32                   1   +key    # 每期ID
    itermList                set<int64>              2           # 道具
    redPoint                 bool                    3           # 每期开启的红点
    conditionGroupMap        ConditionGroup          4           # 条件

@AttrDisplayBoardData
  # historyContent                string[]                                1           # 历史内容
  itemUuid                      int64                                   1           # itemUuid
  displayBoardHistoryData       map<DisplayBoardHistoryData>            2 +nocs     # 历史设置(最大100张)
  waitCreateRoomEpochMillis     int64                                   4 +nocs     # 等待roomsvr创房时间戳毫秒

@DisplayBoardHistoryData
  setEpochMillis                int64                                   1 +key      # 设置时间戳毫秒
  ugcId                         int64                                   2           # ugcId

@AttrDisplayBoardInfo
  isOpen                        bool                                    1           # 推图版是否装配
  itemId                        int32                                   2           # 道具配置id
  content                       string                                  3           # 内容
  displayBoardUgc               AttrDisplayBoardUgc                     5           # 展板UGC信息
  hasPass                       bool                                    6           # 是否有密码
  roomId                        int64                                   7           # roomId
  curMemberNum                  int32                                   8           # 房间当前人数
  maxMemberNum                  int32                                   9           # 房间满员人数

@AttrDisplayBoardUgc
  ugcId                         int64                                   1           # ugcId
  ugcMapMetaInfo                map<AttrUgcMapMetaInfo>                 2           # 缩略图信息metaInfo (参见 message PublishItem)
  bucket                        string                                  3           # 缩略图信息bucket (参见 message PublishItem)

@AttrUgcMapMetaInfo
  size                          int32                                   1
  msg                           string                                  2           # md5
  msgType                       int32                                   3           # 消息类型, 具体值见枚举 MdType
  processType                   int32                                   4           # 处理类型, 详情见枚举 UgcMapMetaInfoProcessType
  version                       string                                  5
  isCoverCheckPass              bool                                    6           # 是否通过了图片检查（目前仅组合使用）
  preMsg                        string                                  7           # pre md5 加密前的md5，只有加密的文件才会有这个数据
  layerId                       int64                                   8           # 图层信息
  keyIgnore                     int32                                   31 +key     # map结构需要的key

@Vector
  x           float       1   # x
  y           float       2   # y
  z           float       3   # z

@Rotator
  roll        float       1   # roll
  pitch       float       2   # pitch
  yaw         float       3   # yaw

@Transform
  vector      Vector      1   # 偏移
  rotator     Rotator     2   # 旋转
  scale       float       3   # 缩放

@OrnamentTransform
  itemId                        int32                                   1 +key      # 配饰道具id
  itemUuid                      int64                                   2           # 道具uuid
  transform                     Transform                               3           # 自定义信息

@SuitOrnamentData
  suitItemId                    int32                                   1 +key      # 套装道具id
  itemUuid                      int64                                   2           # 道具uuid
  suitTransforms                map<OrnamentTransform>                  3           # 配饰

@OrnamentCustomData
  suitTransformData             map<SuitOrnamentData>                   1           # 所有套装配饰自定义数据

@OrnamentTransformDress
  itemId                        int32                                   1 +key      # 配饰道具id
  transform                     Transform                               2           # 自定义信息

@OrnamentCustomDress
  currSuitItemId                int32                                   1           # 当前穿着套装道具id
  dressTransforms               map<OrnamentTransformDress>             2           # 当前穿着套装配饰自定义数据

@ChaseSideStatics
    sideId                      int32               1   +key    #   阵营id
    battleRecordCnt             int32               2           #   总的战斗记录(已注册玩法只能获取字段加入后的数据)
    battleRecordCntCustom       int32               3           #   总的自定义房间的战斗记录(已注册玩法只能获取字段加入后的数据)

@ChaseMatchTypeBattleRecord
    matchTypeId                 int32                       1   +key    #   玩法模式id
    sideStatics                 map<ChaseSideStatics>       2           #   分玩法数据统计

@ChaseGameDataNoPublic
    isCompleteOldPlayerUnlock   bool                                1  +nocs  # 是否已完成老玩家一次性解锁指定身份
    matchTypeBattleRecords      map<ChaseMatchTypeBattleRecord>     2  +nocs  # 按玩法模式id细分的战斗数据
    dailyTaskRewardIds          map<ChaseTaskResult>                3  +nocs  # 每日任务刷新结果
    isCompleteOldPlayerUnlockSecond   bool                          4  +nocs  # 是否第二次解锁身份成功

@ArenaDailyVictoryRecord
    dateKey             int64                       1   +key      #当天0点时间戳
    num                 int32                       2             #胜利次数

@ChaseGameData
    normalDressItem             map<ChaseDressItemInfo>         1       # 星宝装扮
    bossDressItem               map<ChaseDressItemInfo>         2       # 暗星装扮
    identitySpecialization      ChaseIdentitySpecialization     3       # 大王身份专精
    chaseIdentityProficiencyInfo ChaseIdentityProficiencyInfo   4       # 大王身份数量度
    chaseSettlementData         ChaseSettlementData             5       # 大王结算数据
    chaseInteractions           map<Interaction>                6       # 大王语音槽位

@ChaseSettlementData
    seasonScoreHistory      map<ChaseSettlementScore>       1   # 大王赛季分数

@ChaseSettlementScore
    seasonId        int32       1   +key    # 赛季id
    scoreMap        map<KvSL>   2           # 分数<玩法id_身份,分数>
    brandMap        map<KvII>   3           # 牌子<金/银,数量>


@ChaseDressItemInfo
    actorId                  int32                   1   +key    # 角色ID
    dressItemInfos       map<DressItemInfo>          2           # 装扮信息
    actorInteractions    map<Interaction>            3           # 角色交互表情动作槽位

@ChaseIdentitySpecialization  # 大王身份专精
    identityData    map<ChaseIdentitySpecializationData>    1   # 大王身份专精数据
    lastPlayIdentityId        int32                           2   # 最近游玩的身份
    identityQualifyingSeasonInfo    map<IdentityQualifyingSeasonInfo>   3   # 赛季信息
    lastWeeklySettlementTime      int64                                     4   # 每周结算时间

@IdentityQualifyingSeasonInfo
    qualifyType           int32           1   +key    # 段位类型
    curSeasonId           int32           2           # 当前赛季ID
    updateTime            int64           3           # 更新时间

@ChaseIdentitySpecializationData  # 大王身份专精数据
    identityId            int32                       1   +key
    activePoint           int32                       2           # 活跃系数（百分定点数），每次登录根据上次排位赛时间重新计算
    battleScore           int32                       3           # 实际场次分
    perfScore             int32                       4           # 实际表现分
    lastPlayTime          int64                       5   +nocs   # 上次游玩时间（ms）
    activePointUpdateTime int64                       6   +nocs   # 上次活跃系数更新时间（ms）
    badgeId               int32                       7           # 上周结算最高级别的徽章
    historyRank           map<ChaseIdentityRankItem>  9           # 历史最高结算信息，最多3个级别，上榜才记录
    latestRank            map<ChaseIdentityRankItem>  10          # 上周结算信息，最多3个级别，上榜才记录

@ChaseIdentityRankItem
    geoLevel          int32           1   +key    # enum GeoLevel，全服榜单为0
    rankLevel         int32           2           # 级别越高，值越小
    geoCode           int32           3           # 地区编号，全服榜单为0
    seasonId          int64           4           # 赛季ID，每周结算
    position          int32           5           # 名次
    score             int32           6           # 排行榜专精值（百分定点数）：activePoint * (battleScore + perfScore)
    badgeId           int32           7           # 专精徽章

@ChaseIdentityProficiencyInfo
    completeFirstCheckOpenFightPower   int32                             1  +nocs   # 首次解锁专精是否成功
    chaseIdentityProficiencyDatas map<ChaseIdentityProficiencyData>      2  +nocs        # 大王熟练度
    dailyAddProficiency                int32                             3  +nocs   #每日获取的总计熟练度
    refreshTimeMs                      int64                             4  +nocs   #刷新的时间戳

@ChaseIdentityProficiencyData   #大王身份熟练度 30条
    identityId                          int32           1  +key     # 身份ID
    proficiency                         int32           2           # 熟练度
    unlockBiography                     set<int32>      3           # 已解锁的角色小传
    unlockSpecialization                int32           4           # 是否解锁专精
    claimedProgressReward               set<int32>      5           # 已领取过的进度奖励
    lastPlayTime                        int64           6           # 最后游玩时间
    unReadBiography                     set<int32>      7           # 未读的角色小传

@ChaseIdentityBattlePerformanceDatas
    chaseIdentityBattlePerformanceData map<ChaseIdentityBattlePerformanceData> 1  # 身份对局详情

@ChaseIdentityBattlePerformanceData
    identityId                          int32           1  +key     # 身份ID
    chaseIdentityBattlePerformance  map<ChaseIdentityBattlePerformance>   2      # 对局数据记录

@ChaseIdentityBattlePerformance
    type                                int32           1 +key      # 类型
    value                               int64           2           # 值

@ChestIdentityBattlePerformanceDatas
    chestIdentityBattlePerformanceData map<ChestIdentityBattlePerformanceData> 1  # 身份对局详情

@ChestIdentityBattlePerformanceData
    identityId                          int32           1  +key     # 身份ID
    chestIdentityBattlePerformance  map<ChestIdentityBattlePerformance>   2      # 对局数据记录

@ChestIdentityBattlePerformance
    type                                int32           1 +key      # 类型
    value                               int64           2           # 值

@FarmReturningInfo # 农场回流
   enterFarmTime             int64                           1            # 上一次进入农场的时间
   rtnActInfo                map<ReturningActInfo>           2            # 回流活动信息

@ReturningActInfo # 回流活动信息
   activityId               int32                1      +key      # 活动ID
   startTime                int64                2
   endTime                  int64                3

@AppearanceRoad
  lv                       map<AppearanceRoadLv>     1 # 时尚之路等级数据
  gather                   map<AppearanceRoadGather> 2 # 时尚之路主题时装收集数据
  appearanceInfo           map<KvII>                 3 # 图鉴页签数量信息 AppearanceRoadType=>num
  updateTimeMs             int64                     4 # 最后更新时间
  activeSuitBookByType     map<ActiveSuitTypeBook>   5 # 激活的时装图鉴

@ActiveSuitTypeBook
  type                    int32           1 +key      # AppearanceRoadType
  activeSuitBooks         int32[]         2           # 激活的图鉴ID

@AppearanceRoadLv
  lv            int32           1 +key      # 大等级
  awarded       set<int32>      2           # 已领奖的小等级

@AppearanceRoadGather
  type          int32           1 +key      # 类型 1赛季，2主题
  awarded       set<int32>      2           # 已领奖的(赛季为赛季id， 主题为主题id)
  collected     set<int32>      3           # 已集齐的(赛季为赛季id， 主题为主题id)

@ModuleRedDotInfo
    moduleId                  int32                             1    +key         #模块id
    redDotMap                 map<ActivityRedDot>               2                 #点击消失红点

@GeneralRedDotInfo
    moduleType                int32                             1    +key         #模块类型
    moduleRedDotMap           map<ModuleRedDotInfo>             2                 #点击消失红点

@GuidedDiscoverInfo
  id              set<int32>               1     # 已处理的引导发现配置id
  alreadyHttp     bool                     2     # true请求过http或DB
  clickedFound    bool                     3     # true点击过发现

@WolfKillTreasureEquipInfo
   id             int32                                   1 +key
   inUse          bool                                    2   # 某些珍宝，可以配置使用
   rewarded       bool                                    3   # 每日领取，定时清理
   isNew          bool                                    4   # 新的珍宝

@WolfKillTreasureRentInfo
  id          int32                                   1 +key
  rentUid     int64                                   2
  rentTime    int64                                   3

@WolfKillTreasureRentFromInfo
   rentFromUid                    int64                           1 # 谁分享来的，要感谢他
   rentFromTime                   int64                           2 # 分享时间
   retFromFlag                    bool                            3 # 分享标记，首次分享要标

@WolfKillTreasureRentHighVersionItemInfo
  itemId           int32                                   1 +key
  itemNum          int64                                   2
  expiredTime      int64                                   3
  sharePlayerUid   int64                                   4
  rentType         int32                                   5
  relation         int32                                   6

@WolfKillInfo
    treasureEquipInfo              map<WolfKillTreasureEquipInfo>  1 # 珍宝系统使用情况
    initTreasureFlag               bool                            2 # 未初始化过的，第一次统计之前的珍宝值,后续实时增加
    lv                             int32                           3 # 当前等级
    lvLast                         int32                           4 # 之前等级
    treasureNum                    int64                           5 # 当前剩余的
    treasureNumLast                int64                           6 # 之前剩余的
    treasureNumAdd                 int64                           7 # 新增的，可能为0。实际就是treasureNumAllLast和当前开放珍宝最大值（并不一定是当前珍宝值）的差值
    treasureNumAllLast             int64                           8 # 之前的总量，根据lvLast和treasureNumLast可以计算，冗余存储
    treasureNumAll                 int64                           9 # 当前的总量，根据lv和treasureNum可以计算，冗余存储
    rentVocation                   map<WolfKillTreasureRentInfo>   10 # 共享的身份给哪几个玩家
    rentAni                        map<WolfKillTreasureRentInfo>   11 # 共享的动画给哪几个玩家
    rentFromVocation               WolfKillTreasureRentFromInfo    12 # 只记录最后一个，其他分享的不管
    rentFromAni                    WolfKillTreasureRentFromInfo    13 # 只记录最后一个，其他分享的不管
    rentClearTime                  int64                           14 +nocs #每个月清理一次
    hyperCoreScore                 int32                           15
    hyperCoreClearTime             int64                           16 +nocs #每个赛季清理一次
    rentHighVersionItemList         map<WolfKillTreasureRentHighVersionItemInfo>   17 +nocs #某些低版本获得的物品，高版本要加上的物品列表
    brawlLastSelected              int32                           18 +nocs # 大乱斗上局所选项
    lotteryNum                     int32                           19 # 剩余抽奖次数，每天更新为1
    lotteryInitFlag                bool                            20 # 刚上线的时候，要给一次抽奖机会
    lotteryLastBattleTime          int64                           21 # 最后一次对局奖励的抽奖时间


@NR3E8RichInfo # 大富翁信息
    myRichBoardInfo             MyRichBoardInfo                1         # 我的棋盘
    visitRichBoardInfo          VisitRichBoardInfo             2         # 玩家当前所在的棋盘
    taskInfo                    NR3E8TaskInfo                  3         # 任务信息
    cityLevel                   int32                          4         # 城市等级
    enterRichTime               int64                          5         # 上一次进入大富翁的时间
    richItem                    map<KvLL>                      6         # 大富翁道具信息 key:itemId value:itemNum
    diceStep                    int64                          7         # 步数

@NR3E8TaskInfo # 任务信息
   tasks                       map<NR3E8DBTaskInfo>                     1           # 任务进度
   weekActivityInfo            map<NR3E8WeekActivityInfo>               2           # 周活跃度信息
   weekActivity                int32                                    3           # 活跃度值
   isNew                       bool                                     4           # 任务的新标记
   generateTime                int64                                    5           # 任务生成时间
   remainRewardItems           map<KvLL>                                6           # 大富翁未领取奖励道具信息 key:itemId value:itemNum
   mileStoneInfo               NR3E8MileStoneInfo                       7           # 里程碑数据
   completeMileStone           map<NR3E8MileStoneInfo>                  8           # 已完成的列表

@NR3E8MileStoneInfo # 里程碑信息
   id                          int32                                1    +key       # 里程碑id,下一个任务通过配置表里的排序，找到下一个排序的里程碑id
   mileStoneTasks              map<NR3E8DBTaskInfo>                 2               # 任务进度
   rewarded                    bool                                 3               # 是否已经领
   itemId                   int32[]                                 4              #奖励物品
   itemNum                  int64[]                                 5
   createTime               int64                                   10         # 创建时间
   completeTime             int64                                   11         # 完成时间


@NR3E8WeekActivityInfo # 周活跃度信息
   id                   int32                                1    +key       # 活跃度id
   rewarded             bool                                 2               # 是否已经领
   itemId               int32[]                              3
   itemNum              int64[]                              4
   activityDegree       int32                                5


@NR3E8DBTaskInfo # 任务信息
   id                       int32                                        1   +key   # 任务id
   value                    int64                                        2          # 进度值
   rewarded                 bool                                         3          # 是否领奖
   completed                bool                                         4          # 是否已完成
   valueTotal               int64                                        5          # 进度值总量
   poolId                   int32                                        6          # 任务池ID，按照这个排序，总是固定为1,2,3
   itemId                   int32[]                                      7
   itemNum                  int64[]                                      8
   activityDegree           int32                                        9
   createTime               int64                                        10         # 创建时间
   completeTime             int64                                        11         # 完成时间
   valueList                int64[]                                      1002 +nocs # 服务器内部辅助字段
   valueInternal            int64                                        1003 +nocs # 服务器内部辅助字段


@MyRichBoardInfo
    hasBoard                    bool                           1          # 有没有创建棋盘
    createTime                  int64                          2          # 创建时间

@VisitRichBoardInfo
    currentBoardId              int64                          1          # 当前所在棋盘id
    enterTimeMs                 int64                          2          # 进入时间
    tlogInfo                    VisitRichTlogInfo              3   +nocs  # 为了流水存储的信息

@VisitRichTlogInfo
    enterSource                 int32                          1

@UgcActivityInfo
    appreciateScore             int32           1     # 本周的鉴赏家积分
    scoredMapCount              int32           2     # 总共评分过的鉴赏地图数目
    ugcMapSimpleActivity        UgcMapSimpleActivity           3     +nocs      # ugc地图简单进度活动

@ClientLogColoring
    stainId                     int64           1 +key  # 染色Id
    beginTimeMs                 int64           2       # 着色生效时间(unix时间戳, 毫秒)
    endTimeMs                   int64           3       # 着色失效时间(unix时间戳, 毫秒)
    logLevel                    int32           4
    isSetTimer                  bool            5       # 是否设置定时器

@SecondaryPassword
    isOpen                      bool            1         # 是否开启
    password                    string          2 +nocs
    forceCloseTimeMs            int64           3         # 强制关闭时间
    passwordLess                bool            4         # 开启免密
    passwordLessEndTimeMs       int64           5         # 免密结束时间
    token                       string          6 +nocs   # 单次token
    tokenExpireTimeMs           int64           7 +nocs   # 单次token失效时间

@EntertainmentGuide
    task              map<EntertainmentGuideTask>   2         # 任务
    unlockCondition   map<ConditionGroupInfo>       3 +nocs
    unlockMatchType   set<int32>                    4         # 已打卡模式
    stickers          set<int32>                    5         # 已获取贴纸
    isOpen            bool                          6         # 功能是否开放
    reissueStickers   bool                          7 +nocs   # 贴纸道具补发
    registerGuideTime int64                         8         # 用户参与活动盛典时间

@EntertainmentGuideTask
    taskId              int32                               1   +key    # 任务id
    condition           ConditionGroup                      2           # 任务条件
    status              com.tencent.wea.xlsRes.TaskStatus   3           # 任务状态

@MatchTypeHistory
    historyPlay             set<int32>      1           # 历史所有玩过的玩法id
    newRecommend            bool            2           # 新推荐
    curRecommendId          int32           3  +nocs    # 当前推荐id

@RecommendMatchType
    recommendId         int32                               1
    expireTimeMs        int64                               2
    tasks               map<RecommendMatchTypeTask>         3
    status              com.tencent.wea.xlsRes.TaskStatus   4           # 大礼包状态
    alreadySettled      bool                                5   +nocs   # 已过期结算
    conditionGroup      ConditionGroup                      6   +nocs   # 条件组
    refreshInfo         RecommendRefreshInfo                7           # 刷新信息

@RecommendMatchTypeTask
    taskId              int32                               1   +key    # 任务id
    condition           ConditionGroup                      2           # 任务条件
    status              com.tencent.wea.xlsRes.TaskStatus   3           # 任务状态

@RecommendRefreshInfo
    refreshedCount      int32                               1           # 刷新次数
    completedCount      int32                               2           # 任务完成次数
    refreshTimeMs       int64                               3           # 下次刷新时间

@MatchUnlockInfo
   matchTypeId             int32          1   +key  # 玩法ID
   unlockType              set<int32>     2         # 解锁类型 MatchUnlockType

@PicLikeCountInfo
    picKey                 string      1   +key
    likeCount              int32       2            #点赞数量
    isLike                 bool        3            #是否点赞了

@AlbumPicNewLikeHis
    picKey                 string      1
    uid                    int64       2        #点赞者
    time                   int64       3        #点赞时间(毫秒)

@PicLikeInfo
    uid                    int64       1   +key
    time                   int64       2            #点赞时间(毫秒)

@AlbumPicLikeHisInfo
    picKey                  string        1      +key
    picLikeHis              map<PicLikeInfo>  2          # 单个照片点赞历史
    likeCount               int32         3      +nocs   # 点赞数量

@AlbumLikeHisInfo
    AlbumPicLikeHis         map<AlbumPicLikeHisInfo> 1   # 相册照片点赞历史信息
    dataVersion             int64                    2   # 数据版本号, 数据变更即加一

@AlbumLimitConditionInfo
    id              int32           1   +key    # 相册上限表id
    curValue        int32           2           # 当前值

@AlbumLimitInfo
    albumLimitConditionMap  map<AlbumLimitConditionInfo>    1           # 相册上限条件信息
    totalUpperLimit         int32                           2           # 相片数量上限

@AlbumPicTargetAtInfo
    picKey      string          1   +key
    uid         int64           2         # at相片来源uid
    atTime      int32           3         # 被at时间

@AlbumPicAtTargetInfo
    uid         int64           1    +key       # 目标uid
    atTime      int32           2               # at时间

@AlbumPicExtInfo
    picKey                  string        1   +key
    atTargetUidSet          map<AlbumPicAtTargetInfo>    2             # 已at目标

@AlbumExtInfo
    albumPicExtInfo         map<AlbumPicExtInfo>            1   # 相片额外信息
    albumLimitInfo          AlbumLimitInfo                  2   # 相册上限信息
    picTargetAtInfo         map<AlbumPicTargetAtInfo>       3   # 被人at信息

@AlbumPicInfo
    picKey                  string        1   +key
    bucket                  string        2
    cosPath                 string        3
    sourceScene             int32         4         # 来源场景
    createTimeSec           int64         5         # 创建时间
    isChecking              bool          6         # 审核中
    thumbnailCosPath        string        7         # 缩略图cos路径
    picLike                 map<PicLikeInfo>  8  +nocs   # 照片点赞信息(废弃)
    isLike                  bool          9      +nodb   # true赞过了(废弃)
    likeCount               int32         10     +nodb   # 点赞数量(废弃)
    sceneTag                set<int32>    11             # 支持多个标签-场景标签
    isHide					bool 		  12             # 是否隐藏,true隐藏
    topTime                 int32         13             # 置顶时间,0未置顶
    sceneTagStr             string        14             # 场景标签
    labelType               com.tencent.wea.xlsRes.AlbumPicLabelType 15 # 照片分类标签

@AlbumWallPicLike
    wallPicKey              string              1   +key
    likeTimeMs              int64               2

@AlbumPicWallInfo
    myPicList               map<AlbumPicInfo>         1       # 我上传的照片
    likeHistory             map<AlbumWallPicLike>     2       # 点赞过的照片
    likeCount               int32                     3       # 点赞次数

@AlbumInfo
    albumPicMap             map<AlbumPicInfo>     1 # 照片信息
    isDataMigrate			bool                  2 # 数据是否迁移
    dataMigrateTimeSec      int64                 3 # 数据迁移时间
    dataVersion             int64                 4 # 数据版本号, 数据变更即加一
    albumPicWallInfo        AlbumPicWallInfo      5 +nocs # 照片墙信息

@UgcGrowUpInfo
    weeklyActivityDegree            int64                       1           # 星世界每周活跃度
    lastRefreshTime                 int64                       2           # 周活跃重置时间
    ugcBadgeInfoMap                 map<UgcBadgeInfo>           3           # 徽章
    reissueBatchId                  int32                       4
    communityMaxLikeScore           int32                       5           # 种草社区历史最高种草分

@UgcBadgeInfo
    id                          int32               1  +key
    addTime                     int64               2       # 获取时间
    redDot                      bool                3       # 是否显示红点

@GameTvInfo
    hasReward               bool            1       # 是否有奖励
    rewardExpireTime        int64           2       # 奖励过期时间


@XlsWhiteList
    moduleId                    string                          1 +key      # 开白模块
    status                      int32                           2           # 0:关闭. 1:开启.
@UserActivityAttr   +listenable,+root #玩家活动数据
    lastMidnightRefreshTimeMs   int64            1    +nocs   # 上次凌晨刷新时间
    lastWeekRefreshTimeMs       int64            2    +nocs   # 上周更新时间
    fiveClockRefreshTimeMs      int64            3    +nocs   # 上次早上5点更新时间
    activity                    Activity         4            # 活动数据
    task                        TaskInfo         5            # 活动任务数据
    rewardCompensate            RewardCompensateInfo    6    +nocs    # 奖励补领信息
    raffle                      ActivityRaffle   7            # 祈愿信息

@UgcSlotInfo
    slotCount                   int32                            1       # 数量
    slotInfo                    map<UgcSlotKeyInfo>              2       # key ->slot

@UgcSlotKeyInfo
    slotKey                     int32              1  +key
    info                        string             2       # 快捷键内容

@PlayerGrayRuleData
    grayRuleConfId          int32                   1 +key        # 灰度规则表配置Id
    tagTime                 int64                   2             # 打标签时间
    tagSucc                 bool                    3             # 打tag成功状态，失败也会记录

@PlayerGrayTagsInfo
    infos                   map<PlayerGrayRuleData>  1            #玩家标签列表

@UgcBuyGoodsInfo
   failOrder        map<UgcBuyGoodsDeliverFailOrder>   1  +nocs # 发货失败订单


@UgcBuyGoodsDeliverFailOrder
   billNo                     string            1  +key
   goodsInfo       map<UgcDeliverGoodsInfo>            2       # 商品信息
   addTime                    int64             3
   retryNum                   int32             4       # 重试次数
   mapId                      int64            5       # ugc地图id
   reason        com.tencent.wea.xlsRes.ItemChangeReason  6
   busBillNo                     string           7
   checkCode                   string           8    # 用于校验是否作弊的校验码
   costDiamonds             int64            9       # 消费的星钻数量

@UgcDeliverGoodsInfo
    goodsId                     string            1  +key
    num                         int32             2       # 数量
    farmInfo                    FarmInfo                       105          # 农场信息

@UgcCollection
    collected                   set<string>                    1            # 收藏列表
    createIndex                 int32                          2            # 创建idx
    created                     set<string>                    3            # 创建列表
    collectedDigest             map<UgcCollectionDigest>       4            # 收藏列表摘要
    createdDigest               map<UgcCollectionDigest>       5            # 创建列表摘要

@UgcCollectionDigest
    collectionId                string            1 +key
    name                        string            2
    mapCount                    int32             3
    createMs                    int64             4
    updateMs                    int64             5
    collectedTimes              int64             6
    playPv                      int64             7
    playUv                      int64             8

@CommonConditionGroup
   contionGroupType            com.tencent.wea.xlsRes.ContionGroupType  1   +key
   attrConditionGroup          map<ConditionGroupInfo>          2

@ConditionGroupInfo
   id                          int32                           1    +key
   conditionGroup              ConditionGroup                  2

@UgcAccountInfo
  draftCap       int32           1        # 草稿箱数量上限
  publishedCap   int32           2        # 已发布数量上限
  fansCount      int32           3  +nocs # 粉丝数量
  subCount       int32           4  +nocs # 订阅数量
  publishGoodsProtoVersion   int32           5   +nocs     # UGC星钻上架签署协议版本号


@DsUserDBInfo
    matchType                   int32                          1    +key    # 数据隔离的玩法模式id
    dsUserDBInfoUnion           DsUserDBInfoUnion              2            # DS中玩家需要存储的单玩法信息

@DsUserDBInfoUnion +oneof
    tycoonUserDBInfo            TycoonUserDBInfo               1            # 塔防玩法的玩家存储信息
    dfUserDBInfo                DfUserDBInfo                   2            # 撤离玩法的玩家存储信息
    omdUserDBInfo               bytes                          3            # 兽人塔防玩法的玩家存储信息
    fpsWeaponUnLockUserDBInfo   FpsWeaponUnLockUserDBInfo      4            # fps武器解锁的玩家存储信息 (统一使用冲锋竞技的matchType 29)
    roguelikeUserDBInfo         RoguelikeUserDBInfo            5            # 肉玩法的玩家存储信息
    maydayUserDBInfo            MaydayUserDBInfo               6            # MayDay玩家存储信息

@TycoonUserDBInfo
    tycoonUserStatInfo          TycoonUserStatInfo             1            # 塔防玩法玩家统计信息
    tycoonUserCurrencyInfo      TycoonUserCurrencyInfo         2            # 塔防玩法玩家货币信息
    tycoonUserBuildingInfo      TycoonUserBuildingInfo         3            # 塔防玩家建筑信息
    tycoonUserBasicInfo         TycoonUserBasicInfo            4            # 塔防玩家基础信息
    tycoonUserBackpackInfo      DSUserBackpackInfo             5            # 塔防玩家背包信息
    tycoonUserMonsterInfo       TycoonUserMonsterInfo          6            # 塔防玩家野怪信息

@TycoonUserStatInfo
    tycoonUserReincarnationNum  int32                          1            # 塔防玩法玩家转生次数
    tycoonUserKillUserNum       int32                          2            # 塔防玩法玩家击杀玩家数
    tycoonUserKillMonsterNum    int32                          3            # 塔防玩法玩家击杀野怪数
    tycoonUserCollectNum        int32                          4            # 塔防玩法玩家收藏值
    tycoonUserMaxBuildingPercentInReincarnation        int32                  5            # 塔防玩法玩家一次转生最大建筑百分比
    tycoonUserMaxWaveNumInReincarnation                int32                  6            # 塔防玩法玩家一次转生
    tycoonUserMaxKillMonsterNumInReincarnation         int32                  7            # 塔防玩法玩家一次转生
    tycoonUserCurBuildingPercentInReincarnation        int32                  8            # 塔防玩法玩家当前转生建筑百分比
    tycoonUserCurWaveNumInReincarnation                int32                  9            # 塔防玩法玩家当前转生防守波次数
    tycoonUserCurKillMonsterNumInReincarnation         int32                  10           # 塔防玩法玩家当前击杀怪物数量

@TycoonUserCurrencyInfo
    tycoonUserCurCurrency       int64                          1            # 塔防玩法玩家身上的货币数量
    tycoonUserBankCurrency      int64                          2            # 塔防玩法银行待领取的货币数量
    tycoonUserCurCrystals       int64                          3            # 塔防玩法玩家身上的水晶数量

@TycoonUserBuildingInfo
    tycoonBuildingTemplateId    int32                          1            # 塔防玩法建筑模板Id（模板决定了有哪些建筑可以解锁，以及每个建筑配置的前后依赖，该配置可以理解为是树形结构）
    tycoonBuildingMap           map<TycoonBuildingItem>        2            # 塔防玩法已经拥有的建筑，考虑到之后功能的扩展没有用Set只存配置ID

@TycoonBuildingItem
    tycoonBuildingConfId        int32                          1    +key    # 塔防玩法建筑配置id
    tycoonBuildingConfVersion   int64                          2            # 塔防玩法建筑配置的版本号
    tycoonBuildingLevel         int32                          3            # 塔防玩法建筑等级
    tycoonBuildingItemConfId    int32                          4            # 塔防玩法建筑配置itemId

@TycoonUserBasicInfo
    tycoonNormalLevel           int32                          1            # 塔防玩法玩家的普通等级
    tycoonPeakLevel             int32                          2            # 塔防玩法玩家的巅峰等级
    tycoonLastLoginUnixSecs     int64                          3            # 塔防玩法玩家上次登录玩法的unix秒数
    tycoonLastLogoutUnixSecs    int64                          4            # 塔防玩法玩家上次登出玩法的unix秒数
    tycoonRegisterUnixSecs      int64                          5            # 塔防玩法玩家玩法账号第一次登录的unix秒数

@TycoonUserMonsterInfo
    waveRebirthTimes                 int32                          1            # 最新的野怪刷新转生次数
    rebirthMonsterIdx                int32                          2            # 野怪转生波次序列
    rebirthMonsterPrice              int32                          3            # 野怪转生波次价值
    rebirthMonsterReward             bool                           4            # 野怪转生波次是否领取了奖励
    rebirthBossIdx                   int32                          5            # Boss转生波次序列
    rebirthBossReward                bool                           6            # boss转生波次是否领取了奖励
    waveSize                         int32                          7            # 经历的波次总数
    guildSchedule                    int32                          8            # 波次引导进度
    waveStatus4Quit                  int32                          9            # 玩家退出时波次状态
    remainSecs4Quit                  int32                          10           # 玩家退出时波次剩余时间
    curBuildingHp                    int32                          11           # 玩家当前建筑血量
    smallWaveId                      int32                          12           # 波次id
    hasWaveReward                    bool                           13           # 是否有波次奖励
    param1                           int32                          14           # 预留字段

@DfUserDBInfo
    storeResetTimestamp              int64                   1            # 重置时间戳
    storeFreeTimes                   map<DfStoreFreeTimes>   2            # 商店免费次数

@DfStoreFreeTimes
    packageId                        int32                   1  +key      # 套装ID
    freeCount                        int32                   2            # 免费次数

@FpsWeaponUnLockUserDBInfo
    weaponUnLockInfo                 map<FpsWeaponUnLockInfo>   1         # 武器解锁信息列表，每个解锁条件对应一条信息

@FpsWeaponUnLockInfo
    weaponUnlockConditionId   			int32                          1    +key    # 武器解锁条件表的条件Id
    weaponUnlockConditionNum        	int32                          2            # 此条件的当前完成数量值
    weaponUnlockConditionSuccessNum     int32                          3            # 此条件的当前成功的次数

@RoguelikeUserDBInfo
    qqMusicCustomSongs                 map<RoguelikeCustomQQMusicInfo>   	1         # 用户自定义QQ音乐
    qqMusicDefaultSongBlackList        map<RoguelikeCustomQQMusicInfo>   	2         # 用户默认QQ音乐黑名单

@RoguelikeCustomQQMusicInfo
    musicId   			int32                          1    +key    # 引用索引Id
    songId        		string                         2            # 歌曲id

@MaydayUserInfIdentityInfo
  identityID      int32                          1 +key   # 职业ID
  level           int32                          2        # 职业等级

@MaydayUserInfPosInfo
  pos      int32                          1 +key          # 位置
  IdentityInfo     map<MaydayUserInfIdentityInfo>  2      # 职业信息
  bookCount            int32                       3      # 技能书数量

@MaydayUserInfRoomInfo
    level  int32                                   1      # 层数
    groupMoney  int32                              2      # 团队资金
    techPoint   int32                              3      # 已购买技能书数量
    posInfo  map<MaydayUserInfPosInfo>             4      # 位置信息
    levelSaveCount   int32                         5      # 当层保存次数

@MaydayUserDBInfo
    WinDifficultys  int32[]                        1        # 用户通过合作模式的难度列表
    monsterPictures int32[]                        2        # 怪物图鉴
    infInfo               MaydayUserInfRoomInfo    3        # 无尽挑战房间信息
    infMaxLevel           int32                    4        # 无尽挑战个人最大层数信息

@PlayerPublicBattleInfo
    matchTypeId               int32                          1            # 玩法id
    battleId                  int64                          2            # battleid
    battleSvrId               int32                          3            # 战场节点id

@DSUserBackpackInfo
    dsOwnerBackpackMap          map<DSOwnerBackpackInfo>       1            # DS物品背包，分不同拥有者，拥有者定义见DSOwnerBackpackInfo的backpackOwnerId字段

@DSItem
    dsItemConfId                int32                          1    +key    # DS物品配置id
    dsItemNum                   int64                          2            # DS物品数量

@DSOwnerBackpackInfo
    backpackOwnerId           int64                          1    +key    # 在自己的玩法定义枚举或常量进行维护（表示绑定皮肤的应用主体可以玩家，可以是某个英雄，某把枪，某种炮台，某个建筑），也可以直接绑定EntityId（如果有的话）
    curItemId                 int32                          2            # DS物品拥有者的当前物品id（可以没有）
    itemMap                   map<DSItem>                    3            # DS物品拥有者当前拥有的所有物品

@UgcOpInfo
  lastOpenFansTime                      int64           1   +nocs # 上次打开粉丝列表的时间
  lastOpenSubsTime                      int64           2   +nocs # 上次打开订阅列表的时间
  openFansTime                          int64           3   +nocs # 打开粉丝列表的时间
  openSubsTime                          int64           4   +nocs # 打开订阅列表的时间
  nextDailyReportTimeMs                 int64           5   +nocs # 下次推送日报红点的时间
  lastGetSubPlayerMapsTs                int64           6   +nocs # 上次打开我的订阅的时间
  getSubPlayerMapsTs                    int64           7   +nocs # 打开我的订阅的时间
  lastGetCollectStarActivityMapsTs      int64           8   +nocs # 上次获取跑图集星地图时间
  nextHotMapsReportTimeMs               int64           9   +nocs # 下次爆款推荐时间
  lastRecommendedMaps                   set<int64>      10  +nocs # 上次推送的爆款-入选推荐
  lastHotPlayMaps                       set<int64>      11  +nocs # 上次推送的爆款-热玩
  lastHomePageTabId                     int32           12        # 上次请求的首页推荐标签
  lastOpenStartWorldTimeMs              int64           13  +nocs # 上次打开星世界的时间
  homeRecommendHotTags                  int32[]         14  +nocs # 首页推荐设置的爱玩标签
  lastSendUgcBpCoinTipsTs               int64           15  +nocs # 上次发送星世界BP漫游币使用提醒邮件的时间

@ItemPackageLimit
   itemId                 int32                                1 +key
   limitInfo              map<LimitInfoStruct>                 2            # packageItemId => count
   guaranteeLimitInfo     map<LimitInfoStruct>                 3            # guaranteeKeyIndex => count
   totalDrawTimes         int32                                4            # 礼包总抽取次数

@LabelInfo
  id                      int32                                 1   +key      # 标签id
  conditionGroup          ConditionGroup                        2   +nocs     # 条件组
  status                  int32                                 3             # 解锁状态


@MailCache  # 在批量操作时辅助加速, 分散压力
    delMailIds                  set<int64>                     1            # 已删除的邮件id
    gotMailIds                  set<int64>                     2            # 已领取附件的邮件id
    globalMailIdsIdip           map<KvLL>                      3            # 已接收的全服邮件id(idip)
    globalMailIdsXls            map<KvLL>                      4            # 已接收的全服邮件id(xls)
    lastRecvGlobalMailMs        int64                          5            # 上次收取全服邮件的时间
    purchasedMailIds            set<int64>                     6            # 已购买的邮件id

@MonthCardInfo
    id                          string                        1     +key
    expirationTime              int64                         2             # 过期时间
    lastGetDailyItem            int64                         3             # 最近领取每日道具时间
    lastRecordRewardMs          int64                         4             # 最近记录奖励的时间
    absentDays                  int32                         5             # 过期前未领取的天数
    beginTimeMs                 int64                         6             # 当前月卡周期的开始时间
    expiredRemindMallTimeMs     int64                         7     +nocs   # 发送月卡过期提醒的时间
    expiredRedDot               bool                          8             # 过期红点
    expiredRedDotTimeMs         int64                         9     +nocs   # 月卡过期红点更新时间
    cumDays                     int32                         10            # 累计开通天数


@UgcTestPlayerInfo
    uid                         int64                          1    +key    # 玩家Uid
    lastTestTime                int64                          2            # 上次测试的时间

@UgcDanMu
    danMuAble                   bool                           1            # 是否打开弹幕
    danMuArea                   int32                          3            # 弹幕区域
    danMuAlpha                  int32                          4            # 弹幕透明度
    danMuFont                   int32                          5            # 弹幕字号
    danMuSpeed                  int32                          6            # 弹幕速度

@UgcMapSetInfo
    musicSize                   int32                          1            # 音乐
    soundSize                   int32                          2            # 音响
    editAngle                   int32                          3            # 视角
    netDisplay                  int32                          4            # 网络视角
    operateAid                  int32                          5            # 操作辅助
    bucket                      string                         6            # bucket
    publishId                   int64                          7            # 发布id(弃用了)
    operateMap                  map<UgcOperate>                8            # 操作 点赞 发布 (弃用了)
    copyId                      int64                          9            # copyId (弃用了)
    objectInfo                  set<int64>                     10   +nocs   # 物件信息上限100个
    composeObjectInfo           set<int64>                     11   +nocs   # 组合物件信息上限50个
    uniqueId                    int64                          12           # uniqueId(弃用了)
    groupFreeObjectId           set<int64>                     13   +nocs   # 空闲group对象50个
    groupObjectInfo             map<UgcGroup>                  14   +nocs   # 组合物件信息上限50个
    idMap                       map<UgcIdMap>                  15   +nocs   # 不同地图id 集合
    ugcSave                     map<UgcSave>                   16   +nocs   # 保存功能，数量受限，不能无限扩大
    agreementStatus             string                         17   +nocs   # 开发者协议状态
    saveIdMap                   set<int64>                     18   +nocs   # 保存地图id
    publishIdMap                set<int64>                     19   +nocs   # 发布地图id
    homeId                      int64                          20           # 小窝id
    editHomeId                  int64                          21           # 编辑小窝id
    aigcBucket                  string                         22  +nocs    # aigcBucket(废弃)
    mapKey                      string                         23  +nocs    # 地图密钥
    singleStageInfo             SingleStageInfo                24  +nocs    # 单人游玩
    openAiFlag                  int32                          25           # 开启ai白名单标志字段 0 是关闭 1是开启
    commonBucket                  string                       26  +nocs    # 通用业务cos桶
    openAiImageFlag             int32                          27           # 开启ai参考图白名单标志字段 0 是关闭 1是开启
    aigcCountLimit              AigcCountLimit                 28  +nocs     # aigc次数统计
    isSyncAllocInfo             bool                           29  +nocs    # 同步大区标志，大区返回后设置，如果未同步下次登录进行一致性同步
    openAiAnicapFlag            int32                          30           # 开启ai视频动捕白名单标志字段 0 是关闭 1是开启
    openAiVoiceFlag             int32                          31           # 开启ai语音生成白名单标志字段 0 是关闭 1是开启
    openAiAnswerFlag            int32                          32           # 开启智能npc对话白名单标志字段 0 是关闭 1是开启
    canUpdatePublish            bool                           33           # 开启二次发布更新  false:关闭 true:开启
    ugcTestPlayers              map<UgcTestPlayerInfo>         34  +nocs    # 一起测试UGC地图的历史玩家列表
    ugcTestPlayerCount          int32                          35           # 一起测试UGC地图的历史玩家数量
    openVisualProgramFlag       int32                          36           # 开启可视化编程白名单标志字段 0 是关闭 1是开启
    createChatGroupFlag         int32                          37           # 创作者一键拉群功能白名单 0 是关闭 1是开启
    openDialogueImageFlag       int32                          38           # 剧情编辑插入图片白名单标志字段 0 是关闭 1是开启
    openSkillEditorFlag         int32                          39           # 技能编辑器白名单标志字段 0 是关闭 1是开启
    logFlag                     int32                          40           # 客户端日志上传标志 0 是关闭 1是开启
    ugcDanMu                    UgcDanMu                       41           # 玩家弹幕设置
    openCustomSkeletonAnimFlag  int32                          42           # 自定义骨骼动画白名单标志字段 0 是关闭 1是开启
    platWhiteSwitch             map<WhiteInfo>                 43           # 平台白名单开关

@WhiteInfo
    moduleType                  int32                         1  +key # 模块类型
    enable                      int32                         2       # 开启标志 0 关闭  1开启
    beginTime                   int32                         3       # 开始时间 秒时间戳
    endTime                     int32                         4       # 结束时间 秒时间戳


@AigcCountLimit
    lastRefreshDailyCountTime int64                           1       # 刷新时间
    todayGenImageCount        int32                           2       # 当天使用生成ai图片功能次数
    todayGenVoiceCount        int32                           3       # 当天使用生成语音功能次数
    todayGenAnicapCount       int32                           4       # 当天使用生成视频动捕功能次数

@SingleStageInfo
    battleId                  int64                           1           # 单人游玩battleId
    startTime                 int64                           2           # 开始时间
    mapId                     int64                           3           # 地图ID
    isSettlement              bool                            4           # 是否已结算
    isBestRecord              bool                            5           # 是否最佳记录
    hasStarted                bool                            6           # 是否已经触发开始事件
    mapSource                 int32                           7           # 客户端传递的gameSource
    blueTopicId               set<int32>                      8
    goldTopicId               set<int32>                      9
    singleType                int32                           10          # 游玩类型:SingleStageType
    fromCollectionId          string                          11          # 从哪个合集进入的
    blueTopicTxt              set<string>                     12
    goldTopicTxt              set<string>                     13
    logicMapSource            int32                           14           # 客户端传递的logicMapSource

@UgcSave
    keyName                     string                         1    +key    # key 长度不能超过一定值
    value                       string                         2            # value 长度不能超过一定值

@UgcIdMap
    idType                      int32                          1    +key    # id类型
    id                          int64                          2            # id

@UgcGroup
    uid                         int64                          1    +key    # uid
    name                        string                         2            # 名字
    md5                         string                         3            # md5
    size                        int32                          4            # size

@UgcOperate
    opType                      int32                          1    +key    # 类型 common.proto 里面UgcOpType
    ugcSet                      set<int64>                     2            # 地图列表
    onceUgcSet                  set<int64>                     3            # 曾经点赞过的地图列表

@RechargeInfo
    vipRewardReceivedLvSet      set<int32>                     3            # vip充值奖励当前已领取等级
    firstChargeNtfTimes         int32                          4            # 首充通知次数

@SeasonInfo
    seasonId                    int32                          1            # 玩家存储的赛季Id(不一定是服务器当前的赛季)
    dresses                     set<int32>                     2            # 当前赛季获得的所有时装和交互类道具的itemId

@ClientCache
   id                           int32                          1   +key     # key
   val                          string                         2
   expireTime                   int64                          3            # 过期时间 0不过期

@RewardNtfInfo
    id                      int32                1    +key   # 奖励id
    rewards                 map<RewardItemInfo>  2           # 奖励内容
    cnt                     int32                3           # 次数

@IdipTask
    id                      int32   1    +key   # 条件id
    completeCount           int32   2           # 完成次数

@IdipTaskInfo
    curTime         int64                   1           # 最近登录时间
    task            map<IdipTask>           2           # 购买次数
    battle          map<IdipStatistics>     3           # 战斗统计
    login           map<IdipStatistics>     4           # 登录统计
    charge          map<IdipStatistics>     5           # 充值统计


@CommonGiftBuyInfo
    id              int64   1    +key   # gift id
    buyCount        int64   2           # 购买次数

@BenefitCardInfo
    enable            bool   1     # 是否解锁
    beginTime         int64  2     # 开始时间
    expireTime        int64  3     # 结束时间

@SnsShareReward
    sceneType             int32                     1 +key    #分享场景id
    lastRewardTime        int64                     2         #上次领奖时间

@SpecReward
    type                  int32                     1 +key    #奖励类型
    status                int32                     2         #领奖状态
    itemInfo              map<RewardItemInfo>       3 +nodb   #奖励详情

@SpecRewardInfo
    info              map<SpecReward>                    1         # 特殊奖励信息
    snsShareInfo      map<SnsShareReward>                2         # 分享奖励信息


@LastDressOutLookInfo
   baseDressOutLook         int32       1 +key #基础装扮角色
   lastDressOutLook         int32       2      #最近穿戴的皮肤

@CollectionInfos # 图鉴
   collectionType           com.tencent.wea.xlsRes.CollectionType  1  +key   # 类型
   collections              map<CollectionInfo>                    2         # 集合

@CollectionInfo #
   collectionId             int32                          1  +key      # Id
   expireTimeMs             int64                          2            # 过期时间
   getTimeMs                int64                          3            # 获取时间

@RecommendFriendInfo
   recommendFriend           set<int64>                                 9   +nodb   # 推荐好友UID
   isUnRecommendFriend       bool                                       10  +nodb   # 是否推荐好友
   removeRecommendFriend     set<int64>                                 11  +nocs   # 已删除推荐好友UID

@RelationMapInfo
   relationInfo             map<RelationInfo>                           1                # 关系列表
   platFriendUid            int64[]                                     2     +nocs      # 平台好友的uid列表备份,登陆拉取失败时使用备份  del
   inviteeInfos             map<InviteeInfo>                            3     +nocs      # 邀请好友注册的信息
   sendGoldCoinUid          int64[]                                     4                # 今天已送金币好友
   dailyIntimacy            map<DailyIntimacyData>                      7     +nocs      # 每日亲密度
   recentIntimacy           map<RecentIntimacyData>                     8     +nodb      # 最近n天亲密度
   intimateNoticeAttr       IntimateRelationOnlineNoticeAttr            9                # 亲密好友上线提示信息
   intimateGuideAttr        IntimateRelationGuideAttr                   10    +nocs      # 亲密关系引导数据
   allFriendInteractAttr    AllFriendInteractAttr                       11    +nocs      # 好友交互数据统计
   allIntimateRelationAttr  AllIntimateRelationAttr                     12               # 所有亲密关系共用属性
   luckyFriendAttr          LuckyFriendAttr                             13               # 幸运好友数据
   newFollowerCount         int32                                       14               # 新增粉丝数
   recallInviteeInfos       map<InviteeInfo>                            15     +nocs     # 邀请好友回流的信息

@DailyIntimacyData
   uid                        int64                                      1        +key
   todayIntimacy              int64                                      2
   lastIncreaseTime           int64                                      3
   todayMsgNum                int32                                      4               # 每日发送消息数量
   realTodayIntimacy          int64                                      5      # 今日所有亲密度增加，0点自动清零
   realYesterdayIntimacy      int64                                      6      # 昨日所有亲密度增加，0点自动设为realTodayIntimacy
   realIntimacyUpdateTime     int64                                      7      # realTodayIntimacy和realYesterdayIntimacy的更新时间

@RecentIntimacyData
   uid                        int64                                      1        +key
   recentIntimacy             int64[]                                    2      # 从今天开始往前N天的每日亲密度变化

@InviteeInfo
   inviteeUid               int64                                      1        +key
   registerTimeMs           int64                                      2
   deviceId                 string                                     3
   activityType             int32                                      4
   activityId               int32                                      5
   isRecall                 bool                                       6

@RelationInfo
   relationType              com.tencent.wea.xlsRes.RelationTypeEnum    1  +key     # 关系类型
   modRelationInfo           map<DBRelation>                            4  +nodb    # 关系列表
   receiveApplyInfo          map<DBApplyRelation>                       7           # 收到的申请列表
   sendApplyInfo             map<DBApplyRelation>                       8           # 发送的申请列表
   removeInfo                map<DBRemoveInfo>                          9  +nocs    # 删除的列表

@DBRemoveInfo
   uid                   int64                                 1  +key  # 删除的玩家uid
   removeTime            int64                                 2        # 删除时间

@DBApplyRelation
   uid                   int64                                       1   +key
   applyTime             int64                                       2           # 申请时间
   hotData               DBPlayerHotData                             3   +nodb   # 玩家热数据
   reason                int32                                       4   +nocs   # 主要来源
   subReason             int32                                       5   +nocs   # 次要来源
   applyType             com.tencent.wea.xlsRes.RelationApplyType    6           # 申请类型
   intimateId            int32                                       8           # 亲密关系类型
   addMotivation         int32                                       9           # 添加原因, -1代表无效值, 不需要展示
   motivationParams      string                                     10           # 部分添加原因需要额外参数, 可能为空
   algoInfo              AlgoInfo                                   11   +nocs   # 推荐添加好友透传信息
   recommendReason       int32                                      12           # 当添加原因为来自好友推荐时, 对应的推荐原因
   recommendReasonText   string                                     13           # 推荐原因文本

@DBRelation
   uid                   int64                                       1   +key
   addTime               int64                                       2           # 添加时间
   hotData               DBPlayerHotData                             3   +nodb   # 玩家热数据
   reason                int32                                       4   +nocs   # 主要来源
   subReason             int32                                       5   +nocs   # 次要来源
   intimateId            int32                                       7           # 亲密关系类型
   nameData              RelationPlayerNameData                      8   +nodb   # 玩家名称信息
   openId                string                                      9   +nocs   # openid
   returning             bool                                        10  +nocs   # 回归用户标志
   returnExpiredSec      int64                                       11  +nocs   # 回归用户过期时间
   followMallWishList    bool                                        12          # 是否关注该好友的商城心愿单
   mallWishListReadTs    int64                                       13          # 商城心愿单已读时间戳(毫秒)
   birthdayMonthDay      int32                                       14          # 生日月*100+生日日
   birthdayVisibleRange com.tencent.wea.xlsRes.BirthdayVisibleRange  15          # 生日可见范围(同步到好友)
   hasNewIntimate        bool                                        16          # 新增亲密关系红点

@DBPlayerHotData
   playerStatus          com.tencent.wea.xlsRes.PlayerStateType         1     # 玩家在线状态
   lastLogoutTime        int64                                          4     # 上次离线时间
   intimacy              int64                                          5     # 好友亲密度(仅用于展示，数据处理使用db)
   svrId                 int32                                          6     # 好友svrId
   hidePlayerStatus      bool                                           7     # 隐藏玩家状态
   togetherBattleCount   int32                                          8     # 共同开黑次数
   recentInteractTs      int64                                          9     # 最近互动时间(私聊、组队、赠送金币、礼物、双人动作)
   creatorId             int64                                          10    # ugc creatorId
   hideProfileToFriend   bool                                           11    # 是否对好友隐藏个人信息 ⇌ 跟游戏界面上的"对好友展示信息"相反

@RelationPlayerNameData
   name                  string                                         1     # 玩家昵称
   friendNickname        string                                         2     # 平台备注名称
   remarkName            string                                         3     # 游戏内备注名称

@IntimateRelationOnlineNoticeAttr
  lastNoticeTime        int64                                           1 # 所有亲密好友最近的提示时间
  noticeDetail          map<IntimateRelationOnlineNoticeDetailAttr>     2 # 单个亲密好友的最近提示时间
  noticeDressItem       map<IntimateRelationOnlineNoticeDressItemAttr>       3 # 亲密关系上线提示装扮皮肤

@IntimateRelationOnlineNoticeDressItemAttr
  itemType              com.tencent.wea.xlsRes.ItemType                      1 +key
  singleDressItem       map<IntimateRelationOnlineNoticeSingleDressItem>     2 # 单个亲密好友的上线提示装扮
  allDressItem          int32                                                3 # 装扮道具ID，应用于所有亲密好友

@IntimateRelationOnlineNoticeSingleDressItem
  uid                   int64             1 +key      # 好友UID
  itemId                int32             2           # 皮肤道具ID

@IntimateRelationOnlineNoticeDetailAttr
  uid                   int64             1 +key      # 好友UID
  lastNoticeTime        int64             2           # 上线提示时间
  rejectNoticeTime      int64             3           # 拒绝提示时间

@IntimateRelationGuideAttr
  guideDetail           map<IntimateRelationGuideDetail>  1

@IntimateRelationGuideDetail
  uid                   int64             1 +key      # 好友UID
  nextTriggerTimeMs     int64             2           # 下次触发时间

@FriendInteractDetailAttr # 好友交互数据详情
  type              int32         1 +key
  interactCount     int32         2

@FriendInteractAttr # 好友交互数据统计
  uid               int64                             1 +key
  interactDetail    map<FriendInteractDetailAttr>     2

@AllFriendInteractAttr # 好友交互数据
  dailyInteractRefreshTimeMs        int64                      1 +nocs   # 每日交互数据刷新时间
  dailyInteractAttr                 map<FriendInteractAttr>    2 +nocs   # 每日加亲密度限制数据，type参考FriendIntimacyChangeReason  # 已废弃，数据转移至dailyInteractStatistic
  dailyInteractStatistic            map<FriendInteractAttr>    3 +nocs   # 今天交互数据统计，用于推荐加亲密度的行为，type参考FriendInteractDataType

@IntimateRelationMotionAttr # 亲密好友交互动作
  uid                 int64           1 +key
  motionId            set<int32>      2

@AllIntimateRelationAttr # 所有亲密关系共用属性
  blockRecommendUid         set<int64>                        1         # 屏蔽推荐建立亲密关系的好友列表
  motionAttr                map<IntimateRelationMotionAttr>   2 +nocs   # 互动动作
  intimateRelationExtraCnt  int32                             3         # 亲密关系额外解锁数量

@LuckyFriendApplyAttr # 幸运好友邀请信息
  friendUid           int64           1 +key  # 邀请方好友UID
  taskId              int32           2       # 任务ID
  expireTime          int64           3       # 邀请失效时间
  taskUniqueId        int64           4 +nocs # 任务唯一ID

@LuckyFriendTaskAttr   # 幸运好友任务属性
  taskId                int32                                         1       # 任务ID
  state                 com.tencent.wea.xlsRes.LuckyFriendTaskState   2       # 任务状态
  stateEndTimeMs        int64                                         3       # 状态结束时间
  sendApplyFriendUid    int64                                         4       # 向哪个好友发送了邀请
  matchFriendUid        int64                                         5       # 匹配的好友UID
  taskUniqueId          int64                                         6 +nocs # 任务唯一ID

@LuckyFriendAttr
  configId            int32                         1       # 阶段ID，excel配置的ID
  stageStartTimeMs    int64                         2       # 阶段开始时间
  stageEndTimeMs      int64                         3       # 阶段结束时间
  triggerCnt          int32                         4       # 阶段内触发次数
  acceptCnt           int32                         5       # 阶段内接受好友邀请次数
  taskAttr            LuckyFriendTaskAttr           6       # 任务信息
  receiveApplyAttr    map<LuckyFriendApplyAttr>     7       # 接收到的邀请信息
  matchedTaskId       set<int32>                    8       # 已匹配/完成的任务ID

@BattleInfo   #战场信息
   battleid                  int64                              1               #战场id(dsSessionId)
   dsAddr                    string                             2               #ds地址信息
   side                      int32                              3               #阵营id
   dsAuthToken               string                             4               #DS认证Token
   uid                       int64                              5               #玩家uid
   desModInfo                string                             6               #音游 songid:1;diff:1
   globalChatGroupKey        ChatGroupKey                       7               #所有人聊天
   sideChatGroupKey          ChatGroupKey                       8               #阵营聊天
   matchType                 int32                              9               #玩法id
   aiGameType                int32                              10              #AI Control数据类型
   competitionBasicInfo      CompetitionBasicInfo               11              #赛事信息
   dsaInstanceID             int64                              12              #dsa_service_id
   miscInfo                  BattleMiscInfo                     13              #杂项信息
   relatedRoomId             int64                              14              #关联对局的roomId
   sceneId                   int64                              15              #场景ID
   dsSessionId               int64                              16              #独立ds情况下的dsSessionId
   robotType                 int32                              17              #是否导播
   pakType                   int32                              18              #对局创建时使用的包类型，为0则未指定-废弃
   pakGroupIdList            set<int32>                         19              #对局创建时使用的分包id列表，为空则为老逻辑

@CompetitionBasicInfo #赛事基本信息
   season                   int32                    1  # 届数
   gameType                 int32                    2  # 比赛类型 CompetitionGameType
   compType                 int32                    3  # 赛事类别 CompetitionType

@BattleMiscInfo #战场杂项信息
   arenaRandomEvents        string                   1  # arena随机事件

@WaitBattleInfo
   battleId                 int64                    1          +key
   createTimeMs             int64                    2

@ClubIdentityData
   uid                  int64                       1 +key # 玩家ID
   identity             int32                       2      # 玩家职务，对应CsClub.ClubType

@ClubBriefData
    cid                 int64                       1 +key  # 公会ID
    title               string                      2       # 公会称号
    chatGroupKey        ChatGroupKey                3       # 公会房间聊天信息
    name                string                      4       # 公会名称（废弃）
    redDot              bool                        5       # 公会红点
    admins              map<ClubIdentityData>       6 +nodb # 社团管理员
    weekSettleBubble    bool                        7       # 周结算气泡
    joinTimeMs          int64                       8       # 加入社团时间戳

@ClubInviteSendFrequencyAttr # 社团邀请信息发送限频信息
  chatType        int32     1 +key  # 频道ID
  lastSendTimeMs  int64     2       # 上次发送时间

@ClubNotifyJoinRankAttr # 社团提醒团长开启排行榜
  clubId            int64   1 +key # 社团ID
  lastSendTimeMs    int64   2      # 上次发送时间

@ClubWeekSettleShareAttr # 社团周结算分享限频信息
  chatType        int32     1 +key  # 频道ID
  lastSendTimeMs  int64     2       # 上次分享时间

@AttrRecentActivity     #玩家最近活跃信息
   lastDegreeUpgradeMs      int64   1   # 最近一次升级
   lastDegreeUpgradeType    com.tencent.wea.xlsRes.QualifyingDegreeType     2   # 升级到段位
   lastDegreeUpgradeStar    int32   3   # 升级到的星级
   lastPublishUgcMapMs      int64   4   # 最近一次发布UGC地图
   lastSaveXiaoWoMs         int64   5   # 最近一次更新小窝
   lastChampionMs           int64   6   # 最近一次夺冠
   lastChampion             int32   7   # 几连冠
   lastPlayUgcMapMs         int64   8   # 最近一次游玩UGC地图
   lastPlayModeMs           int64   9   # 最近一次游玩娱乐模式时间
   lastPlayModeId           int32   10  # 最近一次游玩娱乐模式ID

@AttrClubInfo   #公会信息
   cid                      int64                              1        #公会id(废弃)
   chatGroupKey             ChatGroupKey                       2        #房间聊天信息(废弃)
   applyClub                map<ApplyData>                     3        #申请帮派列表
   clubs                    map<ClubBriefData>                 4        #加入的公会信息
   created                  int64[]                            5        #创建的公会信息
   banCreate                bool                               6        #禁止创建公会
   heatToday                int32                              7 +nocs  #今日活力值
   heatDay                  int64                              8 +nocs  #今日时间
   worldInviteEnabled       bool                               9        #启用世界邀请功能
   inviteSendFrequencyAttr  map<ClubInviteSendFrequencyAttr>   10 +nocs # 邀请信息发送限频信息
   lastReadMsgSeqId         int64                              11 +nocs #上次收到的消息序号
   joinRankLastNotifyMap    map<ClubNotifyJoinRankAttr>        12 +nocs # 上次发送开启地区排行榜功能时间
   heatDetailToday          map<HeatDetail>                    13 +nocs #今日活力值详情
   weekSettleShareAttr      map<ClubWeekSettleShareAttr>       14 +nocs #社团周结算分享
   heatWeek                 int32                              15 +nocs #周活力值
   heatWeekDay              int64                              16 +nocs #周时间
   playerClubRecord         map<PlayerClubRecord>              17 +nocs #记录一下公会数据

@PlayerClubRecord
   cid              int64           1  +key    #工会ID
   joinTimeMs       int64           2          #加入公会时间
   leaveTimeMs      int64           3          #离开的时间

@HeatDetail
   id               int32           1   +key    # 行为id
   cnt              int32           2           # 次数
   conditionGroup   ConditionGroup  3           # 条件组

@AttrPermitInfo  #通行证信息
   id                    int64                             1      #通行证id  =0 没开启
	 level                 int32                             2      #通行证等级
	 exp 				           int32                             3      #通行证当前经验
	 receiveLevel          set<int32>                        4      #已经领取的等级奖励
	 gloryReceive          set<int32>                        5      #已经领取的高级等级奖励
	 type                  int32                             6      # 1:进阶版 2:荣耀
	 unlock                bool                              7      # 是否解锁高级版

@ApplyData
   cid                 int64                              1  +key   #公会id
   applyTime           int64                              2         #申请时间

@AttrTeamInfo
   teamId               int64                               1       #队伍id

@AttrCustomRoomInfo
   roomId               int64                               1       #房间Id
   roomTypeVal          int32                               2       #房间类型数值

@AttRoomMemberClientInfo
   voiceState           int32                               1       #语音状态
   voiceRoomTag         int32                               2       #语音标签

@AttrRoomInfo   #房间信息
   roomid               int64                              1      #房间id
   status               com.tencent.wea.xlsRes.RoomStatus  2      #状态信息
   #battleInfo           BattleInfo                         3      #战场信息
   chatGroupKey         ChatGroupKey                       4      #房间聊天信息
   currentMatchType     int32                              5      #当前选择的模式
   teamInfo             AttrTeamInfo                        10      # 队伍信息
   customRoomInfo       AttrCustomRoomInfo                  11      # 自定义房间信息
   roomMemberClientInfo AttRoomMemberClientInfo             12      # 房间里的状态在玩家身上临时存下，在创建自定义房间的时候，不会从队伍里取状态
   lastMatchType        int32                               13      #最近一次选择的非啾灵模式

@AttrSceneInfo   #场景信息
   lobbySceneId            int64                          1          # 大厅场景id
   lobbyMapId              int32                          2          # 大厅地图id
   roundId                 int64                          3          # 对局id
   levelInfo               LevelInfo                      4          # 关卡信息
   interactionId           int64                          5  +nocs   # 执行指令ID

@SceneInfo   #场景信息
   sceneid             int64                              1      #场景id
   mapid               int32                              2      #地图id
   interactAction      SceneInteractActionInfo            3  +nodb    #交互动作

@LobbyNpcInfo #大厅Npc信息
   npcId                int32                              1  +key      #npcId
   npcStageId           int32                              2            #stageId
   npcStageList         set<int32>                         3            #stageId集合

@LobbyChangeColorInfo   #大厅换色信息
   id                   int32                              1  +key      #换色Id
   name                 string                             2            #换色名称
   levelName            string                             3            #关卡名称
   startTime            int64                              4            #开始时间,unixtime毫秒
   endTime              int64                              5            #结束时间,unixtime毫秒

@UgcMapCreatorInfo
   creatorId            int64                       1  +key     # 创作者id
   editorType           int32                       2           # 编辑器类型
   nickName             string                      3           # 昵称
   avatar               string                      4           # 头像
   dressItemInfos       map<DressItemInfo>          5           # 装扮信息

@UgcMapLoadingInfo
   creatorInfo          map<UgcMapCreatorInfo>      1           # 创作者信息
   loadingTemplateId    int32                       2           # 加载模板
   tags                 string                      3           # 标签信息

@UgcMapInfo
   bucket               string                             1            # 桶信息
   ugcId                int64                              2            # ugc地图id
   fileName             string                             3            # 地图文件名
   version              string                             4            # 地图版本
   msg                  string                             5            # md5
   preMsg               string                             6            # 加密前md5
   mapKey               string                             7            # 加密key
   gameCamParam         int32[]                            8            # ugc镜头参数
   loadingInfo          UgcMapLoadingInfo                  9            # 加载图信息
   chunkGroupIdList     int32[]                            10           # 客户端

@PlayerUgcLobbyInfo
   lobbyId              int64                              1            # 大厅ID
   mapId                int32                              2            # 地图ID
   ugcId                int64                              3            # UgcID

@LobbyInfo   #大厅信息
   lobbyId                  int64                              1              # 大厅id
   dsAddr                   string                             2              # ds地址信息
   dsAuthToken              string                             3              # DS认证Token
   dsaInstanceID            int64                              4 +nocs        # ds所在dsa实例
   mapId                    int32                              5              # 大厅地图mapId
   npcInfos                 map<LobbyNpcInfo>                  6              # npc信息列表
   changeColorInfo          LobbyChangeColorInfo               7              # 大厅换色信息
   gameType                 int32                              8              # 地图类型
   ugcMapInfo               UgcMapInfo                         9 +nocs +nodb  # ugc地图数据 弃用
   name                     string                             10 +nocs +nodb # 地图名字
   lastLobbyId              int64                              11             # 上次大厅id
   lastMapId                int32                              12             # 上次大厅地图id
   lastUgcId                int64                              13             # 上次大厅ugcId
   lastTestUgcLobby         PlayerUgcLobbyInfo                 14 +nocs       # 未发布的自测UGC大厅ID,用于重回该大厅
   lastInspectorUgcLobby    PlayerUgcLobbyInfo                 15 +nocs       # 上次未发布的自测UGC大厅ID,用于重回该大厅
   lobbyCreatorUid          int64                              16             # 大厅的创建者信息，用于UGC自测大厅邀请的时候做创建人校验
   ugcId                    int64                              17             # 当前大厅的ugcId
   ugcSafeStatus            int32                              18             # 当前地图的状态


@PublicLobbyInfo   #公开大厅信息
   lobbyId              int64                              1      #大厅id
   mapId                int32                              2      #地图id
   mapType              int32                              3      #地图类型

@PublicXiaoWoInfo   #公开小窝信息
   xiaoWoId              int64                              1     #小窝id

@PublicFarmInfo   #公开农场信息
   farmId              int64                              1
   farmCheckedLeave    bool                               2  # 农场检测到离线

@PublicHouseInfo   #公开农场小屋信息
   houseId              int64                              1

@PublicRichInfo   #公开大富翁信息
   boardId              int64                              1

@PublicCookInfo   #公开农场餐厅信息
   cookId              int64                              1

@LevelInfo   #关卡信息
    championNum              int32                          1            # 冠军次数
    collections              map<SceneCollection>           2            # 收集物
    maxLevelScore            map<LevelScore>                3            # 每关最高得分
    maxTotalScore            int32                          4            # 最高总分

@LevelScore
    levelId           int32                     1   +key       # 关卡id
    score             int32                     2              # 得分

@LevelIllustration
    championInfo            map<ChampionCountInfo>      1           # 奖杯数量
    levelAchievementInfo    map<LevelAchievementInfo>   2           # 关卡成就信息

@ChampionCountInfo
    type                int32                   1   +key    # 奖杯类型
    cnt                 int32                   2           # 数量

@LevelAchievementInfo
    id                  int32                   1   +key    # 关卡id
    type                int32                   2           # 关卡类型
    bestScore           int32                   3           # 最佳得分，竞速则为耗时，积分则为得分
    achievementGroupId  int32                   4           # 成就任务组
    dataByPlay          map<LevelRecordByPlay>  5           # 玩法细分下的记录数据

@LevelRecordByPlay
    playId              int32                   1   +key    # 玩法id
    data                LevelRecordData         2           # 记录数据

@LevelRecord
    records                 map<LevelRecordDetails>         1           # 记录

@LevelRecordDetails
    levelId                 int32                           1   +key    # 关卡id
    detailItems             map<LevelRecordDetailItem>      2           # 关卡记录细项

@LevelRecordDetailItem
    itemId                  string                                      1   +key    # 组合key "season_play" 赛季+玩法
    season                  int32                                       2           # 赛季
    play                    int32                                       3           # 玩法
    timestamp               int64                                       4           # 记录项的产生时间
    data                    LevelRecordData                             5           # 数据

@LevelRecordData
    timeCost                int64                   1       # 耗时
    score                   int32                   2       # 得分

@SceneCollection
    id                int32                   1   +key    # 收集物id
    num               int32                   2           # 收集数量

@SceneInteractActionInfo
  type          com.tencent.wea.xlsRes.SceneInteractActionType          1 # 互动类型
  status        com.tencent.wea.xlsRes.SceneInteractActionStatus        2 # 互动状态
  actionId      int32                                                   3 # 动作ID
  objectUid     int64                                                   4 # 目标玩家Uid
  timeoutTs     int64                                                   5 # 动作激活超时时间

@PrayInfo
   resultId                 int32                1    # 运势结果ID
   propertyIds              set<int32>           2    # 词条ID
   expireTimeMs             int64                3    # 过期时间
   todayTopPrayQuality      int32                4    # 今天最高祈福品质

@RoguelikePropInfo
   propID                   int32                1  +key
   unlockThreshold          float                2
   unlockProgress           float                3
   isUnlock                 bool                 4
   unlockTimeStamp          int64                5
   acquireTimes             int32                6

@RoguelikeMonsterInfo
    monsterID               int32                1  +key
    isUnlock                bool                 2
    killTimes               int32                3    # 击杀次数
	unlockTimeStamp         int64                4

@RoguelikeWeaponDamageData
   damageType               int32                1  +key
   damageNumber             float                2

@RoguelikeUseSkillData
   skillID                  int32                1  +key
   userNumber               int32                2

@RoguelikeGetPropsData
   propsID                  int32                1  +key
   getNumber                int32                2

@RoguelikeKillMonsterData
   monsterID                int32                1  +key
   killNumber               int32                2

@RoguelikeWeaponInfo
   weaponID                 int32                             1  +key
   damageData               map<RoguelikeWeaponDamageData>    2
   takeDamage               float                             3
   useSkill                 map<RoguelikeUseSkillData>        4
   getProps                 map<RoguelikeGetPropsData>        5
   getGold                  int32                             6
   costGold                 int32                             7
   passLevel                int32                             8
   killMonster              map<RoguelikeKillMonsterData>     9
   weaponShoot              int32                             10
   weaponReload             int32                             11
   criticalNumber           int32                             12
   gambleNumber             int32                             13

@RoguelikeInfo
   weaponId                 int32                   1            # 当前使用的武器ID
   skillId                  int32                   2
   taskData                 RoguelikeTaskData       3    +nocs   # 肉鸽玩法任务数据
   seasonData               RoguelikeSeasonData     4    +nocs   # 肉鸽玩法赛季数据
   extraInfo                RoguelikeExtraInfo      5            # 肉鸽玩法额外数据
   talentData               RoguelikeTalentData     6    +nocs   # 肉鸽玩法天赋数据
   rankData                 RoguelikeRankData       7            # 排行榜数据
   endlessFirstWeaponId     int32					8
   endlessSecondWeaponId    int32					9

@RoguelikeRankRecord
   settlementTimeMs         int64                         1    +key    # 结算时间
   passLevels               int32                         2            # 通过关卡数

@RoguelikeRankData
   seasonId                 int32                         1            # 肉鸽赛季ID
   records                  map<RoguelikeRankRecord>      2            # 个人排行榜数据

@RoguelikeEndlessSavePropsInfo
   propsID					int32				 1  +key
   propsCount				int32				 2

@RoguelikeEndlessSaveSpecialBuffInfo
   buffID					int32				 1  +key
   stackCount				int32				 2

@RoguelikeEndLessSaveInfo
   lastTaskLevel            int32                                       1
   lastLevelNumber          int32                                       2
   propsList                map<RoguelikeEndlessSavePropsInfo>          3	+nocs
   specialBuffList          map<RoguelikeEndlessSaveSpecialBuffInfo>    4	+nocs
   gold                     int32                                       5
   ex                       int32                                       6
   nowHp                    int32                                       7
   damage                   int32                                       8
   killCount                int32                                       9
   roleLevel                int32                                       10
   sumGold                  int32                                       11
   helpCount                int32                                       12
   matchID                  int64                                       13
   battleID                 int64                                       14
   battleType               int32                                       15    # W_玩法模式_fps.xlsx 玩法ID

@RoguelikePassLevelInfo
   easyPassNumber			int32				 				1
   normalPassNumber			int32				 				2
   hardPassNumber			int32								3
   endlessMaxLevelNumber	int32				 				4
   lastEndlessPassLevel     int32                               5
   isNewRecord				bool								6

@RoguelikeExtraInfo
   monsterRows              map<RoguelikeMonsterInfo> 1		+nocs
   propRows                 map<RoguelikePropInfo>    2		+nocs
   weaponRows				map<RoguelikeWeaponInfo>  3		+nocs
   endLessSaveRows			RoguelikeEndLessSaveInfo  4
   markData                 RoguelikeMarkData         5		+nocs
   passLevelRows			RoguelikePassLevelInfo	  6

@RoguelikeUnlockTalent
   talentId                  int32                1  +key

@RoguelikeCommonProp
   propsID                  int32                1  +key
   stackNumber                int32                2

@RoguelikeTalentData
   unlockTalentIds         set<int32>                 1
   costPropInfo            RoguelikeCommonProp         2

@RoguelikeTask
   id                      int32                1  +key    # 任务ID
   state                   int32                2          # 任务状态
   progress                int32                3          # 任务进度

@RoguelikeTaskData
   tasks                   map<RoguelikeTask>   1          # 任务列表
   refreshTime             int64                2          # 上次自动刷新任务的时间(单位秒)
   manuallyRefreshTimes    int32                3          # 已使用的手动刷新次数
   preRefreshTime          int64                4          # 上次自动刷新任务的时间(单位秒)

@RoguelikeSeasonData
   seasonId                int32                1
   curMilestoneScore       int32                2
   hadDrawRewardLevels     set<int32>                 3  #已领取奖励的等级
   dailyTaskData           RoguelikeTaskData            4 #每日任务
   weekTaskData            RoguelikeTaskData            5 #每周任务
   seasonTaskData          RoguelikeTaskData            6 #赛季任务

@RoguelikeMarkEntry
   entryId                 int32                      1   +key
   entryLevel              int32                      2
   isMainEntry             bool                       3

@RoguelikeMark
   id                      int32                           1  +key    #印记ID
   level                   int32                           2          #印记等级
   rarity                  int32                           3          #印记稀有度
   baseEffectId            int32                           4          #基础效果ID
   entryInfo               map<RoguelikeMarkEntry>         5          #词条
   specialEffectId         int32                           6          #特殊效果ID
   shapeId                 int32                           7          #形状ID
   isLock                  bool                            8          #印记锁定
   breakThroughLevel       int32                           9          #印记突破等级

@RoguelikeMarkInBoardInfo
   markId                  int32                           1  +key    #印记板中的印记ID
   rowIndex                int32                           2          #印记在印记板中的位置(横坐标)
   columnIndex             int32                           3          #印记在印记板中的位置(纵坐标)
   rotateType              int32                           4          #印记在印记板中的旋转方式

@RoguelikeMarkBoard
   id                      int32                           1  +key    #印记板ID
   shapeId                 int32                           2          #印记板形状ID
   marksInBoard            map<RoguelikeMarkInBoardInfo>   3          #印记板中的印记
   boardName               string                          4          #印记板名字

@RoguelikeMarkData
   marks                   map<RoguelikeMark>              1          #印记数据
   markBoards              map<RoguelikeMarkBoard>         2          #印记板数据
   currMarkBoardId         int32                           3          #当前使用的印记板ID
   totalMaterials          int64                           4          #印记材料
   markBoardTemplates      set<int32>                      5          #当前拥有的印记板模板
   DropPreference          int32                           6          #掉落偏好

@ChatGroupKey
  chatType                 int32                1     # 聊天类型
  id                       int64                2     # id
  subID                    int64                3     # subID

@CommonLimitInfo
   limitType        com.tencent.wea.xlsRes.CommonLimitType  1 +key #限制类型
   limitInfo        map<LimitInfoStruct>        2      #过期信息

@LimitInfoStruct        #通用限制结构
  limitId                  int64                1   +key   #限制ID
  value                    int64                2          #当前数量
  expireTimeMs             int64                3          #过期时间
  maxValue                 int64                4          #上限值

@P2PChatGroup
  id                        int64               1   +key    # 对象id
  status                    int32               2           # 自定义状态
  updateTs                  int64               3           # 更新时间戳
  sentCount                 int32               4           # 发送消息数
  startSeq                  int64               5           # 起始序号
  lastSeq                   int64               6           # 上次同步序号
  recvCount                 int32               7           # 接收消息数
  latestBattleTs            int64               8           # 最近同玩时间

@P2PChatInfo
  groups                    map<P2PChatGroup>       1           # 点对点聊天
  sayHiDailyCount           int32                   2           # 打招呼的次数-每天
  beGreetedDailyCount       int32                   3           # 被打招呼的次数-每天

#热点话题点赞信息
@HotTopicInfo
  id                  int32                   1   +key    # 话题id
  thumbUpMS           int64                   2           # 点赞时间戳

@CommunityChannelHotTopicInfo
  arenaHotTopicInfo              map<HotTopicInfo>         5          #峡谷社区频道
  farmHotTopicInfo               map<HotTopicInfo>         6          #农场社区频道
  wolfKillHotTopicInfo           map<HotTopicInfo>         7          #狼人杀社区频道
  tradingCardHotTopicInfo        map<HotTopicInfo>         8          #卡牌社区频道
  spHotTopicInfo                 map<HotTopicInfo>         9          #sp兴趣频道

@CommunityChannelInfo
  chatGroupKey             ChatGroupKey       1           # 社区频道信息
  joinTS                   int64              2           # 加入时间戳
  stickTS                  int64              3           # 置顶时间戳
  initiatedQuitTs          int64              4           # 主动退出的时间戳

@PublicChatInfo
  worldChat                ChatGroupKey         1          #世界聊天
  teamRecruitChat          ChatGroupKey         2          #组队招募聊天
  newStarChat              ChatGroupKey         3          #新人聊天
  P2PChat                  P2PChatInfo          4          # 私聊
  arenaCommunityChannel              CommunityChannelInfo         5          #峡谷社区频道
  farmCommunityChannel               CommunityChannelInfo         6          #农场社区频道
  wolfKillCommunityChannel           CommunityChannelInfo         7          #狼人杀社区频道
  communityChannelHotTopicInfo       CommunityChannelHotTopicInfo 8          #热点话题点赞信息
  tradingCardCommunityChannel        CommunityChannelInfo         9          #卡牌社区频道
  spCommunityChannel                 CommunityChannelInfo         10         #sp社区频道
  chatModuleUpdateTimestampMs           int64                     11         # 聊天模最近更新时间戳
  boundChatModuleList                   set<int32>                12         # 已绑定的模块
  chatRedDotInfo                      map<ChatRedDots>            13  +nocs  # 频道的红点数据
  oneKeyClearNotReadTimeMs           int64                        14  +nocs  # 一键清理未读时间

@ChatRedDots
  formatKey                 string              1 +key      # 格式化的key type_id_subid
  chatGroupKey              ChatGroupKey        2           # 频道
  redDotCnt                 int32               3           # 红点数
  unReadSeqIds              set<int64>          4           # 未读消息列表
  lastUnReadSeq             int64               5           # 最后一条未读的序号

@RootAttr
   userAttr                 UserAttr                 1    +nodb
                                                # 不要用2
   cocUserAttr              CocUserAttr              3     +nodb,+deletable   # coc玩法场景内玩家数据,玩家进入玩法内才会下发给客户端

@EquipDressInfo
   dressUpType           com.tencent.wea.xlsRes.ItemType     1 +key  #装扮类型
   equipCollections      EquipCollections                    2       #装扮信息

@DressItemInfo
   dressUpType           com.tencent.wea.xlsRes.ItemType     1 +key  #装扮类型
   itemUUID              int64                               2
   itemId                int32                               3

@EquipCollections +oneof
   singleCollection      int64                    1  #唯一
   randomCollections     RandomCollections        2  #多个随机
   positionCollections   PositionCollections      3  #有位置穿戴

@RandomCollections
   singleCollection      int64                  1  #穿戴单个
   collections           set<int64>             2  #随机
   isRandom              bool                   3  #随机开关

@PositionCollections
   positionCollections   map<EquipPosInfo>      1  #有位置穿戴

@EquipPosInfo
   position              int32                  1   +key  #穿戴位置
   collectionId          int64                  2         #穿戴道具UUID

@EquipItemInfo
   itemType              com.tencent.wea.xlsRes.ItemType     1 +key  #装扮类型
   posItemInfo           PositionCollections                 2

@TargetEquipInfo
   targetId              int32                   1 +key
   posItemInfo           PositionCollections     2

@ABTestInfo
   testType                int32                 1 +key     # 对应 T_TAB实验配置.xlsx 中的 TestType 字段
   GroupId                 int32                 2          # ABTest组id
   guideCount              int32                 3          # 新手引导次数
   disable                 int32                 4          # 不启用实验 0.默认 启用 1.不启用

@LetsGoSpecPlayerPublicInfo
    winTimes               int32            2     # 达标次数
    continuousWin          int32            5     # 连赢次数
    winTop3Times           int32            6     # 前三名次数
    winChampTimes          int32            7     # 夺冠次数
    crownPlayTimes         int32            8     # 参与抢皇冠次数
    teamPlayTimes          int32            9     # 组队开局次数
    crashTotalTimes        int32            10    # 累计碰撞次数
    countdownFinishTimes   int32            11    # 倒计时内达标次数
    winChampAtTheEndTimes  int32            12    # 最终夺冠次数，多关卡只算最后一次

@PlayerRankGeoInfo
    region           com.tencent.wea.xlsRes.RankGeoRegion     1      # 地区类型，已废弃
    province         int32                                    2      # 省份编码
    city             int32                                    3      # 城市编码
    town             int32                                    4      # 区县编码
    detail           string                                   5      # 地区描述，已废弃

    isOpen           bool                                  6         # 是否开启
    lastUpdateTs     int64                                 7         # 上次更新时间
    nation           int32                                 8         # 国家编码
    nationStr        string                                9         # 国家
    provinceStr      string                                10        # 省份
    cityStr          string                                11        # 城市
    townStr          string                                12        # 区县


@PlayerRankData
    seasonProvinces         set<int32>                      1         # 本赛季待修改的省份集合，已废弃
    seasonCities            set<int32>                      2         # 本赛季待修改的城市集合，已废弃
    seasonTowns             set<int32>                      3         # 本赛季待修改的县区集合，已废弃
    weeklyGeoModTimes       int32                           4         # 每周位置信息更改次数，已废弃
    rankReportStatus        map<RankInfoReportStatus>       5         # 上报状态
    lastGeoChangeTs         int64                           6         # 上一次开始迁榜时间
    hideAllFromOthers       bool                            7         # 屏蔽所有成绩
    refreshQueueDb          map<RankInfoItem>               8  +nocs  # 未处理完毕的更新请求
    settleInfos             map<PlayerRankSettlement>       10 +nocs  # 结算记录
    lastGeoInfo             PlayerRankGeoInfo               11 +nocs  # 上一次迁榜数据

@PlayerRaffleInfo
    id                     int32                           1   +key     # 奖池ID
    dailyTimes             int32                           2            # 单日抽取次数，每日5点刷新
    times                  int32                           3            # 本刷新周期内，抽奖次数
    oneDraw                RafflePurchaseRecord            4            # 本刷新周期内，单抽记录
    multiDraw              RafflePurchaseRecord            5            # 本刷新周期内，多连抽记录
    guarantee              map<RaffleGuaranteeRecord>      6            # 保底机制记录
    lastRefreshTs          int64                           7   +nocs    # 上次刷新时间
    rewards                map<RaffleRewardRecord>         8            # 奖励记录
    rewardGroups           map<RaffleRewardGroupRecord>    9            # 奖励组记录
    freeOneDraw            RafflePurchaseRecord           10            # 本刷新周期内，免费单抽记录
    chosenReward           set<int32>                     11            # 已选中奖励ID
    benefitIds             set<int32>                     12            # 福利卡ID
    testWaterTimes         int32                          13            # 累计试水次数

@RafflePurchaseRecord
    lastTs                 int64                 1      # 上次购买时间
    times                  int32                 2      # 购买次数
    lastPerDiscountTs      int64                 3      # 上次常驻优惠购买时间
    perDiscountTimes       int32                 4      # 常驻优惠购买次数
    firstDiscountTs        int64                 5      # 上次首抽优惠购买时间
    freeTimesAcquired      int32                 6      # 累计获得的免费次数, 已废弃
    freeTimesDelivered     int32                 7      # 累计助力的免费次数, 已废弃
    freeTimesSourceId      int64                 8      # 免费次数来源ID, 已废弃
    recordBIDiscount       RaffleBIDiscount      9      # BI折扣
    lastTmpDiscountTs      int64                 10     # 上次时限优惠购买时间
    tmpDiscountTimes       int32                 11     # 时限优惠购买次数
    recordFreeDiscount     RaffleFreeDiscount    12     # 免费折扣
    testWaterTimes         int32                 13     # 试水次数
    lastTestWaterTs        int64                 14     # 上次试水时间

@RaffleGuaranteeRecord
    type              com.tencent.wea.xlsRes.RaffleGuaranteeType     1 +key    # 保底类型
    counter           int32                                          2         # 计数器
    drawnGroupIds     set<int32>                                     3 +nocs   # 抽中的奖励组集合，跟随抽取周期
    deactivated       bool                                           4         # 保底是否关闭
    skipTextId        set<int32>                                     5 +nodb   # 跳过展示的文本，用于保底文本展示控制
    subId             int32                                          6         # 子规则

@RaffleRewardRecord
    rewardId          int32         1   +key    # 奖励ID
    drawnTimes        int32         2           # 抽取上限周期内的抽中次数
    totalDrawnTimes   int32         3           # 总计抽中次数
    fragNum           int32         4           # 碎片数量
    inGroupDrawnTimes int32         5           # 组周期内的抽中次数
    lastDrawnTs       int64         6           # 上次抽中时间

@RaffleRewardGroupRecord
    groupId          int32         1   +key    # 奖励ID
    lastDrawAtCount  int32         3           # 上次抽中所在抽数
    totalDrawnTimes  int32         4           # 累计抽中次数
    drawnTimes       int32         5           # 组周期内的抽中次数
    dailyDrawnTimes  int32         6           # 今日抽中次数

@ActivitySquadInfo
    squadId         int64                                           1       # 小队Id
    activityNo      int32                                           2       # 活动期数
    dailyPoint      int32                                           3       # 当日得分
    accumulatePoint int32                                           4       # 当期已累计得分，除开当日

@SquadItemInfo
    index           int32                                           1   +key    # 挖宝序号
    dug             bool                                            2           # 是否挖过了
    itemId          int32                                           3           # 配表中的挖宝结果物品Id
    dugBy           int64                                           4           # 挖出者uid
    dugTimestamp    int64                                           5           # 挖出时间

@SquadItemCntInfo
    itemId          int32                                           1   +key    # 宝藏id
    cnt             int32                                           2           # 已挖出的数量

@MultiPlayerSquadInfo
    squadId             int64                                           1       # 小队Id
    activityNo          int32                                           2       # 活动期数
    canDig              bool                                            3       # 是否可挖宝
    items               map<SquadItemInfo>                              4       # 宝藏信息
    members             set<int64>                                      5       # 成员列表
    activityId          int32                                           6       # 活动id
    lastOperationTimeMs int64                                           7       # 上次操作时间
    accumulativeItems   map<SquadItemCntInfo>                           8       # 累计宝藏计数信息

@SquadMember
    uid                 int64                                           1   +key    # uid
    nickname            string                                          2           # 昵称
    gender              int32                                           3           # 性别  1-男 2-女 0-未知
    profile             string                                          4           # 头像url (maybe)
    level               int32                                           5           # 等级
    dressUpInfos        set<int32>                                      6           # 所有装扮信息
    openid              string                                          7           # gopenid
    joinTimeMs          int64                                           8           # 加入时间ms
    lastUpdateTimeMs    int64                                           9           # 上次刷新时间
    areaId              int32                                           10          # idip areaId(H5外链用)
    platId              int32                                           11          # 平台id(H5外链用)
    point               int32                                           100         # 积分（外显用）
    memberSquadData     ActivitySquadData                               1001 +nocs  # 队伍成员活动数据

@ActivityGroupPhotoData
    name                string                                          1           # 小队名称
    groupPhotoUrl       string                                          2           # 合影地址

@TrophyTaskCompleteInfo
    taskId              int32                                           1   +key    # 任务id
    completeTimestampMs int64                                           2           # 完成时间
    completePlayerUid   int64                                           3           # 完成者

@ActivityTrophyTaskCompleteData
    trophyCnt           int32                                           1           # 奖杯计数
    taskData            map<TrophyTaskCompleteInfo>                     2   +nocs   # 任务流水

@ActivityWolfKillSquadTrophyData
    trophyCnt                            int32                          1           # 奖杯计数
    taskGainTrophyCnt                    int32                          2           # 任务获取的奖杯计数

@ActivityRewardDetail
    rewardId                            int32                           1   +key    # 奖励唯一标记符
    rewardTimeMs                        int64                           2           # 下发时间
    rewardType                          int32                           3           # 任务:0 农场buff:1

@ActivityPlayerRewardHistory
    uid                                 int64                           1   +key    # 玩家uid
    rewardHistory                       map<ActivityRewardDetail>       2           # 奖励历史

@ActivityFarmSquadData
    rewardTreeLevel                     int32                               1           # 当前奖励树等级-废弃
    rewardHistory                       map<ActivityPlayerRewardHistory>    2  +nocs    # 奖励下发记录
    statisticsData                      map<KvII>                           3  +nocs    # 统计数据
    buffActivated                       set<int32>                          4           # 生效buff集合
    luckyFlag                           bool                                5           # 是否是幸运玩家

@ActivityTrophyData
    uid                                 int64                           1   +key    # 玩家uid
    trophyCnt                           int32                           2           # 奖杯计数

@ActivityFindPartnerData # 匹配搭子队伍数据
    trophyData              map<ActivityTrophyData>                     1           # 奖杯数据 map<uid, trophyCnt>

@ActivitySquadData +oneof
    groupPhotoData          ActivityGroupPhotoData                      1           # 合影活动数据
    groupingReturnData      GroupingReturnData                          2           # 团购返利
    groupTrophyTaskData     ActivityTrophyTaskCompleteData              3           # 奖杯小队任务数据
    groupWolfKillTrophyData ActivityWolfKillSquadTrophyData             4           # 狼人杀奖杯小队任务数据
    farmSquadData           ActivityFarmSquadData                       5           # 农场搭子活动数据
    findPartnerData         ActivityFindPartnerData                     6           # 匹配搭子活动数据
    mobaSquadDrawRedPacketData MobaSquadDrawRedPacketData               7           # moba组队抽红包活动
    twoPeopleSquad          ActivityTwoPeopleSquadData                  8           # 双人成团小队数据
    inflateRedPacket        ActivityInflateRedPacketData                9           # 膨胀爆红包小队数据

@ActivitySquadDetail
    activityId          int32                                           1   +key    # 活动id
    squadId             int64                                           2           # 小队id
    members             map<SquadMember>                                3           # 小队成员列表
    leaderUid           int64                                           4           # 队长uid
    extraData           ActivitySquadData                               5           # 额外活动数据
    lastUpdateTimeMs    int64                                           6           # 上次刷新时间
    activityType        int32                                           7           # 活动类型（冗余）
    squadCreateTimeMs   int64                                           8   +nocs   # 小队创建时间
    sumData             map<KvII>                                       9   +nocs   # 小队合计数据-废弃
    phrase              int32                                           10          # 活动阶段，从1开始

@PlayerDepositInfo
    id                  int32                  1     +key    # 储蓄ID
    balance             int32                  2             # 账面额度
    refreshTs           int64                  3             # 账面更新时间
    boughtIds           set<int32>             6             # 已购买商品IDs
    lastBoughtTs        int64                  7             # 上次购买时间

@PlayerRecruiteInfo
   recruiter            RecruiteEntry                   1              # 招募我的人
   recruits             map<RecruiteEntry>              2              # 被我招募的人
   weeklyGotCoin        int64                           3              # 每周获得的代币-招新
   weeklyGotCoinByPlay  int64                           4              # 每周获得的代币-同玩
   weeklyGotCoinByLogin int64                           5              # 每周获得的代币-登陆
   identityCode         string                          6     +nodb    # 邀请码,由玩家的短序号编码
   activeTimeMs         int64                           7              # 激活时间，可以用于判断玩家绑定有效期
   recruiteRaffles      map<RecruiteRaffleInfo>         8              # 获奖记录
   self                 RecruiteEntry                   9              # 我自己

@RecruiteEntry
   type                 int32                   1              # ResKeywords.RecruiteType
   openid               string                  2              #
   platId               int32                   3              #
   uid                  int64                   4     +key     # 玩家uid
   signingTime          int64                   5              # 签约时间
   charName             string                  6              # 角色名字
   platName             string                  7              # 平台名字
   picUrl               string                  8              # 头像url
   peerLoginDays        int32                   9              # 对点累计记录登陆天数
   peerLoginTime        int64                   10             # 对点上次登录时间
   selfSyncTime         int64                   11             # 主动同步时间，数据持有方，记录的时间
   selfLoginReward      int64                   12             # 登录奖励领取标记

@RecruiteRaffleInfo
   id                   int32                   1     +key     # 获奖次序
   rewardId             int32                   2              #
   drawnTs              int64                   3              # 抽中时间
   hasAddress           bool                    4              # 是否填写过地址

@BookOfFriendsReport
  times                 int32                     1   # 次数
  credit                int64                     2   # 积分
  upTimeSec             int64                     3   # 更新时间戳

@BookOfFriendsContract
  uid                   int64                   1      +key             # 玩家uid
  income                BookOfFriendsReport     2                       # 收益
  expense               BookOfFriendsReport     3                       # 开支

@BookOfFriendsTypeInfo
  type                  int32                   1       +key            # 用户类型
  income                BookOfFriendsReport     2                       # 累计收益

@BookOfFriendsTaskInfo
  accumedIncome         BookOfFriendsReport           1               # 累计收益
  rewardFlag            int64                         2               # 奖励标记
  dailyExpense          BookOfFriendsReport           3               # 每日开支
  contract              map<BookOfFriendsContract>    4               # 契约关系
  typeInfo              map<BookOfFriendsTypeInfo>    5               # 按类统计

@BookOfFriends
  accumedIncome         BookOfFriendsReport           1               # 累计收益
  rewardFlag            int64                         2               # 奖励标记
  dailyExpense          BookOfFriendsReport           3               # 每日开支
  contract              map<BookOfFriendsContract>    4               # 契约关系
  typeInfo              map<BookOfFriendsTypeInfo>    5               # 按类统计
  farmGiftTaskInfo		  BookOfFriendsTaskInfo		      6               # 农场赠礼任务数据

@DepositSnapshot
    timestamp           int64                  1     +key    # 快照时间
    balanceNum          int32                  2             # 快照额度
    major               DepositGift            3             # 主礼包购买时间
    minor               DepositGift            4             # 加赠礼包购买时间

@DepositGift
    intentionTs         int64                  1             # 意向购买时间
    confirmTs           int64                  2             # Midas确认时间
    futureItems         map<DepositFutureItem> 3             # 延期交付礼包

@DepositFutureItem
    uuid                int64                  1     +key
    id                  int32                  2             # 礼包ID
    num                 int32                  3             # 数量
    untilTs             int64                  4             # 可领取时间

@RankInfoReportStatus
    rankId              int32                                            1 +key   # RankID
    globalStatus        com.tencent.wea.xlsRes.RankReportStatus          2        # 全局排行榜上报状态
    geoStatus           com.tencent.wea.xlsRes.RankReportStatus          3        # 地区排行榜上报状态
    bannedUntilTs       int64                                            4        # 封禁截至时间
    nation              int32                                            5        # 分数所在国家
    province            int32                                            6        # 分数所在省份
    city                int32                                            7        # 分数所在城市
    town                int32                                            8        # 分数所在县区

@PlayerRaffleGroupInfo
    id                     int32                           1   +key     # 奖池组ID
    dailyTimes             int32                           2            # 单日抽取次数，每日5点刷新
    times                  int32                           3            # 累计抽取次数
    costs                  map<RaffleCost>                 4            # 累计花费
    awardedTimes           set<int32>                      5            # 已领取宝箱的抽数
    confirmedSlot          map<RaffleSlot>                 6            # 确认已抽取的位置，客户端展示使用
    points                 int32                           7            # 累计积分
    awardedPoints          set<int32>                      8            # 已领取宝箱的积分
    substitutes            map<RaffleRewardSubstitute>     9            # 道具转换记录
    chosenReward           set<int32>                      10           # 已选中奖励ID
    dailyCosts             map<RaffleCost>                 11           # 每日花费
    benefitIds             set<int32>                      12           # 福利卡ID
    inventory              map<RaffleInventoryItem>        13           # 暂存箱
    extraShowStr           string                          14           # 辅助显示字段
    subItems               map<RaffleRewardSubItem>        15           # 子奖励信息
    cratesBounceCount      int32                           16           # 累计惊喜宝箱开启次数
    extraCounters          map<KvII>                       17  +nocs    # 额外计数器

@QAInvestInfo
    id                     int32                           1   +key        # 问卷ID
    investStatus           com.tencent.wea.xlsRes.QAInvestStatus     2     # 问卷状态

@QAInvestTag
    id                     int32                           1   +key        # 标签ID
    createdTime            int64                           2               # 标签时间

@MMRScoreDb
    id                     int32                           1   +key           # ScoreId mmr对应的模式
    score                  int32                           2                  # 具体分数

@MMRScoresInfoDb
    scores                map<MMRScoreDb>                      1                  # 各模式分数

@WarmRoundScoreChangeDb
    millTs                    int64                        1   +key         # 变化时间id
    changedScore              int32                        2                # 分数变化

@WarmRoundBattleResultDb
    millTs                        int64                        1    +key
    result                        int32                        2

@WarmRoundScoreDb
    id                       int32                              1   +key    # 温暖分类型
    changes                  map<WarmRoundScoreChangeDb>        2           # 最近10次温暖局分数变更
    recentEnteredLevel       int32[]                            3           # 最近10次战斗进入了第几轮(1开始)
    consecutiveNoChampionCnt int32                              4           # 连续多少次没有获得冠军 与recentEnteredLevel独立
    dayTriggered             WarmRoundTriggeredDb               5           # 每日触发统计

@WarmRoundTriggeredDb
    id                      int64                               1   +key    # 通用id
    cnt                     int32                               2           # 次数

@WarmRoundReturningInfoDb
    mainGamePlayedCnt       int32                               1       # 主玩法游玩的次数, 废弃
    returningDays           int32                               2       # 回流天数
    warmRoundTimes          int32                               3       # 当前温暖局加权分数

@WarmRoundInfoDb
    scores                  map<WarmRoundScoreDb>               1
    returningInfo           WarmRoundReturningInfoDb            2       # 回归后温暖局信息

@IdcNetworkRecordDb
    id                      int64                               1   +key        # idc id
    rttMs                   int32                               2               # 延迟

@IdcNetworkInfoDb
    records                 map<IdcNetworkRecordDb>             1               # 各idc记录
    millTs                  int64                               2               # 记录的时间戳ms

@MatchIsolateInfoDb
    time                    int64                               1               # 结束时间秒为单位
    ailevel                 int32                               2               # 记录的时间戳ms
    reason                  string                              3               # 提示字符串
    type                    int32                               4               # 标签类型

@MatchMetaAiInfoDb
    lastLoginReportSec       int64                               1           # 最近一次上报的时间

@MatchStaticsDb
    mmrScoresInfo            MMRScoresInfoDb                     1   +nocs   # 各模式MMR分数信息
    warmRoundInfo            WarmRoundInfoDb                     2   +nocs   # 温暖局信息
    idcNetworkInfo           IdcNetworkInfoDb                    3   +nocs   # 玩家延迟信息
    loginCountryCode         int32                               4   +nocs   # 登陆时玩家的地区code
    matchIsolateInfo         MatchIsolateInfoDb                  5   +nocs   # 匹配隔离信息
    matchMetaAiInfo          MatchMetaAiInfoDb                   6   +nocs   # 匹配ai投放信息

@ReturningInfoDb
    returningTs             int64              1   +nocs   # 回归开始时间戳
    returnActivity          ReturnActivity     2           # 回归活动

@ReturnActivity
    beginTime               int64                                  1           # 本次回流活动开始时间
    endTime                 int64                                  2           # 本次回流活动结束时间
    lossDays                int32                                  3           # 本次流失天数
    customGift              bool                                   4           # 回归定制礼领取状态
    dailyReward             ReturnActivityDailyReward              5           # 每日福利信息
    privilege               ReturnActivityPrivilege                6           # 特权
    userConfId              int32                                  7           # 生效的回归配置Id
    userBaitIndex           int32                                  8           # 生效的回归挡位编号
    signInActivityId        int32                                  9           # 签到活动Id
    privilegeActivityId     int32                                  10          # 特权活动Id
    taskActivityId          int32                                  11          # 任务活动Id
    chargeSignInActivityId  int32                                  12          # 付费签到活动Id
    chargeSignInTicketGot   bool                                   13          # 付费签到门票是否获得
    InterfaceVersion        int32                                  14          # 界面版本
    jumpToSignInAfterRound  bool                                   15          # 完成对局后是否要跳到签到面
    chargeGiftActivityId    int32                                  16          # 付费礼物活动Id
    chargeGiftTicketGot     bool                                   17          # 付费礼物门票是否获得
    exclusiveNewsRecommend  int32[]                                18          # 新内容2专属推荐
    rewardOptionalTask      map<ReturnActivityRewardOptionalTask>  19          # 自选奖励任务
    calendarRewardReceived  bool                                   20          # 日历奖励已获得
    beginDayOffset          int32                                  21          # 本次回流距离开始天数偏移
    activeFarmBuffIds       int32[]                                22          # 激活的农场Buff
    privilegeEndTime        int64                                  23          # 特权结束时间
    activeLevel             int32                                  24          # 活跃等级0低1中2高
    dayLoginCount           int32                                  25          # 每日登陆次数
    firstGameActivityId     int32                                  26          # 首局特权活动id
    returnBookActivityId    int32                                  27          # 回归手册活动ID
    loginFreeActivityId     int32                                  28          # 登录送全套活动ID
    startMatchGiftId        int32                                  29          # 开局送配饰ID
    optionalRewardIndex     int32                                  30          # 回归可选奖励选择index
    newTaskIdSort           int32[]                                31          # 登录送全套任务ID顺序修改 空表示没有修改

@ReturnActivityRewardOptionalTask
    taskId                  int32                  1    +key   # 任务ID
    itemId                  int32[]                2           # 道具ID
    itemNum                 int32[]                3           # 道具数量

@ReturnActivityDailyReward
    firstReward                   bool             1 +nocs     # 见面礼(无用废弃)
    checkInCount                  int32            2 +nocs     # 登录次数(无用废弃)
    activityId                    int32            3           # 每日福利活动Id
    exclusiveRecommend            int32[]          4           # 每日福利专属推荐
    exclusiveRecommendExcept      set<int32>       6 +nocs     # 每日福利专属推荐排除
    taskGroupIndex                int32            7           # 当前任务组索引

@ReturnActivityPrivilege
    lossRewardType            int32     1           # 奖励补领(0-未领取,1-普通,2-付费)
    dailyDoubleRewardCnt      int32     2           # 每日奖励双倍次数
    fireGotNumWeekly          int32     3           # 获取的友谊火种的数量(每周刷新)

@AlgoRecommendMatchInfo
    matchTypes                int32[]    1       # 推荐玩法列表
    recId                     string     2       # credId
    expTag                    string     3       # 实验标签

@IdipStatistics
    timestamp       int64         1 +key    # 时间戳
    num             int32         2         # 统计值

@KvLL
    k               int64         1 +key
    value           int64         2

@KvII
    k               int32         1 +key
    value           int32         2

@KvIL
    k               int32         1 +key
    value           int64         2

@KvIF
    k               int32         1 +key
    value           float         2

@KvSS
    k               string        1 +key
    value           string        2
@KvLStr
    k               int64         1 +key
    value           string        2

@KvIS
    k               int32         1 +key
    value           string        2

@KvFI
    k               float         1 +key
    value           int32         2

@KvSL
    k               string          1 +key
    value           int64           2

@PlayerGameTime
   type            com.tencent.wea.xlsRes.PlayerGameTimeType     1    +key
   value           int64                                         2


@RaffleCost
    coinType          int32               1   +key    # 代币类型
    num               int32               2           # 代币数量

@UgcDailyStage
  stepInfos         map<DBStepInfo>  1  #各个阶段信息
  resetCount        int32            2  #已经手动重置的次数
  lastDayResetTime  int64            3  #上次日重置的时间
  roundId           int64            4  #轮次Id
  changeMapCount    int32            5  #废弃

@UgcStarWorld
  stepId                      int32                   1  # 今天第几轮了
  stepInfo            map<StarWorldStepInfo>          2  # 轮次信息
  lastRefreshTime             int64                   3  # 刷新时间
  difficultyGroupId           int32[]                 4  # 难度组合
  todayGoldCoinNum            int32                   5  # 今天已获得金币数量
  redDot                      bool                    6  # 红点

@StarWorldDetailInfo
  reward         com.tencent.wea.xlsRes.UgcStarWorldRewardType   1    # 奖励
  difficulty        int32                                        2    # 难度
  curMapId          int64                                        3    # 当前正在使用的地图ID
  changeMapCount    int32                                        4    # 更换地图的次数
  info              AlgoInfo                                     5    # 推荐透传信息
  failCount         int32                                        6    # 失败次数
  backupMapIds      int64[]                                      7    # 备份的地图ID列表

@StarWorldStepInfo
  stepId            int32           1     +key    # 阶段ID
  isPass            bool            2         # 是否通关
  firstStarInfo      StarWorldDetailInfo           3  # 星球1详细信息
  secondStarInfo     StarWorldDetailInfo           4  # 星球2详细信息
  lastStar          int32           5       # 最后游玩的星球1/2

@DBStepInfo
  stepId            int32       1     +key    #阶段ID
  recommendMapId    int64       2         #推荐的地图ID
  usedMapIds        int64[]     3         #使用过的地图ID列表
  backupMapIds      int64[]     4         #备份的地图ID列表
  curMapId          int64       5         #当前正在使用的地图ID
  battleId          int64       6         #开局最后一个用的battleId
  isPass            bool        7         #是否通关
  failCount         int32       8         #失败次数
  startTime         int64       9         #开局时间
  info              AlgoInfo    10        #推荐透传信息
  changeMapCount    int32       11        #更换地图的次数

@AlgoInfo
  recId  string  1
  expTag string  2

@UgcCoPlayRecord
    playKey        string               1  +key  # 保证唯一用
    playUid        int64                2        # 同玩的玩家Uid
    playTime       int64                3        # 同玩的时间
    playUgcId      int64                4        # 同玩UGC地图ID
    playTimes      int32                5        # 同玩次数
    playRoomId     int64                6        # 同游房间ID

@UgcCoPlayInfo
    coPlayRecords               map<UgcCoPlayRecord>           1          # 同玩记录

@Timeslot
    timeStart int64 1 +key
    timeEnd int64   2

# 农场月卡
@FarmMonthlyPass
    lastBuyTime int64           1 # 最近一次购买时间
    endTime     int64           2 # 到期时间
    timeslot    map<Timeslot>   3 # 时间段
    effectTime  int64           4 # 累计生效时间
    hasSkinID   set<int32>      5 # 拥有的无人机皮肤ID
    isEffective int32           6 # 月卡是否有效（1Y,0N）（通过endTime>now）

@NewbieNPCFarmInfo
    step int32 1 # 状态（common.FarmNewbieNPCState） 0初始 1偷菜教学完成
    firstEnterTime  int64 2 # 第一次进入的时间

# 事件定时器
@FarmTimer
    eid int32[]       1 # 所有等待自动结束的事件
    endTimeMs int64   2 # 最早结束的时间

@FarmEventValue
    id  int32   1 +key
    iv  int64   2
    sv  string  3

# 事件
@FarmEvent
    id          int32   1  +key   # 无意义的不重复id
    series      int32   2   # 系列id
    evtID       int32   3   # 该系列里的事件id
    startTime   int64   4   # 触发时间Sec
    triggerFarm int64   6   # 在哪个农场达成的条件（执行activeTrigger的农场）
    data        map<FarmEventValue> 7 # 数据
    cond        int32   8   # 触发的条件
    cd          int32   9   # cd
    step        int32   10  # 阶段（分阶段领奖功能）
    clientData  string  11  # 客户端存的数据
    endTimeMs   int64   12  # 最迟的结束时间Ms

@FarmEventSeriesStep
    series  int32 1 +key # 系列号
    step    int32 2      # 最近一次的事件ID

@FarmEvtNotTrigger
    evtKey  string 1 +key # series_eventid 拼起来
    series  int32  2
    eventID int32  3
    times   int32  4
    lastAddMs    int64  5
    todayCount   int32  6
    specialTimes int32  7

@FarmEvtSelfCD
    evtKey string      1 +key
    endTime int64   2

@FarmEvtSharedCD
    mutexID  int32      1 +key
    endTime int64   2

@FarmEventHistory
    evtKey string 1 +key
    times  int64  2

@FarmEventCD
    selfCD  map<FarmEvtSelfCD>      1
    sharedCD map<FarmEvtSharedCD>   2
    history map<FarmEventHistory>   3

# 事件信息
@FarmEventInfo
    events          map<FarmEvent>              1       # 当前触发中的npc信息
    evtIDGen        int32                       2 +nocs # 用来生成无意义的不重复id
    history         FarmEventCD                 3 +nocs # CD中的npc信息
    seriesStep      map<FarmEventSeriesStep>    4 +nocs # 系列的进度
    notTriggerCount map<FarmEvtNotTrigger>      5 +nocs # 所有事件允许触发但未触发的次数
    globalCD        int64                       6 +nocs # 事件的全局触发CD
    customData      map<FarmCustomData>         7 +nocs # 跨事件保存的数据
    farmTimer       FarmTimer                   8 +nocs # 事件定时器
    commitTimes     int32                       9       # 本周已经提交了云游几次
    lastCommit      int64                       10      # 上次提交云游的时间
    resetTime       int64                       11      # 重置时间

@FarmInfo
    myFarmInfo                MyFarmInfo                                1         # 我的农场
    visitFarmInfo             VisitFarmInfo                             2         # 玩家当前所在的场id
    farmSocialInfo            FarmSocialInfo                            3         # 农场社交信息
    liuYanMessageInfo         FarmPlayerLiuYanMessageInfo               4         # 留言板信息
    farmMonthlyPass           FarmMonthlyPass                           5         # 农场月卡信息
    fishingInfo               FarmFishingInfo                           6         # 钓鱼模块信息
    myNPCFarmInfo             MyFarmInfo                                7         # 我的NPC农场
    newbieState               NewbieNPCFarmInfo                         8         # 农场新手教学状态
    obtainExpRecord           FarmObtainMainExpRecord                   9  +nocs  # 农场提供主等级经验记录
    farmEvent                 FarmEventInfo                             10        # 事件信息
    enterFarmTime             int64                                     11        # 上一次进入农场的时间
    farmWeekendInfo           FarmWeekendInfo                           12        # 农场周末信息
    npcHouseInfo              MyHouseInfo                               13        # 小红狐的小屋

@FarmWeekendInfo
    startTime                 int64                                     1         # 未来最近一次农场周末的开始时间
    endTime                   int64                                     2         # 未来最近一次农场周末的结束时间

@FarmPlayerLiuYanMessageInfo
    bannedTime  int64   1# 禁言的截止时间
    dayRecord   map<FarmLiuYanMessageRecord> 2# 每天的留言发送记录

@FarmLiuYanMessageRecord
    farmId    int64   1 +key # 留言的家园id
    recordCount       int32   2# 留言的次数

@MyFarmInfo
    hasFarm                   bool                         1          # 有没有创建农场
    createTime                int64                        2          # 创建时间
    farmID                    int64                        3          # 自己的农场，这个id是0，或者跟自己的uid一样
    initStage                 int32                        4          # 小红狐阶段 0新手小红狐农场，1正式小红狐农场

@VisitFarmInfo
    currentFarmId             int64                          1          # 玩家当前所在的农场id
    enterTimeMs               int64                          2          # 进入时间
    tlogInfo                  VisitFarmTlogInfo              3   +nocs  # 为了流水存储的信息

@VisitFarmTlogInfo
    relationType            int32                                       1
    enterSource             int32                                       2
    tabName                 string                                      3
    RcmdInfo                string                                      4
    ABTestInfo              string                                      5
    channel                 int32                                       6

@FarmSocialInfo
    todayStealingCount           int32                        1           # 当日偷取次数
    lastStealingTime             int64                        2           # 上次偷取时间
    farmEvict                    map<FarmEvictInfo>           3           # 被驱逐信息
    lastHitBadGuyListTime        int64                        4           # 最近一次点击坏人列表时间
    todayFertilizeCount          int32                        5           # 当日施肥次数
    lastFertilizeTime            int64                        6           # 上次施肥时间
    farmPinnedFriends            map<FarmPinFriend>           7           # 农场置顶好友
    farmBlockFriends             map<FarmBlockFriend>         8           # 农场拉黑好友
    farmBeBlocked                map<FarmBlockFriend>         9   +nodb   # 农场被好友拉黑
    hasSyncBlockInfoTag          bool                         10          # 已同步拉黑好友数据标记，兼容之前已拉黑的帐号
    farmFriendNum                int32                        11          # 农场好友数
    lastStealRequestTime         int64                        12          # 上次偷取请求时间
    godStoneRecords              map<FarmGodStoneRecord>      13          # 农场神石信息

@FarmEvictInfo
    farmId                int64                1    +key   # 被哪个农场驱逐
    updateTime            int64                2           # 更新时间
    reason                int32                3           # 驱逐原因

@FarmPinFriend
    uid                   int64               1  +key      # 置顶好友的uid
    updateTime            int64               2            # 更新时间

@FarmBlockFriend
    uid                   int64               1  +key      # 拉黑好友的uid
    updateTime            int64               2            # 更新时间

@FarmFishingInfo
    fishingCount          int32               1
    stealFishCount        int32               2

@FarmBuildingObtainMainExpRecord
    confId                 int32              1   +key    # 农场建筑ID
    alreadyLevel           int32              2           # 已发放等级

@FarmObtainMainExpRecord
    building               map<FarmBuildingObtainMainExpRecord>      1           # 农场建筑发放记录

@FarmGodStoneRecord
    type                      int32                        1 +key      # 神石类型
    todayPolishCount          int32                        2           # 当日打磨次数
    lastPolishTime            int64                        3           # 上次打磨时间

@MyXiaoWoInfo
    hasXiaoWo                   bool                           1          # 有没有创建小窝
    templateId                  int32                          2          # 玩家创建的小窝的模板Id
    instruction                 string                         3          # 玩家的小窝的介绍
    pubID                       int64                          4          # 玩家的发布id
    saveTime                    int64                          5          # 保存时间
    createTime                  int64                          6          # 创建时间
    editID                      int64                          7          # 草稿的ID
    ugcMapMetaInfo              map<XiaowoUgcMapMetaInfo>      8          # ugc信息
    waterExpDay                 int32                          9          # 今日浇水获得的经验
    lastWater                   int64                          10         # 上次浇水的动作时间
    waterDrop                   int32                          11         # 今日浇水掉落次数
    lastWaterDropTime           int64                          12         # 上次浇水掉落时间
    lastGetWaterExpTime         int64                          13         # 最后一次因为浇水获得种植经验的时间
    bucket                      string                         14
    region                      string                         15

@VisitXiaoWoInfo
    currentXiaoWoId             int64                          1          # 玩家当前所在的小窝id
    currentXiaoWoType           int32                          2          # 玩家当前所在的小窝类型
    sampleRoomConfId            int64                          3          # 样板间配置ID (如果在样板间)

@XiaowoUgcMapMetaInfo
    index                       int32                          1  +key    # 无意义
    size                        int32                          2
    msg                         string                         3          # md5
    msgType                     int32                          4          # 1：缩略图 2：关卡 3：编辑 4：组合
    processType                 int32                          5          # 处理类型, 详情见UgcMapMetaInfoProcessType所示
    version                     string                         6
    isCoverCheckPass            bool                           7          # 是否通过了图片检查（目前仅组合使用）
    preMsg                      string                         8          # pre msg
    layerId                     int64                          9

@LayoutIDInfo
    layoutID                    int64                                       1  +key   # 方案ID
    status                      com.tencent.wea.xlsRes.XiaowoLayoutStatus   2         # 方案状态
    updateTime                  int64                                       3         # 更新时间
    layoutName                  string                                      4         # 方案名

@XiaoWoHandbookInfo
    farmHandbook                map<XiaoWoHandbookItem>                     1         # 种田图鉴
    farmingLevel                int32                                       2         # 种田图鉴等级

@XiaoWoHandbookItem
    confId                      int32                                       1  +key   # 配置ID
    totalCount                  int64                                       2
    gotReward                   set<int32>                                  3         # 领取过的奖励档位

@XiaoWoInfo
    myXiaoWoInfo                MyXiaoWoInfo                                1         # 我的小窝
    visitXiaoWoInfo             VisitXiaoWoInfo                             2         # 玩家当前所在的小窝id
    todayLikeXiaoWo             set<int64>                                  3         # 今天点赞的小窝
    starXiaoWo                  set<int64>                                  4         # 收藏的小窝
    starXiaoWoHistory           set<int64>                                  5  +nocs  # 历史收藏的小窝
    unlockedFurniture           set<int64>                                  7         # 已解锁的小窝家具
    initFurnitureVersion        int32                                       9         # 初始化元件版本号
    layoutIDInfo                map<LayoutIDInfo>                           13        # 方案ID信息
    xiaoWoHandbookInfo          XiaoWoHandbookInfo                          14        # 图鉴信息
    farmingInfo                 FarmingInfo                                 15        # 种田信息
    initFurnitureZeroFlag       bool                                        16        # 为了解决老版本线上玩家发过0批次的问题
    liuYanMessageInfo           XiaoWoPlayerLiuYanMessageInfo               17        # 留言板信息
    todayShareXiaoWo            set<int64>                                  18        # 今天分享的小窝

@FarmingInfo
   todayWaterRewardTimes        int32                       1       # 今日浇水产出次数
   todayHarvestCropObtainNum    int32                       2       # 今日收获幼苗数量

@XiaoWoPlayerLiuYanMessageInfo
    bannedTime  int64   1# 禁言的截止时间
    dayRecord   map<LiuYanMessageRecord> 2# 每天的留言发送记录

@LiuYanMessageRecord
    xiaoWoId    int64   1 +key # 留言的家园id
    recordCount       int32   2# 留言的次数

@PartyInfo
    content                           string                1       # 发布内容
    open                              int32                 2       # 是否打开
    hotValueLastUpdated               int32                 3
    playerInPartyLastUpdated          int32                 4
    hotSafeRatioLastUpdated           float                 5
    nicknameUpdated                   string                6
    url                               string                7
    image                             CosImage              8
    urlVersion                        string                9
    xiaowoVersionGroupLastUpdated     int32                 10
    expireTime                        int64                 11

@CosImage
    bucket                            string                1
    region                            string                2
    url                               string                3
    md5                               string                4
    cdnVersion                        string                5

@RaffleSlot
    slotId        int32           1  +key    # 槽位ID
    rewardIds     set<int32>      2          # 关联奖励ID
    lastTs        int64           3          # 上次更新时间戳

@TakeawayActivity
    unlockingBoxId    int32                   1        # 解锁中的宝箱ID, 0表示没有正在解锁的宝箱
    unlockUntilTs     int64                   2        # 解锁完成时间
    invitationId      int64                   5        # 邀请ID
    ackReceivedDaily  int32                   6        # 今日接受助力次数
    ackRewardBoxIds   set<int32>              7        # 已领取助力奖励
    ackUpdateTs       int64                   8        # 今日接受助力次数更新时间
    ackRewardAtCount  set<int32>              9        # 已领取助力奖励对应助力次数

@PlayerSnsInvitationInfo
    uuid               int64                          1  +key  # 邀请ID
    codeStr            string                         2        # 邀请码
    cfgId              int32                          3        # 邀请配置ID
    data               PlayerSnsInvitationData        4  +nocs # 配置信息
    acceptInfos        map<PlayerSnsAcceptedRecord>   5        # 成功助力记录，历史数据

@PlayerSnsAcceptedRecord
    invitee           int64         1       # 助力玩家ID
    acceptTs          int64         2 +key  # 助力时间

@PlayerSnsInvitationData
    activity        PlayerSnsInvitationActivityData         1   # 活动数据
    raffle          PlayerSnsInvitationRaffleData           2   # 抽奖数据

@PlayerSnsInvitationActivityData
    activityId        int32         1     # 活动ID

@PlayerSnsInvitationRaffleData
    raffleId          int32         1     # 奖池组ID
    poolId            int32         2     # 奖池ID

@PlayerSnsAcceptInfo
    cfgId          int32                              1  +key  # 配置ID
    accepted       map<PlayerSnsInvitationBrief>      2        # 已助力的邀请
    updateTs       int64                              3        # 更新时间
    lastData       PlayerSnsInvitationData            4  +nocs # 最后一次助力的配置信息

@PlayerSnsInvitationBrief
    uuid               int64                      1  +key  # 邀请ID
    inviterId          int64                      2        # 邀请人
    ts                 int64                      3        # 助力时间

@FireworksInfo
    showText      string          1    # 烟花活动显示的文本

@RedEnvelopeRainReceivedCnt
    id                   int64            1   # 通用id
    cnt                  int32            2   # 次数

#@RedEnvelopeRewardDetail
#    millSec    int64                    1    +key  # 获取时间
#    items      map<RewardItemInfo>      2          # 道具信息

#@RedEnvelopRainInGameRewards
#    packageGroupId            string                                 1  +key      # 礼包组id
#    items                     map<RedEnvelopeRewardDetail>           2            # 奖励
#    lastUpdateMillSec         int64                                  3            # 最近更新

@RedEnvelopRainShareInfo
    dayId                     int64                               1              # 今日0点时间戳
    shareExtraOpp             int32                               2              # 分享获取的本日额外机会
    clickExtraOpp             int32                               3              # 点击链接获取的本日额外机会
    curDayExtraReceivedCnt    int32                               4              # 本日占用额外次数获取的数量
    extraReceivedCnt          int32                               5              # 额外次数获得的总数量
    extraOpenedCnt            int32                               6              # 额外次数被打开的数量
    curTurnExtraReceivedCnt   int32                               7              # 本轮占用额外次数获取的数量(计数用 跟着curTurnReceivedCnt刷新)

@RedEnvelopeRainActInfo
    actId                     int32                               1  +key         # 红包雨活id
    curTurnReceivedCnt        RedEnvelopeRainReceivedCnt          2               # 本轮获得的数量 id是本轮start时间
    curDayReceivedCnt         RedEnvelopeRainReceivedCnt          3               # 本日获得的数量 id是今日0点
    actReceivedCnt            int32                               4               # 活动总获取数量
    openedCnt                 int32                               5               # 活动打开数量
    isEverShowedRedPoint      int32                               6               # 是否显示过红点(默认没有)
    shareReceivedInfo         RedEnvelopRainShareInfo             7               # 分享获取的机会


@RedEnvelopeRainActivities
    activities            map<RedEnvelopeRainActInfo>         1            # 活动雨活动信息
#    inGameRewards         map<RedEnvelopRainInGameRewards>    2  +nocs     # 游戏内奖励(跟礼包组id走)

@TakeawayInviteeInfo
    ts                    int64           1   # 时间戳
    supportedUids         set<int64>      2   # 已助力好友ID


@PlayerIdipInfo
  dsReplayRecordCount     int32           1   # 录像次数

@KeyValStr
   k                     string               1   +key
   v                     string               2

@LuckyBalloonData
  drawnCount       int32                       1   # 抽取次数
  rewardIds        map<LuckyBalloonItem>       2   # 奖励
  blessIds         map<LuckyBalloonItem>       3   # 祝福

@LuckyBalloonItem
  id            int32       1  +key  # ID
  drawnTs       int64       2        # 抽中时间戳
  drawnAtCount  int32       3        # 在第几次抽中

@LotteryDrawActivity
  totalDrawCount  int32                                  1   # 已抽奖次数，用于判定某些奖励是否进入奖池
  rewardInfo      map<LotteryDrawActivityRewardInfo>     2   # 奖励获取信息

@LotteryDrawActivityRewardInfo
  rewardId          int32       1   +key  # 奖励id
  rewardGetCount    int32       2         # 已抽中次数，用于显示限次获取的奖励是否已全部获取

@LotteryDrawInfo
  totalDrawCount  int32                                  1   # 已抽奖次数，用于判定某些奖励是否进入奖池
  rewardInfo      map<LotteryDrawActivityRewardInfo>     2   # 奖励获取信息

@MinesweeperActivity      # 扫雷活动数据
  lotteryDrawData         LotteryDrawInfo           1   +nocs   # 抽奖信息
  checkerboardData        CheckerboardInfo          2   +nocs   # 棋盘信息

  totalRewardCount             int32                3           # 奖励数量

@CheckerboardInfo  # 扫雷棋盘信息
  row           int32                                    1           # 行
  column        int32                                    2           # 列

  gridInfo      map<CheckerboardGridInfo>                10  +nocs   # 格子存储信息

@CheckerboardGridInfo     # 扫雷棋盘格子信息
  index             int32     1   +key  # 格子索引
  status            int32     2         # 格子状态 比如是否已经领奖 是否已揭露
  type              int32     3         # 格子类型 奖励类型 事件类型 数字类型
  param             int32     4         # 格子参数 比如奖励ID,事件ID等
  roundRewardCount  int32     5         # 周围的奖励数量

@FlyingChessActivityPosInfo
  mapId             int32                         1             # 地图编号
  gridIndex         int32                         2             # 格子编号
  round             int32                         3             # 圈数

@FlyingChessActivityInfo
  curPos            FlyingChessActivityPosInfo    1             # 当前位置
  gridIndex         int32[]                       2             # 当前地图已行走的格子Index
  roundNum          int32                         3             # 已行走圈数
  roundRewardId     set<int32>                    4             # 已领取的圈奖励ID
  bigRewardPos      FlyingChessActivityPosInfo    5             # 领取大奖的位置

  hasGetBigReward   bool                          10            # 大奖是否已发放
  hasGetFreeGift    bool                          11            # 是否拿过初始代币
  runCount          int32                         12     +nocs  # 行走次数
  boxRewardMissCount  int32                       13     +nocs  # 宝箱已miss次数

@PlayerClubChallengeBattleInfo
  isValid               bool                        1         # 是否有效，处于挑战中
  clubId                int64                       2         # 社团ID
  battleId              int64                       3         # 战斗ID
  startTime             int64                       4         # 开始时间

@PlayerClubChallengeReportInfo
  reportStarLight         int32               1         # 总贡献
  reportCount             int32               2         # 贡献次数
  lastReportClubId        int64               3         # 上一次上报贡献的社团ID
  lastReportStarLight     int32               4         # 上一次贡献的星光值

@ClubChallengeActivity
  challengeCount        int32                       1           # 已挑战次数
  pickedRewardId        set<int32>                  2           # 已领取的星光奖励ID列表
  markedMatchTypeId     set<int32>                  3           # 标记使用挑战的玩法ID列表

  battleInfo            PlayerClubChallengeBattleInfo   11  +nocs   # 挑战信息
  reportInfo            PlayerClubChallengeReportInfo   12  +nocs   # 个人贡献信息

@RaffleBIDiscount
  updateTs           int64       3    # 更新时间
  discountNum        int32       4    # 总计次数
  discountAtDraw     set<int32>  5    # 折扣对应抽数

@RaffleFreeDiscount
  updateTs               int64       1     # 更新时间
  usedCount              int32       2     # 每日使用次数
  shareNum               int32       3     # 分享折扣次数
  adNum                  int32       4     # 看广告折扣次数
  totalUsedCount         int32       5     # 累计使用次数
  gainNum                int32       6     # 领取折扣次数
  gainTs                 set<int64>  7     # 领取时间

@SpecialFace
    faceId                      int32       1       +key    # 拍脸id
    faceCount                   int32       2               # 已拍次数
    expireTime                  int64       3               # 过期时间
    dailyShowCountLastDay       int32       4               # 最后一次展示的时候 这一天展示了多少次
    lastShowTime                int64       5               # 最后一次展示的时间 这样可以对比着dailyShowCountLastDay 进行扩展判断今天展示了多少次
    todayNotShowTime            int64       6               # 今日不在展示

@MarqueNoticeInfoItem       # 跑马灯map
    noticeId            int64                           1       +key    # 通知id
    hasNoticedTimes     int32                           2               # 已经通知过的次数
    lastNoticeTimeSec   int64                           3               # 最后一次通知的时间
    hasSentCanceled     bool                            4               # 是否已发送下架
    hasSentThisLogin    bool                            5               # 此次登录是否已经通知

@MarqueeNoticeInfo          # 跑马灯通知信息
    notices             map<MarqueNoticeInfoItem>       1               # 当前持有的通知的信息

@NewYearPilotInfo
    enabled     bool            1   # 新春导航活动开启

@IntellectualActivity       # 脑力达人活动
    activities          set<int32>                      1               # 参加脑力达人活动的场次

@StickFriendInfo
    scene       int32       1   +key    # 场景
    friends     int64[]     2           # 好友UID

@AiChatSession              #智能npc对话session
    roleId      string        1     +key                # 用于区别角色
    round       int32         2     +nodb               # 轮次(仅stream侧使用，不影响llm上下文)
    npcId       int32         3     +nodb               # npcId  (废弃)
    puzzleInfo  map<AigcNpcPuzzleInfo>    4             # 海龟汤完成情况
    dressInfo   map<DressItemInfo>        5             # 装扮
    mood        int32         6                         # 心情值
    profile     AigcNpcProfile            7             # 伙伴设定
    playerProfile  AigcNpcPlayerProfile   8             # 玩家设定
    lastGenTime    int64                  9             # 上次生成时间
    renderId       string                 10            # lab侧的ID （废弃）
    dailyAction    bool                   11            # 每日首次交互
    dailyTouch     bool                   12            # 每日首次触摸
    validChatTimes int32                  13  +nodb     # 连续有效对话次数
    lastTouchPart  int32                  14  +nodb     # 上次触摸部位
    touchSamePart  int32                  15  +nodb     # 连续触摸同一部位
    lastActionTime int64                  16            # 上次交互时间
    labRoleId      int64                  17            # 对应lab侧role_id
    actionState    int32                  18            # npc当前状态
    dailyMoodPunishTotal  int32           19            # 每日心情值惩罚降低累计
    doNotDisturb   int32                  20            # 免打扰功能状态

@AigcNpcProfile             # 对NPC伙伴的设定
    name           string        1                         # 伙伴名字
    title          string        2                         # 称号
    claim          string        3                         # 自称
    playerNick     string        4                         # 对玩家的昵称
    personality    set<int32>    5                         # 性格
    gender         int32         6                         # 性别
    timbre         int32         7                         # 音色
    career         int32         8                         # 职业
    birthday       BirthdayInfo  9                         # 生日
    wishContent    string        10                        # 许愿内容
    labNpcId       string        11                        # 对应lab侧npc_id

@BirthdayInfo               # 生日信息
    year        int32         1
    month       int32         2
    day         int32         3

@AigcNpcModifyTime         # 修改时间CD
    type        int32         1   +key          # 类型
    lastTimeSec int64         2                 # 上次修改时间

@AigcNpcPlayerProfile       # 对玩家自己的设定
    name        string        1                         # 玩家的名字
    birthday    BirthdayInfo  2                         # 生日
    city        string        3                         # 城市
    mbti        int32         4                         # MBTI
    matchTypes  set<int32>    5                         # 喜欢的玩法
    lastModifyTimeSec map<AigcNpcModifyTime>   6        # 修改CD时间(key为AiNpcPlayerProfileType)
    gender      int32         7                         # 性别

@AigcNpcPuzzleInfo          #智能npc海龟汤完成记录
    puzzleId    int32         1     +key   # 海龟汤id
    completed   bool          2            # 是否完成

@AiChatInfo                 #智能npc对话
    sessions     map<AiChatSession>         1              # session管理
    license      bool                       2              # 好好鸭license
    enableAigcNpc  bool                     3    +nodb     # 是否开启智能伙伴
    finishGuide    bool                     4              # 是否完成指引
    PalLicense   bool                       5              # AI伙伴license
    enableToneChat  bool                    6    +nodb     # 是否开启伙伴聊天

@StickerData
    chapters           map<StickerChapterData>           1    # 章节

@StickerChapterData
    chapterId           int32           1 +key    # 章节ID
    normalRewards       set<int32>      2         # 已领取的普通奖励
    bingoRewards        set<int32>      3         # 已领取的连线奖励
    chapterRewarded     bool            4         # 章节奖励是否已领取
    drawCount           int32           5         # 揭开次数

@PlayerRankSettlement
    rankId                       int32             1 +key    # 榜单ID
    participatedSeasons          set<int32>        2         # 参与的赛季ID
    rewardedSeasons              set<int32>        3         # 已领奖赛季ID
    lastRewardedTs               int64             4         # 上次领奖时间

@BubbleConfig
    id                          int32               1   # 当前气泡id
    setTimeMs                   int64               2   # 气泡状态设置的时间

@SubscribeQqRobt
    queryTimeMs                 int64               1   # 上次查询平台订阅状态的时间
    hasSubscribe                bool                2   # 当前订阅状态

@RankHistoryItem
    seasonId                    int32       1 +key    # 赛季ID
    scores                      int32[]     2         # 分数
    refreshTs                   int64       3         # 更新时间
    rewardTs                    int64       4         # 发奖时间
    rewardRankNo                int32       5         # 发奖排名
    rewardReason                int32       6         # 发奖理由，-1过期，0未找到，正整数为priority

@RankHistory
    rankId           int32                    1 +key    # 榜单ID
    items            map<RankHistoryItem>     2         # 记录
    refreshTs        int64                    3         # 更新时间

@GuideInfo
    id                          int64               1 +key  # 引导id
    times                       int64               2       # 引导次数
    timestamp                   int64               3       # 时间戳

@FriendRecommendInfo
    nextAllowRecommendSec       int64               1    # 下一个允许推荐的时间戳单位s

@GuideStatistics
    newInfo                     map<GuideInfo>      1   # 新手引导统计

@PlayerIAAInfo
    id                          int32       1 +key    # 配置ID
    dailyCount                  int32       2         # 当日次数
    totalCount                  int32       3         # 累计次数
    updateTs                    int64       4         # 更新时间

@RaffleRewardSubstitute
    uuid                        int64     1 +key      # UUID
    gainTs                      int64     2           # 获取时间
    atDraw                      int32     3           # 所在抽数
    beforeItemId                int32     4           # 替换前ItemId
    beforeItemNum               int32     5           # 替换前ItemNum
    afterItemId                 int32     6           # 替换后ItemId
    afterItemNum                int32     7           # 替换后ItemId

@RewardCompensateTaskStatus
    type            com.tencent.wea.xlsRes.RewardCompensateType     1   +key    # 类型
    tasks           set<int32>                                      2           # 任务id集合
    htasks          set<int32>                                      3           # 已处理任务集合
    rewards         map<RewardItemInfo>                             4           # 待领取奖励
    expireMs        int64                                           5           # 类型过期时间
    deleteMs        int64                                           6           # 类型应删除的时间
    rewardTasks     set<int32>                                      7           # 领取奖励任务集合

@RewardCompensateTask
    id              int32                                           1   +key    # 任务id
    type            com.tencent.wea.xlsRes.RewardCompensateType     2           # 奖励类型

@RewardCompensateInfo
    typeToTasks      map<RewardCompensateTaskStatus>                 1   # 奖励补领
    taskToType       map<RewardCompensateTask>                       2   # 被监控任务

@MainCups
    progress            int32                                   1           # 总进度
    receivedReward      set<int32>                              2           # 已领取的奖励id
    progressDb          int64                                   3   +nocs   # 保存的保留小数的进度 *100
    weeklyProgress      int32                                   4           # 周进度值
    weeklyProgressDb    int32                                   5   +nocs   # 周进度值保留小数 *100

@MainCupsCycle
    id                  int32                                   1   +key    # 周目id
    progress            int32                                   2           # 当前周目进度
    receivedReward      set<int32>                              3           # 已领取的奖励id
    progressDb          int64                                   4   +nocs   # 保存的保留小数的进度 *100
    cupsCycleNum        int64                                   5           # 当前周目奖杯获得数
    cupsCycleNumDb      int64                                   6   +nocs   # 当前周目奖杯获得数保留小数 *100

@ModeCups
    id              int32                                   1   +key    # 玩法奖杯配置id
    progress        int32                                   2           # 进度
    progressDb      int64                                   3   +nocs   # 保存的保留小数的进度 *100

@CupsRedClick
    showRedByOpen               int32[]                                 1                 # 上新周目红点,周目id列表
    redDotInProgressByOpen      int32[]                                 2   +nocs         # 已经处理的上新周目的红点
    showRedByUnlock             int32[]                                 3                 # 解锁周目红点,周目id列表
    redDotInProgressByUnlock    int32[]                                 4   +nocs         # 已经处理的解锁周目的红点


@CupsInfo
    mainCups                    MainCups                                1           # 总进度
    modeCupsMap                 map<ModeCups>                           2           # 玩法模式进度
    cupsHistoryNum              int64                                   3           # 奖杯总获得数
    cupsHistoryNumDb            int64                                   4   +nocs   # 奖杯总获得数保留小数 *100
    seasonCupsHistoryNum        int32                                   5           # 赛季奖杯总获得数，每赛季重置
    seasonCupsHistoryNumDb      int32                                   6   +nocs   # 赛季奖杯总获得数保留小数 *100
    dailyCupsNum                int32                                   7           # 每日获得奖杯数
    dailyCupsNumDb              int32                                   8   +nocs   # 每日获得奖杯数保留小数 *100
    curCycle                    int32                                   9           # 当前选择周目,0为未开启多周目功能
    mainCupsCycleMap            map<MainCupsCycle>                      10          # 周目进度
    weeklyProgress              int32                                   11          # 多周目共享进度
    weeklyProgressDb            int32                                   12   +nocs  # 多周目共享进度保留小数 *100
    cycleUnLock                 int32[]                                 13          # 解锁过的周目
    unlockCycleCondition        map<ConditionGroupInfo>                 14 +nocs    # 条件组信息
    cupsRedClick                CupsRedClick                            15          # 红点
    cupsCycleOpenLst            int32[]                                 16          # 当前解锁并开启的周目

@MasterPathData
    mainMasterPathMap          map<MainMasterPath>                      1              # 大师之路信息

@MainMasterPath
    masterEnumId               int32                                    1   +key        # 大师之路枚举id
    masterPathInfo             map<MainMasterInfo>                      2               # 大师之路周目进度数据
    unlockCycleCondition       map<ConditionGroupInfo>                  3   +nocs       # 条件组信息

@MainMasterInfo
    cycleId                     int32                                    1   +key        # 周目id
    progress                    int32                                    2               # 当前进度
    rewardedId                  set<int32>                               3               # 已经领取的奖励
    progressDb                  int32                                    4   +nocs       # 当前进度*100

@PixuiRedDotInfo
    id              int32           1   +key        # pixui-id
    clearMs         int64           2               # 清除时间
    deleteMs        int64           3               # 手动删除时间

@RankInfoSubItem
    subTypeId        int64           1   +key        # 子类型+ID
    score            int32[]         2               # 分数
    retry            bool            3               # 是否重试
@BPInfo
   type                  int32                             1  +key   # BPType
	 seasonId              int32                             2         # 当前赛季ID
	 totalExp 	           int32                             3         # 总经验
	 residualExp           int32                             4         # 当前等级结余经验
	 level                 int32                             5         # 当前等级
	 pay                   int32                             6         # 付费等级
	 awardedFreeLevels     set<int32>                        7         # 已领取免费BP奖励的等级
	 awardedPayedLevels    set<int32>                        8         # 已领取付费BP奖励的等级
	 dailyTask             BPTaskInfo                        9   +nocs # 日常任务
	 weekTask              BPTaskInfo                        10  +nocs # 周任务
	 seasonTask            BPTaskInfo                        11  +nocs # 赛季任务
	 inherited             bool                              12  +nocs # 是否完成旧框架数据继承
	 WeekGetExp            int32                             13         # 每周已经获取的经验数量
     LastGetExpTime        int64                             14         # 最近一次获取通行证经验的时间

@BPPublicInfo
   type                  int32                             1  +key   # BPType
	 seasonId              int32                             2         # 当前赛季ID
	 level                 int32                             3         # 当前等级
	 pay                   int32                             4         # 付费等级

@BPTaskInfo
  refreshTs              int64                            1   # 更新时间
  taskGroup              int32                            2   # 已注册任务组,已废弃
  tasks                  set<int32>                       3   # 已注册任务

@RaffleBISeq
  refreshTs             int64       1 # 刷新时间
  seq                   int32[]     2 # BI返回结果
  expTags               string[]    3 # abtest标记
  recid                 string      4 # 推荐标记

@GameTypeDataItem
  dataType              com.tencent.wea.xlsRes.PlayerGameDataStatType     1   +key    # 统计事件id
  dataStatType          com.tencent.wea.xlsRes.PlayerGameDataStatDuration 2           # 统计周期
  value                 int64                                             3           # 值
  statTime              int64                                             4           # 统计时间

@GameTimesStatData
  gameType              int32                   1   +key    # 玩法id
  gameTypeDataItem      map<GameTypeDataItem>   2           # 事件列表

@GameTimesStatistics
  gameTimesData         map<GameTimesStatData>  1 # 副玩法游戏数据统计

@HouseInfo
    myHouseInfo                MyHouseInfo                                1         # 我的农场小屋
    visitHouseInfo             VisitHouseInfo                             2         # 玩家当前所在的农场小屋

@MyHouseInfo
    hasHouse                    bool                         1          # 有没有创建农场小屋
    createTime                  int64                        2          # 创建时间

@VisitHouseInfo
    currentHouseId            int64                          1          # 玩家当前所在的农场小屋id
    enterTimeMs               int64                          2          # 进入时间
    tlogInfo                  VisitHouseTlogInfo             3   +nocs  # 为了流水存储的信息
    currentBuildingId         int32                          4          # 在哪个建筑的house里 兼容村民房子
    currentRoomId             int64                          5          # 玩家当前所在的小屋房间id

@VisitHouseTlogInfo
    relationType            int32                                       1
    enterSource             int32                                       2
    tabName                 string                                      3
    RcmdInfo                string                                      4
    ABTestInfo              string                                      5
    AutoReconnect           bool                                        6

@TradingCardData
  cardList              map<TradingCardInfo>        1 +nocs # 卡牌列表
  wildCardList          map<WildCardInfo>           2 +nocs # 万能卡
  frozenCardList        map<FrozenCardInfo>         3 +nocs # 冻结的卡牌
  rewardFlag            TradingCardRewardFlag       4 +nocs # 奖励信息
  dailyGiveTimes        int32                       5       # 每日赠送次数
  lastRequireTimeMs     int64                       6       # 上次索要时间,0816废弃
  tradingCardRedDot     map<TradingCardRedDot>      7 +nocs # 红点数据
  tradingCycleCup       map<TradingCardCycleCup>    9       # 循环奖杯数据
  lastShareChatTimeMap      map<ShareChatTime>      10      # 上次分享到聊天时间
  tradingCardCollectionTag      map<TradingCardCollectionTag>    11       # 卡集tag
  selfTradeInfoList     map<SelfTradeInfo>          12 +nocs # 自己发起的交易列表
  tradingCardExchange   map<TradingCardExchange>    13      # 卡牌兑换数据
  tradingCardNoviceReward   map<TradingCardNoviceRewardInfo> 14      # 新手奖励数据
  historyCollectionId   set<int32>                  15 +nocs # 已进入历史卡集id
  tradingCardBubbleSendInfo map<TradingCardBubbleSendInfo> 16 +nocs # 气泡数据
  collectionRedDots     map<TradingCardCollectionRedDot> 17   +nocs # 分卡集的红点数据
  tradingCardRankData   map<TradingCardRankData>         18 +nocs # 排行榜需要记录的数据
  extCardBagOutPutData  map<ExtCardBagOutPutData>        19 +nocs # 额外卡包数据
  newCardCollectionRedDot NewCardCollectionRedDot   20      # 新卡集红点
  requireTimeMsList     int64[]                     21       # 索要时间列表

@ExtCardBagOutPutData
  Id                    int32           1 +key  # id
  extCardAddWeight      map<KvII>       2       # 额外卡包增加的对应权重

@NewCardCollectionRedDot
  sendRecord            set<int32>      1       # 已发送的新卡集红点记录

@TradingCardRankData
  collectionId          int32                       1 +key # 卡集ID
  star                  int32                       2      # 通过除交易外获取的总星星数
  tradCardNum           int32                       3      # 通过主动交易送出的卡牌数

@TradingCardNoviceRewardInfo
  collectionId          int32                       1 +key # 卡集ID
  rewardId              int32                       2      # 奖励ID
  draw                  bool                        3      # 是否已领取过

@TradingCardBubbleSendInfo
  bubbleType            int64                       1 +key # 气泡类型
  lastSendTime          int64                       2      # 上次触发气泡的时间

@ShareGiftInfo
    shareTypeGiftInfo     map<ShareTypeGiftInfo>         1  # 分享礼包类别信息


@ShareTypeGiftInfo
    type                        int32                         1 +key      # 类别
    rewardCount                 int32                         2           # 领取次数,废弃
    shareCount                  int32                         3           # 分享次数,废弃
    lastRewardRefreshTs         int64                         4           # 上次领取周期刷新点,废弃
    lastShareRefreshTs          int64                         5           # 上次分享周期刷新点,废弃
    shareLimitInfo              map<ShareLimitInfo>           6           # 上限-次数

@ShareLimitInfo
     limitId                          int32                    1 +key      # 上限表id
     rewardCount                 int32                         2           # 领取次数,废弃兼容老逻辑
     shareCount                  int32                         3           # 分享次数,废弃兼容老逻辑
     lastRewardRefreshTs         int64                         4           # 上次领取周期刷新点,废弃兼容老逻辑
     lastShareRefreshTs          int64                         5           # 上次分享周期刷新点,废弃兼容老逻辑
     dayRewardCount              int32                         6           # 日领取次数
     dayShareCount               int32                         7           # 日分享次数
     dayRefreshTs               int64                          8           # 日刷新时间
     weekShareCount              int32                         9           # 周分享次数
     weekRewardCount             int32                         10           # 周领取次数
     weekRefreshTs               int64                         11           # 周刷新时间
     allShareCount               int32                         12           # 总分享次数
     allRewardCount              int32                         13           # 总分享次数
     allRefreshTs                int64                         14           # 总刷新时间




@TradingCardExchange
  id                        int32                         1 +key      # 兑换id
  tradingCardExchangeInfo   map<TradingCardExchangeInfo>  2           # 单个奖励详情

@TradingCardExchangeInfo
  id                        int32                         1 +key      # 奖励id
  exchangeCount             int32                         2           # 已兑换的次数
  lastExchangeTimeMs        int64                         3           # 上次兑换时间MS

@TradingCardCollectionInfos
  tradingCardCollectionInfo map<TradingCardCollectionInfo> 1 #卡集收集情况,展示数据用

@TradingCardCollectionInfo
 id                     int32                        1 +key #卡集ID
 star                   int32                        2 # 收集的星星数

@TradingCardCollectionCardInfos
  tradingCardCollectionInfo map<TradingCardCollectionCardInfo> 1 #卡集收集情况,展示数据用
  loaded                int32                         2     #是否已加载过  0,未加载  1 已加载

@TradingCardCollectionCardInfo
 id                     int32                        1 +key #卡集ID
 tradingCardInfo        map<TradingCardInfo>         2 # 收集的卡牌数据,交换卡牌中展示用

@SelfTradeInfo
  id                    int64               1 +key  # 交易id
  expireTimeMs          int64               2       # 过期时间

@TradingCardCollectionTag
  id                       int32                            1 +key  # 卡集id
  tag                      int32                            2       # tag
@ShareChatTime
  tradeId                   int64                       1 +key  # 交易id
  time                      int64                       2       # 分享时间

@TradingCardRewardFlag
  rewardedCardDeckId            set<int32>                  1       # 已领奖的卡组
  rewardedCardCollectionId      set<int32>                  2       # 已领奖的卡集

@FrozenCardInfo
  tradeId                   int64                   1  +key     # 交易id
  cardList                  map<TradingCardInfo>    2           # 冻结的卡牌
  expireTimeMs              int64                   3           # 过期时间
  deductSourceType          int32                   4           # 扣除来源 CardDeductSourceType

@WildCardInfo
  uuid                  int64           1  +key # 唯一id
  cardId                int32           2       # 万能卡id
  expireTimeMs          int64           3       # 过期时间

@TradingCardInfo
  id                    int32           1  +key # 卡牌id
  num                   int32           2       # 数量

@TradingCardRedDot
  redDotType                int32                   1    +key
  redDotIds                 set<int64>              2

@TradingCardCollectionRedDot
  collectionId              int32                   1   +key
  tradingCardRedDot         map<TradingCardRedDot>  2   #红点数据


@TradingCardCycleCup
 id                     int32          1  +key # 循环奖杯ID
 stageId                int32          2       # 当前所属的节点
 progress               int32          3       # 进度 这个进度可以超过当前上限
 tradingCardCycleCupStageRecord map<TradingCardCycleCupStageRecord> 4 +nocs #领奖的记录
 weekAddProgress        int32          5       # 每周获取的进度
 updateTimeMS           int64          6       # 更新MS
 totalProgress          int32          7       # 总进度
 sendMailReward         bool           8       # 是否已发送过邮件奖励
 realProgress           int32          9   +nocs  # 真实的奖杯数量 乘100之后的值
 realWeekAddProgress    int32          10  +nocs  # 每周获取的进度 乘100之后的值

@TradingCardCycleCupStageRecord
 id                     int32          1 +key # 节点ID
 drawCount              int32          2      # 领奖次数

@TradingCardClearRedDot
  redDotType                int32                   1    +key
  redDotIds                 set<int64>              2
  updateTimeMs              int64                   3    # 更新时间

@GameModeReturnData
  completeReturnRecords       map<GameModeReturnCompleteRecord>              1   # 已完成的回流的数据
  runningReturnDatas          map<GameModeReturnRunningData>                 2   #  运行中的回流数据
  triggerShowDatas            map<GameModeReturnTriggerShowData>             3   #  已触发的展示数据
  gameModeReturnPushData      map<GameModeReturnPushData>                    4   # 玩法回流推送数据

@GameModeReturnTriggerShowData
  group                    int32                    1    +key # 展示的分组
  gameModeReturnTriggerShows map<GameModeReturnTriggerShow> 2 # 单个ID的详细数据

@GameModeReturnTriggerShow
  id                       int32                    1    +key   # 回流ID
  state                    int32                    2           # 状态 0未展示 1 展示中 2 展示结束
  startShowTimeMs          int64                    3           # 开始展示的时间
  endShowTimeMs            int64                    4           # 结束展示的时间

@GameModeReturnRunningData
    id                      int32                   1   +key
    triggerTimeMs           int64                   2   # 满足条件的时间
    startTimeMs             int64                   3   # 开启回流的时间
    state                   int32                   4   # 状态 1 已触发 2 已开始
    unlockStageIds          set<int32>              5   # 已解锁的阶段
    triggerTags             map<GameModeReturnTriggerTag> 6 # 触发时的tag
    showFace                int32                   7   # 是否需要展示拍脸 0 需要 1不需要
    endTimeMs               int64                   8   # 结束时间 0代表没有
    redDots                 int32                   9   # 0 没有 1有上新红点
    unlockRedDot            set<int32>              10  # 解锁红点  有ID就是有红点
    taskRedDot              set<int32>              11  # 任务红点
    progress                int32                   12  # 进度值
    progressDraw            set<int32>              13  # 已领取过的进度奖励
    drawInnReward           int32                   14  # 是否领取见面礼  0 未领取 1 领取
    triggerTasks            set<int32>              15  # 已触发的任务

@GameModeReturnCompleteRecord
   id                       int32                   1    +key
   completeTimeMs           int64                   2    # 完成的时间
   completeCount            int32                   3    # 完成的次数

@GameModeReturnTriggerTag
   id                       int32                   1    +key
   value                    int64                   2    # 触发时的值

@GameModeReturnPushData
   id                       int32                   1   +key
   name                     string                  2   # 玩法模式名称
   state                    int32                   3   # 状态 1 已触发 2 已开始
   order                    int32                   4   # 优先级
   startTimeMs              int64                   5   # 开启回流的时间
   endTimeMs                int64                   6   # 结束时间 0代表没有
   progress                 int32                   7   # 进度值
   bigTaskId                int32                   8   # 大奖任务Id
   taskIdList               int32[]                 9   # 待完成的任务
   itemList                 int32[]                 10  # 展示的奖励id

@BattleSettlementItemBuffData
   buffs                    map<BattleSettlementItemBuff> 1 #buff数据

@BattleSettlementItemBuff
   id                       int32                   1   +key
   startTime                int64                   2       # 开始时间
   endTime                  int64                   3       # 结束时间
   effectCnt                int32                   4       # 生效次数
   refreshTime              int64                   5       # 限制刷新时间

@PlayerIAAData
  toAppRewardId         int32             1        # 云转端已领取奖励ID
  toAppRewardTs         int64             2        # 云转端奖励领取时间
  forceGuideStat        PlayerIAAStat     3 +nocs  # 强制拍脸计数
  forceGuideRequired    bool              4        # 需要强制拍脸
  forceGuideRefreshTs   int64             5        # 强制拍脸刷新时间

# 奖励找回
@AttrRewardRetrievalData
  retrievalConfId       int32                             1         # 活动配置id
  retrievalList         map<AttrRewardRetrievalInfo>      2         # 找回列表
  retrievalIdRedDot     map<AttrRewardRetrievalIdRedDot>  3         # 每个奖励的红点
  isRedDot              bool                              4         # 是否新红点
  isRedDotRemove        bool                              5         # 是否已经客户端主动消除
  lastCheckEpochSecs    int64                             6         # 上一次检索时间戳

@AttrRewardRetrievalInfo
  retrievalId           int32                             1 +key    # 找回id
  finishedTimes         int32                             2         # 已经完成次数
  retrievedTimes        int32                             3         # 已经找回次数
  retrievalTotalTimes   int32                             4         # 可找回总次数
  isUnlock              bool                              5         # 是否解锁
  unlockEpochSecs       int64                             6         # 解锁时间戳
  finishedTimeSecs      int64                             7         # 上次完成时间
  conditionGroup        ConditionGroup                    8 +nocs   # 条件组
  conditionGroupLastCheckEpochSecs      int64             9         # 条件组上一次检索时间戳

@AttrRewardRetrievalIdRedDot
  retrievalId           int32                             1 +key    # 找回id
  isRedDot              bool                              2         # 是否新红点
  isRedDotRemove        bool                              3         # 是否已经客户端主动消除

@PlayerIAAStat
  onlineMinutes         int32       1     # 在线时间
  battleStartCount      int32       2     # 开局次数

@ArenaHeroUnlockData
  heroId                int32                                    1  +key         # heroId
  gameTimesStatistics   GameTimesStatistics                      2               # 统计次数

@ArenaHeroRankItem
  geoLevel          int32           1   +key    # enum GeoLevel，全服榜单为0
  rankLevel         int32           2           # 级别越高，值越小
  geoCode           int32           3           # 地区编号，全服榜单为0
  seasonId          int64           4           # 赛季ID，每周结算
  position          int32           5           # 名次
  score             int32           6           # 排行榜战力值（百分定点数）：activePoint * (battleScore + perfScore)
  badgeId           int32           7           # 战力徽章

@ArenaHeroCombatEffectivenessDataItem
  group                 int32                       1   +key    # 分组（ArenaCombatEffectivenessGroupType）
  activePoint           int32                       2           # 活跃系数（百分定点数），每次登录根据上次排位赛时间重新计算
  battleScore           int32                       3           # 实际场次分
  perfScore             int32                       4           # 实际表现分
  lastQualifyTime       int64                       5   +nocs   # 上次排位赛时间（ms）
  activePointUpdateTime int64                       6   +nocs   # 上次活跃系数更新时间（ms）
  badgeId               int32                       7           # 上周结算最高级别的战力徽章
  latestRank            map<ArenaHeroRankItem>      8           # 上周结算信息，最多3个级别，上榜才记录
  historyRank           map<ArenaHeroRankItem>      9           # 历史最高结算信息，最多3个级别，上榜才记录
  lastWeekScore         int32                       10          # 上周分数

@ArenaHeroCombatEffectivenessData
  heroId            int32                                       1   +key    # 英雄ID
  ceItems           map<ArenaHeroCombatEffectivenessDataItem>   2           # 各玩法英雄战力数据

@ArenaSeasonSettlementData
  qualifyType           int32           1   +key    # 段位类型
  qualifyInfo           QualifyingInfo  2           # 当前赛季段位信息
  oldQualifyInfo        QualifyingInfo  3           # 上赛季段位信息

@ArenaHeroCombatEffectiveness
  heroData              map<ArenaHeroCombatEffectivenessData>   1   +nodb   # 英雄战力数据
  settlementTime        int64                                   2           # 3v3结算进行时间（ms），每周（赛季）结算一次
  seasonSettlement      map<ArenaSeasonSettlementData>          3   +nocs   # 赛季结算信息
  settlementTimeHOK     int64                                   4           # 5v5结算进行时间（ms），每周（赛季）结算一次

@Arena3V3WarmRoundData
  isBeginner            bool                  1     #是否为小白玩家（新手局结束根据MVP分数判定）
  warmRoundNum          int32                 2     #温暖局次数（新手局之后的特殊温暖局）


@ArenaGameData
  unlockData              map<ArenaHeroUnlockData>                1 # 解锁数据
  ceData                  map<ArenaHeroCombatEffectivenessData>   2 # 英雄战力数据 （废弃）
  combatEffectiveness     ArenaHeroCombatEffectiveness            3 # 战力数据
  sevenDaysLoginActivity  ArenaSevenDaysLoginActivity             4 # 峡谷七日登录活动
  isBigPackage            bool                                    5 # 是否为大小包（3v3大小包的标记）
  arena3V3WarmRoundData   Arena3V3WarmRoundData                   6 # 3v3新手温暖局数据


@CookInfo
    myCookInfo                MyCookInfo                                1         # 我的餐厅
    visitCookInfo             VisitCookInfo                             2         # 玩家当前所在的餐厅

@MyCookInfo
    hasCook                     bool                         1          # 有没有创建餐厅
    createTime                  int64                        2          # 创建时间

@VisitCookInfo
    currentCookId             int64                          1          # 玩家当前所在的餐厅id
    enterTimeMs               int64                          2          # 进入时间
    tlogInfo                  VisitFarmTlogInfo              3   +nocs  # 为了流水存储的信息

@RaffleRewardStashItem
  index                 int32       1 +key  # 下标顺序，从1开始
  itemId                int32       2       # 道具ID
  itemNum               int32       3       # 道具数量
  expireType            int32       4       # 道具过期类型
  expireMs              int64       5       # 道具过期时间
  atDraw                int32       6 +nocs # 对应第几抽
  subItemIds            int32[]     7 +nocs # 替换道具ID
  subItemNums           int32[]     8 +nocs # 替换道具数量
  subItemWeights        int32[]     9 +nocs # 替换道具权重
  reason                int32       10      # 道具变动原因
  fromPoolId            int32       11      # 来源奖池
  isGrand               bool        12      # 是否为大奖

@RaffleStash
  stashId               int64                               1   +key  # stashID
  raffleId              int32                               2         # 奖池组ID
  poolStash             map<RafflePoolStash>                4   +nocs # 奖池结果
  billNo                string                              5   +nocs # 订单号
  gainItems             map<RaffleRewardStashItem>          6         # 获得的道具，包括暂存箱
  extraGainItems        map<RaffleRewardStashItem>          7         # 额外获得的道具
  slot                  map<RaffleSlot>                     8   +nocs # 确认已抽取的位置
  positionId            int32                               9         # 在选择框中的位置
  points                int32                               10        # 累计积分
  subGainItems          map<RaffleRewardStashItem>          11        # 获得的子奖励道具
  subItems              map<RaffleRewardSubItem>            12        # 子奖励信息

@RafflePoolStash
    id                     int32                           1   +key     # 奖池ID
    guarantee              map<RaffleGuaranteeRecord>      6            # 保底机制记录
    rewards                map<RaffleRewardRecord>         8            # 奖励记录
    rewardGroups           map<RaffleRewardGroupRecord>    9            # 奖励组记录

@ArenaSettings      #MOBA设置
  #3V3设置
  minAngle                   int32   1     #最小俯仰角度
  maxAngle                   int32   2     #最大俯仰角度
  targetSelectionStrategy    int32   3     #攻击目标
  cameraControlType          int32   4     #镜头视角
  isAutoNormalAttack         bool    5     #自动普通攻击

  #吃鸡设置
  minAngleBS                   int32   6     #最小俯仰角度
  maxAngleBS                   int32   7     #最大俯仰角度
  targetSelectionStrategyBS    int32   8     #攻击目标
  cameraControlTypeBS          int32   9     #镜头视角
  isAutoNormalAttackBS         bool    10     #自动普通攻击

  #头像索敌(3V3  吃鸡  5V5)
  isLockTargetByIcon3V3         bool    11    #3v3
  isLockTargetByIconBS          bool    12    #吃鸡
  isLockTargetByIcon5V5         bool    13    #5v5

  #英雄战力
  showHeroPower                bool    14     #是否显示英雄战力

  #占点设置
  minAngleHZ                   int32   15     #最小俯仰角度
  maxAngleHZ                   int32   16     #最大俯仰角度
  targetSelectionStrategyHZ    int32   17     #攻击目标
  cameraControlTypeHZ          int32   18     #镜头视角
  isAutoNormalAttackHZ         bool    19     #自动普通攻击
  isLockTargetByIconHZ         bool    20     #头像索敌

  #所有玩法公用设置
  isShowSkillTips              bool    21     #技能tips开关
  indicatorAbsorbType          int32   22     #指示器锁定目标设置

  #扩展参数
  expandData                   map<StrStrPair>  23    #用于扩展

@PlayerLevelEstimation
  levelId              set<int32>                                1          # 已评价关卡ID集合

@PlayerHugOther
  otherUid                int64            1 +key #抱起的其他玩家UID
  hugCount                int64            2 #抱起的次数

@PlayerHugOthersInfo
  sceneType                 int32               1 +key #场景类型,主广场1,分广场2,农场3,家园4
  hugOthers                 map<PlayerHugOther> 2 #抱起玩家的数据
  HugStrangerCount          int64               3 #抱起的次数
  HugStrangerNumber         int64               4 #抱起玩家的数量

@PlayerGameActionInfos
  dailyHugInfos             map<PlayerHugOthersInfo> 1 #每日抱人数据,主要是陌生人的数据
  lastUpdateTime            int64                    2 #上次更新时间,做每日重置用

@RaffleInventoryItem
  uuid                      int64               1 +key  # uuid
  itemId                    int32               2       # 道具ID
  itemNum                   int32               3       # 道具数量
  isGrand                   bool                4       # 是否为大奖
  billNo                    string              5       # 关联流水
  fromPool                  int32               6       # 来源奖池ID

@QualifyingDailyRankInfo
  qualifyType              int32            1 +key # 排位类型
  rankNo                   int32            2      # 排名
  expireMs                 int64            3      # 超时时间

@CocCardInfo
   id           int32   1   +key   #月卡id
   startTime    int64   2
   endTime      int64   3
   hasSyncCoc   bool    4

@CocActivityData
   activityId        int32   1   +key   #活动id
   beginTime         int64   2          #活动开始时间 单位毫秒
   endTime           int64   3          #活动结束时间 单位毫秒
   EndedAndCleared   bool    4          #活动是否已经结束并清理

@CocActivityInfo
   eightDaysActivity  CocActivityData          1 # Coc八日签到活动

@CocModeInfo
   needSyncItemIds   set<int32>         1           # 需要同步coc服的道具id
   hasCoc            bool               2           # 是否进入过coc场景
   cocCardInfo       map<CocCardInfo>   3           # 月卡信息
   modeAttrSnap      CocModeAttrSnap    4   +nocs   # coc玩法内的需要在GS频繁访问的属性快照数据(只读)
   cocActivityInfo   CocActivityInfo    5           # coc玩法活动

@CocModeAttrSnap
   baseCampLv   int32   1      # coc大本营等级

@PlaceholderUnit
    id          int64   1 +key      # 占位的key
    number      int64   2           # 占位的数量
    timestampMs int64   3           # 占位开始时间

@Placeholder
    units   map<PlaceholderUnit>    1   # 每个占位者
    total   int64                   2   # 占位数总数

@RaffleRewardSubItem
    uuid              int64           1 +key     # UUID
    itemIds           int32[]         2          # 道具ID
    itemNums          int32[]         3          # 道具数量
    rewardId          int32[]         4          # 对应奖励ID
    subRewardId       int32           5          # 对应子奖励类型
    coinType          int32           6          # 购买货币类型
    coinNum           int32           7          # 购买货币数量
    bought            bool            8          # 是否已购买

@ActivityPuzzleData
    puzzles         map<ActivityPuzzleInfo>     1   # 拼图信息

@ActivityPuzzleInfo
    puzzleId        int32             1 +key      # 拼图ID
    uncoverIndices  set<int32>        2           # 已揭开的拼图下标
    completed       bool              3           # 已完成
    stageRewardIds  set<int32>        4           # 已发放的阶段奖励
@SafetyCheck
    isNeedCheckProfileBeforMatch     bool            1          # 匹配时候是否需要检测头像安全性
    isUpdateOnline    bool                          2          # 需不需要更新线上载具

@PasswordCodeData
   taskId               int32          1 +key    # 任务ID
   passwordCode         string         2         # 口令码
   createTime           int64          3         # 生成时间
   usedPasswordCode     set<string>    4         # 使用的口令码
@DanceData
    highestScore      int32            1         # 最高分

@PlayerPakPlayRecord
    pakId                 int32                           1 +key      # 玩法包体ID
    weekPakPlayRecord     map<WeekPakPlayRecord>          2           # 每周包体玩法次数记录
    LastPlayTime          int64                           3           # 最近包体玩法游玩时间

@WeekPakPlayRecord
    weekStartTime         int64                           1 +key      # 每周开始时间
    pakPlayTimes          int32                           2           # 玩法包体游玩次数

@RaffleCommonInfo
    raffleId              int32                           1   +key      # 奖池组ID
    times                 int32                           2             # 抽取次数
    dailyTimes            int32                           3             # 今日抽取次数
    lastTs                int64                           4             # 上次抽取时间
    pools                 map<RafflePoolCommonInfo>       5             # 奖池信息
    extraData             map<RaffleCommonKeyVal>         7   +nocs     # 额外信息

@RafflePoolCommonInfo
    poolId                int32                             1   +key      # 奖池ID
    times                 int32                             2             # 抽取次数
    dailyTimes            int32                             3             # 今日抽取次数
    lastTs                int64                             4             # 上次抽取时间
    rewardGroups          map<RaffleRewardGroupCommonInfo>  5             # 奖励组
    rewards               map<RaffleRewardCommonInfo>       6             # 奖励
    extraData             map<RaffleCommonKeyVal>           8   +nocs     # 额外信息

@RaffleRewardGroupCommonInfo
    groupId               int32                         1  +key       # 奖励组ID
    totalDrawnTimes       int32                         2             # 累计抽取次数
    lastAtDraw            int32                         3  +nocs      # 上次抽中所在次数
    extraData             map<RaffleCommonKeyVal>       4  +nocs      # 额外信息

@RaffleRewardCommonInfo
    rewardId              int32                    1  +key       # 奖励ID
    totalDrawnTimes       int32                    2             # 累计抽取次数
    lastAtDraw            int32                    3  +nocs      # 上次抽中所在次数
    extraData             map<RaffleCommonKeyVal>  4  +nocs      # 额外信息

@RaffleCommonKeyVal
    keyId                 int32                1 +key       # 参考RaffleCommonAttrKey
    value                 RaffleCommonData     2

@RaffleCommonData       +oneof
    intVal                int32                           1             # int
    longVal               int64                           2             # long
    strVal                string                          3             # str
    intSet                Int32Set                        4             # int集合
    poolGuarantee         RafflePoolGuaranteeCounters     5             # 保底计数器
    poolPurchase          RafflePoolPurchaseCounters      6             # 价格计数器
    freeTicket            RaffleFreeTicket                7             # 免费抽数

@Int32Set
    element             set<int32>            1

@RafflePoolGuaranteeCounters
    counters      map<RafflePoolGuaranteeCounter>   1

@RafflePoolGuaranteeCounter
    type          int32         1 +key    # 类型
    counter       int32         2         # 计数
    deactivated   bool          3         # 开关

@RafflePoolPurchaseCounters
    counters      map<RafflePoolPurchaseCounter>      1

@RafflePoolPurchaseCounter
    type            int32       1 +key    # 类型
    times           int32       2         # 购买次数
    dailyTimes      int32       3         # 今日购买次数
    lastTs          int64       4         # 购买时间
    discountTimes   int32       5         # 折扣次数
    lastDiscountTs  int64       6         # 折扣时间


@RaffleFreeTicket
    totalTickets     int32        1     # 累计获取数量
    dailyTickets     int32        2     # 今日获取数量
    lastGainTs       int64        3     # 上次获取时间

@ActivityRaffleInfo
    raffleId         int32                  1 +key
    data             RaffleCommonInfo       2           # 数据
    costs            map<RaffleCommonCost>  3           # 累计花费

@ActivityRaffle #祈愿
    runningRaffles     map<ActivityRaffleInfo>      1       # 自动管理的

@RaffleCommonCost
    coinType          int32               1   +key    # 代币类型
    num               int64               2           # 代币数量

@Int32Map
  kvs     map<KvII>       1

@ChaseTaskResult
  taskId        int32           1 +key  # 任务ID
  rewardId      int32           2       # 奖励ID
  ts            int64           3       # 时间戳

@KaiboInfo
  lastEventDispatchTs             int64             1         # 上次事件分发时间戳
  lastDispatchedDouyinRoomId      string            2 +nocs   # 最后一次分发的抖音直播间ID

@StochasticStateDelegations
  delegations		map<StochasticStateDelegation>	1 +nocs

@StochasticStateDelegation
  uuid           int64                                        1 +key  # UUID
  scenario       int32                                        2	      # 业务场景
  id             int64                                        3	      # 业务ID
  expireTs       int64                                        4	      # 过期时间
  state	         StochasticState                              5	      # 已确认状态
  imStates       map<StochasticIntermediateState>             6	      # 未确认状态

@StochasticState +oneof
  int32Map       Int32Map	                    1      # int32map

@StochasticIntermediateState
  uuid           int64                        1 +key  #
  seq            int32[]                      2	      # 结果序列
  rawData        bytes                        3	      # 属性delta
  tag            string	                      4	      # 自定义标签

@OnlineEarningData
  firstEnterTs         int64                       1       # 首次参与时间ms
  currentInfo          OnlineEarningInfo           2       # 当前网赚系统配置

@OnlineEarningInfo
  id                      int32                           1        # 配置组ID
  firstEnterTs            int64                           2        # 首次进入时间ms
  lastEnterTs             int64                           3        # 上次进入时间ms
  opRecordInfo            OnlineEarningOpRecordInfo       6        # 操作日志记录
  taskInfo                OnlineEarningTaskInfo           7        # 任务记录
  totalDayCount           int32                           8        # 累计活跃天数
  consecutiveDayCount     int32                           9        # 连续活跃天数
  consecutiveStartTs      int64                           10       # 连续活跃天数开始时间
  returningTs             int64                           11       # 回流时间
  checkin                 OnlineEarningCheckinInfo        12       # 7日签到
  bank                    OnlineEarningBankInfo           13 +nocs # 储蓄罐
  card                    OnlineEarningCardInfo           14       # 翻牌
  reservation             OnlineEarningReservationInfo    15       # 预约
  commonTaskGroups        set<int32>                      16       # 常规任务组
  allowance               OnlineEarningAllowanceInfo      17       # 吃饭补贴
  withdrawal              OnlineEarningWithdrawalInfo     18       # 提现功能

@OnlineEarningOpRecordInfo
  uuid                    int64           1 +nocs # 操作记录集合UUID
  opCounter               map<KvIL>       2       # 操作记录, key为OnlineEarningOpType

@OnlineEarningTaskInfo
  lastWatchingAdTs        int64                         1 +nocs # 看广告时间ms
  watchAdTaskId           int32                         2 +nocs # 看广告任务ID
  taskIdLists             map<OnlineEarningTaskIdList>  3       # 任务ID

@OnlineEarningTaskIdList
  type                   int32                              1 +key  # 任务类型
  refreshTs              int64                              2       # 更新时间
  tasks                  set<int32>                         3       # 已注册任务
  taskRepeats            map<KvII>                          4 +nocs # 重复次数
  taskRewards            map<OnlineEarningTaskReward>       5 +nocs # 奖励

@OnlineEarningTaskReward
  taskId                int32                               1 +key  # 任务ID
  rewards               map<RewardItemInfo>                 2       # 奖励

@OnlineEarningCheckinInfo
  weekCompleteCount             int32           1   # 完成签到周数
  dayOfWeekCompleteCount        int32           2   # 完成签到天数
  lastCheckinTs                 int64           3   # 签到时间戳ms
  currentWeekRefreshTs          int64           4 +nocs  # 本轮签到刷新时间ms
  isNewbie                      bool            5 +nocs  # 是否满足新人标签

@OnlineEarningBankInfo
  cashbackRate                  int32           1   # 返现比例

@OnlineEarningCardInfo
  nextWatchingAdTs              int64           1       # 允许下次看广告的时间ms
  rewardDigit                   int32[]         2       # 位数，从个位开始
  lastRewardDigitCheckTs        int64           3       # 上次位数检查时间ms
  expectedTaskRefreshTs         int64           4       # 期望的任务刷新时间点

@OnlineEarningReservationInfo
  status                      int32     1         # 状态，参考OnlineEarningReservationState
  reserveTs                   int64     2 +nocs   # 发起预约时间

@OnlineEarningAllowanceInfo
  expectedTaskRefreshTs       int64     1         # 预期任务刷新时间

@OnlineEarningWithdrawalInfo
  lastWatchingAdTs        int64                         1 +nocs # 看广告时间ms
  watchAdItemId           int32                         2 +nocs # 看广告道具ID

@AttrHandOrnamentGong # 双手手持物锣
  knockNum                      int32                                   1           # 敲击次数
  freshEpochMillis              int64                                   2           # 时间戳毫秒
  changeEpochMillis             int64                                   3           # 时间戳毫秒
  gongItem                      map<GongItem>                           4           # 每个锣道具

@GongItem
  itemId                        int32                                   1 +key      # 锣道具
  itemUuid                      int64                                   2           # 道具uuid
  knockNum                      int32                                   3           # 敲击次数
  freshEpochMillis              int64                                   4           # 时间戳毫秒

